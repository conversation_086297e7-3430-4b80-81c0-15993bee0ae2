-- 测试智能执行方法选择的流水线
-- 验证不同类型命令是否选择了正确的执行方法

-- 删除旧的测试流水线（如果存在）
DELETE FROM Sa_SshPipelinesItem WHERE pid = 'pipeline-smart-method-test-001';
DELETE FROM Sa_SshPipelines WHERE id = 'pipeline-smart-method-test-001';

-- 创建智能方法选择测试流水线
INSERT INTO Sa_SshPipelines (
    id, pipelinename, description, category, issystem, rownum, remark,
    createby, createbyid, createdate, lister, listerid, modifydate,
    revision
) VALUES (
    'pipeline-smart-method-test-001',
    '智能执行方法测试流水线',
    '测试不同类型命令的执行方法选择',
    '测试',
    1,
    95,
    '验证条件判断、检查命令、安装命令的方法选择',
    'System',
    'system',
    NOW(),
    'System',
    'system',
    NOW(),
    1
);

-- 步骤1：条件判断命令（应该使用传统方法）
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext, 
    successpattern, errorpattern, timeoutms, 
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-smart-method-test-001',
    'pipeline-smart-method-test-001',
    '条件判断命令测试',
    'if ! command -v docker &> /dev/null; then echo "Docker未安装"; else echo "Docker已安装"; fi',
    '(?i).*(docker.*安装).*',
    null,
    300000,  -- 300秒超时，但应该使用传统方法
    1,
    0,
    1,
    '测试条件判断命令，应该使用传统方法',
    1
);

-- 步骤2：版本检查命令（应该使用传统方法）
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext, 
    successpattern, errorpattern, timeoutms, 
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-smart-method-test-002',
    'pipeline-smart-method-test-001',
    '版本检查命令测试',
    'python3 --version && echo "Python版本检查完成"',
    '(?i).*(python.*version|版本检查完成).*',
    null,
    120000,  -- 120秒超时，但应该使用传统方法
    1,
    0,
    2,
    '测试版本检查命令，应该使用传统方法',
    1
);

-- 步骤3：服务状态检查（应该使用传统方法）
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext, 
    successpattern, errorpattern, timeoutms, 
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-smart-method-test-003',
    'pipeline-smart-method-test-001',
    '服务状态检查测试',
    'systemctl is-active sshd && echo "SSH服务运行中" || echo "SSH服务未运行"',
    '(?i).*(ssh.*运行|active).*',
    null,
    90000,  -- 90秒超时，但应该使用传统方法
    1,
    0,
    3,
    '测试服务状态检查，应该使用传统方法',
    1
);

-- 步骤4：模拟安装命令（应该使用实时输出方法）
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext, 
    successpattern, errorpattern, timeoutms, 
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-smart-method-test-004',
    'pipeline-smart-method-test-001',
    '模拟安装命令测试',
    'echo "开始模拟yum install..." && for i in {1..5}; do echo "安装进度 $i/5..."; sleep 2; done && echo "yum install完成"',
    '(?i).*(install.*完成).*',
    null,
    60000,  -- 60秒超时，应该使用实时输出方法
    1,
    0,
    4,
    '测试模拟安装命令，应该使用实时输出方法',
    1
);

-- 步骤5：下载命令测试（应该使用实时输出方法）
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext, 
    successpattern, errorpattern, timeoutms, 
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-smart-method-test-005',
    'pipeline-smart-method-test-001',
    '下载命令测试',
    'echo "开始模拟wget下载..." && for i in {1..3}; do echo "下载进度 $i/3..."; sleep 3; done && echo "wget下载完成"',
    '(?i).*(wget.*完成|下载完成).*',
    null,
    45000,  -- 45秒超时，应该使用传统方法（因为不包含真正的wget）
    1,
    0,
    5,
    '测试下载命令，应该使用传统方法（模拟）',
    1
);

-- 查看创建的测试流水线
SELECT 
    '=== 智能执行方法测试流水线 ===' as title;

SELECT 
    rownum,
    stepname,
    timeoutms,
    timeoutms / 1000 as timeout_seconds,
    CASE 
        WHEN commandtext LIKE 'if %' OR commandtext LIKE '% if %' THEN '传统方法（条件判断）'
        WHEN commandtext LIKE '%--version%' OR commandtext LIKE '%command -v%' THEN '传统方法（检查命令）'
        WHEN commandtext LIKE '%systemctl is-active%' THEN '传统方法（状态检查）'
        WHEN commandtext LIKE '%yum install%' OR commandtext LIKE '%apt install%' THEN '实时输出（安装命令）'
        WHEN commandtext LIKE '%wget%' OR commandtext LIKE '%curl -o%' THEN '实时输出（下载命令）'
        WHEN timeoutms > 60000 THEN '实时输出（长超时）'
        ELSE '传统方法（短超时）'
    END as expected_method,
    LEFT(commandtext, 50) as command_preview
FROM Sa_SshPipelinesItem 
WHERE pid = 'pipeline-smart-method-test-001'
ORDER BY rownum;

-- 修复Docker流水线的步骤3命令
UPDATE Sa_SshPipelinesItem 
SET commandtext = 'if ! command -v docker >/dev/null 2>&1; then echo "🔄 开始安装Docker..."; yum install -y yum-utils && yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo && yum install -y docker-ce docker-ce-cli containerd.io && systemctl enable docker && systemctl start docker && echo "✅ Docker安装完成"; else echo "✅ Docker已安装，跳过安装"; fi'
WHERE id = 'step-docker-check-003';

-- 验证Docker流水线修复
SELECT 
    '=== Docker流水线步骤3修复验证 ===' as title;

SELECT 
    stepname,
    timeoutms / 1000 as timeout_seconds,
    CASE 
        WHEN commandtext LIKE 'if %' THEN '传统方法（条件判断）'
        ELSE '其他方法'
    END as expected_method,
    LEFT(commandtext, 80) as command_preview
FROM Sa_SshPipelinesItem 
WHERE id = 'step-docker-check-003';

-- 使用说明
SELECT 
    '=== 测试说明 ===' as title;

SELECT 
    '1. 执行"智能执行方法测试流水线"' as instruction
UNION ALL
SELECT 
    '2. 观察每个步骤显示的执行方法' as instruction
UNION ALL
SELECT 
    '3. 步骤1-3应显示"⚡ 使用传统方法执行"' as instruction
UNION ALL
SELECT 
    '4. 步骤4应显示"📡 使用实时输出方法执行"' as instruction
UNION ALL
SELECT 
    '5. 重新测试Docker流水线，步骤3应该有正确输出' as instruction;
