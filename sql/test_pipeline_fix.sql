-- 测试流水线修复效果
-- 验证配置是否正确

-- 1. 查看修复后的流水线配置
SELECT 
    '=== 修复后的流水线配置 ===' as title;

SELECT 
    rownum,
    stepname,
    LEFT(commandtext, 60) as command_preview,
    successpattern,
    errorpattern,
    continueonerror,
    retrycount,
    timeoutms
FROM Sa_SshPipelinesItem 
WHERE pid = 'pipeline-docker-check-001'
ORDER BY rownum;

-- 2. 验证关键配置
SELECT 
    '=== 配置验证 ===' as title;

SELECT 
    '第1步应该允许继续执行' as check_item,
    CASE WHEN continueonerror = 1 THEN '✅ 正确' ELSE '❌ 错误' END as result
FROM Sa_SshPipelinesItem 
WHERE id = 'step-docker-check-001'

UNION ALL

SELECT 
    '第2步应该允许继续执行' as check_item,
    CASE WHEN continueonerror = 1 THEN '✅ 正确' ELSE '❌ 错误' END as result
FROM Sa_SshPipelinesItem 
WHERE id = 'step-docker-check-002'

UNION ALL

SELECT 
    '第3步应该允许继续执行' as check_item,
    CASE WHEN continueonerror = 1 THEN '✅ 正确' ELSE '❌ 错误' END as result
FROM Sa_SshPipelinesItem 
WHERE id = 'step-docker-check-003'

UNION ALL

SELECT 
    '第1步不应该重试' as check_item,
    CASE WHEN retrycount = 0 THEN '✅ 正确' ELSE '❌ 错误' END as result
FROM Sa_SshPipelinesItem 
WHERE id = 'step-docker-check-001'

UNION ALL

SELECT 
    '第3步应该有重试' as check_item,
    CASE WHEN retrycount > 0 THEN '✅ 正确' ELSE '❌ 错误' END as result
FROM Sa_SshPipelinesItem 
WHERE id = 'step-docker-check-003';

-- 3. 检查正则表达式
SELECT 
    '=== 正则表达式检查 ===' as title;

SELECT 
    stepname,
    CASE 
        WHEN successpattern IS NULL THEN '无成功模式'
        WHEN successpattern LIKE '%docker%' THEN '✅ 包含docker关键词'
        ELSE '⚠️ 可能需要检查'
    END as success_pattern_check,
    CASE 
        WHEN errorpattern IS NULL THEN '无错误模式'
        WHEN errorpattern LIKE '%error%' OR errorpattern LIKE '%failed%' THEN '✅ 包含错误关键词'
        ELSE '⚠️ 可能需要检查'
    END as error_pattern_check
FROM Sa_SshPipelinesItem 
WHERE pid = 'pipeline-docker-check-001'
ORDER BY rownum;

-- 4. 统计信息
SELECT 
    '=== 统计信息 ===' as title;

SELECT 
    COUNT(*) as total_steps,
    SUM(CASE WHEN continueonerror = 1 THEN 1 ELSE 0 END) as continue_on_error_count,
    SUM(CASE WHEN retrycount > 0 THEN 1 ELSE 0 END) as retry_enabled_count,
    SUM(CASE WHEN successpattern IS NOT NULL THEN 1 ELSE 0 END) as has_success_pattern_count,
    SUM(CASE WHEN errorpattern IS NOT NULL THEN 1 ELSE 0 END) as has_error_pattern_count
FROM Sa_SshPipelinesItem 
WHERE pid = 'pipeline-docker-check-001';

-- 5. 预期执行流程
SELECT 
    '=== 预期执行流程 ===' as title;

SELECT 
    rownum,
    stepname,
    CASE 
        WHEN continueonerror = 1 THEN '失败后继续'
        ELSE '失败后停止'
    END as failure_behavior,
    CASE 
        WHEN retrycount = 0 THEN '不重试'
        ELSE CONCAT('重试', retrycount, '次')
    END as retry_behavior,
    CASE 
        WHEN successpattern IS NULL AND errorpattern IS NULL THEN '仅检查退出码'
        WHEN successpattern IS NOT NULL AND errorpattern IS NULL THEN '检查成功模式'
        WHEN successpattern IS NULL AND errorpattern IS NOT NULL THEN '检查错误模式'
        ELSE '检查成功和错误模式'
    END as pattern_behavior
FROM Sa_SshPipelinesItem 
WHERE pid = 'pipeline-docker-check-001'
ORDER BY rownum;
