-- 智能Docker检查与安装流水线
-- 这个流水线会智能处理Docker的各种状态

-- 1. 删除旧的流水线步骤（如果存在）
DELETE FROM Sa_SshPipelinesItem WHERE pid = 'pipeline-smart-docker-001';
DELETE FROM Sa_SshPipelines WHERE id = 'pipeline-smart-docker-001';

-- 2. 创建新的智能Docker流水线
INSERT INTO Sa_SshPipelines (
    id, pipelinename, description, category, issystem, rownum, remark,
    createby, createbyid, createdate, lister, listerid, modifydate,
    revision
) VALUES (
    'pipeline-smart-docker-001',
    '智能Docker检查与安装',
    '智能检测Docker状态并根据需要进行安装和配置',
    '系统安装',
    1,
    2,
    '智能处理Docker未安装、已安装但未启动等各种情况',
    'System',
    'system',
    NOW(),
    'System',
    'system',
    NOW(),
    1
);

-- 步骤1：检查系统信息
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext, 
    successpattern, errorpattern, timeoutms, 
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-smart-docker-001',
    'pipeline-smart-docker-001',
    '检查系统信息',
    'echo "=== 系统信息 ===" && cat /etc/os-release | head -3 && echo "当前用户: $(whoami)" && echo "==================="',
    '(?i).*(系统信息).*',
    null,
    10000,
    1,  -- 继续执行
    0,
    1,
    '显示系统基本信息',
    1
);

-- 步骤2：检查Docker是否已安装
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext, 
    successpattern, errorpattern, timeoutms, 
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-smart-docker-002',
    'pipeline-smart-docker-001',
    '检查Docker安装状态',
    'if command -v docker >/dev/null 2>&1; then echo "Docker已安装: $(docker --version)"; else echo "Docker未安装"; fi',
    '(?i).*(docker已安装|docker.*version).*',
    null,  -- 不设置错误模式，让它自然继续
    10000,
    1,  -- 继续执行
    1,
    2,
    '检查Docker是否已安装',
    1
);

-- 步骤3：检查Docker服务状态
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext, 
    successpattern, errorpattern, timeoutms, 
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-smart-docker-003',
    'pipeline-smart-docker-001',
    '检查Docker服务状态',
    'if systemctl is-active docker >/dev/null 2>&1; then echo "Docker服务正在运行"; elif systemctl is-enabled docker >/dev/null 2>&1; then echo "Docker服务已安装但未运行"; else echo "Docker服务未安装或未配置"; fi',
    '(?i).*(docker服务).*',
    null,
    15000,
    1,  -- 继续执行
    2,
    3,
    '检查Docker服务运行状态',
    1
);

-- 步骤4：安装Docker（如果需要）
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext, 
    successpattern, errorpattern, timeoutms, 
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-smart-docker-004',
    'pipeline-smart-docker-001',
    '安装Docker（如果需要）',
    'if ! command -v docker >/dev/null 2>&1; then echo "开始安装Docker..."; apt-get update -qq && apt-get install -y docker.io && echo "Docker安装完成"; else echo "Docker已存在，跳过安装"; fi',
    '(?i).*(docker安装完成|docker已存在).*',
    '(?i).*(failed|error|unable|失败|错误|无法).*',
    300000,  -- 5分钟
    1,  -- 即使安装失败也继续，可能是权限问题
    2,
    4,
    '根据需要安装Docker',
    1
);

-- 步骤5：启动Docker服务（如果需要）
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext, 
    successpattern, errorpattern, timeoutms, 
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-smart-docker-005',
    'pipeline-smart-docker-001',
    '启动Docker服务（如果需要）',
    'if command -v docker >/dev/null 2>&1; then if ! systemctl is-active docker >/dev/null 2>&1; then echo "启动Docker服务..."; systemctl start docker && systemctl enable docker && echo "Docker服务已启动"; else echo "Docker服务已在运行"; fi; else echo "Docker未安装，跳过服务启动"; fi',
    '(?i).*(docker服务已启动|docker服务已在运行|跳过服务启动).*',
    '(?i).*(failed|error|启动失败).*',
    30000,
    1,  -- 继续执行
    3,
    5,
    '根据需要启动Docker服务',
    1
);

-- 步骤6：测试Docker功能
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext, 
    successpattern, errorpattern, timeoutms, 
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-smart-docker-006',
    'pipeline-smart-docker-001',
    '测试Docker功能',
    'if command -v docker >/dev/null 2>&1 && systemctl is-active docker >/dev/null 2>&1; then echo "测试Docker功能..."; docker run --rm hello-world 2>/dev/null && echo "Docker功能测试成功" || echo "Docker功能测试失败，可能需要权限或网络"; else echo "Docker未就绪，跳过功能测试"; fi',
    '(?i).*(docker功能测试成功|跳过功能测试).*',
    null,  -- 不设置错误模式
    90000,  -- 1.5分钟
    1,  -- 继续执行
    2,
    6,
    '测试Docker基本功能',
    1
);

-- 步骤7：显示最终状态
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext, 
    successpattern, errorpattern, timeoutms, 
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-smart-docker-007',
    'pipeline-smart-docker-001',
    '显示最终状态报告',
    'echo "=== Docker状态报告 ===" && echo "安装状态: $(command -v docker >/dev/null 2>&1 && echo "已安装 ($(docker --version))" || echo "未安装")" && echo "服务状态: $(systemctl is-active docker 2>/dev/null || echo "未运行")" && echo "用户组: $(groups | grep -o docker || echo "未加入docker组")" && echo "==================="',
    '(?i).*(docker状态报告).*',
    null,
    15000,
    1,  -- 总是继续
    0,
    7,
    '显示Docker完整状态报告',
    1
);

-- 验证创建结果
SELECT 
    id,
    stepname,
    LEFT(commandtext, 50) as command_preview,
    successpattern,
    continueonerror,
    retrycount,
    rownum
FROM Sa_SshPipelinesItem 
WHERE pid = 'pipeline-smart-docker-001'
ORDER BY rownum;
