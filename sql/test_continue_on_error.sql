-- 测试ContinueOnError配置的流水线
-- 验证不同的错误处理策略

-- 删除旧的测试流水线（如果存在）
DELETE FROM Sa_SshPipelinesItem WHERE pid = 'pipeline-continue-error-test-001';
DELETE FROM Sa_SshPipelines WHERE id = 'pipeline-continue-error-test-001';

-- 创建错误处理测试流水线
INSERT INTO Sa_SshPipelines (
    id, pipelinename, description, category, issystem, rownum, remark,
    createby, createbyid, createdate, lister, listerid, modifydate,
    revision
) VALUES (
    'pipeline-continue-error-test-001',
    'ContinueOnError测试流水线',
    '测试不同的错误处理策略配置',
    '测试',
    1,
    97,
    '验证ContinueOnError=0和ContinueOnError=1的不同行为',
    'System',
    'system',
    NOW(),
    'System',
    'system',
    NOW(),
    1
);

-- 步骤1：成功步骤
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext, 
    successpattern, errorpattern, timeoutms, 
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-continue-error-test-001',
    'pipeline-continue-error-test-001',
    '成功步骤',
    'echo "✅ 这个步骤会成功"',
    '(?i).*(会成功).*',
    null,
    10000,
    0,  -- ContinueOnError=0
    0,
    1,
    '正常成功的步骤',
    1
);

-- 步骤2：失败但允许继续 (ContinueOnError=1)
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext, 
    successpattern, errorpattern, timeoutms, 
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-continue-error-test-002',
    'pipeline-continue-error-test-001',
    '失败但允许继续',
    'echo "❌ 这个步骤会失败但允许继续" && exit 1',
    '(?i).*(不会匹配).*',  -- 故意不匹配，让它失败
    null,
    10000,
    1,  -- ContinueOnError=1，允许继续
    0,
    2,
    '失败但配置为允许继续执行',
    1
);

-- 步骤3：继续执行的步骤
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext, 
    successpattern, errorpattern, timeoutms, 
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-continue-error-test-003',
    'pipeline-continue-error-test-001',
    '继续执行的步骤',
    'echo "✅ 前面失败了但我还是执行了"',
    '(?i).*(还是执行了).*',
    null,
    10000,
    0,  -- ContinueOnError=0
    0,
    3,
    '验证前面失败后是否还能继续执行',
    1
);

-- 步骤4：失败且禁止继续 (ContinueOnError=0)
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext, 
    successpattern, errorpattern, timeoutms, 
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-continue-error-test-004',
    'pipeline-continue-error-test-001',
    '失败且禁止继续',
    'echo "🛑 这个步骤会失败且禁止继续" && exit 1',
    '(?i).*(不会匹配).*',  -- 故意不匹配，让它失败
    null,
    10000,
    0,  -- ContinueOnError=0，禁止继续
    0,
    4,
    '失败且配置为禁止继续执行',
    1
);

-- 步骤5：不应该执行的步骤
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext, 
    successpattern, errorpattern, timeoutms, 
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-continue-error-test-005',
    'pipeline-continue-error-test-001',
    '不应该执行的步骤',
    'echo "❌ 如果看到这个说明流水线没有正确停止"',
    '(?i).*(没有正确停止).*',
    null,
    10000,
    0,  -- ContinueOnError=0
    0,
    5,
    '这个步骤不应该被执行',
    1
);

-- 查看创建的测试流水线
SELECT 
    '=== ContinueOnError测试流水线 ===' as title;

SELECT 
    rownum,
    stepname,
    continueonerror,
    CASE 
        WHEN continueonerror = 1 THEN '允许继续'
        WHEN continueonerror = 0 THEN '禁止继续'
        ELSE '未配置'
    END as error_handling,
    LEFT(commandtext, 40) as command_preview,
    remark
FROM Sa_SshPipelinesItem 
WHERE pid = 'pipeline-continue-error-test-001'
ORDER BY rownum;

-- 验证Docker流水线的ContinueOnError配置
SELECT 
    '=== Docker流水线ContinueOnError配置 ===' as title;

SELECT 
    rownum,
    stepname,
    continueonerror,
    CASE 
        WHEN continueonerror = 1 THEN '✅ 允许继续'
        WHEN continueonerror = 0 THEN '🛑 禁止继续'
        ELSE '❓ 未配置'
    END as error_handling,
    retrycount
FROM Sa_SshPipelinesItem 
WHERE pid = 'pipeline-docker-check-001'
ORDER BY rownum;

-- 预期执行结果
SELECT 
    '=== 预期执行结果 ===' as title;

SELECT 
    '步骤1: 成功执行' as expected_result
UNION ALL
SELECT 
    '步骤2: 失败但显示"允许继续执行"' as expected_result
UNION ALL
SELECT 
    '步骤3: 继续执行并成功' as expected_result
UNION ALL
SELECT 
    '步骤4: 失败且显示"禁止继续执行"' as expected_result
UNION ALL
SELECT 
    '步骤5: 不会执行（流水线已停止）' as expected_result;

-- 使用说明
SELECT 
    '=== 测试说明 ===' as title;

SELECT 
    '1. 执行"ContinueOnError测试流水线"' as instruction
UNION ALL
SELECT 
    '2. 观察步骤2失败时的状态显示' as instruction
UNION ALL
SELECT 
    '3. 确认步骤3是否继续执行' as instruction
UNION ALL
SELECT 
    '4. 观察步骤4失败时的状态显示' as instruction
UNION ALL
SELECT 
    '5. 确认步骤5不会执行（流水线停止）' as instruction;
