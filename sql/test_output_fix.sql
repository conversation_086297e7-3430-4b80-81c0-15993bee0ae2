-- 测试输出修复的流水线
-- 验证不同类型命令的输出显示

-- 删除旧的测试流水线（如果存在）
DELETE FROM Sa_SshPipelinesItem WHERE pid = 'pipeline-output-test-001';
DELETE FROM Sa_SshPipelines WHERE id = 'pipeline-output-test-001';

-- 创建输出测试流水线
INSERT INTO Sa_SshPipelines (
    id, pipelinename, description, category, issystem, rownum, remark,
    createby, createbyid, createdate, lister, listerid, modifydate,
    revision
) VALUES (
    'pipeline-output-test-001',
    '输出显示测试流水线',
    '测试不同类型命令的输出显示效果',
    '测试',
    1,
    96,
    '验证短命令和长命令的输出显示',
    'System',
    'system',
    NOW(),
    'System',
    'system',
    NOW(),
    1
);

-- 步骤1：短命令测试（使用传统方法）
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext, 
    successpattern, errorpattern, timeoutms, 
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-output-test-001',
    'pipeline-output-test-001',
    '短命令测试-查看当前目录',
    'pwd && echo "当前目录查看完成"',
    '(?i).*(当前目录查看完成).*',
    null,
    10000,  -- 10秒，使用传统方法
    1,
    0,
    1,
    '测试短命令的输出显示',
    1
);

-- 步骤2：短命令测试-系统信息
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext, 
    successpattern, errorpattern, timeoutms, 
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-output-test-002',
    'pipeline-output-test-001',
    '短命令测试-系统信息',
    'whoami && date && echo "系统信息获取完成"',
    '(?i).*(系统信息获取完成).*',
    null,
    15000,  -- 15秒，使用传统方法
    1,
    0,
    2,
    '测试系统信息命令的输出',
    1
);

-- 步骤3：中等命令测试（使用传统方法）
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext, 
    successpattern, errorpattern, timeoutms, 
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-output-test-003',
    'pipeline-output-test-001',
    '中等命令测试-文件列表',
    'ls -la /etc | head -10 && echo "文件列表显示完成"',
    '(?i).*(文件列表显示完成).*',
    null,
    30000,  -- 30秒，使用传统方法
    1,
    0,
    3,
    '测试文件列表命令的输出',
    1
);

-- 步骤4：长命令测试（使用实时输出方法）
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext, 
    successpattern, errorpattern, timeoutms, 
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-output-test-004',
    'pipeline-output-test-001',
    '长命令测试-模拟安装',
    'echo "开始模拟安装..." && for i in {1..8}; do echo "安装进度 $i/8..."; sleep 5; done && echo "模拟安装完成"',
    '(?i).*(模拟安装完成).*',
    null,
    90000,  -- 90秒，使用实时输出方法
    1,
    0,
    4,
    '测试长时间命令的实时输出',
    1
);

-- 步骤5：Docker命令测试（短命令）
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext, 
    successpattern, errorpattern, timeoutms, 
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-output-test-005',
    'pipeline-output-test-001',
    'Docker命令测试',
    'docker --version 2>/dev/null && echo "Docker已安装" || echo "Docker未安装"',
    '(?i).*(docker.*version|docker已安装|docker未安装).*',
    null,
    10000,  -- 10秒，使用传统方法
    1,
    0,
    5,
    '测试Docker命令的输出显示',
    1
);

-- 查看创建的测试流水线
SELECT 
    '=== 输出显示测试流水线 ===' as title;

SELECT 
    rownum,
    stepname,
    timeoutms,
    timeoutms / 1000 as timeout_seconds,
    CASE 
        WHEN timeoutms > 60000 THEN '实时输出方法'
        ELSE '传统方法'
    END as execution_method,
    LEFT(commandtext, 40) as command_preview
FROM Sa_SshPipelinesItem 
WHERE pid = 'pipeline-output-test-001'
ORDER BY rownum;

-- 修复Docker流水线的超时配置
UPDATE Sa_SshPipelinesItem 
SET timeoutms = 15000  -- 改为15秒，使用传统方法
WHERE id = 'step-docker-check-001';

UPDATE Sa_SshPipelinesItem 
SET timeoutms = 20000  -- 改为20秒，使用传统方法
WHERE id = 'step-docker-check-002';

-- 验证Docker流水线修复
SELECT 
    '=== Docker流水线超时配置修复 ===' as title;

SELECT 
    rownum,
    stepname,
    timeoutms,
    timeoutms / 1000 as timeout_seconds,
    CASE 
        WHEN timeoutms > 60000 THEN '实时输出方法'
        ELSE '传统方法'
    END as execution_method
FROM Sa_SshPipelinesItem 
WHERE pid = 'pipeline-docker-check-001'
ORDER BY rownum;

-- 使用说明
SELECT 
    '=== 测试说明 ===' as title;

SELECT 
    '1. 执行"输出显示测试流水线"' as instruction
UNION ALL
SELECT 
    '2. 观察步骤1-3的输出（传统方法）' as instruction
UNION ALL
SELECT 
    '3. 观察步骤4的实时输出（实时方法）' as instruction
UNION ALL
SELECT 
    '4. 重新测试Docker流水线' as instruction
UNION ALL
SELECT 
    '5. 确认输出不再显示"(无)"' as instruction;
