-- 修复Docker流水线配置
-- 解决正则表达式和ContinueOnError的问题

-- 1. 修复第一步：检查Docker是否安装
UPDATE Sa_SshPipelinesItem
SET
    commandtext = 'docker --version 2>/dev/null && echo "✅ Docker已安装" || echo "❌ Docker未安装"',
    successpattern = '(?i).*(docker.*version|docker已安装|docker未安装).*',
    errorpattern = null,  -- 不设置错误模式，让它自然处理
    continueonerror = 1,  -- 允许继续执行
    retrycount = 0  -- 检查不需要重试
WHERE id = 'step-docker-check-001';

-- 2. 修复第二步：检查Docker服务状态
UPDATE Sa_SshPipelinesItem
SET
    commandtext = 'systemctl is-active docker 2>/dev/null && echo "✅ Docker服务运行中" || echo "❌ Docker服务未运行"',
    successpattern = '(?i).*(active|running|docker服务运行中|docker服务未运行).*',
    errorpattern = null,  -- 不设置错误模式，让它自然处理
    continueonerror = 1,  -- 允许继续执行
    retrycount = 0  -- 检查不需要重试
WHERE id = 'step-docker-check-002';

-- 3. 修复第三步：安装Docker
UPDATE Sa_SshPipelinesItem
SET
    commandtext = 'if ! command -v docker >/dev/null 2>&1; then echo "🔄 开始安装Docker..."; (yum install -y docker || apt-get update && apt-get install -y docker.io || echo "❌ 安装失败：包管理器不可用"); else echo "✅ Docker已存在，跳过安装"; fi',
    successpattern = '(?i).*(docker已存在|安装.*完成|docker.*installed|跳过安装).*',
    errorpattern = '(?i).*(安装失败|unable to locate|error.*install|failed.*install).*',
    continueonerror = 1,  -- 安装失败也继续，可能是权限问题
    retrycount = 2
WHERE id = 'step-docker-check-003';

-- 4. 修复第四步：启动Docker服务
UPDATE Sa_SshPipelinesItem 
SET 
    successpattern = '(?i).*(docker started|docker already running|enabled|已启动).*',
    errorpattern = '(?i).*(failed to start|permission denied|not found|启动失败|权限拒绝).*',
    continueonerror = 0,  -- 启动失败应该停止
    retrycount = 3
WHERE id = 'step-docker-check-004';

-- 5. 修复第五步：运行Hello World容器
UPDATE Sa_SshPipelinesItem 
SET 
    successpattern = '(?i).*(hello from docker|this message shows).*',
    errorpattern = '(?i).*(error|not found|permission denied|cannot connect|错误|未找到|权限拒绝).*',
    continueonerror = 0,  -- 容器运行失败应该停止
    retrycount = 2
WHERE id = 'step-docker-check-005';

-- 6. 添加新的最终状态检查步骤（如果不存在）
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext, 
    successpattern, errorpattern, timeoutms, 
    continueonerror, retrycount, rownum, remark,
    revision
) 
SELECT 
    'step-docker-check-006',                 -- id
    'pipeline-docker-check-001',             -- pid
    '显示Docker最终状态',                    -- stepname
    'echo "=== Docker状态检查完成 ===" && (docker --version 2>/dev/null || echo "Docker未安装") && (systemctl is-active docker 2>/dev/null || echo "Docker服务未运行")',  -- commandtext
    '(?i).*(docker状态检查完成).*',           -- successpattern
    null,                                    -- errorpattern
    15000,                                   -- timeoutms (15 seconds)
    1,                                       -- continueonerror (always continue)
    0,                                       -- retrycount
    6,                                       -- rownum
    '显示Docker安装和运行状态的总结',         -- remark
    1                                        -- revision
WHERE NOT EXISTS (
    SELECT 1 FROM Sa_SshPipelinesItem WHERE id = 'step-docker-check-006'
);

-- 验证修复结果
SELECT
    id,
    stepname,
    LEFT(commandtext, 50) as command_preview,
    successpattern,
    errorpattern,
    continueonerror,
    retrycount,
    rownum
FROM Sa_SshPipelinesItem
WHERE pid = 'pipeline-docker-check-001'
ORDER BY rownum;

-- 显示修复摘要
SELECT
    '修复完成' as status,
    COUNT(*) as total_steps,
    SUM(CASE WHEN continueonerror = 1 THEN 1 ELSE 0 END) as continue_on_error_steps,
    SUM(CASE WHEN retrycount > 0 THEN 1 ELSE 0 END) as retry_enabled_steps
FROM Sa_SshPipelinesItem
WHERE pid = 'pipeline-docker-check-001';

-- 立即执行修复
COMMIT;
