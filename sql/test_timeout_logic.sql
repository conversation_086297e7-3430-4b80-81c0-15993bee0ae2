-- 测试超时逻辑的流水线
-- 验证超时时间是否正确工作

-- 删除旧的测试流水线（如果存在）
DELETE FROM Sa_SshPipelinesItem WHERE pid = 'pipeline-timeout-test-001';
DELETE FROM Sa_SshPipelines WHERE id = 'pipeline-timeout-test-001';

-- 创建超时测试流水线
INSERT INTO Sa_SshPipelines (
    id, pipelinename, description, category, issystem, rownum, remark,
    createby, createbyid, createdate, lister, listerid, modifydate,
    revision
) VALUES (
    'pipeline-timeout-test-001',
    '超时逻辑测试流水线',
    '测试不同超时时间的命令执行',
    '测试',
    1,
    98,
    '验证超时时间配置是否正确工作',
    'System',
    'system',
    NOW(),
    'System',
    'system',
    NOW(),
    1
);

-- 步骤1：短超时测试（10秒）
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext, 
    successpattern, errorpattern, timeoutms, 
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-timeout-test-001',
    'pipeline-timeout-test-001',
    '短超时测试(10秒)',
    'echo "开始10秒测试..." && sleep 5 && echo "5秒完成，应该成功"',
    '(?i).*(应该成功).*',
    null,
    10000,  -- 10秒超时
    1,
    0,
    1,
    '测试10秒超时，命令5秒完成，应该成功',
    1
);

-- 步骤2：中等超时测试（30秒）
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext, 
    successpattern, errorpattern, timeoutms, 
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-timeout-test-002',
    'pipeline-timeout-test-001',
    '中等超时测试(30秒)',
    'echo "开始30秒测试..." && for i in {1..6}; do echo "进度 $i/6..."; sleep 4; done && echo "24秒完成，应该成功"',
    '(?i).*(应该成功).*',
    null,
    30000,  -- 30秒超时
    1,
    0,
    2,
    '测试30秒超时，命令24秒完成，应该成功',
    1
);

-- 步骤3：长超时测试（120秒）
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext, 
    successpattern, errorpattern, timeoutms, 
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-timeout-test-003',
    'pipeline-timeout-test-001',
    '长超时测试(120秒)',
    'echo "开始120秒测试..." && for i in {1..10}; do echo "长时间处理 $i/10..."; sleep 6; done && echo "60秒完成，应该成功"',
    '(?i).*(应该成功).*',
    null,
    120000,  -- 120秒超时
    1,
    0,
    3,
    '测试120秒超时，命令60秒完成，应该成功',
    1
);

-- 步骤4：超长超时测试（300秒 = 5分钟）
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext, 
    successpattern, errorpattern, timeoutms, 
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-timeout-test-004',
    'pipeline-timeout-test-001',
    '超长超时测试(300秒)',
    'echo "开始300秒测试..." && echo "模拟大型软件安装..." && for i in {1..20}; do echo "安装进度 $i/20 ($(($i * 5))%)..."; sleep 3; done && echo "60秒完成，应该成功"',
    '(?i).*(应该成功).*',
    null,
    300000,  -- 300秒超时（5分钟）
    1,
    2,  -- 允许重试2次
    4,
    '测试300秒超时，命令60秒完成，应该成功',
    1
);

-- 步骤5：故意超时测试（15秒超时，但命令需要30秒）
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext, 
    successpattern, errorpattern, timeoutms, 
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-timeout-test-005',
    'pipeline-timeout-test-001',
    '故意超时测试(15秒超时30秒命令)',
    'echo "开始超时测试..." && echo "这个命令会超时..." && sleep 30 && echo "不应该看到这个"',
    '(?i).*(不应该看到).*',
    null,
    15000,  -- 15秒超时，但命令需要30秒
    1,  -- 继续执行
    1,  -- 重试1次
    5,
    '测试超时机制，15秒超时但命令需要30秒，应该超时',
    1
);

-- 查看创建的测试流水线
SELECT 
    '=== 超时逻辑测试流水线 ===' as title;

SELECT 
    rownum,
    stepname,
    timeoutms,
    timeoutms / 1000 as timeout_seconds,
    retrycount,
    continueonerror,
    LEFT(commandtext, 50) as command_preview
FROM Sa_SshPipelinesItem 
WHERE pid = 'pipeline-timeout-test-001'
ORDER BY rownum;

-- 验证Docker安装步骤的超时配置
SELECT 
    '=== Docker安装步骤超时验证 ===' as title;

SELECT 
    stepname,
    timeoutms,
    timeoutms / 1000 as timeout_seconds,
    retrycount,
    continueonerror
FROM Sa_SshPipelinesItem 
WHERE id = 'step-docker-check-003';

-- 使用说明
SELECT 
    '=== 测试说明 ===' as title;

SELECT 
    '1. 执行"超时逻辑测试流水线"' as instruction
UNION ALL
SELECT 
    '2. 观察每个步骤的超时配置显示' as instruction
UNION ALL
SELECT 
    '3. 验证步骤是否按配置的超时时间执行' as instruction
UNION ALL
SELECT 
    '4. 检查第5步是否会在15秒后超时' as instruction
UNION ALL
SELECT 
    '5. 确认Docker安装步骤的300秒超时是否生效' as instruction;
