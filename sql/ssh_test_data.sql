-- Test data for SSH pipeline execution system
-- Created: 2025-05-26

-- 1. Add test server configuration
INSERT INTO Sa_SshServers (
    id, servername, host, port, username, password, 
    groupname, rownum, remark, 
    createby, createbyid, createdate, 
    lister, listerid, modifydate, 
    revision
) VALUES (
    'server-test-001',                       -- id
    'Docker测试服务器',                      -- servername
    '*************',                         -- host (replace with your actual server IP)
    22,                                      -- port
    'ubuntu',                                -- username (replace with your actual username)
    'encrypted_password_here',               -- password (replace with properly encrypted password)
    'test',                                  -- groupname
    1,                                       -- rownum
    'Docker功能测试专用服务器',              -- remark
    'System',                                -- createby
    'system',                                -- createbyid
    NOW(),                                   -- createdate
    'System',                                -- lister
    'system',                                -- listerid
    NOW(),                                   -- modifydate
    1                                        -- revision
);

-- 2. Create Docker check and Hello World pipeline
INSERT INTO Sa_SshPipelines (
    id, pipelinename, description, category, 
    issystem, rownum, remark,
    createby, createbyid, createdate,
    lister, listerid, modifydate,
    revision
) VALUES (
    'pipeline-docker-check-001',             -- id
    'Docker检查与HelloWorld测试',            -- pipelinename
    '检查Docker是否安装并运行HelloWorld容器', -- description
    '系统检查',                              -- category
    0,                                       -- issystem
    1,                                       -- rownum
    'Docker功能验证流水线',                  -- remark
    'System',                                -- createby
    'system',                                -- createbyid
    NOW(),                                   -- createdate
    'System',                                -- lister
    'system',                                -- listerid
    NOW(),                                   -- modifydate
    1                                        -- revision
);

-- 3. Add pipeline steps
-- Step 1: Check if Docker is installed
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext,
    successpattern, errorpattern, timeoutms,
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-docker-check-001',                 -- id
    'pipeline-docker-check-001',             -- pid
    '检查Docker是否安装',                    -- stepname
    'docker --version',                      -- commandtext
    '(?i).*(docker version).*',             -- successpattern (simplified)
    '(?i).*(command not found|未找到命令|not installed).*', -- errorpattern (added Chinese)
    10000,                                   -- timeoutms (10 seconds)
    1,                                       -- continueonerror (continue even if not found)
    1,                                       -- retrycount (allow 1 retry)
    1,                                       -- rownum
    '验证Docker CLI是否可用',                -- remark
    1                                        -- revision
);

-- Step 2: Check Docker service status
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext,
    successpattern, errorpattern, timeoutms,
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-docker-check-002',                 -- id
    'pipeline-docker-check-001',             -- pid
    '检查Docker服务状态',                    -- stepname
    'systemctl is-active docker 2>/dev/null || echo "inactive"',  -- commandtext (simplified)
    '(?i).*(active|running).*',              -- successpattern
    '(?i).*(inactive|failed|not-found|未找到).*',   -- errorpattern (added Chinese)
    15000,                                   -- timeoutms (15 seconds)
    1,                                       -- continueonerror (continue even if not running)
    2,                                       -- retrycount (allow 2 retries)
    2,                                       -- rownum
    '验证Docker服务是否运行中',              -- remark
    1                                        -- revision
);

-- Step 3: Install Docker if not installed
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext,
    successpattern, errorpattern, timeoutms,
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-docker-check-003',                 -- id
    'pipeline-docker-check-001',             -- pid
    '安装Docker(如果未安装)',                -- stepname
    'if ! command -v docker &> /dev/null; then echo "Installing Docker"; apt-get update && apt-get install -y docker.io; else echo "Docker already installed"; fi',  -- commandtext (removed sudo for compatibility)
    '(?i).*(docker already installed|complete|done|installed|设置).*',  -- successpattern (added Chinese)
    '(?i).*(unable to locate|error|failed|permission denied|无法定位|错误|失败).*',   -- errorpattern (added Chinese)
    300000,                                  -- timeoutms (5 minutes)
    0,                                       -- continueonerror (stop if installation fails)
    2,                                       -- retrycount (allow 2 retries for network issues)
    3,                                       -- rownum
    '如果Docker未安装则进行安装',            -- remark
    1                                        -- revision
);

-- Step 4: Start Docker service if not running
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext,
    successpattern, errorpattern, timeoutms,
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-docker-check-004',                 -- id
    'pipeline-docker-check-001',             -- pid
    '启动Docker服务(如果未运行)',            -- stepname
    'if ! systemctl is-active docker &> /dev/null; then systemctl start docker && systemctl enable docker && echo "Docker started"; else echo "Docker already running"; fi',  -- commandtext (removed sudo)
    '(?i).*(docker started|docker already running|enabled|已启动).*',  -- successpattern (added Chinese)
    '(?i).*(failed to start|permission denied|not found|启动失败|权限拒绝).*',    -- errorpattern (added Chinese)
    30000,                                   -- timeoutms (30 seconds)
    0,                                       -- continueonerror (stop if cannot start)
    3,                                       -- retrycount (allow 3 retries for service start)
    4,                                       -- rownum
    '如果Docker服务未运行则启动',            -- remark
    1                                        -- revision
);

-- Step 5: Run Docker Hello World container
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext,
    successpattern, errorpattern, timeoutms,
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-docker-check-005',                 -- id
    'pipeline-docker-check-001',             -- pid
    '运行Hello World容器',                   -- stepname
    'docker run --rm hello-world',           -- commandtext (removed sudo)
    '(?i).*(hello from docker|this message shows).*',  -- successpattern
    '(?i).*(error|not found|permission denied|cannot connect|错误|未找到|权限拒绝).*',  -- errorpattern (added Chinese)
    60000,                                   -- timeoutms (1 minute)
    0,                                       -- continueonerror
    2,                                       -- retrycount (allow 2 retries for network issues)
    5,                                       -- rownum
    '运行Docker Hello World验证容器功能',    -- remark
    1                                        -- revision
);

-- Step 6: Show final status
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext,
    successpattern, errorpattern, timeoutms,
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-docker-check-006',                 -- id
    'pipeline-docker-check-001',             -- pid
    '显示Docker最终状态',                    -- stepname
    'echo "=== Docker状态检查完成 ===" && docker --version 2>/dev/null || echo "Docker未安装" && systemctl is-active docker 2>/dev/null || echo "Docker服务未运行"',  -- commandtext
    '(?i).*(docker状态检查完成).*',           -- successpattern
    null,                                    -- errorpattern
    15000,                                   -- timeoutms (15 seconds)
    1,                                       -- continueonerror (always continue)
    0,                                       -- retrycount
    6,                                       -- rownum
    '显示Docker安装和运行状态的总结',         -- remark
    1                                        -- revision
);

-- Step 6: Show Docker info
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext, 
    successpattern, errorpattern, timeoutms, 
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-docker-check-006',                 -- id
    'pipeline-docker-check-001',             -- pid
    '显示Docker系统信息',                    -- stepname
    'sudo docker info',                      -- commandtext
    'Containers:|Images:',                   -- successpattern
    'Error',                                 -- errorpattern
    20000,                                   -- timeoutms (20 seconds)
    0,                                       -- continueonerror
    0,                                       -- retrycount
    6,                                       -- rownum
    '输出Docker系统信息以供查看',            -- remark
    1                                        -- revision
);
