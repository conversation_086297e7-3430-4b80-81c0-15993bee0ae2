-- 测试实时输出的流水线配置
-- 创建一个专门用于测试实时输出的流水线

-- 删除旧的测试流水线（如果存在）
DELETE FROM Sa_SshPipelinesItem WHERE pid = 'pipeline-realtime-test-001';
DELETE FROM Sa_SshPipelines WHERE id = 'pipeline-realtime-test-001';

-- 创建测试流水线
INSERT INTO Sa_SshPipelines (
    id, pipelinename, description, category, issystem, rownum, remark,
    createby, createbyid, createdate, lister, listerid, modifydate,
    revision
) VALUES (
    'pipeline-realtime-test-001',
    '实时输出测试流水线',
    '测试SSH命令的实时输出显示功能',
    '测试',
    1,
    99,
    '用于验证实时输出和步骤状态显示',
    'System',
    'system',
    NOW(),
    'System',
    'system',
    NOW(),
    1
);

-- 步骤1：快速命令测试
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext, 
    successpattern, errorpattern, timeoutms, 
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-realtime-test-001',
    'pipeline-realtime-test-001',
    '快速命令测试',
    'echo "开始测试..." && sleep 2 && echo "测试完成"',
    '(?i).*(测试完成).*',
    null,
    10000,
    1,
    0,
    1,
    '测试基本的实时输出',
    1
);

-- 步骤2：长时间命令测试
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext, 
    successpattern, errorpattern, timeoutms, 
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-realtime-test-002',
    'pipeline-realtime-test-001',
    '长时间命令测试',
    'for i in {1..5}; do echo "处理步骤 $i/5..."; sleep 3; done && echo "所有步骤完成"',
    '(?i).*(所有步骤完成).*',
    null,
    30000,
    1,
    0,
    2,
    '测试长时间运行命令的实时输出',
    1
);

-- 步骤3：模拟安装过程
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext, 
    successpattern, errorpattern, timeoutms, 
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-realtime-test-003',
    'pipeline-realtime-test-001',
    '模拟安装过程',
    'echo "开始模拟安装..." && for pkg in "基础包" "依赖包" "主程序"; do echo "正在安装 $pkg..."; sleep 2; echo "$pkg 安装完成"; done && echo "✅ 安装成功完成"',
    '(?i).*(安装成功完成).*',
    '(?i).*(安装失败|error|failed).*',
    60000,
    1,
    2,
    3,
    '模拟软件安装过程的实时输出',
    1
);

-- 步骤4：错误重试测试
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext, 
    successpattern, errorpattern, timeoutms, 
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-realtime-test-004',
    'pipeline-realtime-test-001',
    '错误重试测试',
    'echo "尝试连接服务..." && if [ $((RANDOM % 3)) -eq 0 ]; then echo "✅ 连接成功"; else echo "❌ 连接失败，请重试" && exit 1; fi',
    '(?i).*(连接成功).*',
    '(?i).*(连接失败).*',
    15000,
    1,
    3,
    4,
    '测试重试机制和错误处理',
    1
);

-- 步骤5：最终状态检查
INSERT INTO Sa_SshPipelinesItem (
    id, pid, stepname, commandtext, 
    successpattern, errorpattern, timeoutms, 
    continueonerror, retrycount, rownum, remark,
    revision
) VALUES (
    'step-realtime-test-005',
    'pipeline-realtime-test-001',
    '最终状态检查',
    'echo "=== 测试总结 ===" && echo "✅ 实时输出测试" && echo "✅ 长时间命令测试" && echo "✅ 模拟安装测试" && echo "✅ 重试机制测试" && echo "🎉 所有测试完成"',
    '(?i).*(所有测试完成).*',
    null,
    10000,
    1,
    0,
    5,
    '显示测试结果总结',
    1
);

-- 查看创建的测试流水线
SELECT 
    '=== 实时输出测试流水线 ===' as title;

SELECT 
    rownum,
    stepname,
    LEFT(commandtext, 50) as command_preview,
    timeoutms / 1000 as timeout_seconds,
    continueonerror,
    retrycount
FROM Sa_SshPipelinesItem 
WHERE pid = 'pipeline-realtime-test-001'
ORDER BY rownum;

-- 使用说明
SELECT 
    '=== 使用说明 ===' as title;

SELECT 
    '1. 在前端选择"实时输出测试流水线"' as instruction
UNION ALL
SELECT 
    '2. 点击执行，观察实时输出效果' as instruction
UNION ALL
SELECT 
    '3. 注意观察步骤开始、进行中、完成的状态' as instruction
UNION ALL
SELECT 
    '4. 检查重试机制是否正常工作' as instruction
UNION ALL
SELECT 
    '5. 验证长时间命令的实时输出显示' as instruction;
