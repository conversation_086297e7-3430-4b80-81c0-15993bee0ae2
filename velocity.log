2025-07-29 13:06:04,065 - Initializing Velocity, Calling init()...
2025-07-29 13:06:04,065 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:06:04,065 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:06:04,065 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:06:04,065 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-07-29 13:06:04,065 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:06:04,065 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:06:04,068 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:06:04,069 - Do unicode file recognition:  false
2025-07-29 13:06:04,069 - FileResourceLoader : adding path '.'
2025-07-29 13:06:04,076 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:06:04,080 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:06:04,081 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:06:04,081 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:06:04,082 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:06:04,082 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:06:04,083 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:06:04,084 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:06:04,084 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:06:04,085 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:06:04,096 - Created '20' parsers.
2025-07-29 13:06:04,098 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:06:04,098 - Velocimacro : Default library not found.
2025-07-29 13:06:04,098 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:06:04,098 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:06:04,098 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:06:04,098 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:06:18,214 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:06:18,214 - Initializing Velocity, Calling init()...
2025-07-29 13:06:18,215 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:06:18,215 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:06:18,215 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:06:18,215 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:06:18,215 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:06:18,215 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:06:18,215 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:06:18,215 - Do unicode file recognition:  false
2025-07-29 13:06:18,215 - FileResourceLoader : adding path '.'
2025-07-29 13:06:18,215 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:06:18,216 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:06:18,216 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:06:18,216 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:06:18,216 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:06:18,217 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:06:18,217 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:06:18,217 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:06:18,217 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:06:18,217 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:06:18,218 - Created '20' parsers.
2025-07-29 13:06:18,218 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:06:18,218 - Velocimacro : Default library not found.
2025-07-29 13:06:18,218 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:06:18,218 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:06:18,218 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:06:18,218 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:06:18,243 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:06:18,243 - Initializing Velocity, Calling init()...
2025-07-29 13:06:18,243 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:06:18,243 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:06:18,243 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:06:18,243 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:06:18,243 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:06:18,243 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:06:18,243 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:06:18,243 - Do unicode file recognition:  false
2025-07-29 13:06:18,243 - FileResourceLoader : adding path '.'
2025-07-29 13:06:18,243 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:06:18,244 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:06:18,244 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:06:18,244 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:06:18,244 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:06:18,244 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:06:18,244 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:06:18,244 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:06:18,244 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:06:18,244 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:06:18,245 - Created '20' parsers.
2025-07-29 13:06:18,245 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:06:18,245 - Velocimacro : Default library not found.
2025-07-29 13:06:18,245 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:06:18,245 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:06:18,245 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:06:18,246 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:16:14,676 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:16:14,677 - Initializing Velocity, Calling init()...
2025-07-29 13:16:14,677 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:16:14,677 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:16:14,678 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:16:14,678 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:16:14,678 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:16:14,678 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:16:14,678 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:16:14,678 - Do unicode file recognition:  false
2025-07-29 13:16:14,678 - FileResourceLoader : adding path '.'
2025-07-29 13:16:14,680 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:16:14,680 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:16:14,680 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:16:14,680 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:16:14,680 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:16:14,681 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:16:14,681 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:16:14,681 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:16:14,681 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:16:14,681 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:16:14,682 - Created '20' parsers.
2025-07-29 13:16:14,682 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:16:14,682 - Velocimacro : Default library not found.
2025-07-29 13:16:14,682 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:16:14,682 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:16:14,682 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:16:14,682 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:16:14,686 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:16:14,686 - Initializing Velocity, Calling init()...
2025-07-29 13:16:14,686 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:16:14,686 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:16:14,686 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:16:14,686 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:16:14,686 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:16:14,686 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:16:14,686 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:16:14,686 - Do unicode file recognition:  false
2025-07-29 13:16:14,686 - FileResourceLoader : adding path '.'
2025-07-29 13:16:14,687 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:16:14,688 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:16:14,688 - Created '20' parsers.
2025-07-29 13:16:14,689 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:16:14,689 - Velocimacro : Default library not found.
2025-07-29 13:16:14,689 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:16:14,689 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:16:14,689 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:16:14,689 - Velocimacro : autoload off : VM system will not automatically reload global library macros
pache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:16:14,678 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:16:14,678 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:16:14,678 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:16:14,678 - Do unicode file recognition:  false
2025-07-29 13:16:14,678 - Do unicode file recognition:  false
2025-07-29 13:16:14,678 - Do unicode file recognition:  false
2025-07-29 13:16:14,678 - FileResourceLoader : adding path '.'
2025-07-29 13:16:14,678 - FileResourceLoader : adding path '.'
2025-07-29 13:16:14,678 - FileResourceLoader : adding path '.'
2025-07-29 13:16:14,680 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:16:14,680 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:16:14,680 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:16:14,680 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:16:14,680 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:16:14,680 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:16:14,680 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:16:14,680 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:16:14,680 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:16:14,680 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:16:14,680 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:16:14,680 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:16:14,680 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:16:14,680 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:16:14,680 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:16:14,681 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:16:14,681 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:16:14,681 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:16:14,681 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:16:14,681 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:16:14,681 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:16:14,681 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:16:14,681 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:16:14,681 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:16:14,681 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:16:14,681 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:16:14,681 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:16:14,681 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:16:14,681 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:16:14,681 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:16:14,682 - Created '20' parsers.
2025-07-29 13:16:14,682 - Created '20' parsers.
2025-07-29 13:16:14,682 - Created '20' parsers.
2025-07-29 13:16:14,682 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:16:14,682 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:16:14,682 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:16:14,682 - Velocimacro : Default library not found.
2025-07-29 13:16:14,682 - Velocimacro : Default library not found.
2025-07-29 13:16:14,682 - Velocimacro : Default library not found.
2025-07-29 13:16:14,682 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:16:14,682 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:16:14,682 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:16:14,682 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:16:14,682 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:16:14,682 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:16:14,682 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:16:14,682 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:16:14,682 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:16:14,682 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:16:14,682 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:16:14,682 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:16:14,686 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:16:14,686 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:16:14,686 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:16:14,686 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:16:14,686 - Initializing Velocity, Calling init()...
2025-07-29 13:16:14,686 - Initializing Velocity, Calling init()...
2025-07-29 13:16:14,686 - Initializing Velocity, Calling init()...
2025-07-29 13:16:14,686 - Initializing Velocity, Calling init()...
2025-07-29 13:16:14,686 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:16:14,686 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:16:14,686 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:16:14,686 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:16:14,686 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:16:14,686 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:16:14,686 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:16:14,686 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:16:14,686 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:16:14,686 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:16:14,686 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:16:14,686 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:16:14,686 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:16:14,686 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:16:14,686 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:16:14,686 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:16:14,686 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:16:14,686 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:16:14,686 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:16:14,686 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:16:14,686 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:16:14,686 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:16:14,686 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:16:14,686 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:16:14,686 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:16:14,686 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:16:14,686 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:16:14,686 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:16:14,686 - Do unicode file recognition:  false
2025-07-29 13:16:14,686 - Do unicode file recognition:  false
2025-07-29 13:16:14,686 - Do unicode file recognition:  false
2025-07-29 13:16:14,686 - Do unicode file recognition:  false
2025-07-29 13:16:14,686 - FileResourceLoader : adding path '.'
2025-07-29 13:16:14,686 - FileResourceLoader : adding path '.'
2025-07-29 13:16:14,686 - FileResourceLoader : adding path '.'
2025-07-29 13:16:14,686 - FileResourceLoader : adding path '.'
2025-07-29 13:16:14,687 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:16:14,687 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:16:14,687 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:16:14,687 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:16:14,687 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:16:14,688 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:16:14,688 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:16:14,688 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:16:14,688 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:16:14,688 - Created '20' parsers.
2025-07-29 13:16:14,688 - Created '20' parsers.
2025-07-29 13:16:14,688 - Created '20' parsers.
2025-07-29 13:16:14,688 - Created '20' parsers.
2025-07-29 13:16:14,689 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:16:14,689 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:16:14,689 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:16:14,689 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:16:14,689 - Velocimacro : Default library not found.
2025-07-29 13:16:14,689 - Velocimacro : Default library not found.
2025-07-29 13:16:14,689 - Velocimacro : Default library not found.
2025-07-29 13:16:14,689 - Velocimacro : Default library not found.
2025-07-29 13:16:14,689 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:16:14,689 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:16:14,689 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:16:14,689 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:16:14,689 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:16:14,689 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:16:14,689 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:16:14,689 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:16:14,689 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:16:14,689 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:16:14,689 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:16:14,689 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:16:14,689 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:16:14,689 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:16:14,689 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:16:14,689 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:19:47,303 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:19:47,303 - Initializing Velocity, Calling init()...
2025-07-29 13:19:47,303 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:19:47,303 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:19:47,303 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:19:47,303 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-07-29 13:19:47,303 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:19:47,304 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:19:47,306 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:19:47,307 - Do unicode file recognition:  false
2025-07-29 13:19:47,307 - FileResourceLoader : adding path '.'
2025-07-29 13:19:47,315 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:19:47,317 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:19:47,318 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:19:47,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:19:47,319 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:19:47,320 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:19:47,320 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:19:47,321 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:19:47,322 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:19:47,322 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:19:47,333 - Created '20' parsers.
2025-07-29 13:19:47,335 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:19:47,335 - Velocimacro : Default library not found.
2025-07-29 13:19:47,335 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:19:47,335 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:19:47,335 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:19:47,335 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:20:01,169 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:20:01,169 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:20:01,170 - Initializing Velocity, Calling init()...
2025-07-29 13:20:01,170 - Initializing Velocity, Calling init()...
2025-07-29 13:20:01,170 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:20:01,170 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:20:01,170 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:20:01,170 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:20:01,170 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:20:01,170 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:20:01,170 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:20:01,170 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:20:01,170 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:20:01,170 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:20:01,170 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:20:01,170 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:20:01,170 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:20:01,170 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:20:01,170 - Do unicode file recognition:  false
2025-07-29 13:20:01,170 - Do unicode file recognition:  false
2025-07-29 13:20:01,170 - FileResourceLoader : adding path '.'
2025-07-29 13:20:01,170 - FileResourceLoader : adding path '.'
2025-07-29 13:20:01,170 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:20:01,170 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:20:01,171 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:20:01,171 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:20:01,171 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:20:01,171 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:20:01,171 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:20:01,171 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:20:01,171 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:20:01,171 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:20:01,171 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:20:01,171 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:20:01,171 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:20:01,171 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:20:01,171 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:20:01,171 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:20:01,171 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:20:01,171 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:20:01,171 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:20:01,171 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:20:01,172 - Created '20' parsers.
2025-07-29 13:20:01,172 - Created '20' parsers.
2025-07-29 13:20:01,172 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:20:01,172 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:20:01,172 - Velocimacro : Default library not found.
2025-07-29 13:20:01,172 - Velocimacro : Default library not found.
2025-07-29 13:20:01,172 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:20:01,172 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:20:01,172 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:20:01,172 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:20:01,172 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:20:01,172 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:20:01,172 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:20:01,172 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:20:01,189 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:20:01,189 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:20:01,189 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:20:01,189 - Initializing Velocity, Calling init()...
2025-07-29 13:20:01,189 - Initializing Velocity, Calling init()...
2025-07-29 13:20:01,189 - Initializing Velocity, Calling init()...
2025-07-29 13:20:01,189 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:20:01,189 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:20:01,189 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:20:01,189 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:20:01,189 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:20:01,189 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:20:01,189 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:20:01,189 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:20:01,189 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:20:01,189 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:20:01,189 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:20:01,189 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:20:01,189 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:20:01,189 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:20:01,189 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:20:01,189 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:20:01,189 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:20:01,189 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:20:01,189 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:20:01,189 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:20:01,189 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:20:01,189 - Do unicode file recognition:  false
2025-07-29 13:20:01,189 - Do unicode file recognition:  false
2025-07-29 13:20:01,189 - Do unicode file recognition:  false
2025-07-29 13:20:01,189 - FileResourceLoader : adding path '.'
2025-07-29 13:20:01,189 - FileResourceLoader : adding path '.'
2025-07-29 13:20:01,189 - FileResourceLoader : adding path '.'
2025-07-29 13:20:01,189 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:20:01,189 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:20:01,189 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:20:01,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:20:01,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:20:01,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:20:01,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:20:01,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:20:01,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:20:01,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:20:01,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:20:01,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:20:01,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:20:01,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:20:01,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:20:01,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:20:01,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:20:01,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:20:01,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:20:01,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:20:01,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:20:01,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:20:01,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:20:01,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:20:01,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:20:01,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:20:01,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:20:01,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:20:01,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:20:01,190 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:20:01,191 - Created '20' parsers.
2025-07-29 13:20:01,191 - Created '20' parsers.
2025-07-29 13:20:01,191 - Created '20' parsers.
2025-07-29 13:20:01,191 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:20:01,191 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:20:01,191 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:20:01,191 - Velocimacro : Default library not found.
2025-07-29 13:20:01,191 - Velocimacro : Default library not found.
2025-07-29 13:20:01,191 - Velocimacro : Default library not found.
2025-07-29 13:20:01,191 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:20:01,191 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:20:01,191 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:20:01,191 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:20:01,191 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:20:01,191 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:20:01,191 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:20:01,191 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:20:01,191 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:20:01,191 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:20:01,191 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:20:01,191 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:20:19,966 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:20:19,966 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:20:19,966 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:20:19,966 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:20:19,967 - Initializing Velocity, Calling init()...
2025-07-29 13:20:19,967 - Initializing Velocity, Calling init()...
2025-07-29 13:20:19,967 - Initializing Velocity, Calling init()...
2025-07-29 13:20:19,967 - Initializing Velocity, Calling init()...
2025-07-29 13:20:19,967 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:20:19,967 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:20:19,967 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:20:19,967 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:20:19,967 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:20:19,967 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:20:19,967 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:20:19,967 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:20:19,967 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:20:19,967 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:20:19,967 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:20:19,967 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:20:19,967 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:20:19,967 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:20:19,967 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:20:19,967 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:20:19,967 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:20:19,967 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:20:19,967 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:20:19,967 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:20:19,967 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:20:19,967 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:20:19,967 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:20:19,967 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:20:19,967 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:20:19,967 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:20:19,967 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:20:19,967 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:20:19,967 - Do unicode file recognition:  false
2025-07-29 13:20:19,967 - Do unicode file recognition:  false
2025-07-29 13:20:19,967 - Do unicode file recognition:  false
2025-07-29 13:20:19,967 - Do unicode file recognition:  false
2025-07-29 13:20:19,967 - FileResourceLoader : adding path '.'
2025-07-29 13:20:19,967 - FileResourceLoader : adding path '.'
2025-07-29 13:20:19,967 - FileResourceLoader : adding path '.'
2025-07-29 13:20:19,967 - FileResourceLoader : adding path '.'
2025-07-29 13:20:19,969 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:20:19,969 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:20:19,969 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:20:19,969 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:20:19,969 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:20:19,969 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:20:19,969 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:20:19,969 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:20:19,969 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:20:19,969 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:20:19,969 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:20:19,969 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:20:19,969 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:20:19,969 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:20:19,969 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:20:19,969 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:20:19,969 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:20:19,969 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:20:19,969 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:20:19,969 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:20:19,969 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:20:19,969 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:20:19,969 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:20:19,969 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:20:19,969 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:20:19,969 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:20:19,969 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:20:19,969 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:20:19,969 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:20:19,969 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:20:19,969 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:20:19,969 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:20:19,970 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:20:19,970 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:20:19,970 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:20:19,970 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:20:19,970 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:20:19,970 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:20:19,970 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:20:19,970 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:20:19,970 - Created '20' parsers.
2025-07-29 13:20:19,970 - Created '20' parsers.
2025-07-29 13:20:19,970 - Created '20' parsers.
2025-07-29 13:20:19,970 - Created '20' parsers.
2025-07-29 13:20:19,970 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:20:19,970 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:20:19,970 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:20:19,970 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:20:19,971 - Velocimacro : Default library not found.
2025-07-29 13:20:19,971 - Velocimacro : Default library not found.
2025-07-29 13:20:19,971 - Velocimacro : Default library not found.
2025-07-29 13:20:19,971 - Velocimacro : Default library not found.
2025-07-29 13:20:19,971 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:20:19,971 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:20:19,971 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:20:19,971 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:20:19,971 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:20:19,971 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:20:19,971 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:20:19,971 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:20:19,971 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:20:19,971 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:20:19,971 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:20:19,971 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:20:19,971 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:20:19,971 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:20:19,971 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:20:19,971 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:20:19,974 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:20:19,974 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:20:19,974 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:20:19,974 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:20:19,974 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:20:19,975 - Initializing Velocity, Calling init()...
2025-07-29 13:20:19,975 - Initializing Velocity, Calling init()...
2025-07-29 13:20:19,975 - Initializing Velocity, Calling init()...
2025-07-29 13:20:19,975 - Initializing Velocity, Calling init()...
2025-07-29 13:20:19,975 - Initializing Velocity, Calling init()...
2025-07-29 13:20:19,975 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:20:19,975 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:20:19,975 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:20:19,975 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:20:19,975 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:20:19,975 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:20:19,975 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:20:19,975 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:20:19,975 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:20:19,975 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:20:19,975 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:20:19,975 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:20:19,975 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:20:19,975 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:20:19,975 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:20:19,975 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:20:19,975 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:20:19,975 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:20:19,975 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:20:19,975 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:20:19,975 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:20:19,975 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:20:19,975 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:20:19,975 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:20:19,975 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:20:19,975 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:20:19,975 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:20:19,975 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:20:19,975 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:20:19,975 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:20:19,975 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:20:19,975 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:20:19,975 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:20:19,975 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:20:19,975 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:20:19,975 - Do unicode file recognition:  false
2025-07-29 13:20:19,975 - Do unicode file recognition:  false
2025-07-29 13:20:19,975 - Do unicode file recognition:  false
2025-07-29 13:20:19,975 - Do unicode file recognition:  false
2025-07-29 13:20:19,975 - Do unicode file recognition:  false
2025-07-29 13:20:19,975 - FileResourceLoader : adding path '.'
2025-07-29 13:20:19,975 - FileResourceLoader : adding path '.'
2025-07-29 13:20:19,975 - FileResourceLoader : adding path '.'
2025-07-29 13:20:19,975 - FileResourceLoader : adding path '.'
2025-07-29 13:20:19,975 - FileResourceLoader : adding path '.'
2025-07-29 13:20:19,975 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:20:19,975 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:20:19,975 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:20:19,975 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:20:19,975 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:20:19,976 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:20:19,978 - Created '20' parsers.
2025-07-29 13:20:19,978 - Created '20' parsers.
2025-07-29 13:20:19,978 - Created '20' parsers.
2025-07-29 13:20:19,978 - Created '20' parsers.
2025-07-29 13:20:19,978 - Created '20' parsers.
2025-07-29 13:20:19,978 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:20:19,978 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:20:19,978 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:20:19,978 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:20:19,978 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:20:19,978 - Velocimacro : Default library not found.
2025-07-29 13:20:19,978 - Velocimacro : Default library not found.
2025-07-29 13:20:19,978 - Velocimacro : Default library not found.
2025-07-29 13:20:19,978 - Velocimacro : Default library not found.
2025-07-29 13:20:19,978 - Velocimacro : Default library not found.
2025-07-29 13:20:19,978 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:20:19,978 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:20:19,978 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:20:19,978 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:20:19,978 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:20:19,978 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:20:19,978 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:20:19,978 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:20:19,978 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:20:19,978 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:20:19,978 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:20:19,978 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:20:19,978 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:20:19,978 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:20:19,978 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:20:19,978 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:20:19,978 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:20:19,978 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:20:19,978 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:20:19,978 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:33:17,340 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:33:17,340 - Initializing Velocity, Calling init()...
2025-07-29 13:33:17,340 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:33:17,341 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:33:17,341 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:33:17,341 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-07-29 13:33:17,341 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:33:17,341 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:33:17,347 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:33:17,349 - Do unicode file recognition:  false
2025-07-29 13:33:17,349 - FileResourceLoader : adding path '.'
2025-07-29 13:33:17,365 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:33:17,369 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:33:17,371 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:33:17,371 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:33:17,372 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:33:17,372 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:33:17,372 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:33:17,374 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:33:17,374 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:33:17,375 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:33:17,388 - Created '20' parsers.
2025-07-29 13:33:17,390 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:33:17,390 - Velocimacro : Default library not found.
2025-07-29 13:33:17,390 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:33:17,390 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:33:17,390 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:33:17,390 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:33:41,355 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:33:41,355 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:33:41,355 - Initializing Velocity, Calling init()...
2025-07-29 13:33:41,355 - Initializing Velocity, Calling init()...
2025-07-29 13:33:41,355 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:33:41,355 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:33:41,355 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:33:41,355 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:33:41,356 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:33:41,356 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:33:41,356 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:33:41,356 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:33:41,356 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:33:41,356 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:33:41,356 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:33:41,356 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:33:41,356 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:33:41,356 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:33:41,356 - Do unicode file recognition:  false
2025-07-29 13:33:41,356 - Do unicode file recognition:  false
2025-07-29 13:33:41,356 - FileResourceLoader : adding path '.'
2025-07-29 13:33:41,356 - FileResourceLoader : adding path '.'
2025-07-29 13:33:41,356 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:33:41,356 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:33:41,357 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:33:41,357 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:33:41,357 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:33:41,357 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:33:41,357 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:33:41,357 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:33:41,357 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:33:41,357 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:33:41,357 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:33:41,357 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:33:41,357 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:33:41,357 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:33:41,357 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:33:41,357 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:33:41,357 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:33:41,357 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:33:41,358 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:33:41,358 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:33:41,359 - Created '20' parsers.
2025-07-29 13:33:41,359 - Created '20' parsers.
2025-07-29 13:33:41,359 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:33:41,359 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:33:41,359 - Velocimacro : Default library not found.
2025-07-29 13:33:41,359 - Velocimacro : Default library not found.
2025-07-29 13:33:41,359 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:33:41,359 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:33:41,359 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:33:41,359 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:33:41,359 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:33:41,359 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:33:41,359 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:33:41,359 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:33:41,387 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:33:41,387 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:33:41,387 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:33:41,387 - Initializing Velocity, Calling init()...
2025-07-29 13:33:41,387 - Initializing Velocity, Calling init()...
2025-07-29 13:33:41,387 - Initializing Velocity, Calling init()...
2025-07-29 13:33:41,387 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:33:41,387 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:33:41,387 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:33:41,387 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:33:41,387 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:33:41,387 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:33:41,387 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:33:41,387 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:33:41,387 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:33:41,387 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:33:41,387 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:33:41,387 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:33:41,387 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:33:41,387 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:33:41,387 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:33:41,387 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:33:41,387 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:33:41,387 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:33:41,388 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:33:41,388 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:33:41,388 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:33:41,388 - Do unicode file recognition:  false
2025-07-29 13:33:41,388 - Do unicode file recognition:  false
2025-07-29 13:33:41,388 - Do unicode file recognition:  false
2025-07-29 13:33:41,388 - FileResourceLoader : adding path '.'
2025-07-29 13:33:41,388 - FileResourceLoader : adding path '.'
2025-07-29 13:33:41,388 - FileResourceLoader : adding path '.'
2025-07-29 13:33:41,388 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:33:41,388 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:33:41,388 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:33:41,388 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:33:41,388 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:33:41,388 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:33:41,389 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:33:41,389 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:33:41,389 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:33:41,389 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:33:41,389 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:33:41,389 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:33:41,389 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:33:41,389 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:33:41,389 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:33:41,389 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:33:41,389 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:33:41,389 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:33:41,389 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:33:41,389 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:33:41,389 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:33:41,389 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:33:41,389 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:33:41,389 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:33:41,389 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:33:41,389 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:33:41,389 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:33:41,389 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:33:41,389 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:33:41,389 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:33:41,390 - Created '20' parsers.
2025-07-29 13:33:41,390 - Created '20' parsers.
2025-07-29 13:33:41,390 - Created '20' parsers.
2025-07-29 13:33:41,390 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:33:41,390 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:33:41,390 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:33:41,390 - Velocimacro : Default library not found.
2025-07-29 13:33:41,390 - Velocimacro : Default library not found.
2025-07-29 13:33:41,390 - Velocimacro : Default library not found.
2025-07-29 13:33:41,390 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:33:41,390 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:33:41,390 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:33:41,390 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:33:41,390 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:33:41,390 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:33:41,390 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:33:41,390 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:33:41,390 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:33:41,390 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:33:41,390 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:33:41,390 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:34:36,470 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:34:36,470 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:34:36,470 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:34:36,470 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:34:36,471 - Initializing Velocity, Calling init()...
2025-07-29 13:34:36,471 - Initializing Velocity, Calling init()...
2025-07-29 13:34:36,471 - Initializing Velocity, Calling init()...
2025-07-29 13:34:36,471 - Initializing Velocity, Calling init()...
2025-07-29 13:34:36,471 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:34:36,471 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:34:36,471 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:34:36,471 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:34:36,472 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:34:36,472 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:34:36,472 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:34:36,472 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:34:36,472 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:34:36,472 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:34:36,472 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:34:36,472 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:34:36,472 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:34:36,472 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:34:36,472 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:34:36,472 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:34:36,472 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:34:36,472 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:34:36,472 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:34:36,472 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:34:36,472 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:34:36,472 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:34:36,472 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:34:36,472 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:34:36,472 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:34:36,472 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:34:36,472 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:34:36,472 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:34:36,472 - Do unicode file recognition:  false
2025-07-29 13:34:36,472 - Do unicode file recognition:  false
2025-07-29 13:34:36,472 - Do unicode file recognition:  false
2025-07-29 13:34:36,472 - Do unicode file recognition:  false
2025-07-29 13:34:36,472 - FileResourceLoader : adding path '.'
2025-07-29 13:34:36,472 - FileResourceLoader : adding path '.'
2025-07-29 13:34:36,472 - FileResourceLoader : adding path '.'
2025-07-29 13:34:36,472 - FileResourceLoader : adding path '.'
2025-07-29 13:34:36,473 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:34:36,473 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:34:36,473 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:34:36,473 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:34:36,473 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:34:36,473 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:34:36,473 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:34:36,473 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:34:36,473 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:34:36,473 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:34:36,473 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:34:36,473 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:34:36,473 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:34:36,473 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:34:36,473 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:34:36,473 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:34:36,473 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:34:36,473 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:34:36,473 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:34:36,473 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:34:36,473 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:34:36,473 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:34:36,473 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:34:36,473 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:34:36,473 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:34:36,473 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:34:36,473 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:34:36,473 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:34:36,473 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:34:36,473 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:34:36,473 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:34:36,473 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:34:36,473 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:34:36,473 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:34:36,473 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:34:36,473 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:34:36,474 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:34:36,474 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:34:36,474 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:34:36,474 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:34:36,474 - Created '20' parsers.
2025-07-29 13:34:36,474 - Created '20' parsers.
2025-07-29 13:34:36,474 - Created '20' parsers.
2025-07-29 13:34:36,474 - Created '20' parsers.
2025-07-29 13:34:36,474 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:34:36,474 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:34:36,474 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:34:36,474 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:34:36,474 - Velocimacro : Default library not found.
2025-07-29 13:34:36,474 - Velocimacro : Default library not found.
2025-07-29 13:34:36,474 - Velocimacro : Default library not found.
2025-07-29 13:34:36,474 - Velocimacro : Default library not found.
2025-07-29 13:34:36,474 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:34:36,474 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:34:36,474 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:34:36,474 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:34:36,475 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:34:36,475 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:34:36,475 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:34:36,475 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:34:36,475 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:34:36,475 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:34:36,475 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:34:36,475 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:34:36,475 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:34:36,475 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:34:36,475 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:34:36,475 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:34:36,479 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:34:36,479 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:34:36,479 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:34:36,479 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:34:36,479 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:34:36,480 - Initializing Velocity, Calling init()...
2025-07-29 13:34:36,480 - Initializing Velocity, Calling init()...
2025-07-29 13:34:36,480 - Initializing Velocity, Calling init()...
2025-07-29 13:34:36,480 - Initializing Velocity, Calling init()...
2025-07-29 13:34:36,480 - Initializing Velocity, Calling init()...
2025-07-29 13:34:36,481 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:34:36,481 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:34:36,481 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:34:36,481 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:34:36,481 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:34:36,481 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:34:36,481 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:34:36,481 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:34:36,481 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:34:36,481 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:34:36,481 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:34:36,481 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:34:36,481 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:34:36,481 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:34:36,481 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:34:36,481 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:34:36,481 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:34:36,481 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:34:36,481 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:34:36,481 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:34:36,481 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:34:36,481 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:34:36,481 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:34:36,481 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:34:36,481 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:34:36,481 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:34:36,481 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:34:36,481 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:34:36,481 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:34:36,481 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:34:36,481 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:34:36,481 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:34:36,481 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:34:36,481 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:34:36,481 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:34:36,481 - Do unicode file recognition:  false
2025-07-29 13:34:36,481 - Do unicode file recognition:  false
2025-07-29 13:34:36,481 - Do unicode file recognition:  false
2025-07-29 13:34:36,481 - Do unicode file recognition:  false
2025-07-29 13:34:36,481 - Do unicode file recognition:  false
2025-07-29 13:34:36,481 - FileResourceLoader : adding path '.'
2025-07-29 13:34:36,481 - FileResourceLoader : adding path '.'
2025-07-29 13:34:36,481 - FileResourceLoader : adding path '.'
2025-07-29 13:34:36,481 - FileResourceLoader : adding path '.'
2025-07-29 13:34:36,481 - FileResourceLoader : adding path '.'
2025-07-29 13:34:36,481 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:34:36,481 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:34:36,481 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:34:36,481 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:34:36,481 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:34:36,483 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:34:36,484 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:34:36,484 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:34:36,484 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:34:36,484 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:34:36,484 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:34:36,484 - Created '20' parsers.
2025-07-29 13:34:36,484 - Created '20' parsers.
2025-07-29 13:34:36,484 - Created '20' parsers.
2025-07-29 13:34:36,484 - Created '20' parsers.
2025-07-29 13:34:36,484 - Created '20' parsers.
2025-07-29 13:34:36,485 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:34:36,485 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:34:36,485 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:34:36,485 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:34:36,485 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:34:36,485 - Velocimacro : Default library not found.
2025-07-29 13:34:36,485 - Velocimacro : Default library not found.
2025-07-29 13:34:36,485 - Velocimacro : Default library not found.
2025-07-29 13:34:36,485 - Velocimacro : Default library not found.
2025-07-29 13:34:36,485 - Velocimacro : Default library not found.
2025-07-29 13:34:36,485 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:34:36,485 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:34:36,485 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:34:36,485 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:34:36,485 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:34:36,485 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:34:36,485 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:34:36,485 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:34:36,485 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:34:36,485 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:34:36,485 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:34:36,485 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:34:36,485 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:34:36,485 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:34:36,485 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:34:36,485 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:34:36,485 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:34:36,485 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:34:36,485 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:34:36,485 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:35:14,180 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:35:14,180 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:35:14,180 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:35:14,180 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:35:14,180 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:35:14,180 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:35:14,181 - Initializing Velocity, Calling init()...
2025-07-29 13:35:14,181 - Initializing Velocity, Calling init()...
2025-07-29 13:35:14,181 - Initializing Velocity, Calling init()...
2025-07-29 13:35:14,181 - Initializing Velocity, Calling init()...
2025-07-29 13:35:14,181 - Initializing Velocity, Calling init()...
2025-07-29 13:35:14,181 - Initializing Velocity, Calling init()...
2025-07-29 13:35:14,182 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:35:14,182 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:35:14,182 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:35:14,182 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:35:14,182 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:35:14,182 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:35:14,182 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:35:14,182 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:35:14,182 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:35:14,182 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:35:14,182 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:35:14,182 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:35:14,182 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:35:14,182 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:35:14,182 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:35:14,182 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:35:14,182 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:35:14,182 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:35:14,182 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:35:14,182 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:35:14,182 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:35:14,182 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:35:14,182 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:35:14,182 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:35:14,182 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:35:14,182 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:35:14,182 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:35:14,182 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:35:14,182 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:35:14,182 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:35:14,182 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:35:14,182 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:35:14,182 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:35:14,182 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:35:14,182 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:35:14,182 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:35:14,182 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:35:14,182 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:35:14,182 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:35:14,182 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:35:14,182 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:35:14,182 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:35:14,182 - Do unicode file recognition:  false
2025-07-29 13:35:14,182 - Do unicode file recognition:  false
2025-07-29 13:35:14,182 - Do unicode file recognition:  false
2025-07-29 13:35:14,182 - Do unicode file recognition:  false
2025-07-29 13:35:14,182 - Do unicode file recognition:  false
2025-07-29 13:35:14,182 - Do unicode file recognition:  false
2025-07-29 13:35:14,182 - FileResourceLoader : adding path '.'
2025-07-29 13:35:14,182 - FileResourceLoader : adding path '.'
2025-07-29 13:35:14,182 - FileResourceLoader : adding path '.'
2025-07-29 13:35:14,182 - FileResourceLoader : adding path '.'
2025-07-29 13:35:14,182 - FileResourceLoader : adding path '.'
2025-07-29 13:35:14,182 - FileResourceLoader : adding path '.'
2025-07-29 13:35:14,182 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:35:14,182 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:35:14,182 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:35:14,182 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:35:14,182 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:35:14,182 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:35:14,185 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:35:14,186 - Created '20' parsers.
2025-07-29 13:35:14,186 - Created '20' parsers.
2025-07-29 13:35:14,186 - Created '20' parsers.
2025-07-29 13:35:14,186 - Created '20' parsers.
2025-07-29 13:35:14,186 - Created '20' parsers.
2025-07-29 13:35:14,186 - Created '20' parsers.
2025-07-29 13:35:14,186 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:35:14,186 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:35:14,186 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:35:14,186 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:35:14,186 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:35:14,186 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:35:14,186 - Velocimacro : Default library not found.
2025-07-29 13:35:14,186 - Velocimacro : Default library not found.
2025-07-29 13:35:14,186 - Velocimacro : Default library not found.
2025-07-29 13:35:14,186 - Velocimacro : Default library not found.
2025-07-29 13:35:14,186 - Velocimacro : Default library not found.
2025-07-29 13:35:14,186 - Velocimacro : Default library not found.
2025-07-29 13:35:14,186 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:35:14,186 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:35:14,186 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:35:14,186 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:35:14,186 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:35:14,186 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:35:14,186 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:35:14,186 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:35:14,186 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:35:14,186 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:35:14,186 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:35:14,186 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:35:14,186 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:35:14,186 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:35:14,186 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:35:14,186 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:35:14,186 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:35:14,186 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:35:14,186 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:35:14,186 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:35:14,186 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:35:14,186 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:35:14,186 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:35:14,186 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:35:14,190 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:35:14,190 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:35:14,190 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:35:14,190 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:35:14,190 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:35:14,190 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:35:14,190 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:35:14,190 - Initializing Velocity, Calling init()...
2025-07-29 13:35:14,190 - Initializing Velocity, Calling init()...
2025-07-29 13:35:14,190 - Initializing Velocity, Calling init()...
2025-07-29 13:35:14,190 - Initializing Velocity, Calling init()...
2025-07-29 13:35:14,190 - Initializing Velocity, Calling init()...
2025-07-29 13:35:14,190 - Initializing Velocity, Calling init()...
2025-07-29 13:35:14,190 - Initializing Velocity, Calling init()...
2025-07-29 13:35:14,190 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:35:14,190 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:35:14,190 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:35:14,190 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:35:14,190 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:35:14,190 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:35:14,190 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:35:14,190 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:35:14,190 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:35:14,190 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:35:14,190 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:35:14,190 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:35:14,190 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:35:14,190 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:35:14,190 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:35:14,190 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:35:14,190 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:35:14,190 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:35:14,190 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:35:14,190 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:35:14,190 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:35:14,191 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:35:14,191 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:35:14,191 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:35:14,191 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:35:14,191 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:35:14,191 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:35:14,191 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:35:14,191 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:35:14,191 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:35:14,191 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:35:14,191 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:35:14,191 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:35:14,191 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:35:14,191 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:35:14,191 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:35:14,191 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:35:14,191 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:35:14,191 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:35:14,191 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:35:14,191 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:35:14,191 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:35:14,191 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:35:14,191 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:35:14,191 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:35:14,191 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:35:14,191 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:35:14,191 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:35:14,191 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:35:14,191 - Do unicode file recognition:  false
2025-07-29 13:35:14,191 - Do unicode file recognition:  false
2025-07-29 13:35:14,191 - Do unicode file recognition:  false
2025-07-29 13:35:14,191 - Do unicode file recognition:  false
2025-07-29 13:35:14,191 - Do unicode file recognition:  false
2025-07-29 13:35:14,191 - Do unicode file recognition:  false
2025-07-29 13:35:14,191 - Do unicode file recognition:  false
2025-07-29 13:35:14,191 - FileResourceLoader : adding path '.'
2025-07-29 13:35:14,191 - FileResourceLoader : adding path '.'
2025-07-29 13:35:14,191 - FileResourceLoader : adding path '.'
2025-07-29 13:35:14,191 - FileResourceLoader : adding path '.'
2025-07-29 13:35:14,191 - FileResourceLoader : adding path '.'
2025-07-29 13:35:14,191 - FileResourceLoader : adding path '.'
2025-07-29 13:35:14,191 - FileResourceLoader : adding path '.'
2025-07-29 13:35:14,191 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:35:14,191 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:35:14,191 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:35:14,191 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:35:14,191 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:35:14,191 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:35:14,191 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:35:14,193 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:35:14,194 - Created '20' parsers.
2025-07-29 13:35:14,194 - Created '20' parsers.
2025-07-29 13:35:14,194 - Created '20' parsers.
2025-07-29 13:35:14,194 - Created '20' parsers.
2025-07-29 13:35:14,194 - Created '20' parsers.
2025-07-29 13:35:14,194 - Created '20' parsers.
2025-07-29 13:35:14,194 - Created '20' parsers.
2025-07-29 13:35:14,194 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:35:14,194 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:35:14,194 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:35:14,194 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:35:14,194 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:35:14,194 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:35:14,194 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:35:14,194 - Velocimacro : Default library not found.
2025-07-29 13:35:14,194 - Velocimacro : Default library not found.
2025-07-29 13:35:14,194 - Velocimacro : Default library not found.
2025-07-29 13:35:14,194 - Velocimacro : Default library not found.
2025-07-29 13:35:14,194 - Velocimacro : Default library not found.
2025-07-29 13:35:14,194 - Velocimacro : Default library not found.
2025-07-29 13:35:14,194 - Velocimacro : Default library not found.
2025-07-29 13:35:14,194 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:35:14,194 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:35:14,194 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:35:14,194 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:35:14,194 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:35:14,194 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:35:14,194 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:35:14,194 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:35:14,194 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:35:14,194 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:35:14,194 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:35:14,194 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:35:14,194 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:35:14,194 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:35:14,194 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:35:14,194 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:35:14,194 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:35:14,194 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:35:14,194 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:35:14,194 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:35:14,194 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:35:14,194 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:35:14,194 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:35:14,194 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:35:14,194 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:35:14,194 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:35:14,194 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:35:14,194 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:37:41,196 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:37:41,196 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:37:41,196 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:37:41,196 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:37:41,196 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:37:41,196 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:37:41,196 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:37:41,196 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:37:41,197 - Initializing Velocity, Calling init()...
2025-07-29 13:37:41,197 - Initializing Velocity, Calling init()...
2025-07-29 13:37:41,197 - Initializing Velocity, Calling init()...
2025-07-29 13:37:41,197 - Initializing Velocity, Calling init()...
2025-07-29 13:37:41,197 - Initializing Velocity, Calling init()...
2025-07-29 13:37:41,197 - Initializing Velocity, Calling init()...
2025-07-29 13:37:41,197 - Initializing Velocity, Calling init()...
2025-07-29 13:37:41,197 - Initializing Velocity, Calling init()...
2025-07-29 13:37:41,197 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:37:41,197 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:37:41,197 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:37:41,197 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:37:41,197 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:37:41,197 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:37:41,197 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:37:41,197 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:37:41,198 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:37:41,198 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:37:41,198 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:37:41,198 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:37:41,198 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:37:41,198 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:37:41,198 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:37:41,198 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:37:41,198 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:37:41,198 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:37:41,198 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:37:41,198 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:37:41,198 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:37:41,198 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:37:41,198 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:37:41,198 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:37:41,198 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:37:41,198 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:37:41,198 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:37:41,198 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:37:41,198 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:37:41,198 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:37:41,198 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:37:41,198 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:37:41,198 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:37:41,198 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:37:41,198 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:37:41,198 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:37:41,198 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:37:41,198 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:37:41,198 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:37:41,198 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:37:41,198 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:37:41,198 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:37:41,198 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:37:41,198 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:37:41,198 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:37:41,198 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:37:41,198 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:37:41,198 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:37:41,198 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:37:41,198 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:37:41,198 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:37:41,198 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:37:41,198 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:37:41,198 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:37:41,198 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:37:41,198 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:37:41,198 - Do unicode file recognition:  false
2025-07-29 13:37:41,198 - Do unicode file recognition:  false
2025-07-29 13:37:41,198 - Do unicode file recognition:  false
2025-07-29 13:37:41,198 - Do unicode file recognition:  false
2025-07-29 13:37:41,198 - Do unicode file recognition:  false
2025-07-29 13:37:41,198 - Do unicode file recognition:  false
2025-07-29 13:37:41,198 - Do unicode file recognition:  false
2025-07-29 13:37:41,198 - Do unicode file recognition:  false
2025-07-29 13:37:41,198 - FileResourceLoader : adding path '.'
2025-07-29 13:37:41,198 - FileResourceLoader : adding path '.'
2025-07-29 13:37:41,198 - FileResourceLoader : adding path '.'
2025-07-29 13:37:41,198 - FileResourceLoader : adding path '.'
2025-07-29 13:37:41,198 - FileResourceLoader : adding path '.'
2025-07-29 13:37:41,198 - FileResourceLoader : adding path '.'
2025-07-29 13:37:41,198 - FileResourceLoader : adding path '.'
2025-07-29 13:37:41,198 - FileResourceLoader : adding path '.'
2025-07-29 13:37:41,198 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:37:41,198 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:37:41,198 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:37:41,198 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:37:41,198 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:37:41,198 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:37:41,198 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:37:41,198 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:37:41,200 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:37:41,200 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:37:41,200 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:37:41,200 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:37:41,200 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:37:41,200 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:37:41,200 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:37:41,200 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:37:41,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:37:41,202 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:37:41,202 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:37:41,202 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:37:41,202 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:37:41,202 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:37:41,202 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:37:41,202 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:37:41,202 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:37:41,202 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:37:41,202 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:37:41,202 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:37:41,202 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:37:41,202 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:37:41,202 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:37:41,202 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:37:41,202 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:37:41,202 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:37:41,202 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:37:41,202 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:37:41,202 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:37:41,202 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:37:41,202 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:37:41,202 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:37:41,202 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:37:41,202 - Created '20' parsers.
2025-07-29 13:37:41,202 - Created '20' parsers.
2025-07-29 13:37:41,202 - Created '20' parsers.
2025-07-29 13:37:41,202 - Created '20' parsers.
2025-07-29 13:37:41,202 - Created '20' parsers.
2025-07-29 13:37:41,202 - Created '20' parsers.
2025-07-29 13:37:41,202 - Created '20' parsers.
2025-07-29 13:37:41,202 - Created '20' parsers.
2025-07-29 13:37:41,202 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:37:41,202 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:37:41,202 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:37:41,202 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:37:41,202 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:37:41,202 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:37:41,202 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:37:41,202 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:37:41,202 - Velocimacro : Default library not found.
2025-07-29 13:37:41,202 - Velocimacro : Default library not found.
2025-07-29 13:37:41,202 - Velocimacro : Default library not found.
2025-07-29 13:37:41,202 - Velocimacro : Default library not found.
2025-07-29 13:37:41,202 - Velocimacro : Default library not found.
2025-07-29 13:37:41,202 - Velocimacro : Default library not found.
2025-07-29 13:37:41,202 - Velocimacro : Default library not found.
2025-07-29 13:37:41,202 - Velocimacro : Default library not found.
2025-07-29 13:37:41,202 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:37:41,202 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:37:41,202 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:37:41,202 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:37:41,202 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:37:41,202 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:37:41,202 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:37:41,202 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:37:41,203 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:37:41,203 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:37:41,203 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:37:41,203 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:37:41,203 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:37:41,203 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:37:41,203 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:37:41,203 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:37:41,203 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:37:41,203 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:37:41,203 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:37:41,203 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:37:41,203 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:37:41,203 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:37:41,203 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:37:41,203 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:37:41,203 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:37:41,203 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:37:41,203 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:37:41,203 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:37:41,203 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:37:41,203 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:37:41,203 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:37:41,203 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:37:41,207 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:37:41,207 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:37:41,207 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:37:41,207 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:37:41,207 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:37:41,207 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:37:41,207 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:37:41,207 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:37:41,207 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:37:41,208 - Initializing Velocity, Calling init()...
2025-07-29 13:37:41,208 - Initializing Velocity, Calling init()...
2025-07-29 13:37:41,208 - Initializing Velocity, Calling init()...
2025-07-29 13:37:41,208 - Initializing Velocity, Calling init()...
2025-07-29 13:37:41,208 - Initializing Velocity, Calling init()...
2025-07-29 13:37:41,208 - Initializing Velocity, Calling init()...
2025-07-29 13:37:41,208 - Initializing Velocity, Calling init()...
2025-07-29 13:37:41,208 - Initializing Velocity, Calling init()...
2025-07-29 13:37:41,208 - Initializing Velocity, Calling init()...
2025-07-29 13:37:41,208 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:37:41,208 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:37:41,208 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:37:41,208 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:37:41,208 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:37:41,208 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:37:41,208 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:37:41,208 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:37:41,208 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:37:41,208 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:37:41,208 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:37:41,208 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:37:41,208 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:37:41,208 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:37:41,208 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:37:41,208 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:37:41,208 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:37:41,208 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:37:41,208 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:37:41,208 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:37:41,208 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:37:41,208 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:37:41,208 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:37:41,208 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:37:41,208 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:37:41,208 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:37:41,208 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:37:41,208 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:37:41,208 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:37:41,208 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:37:41,208 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:37:41,208 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:37:41,208 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:37:41,208 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:37:41,208 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:37:41,208 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:37:41,208 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:37:41,208 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:37:41,208 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:37:41,208 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:37:41,208 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:37:41,208 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:37:41,208 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:37:41,208 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:37:41,208 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:37:41,208 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:37:41,208 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:37:41,208 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:37:41,208 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:37:41,208 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:37:41,208 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:37:41,208 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:37:41,208 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:37:41,208 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:37:41,208 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:37:41,208 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:37:41,208 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:37:41,208 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:37:41,208 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:37:41,208 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:37:41,208 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:37:41,208 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:37:41,208 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:37:41,209 - Do unicode file recognition:  false
2025-07-29 13:37:41,209 - Do unicode file recognition:  false
2025-07-29 13:37:41,209 - Do unicode file recognition:  false
2025-07-29 13:37:41,209 - Do unicode file recognition:  false
2025-07-29 13:37:41,209 - Do unicode file recognition:  false
2025-07-29 13:37:41,209 - Do unicode file recognition:  false
2025-07-29 13:37:41,209 - Do unicode file recognition:  false
2025-07-29 13:37:41,209 - Do unicode file recognition:  false
2025-07-29 13:37:41,209 - Do unicode file recognition:  false
2025-07-29 13:37:41,209 - FileResourceLoader : adding path '.'
2025-07-29 13:37:41,209 - FileResourceLoader : adding path '.'
2025-07-29 13:37:41,209 - FileResourceLoader : adding path '.'
2025-07-29 13:37:41,209 - FileResourceLoader : adding path '.'
2025-07-29 13:37:41,209 - FileResourceLoader : adding path '.'
2025-07-29 13:37:41,209 - FileResourceLoader : adding path '.'
2025-07-29 13:37:41,209 - FileResourceLoader : adding path '.'
2025-07-29 13:37:41,209 - FileResourceLoader : adding path '.'
2025-07-29 13:37:41,209 - FileResourceLoader : adding path '.'
2025-07-29 13:37:41,209 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:37:41,209 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:37:41,209 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:37:41,209 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:37:41,209 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:37:41,209 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:37:41,209 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:37:41,209 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:37:41,209 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:37:41,212 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:37:41,213 - Created '20' parsers.
2025-07-29 13:37:41,213 - Created '20' parsers.
2025-07-29 13:37:41,213 - Created '20' parsers.
2025-07-29 13:37:41,213 - Created '20' parsers.
2025-07-29 13:37:41,213 - Created '20' parsers.
2025-07-29 13:37:41,213 - Created '20' parsers.
2025-07-29 13:37:41,213 - Created '20' parsers.
2025-07-29 13:37:41,213 - Created '20' parsers.
2025-07-29 13:37:41,213 - Created '20' parsers.
2025-07-29 13:37:41,214 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:37:41,214 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:37:41,214 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:37:41,214 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:37:41,214 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:37:41,214 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:37:41,214 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:37:41,214 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:37:41,214 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:37:41,214 - Velocimacro : Default library not found.
2025-07-29 13:37:41,214 - Velocimacro : Default library not found.
2025-07-29 13:37:41,214 - Velocimacro : Default library not found.
2025-07-29 13:37:41,214 - Velocimacro : Default library not found.
2025-07-29 13:37:41,214 - Velocimacro : Default library not found.
2025-07-29 13:37:41,214 - Velocimacro : Default library not found.
2025-07-29 13:37:41,214 - Velocimacro : Default library not found.
2025-07-29 13:37:41,214 - Velocimacro : Default library not found.
2025-07-29 13:37:41,214 - Velocimacro : Default library not found.
2025-07-29 13:37:41,214 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:37:41,214 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:37:41,214 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:37:41,214 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:37:41,214 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:37:41,214 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:37:41,214 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:37:41,214 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:37:41,214 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:37:41,214 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:37:41,214 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:37:41,214 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:37:41,214 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:37:41,214 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:37:41,214 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:37:41,214 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:37:41,214 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:37:41,214 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:37:41,214 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:37:41,214 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:37:41,214 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:37:41,214 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:37:41,214 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:37:41,214 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:37:41,214 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:37:41,214 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:37:41,214 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:37:41,214 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:37:41,214 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:37:41,214 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:37:41,214 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:37:41,214 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:37:41,214 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:37:41,214 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:37:41,214 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:37:41,214 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:54:36,751 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:54:36,751 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:54:36,751 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:54:36,751 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:54:36,751 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:54:36,751 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:54:36,751 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:54:36,751 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:54:36,751 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:54:36,751 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:54:36,753 - Initializing Velocity, Calling init()...
2025-07-29 13:54:36,753 - Initializing Velocity, Calling init()...
2025-07-29 13:54:36,753 - Initializing Velocity, Calling init()...
2025-07-29 13:54:36,753 - Initializing Velocity, Calling init()...
2025-07-29 13:54:36,753 - Initializing Velocity, Calling init()...
2025-07-29 13:54:36,753 - Initializing Velocity, Calling init()...
2025-07-29 13:54:36,753 - Initializing Velocity, Calling init()...
2025-07-29 13:54:36,753 - Initializing Velocity, Calling init()...
2025-07-29 13:54:36,753 - Initializing Velocity, Calling init()...
2025-07-29 13:54:36,753 - Initializing Velocity, Calling init()...
2025-07-29 13:54:36,753 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:54:36,753 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:54:36,753 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:54:36,753 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:54:36,753 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:54:36,753 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:54:36,753 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:54:36,753 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:54:36,753 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:54:36,753 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:54:36,753 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:54:36,753 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:54:36,753 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:54:36,753 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:54:36,753 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:54:36,753 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:54:36,753 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:54:36,753 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:54:36,753 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:54:36,753 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:54:36,753 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:54:36,753 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:54:36,753 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:54:36,753 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:54:36,753 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:54:36,753 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:54:36,753 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:54:36,753 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:54:36,753 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:54:36,753 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:54:36,754 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:54:36,754 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:54:36,754 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:54:36,754 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:54:36,754 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:54:36,754 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:54:36,754 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:54:36,754 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:54:36,754 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:54:36,754 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:54:36,754 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,754 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,754 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,754 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,754 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,754 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,754 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,754 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,754 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,754 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,754 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,754 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,754 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,754 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,754 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,754 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,754 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,754 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,754 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,754 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,754 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:54:36,754 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:54:36,754 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:54:36,754 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:54:36,754 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:54:36,754 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:54:36,754 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:54:36,754 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:54:36,754 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:54:36,754 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:54:36,754 - Do unicode file recognition:  false
2025-07-29 13:54:36,754 - Do unicode file recognition:  false
2025-07-29 13:54:36,754 - Do unicode file recognition:  false
2025-07-29 13:54:36,754 - Do unicode file recognition:  false
2025-07-29 13:54:36,754 - Do unicode file recognition:  false
2025-07-29 13:54:36,754 - Do unicode file recognition:  false
2025-07-29 13:54:36,754 - Do unicode file recognition:  false
2025-07-29 13:54:36,754 - Do unicode file recognition:  false
2025-07-29 13:54:36,754 - Do unicode file recognition:  false
2025-07-29 13:54:36,754 - Do unicode file recognition:  false
2025-07-29 13:54:36,754 - FileResourceLoader : adding path '.'
2025-07-29 13:54:36,754 - FileResourceLoader : adding path '.'
2025-07-29 13:54:36,754 - FileResourceLoader : adding path '.'
2025-07-29 13:54:36,754 - FileResourceLoader : adding path '.'
2025-07-29 13:54:36,754 - FileResourceLoader : adding path '.'
2025-07-29 13:54:36,754 - FileResourceLoader : adding path '.'
2025-07-29 13:54:36,754 - FileResourceLoader : adding path '.'
2025-07-29 13:54:36,754 - FileResourceLoader : adding path '.'
2025-07-29 13:54:36,754 - FileResourceLoader : adding path '.'
2025-07-29 13:54:36,754 - FileResourceLoader : adding path '.'
2025-07-29 13:54:36,754 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:54:36,754 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:54:36,754 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:54:36,754 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:54:36,754 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:54:36,754 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:54:36,754 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:54:36,754 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:54:36,754 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:54:36,754 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:54:36,756 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:54:36,756 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:54:36,756 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:54:36,756 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:54:36,756 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:54:36,756 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:54:36,756 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:54:36,756 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:54:36,756 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:54:36,756 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:54:36,756 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:54:36,756 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:54:36,756 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:54:36,756 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:54:36,756 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:54:36,756 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:54:36,756 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:54:36,756 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:54:36,756 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:54:36,756 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:54:36,756 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:54:36,756 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:54:36,756 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:54:36,756 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:54:36,756 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:54:36,756 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:54:36,756 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:54:36,756 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:54:36,756 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:54:36,756 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:54:36,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:54:36,758 - Created '20' parsers.
2025-07-29 13:54:36,758 - Created '20' parsers.
2025-07-29 13:54:36,758 - Created '20' parsers.
2025-07-29 13:54:36,758 - Created '20' parsers.
2025-07-29 13:54:36,758 - Created '20' parsers.
2025-07-29 13:54:36,758 - Created '20' parsers.
2025-07-29 13:54:36,758 - Created '20' parsers.
2025-07-29 13:54:36,758 - Created '20' parsers.
2025-07-29 13:54:36,758 - Created '20' parsers.
2025-07-29 13:54:36,758 - Created '20' parsers.
2025-07-29 13:54:36,758 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:54:36,758 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:54:36,758 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:54:36,758 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:54:36,758 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:54:36,758 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:54:36,758 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:54:36,758 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:54:36,758 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:54:36,758 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:54:36,758 - Velocimacro : Default library not found.
2025-07-29 13:54:36,758 - Velocimacro : Default library not found.
2025-07-29 13:54:36,758 - Velocimacro : Default library not found.
2025-07-29 13:54:36,758 - Velocimacro : Default library not found.
2025-07-29 13:54:36,758 - Velocimacro : Default library not found.
2025-07-29 13:54:36,758 - Velocimacro : Default library not found.
2025-07-29 13:54:36,758 - Velocimacro : Default library not found.
2025-07-29 13:54:36,758 - Velocimacro : Default library not found.
2025-07-29 13:54:36,758 - Velocimacro : Default library not found.
2025-07-29 13:54:36,758 - Velocimacro : Default library not found.
2025-07-29 13:54:36,758 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:54:36,758 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:54:36,758 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:54:36,758 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:54:36,758 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:54:36,758 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:54:36,758 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:54:36,758 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:54:36,758 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:54:36,758 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:54:36,758 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:54:36,758 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:54:36,758 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:54:36,758 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:54:36,758 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:54:36,758 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:54:36,758 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:54:36,758 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:54:36,758 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:54:36,758 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:54:36,758 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:54:36,758 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:54:36,758 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:54:36,758 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:54:36,758 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:54:36,758 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:54:36,758 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:54:36,758 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:54:36,758 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:54:36,758 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:54:36,758 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:54:36,758 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:54:36,758 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:54:36,758 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:54:36,758 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:54:36,758 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:54:36,758 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:54:36,758 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:54:36,758 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:54:36,758 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:54:36,762 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:54:36,762 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:54:36,762 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:54:36,762 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:54:36,762 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:54:36,762 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:54:36,762 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:54:36,762 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:54:36,762 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:54:36,762 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:54:36,762 - Log4JLogChute initialized using file 'velocity.log'
2025-07-29 13:54:36,763 - Initializing Velocity, Calling init()...
2025-07-29 13:54:36,763 - Initializing Velocity, Calling init()...
2025-07-29 13:54:36,763 - Initializing Velocity, Calling init()...
2025-07-29 13:54:36,763 - Initializing Velocity, Calling init()...
2025-07-29 13:54:36,763 - Initializing Velocity, Calling init()...
2025-07-29 13:54:36,763 - Initializing Velocity, Calling init()...
2025-07-29 13:54:36,763 - Initializing Velocity, Calling init()...
2025-07-29 13:54:36,763 - Initializing Velocity, Calling init()...
2025-07-29 13:54:36,763 - Initializing Velocity, Calling init()...
2025-07-29 13:54:36,763 - Initializing Velocity, Calling init()...
2025-07-29 13:54:36,763 - Initializing Velocity, Calling init()...
2025-07-29 13:54:36,763 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:54:36,763 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:54:36,763 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:54:36,763 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:54:36,763 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:54:36,763 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:54:36,763 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:54:36,763 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:54:36,763 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:54:36,763 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:54:36,763 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-29 13:54:36,763 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:54:36,763 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:54:36,763 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:54:36,763 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:54:36,763 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:54:36,763 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:54:36,763 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:54:36,763 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:54:36,763 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:54:36,763 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:54:36,763 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-29 13:54:36,763 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:54:36,763 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:54:36,763 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:54:36,763 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:54:36,763 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:54:36,763 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:54:36,763 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:54:36,763 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:54:36,763 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:54:36,763 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:54:36,763 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-29 13:54:36,763 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:54:36,763 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:54:36,763 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:54:36,763 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:54:36,763 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:54:36,763 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:54:36,763 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:54:36,763 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:54:36,763 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:54:36,763 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:54:36,763 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: Could not initialize class org.apache.velocity.runtime.log.AvalonLogChute).  Falling back to next log system...
2025-07-29 13:54:36,764 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,764 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,764 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,764 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,764 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,764 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,764 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,764 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,764 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,764 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,764 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,764 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,764 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,764 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,764 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,764 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,764 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,764 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,764 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,764 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,764 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,764 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-29 13:54:36,764 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:54:36,764 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:54:36,764 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:54:36,764 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:54:36,764 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:54:36,764 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:54:36,764 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:54:36,764 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:54:36,764 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:54:36,764 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:54:36,764 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-29 13:54:36,764 - Do unicode file recognition:  false
2025-07-29 13:54:36,764 - Do unicode file recognition:  false
2025-07-29 13:54:36,764 - Do unicode file recognition:  false
2025-07-29 13:54:36,764 - Do unicode file recognition:  false
2025-07-29 13:54:36,764 - Do unicode file recognition:  false
2025-07-29 13:54:36,764 - Do unicode file recognition:  false
2025-07-29 13:54:36,764 - Do unicode file recognition:  false
2025-07-29 13:54:36,764 - Do unicode file recognition:  false
2025-07-29 13:54:36,764 - Do unicode file recognition:  false
2025-07-29 13:54:36,764 - Do unicode file recognition:  false
2025-07-29 13:54:36,764 - Do unicode file recognition:  false
2025-07-29 13:54:36,764 - FileResourceLoader : adding path '.'
2025-07-29 13:54:36,764 - FileResourceLoader : adding path '.'
2025-07-29 13:54:36,764 - FileResourceLoader : adding path '.'
2025-07-29 13:54:36,764 - FileResourceLoader : adding path '.'
2025-07-29 13:54:36,764 - FileResourceLoader : adding path '.'
2025-07-29 13:54:36,764 - FileResourceLoader : adding path '.'
2025-07-29 13:54:36,764 - FileResourceLoader : adding path '.'
2025-07-29 13:54:36,764 - FileResourceLoader : adding path '.'
2025-07-29 13:54:36,764 - FileResourceLoader : adding path '.'
2025-07-29 13:54:36,764 - FileResourceLoader : adding path '.'
2025-07-29 13:54:36,764 - FileResourceLoader : adding path '.'
2025-07-29 13:54:36,764 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:54:36,764 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:54:36,764 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:54:36,764 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:54:36,764 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:54:36,764 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:54:36,764 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:54:36,764 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:54:36,764 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:54:36,764 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:54:36,764 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:54:36,766 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-29 13:54:36,767 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:54:36,767 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:54:36,767 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:54:36,767 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:54:36,767 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:54:36,767 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:54:36,767 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:54:36,767 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:54:36,767 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:54:36,767 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:54:36,767 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-29 13:54:36,767 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:54:36,767 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:54:36,767 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:54:36,767 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:54:36,767 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:54:36,767 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:54:36,767 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:54:36,767 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:54:36,767 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:54:36,767 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:54:36,767 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-29 13:54:36,767 - Created '20' parsers.
2025-07-29 13:54:36,767 - Created '20' parsers.
2025-07-29 13:54:36,767 - Created '20' parsers.
2025-07-29 13:54:36,767 - Created '20' parsers.
2025-07-29 13:54:36,767 - Created '20' parsers.
2025-07-29 13:54:36,767 - Created '20' parsers.
2025-07-29 13:54:36,767 - Created '20' parsers.
2025-07-29 13:54:36,767 - Created '20' parsers.
2025-07-29 13:54:36,767 - Created '20' parsers.
2025-07-29 13:54:36,767 - Created '20' parsers.
2025-07-29 13:54:36,767 - Created '20' parsers.
2025-07-29 13:54:36,768 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:54:36,768 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:54:36,768 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:54:36,768 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:54:36,768 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:54:36,768 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:54:36,768 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:54:36,768 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:54:36,768 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:54:36,768 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:54:36,768 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-29 13:54:36,768 - Velocimacro : Default library not found.
2025-07-29 13:54:36,768 - Velocimacro : Default library not found.
2025-07-29 13:54:36,768 - Velocimacro : Default library not found.
2025-07-29 13:54:36,768 - Velocimacro : Default library not found.
2025-07-29 13:54:36,768 - Velocimacro : Default library not found.
2025-07-29 13:54:36,768 - Velocimacro : Default library not found.
2025-07-29 13:54:36,768 - Velocimacro : Default library not found.
2025-07-29 13:54:36,768 - Velocimacro : Default library not found.
2025-07-29 13:54:36,768 - Velocimacro : Default library not found.
2025-07-29 13:54:36,768 - Velocimacro : Default library not found.
2025-07-29 13:54:36,768 - Velocimacro : Default library not found.
2025-07-29 13:54:36,768 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:54:36,768 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:54:36,768 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:54:36,768 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:54:36,768 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:54:36,768 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:54:36,768 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:54:36,768 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:54:36,768 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:54:36,768 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:54:36,768 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-29 13:54:36,768 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:54:36,768 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:54:36,768 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:54:36,768 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:54:36,768 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:54:36,768 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:54:36,768 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:54:36,768 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:54:36,768 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:54:36,768 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:54:36,768 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-29 13:54:36,768 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:54:36,768 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:54:36,768 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:54:36,768 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:54:36,768 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:54:36,768 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:54:36,768 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:54:36,768 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:54:36,768 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:54:36,768 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:54:36,768 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-29 13:54:36,768 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:54:36,768 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:54:36,768 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:54:36,768 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:54:36,768 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:54:36,768 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:54:36,768 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:54:36,768 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:54:36,768 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:54:36,768 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-07-29 13:54:36,768 - Velocimacro : autoload off : VM system will not automatically reload global library macros
