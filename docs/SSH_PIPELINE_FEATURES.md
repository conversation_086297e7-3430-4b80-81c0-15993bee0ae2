# SSH流水线高级功能使用指南

## 概述

SSH流水线系统提供了强大的自动化运维功能，支持正则表达式匹配、智能重试、错误处理等高级特性。

## 核心功能

### 1. 成功匹配正则 (successpattern)

用于判断命令是否执行成功的正则表达式。

#### 使用场景
- 验证服务启动状态
- 确认安装完成
- 检查文件操作结果

#### 示例
```regex
# 检查Docker是否安装成功
(?i).*(docker version|docker.*\d+\.\d+).*

# 检查服务是否启动
(?i).*(active|running|started).*

# 检查文件是否存在
(?i).*(exists|found).*
```

#### 匹配模式
- **完全匹配**: `^pattern$` - 整个输出必须完全匹配
- **查找匹配**: `pattern` - 在输出中查找匹配的部分
- **包含匹配**: `.*pattern.*` - 输出包含指定内容

### 2. 失败匹配正则 (errorpattern)

用于检测命令执行失败的正则表达式。

#### 使用场景
- 检测错误信息
- 识别权限问题
- 发现网络连接问题

#### 示例
```regex
# 通用错误检测
(?i).*(error|fail|failed|exception|denied).*

# 权限错误
(?i).*(permission denied|access denied|unauthorized).*

# 网络错误
(?i).*(connection refused|network unreachable|timeout).*

# 文件错误
(?i).*(no such file|file not found|directory not found).*
```

### 3. 超时时间 (timeoutms)

设置命令执行的最大等待时间（毫秒）。

#### 配置建议
- **快速命令**: 5000-10000ms (5-10秒)
- **普通命令**: 30000ms (30秒) - 默认值
- **安装操作**: 300000ms (5分钟)
- **大文件操作**: 600000ms (10分钟)

#### 示例
```json
{
  "stepname": "安装Docker",
  "commandtext": "yum install -y docker",
  "timeoutms": 300000,
  "successpattern": "(?i).*(complete|installed).*"
}
```

### 4. 是否出错继续 (continueonerror)

控制当步骤失败时是否继续执行后续步骤。

#### 配置值
- **0**: 失败时停止流水线（默认）
- **1**: 失败时继续执行后续步骤

#### 使用场景
```json
{
  "stepname": "尝试停止服务",
  "commandtext": "systemctl stop optional-service",
  "continueonerror": 1,
  "errorpattern": "(?i).*(not found|inactive).*"
}
```

### 5. 失败重试次数 (retrycount)

设置命令失败时的重试次数。

#### 重试策略
- **指数退避**: 重试间隔逐渐增加 (1s, 2s, 4s, 8s...)
- **最大延迟**: 10秒
- **智能重试**: 只有在特定条件下才重试

#### 配置建议
- **网络操作**: 3-5次
- **文件操作**: 2-3次
- **服务操作**: 1-2次
- **一次性操作**: 0次

#### 示例
```json
{
  "stepname": "下载文件",
  "commandtext": "wget https://example.com/file.tar.gz",
  "retrycount": 3,
  "timeoutms": 60000,
  "errorpattern": "(?i).*(connection.*failed|timeout|network).*"
}
```

## 高级配置

### 全局配置 (application-ssh.yml)

```yaml
ssh:
  execution:
    default-timeout-ms: 30000
    default-max-retries: 3
    enable-exponential-backoff: true
    default-continue-on-error: false
    enable-verbose-logging: true
```

### 正则表达式最佳实践

#### 1. 使用不区分大小写匹配
```regex
(?i)pattern  # 推荐
PATTERN      # 不推荐
```

#### 2. 使用具体的匹配模式
```regex
# 好的例子
(?i).*(docker version \d+\.\d+\.\d+).*

# 避免过于宽泛
(?i).*docker.*
```

#### 3. 考虑多语言环境
```regex
# 支持中英文错误信息
(?i).*(error|错误|fail|失败|exception|异常).*
```

## 实际应用示例

### 示例1: Docker安装流水线

```json
{
  "pipelinename": "Docker安装",
  "steps": [
    {
      "stepname": "检查系统版本",
      "commandtext": "cat /etc/os-release",
      "successpattern": "(?i).*(ubuntu|centos|rhel).*",
      "timeoutms": 10000,
      "continueonerror": 0,
      "retrycount": 1
    },
    {
      "stepname": "更新包管理器",
      "commandtext": "yum update -y || apt update -y",
      "successpattern": "(?i).*(complete|updated|nothing to do).*",
      "errorpattern": "(?i).*(error|failed|cannot).*",
      "timeoutms": 180000,
      "continueonerror": 0,
      "retrycount": 2
    },
    {
      "stepname": "安装Docker",
      "commandtext": "yum install -y docker || apt install -y docker.io",
      "successpattern": "(?i).*(complete|installed|already installed).*",
      "errorpattern": "(?i).*(error|failed|package not found).*",
      "timeoutms": 300000,
      "continueonerror": 0,
      "retrycount": 2
    },
    {
      "stepname": "启动Docker服务",
      "commandtext": "systemctl start docker && systemctl enable docker",
      "successpattern": "(?i).*(active|running|enabled).*",
      "errorpattern": "(?i).*(failed|inactive|error).*",
      "timeoutms": 30000,
      "continueonerror": 0,
      "retrycount": 3
    },
    {
      "stepname": "验证Docker安装",
      "commandtext": "docker --version && docker info",
      "successpattern": "(?i).*(docker version|server version).*",
      "errorpattern": "(?i).*(command not found|cannot connect).*",
      "timeoutms": 20000,
      "continueonerror": 0,
      "retrycount": 1
    }
  ]
}
```

### 示例2: 应用部署流水线

```json
{
  "pipelinename": "应用部署",
  "steps": [
    {
      "stepname": "备份现有应用",
      "commandtext": "cp -r /app /app.backup.$(date +%Y%m%d_%H%M%S)",
      "successpattern": "(?i).*",
      "errorpattern": "(?i).*(no such file|permission denied).*",
      "timeoutms": 60000,
      "continueonerror": 1,
      "retrycount": 1
    },
    {
      "stepname": "下载新版本",
      "commandtext": "wget -O /tmp/app.tar.gz https://releases.example.com/app-v2.0.tar.gz",
      "successpattern": "(?i).*(saved|downloaded|100%).*",
      "errorpattern": "(?i).*(404|connection failed|timeout).*",
      "timeoutms": 300000,
      "continueonerror": 0,
      "retrycount": 3
    },
    {
      "stepname": "解压应用",
      "commandtext": "cd /tmp && tar -xzf app.tar.gz",
      "successpattern": "(?i).*",
      "errorpattern": "(?i).*(not found|corrupted|invalid).*",
      "timeoutms": 60000,
      "continueonerror": 0,
      "retrycount": 1
    },
    {
      "stepname": "停止旧服务",
      "commandtext": "systemctl stop myapp",
      "successpattern": "(?i).*",
      "errorpattern": "(?i).*(failed to stop|timeout).*",
      "timeoutms": 30000,
      "continueonerror": 1,
      "retrycount": 2
    },
    {
      "stepname": "部署新版本",
      "commandtext": "cp -r /tmp/app/* /app/",
      "successpattern": "(?i).*",
      "errorpattern": "(?i).*(permission denied|no space|read-only).*",
      "timeoutms": 120000,
      "continueonerror": 0,
      "retrycount": 1
    },
    {
      "stepname": "启动新服务",
      "commandtext": "systemctl start myapp && systemctl status myapp",
      "successpattern": "(?i).*(active|running).*",
      "errorpattern": "(?i).*(failed|inactive|error).*",
      "timeoutms": 60000,
      "continueonerror": 0,
      "retrycount": 3
    }
  ]
}
```

## 故障排除

### 常见问题

1. **正则表达式不匹配**
   - 检查大小写敏感性
   - 验证正则语法
   - 查看实际输出内容

2. **超时问题**
   - 增加超时时间
   - 检查网络连接
   - 优化命令执行

3. **重试失效**
   - 确认错误是否可重试
   - 检查重试条件
   - 调整重试次数

### 调试技巧

1. **启用详细日志**
   ```yaml
   ssh:
     execution:
       enable-verbose-logging: true
   ```

2. **使用简单的测试命令**
   ```bash
   echo "test success"  # 测试成功模式
   echo "test error" && exit 1  # 测试失败模式
   ```

3. **逐步验证正则表达式**
   - 使用在线正则测试工具
   - 先测试简单模式
   - 逐步增加复杂度

## 性能优化建议

1. **合理设置超时时间**
2. **避免过度重试**
3. **使用精确的正则表达式**
4. **合理使用continueonerror**
5. **定期清理执行历史**
