<!DOCTYPE html>
<html>
<head>
    <title>美化执行总结预览</title>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            margin: 0;
            padding: 40px 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
        }
        
        /* 执行总结相关样式 - 全新设计 */
        .summary-section {
            margin: 40px 0;
            padding: 0;
            background: #ffffff;
            border-radius: 24px;
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.1),
                0 8px 16px rgba(0, 0, 0, 0.06),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            overflow: hidden;
            position: relative;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .summary-header {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #a855f7 100%);
            padding: 32px 40px;
            position: relative;
            overflow: hidden;
        }

        .summary-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.15"/><circle cx="20" cy="80" r="0.5" fill="white" opacity="0.15"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }

        .summary-title {
            font-size: 2rem;
            font-weight: 800;
            margin: 0;
            color: white;
            display: flex;
            align-items: center;
            gap: 16px;
            position: relative;
            z-index: 1;
        }

        .summary-title::before {
            content: '📈';
            font-size: 2.4rem;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
        }

        .summary-content {
            padding: 40px;
            background: #ffffff;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: #ffffff;
            padding: 28px 24px;
            border-radius: 20px;
            text-align: center;
            border: 2px solid #f1f5f9;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.15),
                0 12px 24px rgba(0, 0, 0, 0.1);
            border-color: transparent;
        }

        .stat-number {
            font-size: 3.2rem;
            font-weight: 900;
            margin-bottom: 8px;
            display: block;
            line-height: 1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            font-size: 1rem;
            color: #64748b;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .stat-total::before {
            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
        }
        .stat-total .stat-number {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .stat-success::before {
            background: linear-gradient(90deg, #10b981, #059669);
        }
        .stat-success .stat-number {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .stat-failed::before {
            background: linear-gradient(90deg, #ef4444, #dc2626);
        }
        .stat-failed .stat-number {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .stat-time::before {
            background: linear-gradient(90deg, #f59e0b, #d97706);
        }
        .stat-time .stat-number {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .summary-status {
            text-align: center;
            margin: 32px 0;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 12px;
            padding: 16px 32px;
            border-radius: 60px;
            font-size: 1.1rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
            transition: all 0.3s ease;
        }

        .status-badge:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.18);
        }

        .status-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }

        .status-failed {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
        }

        .status-partial {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
        }

        .summary-failed-step {
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            border: 2px solid #fecaca;
            border-radius: 16px;
            padding: 20px 24px;
            margin-top: 24px;
            color: #dc2626;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.1);
        }

        .summary-failed-step::before {
            content: '⚠️ ';
            margin-right: 8px;
            font-size: 1.2rem;
        }

        h1 {
            color: #1e293b;
            text-align: center;
            margin-bottom: 60px;
            font-size: 3rem;
            font-weight: 800;
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>全新执行总结设计</h1>
        
        <!-- 成功案例 -->
        <div class="summary-section">
            <div class="summary-header">
                <div class="summary-title">流水线执行总结</div>
            </div>
            <div class="summary-content">
                <div class="summary-stats">
                    <div class="stat-card stat-total">
                        <span class="stat-number">5</span>
                        <span class="stat-label">总步骤数</span>
                    </div>
                    <div class="stat-card stat-success">
                        <span class="stat-number">5</span>
                        <span class="stat-label">成功步骤</span>
                    </div>
                    <div class="stat-card stat-failed">
                        <span class="stat-number">0</span>
                        <span class="stat-label">失败步骤</span>
                    </div>
                    <div class="stat-card stat-time">
                        <span class="stat-number">12.5s</span>
                        <span class="stat-label">总耗时</span>
                    </div>
                </div>
                <div class="summary-status">
                    <div class="status-badge status-success">✅ 执行成功</div>
                </div>
            </div>
        </div>
        
        <!-- 部分成功案例 -->
        <div class="summary-section">
            <div class="summary-header">
                <div class="summary-title">流水线执行总结</div>
            </div>
            <div class="summary-content">
                <div class="summary-stats">
                    <div class="stat-card stat-total">
                        <span class="stat-number">5</span>
                        <span class="stat-label">总步骤数</span>
                    </div>
                    <div class="stat-card stat-success">
                        <span class="stat-number">3</span>
                        <span class="stat-label">成功步骤</span>
                    </div>
                    <div class="stat-card stat-failed">
                        <span class="stat-number">2</span>
                        <span class="stat-label">失败步骤</span>
                    </div>
                    <div class="stat-card stat-time">
                        <span class="stat-number">8.2s</span>
                        <span class="stat-label">总耗时</span>
                    </div>
                </div>
                <div class="summary-status">
                    <div class="status-badge status-partial">⚠️ 部分成功</div>
                </div>
                <div class="summary-failed-step">
                    在步骤 3 (安装Docker(如果未安装)) 执行失败，流水线终止
                </div>
            </div>
        </div>
        
        <!-- 失败案例 -->
        <div class="summary-section">
            <div class="summary-header">
                <div class="summary-title">流水线执行总结</div>
            </div>
            <div class="summary-content">
                <div class="summary-stats">
                    <div class="stat-card stat-total">
                        <span class="stat-number">5</span>
                        <span class="stat-label">总步骤数</span>
                    </div>
                    <div class="stat-card stat-success">
                        <span class="stat-number">0</span>
                        <span class="stat-label">成功步骤</span>
                    </div>
                    <div class="stat-card stat-failed">
                        <span class="stat-number">1</span>
                        <span class="stat-label">失败步骤</span>
                    </div>
                    <div class="stat-card stat-time">
                        <span class="stat-number">1.2s</span>
                        <span class="stat-label">总耗时</span>
                    </div>
                </div>
                <div class="summary-status">
                    <div class="status-badge status-failed">❌ 执行失败</div>
                </div>
                <div class="summary-failed-step">
                    在步骤 1 (检查Docker是否安装) 执行失败，流水线终止
                </div>
            </div>
        </div>
    </div>
</body>
</html>
