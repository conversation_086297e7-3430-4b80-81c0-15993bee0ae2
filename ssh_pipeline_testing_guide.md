# SSH流水线测试指南 - Docker检查与HelloWorld测试

本指南将帮助您测试SSH流水线系统的Docker检查和HelloWorld容器运行功能。

## 一、测试前准备

### 1. 导入测试数据

将提供的SQL脚本`sql/ssh_test_data.sql`导入到数据库中：

```sql
-- 在您的数据库客户端执行以下命令导入测试数据
source D:\nanno\WORK-CODE\GitLab\inks-service-sa-uts\sql\ssh_test_data.sql
```

### 2. 修改服务器配置

修改测试服务器配置以匹配您的实际环境：

1. 登录系统后，进入"服务器管理"模块
2. 找到"Docker测试服务器"配置
3. 修改以下信息：
   - 主机地址（Host）：修改为您实际的服务器IP地址
   - 用户名（Username）：修改为您实际的SSH用户名
   - 密码（Password）：修改为您实际的SSH密码
4. 保存更改

## 二、执行测试流程

### 1. 使用API接口测试

可以使用Postman或其他API测试工具调用以下接口：

```
POST http://your-server-address/S34M11B2/executePipeline
```

参数：
- `pipelineId`: pipeline-docker-check-001
- `serverId`: server-test-001

### 2. 从前端界面测试

1. 登录系统
2. 进入"SSH流水线"模块
3. 选择"Docker检查与HelloWorld测试"流水线
4. 选择"Docker测试服务器"作为目标服务器
5. 点击"执行流水线"按钮

## 三、观察测试结果

执行流水线后，系统将按照以下步骤依次执行：

1. **检查Docker是否安装**
   - 成功：输出中包含"Docker version"
   - 失败：输出中包含"command not found"（如果未安装）

2. **检查Docker服务状态**
   - 成功：输出中包含"Active: active (running)"
   - 失败：输出中包含"inactive"或"not-found"（如果服务未运行）

3. **安装Docker(如果未安装)**
   - 如果前面检测未安装，此步骤将自动安装Docker
   - 成功：输出中包含"Docker already installed"或安装成功提示
   - 失败：输出中包含"Unable to locate package"或其他错误信息

4. **启动Docker服务(如果未运行)**
   - 如果服务未运行，此步骤将启动Docker服务
   - 成功：输出中包含"Docker started"或"Docker already running"
   - 失败：输出中包含"Failed to start"

5. **运行Hello World容器**
   - 成功：输出中包含"Hello from Docker!"
   - 失败：输出中包含"Error"或"not found"

6. **显示Docker系统信息**
   - 成功：输出中包含Docker系统信息，包括容器和镜像数量
   - 失败：输出中包含错误信息

## 四、测试结果解读

### 成功场景

1. **已安装Docker的情况**：
   - 步骤1和2会显示Docker已安装并运行
   - 步骤3和4会跳过安装和启动
   - 步骤5会成功运行Hello World容器
   - 步骤6会显示Docker系统信息

2. **未安装Docker的情况**：
   - 步骤1和2会失败，但流水线会继续执行
   - 步骤3会安装Docker
   - 步骤4会启动Docker服务
   - 步骤5会成功运行Hello World容器
   - 步骤6会显示Docker系统信息

### 失败场景

如果在任何关键步骤（步骤3、4、5）出现错误，流水线将中止执行，并在历史记录中标记为"Failed"。常见失败原因：

- 服务器无法访问外网安装包
- 用户权限不足（无法执行sudo命令）
- 服务器磁盘空间不足
- Docker服务无法启动

## 五、流水线历史查看

执行完成后，可以在"SSH执行历史"模块查看详细的执行记录：

1. 进入"SSH执行历史"模块
2. 找到最近的执行记录
3. 点击"查看详情"，可以看到每个步骤的具体执行情况
4. 检查每个步骤的输出和退出状态

## 六、故障排除

如果测试失败，请检查：

1. **连接问题**：
   - 服务器IP地址、端口是否正确
   - 用户名、密码是否正确
   - 服务器防火墙是否允许SSH连接

2. **权限问题**：
   - SSH用户是否有sudo权限
   - 是否需要输入sudo密码（建议配置免密码sudo）

3. **网络问题**：
   - 服务器是否能访问apt仓库
   - 是否能拉取Docker镜像

4. **资源问题**：
   - 服务器磁盘空间是否充足
   - 内存是否足够运行Docker

## 七、附加测试

成功执行基本流水线后，可以尝试以下附加测试：

1. **重复执行测试**：
   - 再次运行同一流水线，观察步骤3和4是否正确跳过安装和启动过程

2. **异步执行测试**：
   - 使用`/S34M11B2/executeAsyncPipeline`接口测试异步执行
   - 使用`/S34M11B2/getExecutionStatus`查询执行状态

3. **单命令执行测试**：
   - 使用`/S34M11B2/executeCommand`接口测试执行单个Docker命令
   - 例如：`docker ps -a`查看所有容器
