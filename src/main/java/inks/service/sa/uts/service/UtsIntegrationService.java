package inks.service.sa.uts.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.UtsIntegrationPojo;
import inks.service.sa.uts.domain.UtsIntegrationEntity;

import com.github.pagehelper.PageInfo;

/**
 * API整合转发(UtsIntegration)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-20 13:06:41
 */
public interface UtsIntegrationService {


    UtsIntegrationPojo getEntity(String key);

    PageInfo<UtsIntegrationPojo> getPageList(QueryParam queryParam);

    UtsIntegrationPojo insert(UtsIntegrationPojo utsIntegrationPojo);

    UtsIntegrationPojo update(UtsIntegrationPojo utsIntegrationpojo);

    int delete(String key);

    UtsIntegrationPojo getEntityByCodeAndType(String code, Integer proxytype);
}
