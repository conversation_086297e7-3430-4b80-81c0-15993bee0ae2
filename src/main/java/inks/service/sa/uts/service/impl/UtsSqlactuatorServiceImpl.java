package inks.service.sa.uts.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.uts.domain.UtsSqlactuatorEntity;
import inks.service.sa.uts.domain.pojo.UtsSqlactuatorPojo;
import inks.service.sa.uts.mapper.UtsSqlactuatorMapper;
import inks.service.sa.uts.service.UtsSqlactuatorService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * SQL执行器(UtsSqlactuator)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-06 16:23:35
 */
@Service("utsSqlactuatorService")
public class UtsSqlactuatorServiceImpl implements UtsSqlactuatorService {
    @Resource
    private UtsSqlactuatorMapper utsSqlactuatorMapper;

    @Override
    public UtsSqlactuatorPojo getEntity(String key) {
        return this.utsSqlactuatorMapper.getEntity(key);
    }


    @Override
    public PageInfo<UtsSqlactuatorPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<UtsSqlactuatorPojo> lst = utsSqlactuatorMapper.getPageList(queryParam);
            return new PageInfo<>(lst);
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public UtsSqlactuatorPojo insert(UtsSqlactuatorPojo utsSqlactuatorPojo) {
        //初始化NULL字段
        cleanNull(utsSqlactuatorPojo);
        UtsSqlactuatorEntity utsSqlactuatorEntity = new UtsSqlactuatorEntity(); 
        BeanUtils.copyProperties(utsSqlactuatorPojo,utsSqlactuatorEntity);
        //生成雪花id
          utsSqlactuatorEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          utsSqlactuatorEntity.setRevision(1);  //乐观锁
          this.utsSqlactuatorMapper.insert(utsSqlactuatorEntity);
        return this.getEntity(utsSqlactuatorEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param utsSqlactuatorPojo 实例对象
     * @return 实例对象
     */
    @Override
    public UtsSqlactuatorPojo update(UtsSqlactuatorPojo utsSqlactuatorPojo) {
        UtsSqlactuatorEntity utsSqlactuatorEntity = new UtsSqlactuatorEntity(); 
        BeanUtils.copyProperties(utsSqlactuatorPojo,utsSqlactuatorEntity);
        this.utsSqlactuatorMapper.update(utsSqlactuatorEntity);
        return this.getEntity(utsSqlactuatorEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.utsSqlactuatorMapper.delete(key) ;
    }
    

    private static void cleanNull(UtsSqlactuatorPojo utsSqlactuatorPojo) {
        if(utsSqlactuatorPojo.getSqlname()==null) utsSqlactuatorPojo.setSqlname("");
        if(utsSqlactuatorPojo.getSqldata()==null) utsSqlactuatorPojo.setSqldata("");
        if(utsSqlactuatorPojo.getModulename()==null) utsSqlactuatorPojo.setModulename("");
        if(utsSqlactuatorPojo.getVersion()==null) utsSqlactuatorPojo.setVersion("");
        if(utsSqlactuatorPojo.getDatabaseid()==null) utsSqlactuatorPojo.setDatabaseid("");
        if(utsSqlactuatorPojo.getResultjson()==null) utsSqlactuatorPojo.setResultjson("");
        if(utsSqlactuatorPojo.getBustype()==null) utsSqlactuatorPojo.setBustype(0);
        if(utsSqlactuatorPojo.getStatusnum()==null) utsSqlactuatorPojo.setStatusnum(0);
        if(utsSqlactuatorPojo.getCode()==null) utsSqlactuatorPojo.setCode("");
        if(utsSqlactuatorPojo.getRemark()==null) utsSqlactuatorPojo.setRemark("");
        if(utsSqlactuatorPojo.getCreateby()==null) utsSqlactuatorPojo.setCreateby("");
        if(utsSqlactuatorPojo.getCreatebyid()==null) utsSqlactuatorPojo.setCreatebyid("");
        if(utsSqlactuatorPojo.getCreatedate()==null) utsSqlactuatorPojo.setCreatedate(new Date());
        if(utsSqlactuatorPojo.getLister()==null) utsSqlactuatorPojo.setLister("");
        if(utsSqlactuatorPojo.getListerid()==null) utsSqlactuatorPojo.setListerid("");
        if(utsSqlactuatorPojo.getModifydate()==null) utsSqlactuatorPojo.setModifydate(new Date());
        if(utsSqlactuatorPojo.getCustom1()==null) utsSqlactuatorPojo.setCustom1("");
        if(utsSqlactuatorPojo.getCustom2()==null) utsSqlactuatorPojo.setCustom2("");
        if(utsSqlactuatorPojo.getCustom3()==null) utsSqlactuatorPojo.setCustom3("");
        if(utsSqlactuatorPojo.getCustom4()==null) utsSqlactuatorPojo.setCustom4("");
        if(utsSqlactuatorPojo.getCustom5()==null) utsSqlactuatorPojo.setCustom5("");
        if(utsSqlactuatorPojo.getTenantid()==null) utsSqlactuatorPojo.setTenantid("");
        if(utsSqlactuatorPojo.getTenantname()==null) utsSqlactuatorPojo.setTenantname("");
        if(utsSqlactuatorPojo.getRevision()==null) utsSqlactuatorPojo.setRevision(0);
   }

    @Override
    public UtsSqlactuatorPojo getMaxEntity(String code) {
        return this.utsSqlactuatorMapper.getMaxEntity(code);
    }
}
