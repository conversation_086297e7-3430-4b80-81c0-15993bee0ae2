package inks.service.sa.uts.service.impl;

import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.uts.config.SshExecutionConfig;
import inks.service.sa.uts.domain.pojo.*;
import inks.service.sa.uts.mapper.SaSshhistoryMapper;
import inks.service.sa.uts.mapper.SaSshpipelinesMapper;
import inks.service.sa.uts.mapper.SaSshpipelinesitemMapper;
import inks.service.sa.uts.mapper.SaSshserversMapper;
import inks.service.sa.uts.service.SshExecutorService;
import inks.service.sa.uts.util.PatternMatcherUtil;
import net.schmizz.sshj.SSHClient;
import net.schmizz.sshj.common.IOUtils;
import net.schmizz.sshj.connection.channel.direct.Session;
import net.schmizz.sshj.connection.channel.direct.Session.Command;
import net.schmizz.sshj.transport.verification.PromiscuousVerifier;
import net.schmizz.sshj.userauth.keyprovider.KeyProvider;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

/**
 * SSH命令执行服务实现类
 * 基于SSHJ库实现SSH命令执行和流水线管理
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Service("sshExecutorService")
public class SshExecutorServiceImpl implements SshExecutorService {

    private static final Logger logger = LoggerFactory.getLogger(SshExecutorServiceImpl.class);

    /**
     * 执行中的会话缓存 <会话ID, 执行结果>
     */
    private final Map<String, SshExecutionResult> activeSessionsCache = new ConcurrentHashMap<>();

    /**
     * 执行任务线程池
     */
    private final ExecutorService executor = Executors.newFixedThreadPool(10);

    @Resource
    private SaSshpipelinesMapper saSshpipelinesMapper;

    @Resource
    private SaSshpipelinesitemMapper saSshpipelinesitemMapper;

    @Resource
    private SaSshserversMapper saSshserversMapper;

    @Resource
    private SaSshhistoryMapper saSshhistoryMapper;
    @Resource
    private SaSshhistoryitemServiceImpl saSshhistoryitemService;
    @Autowired
    private SaSshhistoryServiceImpl saSshhistoryService;
    @Autowired
    private SshExecutionConfig sshConfig;

    @Override
    public SshCommandResult executeCommand(SshConnectionInfo connectionInfo, String command,
                                           String successPattern, String errorPattern,
                                           int timeoutMs) {
        SSHClient ssh = null;
        Session session = null;
        Command cmd = null;
        SshCommandResult result = new SshCommandResult(command);

        try {
            // 建立SSH连接
            ssh = createSshConnection(connectionInfo);

            // 创建会话
            session = ssh.startSession();
            session.allocateDefaultPTY();

            // 开始执行命令
            cmd = session.exec(command);

            // 读取命令输出（设置超时）
            String output = readCommandOutput(cmd, timeoutMs);
            String error = cmd.getErrorStream() != null ?
                    IOUtils.readFully(cmd.getErrorStream()).toString() : "";

            // 等待命令完成
            cmd.join(timeoutMs, TimeUnit.MILLISECONDS);

            // 填充执行结果
            result.complete(cmd.getExitStatus(), output, error);

            // 使用高级正则匹配工具进行成功模式匹配
            if (StringUtils.isNotBlank(successPattern)) {
                PatternMatcherUtil.MatchResult matchResult = PatternMatcherUtil.matchSuccessPattern(
                        successPattern, output, error);

                result.setMatchedSuccessPattern(matchResult.isMatched());

                // 添加详细日志记录
                logger.info("Success pattern matching: pattern='{}', type={}, matched={}",
                        successPattern, matchResult.getMatchType(), matchResult.isMatched());

                if (matchResult.getErrorMessage() != null) {
                    logger.error("Success pattern error: {}", matchResult.getErrorMessage());
                }

                // 正则表达式优先：如果设置了成功模式但未匹配，则强制设置为失败
                if (!matchResult.isMatched()) {
                    result.setStatus("Failed");
                    String errorMsg = matchResult.getErrorMessage() != null ?
                            matchResult.getErrorMessage() : "Success pattern not matched: " + successPattern;
                    result.setError(result.getError() + "\n[Pattern Match Failed] " + errorMsg);
                    logger.warn("Command execution failed due to success pattern not matched: {}", successPattern);
                } else {
                    // 显式设置为成功
                    result.setStatus("Success");
                    logger.info("Command execution succeeded - success pattern matched: '{}'",
                            matchResult.getMatchedText());
                }
            }

            // 使用高级正则匹配工具进行错误模式匹配
            if (StringUtils.isNotBlank(errorPattern)) {
                PatternMatcherUtil.MatchResult matchResult = PatternMatcherUtil.matchErrorPattern(
                        errorPattern, output, error);

                result.setMatchedErrorPattern(matchResult.isMatched());

                // 添加详细日志记录
                logger.debug("Error pattern matching: pattern='{}', type={}, matched={}",
                        errorPattern, matchResult.getMatchType(), matchResult.isMatched());

                if (matchResult.getErrorMessage() != null) {
                    logger.warn("Error pattern warning: {}", matchResult.getErrorMessage());
                }

                // 如果匹配到错误模式，强制设置为失败
                if (matchResult.isMatched()) {
                    result.setStatus("Failed");
                    result.setError(result.getError() + "\n[Pattern Match] Error pattern detected: " +
                            errorPattern + " -> '" + matchResult.getMatchedText() + "'");
                    logger.warn("Command execution failed due to error pattern matched: {} -> '{}'",
                            errorPattern, matchResult.getMatchedText());
                }
            }

        } catch (TimeoutException e) {
            logger.error("Command execution timed out: {}", command, e);
            result.timeout();
            result.setError("Command execution timed out after " + timeoutMs + "ms");
        } catch (Exception e) {
            logger.error("Failed to execute SSH command: {}", command, e);
            result.complete(-1, "", e.getMessage());
        } finally {
            // 关闭资源
            closeQuietly(cmd);
            closeQuietly(session);
            closeQuietly(ssh);
        }

        return result;
    }

    @Override
    @Transactional
    public SshExecutionResult executePipeline(String pipelineId, String serverId) {
        // 同步执行流水线，直接使用异步方法然后等待完成
        String sessionId = executeAsyncPipelineInternal(pipelineId, serverId);

        // 等待执行完成，最多等待2小时
        SshExecutionResult result = null;
        try {
            for (int i = 0; i < 720; i++) { // 720 * 10s = 2小时
                result = getExecutionStatus(sessionId);
                if (!"Running".equals(result.getStatus())) {
                    break;
                }
                Thread.sleep(10000); // 每10秒检查一次
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.warn("Waiting for pipeline execution was interrupted", e);
        }

        // 如果仍未完成，标记为超时
        if ("Running".equals(result.getStatus())) {
            cancelExecution(sessionId);
            result = getExecutionStatus(sessionId);
        }

        return result;
    }

    @Override
    public List<SshExecutionResult> executePipelineToMultipleServers(String pipelineId, List<String> serverIds) {
        List<SshExecutionResult> results = new ArrayList<>();
        List<Future<SshExecutionResult>> futures = new ArrayList<>();

        // 为每个服务器提交异步执行任务
        for (String serverId : serverIds) {
            Future<SshExecutionResult> future = executor.submit(() -> executePipeline(pipelineId, serverId));
            futures.add(future);
        }

        // 等待所有任务完成并收集结果
        for (Future<SshExecutionResult> future : futures) {
            try {
                results.add(future.get());
            } catch (Exception e) {
                logger.error("Failed to execute pipeline on server", e);
                SshExecutionResult errorResult = new SshExecutionResult();
                errorResult.setStatus("Failed");
                errorResult.setEndTime(new Date());
                results.add(errorResult);
            }
        }

        return results;
    }

    @Override
    public SshExecutionResult quickShutdown(String serverId) {
        // 查找关机流水线
        String shutdownPipelineId = findSystemPipeline("Ubuntu安全关机");
        if (shutdownPipelineId == null) {
            throw new BaseBusinessException("系统关机流水线未配置");
        }

        return executePipeline(shutdownPipelineId, serverId);
    }

    @Override
    public SshExecutionResult quickRestart(String serverId) {
        // 查找重启流水线
        String restartPipelineId = findSystemPipeline("Ubuntu系统重启");
        if (restartPipelineId == null) {
            throw new BaseBusinessException("系统重启流水线未配置");
        }

        return executePipeline(restartPipelineId, serverId);
    }

    @Override
    public String executeAsyncPipeline(String pipelineId, String serverId) {
        return executeAsyncPipelineInternal(pipelineId, serverId);
    }

    @Override
    public boolean cancelExecution(String sessionId) {
        SshExecutionResult result = activeSessionsCache.get(sessionId);
        if (result == null) {
            return false;
        }

        // 标记为取消状态
        result.cancel();

        // 更新数据库状态
        try {
            updateExecutionStatus(result.getHistoryId(), "Cancelled");
        } catch (Exception e) {
            logger.error("Failed to update execution status in database", e);
        }

        return true;
    }

    @Override
    public SshExecutionResult getExecutionStatus(String sessionId) {
        return activeSessionsCache.getOrDefault(sessionId, new SshExecutionResult());
    }

    /**
     * 内部方法：异步执行流水线并返回会话ID
     */
    private String executeAsyncPipelineInternal(String pipelineId, String serverId) {
        // 1. 获取流水线和服务器信息
        SaSshpipelinesPojo pipeline = saSshpipelinesMapper.getEntity(pipelineId);
        if (pipeline == null) {
            throw new BaseBusinessException("流水线不存在: " + pipelineId);
        }

        SaSshserversPojo server = saSshserversMapper.getEntity(serverId);
        if (server == null) {
            throw new BaseBusinessException("服务器不存在: " + serverId);
        }

        // 获取流水线步骤
        List<SaSshpipelinesitemPojo> steps = saSshpipelinesitemMapper.getList(pipelineId);
        if (steps == null || steps.isEmpty()) {
            throw new BaseBusinessException("流水线没有定义步骤");
        }

        // 2. 生成会话ID并创建执行历史记录
        String sessionId = inksSnowflake.getSnowflake().nextIdStr();
        String historyId = createExecutionHistory(pipeline, server, sessionId);

        // 3. 创建执行结果对象并缓存
        SshExecutionResult executionResult = new SshExecutionResult();
        executionResult.setSessionId(sessionId);
        executionResult.setPipelineId(pipelineId);
        executionResult.setPipelineName(pipeline.getPipelinename());
        executionResult.setServerId(serverId);
        executionResult.setServerName(server.getServername());
        executionResult.setHistoryId(historyId);
        executionResult.setTotalSteps(steps.size());

        //执行中的会话缓存 <会话ID, 执行结果>
        activeSessionsCache.put(sessionId, executionResult);

        // 4. 提交异步任务执行流水线
        executor.submit(() -> {
            try {
                logger.info("Starting pipeline execution for session: {}", sessionId);
                executeStepsIncrementally(executionResult, steps, server);
                logger.info("Pipeline execution completed for session: {}", sessionId);
            } catch (Exception e) {
                logger.error("Pipeline execution failed for session: " + sessionId, e);
                executionResult.cancel();
                updateExecutionStatus(historyId, "Failed");
                // 添加异常日志到执行结果
                executionResult.addLog("");
                executionResult.addLog("❌ 流水线执行异常: " + e.getMessage());
            }
        });

        return sessionId;
    }


    /**
     * 按步骤增量执行流水线并实时返回结果
     * 每执行一个流水线步骤后立即返回结果，而不是等待所有步骤完成
     *
     * @param pipelineId 流水线ID
     * @param serverId   服务器ID
     * @return 流水线会话ID，用于获取进一步的执行结果
     */
    @Override
    public String executeIncrementalPipeline(String pipelineId, String serverId) {
        // 1. 验证参数和查找流水线
        SaSshpipelinesPojo pipeline = saSshpipelinesMapper.getEntity(pipelineId);
        if (pipeline == null) {
            throw new BaseBusinessException("流水线不存在: " + pipelineId);
        }

        SaSshserversPojo server = saSshserversMapper.getEntity(serverId);
        if (server == null) {
            throw new BaseBusinessException("服务器不存在: " + serverId);
        }

        // 获取流水线步骤
        List<SaSshpipelinesitemPojo> steps = saSshpipelinesitemMapper.getList(pipelineId);
        if (steps == null || steps.isEmpty()) {
            throw new BaseBusinessException("流水线没有定义步骤");
        }

        // 2. 生成会话ID并创建执行历史记录
        String sessionId = inksSnowflake.getSnowflake().nextIdStr();
        String historyId = createExecutionHistory(pipeline, server, sessionId);

        // 3. 创建执行结果对象并缓存
        SshExecutionResult executionResult = new SshExecutionResult();
        executionResult.setSessionId(sessionId);
        executionResult.setPipelineId(pipelineId);
        executionResult.setPipelineName(pipeline.getPipelinename());
        executionResult.setServerId(serverId);
        executionResult.setServerName(server.getServername());
        executionResult.setHistoryId(historyId);
        executionResult.setTotalSteps(steps.size());

        // 执行中的会话缓存 <会话ID, 执行结果>
        activeSessionsCache.put(sessionId, executionResult);

        // 4. 提交异步任务执行流水线 - 使用增量执行模式
        executor.submit(() -> {
            try {
                executeStepsIncrementally(executionResult, steps, server);
            } catch (Exception e) {
                logger.error("Incremental pipeline execution failed", e);
                executionResult.cancel();
                updateExecutionStatus(historyId, "Failed");
            }
        });

        return sessionId;
    }

    /**
     * 增量执行流水线步骤
     * 每执行完一个步骤就立即将结果写入历史表，便于前端实时获取
     */
    private void executeStepsIncrementally(SshExecutionResult executionResult,
                                           List<SaSshpipelinesitemPojo> steps,
                                           SaSshserversPojo server) {

        // 创建SSH连接信息
        SshConnectionInfo connectionInfo = new SshConnectionInfo();
        connectionInfo.setServerId(server.getId());
        connectionInfo.setServerName(server.getServername());
        connectionInfo.setHost(server.getHost());
        connectionInfo.setPort(server.getPort());
        connectionInfo.setUsername(server.getUsername());
        connectionInfo.setPassword(server.getPassword());

        // 添加流水线开始日志
        executionResult.addLog("╔══════════════════════════════════════════════════════════════");
        executionResult.addLog(String.format("║ 🚀 开始执行流水线: %s", executionResult.getPipelineName()));
        executionResult.addLog(String.format("║ 📡 目标服务器: %s", executionResult.getServerName()));
        executionResult.addLog(String.format("║ 📋 总步骤数: %d", steps.size()));
        executionResult.addLog(String.format("║ ⏰ 开始时间: %s", new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())));
        executionResult.addLog("╚══════════════════════════════════════════════════════════════");

        // 当前执行的步骤索引
        int currentStepIndex = 0;

        // 逐步执行命令
        logger.info("Starting to execute {} steps for session: {}", steps.size(), executionResult.getSessionId());
        for (SaSshpipelinesitemPojo step : steps) {
            // 检查是否已被取消
            if ("Cancelled".equals(executionResult.getStatus())) {
                break;
            }

            // 更新当前执行的步骤信息
            currentStepIndex++;
            executionResult.setCurrentStepIndex(currentStepIndex - 1);  // 0-based index
            executionResult.setCurrentStepName(step.getStepname());

            // 立即添加步骤开始执行的日志
            executionResult.addLog("");
            executionResult.addLog(String.format("🚀 开始执行步骤 %d: %s", currentStepIndex, step.getStepname()));
            executionResult.addLog(String.format("📝 命令: %s", step.getCommandtext()));

            // 获取有效的超时时间和重试次数
            int timeoutMs = sshConfig.getEffectiveTimeout(step.getTimeoutms());
            int maxRetries = sshConfig.getEffectiveRetries(step.getRetrycount());

            // 添加调试日志
            logger.info("Step {} - Configured timeout: {}ms, Effective timeout: {}ms, Max retries: {}",
                    currentStepIndex, step.getTimeoutms(), timeoutMs, maxRetries);

            // 显示超时和重试信息
            executionResult.addLog(String.format("⚙️ 超时配置: %d秒 (原始: %dms)", timeoutMs / 1000, step.getTimeoutms()));
            if (maxRetries > 0) {
                executionResult.addLog(String.format("🔄 重试配置: %d次", maxRetries));
            }

            // 显示错误继续配置
            Integer continueOnError = step.getContinueonerror();
            if (continueOnError != null && continueOnError == 1) {
                executionResult.addLog(String.format("⚠️ 错误处理: 允许继续执行 (ContinueOnError=1)"));
            } else {
                executionResult.addLog(String.format("🛑 错误处理: 失败时停止 (ContinueOnError=0)"));
            }

            executionResult.addLog(String.format("⚡ 正在执行中..."));

            // 强制刷新日志显示（确保前端能立即看到）
            try {
                Thread.sleep(100); // 短暂延迟确保日志被处理
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }

            // 执行命令（添加智能重试逻辑）
            SshCommandResult stepResult = null;
            int retries = 0;
            // 注意：timeoutMs和maxRetries已经在上面获取了，不需要重复获取

            do {
                // 如果是重试，记录重试信息并等待
                if (retries > 0) {
                    executionResult.addLog("");
                    executionResult.addLog(String.format("🔄 步骤 %d 重试 %d/%d: %s",
                            currentStepIndex, retries, maxRetries, step.getStepname()));

                    // 智能重试延迟：使用配置的退避策略
                    int delayMs = sshConfig.calculateRetryDelay(retries);
                    try {
                        executionResult.addLog(String.format("⏳ 等待 %d 毫秒后重试...", delayMs));
                        Thread.sleep(delayMs);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        executionResult.addLog("❌ 重试被中断");
                        break;
                    }
                }

                // 智能选择执行方法
                boolean useRealTimeOutput = shouldUseRealTimeOutput(step.getCommandtext(), timeoutMs);

                if (useRealTimeOutput) {
                    executionResult.addLog("📡 使用实时输出方法执行");
                    stepResult = executeCommandWithRealTimeOutput(
                            connectionInfo,
                            step.getCommandtext(),
                            step.getSuccesspattern(),
                            step.getErrorpattern(),
                            timeoutMs,
                            executionResult,
                            currentStepIndex
                    );
                } else {
                    executionResult.addLog("⚡ 使用传统方法执行");
                    stepResult = executeCommand(
                            connectionInfo,
                            step.getCommandtext(),
                            step.getSuccesspattern(),
                            step.getErrorpattern(),
                            timeoutMs
                    );
                }

                // 设置步骤名称和重试次数到结果对象中
                stepResult.setStepName(step.getStepname());
                stepResult.setRetryCount(retries);

                // 检查命令执行状态
                if ("Success".equals(stepResult.getStatus())) {
                    // 命令成功执行，跳出重试循环
                    if (retries > 0) {
                        executionResult.addLog(String.format("✅ 重试成功: 步骤在第 %d 次重试后成功", retries));
                    }
                    break;
                } else {
                    // 命令执行失败
                    if (retries > 0) {
                        executionResult.addLog(String.format("❌ 重试 %d/%d 失败: %s",
                                retries, maxRetries, stepResult.getStatus()));
                    }

                    if (retries >= maxRetries) {
                        // 已达到最大重试次数，跳出循环
                        if (maxRetries > 0) {
                            executionResult.addLog(String.format("🚫 重试失败: 步骤在 %d 次重试后仍然失败", maxRetries));
                        }
                        break;
                    }
                    // 增加重试计数并继续
                    retries++;
                }

            } while (retries <= maxRetries);

            // 记录步骤执行结果
            executionResult.addStepResult(stepResult);

            // 添加格式化的步骤执行日志
            addFormattedStepLog(executionResult, currentStepIndex, step, stepResult);

            // 保存步骤执行结果到数据库
            createStepExecutionHistory(
                    executionResult.getHistoryId(),
                    step,
                    stepResult
            );

            // 注意：不在这里更新步骤索引，因为currentStepIndex在循环开始时已经正确设置

            // 智能错误处理：根据continueonerror配置决定是否继续
            if (!"Success".equals(stepResult.getStatus())) {
                boolean shouldContinue = sshConfig.getEffectiveContinueOnError(step.getContinueonerror());

                if (shouldContinue) {
                    executionResult.addLog("");
                    executionResult.addLog(String.format("⚠️  步骤 %d 失败但继续: %s",
                            currentStepIndex, step.getStepname()));
                    executionResult.addLog(String.format("📋 ContinueOnError=1，允许错误继续执行"));
                } else {
                    executionResult.addLog("");
                    executionResult.addLog(String.format("🛑 流水线中断: 步骤 %d 执行失败", currentStepIndex));
                    executionResult.addLog(String.format("📋 ContinueOnError=0，失败时停止执行"));

                    // 设置流水线整体状态为失败
                    executionResult.setStatus("Failed");
                    break;
                }
            } else {
                // 步骤成功，添加继续执行的日志
                if (currentStepIndex < steps.size()) {
                    executionResult.addLog("");
                    executionResult.addLog(String.format("✅ 步骤 %d 执行成功，继续执行下一步...", currentStepIndex));
                    logger.info("Step {} completed successfully, continuing to next step. Session: {}",
                            currentStepIndex, executionResult.getSessionId());
                } else {
                    executionResult.addLog("");
                    executionResult.addLog(String.format("✅ 步骤 %d 执行成功，所有步骤已完成", currentStepIndex));
                    logger.info("Step {} completed successfully, all steps finished. Session: {}",
                            currentStepIndex, executionResult.getSessionId());
                }
            }
        }

        logger.info("All steps completed for session: {}, status: {}",
                executionResult.getSessionId(), executionResult.getStatus());

        // 添加流水线完成日志
        addPipelineCompletionLog(executionResult);

        // 标记流水线执行完成
        executionResult.complete();

        logger.info("Pipeline execution marked as complete for session: {}, final status: {}",
                executionResult.getSessionId(), executionResult.getStatus());

        // 更新数据库执行状态
        updateExecutionStatus(executionResult.getHistoryId(), executionResult.getStatus());
    }

    /**
     * 创建流水线执行历史记录
     */
    private String createExecutionHistory(SaSshpipelinesPojo pipeline,
                                          SaSshserversPojo server,
                                          String sessionId) {
        // 生成ID
        String historyId = inksSnowflake.getSnowflake().nextIdStr();

        // 构建历史记录数据
        SaSshhistoryPojo history = new SaSshhistoryPojo();
        history.setId(historyId);
        history.setPipelineid(pipeline.getId());
        history.setPipelinename(pipeline.getPipelinename());
        history.setServerid(server.getId());
        history.setServername(server.getServername());
        history.setSessionid(sessionId);
        history.setStatus("Running");
        history.setStarttime(new Date());
        history.setRownum(1);
        history.setRemark("执行SSH流水线: " + pipeline.getPipelinename());

        // 设置创建者信息
        // 这里应该使用当前登录用户信息，这里使用系统用户
        history.setCreateby("System");
        history.setCreatebyid("system");
        history.setCreatedate(new Date());
        history.setLister("System");
        history.setListerid("system");
        history.setModifydate(new Date());

        // 保存到数据库
        saSshhistoryService.insert(history);

        return historyId;
    }

    /**
     * 添加流水线完成日志
     */
    private void addPipelineCompletionLog(SshExecutionResult executionResult) {
        long totalDuration = executionResult.getEndTime() != null ?
                executionResult.getEndTime().getTime() - executionResult.getStartTime().getTime() : 0;

        int successCount = 0;
        int failedCount = 0;
        String firstFailedStep = null;

        for (SshCommandResult stepResult : executionResult.getStepResults()) {
            // 排除系统日志
            if (stepResult.getIsSystemLog() != null && stepResult.getIsSystemLog()) {
                continue;
            }
            if ("Success".equals(stepResult.getStatus())) {
                successCount++;
            } else {
                failedCount++;
                if (firstFailedStep == null) {
                    firstFailedStep = stepResult.getStepName();
                }
            }
        }

        executionResult.addLog("");
        executionResult.addLog("╔══════════════════════════════════════════════════════════════");
        executionResult.addLog("║ 📊 流水线执行总结");
        executionResult.addLog("╠══════════════════════════════════════════════════════════════");
        executionResult.addLog(String.format("║ 📋 总步骤数: %d", successCount + failedCount));
        executionResult.addLog(String.format("║ ✅ 成功步骤: %d", successCount));
        executionResult.addLog(String.format("║ ❌ 失败步骤: %d", failedCount));

        if (firstFailedStep != null) {
            executionResult.addLog(String.format("║ 🚫 首个失败步骤: %s", firstFailedStep));
        }

        String statusIcon = "Success".equals(executionResult.getStatus()) ? "✅" : "❌";
        String statusText = "Success".equals(executionResult.getStatus()) ? "成功" : "失败";

        executionResult.addLog(String.format("║ 🏁 最终状态: %s %s", statusIcon, statusText));
        executionResult.addLog(String.format("║ ⏱️  总耗时: %d毫秒 (%.2f秒)", totalDuration, totalDuration / 1000.0));
        executionResult.addLog(String.format("║ ⏰ 完成时间: %s",
                new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())));
        executionResult.addLog("╚══════════════════════════════════════════════════════════════");
    }

    /**
     * 添加格式化的步骤执行日志
     */
    private void addFormattedStepLog(SshExecutionResult executionResult, int stepIndex,
                                     SaSshpipelinesitemPojo step, SshCommandResult result) {
        // 添加步骤分隔线
        executionResult.addLog("");
        executionResult.addLog(String.format("┌─ 步骤 %d: %s", stepIndex, step.getStepname()));
        executionResult.addLog(String.format("│ 命令: %s", result.getCommandText()));

        // 输出内容
        String output = result.getOutput();
        if (output != null && !output.trim().isEmpty()) {
            executionResult.addLog("│ 输出:");
            String[] outputLines = output.trim().split("\n");
            for (String line : outputLines) {
                executionResult.addLog("│   " + line);
            }
        } else {
            executionResult.addLog("│ 输出: (无)");
        }

        // 错误内容
        String error = result.getError();
        if (error != null && !error.trim().isEmpty()) {
            executionResult.addLog("│ 错误:");
            String[] errorLines = error.trim().split("\n");
            for (String line : errorLines) {
                executionResult.addLog("│   " + line);
            }
        }

        // 状态和耗时
        String statusIcon = "Success".equals(result.getStatus()) ? "✓" : "✗";
        String statusText;

        if ("Success".equals(result.getStatus())) {
            statusText = "成功";
        } else {
            // 根据continueonerror配置显示不同的失败状态
            Integer continueOnError = step.getContinueonerror();
            if (continueOnError != null && continueOnError == 1) {
                statusText = "失败 允许继续执行";
            } else {
                statusText = "失败 禁止继续执行";
            }
        }

        executionResult.addLog(String.format("│ 状态: %s %s", statusIcon, statusText));
        executionResult.addLog(String.format("│ 耗时: %d毫秒", result.getDurationMs()));

        if (result.getRetryCount() > 0) {
            executionResult.addLog(String.format("│ 重试: %d次", result.getRetryCount()));
        }

        executionResult.addLog("└─────────────────────────────────");
    }

    /**
     * 创建步骤执行历史记录
     */
    private void createStepExecutionHistory(String historyId,
                                            SaSshpipelinesitemPojo step,
                                            SshCommandResult result) {
        // 生成ID
        String itemId = inksSnowflake.getSnowflake().nextIdStr();

        // 构建步骤历史记录
        SaSshhistoryitemPojo historyItem = new SaSshhistoryitemPojo();
        historyItem.setId(itemId);
        historyItem.setPid(historyId);
        historyItem.setStepid(step.getId());
        historyItem.setStepname(step.getStepname());
        historyItem.setSteprownum(step.getRownum());
        historyItem.setCommandtext(result.getCommandText());
        historyItem.setOutput(result.getOutput());
        historyItem.setError(result.getError());
        historyItem.setExitstatus(result.getExitStatus());
        historyItem.setStatus(result.getStatus());
        historyItem.setStarttime(result.getStartTime());
        historyItem.setEndtime(result.getEndTime());
        historyItem.setDurationms((int) result.getDurationMs());
        historyItem.setRetrycount(result.getRetryCount());
        historyItem.setRownum(step.getRownum());

        // 设置创建者信息
        historyItem.setCreateby("System");
        historyItem.setCreatebyid("system");
        historyItem.setCreatedate(new Date());
        historyItem.setLister("System");
        historyItem.setListerid("system");
        historyItem.setModifydate(new Date());

        // 保存到数据库
        saSshhistoryitemService.insert(historyItem);
    }

    /**
     * 更新执行历史状态
     */
    private void updateExecutionStatus(String historyId, String status) {
        SaSshhistoryPojo history = saSshhistoryMapper.getEntity(historyId);
        if (history != null) {
            history.setStatus(status);
            if (!"Running".equals(status)) {
                history.setEndtime(new Date());
            }
            history.setModifydate(new Date());
            saSshhistoryService.update(history);
        }
    }

    /**
     * 查找系统级流水线
     */
    private String findSystemPipeline(String pipelineName) {
        SaSshpipelinesPojo saSshpipelinesPojo = new SaSshpipelinesPojo();
        saSshpipelinesPojo.setPipelinename(pipelineName);
        saSshpipelinesPojo.setIssystem(1);
        List<SaSshpipelinesPojo> pipelines = saSshpipelinesMapper.getListByAll(saSshpipelinesPojo);
        if (pipelines != null && !pipelines.isEmpty()) {
            return pipelines.get(0).getId();
        }

        return null;
    }

    /**
     * 创建SSH连接
     */
    private SSHClient createSshConnection(SshConnectionInfo info) throws IOException {
        SSHClient ssh = new SSHClient();
        ssh.addHostKeyVerifier(new PromiscuousVerifier());
        ssh.setConnectTimeout(15000); // 15秒连接超时

        // 使用指定的连接信息
        if (info.getServerId() != null && !info.getServerId().isEmpty()) {
            // 从数据库获取服务器配置
            SaSshserversPojo server = saSshserversMapper.getEntity(info.getServerId());
            if (server == null) {
                throw new BaseBusinessException("服务器不存在: " + info.getServerId());
            }

            // 更新连接信息
            info.setHost(server.getHost());
            info.setPort(server.getPort());
            info.setUsername(server.getUsername());
            info.setPassword(server.getPassword());
            info.setServerName(server.getServername());
        }

        // 连接到服务器
        ssh.connect(info.getHost(), info.getPort());

        // 认证
        if (info.getPrivateKeyPath() != null && !info.getPrivateKeyPath().isEmpty()) {
            // 使用私钥认证
            KeyProvider keyProvider = ssh.loadKeys(info.getPrivateKeyPath(), info.getPrivateKeyPassphrase());
            ssh.authPublickey(info.getUsername(), keyProvider);
        } else {
            // 使用密码认证
            ssh.authPassword(info.getUsername(), info.getPassword());
        }

        return ssh;
    }

    /**
     * 测试SSH连接
     * 尝试建立SSH连接并验证是否可以成功连接到服务器
     */
    @Override
    public boolean testConnection(String serverId) {
        SSHClient ssh = null;

        try {
            // 从数据库获取服务器配置
            SaSshserversPojo server = saSshserversMapper.getEntity(serverId);
            if (server == null) {
                logger.error("Server not found with ID: {}", serverId);
                return false;
            }

            // 创建连接信息
            SshConnectionInfo connectionInfo = new SshConnectionInfo();
            connectionInfo.setServerId(server.getId());
            connectionInfo.setServerName(server.getServername());
            connectionInfo.setHost(server.getHost());
            connectionInfo.setPort(server.getPort());
            connectionInfo.setUsername(server.getUsername());
            connectionInfo.setPassword(server.getPassword());

            // 尝试建立连接
            ssh = createSshConnection(connectionInfo);

            // 如果能够成功创建连接并认证，则测试成功
            return ssh.isAuthenticated();

        } catch (Exception e) {
            logger.error("Failed to connect to server with ID: {}", serverId, e);
            return false;
        } finally {
            // 关闭连接
            closeQuietly(ssh);
        }
    }

    /**
     * 智能判断是否应该使用实时输出方法
     */
    private boolean shouldUseRealTimeOutput(String command, int timeoutMs) {
        // 1. 超时时间太短的命令，使用传统方法
        if (timeoutMs < 30000) { // 小于30秒
            return false;
        }

        // 2. 包含条件判断的命令（如if语句），通常执行很快，使用传统方法
        if (command.trim().startsWith("if ") || command.contains(" if ")) {
            return false;
        }

        // 3. 简单的检查命令，使用传统方法
        String lowerCommand = command.toLowerCase();
        if (lowerCommand.contains("command -v") ||
            lowerCommand.contains("which ") ||
            lowerCommand.contains("--version") ||
            lowerCommand.contains("systemctl is-active") ||
            lowerCommand.contains("systemctl status")) {
            return false;
        }

        // 4. 明确的安装命令，使用实时输出
        if (lowerCommand.contains("yum install") ||
            lowerCommand.contains("apt-get install") ||
            lowerCommand.contains("apt install") ||
            lowerCommand.contains("dnf install") ||
            lowerCommand.contains("zypper install")) {
            return true;
        }

        // 5. 下载命令，使用实时输出
        if (lowerCommand.contains("wget") ||
            lowerCommand.contains("curl -o") ||
            lowerCommand.contains("download")) {
            return true;
        }

        // 6. 编译命令，使用实时输出
        if (lowerCommand.contains("make") ||
            lowerCommand.contains("mvn") ||
            lowerCommand.contains("gradle") ||
            lowerCommand.contains("npm install")) {
            return true;
        }

        // 7. 默认：超过60秒的使用实时输出，否则使用传统方法
        return timeoutMs > 60000;
    }

    /**
     * 执行命令并支持实时输出显示
     */
    private SshCommandResult executeCommandWithRealTimeOutput(SshConnectionInfo connectionInfo, String command,
                                                              String successPattern, String errorPattern,
                                                              int timeoutMs, SshExecutionResult executionResult,
                                                              int stepIndex) {
        SSHClient ssh = null;
        Session session = null;
        Command cmd = null;
        SshCommandResult result = new SshCommandResult(command);
        long startTime = System.currentTimeMillis();  // 记录开始时间

        try {
            // 建立SSH连接
            ssh = createSshConnection(connectionInfo);

            // 创建会话
            session = ssh.startSession();
            session.allocateDefaultPTY();

            // 开始执行命令
            cmd = session.exec(command);

            // 实时读取命令输出
            StringBuilder outputBuilder = new StringBuilder();
            StringBuilder errorBuilder = new StringBuilder();

            // 添加执行开始的日志
            executionResult.addLog(String.format("⚡ 步骤 %d 开始执行...", stepIndex));

            // 读取输出流（实时显示）
            readOutputStreamRealTime(cmd, outputBuilder, errorBuilder, executionResult, stepIndex, timeoutMs);

            // 等待命令完成
            cmd.join(timeoutMs, TimeUnit.MILLISECONDS);

            String output = outputBuilder.toString();
            String error = errorBuilder.toString();

            // 添加调试日志
            logger.info("Command '{}' completed. Output length: {}, Error length: {}",
                    command, output.length(), error.length());
            executionResult.addLog(String.format("📊 命令输出长度: %d字符, 错误长度: %d字符",
                    output.length(), error.length()));

            // 检查是否超时
            long actualDuration = System.currentTimeMillis() - startTime;
            if (actualDuration >= timeoutMs) {
                logger.error("Command execution timed out: {} ({}ms)", command, actualDuration);
                result.timeout();
                result.setError("Command execution timed out after " + actualDuration + "ms (limit: " + timeoutMs + "ms)");
                executionResult.addLog(String.format("⏰ 步骤 %d 执行超时 (%d秒，限制: %d秒)",
                        stepIndex, actualDuration / 1000, timeoutMs / 1000));
            } else {
                // 填充执行结果
                result.complete(cmd.getExitStatus(), output, error);

                // 添加执行完成的日志
                executionResult.addLog(String.format("🏁 步骤 %d 执行完成，退出码: %d，耗时: %d秒",
                        stepIndex, cmd.getExitStatus(), actualDuration / 1000));

                // 处理正则表达式匹配（与原方法相同的逻辑）
                processPatternMatching(result, successPattern, errorPattern, output, error);
            }

        } catch (Exception e) {
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt();
                logger.error("Command execution was interrupted: {}", command, e);
                result.complete(-1, "", "Command execution was interrupted");
                executionResult.addLog(String.format("🚫 步骤 %d 执行被中断", stepIndex));
            } else {
                logger.error("Command execution failed: {}", command, e);
                result.complete(-1, "", e.getMessage());
                executionResult.addLog(String.format("❌ 步骤 %d 执行异常: %s", stepIndex, e.getMessage()));
            }
        } finally {
            // 关闭资源
            closeQuietly(cmd);
            closeQuietly(session);
            closeQuietly(ssh);
        }

        return result;
    }

    /**
     * 实时读取命令输出流
     */
    private void readOutputStreamRealTime(Command cmd, StringBuilder outputBuilder, StringBuilder errorBuilder,
                                          SshExecutionResult executionResult, int stepIndex, int timeoutMs) {
        try {
            InputStream stdout = cmd.getInputStream();
            InputStream stderr = cmd.getErrorStream();

            byte[] buffer = new byte[1024];
            long startTime = System.currentTimeMillis();
            long lastOutputTime = startTime;

            executionResult.addLog(String.format("📊 开始实时监控输出，超时时间: %d秒", timeoutMs / 1000));

            int totalBytesRead = 0;

            while (cmd.isOpen()) {
                long currentTime = System.currentTimeMillis();

                // 检查总超时时间
                if ((currentTime - startTime) > timeoutMs) {
                    executionResult.addLog(String.format("⏰ 命令执行超时 (%d秒)，强制终止", timeoutMs / 1000));
                    break;
                }
                // 读取标准输出
                boolean hasOutput = false;
                if (stdout.available() > 0) {
                    int bytesRead = stdout.read(buffer);
                    if (bytesRead > 0) {
                        hasOutput = true;
                        lastOutputTime = currentTime;
                        totalBytesRead += bytesRead;
                        String output = new String(buffer, 0, bytesRead, StandardCharsets.UTF_8);
                        outputBuilder.append(output);

                        // 实时显示输出（按行分割）
                        String[] lines = output.split("\n");
                        for (String line : lines) {
                            if (!line.trim().isEmpty()) {
                                executionResult.addLog(String.format("│ 📤 %s", line.trim()));
                            }
                        }
                    }
                }

                // 读取错误输出
                if (stderr.available() > 0) {
                    int bytesRead = stderr.read(buffer);
                    if (bytesRead > 0) {
                        hasOutput = true;
                        lastOutputTime = currentTime;
                        String error = new String(buffer, 0, bytesRead, StandardCharsets.UTF_8);
                        errorBuilder.append(error);

                        // 实时显示错误输出
                        String[] lines = error.split("\n");
                        for (String line : lines) {
                            if (!line.trim().isEmpty()) {
                                executionResult.addLog(String.format("│ ❌ %s", line.trim()));
                            }
                        }
                    }
                }

                // 如果没有输出，显示进度提示（每30秒一次）
                if (!hasOutput && (currentTime - lastOutputTime) > 30000) {
                    long elapsedSeconds = (currentTime - startTime) / 1000;
                    long remainingSeconds = (timeoutMs - (currentTime - startTime)) / 1000;
                    executionResult.addLog(String.format("│ ⏳ 命令执行中... 已耗时: %d秒, 剩余超时: %d秒", elapsedSeconds, Math.max(0, remainingSeconds)));
                    lastOutputTime = currentTime;
                }

                // 短暂休眠，避免CPU占用过高
                Thread.sleep(500); // 增加到500ms，减少CPU占用
            }

            // 添加读取完成的调试信息
            executionResult.addLog(String.format("📊 实时监控完成，总共读取: %d字节", totalBytesRead));

        } catch (Exception e) {
            logger.warn("Error reading real-time output: {}", e.getMessage());
            executionResult.addLog(String.format("❌ 实时监控异常: %s", e.getMessage()));
        }
    }

    /**
     * 处理正则表达式匹配
     */
    private void processPatternMatching(SshCommandResult result, String successPattern, String errorPattern,
                                        String output, String error) {
        // 检查成功模式
        if (successPattern != null && !successPattern.trim().isEmpty()) {
            PatternMatcherUtil.MatchResult matchResult = PatternMatcherUtil.matchSuccessPattern(
                    successPattern, output, error);

            logger.info("Success pattern matching: pattern='{}', type={}, matched={}",
                    successPattern, matchResult.getMatchType(), matchResult.isMatched());

            // 正则表达式优先：如果设置了成功模式但未匹配，则强制设置为失败
            if (!matchResult.isMatched()) {
                result.setStatus("Failed");
                String errorMsg = matchResult.getErrorMessage() != null ?
                        matchResult.getErrorMessage() : "Success pattern not matched: " + successPattern;
                result.setError(result.getError() + "\n[Pattern Match Failed] " + errorMsg);
                logger.warn("Command execution failed due to success pattern not matched: {}", successPattern);
            } else {
                // 显式设置为成功
                result.setStatus("Success");
                logger.info("Command execution succeeded - success pattern matched: '{}'",
                        matchResult.getMatchedText());
            }
        }

        // 检查错误模式
        if (errorPattern != null && !errorPattern.trim().isEmpty()) {
            PatternMatcherUtil.MatchResult matchResult = PatternMatcherUtil.matchErrorPattern(
                    errorPattern, output, error);

            logger.info("Error pattern matching: pattern='{}', type={}, matched={}",
                    errorPattern, matchResult.getMatchType(), matchResult.isMatched());

            // 如果匹配到错误模式，强制设置为失败
            if (matchResult.isMatched()) {
                result.setStatus("Failed");
                result.setError(result.getError() + "\n[Pattern Match] Error pattern detected: " +
                        errorPattern + " -> '" + matchResult.getMatchedText() + "'");
                logger.warn("Command execution failed due to error pattern matched: {} -> '{}'",
                        errorPattern, matchResult.getMatchedText());
            }
        }
    }

    /**
     * 读取命令输出（带超时）
     */
    private String readCommandOutput(Command cmd, int timeoutMs) throws IOException, TimeoutException {
        Future<String> future = executor.submit(() -> {
            try {
                return IOUtils.readFully(cmd.getInputStream()).toString(String.valueOf(StandardCharsets.UTF_8));
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });

        try {
            return future.get(timeoutMs, TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IOException("Command reading interrupted", e);
        } catch (ExecutionException e) {
            throw new IOException("Error reading command output", e.getCause());
        }
    }

    /**
     * 安全关闭资源
     */
    private void closeQuietly(AutoCloseable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (Exception e) {
                logger.warn("Failed to close resource", e);
            }
        }
    }
}
