package inks.service.sa.uts.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.SaJoblogPojo;
import inks.service.sa.uts.domain.SaJoblogEntity;

import com.github.pagehelper.PageInfo;

/**
 * 定时任务调度日志表(SaJoblog)表服务接口
 *
 * <AUTHOR>
 * @since 2025-03-07 16:07:35
 */
public interface SaJoblogService {


    SaJoblogPojo getEntity(String key);

    PageInfo<SaJoblogPojo> getPageList(QueryParam queryParam);

    SaJoblogPojo insert(SaJoblogPojo saJoblogPojo);

    SaJoblogPojo update(SaJoblogPojo saJoblogpojo);

    int delete(String key);

    int deleteJobLogByIds(String[] jobLogIds);

    int cleanJobLog();
}
