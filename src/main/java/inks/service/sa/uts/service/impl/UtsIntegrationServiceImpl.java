package inks.service.sa.uts.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.uts.domain.pojo.UtsIntegrationPojo;
import inks.service.sa.uts.domain.UtsIntegrationEntity;
import inks.service.sa.uts.mapper.UtsIntegrationMapper;
import inks.service.sa.uts.service.UtsIntegrationService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/**
 * API整合转发(UtsIntegration)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-20 13:06:41
 */
@Service("utsIntegrationService")
public class UtsIntegrationServiceImpl implements UtsIntegrationService {
    @Resource
    private UtsIntegrationMapper utsIntegrationMapper;

    @Override
    public UtsIntegrationPojo getEntity(String key) {
        return this.utsIntegrationMapper.getEntity(key);
    }


    @Override
    public PageInfo<UtsIntegrationPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<UtsIntegrationPojo> lst = utsIntegrationMapper.getPageList(queryParam);
            PageInfo<UtsIntegrationPojo> pageInfo = new PageInfo<UtsIntegrationPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public UtsIntegrationPojo insert(UtsIntegrationPojo utsIntegrationPojo) {
        //初始化NULL字段
        cleanNull(utsIntegrationPojo);
        UtsIntegrationEntity utsIntegrationEntity = new UtsIntegrationEntity(); 
        BeanUtils.copyProperties(utsIntegrationPojo,utsIntegrationEntity);
        //生成雪花id
          utsIntegrationEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          utsIntegrationEntity.setRevision(1);  //乐观锁
          this.utsIntegrationMapper.insert(utsIntegrationEntity);
        return this.getEntity(utsIntegrationEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param utsIntegrationPojo 实例对象
     * @return 实例对象
     */
    @Override
    public UtsIntegrationPojo update(UtsIntegrationPojo utsIntegrationPojo) {
        UtsIntegrationEntity utsIntegrationEntity = new UtsIntegrationEntity(); 
        BeanUtils.copyProperties(utsIntegrationPojo,utsIntegrationEntity);
        this.utsIntegrationMapper.update(utsIntegrationEntity);
        return this.getEntity(utsIntegrationEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.utsIntegrationMapper.delete(key) ;
    }
    

    private static void cleanNull(UtsIntegrationPojo utsIntegrationPojo) {
        if(utsIntegrationPojo.getIntecode()==null) utsIntegrationPojo.setIntecode("");
        if(utsIntegrationPojo.getIntename()==null) utsIntegrationPojo.setIntename("");
        if(utsIntegrationPojo.getProxytype()==null) utsIntegrationPojo.setProxytype(0);
        if(utsIntegrationPojo.getApiurl()==null) utsIntegrationPojo.setApiurl("");
        if(utsIntegrationPojo.getReqmethod()==null) utsIntegrationPojo.setReqmethod("");
        if(utsIntegrationPojo.getReqparam()==null) utsIntegrationPojo.setReqparam("");
        if(utsIntegrationPojo.getReqbody()==null) utsIntegrationPojo.setReqbody("");
        if(utsIntegrationPojo.getRespformat()==null) utsIntegrationPojo.setRespformat("");
        if(utsIntegrationPojo.getAuthtype()==null) utsIntegrationPojo.setAuthtype(0);
        if(utsIntegrationPojo.getAuthcode()==null) utsIntegrationPojo.setAuthcode("");
        if(utsIntegrationPojo.getAuthname()==null) utsIntegrationPojo.setAuthname("");
        if(utsIntegrationPojo.getAuthsecret()==null) utsIntegrationPojo.setAuthsecret("");
        if(utsIntegrationPojo.getRownum()==null) utsIntegrationPojo.setRownum(0);
        if(utsIntegrationPojo.getRemark()==null) utsIntegrationPojo.setRemark("");
        if(utsIntegrationPojo.getCreateby()==null) utsIntegrationPojo.setCreateby("");
        if(utsIntegrationPojo.getCreatebyid()==null) utsIntegrationPojo.setCreatebyid("");
        if(utsIntegrationPojo.getCreatedate()==null) utsIntegrationPojo.setCreatedate(new Date());
        if(utsIntegrationPojo.getLister()==null) utsIntegrationPojo.setLister("");
        if(utsIntegrationPojo.getListerid()==null) utsIntegrationPojo.setListerid("");
        if(utsIntegrationPojo.getModifydate()==null) utsIntegrationPojo.setModifydate(new Date());
        if(utsIntegrationPojo.getCustom1()==null) utsIntegrationPojo.setCustom1("");
        if(utsIntegrationPojo.getCustom2()==null) utsIntegrationPojo.setCustom2("");
        if(utsIntegrationPojo.getCustom3()==null) utsIntegrationPojo.setCustom3("");
        if(utsIntegrationPojo.getCustom4()==null) utsIntegrationPojo.setCustom4("");
        if(utsIntegrationPojo.getCustom5()==null) utsIntegrationPojo.setCustom5("");
        if(utsIntegrationPojo.getCustom6()==null) utsIntegrationPojo.setCustom6("");
        if(utsIntegrationPojo.getCustom7()==null) utsIntegrationPojo.setCustom7("");
        if(utsIntegrationPojo.getCustom8()==null) utsIntegrationPojo.setCustom8("");
        if(utsIntegrationPojo.getCustom9()==null) utsIntegrationPojo.setCustom9("");
        if(utsIntegrationPojo.getCustom10()==null) utsIntegrationPojo.setCustom10("");
        if(utsIntegrationPojo.getTenantid()==null) utsIntegrationPojo.setTenantid("");
        if(utsIntegrationPojo.getTenantname()==null) utsIntegrationPojo.setTenantname("");
        if(utsIntegrationPojo.getRevision()==null) utsIntegrationPojo.setRevision(0);
   }

    @Override
    public UtsIntegrationPojo getEntityByCodeAndType(String code, Integer proxytype) {
        return this.utsIntegrationMapper.getEntityByCodeAndType(code,proxytype);
    }
}
