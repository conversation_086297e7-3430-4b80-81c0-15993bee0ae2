package inks.service.sa.uts.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.uts.domain.pojo.UtsDingapprrecPojo;
import inks.service.sa.uts.domain.UtsDingapprrecEntity;
import inks.service.sa.uts.mapper.UtsDingapprrecMapper;
import inks.service.sa.uts.service.UtsDingapprrecService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/**
 * 钉钉审批记录(UtsDingapprrec)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-16 14:12:14
 */
@Service("utsDingapprrecService")
public class UtsDingapprrecServiceImpl implements UtsDingapprrecService {
    @Resource
    private UtsDingapprrecMapper utsDingapprrecMapper;

    @Override
    public UtsDingapprrecPojo getEntity(String key) {
        return this.utsDingapprrecMapper.getEntity(key);
    }


    @Override
    public PageInfo<UtsDingapprrecPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<UtsDingapprrecPojo> lst = utsDingapprrecMapper.getPageList(queryParam);
            PageInfo<UtsDingapprrecPojo> pageInfo = new PageInfo<UtsDingapprrecPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public UtsDingapprrecPojo insert(UtsDingapprrecPojo utsDingapprrecPojo) {
        //初始化NULL字段
        cleanNull(utsDingapprrecPojo);
        UtsDingapprrecEntity utsDingapprrecEntity = new UtsDingapprrecEntity(); 
        BeanUtils.copyProperties(utsDingapprrecPojo,utsDingapprrecEntity);
        //生成雪花id
          utsDingapprrecEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          utsDingapprrecEntity.setRevision(1);  //乐观锁
          this.utsDingapprrecMapper.insert(utsDingapprrecEntity);
        return this.getEntity(utsDingapprrecEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param utsDingapprrecPojo 实例对象
     * @return 实例对象
     */
    @Override
    public UtsDingapprrecPojo update(UtsDingapprrecPojo utsDingapprrecPojo) {
        UtsDingapprrecEntity utsDingapprrecEntity = new UtsDingapprrecEntity(); 
        BeanUtils.copyProperties(utsDingapprrecPojo,utsDingapprrecEntity);
        this.utsDingapprrecMapper.update(utsDingapprrecEntity);
        return this.getEntity(utsDingapprrecEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.utsDingapprrecMapper.delete(key) ;
    }
    

    private static void cleanNull(UtsDingapprrecPojo utsDingapprrecPojo) {
        if(utsDingapprrecPojo.getModulecode()==null) utsDingapprrecPojo.setModulecode("");
        if(utsDingapprrecPojo.getTemplateid()==null) utsDingapprrecPojo.setTemplateid("");
        if(utsDingapprrecPojo.getApprname()==null) utsDingapprrecPojo.setApprname("");
        if(utsDingapprrecPojo.getDatatemp()==null) utsDingapprrecPojo.setDatatemp("");
        if(utsDingapprrecPojo.getCallbackurl()==null) utsDingapprrecPojo.setCallbackurl("");
        if(utsDingapprrecPojo.getCallbackbean()==null) utsDingapprrecPojo.setCallbackbean("");
        if(utsDingapprrecPojo.getResultcode()==null) utsDingapprrecPojo.setResultcode("");
        if(utsDingapprrecPojo.getApprtype()==null) utsDingapprrecPojo.setApprtype("");
        if(utsDingapprrecPojo.getApprsn()==null) utsDingapprrecPojo.setApprsn("");
        if(utsDingapprrecPojo.getBillid()==null) utsDingapprrecPojo.setBillid("");
        if(utsDingapprrecPojo.getCallbackuuid()==null) utsDingapprrecPojo.setCallbackuuid("");
        if(utsDingapprrecPojo.getCallbackname()==null) utsDingapprrecPojo.setCallbackname("");
        if(utsDingapprrecPojo.getCallbackdate()==null) utsDingapprrecPojo.setCallbackdate(new Date());
        if(utsDingapprrecPojo.getCallbackresult()==null) utsDingapprrecPojo.setCallbackresult("");
        if(utsDingapprrecPojo.getCallbackmsg()==null) utsDingapprrecPojo.setCallbackmsg("");
        if(utsDingapprrecPojo.getUserid()==null) utsDingapprrecPojo.setUserid("");
        if(utsDingapprrecPojo.getRealname()==null) utsDingapprrecPojo.setRealname("");
        if(utsDingapprrecPojo.getRemark()==null) utsDingapprrecPojo.setRemark("");
        if(utsDingapprrecPojo.getCreateby()==null) utsDingapprrecPojo.setCreateby("");
        if(utsDingapprrecPojo.getCreatebyid()==null) utsDingapprrecPojo.setCreatebyid("");
        if(utsDingapprrecPojo.getCreatedate()==null) utsDingapprrecPojo.setCreatedate(new Date());
        if(utsDingapprrecPojo.getLister()==null) utsDingapprrecPojo.setLister("");
        if(utsDingapprrecPojo.getListerid()==null) utsDingapprrecPojo.setListerid("");
        if(utsDingapprrecPojo.getModifydate()==null) utsDingapprrecPojo.setModifydate(new Date());
        if(utsDingapprrecPojo.getCustom1()==null) utsDingapprrecPojo.setCustom1("");
        if(utsDingapprrecPojo.getCustom2()==null) utsDingapprrecPojo.setCustom2("");
        if(utsDingapprrecPojo.getCustom3()==null) utsDingapprrecPojo.setCustom3("");
        if(utsDingapprrecPojo.getCustom4()==null) utsDingapprrecPojo.setCustom4("");
        if(utsDingapprrecPojo.getCustom5()==null) utsDingapprrecPojo.setCustom5("");
        if(utsDingapprrecPojo.getTenantid()==null) utsDingapprrecPojo.setTenantid("");
        if(utsDingapprrecPojo.getTenantname()==null) utsDingapprrecPojo.setTenantname("");
        if(utsDingapprrecPojo.getRevision()==null) utsDingapprrecPojo.setRevision(0);
   }
    /**
     * 通过Spno查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public UtsDingapprrecPojo getEntityBySpno(String key, String tid) {

        return this.utsDingapprrecMapper.getEntityBySpno(key, tid);
    }

    /**
     * 通过Billid查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public List<UtsDingapprrecPojo> getOnlineByBillid(String key, String tid) {
        return this.utsDingapprrecMapper.getOnlineByBillid(key, tid);
    }

}
