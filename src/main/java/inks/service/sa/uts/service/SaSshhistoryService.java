package inks.service.sa.uts.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.SaSshhistoryPojo;
import inks.service.sa.uts.domain.pojo.SaSshhistoryitemdetailPojo;
import inks.service.sa.uts.domain.SaSshhistoryEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.github.pagehelper.PageInfo;

/**
 * SSH流水线执行历史(SaSshhistory)表服务接口
 *
 * <AUTHOR>
 * @since 2025-05-19 11:11:28
 */
public interface SaSshhistoryService {


    SaSshhistoryPojo getEntity(String key);

    PageInfo<SaSshhistoryitemdetailPojo> getPageList(QueryParam queryParam);

    SaSshhistoryPojo getBillEntity(String key);

    PageInfo<SaSshhistoryPojo> getBillList(QueryParam queryParam);

    PageInfo<SaSshhistoryPojo> getPageTh(QueryParam queryParam);

    SaSshhistoryPojo insert(SaSshhistoryPojo saSshhistoryPojo);

    SaSshhistoryPojo update(SaSshhistoryPojo saSshhistorypojo);

    int delete(String key);

}
