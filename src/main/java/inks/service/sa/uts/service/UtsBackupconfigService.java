package inks.service.sa.uts.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.job.TaskException;
import inks.service.sa.uts.domain.pojo.UtsBackupconfigPojo;
import inks.service.sa.uts.domain.vo.TableInfoDTO;
import org.quartz.SchedulerException;

import java.util.List;

/**
 * 数据库备份配置(UtsBackupconfig)表服务接口
 *
 * <AUTHOR>
 * @since 2025-02-11 16:52:45
 */
public interface UtsBackupconfigService {


    UtsBackupconfigPojo getEntity(String key);

    PageInfo<UtsBackupconfigPojo> getPageList(QueryParam queryParam);

    UtsBackupconfigPojo insert(UtsBackupconfigPojo utsBackupconfigPojo) throws SchedulerException, TaskException;

    UtsBackupconfigPojo update(UtsBackupconfigPojo utsBackupconfigpojo) throws SchedulerException, TaskException;

    int delete(String key);

    Object backup(String key);

    List<TableInfoDTO> getTablesInfo(String key) throws Exception;

    //restore(url,username,password,driverclassname,sqlContent);
    String restore(LoginUser loginUser, String url, String username, String password, String driverclassname, String sqlContent,String toEmail) throws Exception;
}
