package inks.service.sa.uts.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.UtsMedialibraryPojo;
import inks.service.sa.uts.domain.UtsMedialibraryEntity;

import com.github.pagehelper.PageInfo;

/**
 * 素材库(UtsMedialibrary)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-13 15:45:12
 */
public interface UtsMedialibraryService {


    UtsMedialibraryPojo getEntity(String key);

    PageInfo<UtsMedialibraryPojo> getPageList(QueryParam queryParam);

    UtsMedialibraryPojo insert(UtsMedialibraryPojo utsMedialibraryPojo);

    UtsMedialibraryPojo update(UtsMedialibraryPojo utsMedialibrarypojo);

    int delete(String key);
}
