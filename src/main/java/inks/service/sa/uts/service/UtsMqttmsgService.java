package inks.service.sa.uts.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.UtsMqttmsgPojo;

import java.util.List;

/**
 * MQTT信息(UtsMqttmsg)表服务接口
 *
 * <AUTHOR>
 * @since 2023-07-25 16:07:03
 */
public interface UtsMqttmsgService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    UtsMqttmsgPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<UtsMqttmsgPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param utsMqttmsgPojo 实例对象
     * @return 实例对象
     */
    UtsMqttmsgPojo insert(UtsMqttmsgPojo utsMqttmsgPojo);

    /**
     * 修改数据
     *
     * @param utsMqttmsgpojo 实例对象
     * @return 实例对象
     */
    UtsMqttmsgPojo update(UtsMqttmsgPojo utsMqttmsgpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);


    List<UtsMqttmsgPojo> getListByModuleCode(String code, String tid);

    UtsMqttmsgPojo getEntityByMsgCode(String msgCode, String tid);
}
