package inks.service.sa.uts.service;




import inks.service.sa.uts.domain.database.DataSourceDto;

import java.sql.Connection;
import java.sql.SQLException;

/**
 * Created by raodeming on 2021/8/6.
 */
public interface JdbcService {

    ///**
    // * 删除数据库连接池
    // *
    // * @param id
    // */
    //void removeJdbcConnectionPool(Long id);


    /**
     * 获取连接
     *
     * @param databaseid
     * @return
     * @throws SQLException
     */
    Connection getPooledConnection(String databaseid) throws SQLException;

    ///**
    // * 测试数据库连接  获取一个连接
    // *
    // * @param dataSource
    // * @return
    // * @throws ClassNotFoundException driverName不正确
    // * @throws SQLException
    // */
    //Connection getUnPooledConnection(DataSourceDto dataSource) throws SQLException;
}
