package inks.service.sa.uts.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.UtsInfoagentPojo;
import inks.service.sa.uts.domain.UtsInfoagentEntity;

import com.github.pagehelper.PageInfo;

/**
 * 信息代理中心(UtsInfoagent)表服务接口
 *
 * <AUTHOR>
 * @since 2025-02-28 13:12:39
 */
public interface UtsInfoagentService {


    UtsInfoagentPojo getEntity(String key);

    PageInfo<UtsInfoagentPojo> getPageList(QueryParam queryParam);

    UtsInfoagentPojo insert(UtsInfoagentPojo utsInfoagentPojo);

    UtsInfoagentPojo update(UtsInfoagentPojo utsInfoagentpojo);

    int delete(String key);

    String sendAgent(String json, String tid);
}
