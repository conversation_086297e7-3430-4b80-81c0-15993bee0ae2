package inks.service.sa.uts.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.uts.domain.UtsDingmsgEntity;
import inks.service.sa.uts.domain.pojo.UtsDingmsgPojo;
import inks.service.sa.uts.mapper.UtsDingmsgMapper;
import inks.service.sa.uts.service.UtsDingmsgService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 钉钉信息(UtsDingmsg)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-10 11:11:53
 */
@Service("utsDingmsgService")
public class UtsDingmsgServiceImpl implements UtsDingmsgService {
    @Resource
    private UtsDingmsgMapper utsDingmsgMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public UtsDingmsgPojo getEntity(String key, String tid) {
        return this.utsDingmsgMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<UtsDingmsgPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<UtsDingmsgPojo> lst = utsDingmsgMapper.getPageList(queryParam);
            PageInfo<UtsDingmsgPojo> pageInfo = new PageInfo<UtsDingmsgPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param utsDingmsgPojo 实例对象
     * @return 实例对象
     */
    @Override
    public UtsDingmsgPojo insert(UtsDingmsgPojo utsDingmsgPojo) {
        //初始化NULL字段
        if (utsDingmsgPojo.getMsggroupid() == null) utsDingmsgPojo.setMsggroupid("");
        if (utsDingmsgPojo.getMsgcode() == null) utsDingmsgPojo.setMsgcode("");
        if (utsDingmsgPojo.getMsgname() == null) utsDingmsgPojo.setMsgname("");
        if (utsDingmsgPojo.getMsgtype() == null) utsDingmsgPojo.setMsgtype("");
        if (utsDingmsgPojo.getMsgtemplate() == null) utsDingmsgPojo.setMsgtemplate("");
        if (utsDingmsgPojo.getModulecode() == null) utsDingmsgPojo.setModulecode("");
        if (utsDingmsgPojo.getUserlist() == null) utsDingmsgPojo.setUserlist("");
        if (utsDingmsgPojo.getDeptlist() == null) utsDingmsgPojo.setDeptlist("");
        if (utsDingmsgPojo.getObjjson() == null) utsDingmsgPojo.setObjjson("");
        if (utsDingmsgPojo.getRownum() == null) utsDingmsgPojo.setRownum(0);
        if (utsDingmsgPojo.getUrltemplate() == null) utsDingmsgPojo.setUrltemplate("");
        if (utsDingmsgPojo.getEnabledmark() == null) utsDingmsgPojo.setEnabledmark(0);
        if (utsDingmsgPojo.getRemark() == null) utsDingmsgPojo.setRemark("");
        if (utsDingmsgPojo.getCreateby() == null) utsDingmsgPojo.setCreateby("");
        if (utsDingmsgPojo.getCreatebyid() == null) utsDingmsgPojo.setCreatebyid("");
        if (utsDingmsgPojo.getCreatedate() == null) utsDingmsgPojo.setCreatedate(new Date());
        if (utsDingmsgPojo.getLister() == null) utsDingmsgPojo.setLister("");
        if (utsDingmsgPojo.getListerid() == null) utsDingmsgPojo.setListerid("");
        if (utsDingmsgPojo.getModifydate() == null) utsDingmsgPojo.setModifydate(new Date());
        if (utsDingmsgPojo.getCustom1() == null) utsDingmsgPojo.setCustom1("");
        if (utsDingmsgPojo.getCustom2() == null) utsDingmsgPojo.setCustom2("");
        if (utsDingmsgPojo.getCustom3() == null) utsDingmsgPojo.setCustom3("");
        if (utsDingmsgPojo.getCustom4() == null) utsDingmsgPojo.setCustom4("");
        if (utsDingmsgPojo.getCustom5() == null) utsDingmsgPojo.setCustom5("");
        if (utsDingmsgPojo.getTenantid() == null) utsDingmsgPojo.setTenantid("");
        if (utsDingmsgPojo.getTenantname() == null) utsDingmsgPojo.setTenantname("");
        if (utsDingmsgPojo.getRevision() == null) utsDingmsgPojo.setRevision(0);
        UtsDingmsgEntity utsDingmsgEntity = new UtsDingmsgEntity();
        BeanUtils.copyProperties(utsDingmsgPojo, utsDingmsgEntity);

        utsDingmsgEntity.setId(UUID.randomUUID().toString());
        utsDingmsgEntity.setRevision(1);  //乐观锁
        this.utsDingmsgMapper.insert(utsDingmsgEntity);
        return this.getEntity(utsDingmsgEntity.getId(), utsDingmsgEntity.getTenantid());

    }


    /**
     * 修改数据
     *
     * @param utsDingmsgPojo 实例对象
     * @return 实例对象
     */
    @Override
    public UtsDingmsgPojo update(UtsDingmsgPojo utsDingmsgPojo) {
        UtsDingmsgEntity utsDingmsgEntity = new UtsDingmsgEntity();
        BeanUtils.copyProperties(utsDingmsgPojo, utsDingmsgEntity);
        this.utsDingmsgMapper.update(utsDingmsgEntity);
        return this.getEntity(utsDingmsgEntity.getId(), utsDingmsgEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.utsDingmsgMapper.delete(key, tid);
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public UtsDingmsgPojo getBillEntityByMsgCode(String key, String tid) {
        try {
            //读取主表
            UtsDingmsgPojo utsDingmsgPojo = this.utsDingmsgMapper.getEntityByMsgCode(key, tid);
            return utsDingmsgPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }
    @Override
    public String getDingUserIdByOmsUserid(String omsUserid, String tid) {
        return this.utsDingmsgMapper.getDingUserIdByOmsUserid(omsUserid, tid);
    }
}
