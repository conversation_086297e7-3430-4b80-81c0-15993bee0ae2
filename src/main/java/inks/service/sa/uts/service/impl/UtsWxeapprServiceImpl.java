package inks.service.sa.uts.service.impl;

import inks.common.core.constant.InksConstants;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.uts.domain.pojo.UtsWxeapprPojo;
import inks.service.sa.uts.domain.UtsWxeapprEntity;
import inks.service.sa.uts.mapper.UtsWxeapprMapper;
import inks.service.sa.uts.service.UtsWxeapprService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/**
 * 企业微审核(UtsWxeappr)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-16 14:12:14
 */
@Service("utsWxeapprService")
public class UtsWxeapprServiceImpl implements UtsWxeapprService {
    @Resource
    private UtsWxeapprMapper utsWxeapprMapper;

    @Override
    public UtsWxeapprPojo getEntity(String key) {
        return this.utsWxeapprMapper.getEntity(key);
    }


    @Override
    public PageInfo<UtsWxeapprPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<UtsWxeapprPojo> lst = utsWxeapprMapper.getPageList(queryParam);
            PageInfo<UtsWxeapprPojo> pageInfo = new PageInfo<UtsWxeapprPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public UtsWxeapprPojo insert(UtsWxeapprPojo utsWxeapprPojo) {
        //初始化NULL字段
        cleanNull(utsWxeapprPojo);
        UtsWxeapprEntity utsWxeapprEntity = new UtsWxeapprEntity(); 
        BeanUtils.copyProperties(utsWxeapprPojo,utsWxeapprEntity);
        //生成雪花id
          utsWxeapprEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          utsWxeapprEntity.setRevision(1);  //乐观锁
          this.utsWxeapprMapper.insert(utsWxeapprEntity);
        return this.getEntity(utsWxeapprEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param utsWxeapprPojo 实例对象
     * @return 实例对象
     */
    @Override
    public UtsWxeapprPojo update(UtsWxeapprPojo utsWxeapprPojo) {
        UtsWxeapprEntity utsWxeapprEntity = new UtsWxeapprEntity(); 
        BeanUtils.copyProperties(utsWxeapprPojo,utsWxeapprEntity);
        this.utsWxeapprMapper.update(utsWxeapprEntity);
        return this.getEntity(utsWxeapprEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.utsWxeapprMapper.delete(key) ;
    }
    

    private static void cleanNull(UtsWxeapprPojo utsWxeapprPojo) {
        if(utsWxeapprPojo.getModulecode()==null) utsWxeapprPojo.setModulecode("");
        if(utsWxeapprPojo.getTemplateid()==null) utsWxeapprPojo.setTemplateid("");
        if(utsWxeapprPojo.getApprname()==null) utsWxeapprPojo.setApprname("");
        if(utsWxeapprPojo.getDatatemp()==null) utsWxeapprPojo.setDatatemp("");
        if(utsWxeapprPojo.getCallbackurl()==null) utsWxeapprPojo.setCallbackurl("");
        if(utsWxeapprPojo.getCallbackbean()==null) utsWxeapprPojo.setCallbackbean("");
        if(utsWxeapprPojo.getResultcode()==null) utsWxeapprPojo.setResultcode("");
        if(utsWxeapprPojo.getApprtype()==null) utsWxeapprPojo.setApprtype("");
        if(utsWxeapprPojo.getRownum()==null) utsWxeapprPojo.setRownum(0);
        if(utsWxeapprPojo.getTestdata()==null) utsWxeapprPojo.setTestdata("");
        if(utsWxeapprPojo.getEnabledmark()==null) utsWxeapprPojo.setEnabledmark(0);
        if(utsWxeapprPojo.getRemark()==null) utsWxeapprPojo.setRemark("");
        if(utsWxeapprPojo.getCreateby()==null) utsWxeapprPojo.setCreateby("");
        if(utsWxeapprPojo.getCreatebyid()==null) utsWxeapprPojo.setCreatebyid("");
        if(utsWxeapprPojo.getCreatedate()==null) utsWxeapprPojo.setCreatedate(new Date());
        if(utsWxeapprPojo.getLister()==null) utsWxeapprPojo.setLister("");
        if(utsWxeapprPojo.getListerid()==null) utsWxeapprPojo.setListerid("");
        if(utsWxeapprPojo.getModifydate()==null) utsWxeapprPojo.setModifydate(new Date());
        if(utsWxeapprPojo.getCustom1()==null) utsWxeapprPojo.setCustom1("");
        if(utsWxeapprPojo.getCustom2()==null) utsWxeapprPojo.setCustom2("");
        if(utsWxeapprPojo.getCustom3()==null) utsWxeapprPojo.setCustom3("");
        if(utsWxeapprPojo.getCustom4()==null) utsWxeapprPojo.setCustom4("");
        if(utsWxeapprPojo.getCustom5()==null) utsWxeapprPojo.setCustom5("");
        if(utsWxeapprPojo.getTenantid()==null) utsWxeapprPojo.setTenantid("");
        if(utsWxeapprPojo.getTenantname()==null) utsWxeapprPojo.setTenantname("");
        if(utsWxeapprPojo.getRevision()==null) utsWxeapprPojo.setRevision(0);
   }

    /**
     * 通过功能号获得报表样式
     *
     * @param moduleCode 主键
     * @return 是否成功
     */
    @Override
    public List<UtsWxeapprPojo> getListByModuleCode(String moduleCode, String tid) {
        try {
            //自定义报表 mapper层没有tid的（查所有）
            List<UtsWxeapprPojo> lst = this.utsWxeapprMapper.getListByModuleCode(moduleCode, tid);
            ////默认格式
            //List<UtsWxeapprPojo> lstdef = this.utsWxeapprMapper.getListByModuleCode(moduleCode, InksConstants.DEFAULT_TENANT);
            //lst.addAll(lstdef);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

}
