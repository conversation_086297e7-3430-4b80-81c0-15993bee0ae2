package inks.service.sa.uts.service.sql;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import javax.sql.DataSource;
import java.math.BigDecimal;
import java.sql.*;
import java.util.Date;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SqlExecutorServiceImpl implements SqlExecutorService {
    
    @Autowired
    private DataSource defaultDataSource;
    
    @Autowired
    private SqlValidator sqlValidator;
    
    // 缓存预编译语句的模板
    private final Map<String, String> preparedStatementCache = new ConcurrentHashMap<>();
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SqlExecutionResult executeSql(SqlExecuteRequest request) {
        // 创建计时器
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        
        SqlExecutionResult result = new SqlExecutionResult();
        
        try {
            // 验证SQL
            sqlValidator.validate(request.getSql());
            
            // 获取或创建预编译SQL模板
            String preparedSql = getPreparedSql(request.getSql());
            
            // 执行SQL
            try (Connection conn = defaultDataSource.getConnection();
                 PreparedStatement stmt = prepareStatement(conn, preparedSql, request)) {
                
                configureStatement(stmt, request);
                return executeStatement(stmt, request, result);
            }
        } catch (Exception e) {
            handleExecutionException(e, result);
            throw new SqlExecutionException("SQL执行失败: " + e.getMessage(), e);
        } finally {
            stopWatch.stop();
            result.setExecutionTime(stopWatch.getTotalTimeMillis());
            logExecutionResult(request, result, stopWatch.getTotalTimeMillis());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<SqlExecutionResult> executeBatchSql(List<SqlExecuteRequest> requests) {
        log.info("开始执行批量SQL, 总数: {}", requests.size());
        return requests.stream()
            .map(this::executeSql)
            .collect(Collectors.toList());
    }
    
    @Override
    @Async
    public CompletableFuture<SqlExecutionResult> executeAsyncSql(SqlExecuteRequest request) {
        log.info("开始异步执行SQL: {}", request.getSql());
        return CompletableFuture.supplyAsync(() -> executeSql(request));
    }
    
    /**
     * 获取预编译SQL模板
     */
    private String getPreparedSql(String originalSql) {
        return preparedStatementCache.computeIfAbsent(originalSql, this::convertToParameterizedSql);
    }
    
    /**
     * 将原始SQL转换为参数化SQL
     */
    private String convertToParameterizedSql(String sql) {
        // 这里可以实现更复杂的SQL解析和参数化逻辑
        return sql.replaceAll("\\?", "?");
    }
    
    /**
     * 准备PreparedStatement并设置参数
     */
    private PreparedStatement prepareStatement(Connection conn, String sql, SqlExecuteRequest request) 
            throws SQLException {
        PreparedStatement stmt = conn.prepareStatement(sql, 
            ResultSet.TYPE_SCROLL_INSENSITIVE, 
            ResultSet.CONCUR_READ_ONLY);
        
        // 设置参数
        if (request.getParams() != null && !request.getParams().isEmpty()) {
            int paramIndex = 1;
            for (Map.Entry<String, Object> entry : request.getParams().entrySet()) {
                setParameter(stmt, paramIndex++, entry.getValue());
            }
        }
        
        return stmt;
    }
    
    /**
     * 设置PreparedStatement参数
     */
    private void setParameter(PreparedStatement stmt, int index, Object value) throws SQLException {
        if (value == null) {
            stmt.setNull(index, Types.NULL);
            return;
        }
        
        // 根据参数类型设置值
        if (value instanceof String) {
            stmt.setString(index, (String) value);
        } else if (value instanceof Integer) {
            stmt.setInt(index, (Integer) value);
        } else if (value instanceof Long) {
            stmt.setLong(index, (Long) value);
        } else if (value instanceof Double) {
            stmt.setDouble(index, (Double) value);
        } else if (value instanceof Date) {
            stmt.setTimestamp(index, new Timestamp(((Date) value).getTime()));
        } else if (value instanceof Boolean) {
            stmt.setBoolean(index, (Boolean) value);
        } else if (value instanceof BigDecimal) {
            stmt.setBigDecimal(index, (BigDecimal) value);
        } else if (value instanceof byte[]) {
            stmt.setBytes(index, (byte[]) value);
        } else {
            stmt.setObject(index, value);
        }
    }
    
    /**
     * 配置Statement属性
     */
    private void configureStatement(PreparedStatement stmt, SqlExecuteRequest request) 
            throws SQLException {
        // 设置查询超时
        if (request.getTimeout() != null) {
            stmt.setQueryTimeout(request.getTimeout());
        }
        
        // 设置最大返回行数
        if (request.getMaxRows() != null) {
            stmt.setMaxRows(request.getMaxRows());
        }
        
        // 设置每次获取的行数
        stmt.setFetchSize(1000);
    }
    
    /**
     * 执行Statement并处理结果
     */
    private SqlExecutionResult executeStatement(PreparedStatement stmt, 
            SqlExecuteRequest request, SqlExecutionResult result) throws SQLException {
        boolean isQuery = stmt.execute();
        
        if (isQuery) {
            try (ResultSet rs = stmt.getResultSet()) {
                result.setData(convertResultSetToList(rs));
                result.setType("SELECT");
                // 设置总记录数
                rs.last();
                result.setTotalRows(rs.getRow());
                rs.beforeFirst();
            }
        } else {
            int affected = stmt.getUpdateCount();
            result.setAffectedRows(affected);
            result.setType(determineSqlType(request.getSql()));
        }
        
        result.setSuccess(true);
        return result;
    }
    
    /**
     * 将ResultSet转换为List<Map>
     */
    private List<Map<String, Object>> convertResultSetToList(ResultSet rs) throws SQLException {
        List<Map<String, Object>> results = new ArrayList<>();
        ResultSetMetaData metaData = rs.getMetaData();
        int columnCount = metaData.getColumnCount();
        
        while (rs.next()) {
            Map<String, Object> row = new LinkedHashMap<>();
            for (int i = 1; i <= columnCount; i++) {
                String columnName = metaData.getColumnLabel(i);
                Object value = convertValue(rs.getObject(i));
                row.put(columnName, value);
            }
            results.add(row);
        }
        
        return results;
    }
    
    /**
     * 转换特殊类型的值
     */
    private Object convertValue(Object value) {
        if (value instanceof Timestamp) {
            return new Date(((Timestamp) value).getTime());
        }
        if (value instanceof Blob) {
            try {
                Blob blob = (Blob) value;
                byte[] bytes = blob.getBytes(1, (int) blob.length());
                return Base64.getEncoder().encodeToString(bytes);
            } catch (SQLException e) {
                log.error("Blob转换失败", e);
                return null;
            }
        }
        if (value instanceof Clob) {
            try {
                Clob clob = (Clob) value;
                return clob.getSubString(1, (int) clob.length());
            } catch (SQLException e) {
                log.error("Clob转换失败", e);
                return null;
            }
        }
        return value;
    }
    
    /**
     * 判断SQL类型
     */
    private String determineSqlType(String sql) {
        sql = sql.trim().toUpperCase();
        if (sql.startsWith("INSERT")) return "INSERT";
        if (sql.startsWith("UPDATE")) return "UPDATE";
        if (sql.startsWith("DELETE")) return "DELETE";
        if (sql.startsWith("SELECT")) return "SELECT";
        return "OTHER";
    }
    
    /**
     * 处理执行异常
     */
    private void handleExecutionException(Exception e, SqlExecutionResult result) {
        result.setSuccess(false);
        result.setErrorMessage(e.getMessage());
        
        if (e instanceof SQLException) {
            SQLException sqlException = (SQLException) e;
            result.setErrorCode(sqlException.getErrorCode());
            result.setSqlState(sqlException.getSQLState());
        }
        
        log.error("SQL执行异常", e);
    }
    
    /**
     * 记录执行日志
     */
    private void logExecutionResult(SqlExecuteRequest request, 
            SqlExecutionResult result, long executionTime) {
        if (result.isSuccess()) {
            log.info("SQL执行成功 - 类型: {}, 耗时: {}ms, 影响行数: {}", 
                result.getType(), executionTime, result.getAffectedRows());
        } else {
            log.error("SQL执行失败 - 错误: {}, 耗时: {}ms", 
                result.getErrorMessage(), executionTime);
        }
    }
}