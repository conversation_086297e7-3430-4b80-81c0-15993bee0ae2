package inks.service.sa.uts.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.UtsSqlactuatorPojo;

/**
 * SQL执行器(UtsSqlactuator)表服务接口
 *
 * <AUTHOR>
 * @since 2024-10-06 16:23:35
 */
public interface UtsSqlactuatorService {


    UtsSqlactuatorPojo getEntity(String key);

    PageInfo<UtsSqlactuatorPojo> getPageList(QueryParam queryParam);

    UtsSqlactuatorPojo insert(UtsSqlactuatorPojo utsSqlactuatorPojo);

    UtsSqlactuatorPojo update(UtsSqlactuatorPojo utsSqlactuatorpojo);

    int delete(String key);

    UtsSqlactuatorPojo getMaxEntity(String code);
}
