// SqlExecutorService.java
package inks.service.sa.uts.service.sql;
import java.util.List;
import java.util.concurrent.CompletableFuture;

public interface SqlExecutorService {
    
    /**
     * 执行SQL语句
     * @param request SQL执行请求
     * @return SQL执行结果
     */
    SqlExecutionResult executeSql(SqlExecuteRequest request);
    
    /**
     * 批量执行SQL语句
     * @param requests SQL执行请求列表
     * @return 执行结果列表
     */
    List<SqlExecutionResult> executeBatchSql(List<SqlExecuteRequest> requests);
    
    /**
     * 异步执行SQL语句
     * @param request SQL执行请求
     * @return 异步执行结果
     */
    CompletableFuture<SqlExecutionResult> executeAsyncSql(SqlExecuteRequest request);
}