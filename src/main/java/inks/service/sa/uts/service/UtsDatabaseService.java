package inks.service.sa.uts.service;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.database.DataSourceDto;
import inks.service.sa.uts.domain.pojo.UtsDatabasePojo;

import java.util.List;
import java.util.Map;

/**
 * 数据库连接池(Uts_Database)表服务接口
 *
 * <AUTHOR>
 * @since 2024-05-11 15:28:20
 */
public interface UtsDatabaseService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    UtsDatabasePojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<UtsDatabasePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param utsDatabasePojo 实例对象
     * @return 实例对象
     */
    UtsDatabasePojo insert(UtsDatabasePojo utsDatabasePojo);

    /**
     * 修改数据
     *
     * @param utsDatabasepojo 实例对象
     * @return 实例对象
     */
    UtsDatabasePojo update(UtsDatabasePojo utsDatabasepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

    Map<String, Object> getDataSource(String databaseid, String tenantid);

    Map<String, Object> getDataSource(String databaseid, boolean delete,String tenantid,String url_pre, String username_pre, String password_pre,String drivename_pre);

    String getUrlDatabaseType(String databaseid, String tid);



    //获取Url数据库下所有数据表 databaseid:数据库连接id
    List<Map<String, Object>> table(String databaseid, String tableName, boolean itemFirst, String tid);
    //获取一个数据表所有字段信息 传入数据库连接id和数据表的名字，返回该表所有字段以及字段对应的注释和类型 tableNamePrefix:是否需要表名前缀 默认false
    List<Map<String, Object>> tableFields(String databaseid, String tableName,boolean tableNamePrefix, String tenantid);

    List<JSONObject> execute(DataSourceDto dataSourceDto);

    Map<String, String> getLocalSourceConfig();

    String testConnection(String databaseid, String tenantid);

    String testConnectionBackup(String url, String username, String password, String driverclassname, String tenantid);
}
