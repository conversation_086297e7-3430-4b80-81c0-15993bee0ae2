package inks.service.sa.uts.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.sa.common.core.config.oss.service.FileController;
import inks.service.sa.uts.domain.pojo.UtsFilelibraryPojo;
import inks.service.sa.uts.domain.UtsFilelibraryEntity;
import inks.service.sa.uts.mapper.UtsFilelibraryMapper;
import inks.service.sa.uts.service.UtsFilelibraryService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/**
 * 文件库(UtsFilelibrary)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-17 16:51:51
 */
@Service("utsFilelibraryService")
public class UtsFilelibraryServiceImpl implements UtsFilelibraryService {
    @Resource
    private UtsFilelibraryMapper utsFilelibraryMapper;
    @Resource
    private FileController fileController;
    @Override
    public UtsFilelibraryPojo getEntity(String key) {
        return this.utsFilelibraryMapper.getEntity(key);
    }


    @Override
    public PageInfo<UtsFilelibraryPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<UtsFilelibraryPojo> lst = utsFilelibraryMapper.getPageList(queryParam);
            PageInfo<UtsFilelibraryPojo> pageInfo = new PageInfo<UtsFilelibraryPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public UtsFilelibraryPojo insert(UtsFilelibraryPojo utsFilelibraryPojo) {
        //初始化NULL字段
        cleanNull(utsFilelibraryPojo);
        UtsFilelibraryEntity utsFilelibraryEntity = new UtsFilelibraryEntity(); 
        BeanUtils.copyProperties(utsFilelibraryPojo,utsFilelibraryEntity);
        //生成雪花id
          utsFilelibraryEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          utsFilelibraryEntity.setRevision(1);  //乐观锁
          this.utsFilelibraryMapper.insert(utsFilelibraryEntity);
        return this.getEntity(utsFilelibraryEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param utsFilelibraryPojo 实例对象
     * @return 实例对象
     */
    @Override
    public UtsFilelibraryPojo update(UtsFilelibraryPojo utsFilelibraryPojo) {
        UtsFilelibraryEntity utsFilelibraryEntity = new UtsFilelibraryEntity(); 
        BeanUtils.copyProperties(utsFilelibraryPojo,utsFilelibraryEntity);
        this.utsFilelibraryMapper.update(utsFilelibraryEntity);
        return this.getEntity(utsFilelibraryEntity.getId());
    }


    @Override
    public int delete(String key) {
        UtsFilelibraryPojo utsFilelibraryDB = getEntity(key);
        int delete = this.utsFilelibraryMapper.delete(key);
        // objectname数据库存储的是完整url：http://dev.inksyun.com:30484/File/split/stream/video/202506/2930d7d673b74c63bb31e7bcfb112ae5.mp4
        String realObjectname = utsFilelibraryDB.getDirname() + "/" + utsFilelibraryDB.getFilename();
        fileController.remove(realObjectname);
        return delete;
    }
    

    private static void cleanNull(UtsFilelibraryPojo utsFilelibraryPojo) {
        if(utsFilelibraryPojo.getGengroupid()==null) utsFilelibraryPojo.setGengroupid("");
        if(utsFilelibraryPojo.getFileoriname()==null) utsFilelibraryPojo.setFileoriname("");
        if(utsFilelibraryPojo.getBucketname()==null) utsFilelibraryPojo.setBucketname("");
        if(utsFilelibraryPojo.getDirname()==null) utsFilelibraryPojo.setDirname("");
        if(utsFilelibraryPojo.getFilename()==null) utsFilelibraryPojo.setFilename("");
        if(utsFilelibraryPojo.getContenttype()==null) utsFilelibraryPojo.setContenttype("");
        if(utsFilelibraryPojo.getFilesuffix()==null) utsFilelibraryPojo.setFilesuffix("");
        if(utsFilelibraryPojo.getStorage()==null) utsFilelibraryPojo.setStorage("");
        if(utsFilelibraryPojo.getRelateid()==null) utsFilelibraryPojo.setRelateid("");
        if(utsFilelibraryPojo.getFileurl()==null) utsFilelibraryPojo.setFileurl("");
        if(utsFilelibraryPojo.getModulecode()==null) utsFilelibraryPojo.setModulecode("");
        if(utsFilelibraryPojo.getModule()==null) utsFilelibraryPojo.setModule("");
        if(utsFilelibraryPojo.getEnabledmark()==null) utsFilelibraryPojo.setEnabledmark(0);
        if(utsFilelibraryPojo.getPublicmark()==null) utsFilelibraryPojo.setPublicmark(0);
        if(utsFilelibraryPojo.getRownum()==null) utsFilelibraryPojo.setRownum(0);
        if(utsFilelibraryPojo.getRemark()==null) utsFilelibraryPojo.setRemark("");
        if(utsFilelibraryPojo.getCreateby()==null) utsFilelibraryPojo.setCreateby("");
        if(utsFilelibraryPojo.getCreatebyid()==null) utsFilelibraryPojo.setCreatebyid("");
        if(utsFilelibraryPojo.getCreatedate()==null) utsFilelibraryPojo.setCreatedate(new Date());
        if(utsFilelibraryPojo.getLister()==null) utsFilelibraryPojo.setLister("");
        if(utsFilelibraryPojo.getListerid()==null) utsFilelibraryPojo.setListerid("");
        if(utsFilelibraryPojo.getModifydate()==null) utsFilelibraryPojo.setModifydate(new Date());
        if(utsFilelibraryPojo.getCustom1()==null) utsFilelibraryPojo.setCustom1("");
        if(utsFilelibraryPojo.getCustom2()==null) utsFilelibraryPojo.setCustom2("");
        if(utsFilelibraryPojo.getCustom3()==null) utsFilelibraryPojo.setCustom3("");
        if(utsFilelibraryPojo.getCustom4()==null) utsFilelibraryPojo.setCustom4("");
        if(utsFilelibraryPojo.getCustom5()==null) utsFilelibraryPojo.setCustom5("");
        if(utsFilelibraryPojo.getCustom6()==null) utsFilelibraryPojo.setCustom6("");
        if(utsFilelibraryPojo.getCustom7()==null) utsFilelibraryPojo.setCustom7("");
        if(utsFilelibraryPojo.getCustom8()==null) utsFilelibraryPojo.setCustom8("");
        if(utsFilelibraryPojo.getCustom9()==null) utsFilelibraryPojo.setCustom9("");
        if(utsFilelibraryPojo.getCustom10()==null) utsFilelibraryPojo.setCustom10("");
        if(utsFilelibraryPojo.getDeptid()==null) utsFilelibraryPojo.setDeptid("");
        if(utsFilelibraryPojo.getTenantid()==null) utsFilelibraryPojo.setTenantid("");
        if(utsFilelibraryPojo.getTenantname()==null) utsFilelibraryPojo.setTenantname("");
        if(utsFilelibraryPojo.getRevision()==null) utsFilelibraryPojo.setRevision(0);
   }

}
