package inks.service.sa.uts.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.SaSshpipelinesPojo;
import inks.service.sa.uts.domain.pojo.SaSshpipelinesitemdetailPojo;
import inks.service.sa.uts.domain.SaSshpipelinesEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.github.pagehelper.PageInfo;

/**
 * SSH流水线(SaSshpipelines)表服务接口
 *
 * <AUTHOR>
 * @since 2025-05-19 11:11:27
 */
public interface SaSshpipelinesService {


    SaSshpipelinesPojo getEntity(String key);

    PageInfo<SaSshpipelinesitemdetailPojo> getPageList(QueryParam queryParam);

    SaSshpipelinesPojo getBillEntity(String key);

    PageInfo<SaSshpipelinesPojo> getBillList(QueryParam queryParam);

    PageInfo<SaSshpipelinesPojo> getPageTh(QueryParam queryParam);

    SaSshpipelinesPojo insert(SaSshpipelinesPojo saSshpipelinesPojo);

    SaSshpipelinesPojo update(SaSshpipelinesPojo saSshpipelinespojo);

    int delete(String key);

}
