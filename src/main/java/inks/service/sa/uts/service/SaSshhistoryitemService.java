package inks.service.sa.uts.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.SaSshhistoryitemPojo;
import inks.service.sa.uts.domain.SaSshhistoryitemEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * SSH流水线历史步骤(SaSshhistoryitem)表服务接口
 *
 * <AUTHOR>
 * @since 2025-05-19 11:11:44
 */
public interface SaSshhistoryitemService {


    SaSshhistoryitemPojo getEntity(String key);

    PageInfo<SaSshhistoryitemPojo> getPageList(QueryParam queryParam);

    List<SaSshhistoryitemPojo> getList(String Pid);  

    SaSshhistoryitemPojo insert(SaSshhistoryitemPojo saSshhistoryitemPojo);

    SaSshhistoryitemPojo update(SaSshhistoryitemPojo saSshhistoryitempojo);

    int delete(String key);

    SaSshhistoryitemPojo clearNull(SaSshhistoryitemPojo saSshhistoryitempojo);
}
