package inks.service.sa.uts.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.uts.domain.UtsMqttmsgEntity;
import inks.service.sa.uts.domain.pojo.UtsMqttmsgPojo;
import inks.service.sa.uts.mapper.UtsMqttmsgMapper;
import inks.service.sa.uts.service.UtsMqttmsgService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * MQTT信息(UtsMqttmsg)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-07-31 10:59:56
 */
@Service("utsMqttmsgService")
public class UtsMqttmsgServiceImpl implements UtsMqttmsgService {
    @Resource
    private UtsMqttmsgMapper utsMqttmsgMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public UtsMqttmsgPojo getEntity(String key, String tid) {
        return this.utsMqttmsgMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<UtsMqttmsgPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<UtsMqttmsgPojo> lst = utsMqttmsgMapper.getPageList(queryParam);
            PageInfo<UtsMqttmsgPojo> pageInfo = new PageInfo<UtsMqttmsgPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param utsMqttmsgPojo 实例对象
     * @return 实例对象
     */
    @Override
    public UtsMqttmsgPojo insert(UtsMqttmsgPojo utsMqttmsgPojo) {
        //初始化NULL字段
        if (utsMqttmsgPojo.getMsggroupid() == null) utsMqttmsgPojo.setMsggroupid("");
        if (utsMqttmsgPojo.getMsgcode() == null) utsMqttmsgPojo.setMsgcode("");
        if (utsMqttmsgPojo.getMsgname() == null) utsMqttmsgPojo.setMsgname("");
        if (utsMqttmsgPojo.getMsgtype() == null) utsMqttmsgPojo.setMsgtype("");
        if (utsMqttmsgPojo.getMsgtemplate() == null) utsMqttmsgPojo.setMsgtemplate("");
        if (utsMqttmsgPojo.getMsgicon() == null) utsMqttmsgPojo.setMsgicon(0);
        if (utsMqttmsgPojo.getModulecode() == null) utsMqttmsgPojo.setModulecode("");
        if (utsMqttmsgPojo.getDuration() == null) utsMqttmsgPojo.setDuration(0);
        if (utsMqttmsgPojo.getUseridlist() == null) utsMqttmsgPojo.setUseridlist("");
        if (utsMqttmsgPojo.getItemjson() == null) utsMqttmsgPojo.setItemjson("");
        if (utsMqttmsgPojo.getDeptidlist() == null) utsMqttmsgPojo.setDeptidlist("");
        if (utsMqttmsgPojo.getToalluser() == null) utsMqttmsgPojo.setToalluser(0);
        if (utsMqttmsgPojo.getRownum() == null) utsMqttmsgPojo.setRownum(0);
        if (utsMqttmsgPojo.getEnabledmark() == null) utsMqttmsgPojo.setEnabledmark(0);
        if (utsMqttmsgPojo.getPosition() == null) utsMqttmsgPojo.setPosition("");
        if (utsMqttmsgPojo.getRemark() == null) utsMqttmsgPojo.setRemark("");
        if (utsMqttmsgPojo.getCreateby() == null) utsMqttmsgPojo.setCreateby("");
        if (utsMqttmsgPojo.getCreatebyid() == null) utsMqttmsgPojo.setCreatebyid("");
        if (utsMqttmsgPojo.getCreatedate() == null) utsMqttmsgPojo.setCreatedate(new Date());
        if (utsMqttmsgPojo.getLister() == null) utsMqttmsgPojo.setLister("");
        if (utsMqttmsgPojo.getListerid() == null) utsMqttmsgPojo.setListerid("");
        if (utsMqttmsgPojo.getModifydate() == null) utsMqttmsgPojo.setModifydate(new Date());
        if (utsMqttmsgPojo.getCustom1() == null) utsMqttmsgPojo.setCustom1("");
        if (utsMqttmsgPojo.getCustom2() == null) utsMqttmsgPojo.setCustom2("");
        if (utsMqttmsgPojo.getCustom3() == null) utsMqttmsgPojo.setCustom3("");
        if (utsMqttmsgPojo.getCustom4() == null) utsMqttmsgPojo.setCustom4("");
        if (utsMqttmsgPojo.getCustom5() == null) utsMqttmsgPojo.setCustom5("");
        if (utsMqttmsgPojo.getTenantid() == null) utsMqttmsgPojo.setTenantid("");
        if (utsMqttmsgPojo.getTenantname() == null) utsMqttmsgPojo.setTenantname("");
        if (utsMqttmsgPojo.getRevision() == null) utsMqttmsgPojo.setRevision(0);
        UtsMqttmsgEntity utsMqttmsgEntity = new UtsMqttmsgEntity();
        BeanUtils.copyProperties(utsMqttmsgPojo, utsMqttmsgEntity);
        //生成雪花id
        utsMqttmsgEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        utsMqttmsgEntity.setRevision(1);  //乐观锁
        this.utsMqttmsgMapper.insert(utsMqttmsgEntity);
        return this.getEntity(utsMqttmsgEntity.getId(), utsMqttmsgEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param utsMqttmsgPojo 实例对象
     * @return 实例对象
     */
    @Override
    public UtsMqttmsgPojo update(UtsMqttmsgPojo utsMqttmsgPojo) {
        UtsMqttmsgEntity utsMqttmsgEntity = new UtsMqttmsgEntity();
        BeanUtils.copyProperties(utsMqttmsgPojo, utsMqttmsgEntity);
        this.utsMqttmsgMapper.update(utsMqttmsgEntity);
        return this.getEntity(utsMqttmsgEntity.getId(), utsMqttmsgEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.utsMqttmsgMapper.delete(key, tid);
    }

    @Override
    public List<UtsMqttmsgPojo> getListByModuleCode(String moduleCode, String tid) {
        try {
            return this.utsMqttmsgMapper.getListByModuleCode(moduleCode, tid);
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public UtsMqttmsgPojo getEntityByMsgCode(String msgCode, String tid) {
        return this.utsMqttmsgMapper.getEntityByMsgCode(msgCode, tid);
    }
}
