package inks.service.sa.uts.service.sql;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.sql.Connection;
import java.util.concurrent.atomic.AtomicInteger;

public class ConnectionWrapper {
    private static final Logger log = LoggerFactory.getLogger(ConnectionWrapper.class);
    private static final AtomicInteger leakedConnections = new AtomicInteger(0);

    public static Connection wrap(Connection realConnection) {
        // 记录获取连接的调用栈，用于调试
        Exception stackTrace = new Exception("连接获取位置");

        return (Connection) Proxy.newProxyInstance(
                Connection.class.getClassLoader(),
                new Class[] { Connection.class },
                new ConnectionInvocationHandler(realConnection, stackTrace)
        );
    }

    private static class ConnectionInvocationHandler implements InvocationHandler {
        private final Connection delegate;
        private boolean closed = false;
        private final Exception creationStackTrace;
        private final long creationTime;
        private final Thread creationThread;

        public ConnectionInvocationHandler(Connection delegate, Exception stackTrace) {
            this.delegate = delegate;
            this.creationStackTrace = stackTrace;
            this.creationTime = System.currentTimeMillis();
            this.creationThread = Thread.currentThread();

            // 注册连接到跟踪器
            ConnectionTracker.registerConnection(this);
        }

        @Override
        public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
            if ("close".equals(method.getName())) {
                if (!closed) {
                    closed = true;
                    log.debug("释放连接: {}", delegate);
                    ConnectionTracker.unregisterConnection(this);
                    return method.invoke(delegate, args);
                }
                return null;
            }

            // 如果是isClosed方法，需要考虑我们自己的closed状态
            if ("isClosed".equals(method.getName())) {
                return closed || (Boolean) method.invoke(delegate, args);
            }

            // 对于已关闭的连接，抛出异常
            if (closed && !"finalize".equals(method.getName())) {
                throw new IllegalStateException("尝试使用已关闭的连接");
            }

            return method.invoke(delegate, args);
        }

        @Override
        protected void finalize() throws Throwable {
            if (!closed) {
                leakedConnections.incrementAndGet();
                log.warn("检测到未关闭的数据库连接，创建于: {}, 线程: {}",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date(creationTime)),
                        creationThread.getName());
                log.warn("连接创建位置堆栈:", creationStackTrace);

                try {
                    // 尝试在finalize中关闭连接
                    log.warn("在finalize中自动关闭连接: {}", delegate);
                    delegate.close();
                } catch (Exception e) {
                    log.error("关闭泄漏的连接时出错", e);
                }
            }
            super.finalize();
        }

        public Connection getDelegate() {
            return delegate;
        }

        public long getCreationTime() {
            return creationTime;
        }

        public Thread getCreationThread() {
            return creationThread;
        }

        public Exception getCreationStackTrace() {
            return creationStackTrace;
        }

        public boolean isClosed() {
            return closed;
        }
    }

    /**
     * 连接跟踪器，用于监控活跃连接
     */
    private static class ConnectionTracker {
        private static final Logger log = LoggerFactory.getLogger(ConnectionTracker.class);
        private static final java.util.Map<ConnectionInvocationHandler, Long> activeConnections =
                new java.util.concurrent.ConcurrentHashMap<>();
        private static final long LEAK_THRESHOLD_MS = 30000; // 30秒

        static {
            // 启动定期检查泄漏连接的线程
            Thread leakChecker = new Thread(() -> {
                while (!Thread.currentThread().isInterrupted()) {
                    try {
                        checkForLeaks();
                        Thread.sleep(10000); // 每10秒检查一次
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    } catch (Exception e) {
                        log.error("检查连接泄漏时出错", e);
                    }
                }
            }, "ConnectionLeakChecker");
            leakChecker.setDaemon(true);
            leakChecker.start();
        }

        public static void registerConnection(ConnectionInvocationHandler handler) {
            activeConnections.put(handler, System.currentTimeMillis());
        }

        public static void unregisterConnection(ConnectionInvocationHandler handler) {
            activeConnections.remove(handler);
        }

        private static void checkForLeaks() {
            long now = System.currentTimeMillis();
            activeConnections.forEach((handler, time) -> {
                if (now - time > LEAK_THRESHOLD_MS && !handler.isClosed()) {
                    log.warn("可能的连接泄漏: {} - 已打开 {} ms, 线程: {}",
                            handler.getDelegate(),
                            now - handler.getCreationTime(),
                            handler.getCreationThread().getName());
                    log.warn("连接创建位置堆栈:", handler.getCreationStackTrace());
                }
            });
        }
    }
}