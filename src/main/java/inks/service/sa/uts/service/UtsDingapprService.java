package inks.service.sa.uts.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.UtsDingapprPojo;
import inks.service.sa.uts.domain.UtsDingapprEntity;

import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 钉钉审批(UtsDingappr)表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-16 14:12:14
 */
public interface UtsDingapprService {


    UtsDingapprPojo getEntity(String key);

    PageInfo<UtsDingapprPojo> getPageList(QueryParam queryParam);

    UtsDingapprPojo insert(UtsDingapprPojo utsDingapprPojo);

    UtsDingapprPojo update(UtsDingapprPojo utsDingapprpojo);

    int delete(String key);

    List<UtsDingapprPojo> getListByModuleCode(String code, String tid);
}
