package inks.service.sa.uts.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.uts.domain.UtsFreereportsitemEntity;
import inks.service.sa.uts.domain.pojo.UtsFreereportsitemPojo;
import inks.service.sa.uts.mapper.UtsFreereportsitemMapper;
import inks.service.sa.uts.service.UtsFreereportsitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
/**
 * 自由报表项目(UtsFreereportsitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-05-15 11:29:58
 */
@Service("utsFreereportsitemService")
public class UtsFreereportsitemServiceImpl implements UtsFreereportsitemService {
    @Resource
    private UtsFreereportsitemMapper utsFreereportsitemMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public UtsFreereportsitemPojo getEntity(String key,String tid) {
        return this.utsFreereportsitemMapper.getEntity(key,tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<UtsFreereportsitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<UtsFreereportsitemPojo> lst = utsFreereportsitemMapper.getPageList(queryParam);
            PageInfo<UtsFreereportsitemPojo> pageInfo = new PageInfo<UtsFreereportsitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<UtsFreereportsitemPojo> getList(String Pid,String tid) { 
        try {
            List<UtsFreereportsitemPojo> lst = utsFreereportsitemMapper.getList(Pid,tid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      
   
    
    /**
     * 新增数据
     *
     * @param utsFreereportsitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public UtsFreereportsitemPojo insert(UtsFreereportsitemPojo utsFreereportsitemPojo) {
        //初始化item的NULL
        UtsFreereportsitemPojo itempojo =this.clearNull(utsFreereportsitemPojo);
        UtsFreereportsitemEntity utsFreereportsitemEntity = new UtsFreereportsitemEntity(); 
        BeanUtils.copyProperties(itempojo,utsFreereportsitemEntity);
          //生成雪花id
          utsFreereportsitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          utsFreereportsitemEntity.setRevision(1);  //乐观锁      
          this.utsFreereportsitemMapper.insert(utsFreereportsitemEntity);
        return this.getEntity(utsFreereportsitemEntity.getId(),utsFreereportsitemEntity.getTenantid());
  
    }

    /**
     * 修改数据
     *
     * @param utsFreereportsitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public UtsFreereportsitemPojo update(UtsFreereportsitemPojo utsFreereportsitemPojo) {
        UtsFreereportsitemEntity utsFreereportsitemEntity = new UtsFreereportsitemEntity(); 
        BeanUtils.copyProperties(utsFreereportsitemPojo,utsFreereportsitemEntity);
        this.utsFreereportsitemMapper.update(utsFreereportsitemEntity);
        return this.getEntity(utsFreereportsitemEntity.getId(),utsFreereportsitemEntity.getTenantid());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key,String tid) {
        return this.utsFreereportsitemMapper.delete(key,tid) ;
    }
    
     /**
     * 修改数据
     *
     * @param utsFreereportsitemPojo 实例对象
     * @return 实例对象
     */
     @Override
     public UtsFreereportsitemPojo clearNull(UtsFreereportsitemPojo utsFreereportsitemPojo){
     //初始化NULL字段
     if(utsFreereportsitemPojo.getPid()==null) utsFreereportsitemPojo.setPid("");
     if(utsFreereportsitemPojo.getFieldtype()==null) utsFreereportsitemPojo.setFieldtype("");
     if(utsFreereportsitemPojo.getFieldname()==null) utsFreereportsitemPojo.setFieldname("");
     if(utsFreereportsitemPojo.getHeadertext()==null) utsFreereportsitemPojo.setHeadertext("");
     if(utsFreereportsitemPojo.getDatapropertyname()==null) utsFreereportsitemPojo.setDatapropertyname("");
     if(utsFreereportsitemPojo.getOrderstr()==null) utsFreereportsitemPojo.setOrderstr("");
     if(utsFreereportsitemPojo.getColwidth()==null) utsFreereportsitemPojo.setColwidth(0);
     if(utsFreereportsitemPojo.getColalign()==null) utsFreereportsitemPojo.setColalign("");
     if(utsFreereportsitemPojo.getDisplayno()==null) utsFreereportsitemPojo.setDisplayno(0);
     if(utsFreereportsitemPojo.getDisplaystate()==null) utsFreereportsitemPojo.setDisplaystate(0);
     if(utsFreereportsitemPojo.getFormatstring()==null) utsFreereportsitemPojo.setFormatstring("");
     if(utsFreereportsitemPojo.getDefwidth()==null) utsFreereportsitemPojo.setDefwidth("");
     if(utsFreereportsitemPojo.getMinwidth()==null) utsFreereportsitemPojo.setMinwidth("");
     if(utsFreereportsitemPojo.getFixed()==null) utsFreereportsitemPojo.setFixed(0);
     if(utsFreereportsitemPojo.getSortable()==null) utsFreereportsitemPojo.setSortable(0);
     if(utsFreereportsitemPojo.getOrderfield()==null) utsFreereportsitemPojo.setOrderfield("");
     if(utsFreereportsitemPojo.getOverflow()==null) utsFreereportsitemPojo.setOverflow(0);
     if(utsFreereportsitemPojo.getFormatter()==null) utsFreereportsitemPojo.setFormatter("");
     if(utsFreereportsitemPojo.getClassname()==null) utsFreereportsitemPojo.setClassname("");
     if(utsFreereportsitemPojo.getAligntype()==null) utsFreereportsitemPojo.setAligntype("");
     if(utsFreereportsitemPojo.getEventname()==null) utsFreereportsitemPojo.setEventname("");
     if(utsFreereportsitemPojo.getEditmark()==null) utsFreereportsitemPojo.setEditmark(0);
     if(utsFreereportsitemPojo.getOperationmark()==null) utsFreereportsitemPojo.setOperationmark(0);
     if(utsFreereportsitemPojo.getDisplayindex()==null) utsFreereportsitemPojo.setDisplayindex(0);
     if(utsFreereportsitemPojo.getRownum()==null) utsFreereportsitemPojo.setRownum(0);
     if(utsFreereportsitemPojo.getCustom1()==null) utsFreereportsitemPojo.setCustom1("");
     if(utsFreereportsitemPojo.getCustom2()==null) utsFreereportsitemPojo.setCustom2("");
     if(utsFreereportsitemPojo.getCustom3()==null) utsFreereportsitemPojo.setCustom3("");
     if(utsFreereportsitemPojo.getCustom4()==null) utsFreereportsitemPojo.setCustom4("");
     if(utsFreereportsitemPojo.getCustom5()==null) utsFreereportsitemPojo.setCustom5("");
     if(utsFreereportsitemPojo.getTenantid()==null) utsFreereportsitemPojo.setTenantid("");
     if(utsFreereportsitemPojo.getTenantname()==null) utsFreereportsitemPojo.setTenantname("");
     if(utsFreereportsitemPojo.getRevision()==null) utsFreereportsitemPojo.setRevision(0);
     return utsFreereportsitemPojo;
     }
}
