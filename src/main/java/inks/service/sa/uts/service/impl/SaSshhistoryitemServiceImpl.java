package inks.service.sa.uts.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.uts.domain.pojo.SaSshhistoryitemPojo;
import inks.service.sa.uts.domain.SaSshhistoryitemEntity;
import inks.service.sa.uts.mapper.SaSshhistoryitemMapper;
import inks.service.sa.uts.service.SaSshhistoryitemService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * SSH流水线历史步骤(SaSshhistoryitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-19 11:11:44
 */
@Service("saSshhistoryitemService")
public class SaSshhistoryitemServiceImpl implements SaSshhistoryitemService {
    @Resource
    private SaSshhistoryitemMapper saSshhistoryitemMapper;

    @Override
    public SaSshhistoryitemPojo getEntity(String key) {
        return this.saSshhistoryitemMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaSshhistoryitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaSshhistoryitemPojo> lst = saSshhistoryitemMapper.getPageList(queryParam);
            PageInfo<SaSshhistoryitemPojo> pageInfo = new PageInfo<SaSshhistoryitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    @Override
    public List<SaSshhistoryitemPojo> getList(String Pid) { 
        try {
            List<SaSshhistoryitemPojo> lst = saSshhistoryitemMapper.getList(Pid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      

    @Override
    public SaSshhistoryitemPojo insert(SaSshhistoryitemPojo saSshhistoryitemPojo) {
        //初始化item的NULL
        SaSshhistoryitemPojo itempojo =this.clearNull(saSshhistoryitemPojo);
        SaSshhistoryitemEntity saSshhistoryitemEntity = new SaSshhistoryitemEntity(); 
        BeanUtils.copyProperties(itempojo,saSshhistoryitemEntity);
         //生成雪花id
          saSshhistoryitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saSshhistoryitemEntity.setRevision(1);  //乐观锁      
          this.saSshhistoryitemMapper.insert(saSshhistoryitemEntity);
        return this.getEntity(saSshhistoryitemEntity.getId());
  
    }

    @Override
    public SaSshhistoryitemPojo update(SaSshhistoryitemPojo saSshhistoryitemPojo) {
        SaSshhistoryitemEntity saSshhistoryitemEntity = new SaSshhistoryitemEntity(); 
        BeanUtils.copyProperties(saSshhistoryitemPojo,saSshhistoryitemEntity);
        this.saSshhistoryitemMapper.update(saSshhistoryitemEntity);
        return this.getEntity(saSshhistoryitemEntity.getId());
    }

    @Override
    public int delete(String key) {
        return this.saSshhistoryitemMapper.delete(key) ;
    }

     @Override
     public SaSshhistoryitemPojo clearNull(SaSshhistoryitemPojo saSshhistoryitemPojo){
     //初始化NULL字段
     if(saSshhistoryitemPojo.getPid()==null) saSshhistoryitemPojo.setPid("");
     if(saSshhistoryitemPojo.getStepid()==null) saSshhistoryitemPojo.setStepid("");
     if(saSshhistoryitemPojo.getStepname()==null) saSshhistoryitemPojo.setStepname("");
     if(saSshhistoryitemPojo.getSteprownum()==null) saSshhistoryitemPojo.setSteprownum(0);
     if(saSshhistoryitemPojo.getCommandtext()==null) saSshhistoryitemPojo.setCommandtext("");
     if(saSshhistoryitemPojo.getOutput()==null) saSshhistoryitemPojo.setOutput("");
     if(saSshhistoryitemPojo.getError()==null) saSshhistoryitemPojo.setError("");
     if(saSshhistoryitemPojo.getExitstatus()==null) saSshhistoryitemPojo.setExitstatus(0);
     if(saSshhistoryitemPojo.getStatus()==null) saSshhistoryitemPojo.setStatus("");
     if(saSshhistoryitemPojo.getStarttime()==null) saSshhistoryitemPojo.setStarttime(new Date());
     if(saSshhistoryitemPojo.getEndtime()==null) saSshhistoryitemPojo.setEndtime(new Date());
     if(saSshhistoryitemPojo.getDurationms()==null) saSshhistoryitemPojo.setDurationms(0);
     if(saSshhistoryitemPojo.getRetrycount()==null) saSshhistoryitemPojo.setRetrycount(0);
     if(saSshhistoryitemPojo.getRownum()==null) saSshhistoryitemPojo.setRownum(0);
     if(saSshhistoryitemPojo.getRemark()==null) saSshhistoryitemPojo.setRemark("");
     if(saSshhistoryitemPojo.getCreateby()==null) saSshhistoryitemPojo.setCreateby("");
     if(saSshhistoryitemPojo.getCreatebyid()==null) saSshhistoryitemPojo.setCreatebyid("");
     if(saSshhistoryitemPojo.getCreatedate()==null) saSshhistoryitemPojo.setCreatedate(new Date());
     if(saSshhistoryitemPojo.getLister()==null) saSshhistoryitemPojo.setLister("");
     if(saSshhistoryitemPojo.getListerid()==null) saSshhistoryitemPojo.setListerid("");
     if(saSshhistoryitemPojo.getModifydate()==null) saSshhistoryitemPojo.setModifydate(new Date());
     if(saSshhistoryitemPojo.getCustom1()==null) saSshhistoryitemPojo.setCustom1("");
     if(saSshhistoryitemPojo.getCustom2()==null) saSshhistoryitemPojo.setCustom2("");
     if(saSshhistoryitemPojo.getCustom3()==null) saSshhistoryitemPojo.setCustom3("");
     if(saSshhistoryitemPojo.getCustom4()==null) saSshhistoryitemPojo.setCustom4("");
     if(saSshhistoryitemPojo.getCustom5()==null) saSshhistoryitemPojo.setCustom5("");
     if(saSshhistoryitemPojo.getTenantid()==null) saSshhistoryitemPojo.setTenantid("");
     if(saSshhistoryitemPojo.getRevision()==null) saSshhistoryitemPojo.setRevision(0);
     return saSshhistoryitemPojo;
     }
}
