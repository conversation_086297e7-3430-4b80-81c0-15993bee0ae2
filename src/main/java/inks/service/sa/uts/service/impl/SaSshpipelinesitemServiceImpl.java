package inks.service.sa.uts.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.uts.domain.pojo.SaSshpipelinesitemPojo;
import inks.service.sa.uts.domain.SaSshpipelinesitemEntity;
import inks.service.sa.uts.mapper.SaSshpipelinesitemMapper;
import inks.service.sa.uts.service.SaSshpipelinesitemService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * SSH流水线步骤(SaSshpipelinesitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-19 11:11:44
 */
@Service("saSshpipelinesitemService")
public class SaSshpipelinesitemServiceImpl implements SaSshpipelinesitemService {
    @Resource
    private SaSshpipelinesitemMapper saSshpipelinesitemMapper;

    @Override
    public SaSshpipelinesitemPojo getEntity(String key) {
        return this.saSshpipelinesitemMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaSshpipelinesitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaSshpipelinesitemPojo> lst = saSshpipelinesitemMapper.getPageList(queryParam);
            PageInfo<SaSshpipelinesitemPojo> pageInfo = new PageInfo<SaSshpipelinesitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    @Override
    public List<SaSshpipelinesitemPojo> getList(String Pid) { 
        try {
            List<SaSshpipelinesitemPojo> lst = saSshpipelinesitemMapper.getList(Pid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      

    @Override
    public SaSshpipelinesitemPojo insert(SaSshpipelinesitemPojo saSshpipelinesitemPojo) {
        //初始化item的NULL
        SaSshpipelinesitemPojo itempojo =this.clearNull(saSshpipelinesitemPojo);
        SaSshpipelinesitemEntity saSshpipelinesitemEntity = new SaSshpipelinesitemEntity(); 
        BeanUtils.copyProperties(itempojo,saSshpipelinesitemEntity);
         //生成雪花id
          saSshpipelinesitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saSshpipelinesitemEntity.setRevision(1);  //乐观锁      
          this.saSshpipelinesitemMapper.insert(saSshpipelinesitemEntity);
        return this.getEntity(saSshpipelinesitemEntity.getId());
  
    }

    @Override
    public SaSshpipelinesitemPojo update(SaSshpipelinesitemPojo saSshpipelinesitemPojo) {
        SaSshpipelinesitemEntity saSshpipelinesitemEntity = new SaSshpipelinesitemEntity(); 
        BeanUtils.copyProperties(saSshpipelinesitemPojo,saSshpipelinesitemEntity);
        this.saSshpipelinesitemMapper.update(saSshpipelinesitemEntity);
        return this.getEntity(saSshpipelinesitemEntity.getId());
    }

    @Override
    public int delete(String key) {
        return this.saSshpipelinesitemMapper.delete(key) ;
    }

     @Override
     public SaSshpipelinesitemPojo clearNull(SaSshpipelinesitemPojo saSshpipelinesitemPojo){
     //初始化NULL字段
     if(saSshpipelinesitemPojo.getPid()==null) saSshpipelinesitemPojo.setPid("");
     if(saSshpipelinesitemPojo.getStepname()==null) saSshpipelinesitemPojo.setStepname("");
     if(saSshpipelinesitemPojo.getCommandtext()==null) saSshpipelinesitemPojo.setCommandtext("");
     if(saSshpipelinesitemPojo.getSuccesspattern()==null) saSshpipelinesitemPojo.setSuccesspattern("");
     if(saSshpipelinesitemPojo.getErrorpattern()==null) saSshpipelinesitemPojo.setErrorpattern("");
     if(saSshpipelinesitemPojo.getTimeoutms()==null) saSshpipelinesitemPojo.setTimeoutms(0);
     if(saSshpipelinesitemPojo.getContinueonerror()==null) saSshpipelinesitemPojo.setContinueonerror(0);
     if(saSshpipelinesitemPojo.getRetrycount()==null) saSshpipelinesitemPojo.setRetrycount(0);
     if(saSshpipelinesitemPojo.getRownum()==null) saSshpipelinesitemPojo.setRownum(0);
     if(saSshpipelinesitemPojo.getRemark()==null) saSshpipelinesitemPojo.setRemark("");
     if(saSshpipelinesitemPojo.getCustom1()==null) saSshpipelinesitemPojo.setCustom1("");
     if(saSshpipelinesitemPojo.getCustom2()==null) saSshpipelinesitemPojo.setCustom2("");
     if(saSshpipelinesitemPojo.getCustom3()==null) saSshpipelinesitemPojo.setCustom3("");
     if(saSshpipelinesitemPojo.getCustom4()==null) saSshpipelinesitemPojo.setCustom4("");
     if(saSshpipelinesitemPojo.getCustom5()==null) saSshpipelinesitemPojo.setCustom5("");
     if(saSshpipelinesitemPojo.getTenantid()==null) saSshpipelinesitemPojo.setTenantid("");
     if(saSshpipelinesitemPojo.getRevision()==null) saSshpipelinesitemPojo.setRevision(0);
     return saSshpipelinesitemPojo;
     }
}
