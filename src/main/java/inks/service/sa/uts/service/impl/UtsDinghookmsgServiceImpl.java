package inks.service.sa.uts.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.uts.domain.pojo.UtsDinghookmsgPojo;
import inks.service.sa.uts.domain.UtsDinghookmsgEntity;
import inks.service.sa.uts.domain.pojo.UtsWxehookmsgPojo;
import inks.service.sa.uts.mapper.UtsDinghookmsgMapper;
import inks.service.sa.uts.service.UtsDinghookmsgService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/**
 * 钉钉群机器人信息(UtsDinghookmsg)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-28 13:12:39
 */
@Service("utsDinghookmsgService")
public class UtsDinghookmsgServiceImpl implements UtsDinghookmsgService {
    @Resource
    private UtsDinghookmsgMapper utsDinghookmsgMapper;

    @Override
    public UtsDinghookmsgPojo getEntity(String key) {
        return this.utsDinghookmsgMapper.getEntity(key);
    }


    @Override
    public PageInfo<UtsDinghookmsgPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<UtsDinghookmsgPojo> lst = utsDinghookmsgMapper.getPageList(queryParam);
            PageInfo<UtsDinghookmsgPojo> pageInfo = new PageInfo<UtsDinghookmsgPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public UtsDinghookmsgPojo insert(UtsDinghookmsgPojo utsDinghookmsgPojo) {
        //初始化NULL字段
        cleanNull(utsDinghookmsgPojo);
        UtsDinghookmsgEntity utsDinghookmsgEntity = new UtsDinghookmsgEntity(); 
        BeanUtils.copyProperties(utsDinghookmsgPojo,utsDinghookmsgEntity);
        //生成雪花id
          utsDinghookmsgEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          utsDinghookmsgEntity.setRevision(1);  //乐观锁
          this.utsDinghookmsgMapper.insert(utsDinghookmsgEntity);
        return this.getEntity(utsDinghookmsgEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param utsDinghookmsgPojo 实例对象
     * @return 实例对象
     */
    @Override
    public UtsDinghookmsgPojo update(UtsDinghookmsgPojo utsDinghookmsgPojo) {
        UtsDinghookmsgEntity utsDinghookmsgEntity = new UtsDinghookmsgEntity(); 
        BeanUtils.copyProperties(utsDinghookmsgPojo,utsDinghookmsgEntity);
        this.utsDinghookmsgMapper.update(utsDinghookmsgEntity);
        return this.getEntity(utsDinghookmsgEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.utsDinghookmsgMapper.delete(key) ;
    }
    

    private static void cleanNull(UtsDinghookmsgPojo utsDinghookmsgPojo) {
        if(utsDinghookmsgPojo.getMsggroupid()==null) utsDinghookmsgPojo.setMsggroupid("");
        if(utsDinghookmsgPojo.getMsgcode()==null) utsDinghookmsgPojo.setMsgcode("");
        if(utsDinghookmsgPojo.getMsgname()==null) utsDinghookmsgPojo.setMsgname("");
        if(utsDinghookmsgPojo.getMsgtype()==null) utsDinghookmsgPojo.setMsgtype("");
        if(utsDinghookmsgPojo.getMsgtemplate()==null) utsDinghookmsgPojo.setMsgtemplate("");
        if(utsDinghookmsgPojo.getModulecode()==null) utsDinghookmsgPojo.setModulecode("");
        if(utsDinghookmsgPojo.getWebhooklist()==null) utsDinghookmsgPojo.setWebhooklist("");
        if(utsDinghookmsgPojo.getUserlist()==null) utsDinghookmsgPojo.setUserlist("");
        if(utsDinghookmsgPojo.getDeptlist()==null) utsDinghookmsgPojo.setDeptlist("");
        if(utsDinghookmsgPojo.getObjjson()==null) utsDinghookmsgPojo.setObjjson("");
        if(utsDinghookmsgPojo.getRownum()==null) utsDinghookmsgPojo.setRownum(0);
        if(utsDinghookmsgPojo.getUrltemplate()==null) utsDinghookmsgPojo.setUrltemplate("");
        if(utsDinghookmsgPojo.getEnabledmark()==null) utsDinghookmsgPojo.setEnabledmark(0);
        if(utsDinghookmsgPojo.getRemark()==null) utsDinghookmsgPojo.setRemark("");
        if(utsDinghookmsgPojo.getCreateby()==null) utsDinghookmsgPojo.setCreateby("");
        if(utsDinghookmsgPojo.getCreatebyid()==null) utsDinghookmsgPojo.setCreatebyid("");
        if(utsDinghookmsgPojo.getCreatedate()==null) utsDinghookmsgPojo.setCreatedate(new Date());
        if(utsDinghookmsgPojo.getLister()==null) utsDinghookmsgPojo.setLister("");
        if(utsDinghookmsgPojo.getListerid()==null) utsDinghookmsgPojo.setListerid("");
        if(utsDinghookmsgPojo.getModifydate()==null) utsDinghookmsgPojo.setModifydate(new Date());
        if(utsDinghookmsgPojo.getCustom1()==null) utsDinghookmsgPojo.setCustom1("");
        if(utsDinghookmsgPojo.getCustom2()==null) utsDinghookmsgPojo.setCustom2("");
        if(utsDinghookmsgPojo.getCustom3()==null) utsDinghookmsgPojo.setCustom3("");
        if(utsDinghookmsgPojo.getCustom4()==null) utsDinghookmsgPojo.setCustom4("");
        if(utsDinghookmsgPojo.getCustom5()==null) utsDinghookmsgPojo.setCustom5("");
        if(utsDinghookmsgPojo.getTenantid()==null) utsDinghookmsgPojo.setTenantid("");
        if(utsDinghookmsgPojo.getTenantname()==null) utsDinghookmsgPojo.setTenantname("");
        if(utsDinghookmsgPojo.getRevision()==null) utsDinghookmsgPojo.setRevision(0);
   }

    @Override
    public UtsWxehookmsgPojo getEntityByMsgCode(String msgCode, String tid) {
        return this.utsDinghookmsgMapper.getEntityByMsgCode(msgCode, tid);
    }
}
