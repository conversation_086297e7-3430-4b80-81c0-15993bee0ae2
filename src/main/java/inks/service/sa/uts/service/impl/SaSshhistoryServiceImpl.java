package inks.service.sa.uts.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.uts.domain.pojo.SaSshhistoryPojo;
import inks.service.sa.uts.domain.pojo.SaSshhistoryitemPojo;
import inks.service.sa.uts.domain.pojo.SaSshhistoryitemdetailPojo;
import inks.service.sa.uts.domain.SaSshhistoryEntity;
import inks.service.sa.uts.domain.SaSshhistoryitemEntity;
import inks.service.sa.uts.mapper.SaSshhistoryMapper;
import inks.service.sa.uts.service.SaSshhistoryService;
import inks.service.sa.uts.service.SaSshhistoryitemService;
import inks.service.sa.uts.mapper.SaSshhistoryitemMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * SSH流水线执行历史(SaSshhistory)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-19 11:11:28
 */
@Service("saSshhistoryService")
public class SaSshhistoryServiceImpl implements SaSshhistoryService {
    @Resource
    private SaSshhistoryMapper saSshhistoryMapper;
    
    @Resource
    private SaSshhistoryitemMapper saSshhistoryitemMapper;
    

    @Resource
    private SaSshhistoryitemService saSshhistoryitemService;
    

    @Override
    public SaSshhistoryPojo getEntity(String key) {
        return this.saSshhistoryMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaSshhistoryitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaSshhistoryitemdetailPojo> lst = saSshhistoryMapper.getPageList(queryParam);
            PageInfo<SaSshhistoryitemdetailPojo> pageInfo = new PageInfo<SaSshhistoryitemdetailPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    

    @Override
    public SaSshhistoryPojo getBillEntity(String key) {
       try {
        //读取主表
        SaSshhistoryPojo saSshhistoryPojo = this.saSshhistoryMapper.getEntity(key);
        //读取子表
        saSshhistoryPojo.setItem(saSshhistoryitemMapper.getList(saSshhistoryPojo.getId()));
        return saSshhistoryPojo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public PageInfo<SaSshhistoryPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaSshhistoryPojo> lst = saSshhistoryMapper.getPageTh(queryParam);
             //循环设置每个主表对象的item子表
            for(int i=0;i<lst.size();i++){
                lst.get(i).setItem(saSshhistoryitemMapper.getList(lst.get(i).getId()));
            }
            PageInfo<SaSshhistoryPojo> pageInfo = new PageInfo<SaSshhistoryPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    

    @Override
    public PageInfo<SaSshhistoryPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaSshhistoryPojo> lst = saSshhistoryMapper.getPageTh(queryParam);
            PageInfo<SaSshhistoryPojo> pageInfo = new PageInfo<SaSshhistoryPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    @Transactional
    public SaSshhistoryPojo insert(SaSshhistoryPojo saSshhistoryPojo) {
        //初始化NULL字段
        cleanNull(saSshhistoryPojo);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        SaSshhistoryEntity saSshhistoryEntity = new SaSshhistoryEntity(); 
        BeanUtils.copyProperties(saSshhistoryPojo,saSshhistoryEntity);
        //设置id和新建日期
        saSshhistoryEntity.setId(id);
        saSshhistoryEntity.setRevision(1);  //乐观锁
        //插入主表
        this.saSshhistoryMapper.insert(saSshhistoryEntity);
        //Item子表处理
        List<SaSshhistoryitemPojo> lst = saSshhistoryPojo.getItem();
        if (lst != null){
            //循环每个item子表
            for(int i=0;i<lst.size();i++){
               //初始化item的NULL
               SaSshhistoryitemPojo itemPojo =this.saSshhistoryitemService.clearNull(lst.get(i));
               SaSshhistoryitemEntity saSshhistoryitemEntity = new SaSshhistoryitemEntity(); 
               BeanUtils.copyProperties(itemPojo,saSshhistoryitemEntity);
               //设置id和Pid
               saSshhistoryitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
               saSshhistoryitemEntity.setPid(id);
               saSshhistoryitemEntity.setRevision(1);  //乐观锁
               //插入子表
               this.saSshhistoryitemMapper.insert(saSshhistoryitemEntity);
            }
        } 
        //返回Bill实例
        return this.getBillEntity(saSshhistoryEntity.getId());
    }


    @Override
    @Transactional
    public SaSshhistoryPojo update(SaSshhistoryPojo saSshhistoryPojo) {
        //主表更改
        SaSshhistoryEntity saSshhistoryEntity = new SaSshhistoryEntity(); 
        BeanUtils.copyProperties(saSshhistoryPojo,saSshhistoryEntity);
        this.saSshhistoryMapper.update(saSshhistoryEntity);
        if (saSshhistoryPojo.getItem() != null) {
        //Item子表处理
        List<SaSshhistoryitemPojo> lst = saSshhistoryPojo.getItem();
        //获取被删除的Item
         List<String> lstDelIds =saSshhistoryMapper.getDelItemIds(saSshhistoryPojo);
        if (lstDelIds != null){
            //循环每个删除item子表
            for(int i=0;i<lstDelIds.size();i++){
             this.saSshhistoryitemMapper.delete(lstDelIds.get(i));
            }
        }
        if (lst != null){
            //循环每个item子表
            for(int i=0;i<lst.size();i++){
               SaSshhistoryitemEntity saSshhistoryitemEntity = new SaSshhistoryitemEntity(); 
               if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null){
                //初始化item的NULL
               SaSshhistoryitemPojo itemPojo =this.saSshhistoryitemService.clearNull(lst.get(i));
               BeanUtils.copyProperties(itemPojo,saSshhistoryitemEntity);
               //设置id和Pid
               saSshhistoryitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
               saSshhistoryitemEntity.setPid(saSshhistoryEntity.getId());  // 主表 id
               saSshhistoryitemEntity.setRevision(1);  // 乐观锁
               //插入子表
               this.saSshhistoryitemMapper.insert(saSshhistoryitemEntity);
               }
               else
               {
               BeanUtils.copyProperties(lst.get(i),saSshhistoryitemEntity);             
               this.saSshhistoryitemMapper.update(saSshhistoryitemEntity);
               }
            }
        } 
        }
        //返回Bill实例
        return this.getBillEntity(saSshhistoryEntity.getId());
    }


    @Override
    @Transactional
    public int delete(String key) {
       SaSshhistoryPojo saSshhistoryPojo =  this.getBillEntity(key);
        //Item子表处理
        List<SaSshhistoryitemPojo> lst = saSshhistoryPojo.getItem();
        if (lst != null){
            //循环每个删除item子表
            for(int i=0;i<lst.size();i++){
             this.saSshhistoryitemMapper.delete(lst.get(i).getId());
            }
        }        
        return this.saSshhistoryMapper.delete(key) ;
    }
    

    

    private static void cleanNull(SaSshhistoryPojo saSshhistoryPojo) {
        if(saSshhistoryPojo.getPipelineid()==null) saSshhistoryPojo.setPipelineid("");
        if(saSshhistoryPojo.getPipelinename()==null) saSshhistoryPojo.setPipelinename("");
        if(saSshhistoryPojo.getServerid()==null) saSshhistoryPojo.setServerid("");
        if(saSshhistoryPojo.getServername()==null) saSshhistoryPojo.setServername("");
        if(saSshhistoryPojo.getSessionid()==null) saSshhistoryPojo.setSessionid("");
        if(saSshhistoryPojo.getStatus()==null) saSshhistoryPojo.setStatus("");
        if(saSshhistoryPojo.getStarttime()==null) saSshhistoryPojo.setStarttime(new Date());
        if(saSshhistoryPojo.getEndtime()==null) saSshhistoryPojo.setEndtime(new Date());
        if(saSshhistoryPojo.getRownum()==null) saSshhistoryPojo.setRownum(0);
        if(saSshhistoryPojo.getRemark()==null) saSshhistoryPojo.setRemark("");
        if(saSshhistoryPojo.getCreateby()==null) saSshhistoryPojo.setCreateby("");
        if(saSshhistoryPojo.getCreatebyid()==null) saSshhistoryPojo.setCreatebyid("");
        if(saSshhistoryPojo.getCreatedate()==null) saSshhistoryPojo.setCreatedate(new Date());
        if(saSshhistoryPojo.getLister()==null) saSshhistoryPojo.setLister("");
        if(saSshhistoryPojo.getListerid()==null) saSshhistoryPojo.setListerid("");
        if(saSshhistoryPojo.getModifydate()==null) saSshhistoryPojo.setModifydate(new Date());
        if(saSshhistoryPojo.getCustom1()==null) saSshhistoryPojo.setCustom1("");
        if(saSshhistoryPojo.getCustom2()==null) saSshhistoryPojo.setCustom2("");
        if(saSshhistoryPojo.getCustom3()==null) saSshhistoryPojo.setCustom3("");
        if(saSshhistoryPojo.getCustom4()==null) saSshhistoryPojo.setCustom4("");
        if(saSshhistoryPojo.getCustom5()==null) saSshhistoryPojo.setCustom5("");
        if(saSshhistoryPojo.getTenantid()==null) saSshhistoryPojo.setTenantid("");
        if(saSshhistoryPojo.getRevision()==null) saSshhistoryPojo.setRevision(0);
   }

}
