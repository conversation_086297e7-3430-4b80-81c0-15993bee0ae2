package inks.service.sa.uts.service.impl;

import inks.common.core.constant.InksConstants;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.uts.domain.pojo.UtsDingapprPojo;
import inks.service.sa.uts.domain.UtsDingapprEntity;
import inks.service.sa.uts.mapper.UtsDingapprMapper;
import inks.service.sa.uts.service.UtsDingapprService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/**
 * 钉钉审批(UtsDingappr)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-16 14:12:14
 */
@Service("utsDingapprService")
public class UtsDingapprServiceImpl implements UtsDingapprService {
    @Resource
    private UtsDingapprMapper utsDingapprMapper;

    @Override
    public UtsDingapprPojo getEntity(String key) {
        return this.utsDingapprMapper.getEntity(key);
    }


    @Override
    public PageInfo<UtsDingapprPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<UtsDingapprPojo> lst = utsDingapprMapper.getPageList(queryParam);
            PageInfo<UtsDingapprPojo> pageInfo = new PageInfo<UtsDingapprPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public UtsDingapprPojo insert(UtsDingapprPojo utsDingapprPojo) {
        //初始化NULL字段
        cleanNull(utsDingapprPojo);
        UtsDingapprEntity utsDingapprEntity = new UtsDingapprEntity(); 
        BeanUtils.copyProperties(utsDingapprPojo,utsDingapprEntity);
        //生成雪花id
          utsDingapprEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          utsDingapprEntity.setRevision(1);  //乐观锁
          this.utsDingapprMapper.insert(utsDingapprEntity);
        return this.getEntity(utsDingapprEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param utsDingapprPojo 实例对象
     * @return 实例对象
     */
    @Override
    public UtsDingapprPojo update(UtsDingapprPojo utsDingapprPojo) {
        UtsDingapprEntity utsDingapprEntity = new UtsDingapprEntity(); 
        BeanUtils.copyProperties(utsDingapprPojo,utsDingapprEntity);
        this.utsDingapprMapper.update(utsDingapprEntity);
        return this.getEntity(utsDingapprEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.utsDingapprMapper.delete(key) ;
    }
    

    private static void cleanNull(UtsDingapprPojo utsDingapprPojo) {
        if(utsDingapprPojo.getModulecode()==null) utsDingapprPojo.setModulecode("");
        if(utsDingapprPojo.getTemplateid()==null) utsDingapprPojo.setTemplateid("");
        if(utsDingapprPojo.getApprname()==null) utsDingapprPojo.setApprname("");
        if(utsDingapprPojo.getDatatemp()==null) utsDingapprPojo.setDatatemp("");
        if(utsDingapprPojo.getCallbackurl()==null) utsDingapprPojo.setCallbackurl("");
        if(utsDingapprPojo.getCallbackbean()==null) utsDingapprPojo.setCallbackbean("");
        if(utsDingapprPojo.getResultcode()==null) utsDingapprPojo.setResultcode("");
        if(utsDingapprPojo.getApprtype()==null) utsDingapprPojo.setApprtype("");
        if(utsDingapprPojo.getRownum()==null) utsDingapprPojo.setRownum(0);
        if(utsDingapprPojo.getEnabledmark()==null) utsDingapprPojo.setEnabledmark(0);
        if(utsDingapprPojo.getRemark()==null) utsDingapprPojo.setRemark("");
        if(utsDingapprPojo.getCreateby()==null) utsDingapprPojo.setCreateby("");
        if(utsDingapprPojo.getCreatebyid()==null) utsDingapprPojo.setCreatebyid("");
        if(utsDingapprPojo.getCreatedate()==null) utsDingapprPojo.setCreatedate(new Date());
        if(utsDingapprPojo.getLister()==null) utsDingapprPojo.setLister("");
        if(utsDingapprPojo.getListerid()==null) utsDingapprPojo.setListerid("");
        if(utsDingapprPojo.getModifydate()==null) utsDingapprPojo.setModifydate(new Date());
        if(utsDingapprPojo.getCustom1()==null) utsDingapprPojo.setCustom1("");
        if(utsDingapprPojo.getCustom2()==null) utsDingapprPojo.setCustom2("");
        if(utsDingapprPojo.getCustom3()==null) utsDingapprPojo.setCustom3("");
        if(utsDingapprPojo.getCustom4()==null) utsDingapprPojo.setCustom4("");
        if(utsDingapprPojo.getCustom5()==null) utsDingapprPojo.setCustom5("");
        if(utsDingapprPojo.getTenantid()==null) utsDingapprPojo.setTenantid("");
        if(utsDingapprPojo.getTenantname()==null) utsDingapprPojo.setTenantname("");
        if(utsDingapprPojo.getRevision()==null) utsDingapprPojo.setRevision(0);
   }

    /**
     * 通过功能号获得报表样式
     *
     * @param moduleCode 主键
     * @return 是否成功
     */
    @Override
    public List<UtsDingapprPojo> getListByModuleCode(String moduleCode, String tid) {
        try {
            //自定义报表 mapper层没有tid的（查所有）
            List<UtsDingapprPojo> lst = this.utsDingapprMapper.getListByModuleCode(moduleCode, tid);
            ////默认格式
            //List<UtsDingapprPojo> lstdef = this.utsDingapprMapper.getListByModuleCode(moduleCode, InksConstants.DEFAULT_TENANT);
            //lst.addAll(lstdef);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

}
