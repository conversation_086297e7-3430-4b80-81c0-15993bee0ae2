package inks.service.sa.uts.service;

import inks.service.sa.uts.domain.pojo.SshCommandResult;
import inks.service.sa.uts.domain.pojo.SshConnectionInfo;
import inks.service.sa.uts.domain.pojo.SshExecutionResult;

import java.util.List;

/**
 * SSH命令执行服务接口
 * 提供SSH命令执行、流水线执行和连接管理的核心功能
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
public interface SshExecutorService {

    /**
     * 执行单个SSH命令
     *
     * @param connectionInfo SSH连接信息
     * @param command 要执行的命令
     * @param successPattern 成功判断正则表达式
     * @param errorPattern 失败判断正则表达式
     * @param timeoutMs 超时时间（毫秒）
     * @return 命令执行结果
     */
    SshCommandResult executeCommand(SshConnectionInfo connectionInfo, String command, 
                                   String successPattern, String errorPattern, 
                                   int timeoutMs);

    /**
     * 执行SSH流水线（多个命令序列）
     *
     * @param pipelineId 流水线ID
     * @param serverId 服务器ID
     * @return 流水线执行结果
     */
    SshExecutionResult executePipeline(String pipelineId, String serverId);

    /**
     * 执行SSH流水线（多个命令序列）到多台服务器
     * 
     * @param pipelineId 流水线ID
     * @param serverIds 服务器ID列表
     * @return 每台服务器的执行结果
     */
    List<SshExecutionResult> executePipelineToMultipleServers(String pipelineId, List<String> serverIds);

    /**
     * 快速执行系统关机命令
     *
     * @param serverId 服务器ID
     * @return 执行结果
     */
    SshExecutionResult quickShutdown(String serverId);

    /**
     * 快速执行系统重启命令
     *
     * @param serverId 服务器ID
     * @return 执行结果
     */
    SshExecutionResult quickRestart(String serverId);
    
    /**
     * 异步执行SSH流水线
     * 
     * @param pipelineId 流水线ID
     * @param serverId 服务器ID
     * @return 执行会话ID，可用于后续查询执行状态
     */
    String executeAsyncPipeline(String pipelineId, String serverId);
    
    /**
     * 取消正在执行的SSH流水线
     * 
     * @param sessionId 执行会话ID
     * @return 是否成功取消
     */
    boolean cancelExecution(String sessionId);
    
    /**
     * 获取执行状态
     * 
     * @param sessionId 执行会话ID
     * @return 当前执行状态
     */
    SshExecutionResult getExecutionStatus(String sessionId);
    
    /**
     * 测试SSH连接
     * 
     * @param serverId 服务器ID
     * @return 连接测试结果
     */
    boolean testConnection(String serverId);

    String executeIncrementalPipeline(String pipelineId, String serverId);
}
