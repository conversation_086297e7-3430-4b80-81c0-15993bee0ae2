package inks.service.sa.uts.service;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.job.TaskException;
import inks.service.sa.uts.domain.pojo.SaJobPojo;
import inks.service.sa.uts.domain.SaJobEntity;

import com.github.pagehelper.PageInfo;
import inks.service.sa.uts.domain.pojo.UtsBackupconfigPojo;
import inks.service.sa.uts.utils.quartz.ScheduleInfo;
import org.quartz.SchedulerException;

import java.util.List;

/**
 * 定时任务调度表(SaJob)表服务接口
 *
 * <AUTHOR>
 * @since 2025-03-07 16:07:17
 */
public interface SaJobService {


    SaJobPojo getEntity(String key);

    PageInfo<SaJobPojo> getPageList(QueryParam queryParam);

    SaJobPojo insert(SaJobPojo saJobPojo) throws SchedulerException, TaskException;

    SaJobPojo update(SaJobPojo saJobpojo);

    int delete(String key);

//    --------------------------------------------------------------------
    /**
     * 获取quartz调度器的计划任务
     *
     * @param job 调度信息
     * @return 调度任务集合
     */
    //public List<SaJobPojo> selectJobList(SaJobPojo job);

    /**
     * 通过调度任务ID查询调度信息
     *
     * @param jobId 调度任务ID
     * @return 调度任务对象信息
     */
    public SaJobPojo selectJobById(String jobId);

    /**
     * 暂停任务
     *
     * @param job 调度信息
     * @return 结果
     */
    public int pauseJob(SaJobPojo job) throws SchedulerException;

    /**
     * 恢复任务
     *
     * @param job 调度信息
     * @return 结果
     */
    public int resumeJob(SaJobPojo job) throws SchedulerException;

    /**
     * 删除任务后，所对应的trigger也将被删除
     *
     * @param job 调度信息
     * @return 结果
     */
    public int deleteJob(SaJobPojo job) throws SchedulerException;

    /**
     * 批量删除调度信息
     *
     * @param jobIds 需要删除的任务ID
     * @return 结果
     */
    public void deleteJobByIds(String[] jobIds) throws SchedulerException;

    /**
     * 任务调度状态修改
     *
     * @param job 调度信息
     * @return 结果
     */
    public int changeStatus(SaJobPojo job) throws SchedulerException;

    /**
     * 立即运行任务
     *
     * @param job 调度信息
     * @return 结果
     */
    public boolean run(SaJobPojo job) throws SchedulerException;

    /**
     * 新增任务
     *
     * @param job 调度信息
     * @return 结果
     */
    public int insertJob(SaJobPojo job) throws SchedulerException, TaskException;

    /**
     * 更新任务
     *
     * @param job 调度信息
     * @return 结果
     */
    public int updateJob(SaJobPojo job) throws SchedulerException, TaskException;

    /**
     * 校验cron表达式是否有效
     *
     * @param cronExpression 表达式
     * @return 结果
     */
    public boolean checkCronExpressionIsValid(String cronExpression);

    public void backupToJob(UtsBackupconfigPojo backupConfig) throws SchedulerException, TaskException;

    // 查询当前系统存在的所有定时任务
    public List<ScheduleInfo> getAllScheduledJobs() throws SchedulerException ;
}
