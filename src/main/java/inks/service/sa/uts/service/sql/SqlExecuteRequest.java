// SqlExecuteRequest.java
package inks.service.sa.uts.service.sql;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

@Data
@ApiModel("SQL执行请求")
public class SqlExecuteRequest {
    @ApiModelProperty("SQL语句")
    private String sql;
    
    @ApiModelProperty("SQL参数")
    private Map<String, Object> params;
    
    @ApiModelProperty("超时时间（秒）")
    private Integer timeout;
    
    @ApiModelProperty("最大返回行数")
    private Integer maxRows;
    
    @ApiModelProperty("数据源名称")
    private String dataSource;

    // 构造函数
    public SqlExecuteRequest() {
        this.timeout = 30; // 默认超时时间
        this.maxRows = 1000; // 默认最大返回行数
    }
}
