package inks.service.sa.uts.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.uts.controller.*;
import inks.service.sa.uts.domain.UtsInfoagentEntity;
import inks.service.sa.uts.domain.pojo.UtsInfoagentPojo;
import inks.service.sa.uts.mapper.UtsInfoagentMapper;
import inks.service.sa.uts.service.UtsInfoagentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 信息代理中心(UtsInfoagent)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-28 13:12:39
 */
@Service("utsInfoagentService")
public class UtsInfoagentServiceImpl implements UtsInfoagentService {
    @Resource
    private UtsInfoagentMapper utsInfoagentMapper;
    private final static Logger logger = LoggerFactory.getLogger(UtsInfoagentServiceImpl.class);

    @Override
    public UtsInfoagentPojo getEntity(String key) {
        return this.utsInfoagentMapper.getEntity(key);
    }


    @Override
    public PageInfo<UtsInfoagentPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<UtsInfoagentPojo> lst = utsInfoagentMapper.getPageList(queryParam);
            PageInfo<UtsInfoagentPojo> pageInfo = new PageInfo<UtsInfoagentPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public UtsInfoagentPojo insert(UtsInfoagentPojo utsInfoagentPojo) {
        //初始化NULL字段
        cleanNull(utsInfoagentPojo);
        UtsInfoagentEntity utsInfoagentEntity = new UtsInfoagentEntity();
        BeanUtils.copyProperties(utsInfoagentPojo, utsInfoagentEntity);
        //生成雪花id
        utsInfoagentEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        utsInfoagentEntity.setRevision(1);  //乐观锁
        this.utsInfoagentMapper.insert(utsInfoagentEntity);
        return this.getEntity(utsInfoagentEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param utsInfoagentPojo 实例对象
     * @return 实例对象
     */
    @Override
    public UtsInfoagentPojo update(UtsInfoagentPojo utsInfoagentPojo) {
        UtsInfoagentEntity utsInfoagentEntity = new UtsInfoagentEntity();
        BeanUtils.copyProperties(utsInfoagentPojo, utsInfoagentEntity);
        this.utsInfoagentMapper.update(utsInfoagentEntity);
        return this.getEntity(utsInfoagentEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.utsInfoagentMapper.delete(key);
    }


    private static void cleanNull(UtsInfoagentPojo utsInfoagentPojo) {
        if (utsInfoagentPojo.getMsggroupid() == null) utsInfoagentPojo.setMsggroupid("");
        if (utsInfoagentPojo.getMsgcode() == null) utsInfoagentPojo.setMsgcode("");
        if (utsInfoagentPojo.getMsgname() == null) utsInfoagentPojo.setMsgname("");
        if (utsInfoagentPojo.getAgenttools() == null) utsInfoagentPojo.setAgenttools("");
        if (utsInfoagentPojo.getRownum() == null) utsInfoagentPojo.setRownum(0);
        if (utsInfoagentPojo.getEnabledmark() == null) utsInfoagentPojo.setEnabledmark(0);
        if (utsInfoagentPojo.getRemark() == null) utsInfoagentPojo.setRemark("");
        if (utsInfoagentPojo.getCreateby() == null) utsInfoagentPojo.setCreateby("");
        if (utsInfoagentPojo.getCreatebyid() == null) utsInfoagentPojo.setCreatebyid("");
        if (utsInfoagentPojo.getCreatedate() == null) utsInfoagentPojo.setCreatedate(new Date());
        if (utsInfoagentPojo.getLister() == null) utsInfoagentPojo.setLister("");
        if (utsInfoagentPojo.getListerid() == null) utsInfoagentPojo.setListerid("");
        if (utsInfoagentPojo.getModifydate() == null) utsInfoagentPojo.setModifydate(new Date());
        if (utsInfoagentPojo.getCustom1() == null) utsInfoagentPojo.setCustom1("");
        if (utsInfoagentPojo.getCustom2() == null) utsInfoagentPojo.setCustom2("");
        if (utsInfoagentPojo.getCustom3() == null) utsInfoagentPojo.setCustom3("");
        if (utsInfoagentPojo.getCustom4() == null) utsInfoagentPojo.setCustom4("");
        if (utsInfoagentPojo.getCustom5() == null) utsInfoagentPojo.setCustom5("");
        if (utsInfoagentPojo.getTenantid() == null) utsInfoagentPojo.setTenantid("");
        if (utsInfoagentPojo.getTenantname() == null) utsInfoagentPojo.setTenantname("");
        if (utsInfoagentPojo.getRevision() == null) utsInfoagentPojo.setRevision(0);
    }


    @Resource
    private S34M01B1Controller s34M01B1Controller; // 企业微信信息发送
    @Resource
    private S34M01B2Controller s34M01B2Controller; // 钉钉信息发送
    @Resource
    private S34M06B1BOTController s34M06B1BOTController; // 企业微信群机器人信息发送
    @Resource
    private S34M06B2BOTController s34M06B2BOTController; // 钉钉机器人信息发送
    @Resource
    private S34M08B1Controller s34M08B1EmailController; // 邮件信息发送
    @Resource
    private S34M02B1Controller s34M02B1Controller; // MQTT信息发送


    @Override
    public String sendAgent(String json, String tid) {
        // 从json中获取code和content
        JSONObject jsonObject = JSONObject.parseObject(json);
        String code = jsonObject.getString("code");
        String data = jsonObject.getString("data");
        String userlist = jsonObject.getString("userlist");

        // 创建一个新的JSON对象于钉钉、企业微信方法的入参统一
        // 因为钉钉、企业微信的入参格式要的是msgcode和msgtext!
        JSONObject dingWxeJson = new JSONObject();
        dingWxeJson.put("msgcode", code);
        dingWxeJson.put("msgtext", data);
        dingWxeJson.put("userlist", userlist);
        String dingWxeJsonStr = dingWxeJson.toJSONString();

        //// 根据code获取信息代理
        // 从数据库中获取代理信息  agentTools：ding,wxe,wxmp,email,mqtt
        UtsInfoagentPojo infoAgentPojo = this.utsInfoagentMapper.getEntityByMsgCode(code, tid);
        String agentTools = infoAgentPojo.getAgenttools();

        // 按逗号分割工具类型
        String[] toolsArray = agentTools.split(",");

        // 创建两个集合来存储成功和失败的工具类型
        List<String> successTools = new ArrayList<>();
        List<String> failureTools = new ArrayList<>();

        // 遍历每个工具类型，同时发送给多个工具
        for (String tool : toolsArray) {
            switch (tool.trim().toLowerCase()) {
                case "wxe":
                    logger.info("信息代理接口：开始发送企业微信信息");
                    R<String> stringR = s34M01B1Controller.sendTextMsg(dingWxeJsonStr, tid);
                    if (stringR.getCode() == 200) {
                        logger.info("信息代理接口：企业微信信息发送成功");
                        successTools.add("wxe");
                    } else {
                        logger.error("信息代理接口：企业微信信息发送失败");
                        failureTools.add("wxe");
                    }
                    break;
                case "ding":
                    logger.info("信息代理接口：开始发送钉钉信息");
                    R<String> stringR1 = s34M01B2Controller.sendTextMsg(dingWxeJsonStr, tid);
                    if (stringR1.getCode() == 200) {
                        successTools.add("ding");
                    } else {
                        failureTools.add("ding");
                    }
                    break;
                case "wxebot":// 企业微信机器人-markdown信息
                    logger.info("信息代理接口：开始发送企业微信机器人信息");
                    R<String> stringR2 = s34M06B1BOTController.sendMarkdownMsg(dingWxeJsonStr, tid);
                    if (stringR2.getCode() == 200) {
                        successTools.add("wxebot");
                    } else {
                        failureTools.add("wxebot");
                    }
                    break;
                case "dingbot":// 钉钉机器人-markdown信息
                    logger.info("信息代理接口：开始发送钉钉机器人信息");
                    R<String> stringR3 = s34M06B2BOTController.sendMarkdownMsg(dingWxeJsonStr, tid);
                    if (stringR3.getCode() == 200) {
                        successTools.add("dingbot");
                    } else {
                        failureTools.add("dingbot");
                    }
                    break;
                case "wxmp":
                    logger.info("信息代理接口：开始发送企业微信公众号信息");
                    sendToWxmp(json, tid);
                    successTools.add("wxmp");
                    break;
                case "email":
                    logger.info("信息代理接口：开始发送邮件信息");
                    R<String> stringR4 = s34M08B1EmailController.sendEmailByCode(json, tid);
                    if (stringR4.getCode() == 200) {
                        successTools.add("email");
                    } else {
                        failureTools.add("email");
                    }
                    break;
                case "mqtt":
                    logger.info("信息代理接口：开始发送MQTT信息");
                    R<String> stringR5 = s34M02B1Controller.textMsgByCode(json, tid);
                    if (stringR5.getCode() == 200) {
                        successTools.add("mqtt");
                    } else {
                        failureTools.add("mqtt");
                    }
                    break;
                default:
                    // 未知的工具类型
                    logger.error("Unknown tool: " + tool);
                    failureTools.add(tool);
            }
        }

        // 打印成功和失败的途径
        logger.info("------------------------------------------------------------------------------------------");
        logger.info("成功发送的途径: {}", String.join(", ", successTools));
        logger.error("发送失败的途径: {}", String.join(", ", failureTools));

        return "信息成功发送到下列途径: " + String.join(", ", successTools) +
                (failureTools.isEmpty() ? "" : "，但以下途径发送失败: " + String.join(", ", failureTools));
    }


    private void sendToWxmp(String json, String tid) {
    }
}
