package inks.service.sa.uts.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.SaSshserversPojo;
import inks.service.sa.uts.domain.SaSshserversEntity;

import com.github.pagehelper.PageInfo;

/**
 * SSH服务器配置(SaSshservers)表服务接口
 *
 * <AUTHOR>
 * @since 2025-05-19 11:11:06
 */
public interface SaSshserversService {


    SaSshserversPojo getEntity(String key);

    PageInfo<SaSshserversPojo> getPageList(QueryParam queryParam);

    SaSshserversPojo insert(SaSshserversPojo saSshserversPojo);

    SaSshserversPojo update(SaSshserversPojo saSshserverspojo);

    int delete(String key);
}
