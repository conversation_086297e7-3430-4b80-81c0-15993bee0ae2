package inks.service.sa.uts.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.constant.ScheduleConstants;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.exception.job.TaskException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.uts.domain.SaJobEntity;
import inks.service.sa.uts.domain.pojo.SaJobPojo;
import inks.service.sa.uts.domain.pojo.UtsBackupconfigPojo;
import inks.service.sa.uts.mapper.SaJobMapper;
import inks.service.sa.uts.mapper.UtsBackupconfigMapper;
import inks.service.sa.uts.service.SaJobService;
import inks.service.sa.uts.utils.PrintColor;
import inks.service.sa.uts.utils.quartz.CronUtils;
import inks.service.sa.uts.utils.quartz.ScheduleInfo;
import inks.service.sa.uts.utils.quartz.ScheduleUtils;
import org.quartz.*;
import org.quartz.impl.matchers.GroupMatcher;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 定时任务调度表(SaJob)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-07 16:07:17
 */
@Service("saJobService")
public class SaJobServiceImpl implements SaJobService {
    @Resource
    private SaJobMapper jobMapper;
    @Resource
    private Scheduler scheduler;
    @Resource
    private UtsBackupconfigMapper utsBackupconfigMapper;

    /**
     * 项目启动时，初始化定时器 主要是防止手动修改数据库导致未同步到定时任务处理（注：不能手动修改数据库ID和任务组名，否则会导致脏数据）
     */
    @PostConstruct
    public void init() throws SchedulerException, TaskException {
        scheduler.clear();
        List<SaJobPojo> jobList = jobMapper.selectJobAll();
        for (SaJobPojo job : jobList) {
            ScheduleUtils.createScheduleJob(scheduler, job);
        }
        PrintColor.red("初始化原生定时任务数量：" + jobList.size());
        // 初始化时，也加入数据库备份定时任务 Uts_BackupConfig //有cron表达式才添加定时任务
        List<UtsBackupconfigPojo> backupLst = utsBackupconfigMapper.getEnableAndCronList();
        for (UtsBackupconfigPojo backupConfig : backupLst) {
            backupToJob(backupConfig);
        }
        PrintColor.red("初始化数据库Uts_BackupConfig定时任务数量：" + backupLst);
    }

    // 将数据库备份任务转为Job定时任务
    @Override
    public void backupToJob(UtsBackupconfigPojo backupConfig) throws SchedulerException, TaskException {
        SaJobPojo job = new SaJobPojo();
        // 定时任务唯一标识：这里用 Uts_BackupConfig.id
        job.setId(backupConfig.getId());
        job.setJobgroup("UTS_BACKUP_GROUP");// 任务组名""
        // 直接开启：1（正常）
        job.setStatus(ScheduleConstants.Status.NORMAL.getValue());
        // 任务名称
        job.setJobname("[定时数据库备份] " + backupConfig.getConfigname());
        // 调用目标
        job.setInvoketarget("s34M07B1Controller.backup('" + backupConfig.getId() + "')");
        // 计划执行错误策略（1：立即执行，2：执行一次，3：放弃执行）
        job.setMisfirepolicy("3");
        // 是否并发执行（0：允许，1：禁止）
        job.setConcurrent("1");
        // Cron 表达式
        job.setCronexpression(backupConfig.getCronexpression());

        // 构建 JobKey，用于判断任务是否已存在
        JobKey jobKey = ScheduleUtils.getJobKey(job.getId(), job.getJobgroup());
        if (scheduler.checkExists(jobKey)) {
            // 若已存在，则先删除旧的定时任务
            scheduler.deleteJob(jobKey);
            PrintColor.red("删除旧的定时任务：" + jobKey);
        }
        // 创建或重新创建定时任务
        ScheduleUtils.createScheduleJob(scheduler, job);
        PrintColor.red("添加定时任务：" + job);
    }


    /**
     * 新增任务
     *
     * @param job 调度信息 调度信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertJob(SaJobPojo job) throws SchedulerException, TaskException {
        //NORMAL("0"),正常 、PAUSE("1")，暂停
        job.setStatus(ScheduleConstants.Status.PAUSE.getValue());
        this.insert(job);
        ScheduleUtils.createScheduleJob(scheduler, job);
        return 1;
    }

    ///**
    // * 获取quartz调度器的计划任务列表
    // *
    // * @param job 调度信息
    // * @return
    // */
    //@Override
    //public List<SaJobPojo> selectJobList(SaJobPojo job) {
    //    return jobMapper.selectJobList(job);
    //}

    /**
     * 通过调度任务ID查询调度信息
     *
     * @param jobId 调度任务ID
     * @return 调度任务对象信息
     */
    @Override
    public SaJobPojo selectJobById(String jobId) {
        return jobMapper.getEntity(jobId);
    }

    /**
     * 暂停任务
     *
     * @param job 调度信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int pauseJob(SaJobPojo job) throws SchedulerException {
        String jobId = job.getId();
        String jobGroup = job.getJobgroup();
        job.setStatus(ScheduleConstants.Status.PAUSE.getValue());
        this.update(job);
        scheduler.pauseJob(ScheduleUtils.getJobKey(jobId, jobGroup));
        return 1;
    }

    /**
     * 恢复任务
     *
     * @param job 调度信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int resumeJob(SaJobPojo job) throws SchedulerException {
        String jobId = job.getId();
        String jobGroup = job.getJobgroup();
        job.setStatus(ScheduleConstants.Status.NORMAL.getValue());
        this.update(job);
        scheduler.resumeJob(ScheduleUtils.getJobKey(jobId, jobGroup));
        return 1;
    }

    /**
     * 删除任务后，所对应的trigger也将被删除
     *
     * @param job 调度信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteJob(SaJobPojo job) throws SchedulerException {
        String jobId    = job.getId();
        String jobGroup = job.getJobgroup();
        JobKey jobKey   = ScheduleUtils.getJobKey(jobId, jobGroup);

        // 1. 备份任务直接删调度，返回 1
        if (job.isBackupmark()) {
            scheduler.deleteJob(jobKey);
            return 1;
        }

        // 2. 普通任务：先删库成功后，再删调度
        int rowsDeleted = this.delete(jobId);
        if (rowsDeleted > 0) {
            scheduler.deleteJob(jobKey);
        }
        return rowsDeleted;
    }


    /**
     * 批量删除调度信息
     *
     * @param jobIds 需要删除的任务ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteJobByIds(String[] jobIds) throws SchedulerException {
        for (String jobId : jobIds) {
            SaJobPojo job = jobMapper.getEntity(jobId);
            deleteJob(job);
        }
    }

    /**
     * 任务调度状态修改
     *
     * @param job 调度信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int changeStatus(SaJobPojo job) throws SchedulerException {
        int rows = 0;
        String status = job.getStatus();
        if (ScheduleConstants.Status.NORMAL.getValue().equals(status)) {
            rows = resumeJob(job);
        } else if (ScheduleConstants.Status.PAUSE.getValue().equals(status)) {
            rows = pauseJob(job);
        }
        return rows;
    }

    /**
     * 立即运行任务
     *
     * @param job 调度信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean run(SaJobPojo job) throws SchedulerException {
        boolean result = false;
        String jobId = job.getId();
        String jobGroup = job.getJobgroup();
        SaJobPojo properties = selectJobById(job.getId());
        // 参数
        JobDataMap dataMap = new JobDataMap();
        dataMap.put(ScheduleConstants.TASK_PROPERTIES, properties);
        JobKey jobKey = ScheduleUtils.getJobKey(jobId, jobGroup);
        if (scheduler.checkExists(jobKey)) {
            result = true;
            try {
                scheduler.triggerJob(jobKey, dataMap);
            } catch (SchedulerException e) {
                throw new RuntimeException(e);
            }
        }
        return result;
    }


    /**
     * 更新任务的时间表达式
     *
     * @param job 调度信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateJob(SaJobPojo job) throws SchedulerException, TaskException {
        SaJobPojo properties = selectJobById(job.getId());
        this.update(job);
        updateSchedulerJob(job, properties.getJobgroup());
        return 1;
    }

    /**
     * 更新任务
     *
     * @param job      任务对象
     * @param jobGroup 任务组名
     */
    public void updateSchedulerJob(SaJobPojo job, String jobGroup) throws SchedulerException, TaskException {
        String jobId = job.getId();
        // 判断是否存在
        JobKey jobKey = ScheduleUtils.getJobKey(jobId, jobGroup);
        if (scheduler.checkExists(jobKey)) {
            // 防止创建时存在数据问题 先移除，然后在执行创建操作
            scheduler.deleteJob(jobKey);
        }
        ScheduleUtils.createScheduleJob(scheduler, job);
    }

    /**
     * 校验cron表达式是否有效
     *
     * @param cronExpression 表达式
     * @return 结果
     */
    @Override
    public boolean checkCronExpressionIsValid(String cronExpression) {
        return CronUtils.isValid(cronExpression);
    }


    // 查询当前系统存在的所有定时任务
    @Override
    public List<ScheduleInfo> getAllScheduledJobs() throws SchedulerException {
        return listAllJobs(scheduler);
    }

    static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * 查询当前 Scheduler 中所有任务及其状态、下次执行时间等信息
     *
     * @param scheduler Quartz Scheduler 实例
     * @return List<ScheduleInfo> 列表
     * @throws SchedulerException
     */
    public static List<ScheduleInfo> listAllJobs(Scheduler scheduler) throws SchedulerException {
        List<ScheduleInfo> result = new ArrayList<>();

        // 遍历所有 JobGroup
        for (String groupName : scheduler.getJobGroupNames()) {
            // 获取该组下所有 JobKey
            Set<JobKey> jobKeys = scheduler.getJobKeys(GroupMatcher.jobGroupEquals(groupName));
            for (JobKey jobKey : jobKeys) {
                // 获取 JobDetail
                JobDetail jobDetail = scheduler.getJobDetail(jobKey);
                SaJobPojo pojo = (SaJobPojo) jobDetail.getJobDataMap()
                        .get(ScheduleConstants.TASK_PROPERTIES);

                // 获取该 Job 的所有 Trigger
                List<? extends Trigger> triggers = scheduler.getTriggersOfJob(jobKey);
                for (Trigger trigger : triggers) {
                    Trigger.TriggerState triggerState = scheduler.getTriggerState(trigger.getKey());
                    ScheduleInfo info = new ScheduleInfo();
                    info.setJobgroup(groupName);
                    info.setJobname(pojo != null ? pojo.getJobname() : jobKey.getName());
                    info.setTriggername(trigger.getKey().getName());
                    info.setTriggerstate(triggerState.name());
                    Date nextFire = trigger.getNextFireTime();
                    Date prevFire = trigger.getPreviousFireTime();
                    info.setNextfiretime(nextFire != null ? sdf.format(nextFire) : null);
                    info.setPreviousfiretime(prevFire != null ? sdf.format(prevFire) : null);
                    // 如果是 CronTrigger，则可以获取表达式
                    if (trigger instanceof CronTrigger) {
                        info.setCronexpression(((CronTrigger) trigger).getCronExpression());
                    }
                    result.add(info);
                }
            }
        }
        return result;
    }

    @Override
    public SaJobPojo getEntity(String key) {
        return this.jobMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaJobPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaJobPojo> lst = jobMapper.getPageList(queryParam);
            PageInfo<SaJobPojo> pageInfo = new PageInfo<SaJobPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public SaJobPojo insert(SaJobPojo saJobPojo) throws SchedulerException, TaskException {
        //初始化NULL字段
        cleanNull(saJobPojo);
        SaJobEntity saJobEntity = new SaJobEntity();
        BeanUtils.copyProperties(saJobPojo, saJobEntity);
        //生成雪花id
        saJobEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        this.jobMapper.insert(saJobEntity);
        return this.getEntity(saJobEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saJobPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaJobPojo update(SaJobPojo saJobPojo) {
        SaJobEntity saJobEntity = new SaJobEntity();
        BeanUtils.copyProperties(saJobPojo, saJobEntity);
        this.jobMapper.update(saJobEntity);
        return this.getEntity(saJobEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.jobMapper.delete(key);
    }


    private static void cleanNull(SaJobPojo saJobPojo) {
        if (saJobPojo.getJobname() == null) saJobPojo.setJobname("");
        if (saJobPojo.getJobgroup() == null) saJobPojo.setJobgroup("");
        if (saJobPojo.getInvoketarget() == null) saJobPojo.setInvoketarget("");
        if (saJobPojo.getCronexpression() == null) saJobPojo.setCronexpression("");
        if (saJobPojo.getMisfirepolicy() == null) saJobPojo.setMisfirepolicy("");
        if (saJobPojo.getConcurrent() == null) saJobPojo.setConcurrent("");
        if (saJobPojo.getStatus() == null) saJobPojo.setStatus("");
        if (saJobPojo.getCreateby() == null) saJobPojo.setCreateby("");
        if (saJobPojo.getCreatetime() == null) saJobPojo.setCreatetime(new Date());
        if (saJobPojo.getUpdateby() == null) saJobPojo.setUpdateby("");
        if (saJobPojo.getUpdatetime() == null) saJobPojo.setUpdatetime(new Date());
        if (saJobPojo.getRemark() == null) saJobPojo.setRemark("");
    }


}
