// SqlExecutorController.java
package inks.service.sa.uts.service.sql;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/sql")
@Api(tags = "SQL执行器接口")
public class SqlExecutorController {
    
    @Resource
    private SqlExecutorService sqlExecutorService;
    
    @PostMapping("/execute")
    @ApiOperation("执行单条SQL语句")
    public R<SqlExecutionResult> executeSql(@RequestBody SqlExecuteRequest request) {
        try {
            log.info("收到SQL执行请求: {}", request);
            SqlExecutionResult result = sqlExecutorService.executeSql(request);
            return R.ok(result);
        } catch (SqlExecutionException e) {
            log.error("SQL执行失败", e);
            return R.fail(e.getMessage());
        }
    }
    
    @PostMapping("/batch")
    @ApiOperation("批量执行SQL语句")
    public R<List<SqlExecutionResult>> executeBatchSql(@RequestBody List<SqlExecuteRequest> requests) {
        try {
            log.info("收到批量SQL执行请求, 数量: {}", requests.size());
            List<SqlExecutionResult> results = sqlExecutorService.executeBatchSql(requests);
            return R.ok(results);
        } catch (SqlExecutionException e) {
            log.error("批量SQL执行失败", e);
            return R.fail(e.getMessage());
        }
    }
    
    @PostMapping("/async")
    @ApiOperation("异步执行SQL语句")
    public R<String> executeAsyncSql(@RequestBody SqlExecuteRequest request) {
        try {
            log.info("收到异步SQL执行请求: {}", request);
            sqlExecutorService.executeAsyncSql(request)
                .thenAccept(result -> log.info("异步SQL执行完成: {}", result));
            return R.ok("异步执行已提交");
        } catch (Exception e) {
            log.error("异步SQL提交失败", e);
            return R.fail(e.getMessage());
        }
    }
}