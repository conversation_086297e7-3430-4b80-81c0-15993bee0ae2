package inks.service.sa.uts.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.SaSshpipelinesitemPojo;
import inks.service.sa.uts.domain.SaSshpipelinesitemEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * SSH流水线步骤(SaSshpipelinesitem)表服务接口
 *
 * <AUTHOR>
 * @since 2025-05-19 11:11:44
 */
public interface SaSshpipelinesitemService {


    SaSshpipelinesitemPojo getEntity(String key);

    PageInfo<SaSshpipelinesitemPojo> getPageList(QueryParam queryParam);

    List<SaSshpipelinesitemPojo> getList(String Pid);  

    SaSshpipelinesitemPojo insert(SaSshpipelinesitemPojo saSshpipelinesitemPojo);

    SaSshpipelinesitemPojo update(SaSshpipelinesitemPojo saSshpipelinesitempojo);

    int delete(String key);

    SaSshpipelinesitemPojo clearNull(SaSshpipelinesitemPojo saSshpipelinesitempojo);
}
