package inks.service.sa.uts.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.UtsFreereportsPojo;
import inks.service.sa.uts.domain.pojo.UtsFreereportsitemPojo;
import inks.service.sa.uts.domain.pojo.UtsFreereportsitemdetailPojo;

import java.util.List;
import java.util.Map;

/**
 * 自由报表(UtsFreereports)表服务接口
 *
 * <AUTHOR>
 * @since 2024-05-14 10:14:36
 */
public interface UtsFreereportsService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    UtsFreereportsPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<UtsFreereportsitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    UtsFreereportsPojo getBillEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<UtsFreereportsPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<UtsFreereportsPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param utsFreereportsPojo 实例对象
     * @return 实例对象
     */
    UtsFreereportsPojo insert(UtsFreereportsPojo utsFreereportsPojo);

    /**
     * 修改数据
     *
     * @param utsFreereportspojo 实例对象
     * @return 实例对象
     */
    UtsFreereportsPojo update(UtsFreereportsPojo utsFreereportspojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    List<Map<String, Object>> select(String key, String tid);

    Object selectEdb(String key, Map<String, Object> params, String datapath, String tid);

    Object pageListEdb(String code, String key, Map<String, Object> params,String datapath, LoginUser loginUser);

    PageInfo<Map<String, Object>> pageList(String code, QueryParam queryParam, LoginUser loginUser);

    List<UtsFreereportsPojo> getListBySelf(int domainnum, String userid, String tid);

    List<UtsFreereportsPojo> myFreeReports(String userid, String tid);

    List<UtsFreereportsitemPojo> getListByReportCode(String reportcode, String tenantid);

    List<Map<String, Object>> getSqlSelectFieldList(String key, String tid);
}
