package inks.service.sa.uts.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.uts.domain.UtsWxemsgEntity;
import inks.service.sa.uts.domain.pojo.UtsWxemsgPojo;
import inks.service.sa.uts.mapper.UtsWxemsgMapper;
import inks.service.sa.uts.service.UtsWxemsgService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 企业微信信息(UtsWxemsg)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-10 11:12:38
 */
@Service("utsWxemsgService")
public class UtsWxemsgServiceImpl implements UtsWxemsgService {
    @Resource
    private UtsWxemsgMapper utsWxemsgMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public UtsWxemsgPojo getEntity(String key, String tid) {
        return this.utsWxemsgMapper.getEntity(key, tid);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<UtsWxemsgPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<UtsWxemsgPojo> lst = utsWxemsgMapper.getPageList(queryParam);
            PageInfo<UtsWxemsgPojo> pageInfo = new PageInfo<UtsWxemsgPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param utsWxemsgPojo 实例对象
     * @return 实例对象
     */
    @Override
    public UtsWxemsgPojo insert(UtsWxemsgPojo utsWxemsgPojo) {
        //初始化NULL字段
        if (utsWxemsgPojo.getMsggroupid() == null) utsWxemsgPojo.setMsggroupid("");
        if (utsWxemsgPojo.getMsgcode() == null) utsWxemsgPojo.setMsgcode("");
        if (utsWxemsgPojo.getMsgname() == null) utsWxemsgPojo.setMsgname("");
        if (utsWxemsgPojo.getMsgtype() == null) utsWxemsgPojo.setMsgtype("");
        if (utsWxemsgPojo.getMsgtemplate() == null) utsWxemsgPojo.setMsgtemplate("");
        if (utsWxemsgPojo.getModulecode() == null) utsWxemsgPojo.setModulecode("");
        if (utsWxemsgPojo.getUserlist() == null) utsWxemsgPojo.setUserlist("");
        if (utsWxemsgPojo.getDeptlist() == null) utsWxemsgPojo.setDeptlist("");
        if (utsWxemsgPojo.getObjjson() == null) utsWxemsgPojo.setObjjson("");
        if (utsWxemsgPojo.getRownum() == null) utsWxemsgPojo.setRownum(0);
        if (utsWxemsgPojo.getUrltemplate() == null) utsWxemsgPojo.setUrltemplate("");
        if (utsWxemsgPojo.getEnabledmark() == null) utsWxemsgPojo.setEnabledmark(0);
        if (utsWxemsgPojo.getSummary() == null) utsWxemsgPojo.setSummary("");
        if (utsWxemsgPojo.getCreateby() == null) utsWxemsgPojo.setCreateby("");
        if (utsWxemsgPojo.getCreatebyid() == null) utsWxemsgPojo.setCreatebyid("");
        if (utsWxemsgPojo.getCreatedate() == null) utsWxemsgPojo.setCreatedate(new Date());
        if (utsWxemsgPojo.getLister() == null) utsWxemsgPojo.setLister("");
        if (utsWxemsgPojo.getListerid() == null) utsWxemsgPojo.setListerid("");
        if (utsWxemsgPojo.getModifydate() == null) utsWxemsgPojo.setModifydate(new Date());
        if (utsWxemsgPojo.getCustom1() == null) utsWxemsgPojo.setCustom1("");
        if (utsWxemsgPojo.getCustom2() == null) utsWxemsgPojo.setCustom2("");
        if (utsWxemsgPojo.getCustom3() == null) utsWxemsgPojo.setCustom3("");
        if (utsWxemsgPojo.getCustom4() == null) utsWxemsgPojo.setCustom4("");
        if (utsWxemsgPojo.getCustom5() == null) utsWxemsgPojo.setCustom5("");
        if (utsWxemsgPojo.getTenantid() == null) utsWxemsgPojo.setTenantid("");
        if (utsWxemsgPojo.getTenantname() == null) utsWxemsgPojo.setTenantname("");
        if (utsWxemsgPojo.getRevision() == null) utsWxemsgPojo.setRevision(0);
        UtsWxemsgEntity utsWxemsgEntity = new UtsWxemsgEntity();
        BeanUtils.copyProperties(utsWxemsgPojo, utsWxemsgEntity);

        utsWxemsgEntity.setId(UUID.randomUUID().toString());
        utsWxemsgEntity.setRevision(1);  //乐观锁
        this.utsWxemsgMapper.insert(utsWxemsgEntity);
        return this.getEntity(utsWxemsgEntity.getId(), utsWxemsgEntity.getTenantid());

    }

    /**
     * 修改数据
     *
     * @param utsWxemsgPojo 实例对象
     * @return 实例对象
     */
    @Override
    public UtsWxemsgPojo update(UtsWxemsgPojo utsWxemsgPojo) {
        UtsWxemsgEntity utsWxemsgEntity = new UtsWxemsgEntity();
        BeanUtils.copyProperties(utsWxemsgPojo, utsWxemsgEntity);
        this.utsWxemsgMapper.update(utsWxemsgEntity);
        return this.getEntity(utsWxemsgEntity.getId(), utsWxemsgEntity.getTenantid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.utsWxemsgMapper.delete(key, tid);
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public UtsWxemsgPojo getEntityByMsgCode(String key, String tid) {
        try {
            //读取主表
            UtsWxemsgPojo oaWxemsgPojo = this.utsWxemsgMapper.getEntityByMsgCode(key, tid);
            return oaWxemsgPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public String getWxeUserIdByOmsUserid(String omsUserid, String tid) {
        return this.utsWxemsgMapper.getWxeUserIdByOmsUserid(omsUserid, tid);
    }
}
