package inks.service.sa.uts.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.UtsDinghookmsgPojo;
import inks.service.sa.uts.domain.UtsDinghookmsgEntity;

import com.github.pagehelper.PageInfo;
import inks.service.sa.uts.domain.pojo.UtsWxehookmsgPojo;

/**
 * 钉钉群机器人信息(UtsDinghookmsg)表服务接口
 *
 * <AUTHOR>
 * @since 2025-02-28 13:12:38
 */
public interface UtsDinghookmsgService {


    UtsDinghookmsgPojo getEntity(String key);

    PageInfo<UtsDinghookmsgPojo> getPageList(QueryParam queryParam);

    UtsDinghookmsgPojo insert(UtsDinghookmsgPojo utsDinghookmsgPojo);

    UtsDinghookmsgPojo update(UtsDinghookmsgPojo utsDinghookmsgpojo);

    int delete(String key);

    UtsWxehookmsgPojo getEntityByMsgCode(String msgCode, String tid);
}
