package inks.service.sa.uts.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.UtsWxehookmsgPojo;
import inks.service.sa.uts.domain.UtsWxehookmsgEntity;

import com.github.pagehelper.PageInfo;

/**
 * 企业微信群机器人信息(UtsWxehookmsg)表服务接口
 *
 * <AUTHOR>
 * @since 2025-02-28 13:12:38
 */
public interface UtsWxehookmsgService {


    UtsWxehookmsgPojo getEntity(String key);

    PageInfo<UtsWxehookmsgPojo> getPageList(QueryParam queryParam);

    UtsWxehookmsgPojo insert(UtsWxehookmsgPojo utsWxehookmsgPojo);

    UtsWxehookmsgPojo update(UtsWxehookmsgPojo utsWxehookmsgpojo);

    int delete(String key);

    UtsWxehookmsgPojo getEntityByMsgCode(String msgCode, String tid);
}
