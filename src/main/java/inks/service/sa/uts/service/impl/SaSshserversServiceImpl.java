package inks.service.sa.uts.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.uts.domain.pojo.SaSshserversPojo;
import inks.service.sa.uts.domain.SaSshserversEntity;
import inks.service.sa.uts.mapper.SaSshserversMapper;
import inks.service.sa.uts.service.SaSshserversService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/**
 * SSH服务器配置(SaSshservers)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-19 11:11:06
 */
@Service("saSshserversService")
public class SaSshserversServiceImpl implements SaSshserversService {
    @Resource
    private SaSshserversMapper saSshserversMapper;

    @Override
    public SaSshserversPojo getEntity(String key) {
        return this.saSshserversMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaSshserversPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaSshserversPojo> lst = saSshserversMapper.getPageList(queryParam);
            PageInfo<SaSshserversPojo> pageInfo = new PageInfo<SaSshserversPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public SaSshserversPojo insert(SaSshserversPojo saSshserversPojo) {
        //初始化NULL字段
        cleanNull(saSshserversPojo);
        SaSshserversEntity saSshserversEntity = new SaSshserversEntity(); 
        BeanUtils.copyProperties(saSshserversPojo,saSshserversEntity);
        //生成雪花id
          saSshserversEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saSshserversEntity.setRevision(1);  //乐观锁
          this.saSshserversMapper.insert(saSshserversEntity);
        return this.getEntity(saSshserversEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param saSshserversPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaSshserversPojo update(SaSshserversPojo saSshserversPojo) {
        SaSshserversEntity saSshserversEntity = new SaSshserversEntity(); 
        BeanUtils.copyProperties(saSshserversPojo,saSshserversEntity);
        this.saSshserversMapper.update(saSshserversEntity);
        return this.getEntity(saSshserversEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saSshserversMapper.delete(key) ;
    }
    

    private static void cleanNull(SaSshserversPojo saSshserversPojo) {
        if(saSshserversPojo.getServername()==null) saSshserversPojo.setServername("");
        if(saSshserversPojo.getHost()==null) saSshserversPojo.setHost("");
        if(saSshserversPojo.getPort()==null) saSshserversPojo.setPort(0);
        if(saSshserversPojo.getUsername()==null) saSshserversPojo.setUsername("");
        if(saSshserversPojo.getPassword()==null) saSshserversPojo.setPassword("");
        if(saSshserversPojo.getGroupname()==null) saSshserversPojo.setGroupname("");
        if(saSshserversPojo.getRownum()==null) saSshserversPojo.setRownum(0);
        if(saSshserversPojo.getRemark()==null) saSshserversPojo.setRemark("");
        if(saSshserversPojo.getCreateby()==null) saSshserversPojo.setCreateby("");
        if(saSshserversPojo.getCreatebyid()==null) saSshserversPojo.setCreatebyid("");
        if(saSshserversPojo.getCreatedate()==null) saSshserversPojo.setCreatedate(new Date());
        if(saSshserversPojo.getLister()==null) saSshserversPojo.setLister("");
        if(saSshserversPojo.getListerid()==null) saSshserversPojo.setListerid("");
        if(saSshserversPojo.getModifydate()==null) saSshserversPojo.setModifydate(new Date());
        if(saSshserversPojo.getCustom1()==null) saSshserversPojo.setCustom1("");
        if(saSshserversPojo.getCustom2()==null) saSshserversPojo.setCustom2("");
        if(saSshserversPojo.getCustom3()==null) saSshserversPojo.setCustom3("");
        if(saSshserversPojo.getCustom4()==null) saSshserversPojo.setCustom4("");
        if(saSshserversPojo.getCustom5()==null) saSshserversPojo.setCustom5("");
        if(saSshserversPojo.getTenantid()==null) saSshserversPojo.setTenantid("");
        if(saSshserversPojo.getRevision()==null) saSshserversPojo.setRevision(0);
   }

}
