package inks.service.sa.uts.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.UtsWxeapprPojo;
import inks.service.sa.uts.domain.UtsWxeapprEntity;

import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 企业微审核(UtsWxeappr)表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-16 14:12:14
 */
public interface UtsWxeapprService {


    UtsWxeapprPojo getEntity(String key);

    PageInfo<UtsWxeapprPojo> getPageList(QueryParam queryParam);

    UtsWxeapprPojo insert(UtsWxeapprPojo utsWxeapprPojo);

    UtsWxeapprPojo update(UtsWxeapprPojo utsWxeapprpojo);

    int delete(String key);

    List<UtsWxeapprPojo> getListByModuleCode(String code, String tid);
}
