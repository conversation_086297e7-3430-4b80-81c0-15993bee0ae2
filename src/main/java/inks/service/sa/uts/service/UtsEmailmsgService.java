package inks.service.sa.uts.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.UtsEmailmsgPojo;
import inks.service.sa.uts.domain.UtsEmailmsgEntity;

import com.github.pagehelper.PageInfo;

/**
 * 邮件信息(UtsEmailmsg)表服务接口
 *
 * <AUTHOR>
 * @since 2025-02-28 14:01:05
 */
public interface UtsEmailmsgService {


    UtsEmailmsgPojo getEntity(String key);

    PageInfo<UtsEmailmsgPojo> getPageList(QueryParam queryParam);

    UtsEmailmsgPojo insert(UtsEmailmsgPojo utsEmailmsgPojo);

    UtsEmailmsgPojo update(UtsEmailmsgPojo utsEmailmsgpojo);

    int delete(String key);
}
