package inks.service.sa.uts.service.sql;

import inks.sa.common.core.utils.PrintColor;
import inks.service.sa.uts.config.DataSourceManager;
import inks.service.sa.uts.domain.database.DataSourceConfig;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import javax.sql.DataSource;
import java.math.BigDecimal;
import java.sql.*;
import java.util.Date;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SqlExecutorServiceImpl2 implements SqlExecutorService2 {

    @Autowired
    private DataSourceManager dataSourceManager;

    @Autowired
    private SqlValidator sqlValidator;

    // 缓存预编译语句的模板
    private final Map<String, String> preparedStatementCache = new ConcurrentHashMap<>();



    @Override
    @Transactional(rollbackFor = Exception.class)
    public SqlExecutionResult executeSql(SqlExecuteRequest2 request) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start(); // 启动计时器

        SqlExecutionResult result = new SqlExecutionResult();
        // 最终执行的SQL
        String sql = request.getSql();

        // 获取指定的数据源
        DataSource dataSource = dataSourceManager.getDataSource(request.getDataSourceKey());
        if (dataSource == null) {
            throw new SqlExecutionException("数据源未找到: " + request.getDataSourceKey());
        }

        try {
            // 验证SQL
            sqlValidator.validate(sql);

            // 获取或创建预编译SQL模板
            String preparedSql = getPreparedSql(sql);

            // 从连接池获取连接并执行
            try (Connection conn = dataSource.getConnection();
                 PreparedStatement stmt = prepareStatement(conn, preparedSql, request)) {
                PrintColor.zi("获取连接成功，最终执行SQL： " + preparedSql); // 打印获取连接成功的信息和执行的SQL
                configureStatement(stmt, request);
                return executeStatement(stmt, request, result);
            }
        } catch (SQLException sqlException) {
            handleExecutionException(sqlException, result);
            throw new SqlExecutionException("SQL执行失败: " + sqlException.getMessage(), sqlException);
        } catch (Exception e) {
            // 处理非SQL异常
            handleExecutionException(e, result);
            throw new SqlExecutionException("SQL执行失败: " + e.getMessage(), e);
        } finally {
            stopWatch.stop();
            result.setExecutionTime(stopWatch.getTotalTimeMillis());
            logExecutionResult(request, result, stopWatch.getTotalTimeMillis());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<SqlExecutionResult> executeBatchSql(List<SqlExecuteRequest2> requests) {
        log.info("开始执行批量SQL, 总数: {}", requests.size());
        return requests.stream()
                .map(this::executeSql)
                .collect(Collectors.toList());
    }

    @Override
    @Async
    public CompletableFuture<SqlExecutionResult> executeAsyncSql(SqlExecuteRequest2 request) {
        log.info("开始异步执行SQL: {}", request.getSql());
        return CompletableFuture.supplyAsync(() -> executeSql(request));
    }

    // 添加自定义数据源的方法
    public void addCustomDataSource(String key, String url, String username, String password) {
        DataSourceConfig config = new DataSourceConfig();
        config.setUrl(url);
        config.setUsername(username);
        config.setPassword(password);
        config.setDriverClassName(detectDriverClassName(url));

        dataSourceManager.addDataSource(key, config);
    }

    private String detectDriverClassName(String url) {
        if (url.contains("mysql")) {
            return "com.mysql.cj.jdbc.Driver";
        } else if (url.contains("postgresql")) {
            return "org.postgresql.Driver";
        } else if (url.contains("oracle")) {
            return "oracle.jdbc.OracleDriver";
        }
        throw new IllegalArgumentException("Unsupported database type in URL: " + url);
    }

    // 以下方法保持不变...
    private String getPreparedSql(String originalSql) {
        return preparedStatementCache.computeIfAbsent(originalSql, this::convertToParameterizedSql);
    }

    private String convertToParameterizedSql(String sql) {
        return sql.replaceAll("\\?", "?");
    }

    private PreparedStatement prepareStatement(Connection conn, String sql, SqlExecuteRequest2 request)
            throws SQLException {
        PreparedStatement stmt = conn.prepareStatement(sql,
                ResultSet.TYPE_SCROLL_INSENSITIVE,
                ResultSet.CONCUR_READ_ONLY);

        if (request.getParams() != null && !request.getParams().isEmpty()) {
            int paramIndex = 1;
            for (Map.Entry<String, Object> entry : request.getParams().entrySet()) {
                setParameter(stmt, paramIndex++, entry.getValue());
            }
        }

        return stmt;
    }

    private void setParameter(PreparedStatement stmt, int index, Object value) throws SQLException {
        if (value == null) {
            stmt.setNull(index, Types.NULL);
            return;
        }

        if (value instanceof String) {
            stmt.setString(index, (String) value);
        } else if (value instanceof Integer) {
            stmt.setInt(index, (Integer) value);
        } else if (value instanceof Long) {
            stmt.setLong(index, (Long) value);
        } else if (value instanceof Double) {
            stmt.setDouble(index, (Double) value);
        } else if (value instanceof Date) {
            stmt.setTimestamp(index, new Timestamp(((Date) value).getTime()));
        } else if (value instanceof Boolean) {
            stmt.setBoolean(index, (Boolean) value);
        } else if (value instanceof BigDecimal) {
            stmt.setBigDecimal(index, (BigDecimal) value);
        } else if (value instanceof byte[]) {
            stmt.setBytes(index, (byte[]) value);
        } else {
            stmt.setObject(index, value);
        }
    }

    private void configureStatement(PreparedStatement stmt, SqlExecuteRequest2 request)
            throws SQLException {
        if (request.getTimeout() != null) {
            stmt.setQueryTimeout(request.getTimeout());
        }

        if (request.getMaxRows() != null) {
            stmt.setMaxRows(request.getMaxRows());
        }

        stmt.setFetchSize(1000);
    }

    private SqlExecutionResult executeStatement(PreparedStatement stmt,
                                                SqlExecuteRequest2 request, SqlExecutionResult result) throws SQLException {
        boolean isQuery = stmt.execute();

        if (isQuery) {
            try (ResultSet rs = stmt.getResultSet()) {
                result.setData(convertResultSetToList(rs));
                result.setType("SELECT");
                rs.last();
                result.setTotalRows(rs.getRow());
                rs.beforeFirst();
            }
        } else {
            int affected = stmt.getUpdateCount();
            result.setAffectedRows(affected);
            result.setType(determineSqlType(request.getSql()));
        }

        result.setSuccess(true);
        return result;
    }

    private List<Map<String, Object>> convertResultSetToList(ResultSet rs) throws SQLException {
        List<Map<String, Object>> results = new ArrayList<>();
        ResultSetMetaData metaData = rs.getMetaData();
        int columnCount = metaData.getColumnCount();

        while (rs.next()) {
            Map<String, Object> row = new LinkedHashMap<>();
            for (int i = 1; i <= columnCount; i++) {
                String columnName = metaData.getColumnLabel(i);
                Object value = convertValue(rs.getObject(i));
                row.put(columnName, value);
            }
            results.add(row);
        }

        return results;
    }

    private Object convertValue(Object value) {
        if (value instanceof Timestamp) {
            return new Date(((Timestamp) value).getTime());
        }
        if (value instanceof Blob) {
            try {
                Blob blob = (Blob) value;
                byte[] bytes = blob.getBytes(1, (int) blob.length());
                return Base64.getEncoder().encodeToString(bytes);
            } catch (SQLException e) {
                log.error("Blob转换失败", e);
                return null;
            }
        }
        if (value instanceof Clob) {
            try {
                Clob clob = (Clob) value;
                return clob.getSubString(1, (int) clob.length());
            } catch (SQLException e) {
                log.error("Clob转换失败", e);
                return null;
            }
        }
        return value;
    }

    private String determineSqlType(String sql) {
        sql = sql.trim().toUpperCase();
        if (sql.startsWith("INSERT")) return "INSERT";
        if (sql.startsWith("UPDATE")) return "UPDATE";
        if (sql.startsWith("DELETE")) return "DELETE";
        if (sql.startsWith("SELECT")) return "SELECT";
        return "OTHER";
    }

    private void handleExecutionException(Exception e, SqlExecutionResult result) {
        result.setSuccess(false);
        result.setErrorMessage(e.getMessage());

        if (e instanceof SQLException) {
            SQLException sqlException = (SQLException) e;
            result.setErrorCode(sqlException.getErrorCode());
            result.setSqlState(sqlException.getSQLState());
        }

        log.error("SQL执行异常", e);
    }

    private void logExecutionResult(SqlExecuteRequest2 request,
                                    SqlExecutionResult result, long executionTime) {
        if (result.isSuccess()) {
            log.info("SQL执行成功 - 类型: {}, 耗时: {}ms, 影响行数: {}",
                    result.getType(), executionTime, result.getAffectedRows());
        } else {
            log.error("SQL执行失败 - 错误: {}, 耗时: {}ms",
                    result.getErrorMessage(), executionTime);
        }
    }
}