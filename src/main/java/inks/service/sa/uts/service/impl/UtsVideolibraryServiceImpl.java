package inks.service.sa.uts.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.sa.common.core.config.oss.service.FileController;
import inks.service.sa.uts.domain.pojo.UtsVideolibraryPojo;
import inks.service.sa.uts.domain.UtsVideolibraryEntity;
import inks.service.sa.uts.mapper.UtsVideolibraryMapper;
import inks.service.sa.uts.service.UtsVideolibraryService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/**
 * 视频信息表(UtsVideolibrary)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-29 14:19:51
 */
@Service("utsVideolibraryService")
public class UtsVideolibraryServiceImpl implements UtsVideolibraryService {
    @Resource
    private UtsVideolibraryMapper utsVideolibraryMapper;
    @Resource
    private FileController fileController;


    @Override
    public UtsVideolibraryPojo getEntity(String key) {
        return this.utsVideolibraryMapper.getEntity(key);
    }


    @Override
    public PageInfo<UtsVideolibraryPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<UtsVideolibraryPojo> lst = utsVideolibraryMapper.getPageList(queryParam);
            PageInfo<UtsVideolibraryPojo> pageInfo = new PageInfo<UtsVideolibraryPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public UtsVideolibraryPojo insert(UtsVideolibraryPojo utsVideolibraryPojo) {
        //初始化NULL字段
        cleanNull(utsVideolibraryPojo);
        UtsVideolibraryEntity utsVideolibraryEntity = new UtsVideolibraryEntity(); 
        BeanUtils.copyProperties(utsVideolibraryPojo,utsVideolibraryEntity);
        //生成雪花id
          utsVideolibraryEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          utsVideolibraryEntity.setRevision(1);  //乐观锁
          this.utsVideolibraryMapper.insert(utsVideolibraryEntity);
        return this.getEntity(utsVideolibraryEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param utsVideolibraryPojo 实例对象
     * @return 实例对象
     */
    @Override
    public UtsVideolibraryPojo update(UtsVideolibraryPojo utsVideolibraryPojo) {
        UtsVideolibraryEntity utsVideolibraryEntity = new UtsVideolibraryEntity(); 
        BeanUtils.copyProperties(utsVideolibraryPojo,utsVideolibraryEntity);
        this.utsVideolibraryMapper.update(utsVideolibraryEntity);
        return this.getEntity(utsVideolibraryEntity.getId());
    }


    @Override
    public int delete(String key) {
        // 同步删除minio的文件
        UtsVideolibraryPojo utsVideolibraryDB = getEntity(key);
        // objectname数据库存储的是完整url：http://dev.inksyun.com:30484/File/split/stream/video/202506/2930d7d673b74c63bb31e7bcfb112ae5.mp4
        String objectname = utsVideolibraryDB.getObjectname();
        // 截取/stream/后面的为 真实objectname
        String realObjectname = objectname.substring(objectname.indexOf("/stream/") + 8);
        fileController.remove(realObjectname);
        return this.utsVideolibraryMapper.delete(key) ;
    }
    
    @Override
    @Transactional
    public UtsVideolibraryPojo approval(UtsVideolibraryPojo utsVideolibraryPojo) {
        //主表更改
        UtsVideolibraryEntity utsVideolibraryEntity = new UtsVideolibraryEntity();
        BeanUtils.copyProperties(utsVideolibraryPojo,utsVideolibraryEntity);
        this.utsVideolibraryMapper.approval(utsVideolibraryEntity);
        //返回Bill实例
        return this.getEntity(utsVideolibraryEntity.getId());
    }

    private static void cleanNull(UtsVideolibraryPojo utsVideolibraryPojo) {
        if(utsVideolibraryPojo.getVideotitle()==null) utsVideolibraryPojo.setVideotitle("");
        if(utsVideolibraryPojo.getVideofilename()==null) utsVideolibraryPojo.setVideofilename("");
        if(utsVideolibraryPojo.getObjectname()==null) utsVideolibraryPojo.setObjectname("");
        if(utsVideolibraryPojo.getVideocoverurl()==null) utsVideolibraryPojo.setVideocoverurl("");
        if(utsVideolibraryPojo.getVideoduration()==null) utsVideolibraryPojo.setVideoduration(0);
        if(utsVideolibraryPojo.getVideoplaytimes()==null) utsVideolibraryPojo.setVideoplaytimes(0);
        if(utsVideolibraryPojo.getVideodesc()==null) utsVideolibraryPojo.setVideodesc("");
        if(utsVideolibraryPojo.getGengroupid()==null) utsVideolibraryPojo.setGengroupid("");
        if(utsVideolibraryPojo.getSecretkey()==null) utsVideolibraryPojo.setSecretkey("");
        if(utsVideolibraryPojo.getUploadtime()==null) utsVideolibraryPojo.setUploadtime(new Date());
        if(utsVideolibraryPojo.getTexttutorial()==null) utsVideolibraryPojo.setTexttutorial("");
        if(utsVideolibraryPojo.getVideotag()==null) utsVideolibraryPojo.setVideotag("");
        if(utsVideolibraryPojo.getBackcolorargb()==null) utsVideolibraryPojo.setBackcolorargb("");
        if(utsVideolibraryPojo.getForecolorargb()==null) utsVideolibraryPojo.setForecolorargb("");
        if(utsVideolibraryPojo.getPublicmark()==null) utsVideolibraryPojo.setPublicmark(0);
        if(utsVideolibraryPojo.getEnabledmark()==null) utsVideolibraryPojo.setEnabledmark(0);
        if(utsVideolibraryPojo.getRownum()==null) utsVideolibraryPojo.setRownum(0);
        if(utsVideolibraryPojo.getRemark()==null) utsVideolibraryPojo.setRemark("");
        if(utsVideolibraryPojo.getCreateby()==null) utsVideolibraryPojo.setCreateby("");
        if(utsVideolibraryPojo.getCreatebyid()==null) utsVideolibraryPojo.setCreatebyid("");
        if(utsVideolibraryPojo.getCreatedate()==null) utsVideolibraryPojo.setCreatedate(new Date());
        if(utsVideolibraryPojo.getLister()==null) utsVideolibraryPojo.setLister("");
        if(utsVideolibraryPojo.getListerid()==null) utsVideolibraryPojo.setListerid("");
        if(utsVideolibraryPojo.getModifydate()==null) utsVideolibraryPojo.setModifydate(new Date());
        if(utsVideolibraryPojo.getAssessor()==null) utsVideolibraryPojo.setAssessor("");
        if(utsVideolibraryPojo.getAssessorid()==null) utsVideolibraryPojo.setAssessorid("");
        if(utsVideolibraryPojo.getAssessdate()==null) utsVideolibraryPojo.setAssessdate(new Date());
        if(utsVideolibraryPojo.getCustom1()==null) utsVideolibraryPojo.setCustom1("");
        if(utsVideolibraryPojo.getCustom2()==null) utsVideolibraryPojo.setCustom2("");
        if(utsVideolibraryPojo.getCustom3()==null) utsVideolibraryPojo.setCustom3("");
        if(utsVideolibraryPojo.getCustom4()==null) utsVideolibraryPojo.setCustom4("");
        if(utsVideolibraryPojo.getCustom5()==null) utsVideolibraryPojo.setCustom5("");
        if(utsVideolibraryPojo.getCustom6()==null) utsVideolibraryPojo.setCustom6("");
        if(utsVideolibraryPojo.getCustom7()==null) utsVideolibraryPojo.setCustom7("");
        if(utsVideolibraryPojo.getCustom8()==null) utsVideolibraryPojo.setCustom8("");
        if(utsVideolibraryPojo.getCustom9()==null) utsVideolibraryPojo.setCustom9("");
        if(utsVideolibraryPojo.getCustom10()==null) utsVideolibraryPojo.setCustom10("");
        if(utsVideolibraryPojo.getTenantid()==null) utsVideolibraryPojo.setTenantid("");
        if(utsVideolibraryPojo.getTenantname()==null) utsVideolibraryPojo.setTenantname("");
        if(utsVideolibraryPojo.getRevision()==null) utsVideolibraryPojo.setRevision(0);
   }

}
