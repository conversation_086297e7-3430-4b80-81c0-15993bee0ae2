package inks.service.sa.uts.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.UtsFilelibraryPojo;
import inks.service.sa.uts.domain.UtsFilelibraryEntity;

import com.github.pagehelper.PageInfo;

/**
 * 文件库(UtsFilelibrary)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-17 16:51:51
 */
public interface UtsFilelibraryService {


    UtsFilelibraryPojo getEntity(String key);

    PageInfo<UtsFilelibraryPojo> getPageList(QueryParam queryParam);

    UtsFilelibraryPojo insert(UtsFilelibraryPojo utsFilelibraryPojo);

    UtsFilelibraryPojo update(UtsFilelibraryPojo utsFilelibrarypojo);

    int delete(String key);
}
