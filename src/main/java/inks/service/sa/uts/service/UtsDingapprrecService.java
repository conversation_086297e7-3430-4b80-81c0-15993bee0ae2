package inks.service.sa.uts.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.UtsDingapprrecPojo;
import inks.service.sa.uts.domain.UtsDingapprrecEntity;

import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 钉钉审批记录(UtsDingapprrec)表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-16 14:12:14
 */
public interface UtsDingapprrecService {


    UtsDingapprrecPojo getEntity(String key);

    PageInfo<UtsDingapprrecPojo> getPageList(QueryParam queryParam);

    UtsDingapprrecPojo insert(UtsDingapprrecPojo utsDingapprrecPojo);

    UtsDingapprrecPojo update(UtsDingapprrecPojo utsDingapprrecpojo);

    int delete(String key);

    UtsDingapprrecPojo getEntityBySpno(String key, String tid);

    /**
     * 通过Billid查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    List<UtsDingapprrecPojo> getOnlineByBillid(String key, String tid);}
