package inks.service.sa.uts.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.UtsDingmsgPojo;

/**
 * 钉钉信息(UtsDingmsg)表服务接口
 *
 * <AUTHOR>
 * @since 2023-01-10 11:11:53
 */
public interface UtsDingmsgService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    UtsDingmsgPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<UtsDingmsgPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param utsDingmsgPojo 实例对象
     * @return 实例对象
     */
    UtsDingmsgPojo insert(UtsDingmsgPojo utsDingmsgPojo);

    /**
     * 修改数据
     *
     * @param utsDingmsgpojo 实例对象
     * @return 实例对象
     */
    UtsDingmsgPojo update(UtsDingmsgPojo utsDingmsgpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    UtsDingmsgPojo getBillEntityByMsgCode(String key, String tenantid);

    String getDingUserIdByOmsUserid(String omsUserid, String tid);
}
