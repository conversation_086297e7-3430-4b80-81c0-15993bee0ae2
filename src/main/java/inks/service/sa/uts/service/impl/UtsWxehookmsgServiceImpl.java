package inks.service.sa.uts.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.uts.domain.pojo.UtsWxehookmsgPojo;
import inks.service.sa.uts.domain.UtsWxehookmsgEntity;
import inks.service.sa.uts.mapper.UtsWxehookmsgMapper;
import inks.service.sa.uts.service.UtsWxehookmsgService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/**
 * 企业微信群机器人信息(UtsWxehookmsg)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-28 13:12:38
 */
@Service("utsWxehookmsgService")
public class UtsWxehookmsgServiceImpl implements UtsWxehookmsgService {
    @Resource
    private UtsWxehookmsgMapper utsWxehookmsgMapper;

    @Override
    public UtsWxehookmsgPojo getEntity(String key) {
        return this.utsWxehookmsgMapper.getEntity(key);
    }


    @Override
    public PageInfo<UtsWxehookmsgPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<UtsWxehookmsgPojo> lst = utsWxehookmsgMapper.getPageList(queryParam);
            PageInfo<UtsWxehookmsgPojo> pageInfo = new PageInfo<UtsWxehookmsgPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public UtsWxehookmsgPojo insert(UtsWxehookmsgPojo utsWxehookmsgPojo) {
        //初始化NULL字段
        cleanNull(utsWxehookmsgPojo);
        UtsWxehookmsgEntity utsWxehookmsgEntity = new UtsWxehookmsgEntity(); 
        BeanUtils.copyProperties(utsWxehookmsgPojo,utsWxehookmsgEntity);
        //生成雪花id
          utsWxehookmsgEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          utsWxehookmsgEntity.setRevision(1);  //乐观锁
          this.utsWxehookmsgMapper.insert(utsWxehookmsgEntity);
        return this.getEntity(utsWxehookmsgEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param utsWxehookmsgPojo 实例对象
     * @return 实例对象
     */
    @Override
    public UtsWxehookmsgPojo update(UtsWxehookmsgPojo utsWxehookmsgPojo) {
        UtsWxehookmsgEntity utsWxehookmsgEntity = new UtsWxehookmsgEntity(); 
        BeanUtils.copyProperties(utsWxehookmsgPojo,utsWxehookmsgEntity);
        this.utsWxehookmsgMapper.update(utsWxehookmsgEntity);
        return this.getEntity(utsWxehookmsgEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.utsWxehookmsgMapper.delete(key) ;
    }
    

    private static void cleanNull(UtsWxehookmsgPojo utsWxehookmsgPojo) {
        if(utsWxehookmsgPojo.getMsggroupid()==null) utsWxehookmsgPojo.setMsggroupid("");
        if(utsWxehookmsgPojo.getMsgcode()==null) utsWxehookmsgPojo.setMsgcode("");
        if(utsWxehookmsgPojo.getMsgname()==null) utsWxehookmsgPojo.setMsgname("");
        if(utsWxehookmsgPojo.getMsgtype()==null) utsWxehookmsgPojo.setMsgtype("");
        if(utsWxehookmsgPojo.getMsgtemplate()==null) utsWxehookmsgPojo.setMsgtemplate("");
        if(utsWxehookmsgPojo.getModulecode()==null) utsWxehookmsgPojo.setModulecode("");
        if(utsWxehookmsgPojo.getWebhooklist()==null) utsWxehookmsgPojo.setWebhooklist("");
        if(utsWxehookmsgPojo.getUserlist()==null) utsWxehookmsgPojo.setUserlist("");
        if(utsWxehookmsgPojo.getDeptlist()==null) utsWxehookmsgPojo.setDeptlist("");
        if(utsWxehookmsgPojo.getObjjson()==null) utsWxehookmsgPojo.setObjjson("");
        if(utsWxehookmsgPojo.getRownum()==null) utsWxehookmsgPojo.setRownum(0);
        if(utsWxehookmsgPojo.getUrltemplate()==null) utsWxehookmsgPojo.setUrltemplate("");
        if(utsWxehookmsgPojo.getEnabledmark()==null) utsWxehookmsgPojo.setEnabledmark(0);
        if(utsWxehookmsgPojo.getRemark()==null) utsWxehookmsgPojo.setRemark("");
        if(utsWxehookmsgPojo.getCreateby()==null) utsWxehookmsgPojo.setCreateby("");
        if(utsWxehookmsgPojo.getCreatebyid()==null) utsWxehookmsgPojo.setCreatebyid("");
        if(utsWxehookmsgPojo.getCreatedate()==null) utsWxehookmsgPojo.setCreatedate(new Date());
        if(utsWxehookmsgPojo.getLister()==null) utsWxehookmsgPojo.setLister("");
        if(utsWxehookmsgPojo.getListerid()==null) utsWxehookmsgPojo.setListerid("");
        if(utsWxehookmsgPojo.getModifydate()==null) utsWxehookmsgPojo.setModifydate(new Date());
        if(utsWxehookmsgPojo.getCustom1()==null) utsWxehookmsgPojo.setCustom1("");
        if(utsWxehookmsgPojo.getCustom2()==null) utsWxehookmsgPojo.setCustom2("");
        if(utsWxehookmsgPojo.getCustom3()==null) utsWxehookmsgPojo.setCustom3("");
        if(utsWxehookmsgPojo.getCustom4()==null) utsWxehookmsgPojo.setCustom4("");
        if(utsWxehookmsgPojo.getCustom5()==null) utsWxehookmsgPojo.setCustom5("");
        if(utsWxehookmsgPojo.getTenantid()==null) utsWxehookmsgPojo.setTenantid("");
        if(utsWxehookmsgPojo.getTenantname()==null) utsWxehookmsgPojo.setTenantname("");
        if(utsWxehookmsgPojo.getRevision()==null) utsWxehookmsgPojo.setRevision(0);
   }

    @Override
    public UtsWxehookmsgPojo getEntityByMsgCode(String msgCode, String tid) {
        return this.utsWxehookmsgMapper.getEntityByMsgCode(msgCode, tid);
    }
}
