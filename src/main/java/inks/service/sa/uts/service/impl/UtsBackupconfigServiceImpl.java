package inks.service.sa.uts.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.exception.job.TaskException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.uts.backup.controller.DB_BackupServer;
import inks.service.sa.uts.config.constant.MyConstant;
import inks.service.sa.uts.domain.UtsBackupconfigEntity;
import inks.service.sa.uts.domain.pojo.SaJobPojo;
import inks.service.sa.uts.domain.pojo.UtsBackupconfigPojo;
import inks.service.sa.uts.domain.vo.TableInfoDTO;
import inks.service.sa.uts.mapper.UtsBackupconfigMapper;
import inks.service.sa.uts.service.SaJobService;
import inks.service.sa.uts.service.UtsBackupconfigService;
import inks.service.sa.uts.service.UtsDatabaseService;
import inks.service.sa.uts.utils.PrintColor;
import inks.service.sa.uts.utils.quartz.CronUtils;
import org.apache.commons.lang3.StringUtils;
import org.quartz.SchedulerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.sql.*;
import java.util.*;
import java.util.Date;

/**
 * 数据库备份配置(UtsBackupconfig)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-11 16:52:45
 */
@Service("utsBackupconfigService")
public class UtsBackupconfigServiceImpl implements UtsBackupconfigService {
    @Resource
    private UtsBackupconfigMapper utsBackupconfigMapper;
    @Resource
    private SaJobService saJobService;
    private final static Logger log = LoggerFactory.getLogger(UtsBackupconfigServiceImpl.class);

    @Override
    public UtsBackupconfigPojo getEntity(String key) {
        return this.utsBackupconfigMapper.getEntity(key);
    }


    @Override
    public PageInfo<UtsBackupconfigPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<UtsBackupconfigPojo> lst = utsBackupconfigMapper.getPageList(queryParam);
            PageInfo<UtsBackupconfigPojo> pageInfo = new PageInfo<UtsBackupconfigPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    @Transactional
    public UtsBackupconfigPojo insert(UtsBackupconfigPojo utsBackupconfigPojo) throws SchedulerException, TaskException {
        //初始化NULL字段
        cleanNull(utsBackupconfigPojo);
        UtsBackupconfigEntity utsBackupconfigEntity = new UtsBackupconfigEntity();
        BeanUtils.copyProperties(utsBackupconfigPojo, utsBackupconfigEntity);
        //生成雪花id
        utsBackupconfigEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        utsBackupconfigEntity.setRevision(1);  //乐观锁
        this.utsBackupconfigMapper.insert(utsBackupconfigEntity);
        UtsBackupconfigPojo backupconfigPojo = this.getEntity(utsBackupconfigEntity.getId());
        String cronexpression = utsBackupconfigPojo.getCronexpression();
        // 检查cron表达式是否有效
        if (StringUtils.isNotBlank(cronexpression) && !CronUtils.isValid(cronexpression)) {
            throw new BaseBusinessException("cron表达式不合法");
        }
        // 加入定时任务中
        if (Objects.equals(utsBackupconfigPojo.getEnabledmark(), 1) && StringUtils.isNotBlank(cronexpression)) {
            saJobService.backupToJob(backupconfigPojo);
            log.info("创建调度器定时任务");
        }
        return this.getEntity(utsBackupconfigEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param utsBackupconfigPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public UtsBackupconfigPojo update(UtsBackupconfigPojo utsBackupconfigPojo) throws SchedulerException, TaskException {
        UtsBackupconfigEntity utsBackupconfigEntity = new UtsBackupconfigEntity();
        BeanUtils.copyProperties(utsBackupconfigPojo, utsBackupconfigEntity);
        this.utsBackupconfigMapper.update(utsBackupconfigEntity);
        UtsBackupconfigPojo backupconfigPojo = this.getEntity(utsBackupconfigEntity.getId());
        String cronexpression = utsBackupconfigPojo.getCronexpression();
        // 检查cron表达式是否有效
        if (StringUtils.isNotBlank(cronexpression) && !CronUtils.isValid(cronexpression)) {
            throw new BaseBusinessException("cron表达式不合法");
        }
        // 加入定时任务中
        if (Objects.equals(utsBackupconfigPojo.getEnabledmark(), 1) && StringUtils.isNotBlank(cronexpression)) {
            saJobService.backupToJob(backupconfigPojo);
            PrintColor.red("修改调度器定时任务");
        } else {//否则Enabledmark=0或cron为空都删除定时任务
            // 删除定时任务
            SaJobPojo saJobPojo = new SaJobPojo();
            saJobPojo.setId(utsBackupconfigEntity.getId());
            saJobPojo.setJobgroup("UTS_BACKUP_GROUP");
            saJobPojo.setBackupmark(true);
            saJobService.deleteJob(saJobPojo);
        }
        return backupconfigPojo;
    }


    @Override
    @Transactional
    public int delete(String key) {
        int delete = this.utsBackupconfigMapper.delete(key);
        // 删除定时任务
        try {
            SaJobPojo saJobPojo = new SaJobPojo();
            saJobPojo.setId(key);
            saJobPojo.setJobgroup("UTS_BACKUP_GROUP");
            saJobPojo.setBackupmark(true);
            saJobService.deleteJob(saJobPojo);
            log.info("删除调度器定时任务");
        } catch (SchedulerException e) {
            throw new BaseBusinessException("删除定时任务失败");
        }
        return delete;
    }


    private static void cleanNull(UtsBackupconfigPojo utsBackupconfigPojo) {
        if (utsBackupconfigPojo.getConfigname() == null) utsBackupconfigPojo.setConfigname("");
        if (utsBackupconfigPojo.getLocalmark() == null) utsBackupconfigPojo.setLocalmark(0);
        if (utsBackupconfigPojo.getDbdriver() == null) utsBackupconfigPojo.setDbdriver("");
        if (utsBackupconfigPojo.getDburl() == null) utsBackupconfigPojo.setDburl("");
        if (utsBackupconfigPojo.getDbusername() == null) utsBackupconfigPojo.setDbusername("");
        if (utsBackupconfigPojo.getDbpassword() == null) utsBackupconfigPojo.setDbpassword("");
        if (utsBackupconfigPojo.getLocalpath() == null) utsBackupconfigPojo.setLocalpath("");
        if (utsBackupconfigPojo.getIncludetables() == null) utsBackupconfigPojo.setIncludetables("");
        if (utsBackupconfigPojo.getExcludetables() == null) utsBackupconfigPojo.setExcludetables("");
        if (utsBackupconfigPojo.getUploadprefix() == null) utsBackupconfigPojo.setUploadprefix("");
        if (utsBackupconfigPojo.getZippassword() == null) utsBackupconfigPojo.setZippassword("");
        if (utsBackupconfigPojo.getCronexpression() == null) utsBackupconfigPojo.setCronexpression("");
        if (utsBackupconfigPojo.getOsstype() == null) utsBackupconfigPojo.setOsstype("");
        if (utsBackupconfigPojo.getOssbucket() == null) utsBackupconfigPojo.setOssbucket("");
        if (utsBackupconfigPojo.getOssaccesskey() == null) utsBackupconfigPojo.setOssaccesskey("");
        if (utsBackupconfigPojo.getOsssecretkey() == null) utsBackupconfigPojo.setOsssecretkey("");
        if (utsBackupconfigPojo.getOssendpoint() == null) utsBackupconfigPojo.setOssendpoint("");
        if (utsBackupconfigPojo.getCloudurl() == null) utsBackupconfigPojo.setCloudurl("");
        if (utsBackupconfigPojo.getAuthcode() == null) utsBackupconfigPojo.setAuthcode("");
        if (utsBackupconfigPojo.getEmail() == null) utsBackupconfigPojo.setEmail("");
        if (utsBackupconfigPojo.getEnabledmark() == null) utsBackupconfigPojo.setEnabledmark(0);
        if (utsBackupconfigPojo.getRownum() == null) utsBackupconfigPojo.setRownum(0);
        if (utsBackupconfigPojo.getRemark() == null) utsBackupconfigPojo.setRemark("");
        if (utsBackupconfigPojo.getCreateby() == null) utsBackupconfigPojo.setCreateby("");
        if (utsBackupconfigPojo.getCreatebyid() == null) utsBackupconfigPojo.setCreatebyid("");
        if (utsBackupconfigPojo.getCreatedate() == null) utsBackupconfigPojo.setCreatedate(new Date());
        if (utsBackupconfigPojo.getLister() == null) utsBackupconfigPojo.setLister("");
        if (utsBackupconfigPojo.getListerid() == null) utsBackupconfigPojo.setListerid("");
        if (utsBackupconfigPojo.getModifydate() == null) utsBackupconfigPojo.setModifydate(new Date());
        if (utsBackupconfigPojo.getCustom1() == null) utsBackupconfigPojo.setCustom1("");
        if (utsBackupconfigPojo.getCustom2() == null) utsBackupconfigPojo.setCustom2("");
        if (utsBackupconfigPojo.getCustom3() == null) utsBackupconfigPojo.setCustom3("");
        if (utsBackupconfigPojo.getCustom4() == null) utsBackupconfigPojo.setCustom4("");
        if (utsBackupconfigPojo.getCustom5() == null) utsBackupconfigPojo.setCustom5("");
        if (utsBackupconfigPojo.getCustom6() == null) utsBackupconfigPojo.setCustom6("");
        if (utsBackupconfigPojo.getCustom7() == null) utsBackupconfigPojo.setCustom7("");
        if (utsBackupconfigPojo.getCustom8() == null) utsBackupconfigPojo.setCustom8("");
        if (utsBackupconfigPojo.getCustom9() == null) utsBackupconfigPojo.setCustom9("");
        if (utsBackupconfigPojo.getCustom10() == null) utsBackupconfigPojo.setCustom10("");
        if (utsBackupconfigPojo.getTenantid() == null) utsBackupconfigPojo.setTenantid("");
        if (utsBackupconfigPojo.getTenantname() == null) utsBackupconfigPojo.setTenantname("");
        if (utsBackupconfigPojo.getRevision() == null) utsBackupconfigPojo.setRevision(0);
    }

    @Resource
    private DB_BackupServer DBBackupServer;

    @Override
    public Object backup(String key) {
        List<UtsBackupconfigPojo> backConfigList = new ArrayList<>(); // 初始化列表

        if (StringUtils.isBlank(key)) {
            // 获取所有可用的备份配置
            backConfigList = utsBackupconfigMapper.getEnableList();
            if (backConfigList == null || backConfigList.isEmpty()) {
                throw new BaseBusinessException("没有可用的备份配置");
            }
        } else {
            // 获取指定的备份配置
            UtsBackupconfigPojo configDB = utsBackupconfigMapper.getEntity(key);
            if (configDB == null) {
                throw new BaseBusinessException("没有可用的备份配置");
            }
            backConfigList.add(configDB);
        }

        // 前端传了了key，说明backConfigList中只有一个元配置
        for (UtsBackupconfigPojo configPojo : backConfigList) {
            Integer localmark = configPojo.getLocalmark();//本机数据库备份
            String uploadprefix = configPojo.getUploadprefix();
            String zippassword = configPojo.getZippassword();
            String cronexpression = configPojo.getCronexpression();//定时任务表达式
            String localpath = configPojo.getLocalpath();
            String email = configPojo.getEmail();
            String configname = configPojo.getConfigname();// 备份名称
            //minio/aliyun
            String osstype = configPojo.getOsstype();
            String ossbucket = configPojo.getOssbucket();
            String ossaccesskey = configPojo.getOssaccesskey();
            String osssecretkey = configPojo.getOsssecretkey();
            String ossendpoint = configPojo.getOssendpoint();
            // 不走minio直接走上传接口：http://sautsapi.dm.inksyun.com/File/upload
            String cloudurl = configPojo.getCloudurl();
            String authcode = configPojo.getAuthcode();
            if (localmark == 1) {
                //本机数据库备份
                return DBBackupServer.mysqlBackup(uploadprefix, zippassword,
                        email, osstype, ossbucket, ossaccesskey, osssecretkey, ossendpoint, cloudurl, authcode, localpath, configname);

            }
            // 直连minio/aliyun备份  或者  上传到指定api接口 如：http://sautsapi.dm.inksyun.com/File/upload
            else {
                //远程数据库备份
                //String dbtype = configPojo.getDbtype();必定mysql
                String dburl = configPojo.getDburl();
                String dbusername = configPojo.getDbusername();
                String dbpassword = configPojo.getDbpassword();
                String dbdriver = configPojo.getDbdriver();
                String includetables = configPojo.getIncludetables();
                String excludetables = configPojo.getExcludetables();
                return DBBackupServer.mysqlBackupByUrl(dburl, dbusername, dbpassword, dbdriver, includetables, excludetables, uploadprefix, zippassword,
                        email, osstype, ossbucket, ossaccesskey, osssecretkey, ossendpoint, cloudurl, authcode, localpath, configname);
            }
        }
        return null;
    }

    @Resource
    private UtsDatabaseService utsDatabaseService;

    @Override
    public List<TableInfoDTO> getTablesInfo(String key) throws Exception { // 标记方法可能抛出异常
        if (key == null || key.trim().isEmpty()) {
            throw new BaseBusinessException("配置Key不能为空");
        }
        UtsBackupconfigPojo backupconfigPojo = utsBackupconfigMapper.getEntity(key);
        if (backupconfigPojo == null) {
            throw new BaseBusinessException("未找到Key为 '" + key + "' 的备份配置");
        }
        String dbUrl = backupconfigPojo.getDburl();
        String dbUsername = backupconfigPojo.getDbusername();
        String dbPassword = backupconfigPojo.getDbpassword();
        String dbDriver = backupconfigPojo.getDbdriver(); // 可能为 null
        List<TableInfoDTO> tableInfos = new ArrayList<>();
        String sql = null;
        boolean isMySql = false;
        boolean isSqlServer = false;
        DataSource targetDataSource = null;
        String databaseType = null;
        try {
            // 获取 DataSource 和 数据库类型
            // 注意: getDataSource 的 delete 参数，这里设为 false，避免影响其他操作
            // 如果确实需要强制刷新连接池，可以考虑传入 true，但需谨慎
            Map<String, Object> dataSourceMap = utsDatabaseService.getDataSource(
                    null, // databaseid 为 null，因为我们提供了详细连接信息
                    false, // delete 设为 false
                    null, // tid (租户ID?) 如果需要，从 backupconfigPojo 获取
                    dbUrl, dbUsername, dbPassword, dbDriver);
            targetDataSource = (DataSource) dataSourceMap.get("dataSource");
            databaseType = (String) dataSourceMap.get("databaseType"); // "mysql" or "sqlserver"
            if (targetDataSource == null || databaseType == null) {
                throw new BaseBusinessException("无法获取数据库连接或类型信息");
            }
            // 根据数据库类型选择 SQL
            if (MyConstant.MYSQL.equalsIgnoreCase(databaseType)) {
                // ... (MySQL SQL remains the same) ...
                isMySql = true;
                sql = "SELECT " +
                        "    TABLE_NAME AS TableName, " +
                        "    TABLE_COMMENT AS TableComment, " +
                        "    TABLE_ROWS AS TotalRows " +
                        "FROM " +
                        "    INFORMATION_SCHEMA.TABLES " +
                        "WHERE " +
                        "    TABLE_SCHEMA = ? " +
                        "    AND TABLE_TYPE = 'BASE TABLE' " +
                        "ORDER BY " +
                        "    TABLE_ROWS DESC";
            } else if (MyConstant.SQLSERVER.equalsIgnoreCase(databaseType)) {
                isSqlServer = true;
                // --- 修改 SQL Server 查询：保留 TotalRows 别名 ---
                sql = "SELECT " +
                        "    t.name AS TableName, " +                                         // Column 1
                        "    ISNULL(CAST(ep.value AS VARCHAR(500)), '') AS TableComment, " +  // Column 2
                        "    SUM(p.rows) AS TotalRows " +                                     // Column 3，加上别名
                        "FROM " +
                        "    sys.tables t " +
                        "INNER JOIN " +
                        "    sys.schemas s ON t.schema_id = s.schema_id " +
                        "INNER JOIN " +
                        "    sys.partitions p ON t.object_id = p.object_id AND p.index_id IN (0, 1) " +
                        "LEFT JOIN " +
                        "    sys.extended_properties ep ON t.object_id = ep.major_id AND ep.minor_id = 0 AND ep.name = 'MS_Description' " +
                        "WHERE " +
                        "    t.is_ms_shipped = 0 " +
                        "GROUP BY " +
                        "    s.name, t.name, t.object_id, CAST(ep.value AS VARCHAR(500)) " +
                        "ORDER BY " +
                        "    SUM(p.rows) DESC"; // 排序仍然使用 SUM(p.rows)
                // --- SQL 修改结束 ---
            } else {
                throw new UnsupportedOperationException("不支持的数据库类型: " + databaseType);
            }
            // 使用 try-with-resources 执行查询
            try (Connection connection = targetDataSource.getConnection()) {
                String actualCatalog = connection.getCatalog();
                log.info("Querying tables info for database: {}, type: {}", actualCatalog, databaseType);
                if (isMySql) {
                    log.debug("Executing MySQL query: {}", sql); // Log MySQL query if needed
                    try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
                        pstmt.setString(1, actualCatalog);
                        try (ResultSet rs = pstmt.executeQuery()) {
                            while (rs.next()) {
                                String tableName = rs.getString("TableName");
                                String tableComment = rs.getString("TableComment");
                                // MySQL TABLE_ROWS 可能为 NULL，给个默认值0
                                Long rowCount = rs.getObject("TotalRows") != null ? rs.getLong("TotalRows") : 0L;
                                tableInfos.add(new TableInfoDTO(tableName, tableComment, rowCount));
                            }
                        }
                    }
                } else if (isSqlServer) {
                    // --- 添加日志记录最终的 SQL Server 查询语句 ---
                    log.info("Executing SQL Server query:\n{}", sql);
                    // --- 日志添加结束 ---
                    try (Statement stmt = connection.createStatement(); // SQL Server 查询无参数，用 Statement
                         ResultSet rs = stmt.executeQuery(sql)) {
                        while (rs.next()) {
                            // ... 处理结果 ...
                            String tableName = rs.getString("TableName");
                            String tableComment = rs.getString("TableComment");
                            Long rowCount = rs.getLong("TotalRows"); // <--- 错误可能还在这里
                            tableInfos.add(new TableInfoDTO(tableName, tableComment, rowCount));
                        }
                    }
                }
            } // Connection 会在此自动关闭
        } catch (SQLException e) {
            // 修改日志，包含 SQL (如果已生成)
            log.error("查询数据库表信息时发生 SQL 错误 for Key [{}], URL [{}]. SQL: [{}]. Error: {}",
                    key, dbUrl, sql, e.getMessage(), e);
            throw new Exception("查询数据库表信息失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("获取数据源或查询表信息时发生错误 for Key [{}], URL [{}]: {}", key, dbUrl, e.getMessage(), e);
            if (!(e instanceof BaseBusinessException)) {
                throw new Exception("处理表信息查询时出错: " + e.getMessage(), e);
            } else {
                throw e;
            }
        }
        return tableInfos;
    }

    //restore(url,username,password,driverclassname,sqlContent);
    @Override
    public String restore(LoginUser loginUser, String url, String username, String password, String driverclassname, String sqlContent, String toEmail) {
        // 获取指定的备份配置
        return DBBackupServer.restoreBySql(loginUser, url, username, password, driverclassname, sqlContent, toEmail);
    }
}
