package inks.service.sa.uts.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.uts.domain.pojo.SaJoblogPojo;
import inks.service.sa.uts.domain.SaJoblogEntity;
import inks.service.sa.uts.mapper.SaJoblogMapper;
import inks.service.sa.uts.service.SaJoblogService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/**
 * 定时任务调度日志表(SaJoblog)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-07 16:07:35
 */
@Service("saJoblogService")
public class SaJoblogServiceImpl implements SaJoblogService {
    @Resource
    private SaJoblogMapper saJoblogMapper;

    @Override
    public SaJoblogPojo getEntity(String key) {
        return this.saJoblogMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaJoblogPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaJoblogPojo> lst = saJoblogMapper.getPageList(queryParam);
            PageInfo<SaJoblogPojo> pageInfo = new PageInfo<SaJoblogPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public SaJoblogPojo insert(SaJoblogPojo saJoblogPojo) {
        //初始化NULL字段
        cleanNull(saJoblogPojo);
        SaJoblogEntity saJoblogEntity = new SaJoblogEntity(); 
        BeanUtils.copyProperties(saJoblogPojo,saJoblogEntity);
        //生成雪花id
          saJoblogEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          this.saJoblogMapper.insert(saJoblogEntity);
        return this.getEntity(saJoblogEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param saJoblogPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaJoblogPojo update(SaJoblogPojo saJoblogPojo) {
        SaJoblogEntity saJoblogEntity = new SaJoblogEntity(); 
        BeanUtils.copyProperties(saJoblogPojo,saJoblogEntity);
        this.saJoblogMapper.update(saJoblogEntity);
        return this.getEntity(saJoblogEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saJoblogMapper.delete(key) ;
    }




    private static void cleanNull(SaJoblogPojo saJoblogPojo) {
        if(saJoblogPojo.getJobname()==null) saJoblogPojo.setJobname("");
        if(saJoblogPojo.getJobgroup()==null) saJoblogPojo.setJobgroup("");
        if(saJoblogPojo.getInvoketarget()==null) saJoblogPojo.setInvoketarget("");
        if(saJoblogPojo.getJobmessage()==null) saJoblogPojo.setJobmessage("");
        if(saJoblogPojo.getStatus()==null) saJoblogPojo.setStatus("");
        if(saJoblogPojo.getExceptioninfo()==null) saJoblogPojo.setExceptioninfo("");
        if(saJoblogPojo.getCreatetime()==null) saJoblogPojo.setCreatetime(new Date());
   }

    @Override
    public int deleteJobLogByIds(String[] jobLogIds) {
        return this.saJoblogMapper.deleteJobLogByIds(jobLogIds);
    }

    @Override
    public int cleanJobLog() {
        return this.saJoblogMapper.cleanJobLog();
    }
}
