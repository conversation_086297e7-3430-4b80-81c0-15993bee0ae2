package inks.service.sa.uts.service.impl;

import com.zaxxer.hikari.HikariDataSource;
import com.zaxxer.hikari.HikariPoolMXBean;
import inks.common.core.domain.LoginUser;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.uts.service.JdbcService;
import inks.service.sa.uts.service.UtsDatabaseService;
import inks.service.sa.uts.service.sql.ConnectionWrapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

/**
 * Created by raodeming on 2021/8/6.
 */
@Service
public class JdbcServiceImpl implements JdbcService {
    private static final Logger log = LoggerFactory.getLogger(JdbcServiceImpl.class);

    private final Map<String, HikariDataSource> dataSourceCache = new ConcurrentHashMap<>();
    private final Object cacheLock = new Object();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    @Resource
    private UtsDatabaseService utsDatabaseService;
    @Resource
    private SaRedisService saRedisService;

    //public JdbcServiceImpl() {
    //    // 启动定期清理任务
    //    scheduler.scheduleAtFixedRate(this::cleanupIdleDataSources, 30, 30, TimeUnit.MINUTES);
    //}

    // 获取数据源（带缓存）
    public HikariDataSource getJdbcDataSource(String databaseId) {
        HikariDataSource ds = dataSourceCache.get(databaseId);
        if (ds != null && !ds.isClosed()) {
            log.info("从缓存获取有效HikariDataSource，ID: {}", databaseId);
            return ds;
        }

        synchronized (cacheLock) {
            ds = dataSourceCache.get(databaseId);
            if (ds == null || ds.isClosed()) {
                LoginUser user = saRedisService.getLoginUser();
                String tid = user.getTenantid();
                try {
                    Map<String, Object> config = utsDatabaseService.getDataSource(databaseId, tid);
                    HikariDataSource templateDs = (HikariDataSource) config.get("dataSource");
                    log.info("获取到数据源配置：{}", config); // 确保配置正确

                    // Create a new DataSource from the template's properties
                    ds = new HikariDataSource();
                    ds.setJdbcUrl(templateDs.getJdbcUrl());
                    ds.setUsername(templateDs.getUsername());
                    ds.setPassword(templateDs.getPassword());
                    ds.setDriverClassName(templateDs.getDriverClassName());

                    configureHikariPool(ds, databaseId);
                    dataSourceCache.put(databaseId, ds);
                    log.info("新建HikariDataSource并缓存，ID: {}", databaseId);
                } catch (Exception e) {
                    log.error("创建数据源[{}]失败", databaseId, e);
                    throw new RuntimeException("创建数据源失败", e);
                }
            }
            return ds;
        }
    }

    // 配置 HikariCP 连接池参数
    private void configureHikariPool(HikariDataSource ds, String databaseId) {
        // 设置连接池名称（用于监控和日志识别）
        // 格式示例：HikariPool-master_db
        ds.setPoolName("HikariPool-" + databaseId);

        // 最大连接数（默认10）
        // 建议值 = (核心数 * 2) + 有效磁盘数（SSD可适当增加）
        ds.setMaximumPoolSize(30);

        // 连接获取超时时间（毫秒）
        // 当所有连接都在使用时，新请求等待获取连接的最长时间
        // 超过该时间会抛出SQLException
        ds.setConnectionTimeout(30000); // 30秒

        // 连接空闲超时时间（毫秒）
        // 连接在池中保持空闲状态的最长时间，超时后会被释放
        // 必须小于maxLifetime
        ds.setIdleTimeout(600000); // 600秒（默认30秒）

        // 连接最大存活时间（毫秒）
        // 连接创建后存活的最长时间，超时后会被释放
        // 建议比数据库的wait_timeout小2-3分钟
        ds.setMaxLifetime(1800000); // 30分钟（默认30分钟）

        // 连接泄漏检测阈值（毫秒）
        // 当连接从池中取出超过该时间未归还时，会记录警告日志
        // 生产环境建议设置（5000-10000ms）
        ds.setLeakDetectionThreshold(5000); // 5秒
    }


    // 获取连接（返回包装后的 Connection）
    @Override
    public Connection getPooledConnection(String databaseId) throws SQLException {
        try {
            HikariDataSource ds = getJdbcDataSource(databaseId);
            Connection conn = ds.getConnection();
            logConnectionState(ds);
            // 使用动态代理包装 Connection
            return ConnectionWrapper.wrap(conn);
        } catch (SQLException e) {
            log.error("获取数据库连接失败，ID: {}", databaseId, e);
            throw e;
        }
    }

    // 记录连接池状态（调试用）
    private void logConnectionState(HikariDataSource ds) {
        HikariPoolMXBean pool = ds.getHikariPoolMXBean();
        if (pool != null) {
            log.debug("连接池[{}] 状态：活跃={}, 空闲={}, 等待线程={}",
                    ds.getPoolName(),
                    pool.getActiveConnections(),
                    pool.getIdleConnections(),
                    pool.getThreadsAwaitingConnection());
        }
    }

    //// 清理长时间不用的数据源
    //private void cleanupIdleDataSources() {
    //    log.info("开始清理闲置数据源...");
    //    synchronized (cacheLock) {
    //        Iterator<Map.Entry<String, HikariDataSource>> iterator = dataSourceCache.entrySet().iterator();
    //        while (iterator.hasNext()) {
    //            Map.Entry<String, HikariDataSource> entry = iterator.next();
    //            HikariDataSource ds = entry.getValue();
    //            try {
    //                HikariPoolMXBean pool = ds.getHikariPoolMXBean();
    //                if (pool != null && pool.getActiveConnections() == 0) {
    //                    log.info("关闭闲置数据源: {}", entry.getKey());
    //                    ds.close();
    //                    iterator.remove();
    //                }
    //            } catch (Exception e) {
    //                log.error("关闭数据源[{}]失败", entry.getKey(), e);
    //            }
    //        }
    //    }
    //}


    // SQL安全验证
    public static void validateSqlSelect(String sql) {
        //SQL转大写
        sql = sql.toUpperCase();
        // 定义禁止的 SQL 关键字
        String[] forbiddenKeywords = {"INSERT", "UPDATE", "DELETE", "MERGE", "REPLACE", "TRUNCATE"};
        // 检查禁止的增删改关键字
        for (String keyword : forbiddenKeywords) {
            if (sql.matches(".*\\b" + keyword + "\\b.*")) {
                throw new IllegalArgumentException("禁止执行增删改操作：检测到关键字 " + keyword);
            }
        }
        // 必须有Select关键字
        if (!sql.contains("SELECT")) {
            throw new IllegalArgumentException("必须包含SELECT关键字！");
        }
    }
}
