
// SqlExecutionResult.java
package inks.service.sa.uts.service.sql;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("SQL执行结果")
public class SqlExecutionResult {
    @ApiModelProperty("执行结果数据")
    private Object data;
    
    @ApiModelProperty("SQL类型")
    private String type;
    
    @ApiModelProperty("影响行数")
    private Integer affectedRows;
    
    @ApiModelProperty("总记录数")
    private Integer totalRows;
    
    @ApiModelProperty("执行时间(毫秒)")
    private Long executionTime;
    
    @ApiModelProperty("是否成功")
    private boolean success;
    
    @ApiModelProperty("错误信息")
    private String errorMessage;
    
    @ApiModelProperty("错误码")
    private Integer errorCode;
    
    @ApiModelProperty("SQL状态")
    private String sqlState;

    //當前時間
    private String currentTime;
    //初始化当前时间 YYYY-MM-DD HH:mm:ss
    public SqlExecutionResult() {
        this.currentTime = new Date().toString();
    }
}

