package inks.service.sa.uts.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.UtsVideolibraryPojo;
import inks.service.sa.uts.domain.UtsVideolibraryEntity;

import com.github.pagehelper.PageInfo;

/**
 * 视频信息表(UtsVideolibrary)表服务接口
 *
 * <AUTHOR>
 * @since 2025-05-29 14:16:34
 */
public interface UtsVideolibraryService {


    UtsVideolibraryPojo getEntity(String key);

    PageInfo<UtsVideolibraryPojo> getPageList(QueryParam queryParam);

    UtsVideolibraryPojo insert(UtsVideolibraryPojo utsVideolibraryPojo);

    UtsVideolibraryPojo update(UtsVideolibraryPojo utsVideolibrarypojo);

    int delete(String key);

     UtsVideolibraryPojo approval(UtsVideolibraryPojo utsVideolibraryPojo);
}
