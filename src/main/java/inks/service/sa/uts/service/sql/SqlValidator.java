package inks.service.sa.uts.service.sql;

import inks.service.sa.uts.service.sql.SqlExecutionException;
import org.springframework.stereotype.Component;
import org.apache.commons.lang3.StringUtils;
import java.util.*;
import java.util.regex.Pattern;

@Component
public class SqlValidator {

    private static final Set<String> FORBIDDEN_KEYWORDS = Collections.unmodifiableSet(
            //new HashSet<>(Arrays.asList("DROP", "TRUNCATE", "ALTER", "CREATE", "RENAME", "EXEC", "EXECUTE"))
            new HashSet<>(Arrays.asList(
                    //"DROP",      // 删除数据库、表、视图等
                    //"DELETE",    // 删除表中的数据
                    //"TRUNCATE"   // 清空表中的数据
            ))
    );

    private static final Set<String> CRUD_KEYWORDS = Collections.unmodifiableSet(
            //new HashSet<>(Arrays.asList("INSERT", "UPDATE", "DELETE", "SELECT"))
            new HashSet<>(Arrays.asList( "UPDATE", "DELETE", "SELECT"))
    );

    private static final Pattern SQL_INJECTION_PATTERN = Pattern.compile(
            //"('.+--)|(--)|(;)|(/)|(\\*)|(\\\\)|(@)|(#)",
            "('.+--)|(--)|(;)|(\\\\)|(@)|(#)",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern WHERE_CLAUSE_PATTERN = Pattern.compile(
            "\\s+WHERE\\s+",
            Pattern.CASE_INSENSITIVE
    );

    public void validate(String sql) throws SqlExecutionException {
        if (StringUtils.isBlank(sql)) {
            throw new SqlExecutionException("SQL语句不能为空");
        }

        if (sql.length() > 5000) {
            throw new SqlExecutionException("SQL语句过长");
        }

        String upperSql = sql.toUpperCase().trim();

        // 检查SQL语句是否包含禁止使用的关键词
        checkForbiddenKeywords(upperSql);
        // 检查SQL语句是否包含SQL注入
        checkSqlInjection(sql);
        // 检查SQL语句是否包含CRUD操作 需要加where条件
        checkCrudOperations(upperSql);
        // 检查SQL语句是否使用了表别名
        checkTableAliases(upperSql);
        // 检查SQL语句是否使用了JOIN条件
        checkJoinConditions(upperSql);
    }

    private void checkForbiddenKeywords(String upperSql) throws SqlExecutionException {
        for (String keyword : FORBIDDEN_KEYWORDS) {
            if (upperSql.contains(keyword)) {
                throw new SqlExecutionException("SQL包含禁止使用的关键词: " + keyword);
            }
        }
    }

    private void checkSqlInjection(String sql) throws SqlExecutionException {
        if (SQL_INJECTION_PATTERN.matcher(sql).find()) {
            throw new SqlExecutionException("检测到潜在的SQL注入风险");
        }
    }

    private void checkCrudOperations(String upperSql) throws SqlExecutionException {
        for (String keyword : CRUD_KEYWORDS) {
            if (upperSql.startsWith(keyword)) {
                if (!WHERE_CLAUSE_PATTERN.matcher(upperSql).find()) {
                    throw new SqlExecutionException(keyword + " 操作必须包含 WHERE 条件");
                }
                break;
            }
        }
    }

    private void checkTableAliases(String upperSql) throws SqlExecutionException {
        if (upperSql.contains(" AS ")) {
            Pattern aliasPattern = Pattern.compile("\\s+AS\\s+([A-Za-z0-9_]+)");
            if (!aliasPattern.matcher(upperSql).find()) {
                throw new SqlExecutionException("使用AS时必须提供有效的别名");
            }
        }
    }

    private void checkJoinConditions(String upperSql) throws SqlExecutionException {
        if (upperSql.contains("JOIN")) {
            Pattern joinPattern = Pattern.compile("JOIN\\s+.*?\\s+ON\\s+");
            if (!joinPattern.matcher(upperSql).find()) {
                throw new SqlExecutionException("JOIN操作必须包含ON条件");
            }
        }
    }
}