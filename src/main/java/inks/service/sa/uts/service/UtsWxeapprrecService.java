package inks.service.sa.uts.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.UtsWxeapprrecPojo;
import inks.service.sa.uts.domain.UtsWxeapprrecEntity;

import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 微信审批记录(UtsWxeapprrec)表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-16 14:12:14
 */
public interface UtsWxeapprrecService {


    UtsWxeapprrecPojo getEntity(String key);

    PageInfo<UtsWxeapprrecPojo> getPageList(QueryParam queryParam);

    UtsWxeapprrecPojo insert(UtsWxeapprrecPojo utsWxeapprrecPojo);

    UtsWxeapprrecPojo update(UtsWxeapprrecPojo utsWxeapprrecpojo);

    int delete(String key);

    UtsWxeapprrecPojo getEntityBySpno(String s, String tid);
    /**
     * 通过Billid查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    List<UtsWxeapprrecPojo> getOnlineByBillid(String key, String tid);
}
