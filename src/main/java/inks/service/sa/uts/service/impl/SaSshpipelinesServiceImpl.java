package inks.service.sa.uts.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.uts.domain.pojo.SaSshpipelinesPojo;
import inks.service.sa.uts.domain.pojo.SaSshpipelinesitemPojo;
import inks.service.sa.uts.domain.pojo.SaSshpipelinesitemdetailPojo;
import inks.service.sa.uts.domain.SaSshpipelinesEntity;
import inks.service.sa.uts.domain.SaSshpipelinesitemEntity;
import inks.service.sa.uts.mapper.SaSshpipelinesMapper;
import inks.service.sa.uts.service.SaSshpipelinesService;
import inks.service.sa.uts.service.SaSshpipelinesitemService;
import inks.service.sa.uts.mapper.SaSshpipelinesitemMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import inks.common.core.text.inksSnowflake;
/**
 * SSH流水线(SaSshpipelines)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-19 11:11:27
 */
@Service("saSshpipelinesService")
public class SaSshpipelinesServiceImpl implements SaSshpipelinesService {
    @Resource
    private SaSshpipelinesMapper saSshpipelinesMapper;
    
    @Resource
    private SaSshpipelinesitemMapper saSshpipelinesitemMapper;
    

    @Resource
    private SaSshpipelinesitemService saSshpipelinesitemService;
    

    @Override
    public SaSshpipelinesPojo getEntity(String key) {
        return this.saSshpipelinesMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaSshpipelinesitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaSshpipelinesitemdetailPojo> lst = saSshpipelinesMapper.getPageList(queryParam);
            PageInfo<SaSshpipelinesitemdetailPojo> pageInfo = new PageInfo<SaSshpipelinesitemdetailPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    

    @Override
    public SaSshpipelinesPojo getBillEntity(String key) {
       try {
        //读取主表
        SaSshpipelinesPojo saSshpipelinesPojo = this.saSshpipelinesMapper.getEntity(key);
        //读取子表
        saSshpipelinesPojo.setItem(saSshpipelinesitemMapper.getList(saSshpipelinesPojo.getId()));
        return saSshpipelinesPojo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public PageInfo<SaSshpipelinesPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaSshpipelinesPojo> lst = saSshpipelinesMapper.getPageTh(queryParam);
             //循环设置每个主表对象的item子表
            for(int i=0;i<lst.size();i++){
                lst.get(i).setItem(saSshpipelinesitemMapper.getList(lst.get(i).getId()));
            }
            PageInfo<SaSshpipelinesPojo> pageInfo = new PageInfo<SaSshpipelinesPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    

    @Override
    public PageInfo<SaSshpipelinesPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaSshpipelinesPojo> lst = saSshpipelinesMapper.getPageTh(queryParam);
            PageInfo<SaSshpipelinesPojo> pageInfo = new PageInfo<SaSshpipelinesPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    @Transactional
    public SaSshpipelinesPojo insert(SaSshpipelinesPojo saSshpipelinesPojo) {
        //初始化NULL字段
        cleanNull(saSshpipelinesPojo);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        SaSshpipelinesEntity saSshpipelinesEntity = new SaSshpipelinesEntity(); 
        BeanUtils.copyProperties(saSshpipelinesPojo,saSshpipelinesEntity);
        //设置id和新建日期
        saSshpipelinesEntity.setId(id);
        saSshpipelinesEntity.setRevision(1);  //乐观锁
        //插入主表
        this.saSshpipelinesMapper.insert(saSshpipelinesEntity);
        //Item子表处理
        List<SaSshpipelinesitemPojo> lst = saSshpipelinesPojo.getItem();
        if (lst != null){
            //循环每个item子表
            for(int i=0;i<lst.size();i++){
               //初始化item的NULL
               SaSshpipelinesitemPojo itemPojo =this.saSshpipelinesitemService.clearNull(lst.get(i));
               SaSshpipelinesitemEntity saSshpipelinesitemEntity = new SaSshpipelinesitemEntity(); 
               BeanUtils.copyProperties(itemPojo,saSshpipelinesitemEntity);
               //设置id和Pid
               saSshpipelinesitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
               saSshpipelinesitemEntity.setPid(id);
               saSshpipelinesitemEntity.setRevision(1);  //乐观锁
               //插入子表
               this.saSshpipelinesitemMapper.insert(saSshpipelinesitemEntity);
            }
        } 
        //返回Bill实例
        return this.getBillEntity(saSshpipelinesEntity.getId());
    }


    @Override
    @Transactional
    public SaSshpipelinesPojo update(SaSshpipelinesPojo saSshpipelinesPojo) {
        //主表更改
        SaSshpipelinesEntity saSshpipelinesEntity = new SaSshpipelinesEntity(); 
        BeanUtils.copyProperties(saSshpipelinesPojo,saSshpipelinesEntity);
        this.saSshpipelinesMapper.update(saSshpipelinesEntity);
        if (saSshpipelinesPojo.getItem() != null) {
        //Item子表处理
        List<SaSshpipelinesitemPojo> lst = saSshpipelinesPojo.getItem();
        //获取被删除的Item
         List<String> lstDelIds =saSshpipelinesMapper.getDelItemIds(saSshpipelinesPojo);
        if (lstDelIds != null){
            //循环每个删除item子表
            for(int i=0;i<lstDelIds.size();i++){
             this.saSshpipelinesitemMapper.delete(lstDelIds.get(i));
            }
        }
        if (lst != null){
            //循环每个item子表
            for(int i=0;i<lst.size();i++){
               SaSshpipelinesitemEntity saSshpipelinesitemEntity = new SaSshpipelinesitemEntity(); 
               if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null){
                //初始化item的NULL
               SaSshpipelinesitemPojo itemPojo =this.saSshpipelinesitemService.clearNull(lst.get(i));
               BeanUtils.copyProperties(itemPojo,saSshpipelinesitemEntity);
               //设置id和Pid
               saSshpipelinesitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
               saSshpipelinesitemEntity.setPid(saSshpipelinesEntity.getId());  // 主表 id
               saSshpipelinesitemEntity.setRevision(1);  // 乐观锁
               //插入子表
               this.saSshpipelinesitemMapper.insert(saSshpipelinesitemEntity);
               }
               else
               {
               BeanUtils.copyProperties(lst.get(i),saSshpipelinesitemEntity);             
               this.saSshpipelinesitemMapper.update(saSshpipelinesitemEntity);
               }
            }
        } 
        }
        //返回Bill实例
        return this.getBillEntity(saSshpipelinesEntity.getId());
    }


    @Override
    @Transactional
    public int delete(String key) {
       SaSshpipelinesPojo saSshpipelinesPojo =  this.getBillEntity(key);
        if (Objects.equals(saSshpipelinesPojo.getIssystem(), 1)) {
            throw new BaseBusinessException("系统数据禁止删除！");
        }
        //Item子表处理
        List<SaSshpipelinesitemPojo> lst = saSshpipelinesPojo.getItem();
        if (lst != null){
            //循环每个删除item子表
            for (SaSshpipelinesitemPojo saSshpipelinesitemPojo : lst) {
                this.saSshpipelinesitemMapper.delete(saSshpipelinesitemPojo.getId());
            }
        }        
        return this.saSshpipelinesMapper.delete(key) ;
    }
    

    

    private static void cleanNull(SaSshpipelinesPojo saSshpipelinesPojo) {
        if(saSshpipelinesPojo.getPipelinename()==null) saSshpipelinesPojo.setPipelinename("");
        if(saSshpipelinesPojo.getDescription()==null) saSshpipelinesPojo.setDescription("");
        if(saSshpipelinesPojo.getCategory()==null) saSshpipelinesPojo.setCategory("");
        if(saSshpipelinesPojo.getIssystem()==null) saSshpipelinesPojo.setIssystem(0);
        if(saSshpipelinesPojo.getRownum()==null) saSshpipelinesPojo.setRownum(0);
        if(saSshpipelinesPojo.getRemark()==null) saSshpipelinesPojo.setRemark("");
        if(saSshpipelinesPojo.getCreateby()==null) saSshpipelinesPojo.setCreateby("");
        if(saSshpipelinesPojo.getCreatebyid()==null) saSshpipelinesPojo.setCreatebyid("");
        if(saSshpipelinesPojo.getCreatedate()==null) saSshpipelinesPojo.setCreatedate(new Date());
        if(saSshpipelinesPojo.getLister()==null) saSshpipelinesPojo.setLister("");
        if(saSshpipelinesPojo.getListerid()==null) saSshpipelinesPojo.setListerid("");
        if(saSshpipelinesPojo.getModifydate()==null) saSshpipelinesPojo.setModifydate(new Date());
        if(saSshpipelinesPojo.getCustom1()==null) saSshpipelinesPojo.setCustom1("");
        if(saSshpipelinesPojo.getCustom2()==null) saSshpipelinesPojo.setCustom2("");
        if(saSshpipelinesPojo.getCustom3()==null) saSshpipelinesPojo.setCustom3("");
        if(saSshpipelinesPojo.getCustom4()==null) saSshpipelinesPojo.setCustom4("");
        if(saSshpipelinesPojo.getCustom5()==null) saSshpipelinesPojo.setCustom5("");
        if(saSshpipelinesPojo.getTenantid()==null) saSshpipelinesPojo.setTenantid("");
        if(saSshpipelinesPojo.getRevision()==null) saSshpipelinesPojo.setRevision(0);
   }

}
