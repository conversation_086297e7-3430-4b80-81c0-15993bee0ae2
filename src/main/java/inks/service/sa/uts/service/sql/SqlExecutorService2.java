package inks.service.sa.uts.service.sql;

import java.util.List;
import java.util.concurrent.CompletableFuture;

public interface SqlExecutorService2 {
    
    /**
     * 执行单个SQL请求
     * @param request SQL执行请求
     * @return SQL执行结果
     */
    SqlExecutionResult executeSql(SqlExecuteRequest2 request);
    
    /**
     * 批量执行SQL请求
     * @param requests SQL执行请求列表
     * @return SQL执行结果列表
     */
    List<SqlExecutionResult> executeBatchSql(List<SqlExecuteRequest2> requests);
    
    /**
     * 异步执行SQL请求
     * @param request SQL执行请求
     * @return 异步的SQL执行结果
     */
    CompletableFuture<SqlExecutionResult> executeAsyncSql(SqlExecuteRequest2 request);
    
    /**
     * 添加自定义数据源
     * @param key 数据源键值
     * @param url 数据库URL
     * @param username 用户名
     * @param password 密码
     */
    void addCustomDataSource(String key, String url, String username, String password);
}