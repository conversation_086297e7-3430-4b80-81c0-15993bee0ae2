package inks.service.sa.uts.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.uts.domain.pojo.UtsWxeapprrecPojo;
import inks.service.sa.uts.domain.UtsWxeapprrecEntity;
import inks.service.sa.uts.mapper.U_UtsWxeapprrecMapper;
import inks.service.sa.uts.service.UtsWxeapprrecService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;

import java.util.Date;
import java.util.List;
/**
 * 微信审批记录(UtsWxeapprrec)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-16 14:12:14
 */
@Service("utsWxeapprrecService")
public class UtsWxeapprrecServiceImpl implements UtsWxeapprrecService {
    @Resource
    private U_UtsWxeapprrecMapper UUtsWxeapprrecMapper;

    @Override
    public UtsWxeapprrecPojo getEntity(String key) {
        return this.UUtsWxeapprrecMapper.getEntity(key);
    }


    @Override
    public PageInfo<UtsWxeapprrecPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<UtsWxeapprrecPojo> lst = UUtsWxeapprrecMapper.getPageList(queryParam);
            PageInfo<UtsWxeapprrecPojo> pageInfo = new PageInfo<UtsWxeapprrecPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public UtsWxeapprrecPojo insert(UtsWxeapprrecPojo utsWxeapprrecPojo) {
        //初始化NULL字段
        cleanNull(utsWxeapprrecPojo);
        UtsWxeapprrecEntity utsWxeapprrecEntity = new UtsWxeapprrecEntity(); 
        BeanUtils.copyProperties(utsWxeapprrecPojo,utsWxeapprrecEntity);
        //生成雪花id
          utsWxeapprrecEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          utsWxeapprrecEntity.setRevision(1);  //乐观锁
          this.UUtsWxeapprrecMapper.insert(utsWxeapprrecEntity);
        return this.getEntity(utsWxeapprrecEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param utsWxeapprrecPojo 实例对象
     * @return 实例对象
     */
    @Override
    public UtsWxeapprrecPojo update(UtsWxeapprrecPojo utsWxeapprrecPojo) {
        UtsWxeapprrecEntity utsWxeapprrecEntity = new UtsWxeapprrecEntity(); 
        BeanUtils.copyProperties(utsWxeapprrecPojo,utsWxeapprrecEntity);
        this.UUtsWxeapprrecMapper.update(utsWxeapprrecEntity);
        return this.getEntity(utsWxeapprrecEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.UUtsWxeapprrecMapper.delete(key) ;
    }
    

    private static void cleanNull(UtsWxeapprrecPojo utsWxeapprrecPojo) {
        if(utsWxeapprrecPojo.getModulecode()==null) utsWxeapprrecPojo.setModulecode("");
        if(utsWxeapprrecPojo.getTemplateid()==null) utsWxeapprrecPojo.setTemplateid("");
        if(utsWxeapprrecPojo.getApprname()==null) utsWxeapprrecPojo.setApprname("");
        if(utsWxeapprrecPojo.getDatatemp()==null) utsWxeapprrecPojo.setDatatemp("");
        if(utsWxeapprrecPojo.getCallbackurl()==null) utsWxeapprrecPojo.setCallbackurl("");
        if(utsWxeapprrecPojo.getCallbackbean()==null) utsWxeapprrecPojo.setCallbackbean("");
        if(utsWxeapprrecPojo.getResultcode()==null) utsWxeapprrecPojo.setResultcode("");
        if(utsWxeapprrecPojo.getApprtype()==null) utsWxeapprrecPojo.setApprtype("");
        if(utsWxeapprrecPojo.getApprsn()==null) utsWxeapprrecPojo.setApprsn("");
        if(utsWxeapprrecPojo.getBillid()==null) utsWxeapprrecPojo.setBillid("");
        if(utsWxeapprrecPojo.getCallbackuuid()==null) utsWxeapprrecPojo.setCallbackuuid("");
        if(utsWxeapprrecPojo.getCallbackname()==null) utsWxeapprrecPojo.setCallbackname("");
        if(utsWxeapprrecPojo.getCallbackdate()==null) utsWxeapprrecPojo.setCallbackdate(new Date());
        if(utsWxeapprrecPojo.getCallbackresult()==null) utsWxeapprrecPojo.setCallbackresult("");
        if(utsWxeapprrecPojo.getCallbackmsg()==null) utsWxeapprrecPojo.setCallbackmsg("");
        if(utsWxeapprrecPojo.getUserid()==null) utsWxeapprrecPojo.setUserid("");
        if(utsWxeapprrecPojo.getRealname()==null) utsWxeapprrecPojo.setRealname("");
        if(utsWxeapprrecPojo.getRemark()==null) utsWxeapprrecPojo.setRemark("");
        if(utsWxeapprrecPojo.getCreateby()==null) utsWxeapprrecPojo.setCreateby("");
        if(utsWxeapprrecPojo.getCreatebyid()==null) utsWxeapprrecPojo.setCreatebyid("");
        if(utsWxeapprrecPojo.getCreatedate()==null) utsWxeapprrecPojo.setCreatedate(new Date());
        if(utsWxeapprrecPojo.getLister()==null) utsWxeapprrecPojo.setLister("");
        if(utsWxeapprrecPojo.getListerid()==null) utsWxeapprrecPojo.setListerid("");
        if(utsWxeapprrecPojo.getModifydate()==null) utsWxeapprrecPojo.setModifydate(new Date());
        if(utsWxeapprrecPojo.getCustom1()==null) utsWxeapprrecPojo.setCustom1("");
        if(utsWxeapprrecPojo.getCustom2()==null) utsWxeapprrecPojo.setCustom2("");
        if(utsWxeapprrecPojo.getCustom3()==null) utsWxeapprrecPojo.setCustom3("");
        if(utsWxeapprrecPojo.getCustom4()==null) utsWxeapprrecPojo.setCustom4("");
        if(utsWxeapprrecPojo.getCustom5()==null) utsWxeapprrecPojo.setCustom5("");
        if(utsWxeapprrecPojo.getTenantid()==null) utsWxeapprrecPojo.setTenantid("");
        if(utsWxeapprrecPojo.getTenantname()==null) utsWxeapprrecPojo.setTenantname("");
        if(utsWxeapprrecPojo.getRevision()==null) utsWxeapprrecPojo.setRevision(0);
   }

    /**
     * 通过Spno查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public UtsWxeapprrecPojo getEntityBySpno(String key, String tid) {

        return this.UUtsWxeapprrecMapper.getEntityBySpno(key, tid);
    }

    /**
     * 通过Billid查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public List<UtsWxeapprrecPojo> getOnlineByBillid(String key, String tid) {
        return this.UUtsWxeapprrecMapper.getOnlineByBillid(key, tid);
    }
}
