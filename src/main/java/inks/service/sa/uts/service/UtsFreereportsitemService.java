package inks.service.sa.uts.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.UtsFreereportsitemPojo;

import java.util.List;
/**
 * 自由报表项目(UtsFreereportsitem)表服务接口
 *
 * <AUTHOR>
 * @since 2024-05-15 11:29:58
 */
public interface UtsFreereportsitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    UtsFreereportsitemPojo getEntity(String key,String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<UtsFreereportsitemPojo> getPageList(QueryParam queryParam);
    
     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<UtsFreereportsitemPojo> getList(String Pid,String tid);  
    
    /**
     * 新增数据
     *
     * @param utsFreereportsitemPojo 实例对象
     * @return 实例对象
     */
    UtsFreereportsitemPojo insert(UtsFreereportsitemPojo utsFreereportsitemPojo);

    /**
     * 修改数据
     *
     * @param utsFreereportsitempojo 实例对象
     * @return 实例对象
     */
    UtsFreereportsitemPojo update(UtsFreereportsitemPojo utsFreereportsitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key,String tid);

     /**
     * 修改数据
     *
     * @param utsFreereportsitempojo 实例对象
     * @return 实例对象
     */
    UtsFreereportsitemPojo clearNull(UtsFreereportsitemPojo utsFreereportsitempojo);
}
