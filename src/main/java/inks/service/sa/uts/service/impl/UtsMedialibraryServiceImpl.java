package inks.service.sa.uts.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.config.oss.service.FileController;
import inks.service.sa.uts.domain.UtsMedialibraryEntity;
import inks.service.sa.uts.domain.pojo.UtsMedialibraryPojo;
import inks.service.sa.uts.mapper.UtsMedialibraryMapper;
import inks.service.sa.uts.service.UtsMedialibraryService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 素材库(UtsMedialibrary)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-13 15:45:12
 */
@Service("utsMedialibraryService")
public class UtsMedialibraryServiceImpl implements UtsMedialibraryService {
    @Resource
    private UtsMedialibraryMapper utsMedialibraryMapper;
    @Resource
    private FileController fileController;

    @Override
    public UtsMedialibraryPojo getEntity(String key) {
        return this.utsMedialibraryMapper.getEntity(key);
    }


    @Override
    public PageInfo<UtsMedialibraryPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<UtsMedialibraryPojo> lst = utsMedialibraryMapper.getPageList(queryParam);
            PageInfo<UtsMedialibraryPojo> pageInfo = new PageInfo<UtsMedialibraryPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public UtsMedialibraryPojo insert(UtsMedialibraryPojo utsMedialibraryPojo) {
        //初始化NULL字段
        cleanNull(utsMedialibraryPojo);
        UtsMedialibraryEntity utsMedialibraryEntity = new UtsMedialibraryEntity();
        BeanUtils.copyProperties(utsMedialibraryPojo, utsMedialibraryEntity);
        //生成雪花id
        utsMedialibraryEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        utsMedialibraryEntity.setRevision(1);  //乐观锁
        this.utsMedialibraryMapper.insert(utsMedialibraryEntity);
        return this.getEntity(utsMedialibraryEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param utsMedialibraryPojo 实例对象
     * @return 实例对象
     */
    @Override
    public UtsMedialibraryPojo update(UtsMedialibraryPojo utsMedialibraryPojo) {
        UtsMedialibraryEntity utsMedialibraryEntity = new UtsMedialibraryEntity();
        BeanUtils.copyProperties(utsMedialibraryPojo, utsMedialibraryEntity);
        this.utsMedialibraryMapper.update(utsMedialibraryEntity);
        return this.getEntity(utsMedialibraryEntity.getId());
    }


    @Override
    public int delete(String key) {
        // 同步删除minio的文件
        UtsMedialibraryPojo utsMedialibraryDB = getEntity(key);
        // objectname数据库存储的是完整url：http://dev.inksyun.com:30484/File/split/stream/video/202506/2930d7d673b74c63bb31e7bcfb112ae5.mp4
        String realObjectname = utsMedialibraryDB.getDirname() + "/" + utsMedialibraryDB.getFilename();
        fileController.remove(realObjectname);
        return this.utsMedialibraryMapper.delete(key);
    }


    private static void cleanNull(UtsMedialibraryPojo utsMedialibraryPojo) {
        if (utsMedialibraryPojo.getGengroupid() == null) utsMedialibraryPojo.setGengroupid("");
        if (utsMedialibraryPojo.getFileoriname() == null) utsMedialibraryPojo.setFileoriname("");
        if (utsMedialibraryPojo.getBucketname() == null) utsMedialibraryPojo.setBucketname("");
        if (utsMedialibraryPojo.getDirname() == null) utsMedialibraryPojo.setDirname("");
        if (utsMedialibraryPojo.getFilename() == null) utsMedialibraryPojo.setFilename("");
        if (utsMedialibraryPojo.getContenttype() == null) utsMedialibraryPojo.setContenttype("");
        if (utsMedialibraryPojo.getFilesuffix() == null) utsMedialibraryPojo.setFilesuffix("");
        if (utsMedialibraryPojo.getStorage() == null) utsMedialibraryPojo.setStorage("");
        if (utsMedialibraryPojo.getFileurl() == null) utsMedialibraryPojo.setFileurl("");
        if (utsMedialibraryPojo.getMediatype() == null) utsMedialibraryPojo.setMediatype(0);
        if (utsMedialibraryPojo.getEnabledmark() == null) utsMedialibraryPojo.setEnabledmark(0);
        if (utsMedialibraryPojo.getRownum() == null) utsMedialibraryPojo.setRownum(0);
        if (utsMedialibraryPojo.getUsecount() == null) utsMedialibraryPojo.setUsecount(0);
        if (utsMedialibraryPojo.getRemark() == null) utsMedialibraryPojo.setRemark("");
        if (utsMedialibraryPojo.getCreateby() == null) utsMedialibraryPojo.setCreateby("");
        if (utsMedialibraryPojo.getCreatebyid() == null) utsMedialibraryPojo.setCreatebyid("");
        if (utsMedialibraryPojo.getCreatedate() == null) utsMedialibraryPojo.setCreatedate(new Date());
        if (utsMedialibraryPojo.getLister() == null) utsMedialibraryPojo.setLister("");
        if (utsMedialibraryPojo.getListerid() == null) utsMedialibraryPojo.setListerid("");
        if (utsMedialibraryPojo.getModifydate() == null) utsMedialibraryPojo.setModifydate(new Date());
        if (utsMedialibraryPojo.getCustom1() == null) utsMedialibraryPojo.setCustom1("");
        if (utsMedialibraryPojo.getCustom2() == null) utsMedialibraryPojo.setCustom2("");
        if (utsMedialibraryPojo.getCustom3() == null) utsMedialibraryPojo.setCustom3("");
        if (utsMedialibraryPojo.getCustom4() == null) utsMedialibraryPojo.setCustom4("");
        if (utsMedialibraryPojo.getCustom5() == null) utsMedialibraryPojo.setCustom5("");
        if (utsMedialibraryPojo.getCustom6() == null) utsMedialibraryPojo.setCustom6("");
        if (utsMedialibraryPojo.getCustom7() == null) utsMedialibraryPojo.setCustom7("");
        if (utsMedialibraryPojo.getCustom8() == null) utsMedialibraryPojo.setCustom8("");
        if (utsMedialibraryPojo.getCustom9() == null) utsMedialibraryPojo.setCustom9("");
        if (utsMedialibraryPojo.getCustom10() == null) utsMedialibraryPojo.setCustom10("");
        if (utsMedialibraryPojo.getDeptid() == null) utsMedialibraryPojo.setDeptid("");
        if (utsMedialibraryPojo.getTenantid() == null) utsMedialibraryPojo.setTenantid("");
        if (utsMedialibraryPojo.getTenantname() == null) utsMedialibraryPojo.setTenantname("");
        if (utsMedialibraryPojo.getRevision() == null) utsMedialibraryPojo.setRevision(0);
    }

}
