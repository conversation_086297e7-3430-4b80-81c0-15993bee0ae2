package inks.service.sa.uts.service.sql;

import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@SpringBootTest
public class MyTest {
    @Resource
    private SqlExecutorService sqlExecutorService;
    @Resource
    private SqlExecutorService2 sqlExecutorService2;

    @Test
    public void test() {
// 执行查询
        SqlExecuteRequest request = new SqlExecuteRequest();
        request.setSql("SELECT * FROM Sa_User where 1=1");
        //request.setParams(Collections.singletonMap("age", 18));
        //request.setTimeout(30);
        //request.setMaxRows(1000);

// 调用执行
        SqlExecutionResult result = sqlExecutorService.executeSql(request);
        //SqlExecutionResult result2 = sqlExecutorService2.executeSql(request);

// 检查结果
        if (result.isSuccess()) {
            List<Map<String, Object>> data = (List<Map<String, Object>>) result.getData();
            // 处理数据...
            System.out.println(data);
        } else {
            // 处理错误...
            String errorMessage = result.getErrorMessage();
        }
    }


    @Test
    public void test2() {
        // 1. 创建SQL执行请求对象
        SqlExecuteRequest2 request = new SqlExecuteRequest2();

        // 2. 设置SQL语句
        request.setSql("SELECT * FROM Sa_User where 1=1");

        // 3. 设置数据源（如果使用默认数据源可以不设置）
        //request.setDataSourceKey("default");
        request.setDataSourceKey("1");

        // 4. 如果需要设置查询超时和最大行数（可选）
        request.setTimeout(30); // 30秒超时
        request.setMaxRows(1000); // 最多返回1000行

        // 5. 执行SQL并获取结果
        SqlExecutionResult result = sqlExecutorService2.executeSql(request);

        // 6. 验证结果
        Assert.assertTrue(result.isSuccess());
        Assert.assertEquals("SELECT", result.getType());

        // 7. 获取并处理数据
        List<Map<String, Object>> data = (List<Map<String, Object>>) result.getData();
        if (data != null && !data.isEmpty()) {
            for (Map<String, Object> row : data) {
                // 打印每行数据
                System.out.println("User data: " + row);

                // 可以获取具体字段值，假设Sa_User表有id、username等字段
                Object id = row.get("id");
                Object username = row.get("username");
                System.out.println("ID: " + id + ", Username: " + username);
            }
        }

        // 8. 打印总行数
        System.out.println("Total rows: " + result.getTotalRows());
    }

    @Test
    public void testSqlServer() {
        // 1. 创建SQL执行请求对象
        SqlExecuteRequest2 request = new SqlExecuteRequest2();

        // 2. 设置SQL语句
        request.setSql("SELECT * FROM l_machiningtype where 1=1");

        // 3. 设置数据源（如果使用默认数据源可以不设置）
        //request.setDataSourceKey("default");
        request.setDataSourceKey("6");//*****************************************************

        // 4. 如果需要设置查询超时和最大行数（可选）
        request.setTimeout(30); // 30秒超时
        request.setMaxRows(1000); // 最多返回1000行

        // 5. 执行SQL并获取结果
        SqlExecutionResult result = sqlExecutorService2.executeSql(request);

        // 6. 验证结果
        Assert.assertTrue(result.isSuccess());
        Assert.assertEquals("SELECT", result.getType());

        // 7. 获取并处理数据
        List<Map<String, Object>> data = (List<Map<String, Object>>) result.getData();
        if (data != null && !data.isEmpty()) {
            for (Map<String, Object> row : data) {
                // 打印每行数据
                System.out.println("User data: " + row);

                // 可以获取具体字段值，假设Sa_User表有id、username等字段
                Object id = row.get("id");
                Object username = row.get("username");
                System.out.println("ID: " + id + ", Username: " + username);
            }
        }

        // 8. 打印总行数
        System.out.println("Total rows: " + result.getTotalRows());
    }
}