package inks.service.sa.uts.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.uts.domain.pojo.UtsEmailmsgPojo;
import inks.service.sa.uts.domain.UtsEmailmsgEntity;
import inks.service.sa.uts.mapper.UtsEmailmsgMapper;
import inks.service.sa.uts.service.UtsEmailmsgService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/**
 * 邮件信息(UtsEmailmsg)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-28 14:01:05
 */
@Service("utsEmailmsgService")
public class UtsEmailmsgServiceImpl implements UtsEmailmsgService {
    @Resource
    private UtsEmailmsgMapper utsEmailmsgMapper;

    @Override
    public UtsEmailmsgPojo getEntity(String key) {
        return this.utsEmailmsgMapper.getEntity(key);
    }


    @Override
    public PageInfo<UtsEmailmsgPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<UtsEmailmsgPojo> lst = utsEmailmsgMapper.getPageList(queryParam);
            PageInfo<UtsEmailmsgPojo> pageInfo = new PageInfo<UtsEmailmsgPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public UtsEmailmsgPojo insert(UtsEmailmsgPojo utsEmailmsgPojo) {
        //初始化NULL字段
        cleanNull(utsEmailmsgPojo);
        UtsEmailmsgEntity utsEmailmsgEntity = new UtsEmailmsgEntity(); 
        BeanUtils.copyProperties(utsEmailmsgPojo,utsEmailmsgEntity);
        //生成雪花id
          utsEmailmsgEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          utsEmailmsgEntity.setRevision(1);  //乐观锁
          this.utsEmailmsgMapper.insert(utsEmailmsgEntity);
        return this.getEntity(utsEmailmsgEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param utsEmailmsgPojo 实例对象
     * @return 实例对象
     */
    @Override
    public UtsEmailmsgPojo update(UtsEmailmsgPojo utsEmailmsgPojo) {
        UtsEmailmsgEntity utsEmailmsgEntity = new UtsEmailmsgEntity(); 
        BeanUtils.copyProperties(utsEmailmsgPojo,utsEmailmsgEntity);
        this.utsEmailmsgMapper.update(utsEmailmsgEntity);
        return this.getEntity(utsEmailmsgEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.utsEmailmsgMapper.delete(key) ;
    }
    

    private static void cleanNull(UtsEmailmsgPojo utsEmailmsgPojo) {
        if(utsEmailmsgPojo.getMsgcode()==null) utsEmailmsgPojo.setMsgcode("");
        if(utsEmailmsgPojo.getMsgname()==null) utsEmailmsgPojo.setMsgname("");
        if(utsEmailmsgPojo.getMsgtype()==null) utsEmailmsgPojo.setMsgtype("");
        if(utsEmailmsgPojo.getMsgtemplate()==null) utsEmailmsgPojo.setMsgtemplate("");
        if(utsEmailmsgPojo.getModulecode()==null) utsEmailmsgPojo.setModulecode("");
        if(utsEmailmsgPojo.getMsggroupid()==null) utsEmailmsgPojo.setMsggroupid("");
        if(utsEmailmsgPojo.getRecipient()==null) utsEmailmsgPojo.setRecipient("");
        if(utsEmailmsgPojo.getCcpeople()==null) utsEmailmsgPojo.setCcpeople("");
        if(utsEmailmsgPojo.getItemjson()==null) utsEmailmsgPojo.setItemjson("");
        if(utsEmailmsgPojo.getRownum()==null) utsEmailmsgPojo.setRownum(0);
        if(utsEmailmsgPojo.getEnabledmark()==null) utsEmailmsgPojo.setEnabledmark(0);
        if(utsEmailmsgPojo.getRemark()==null) utsEmailmsgPojo.setRemark("");
        if(utsEmailmsgPojo.getCreateby()==null) utsEmailmsgPojo.setCreateby("");
        if(utsEmailmsgPojo.getCreatebyid()==null) utsEmailmsgPojo.setCreatebyid("");
        if(utsEmailmsgPojo.getCreatedate()==null) utsEmailmsgPojo.setCreatedate(new Date());
        if(utsEmailmsgPojo.getLister()==null) utsEmailmsgPojo.setLister("");
        if(utsEmailmsgPojo.getListerid()==null) utsEmailmsgPojo.setListerid("");
        if(utsEmailmsgPojo.getModifydate()==null) utsEmailmsgPojo.setModifydate(new Date());
        if(utsEmailmsgPojo.getCustom1()==null) utsEmailmsgPojo.setCustom1("");
        if(utsEmailmsgPojo.getCustom2()==null) utsEmailmsgPojo.setCustom2("");
        if(utsEmailmsgPojo.getCustom3()==null) utsEmailmsgPojo.setCustom3("");
        if(utsEmailmsgPojo.getCustom4()==null) utsEmailmsgPojo.setCustom4("");
        if(utsEmailmsgPojo.getCustom5()==null) utsEmailmsgPojo.setCustom5("");
        if(utsEmailmsgPojo.getTenantid()==null) utsEmailmsgPojo.setTenantid("");
        if(utsEmailmsgPojo.getTenantname()==null) utsEmailmsgPojo.setTenantname("");
        if(utsEmailmsgPojo.getRevision()==null) utsEmailmsgPojo.setRevision(0);
   }

}
