package inks.service.sa.uts.utils.ding;

import com.alibaba.fastjson.JSON;
import com.aliyun.dingtalkcalendar_1_0.Client;
import com.aliyun.dingtalkcalendar_1_0.models.CreateEventHeaders;
import com.aliyun.dingtalkcalendar_1_0.models.CreateEventRequest;
import com.aliyun.dingtalkcalendar_1_0.models.CreateEventResponse;
import com.aliyun.dingtalkcalendar_1_0.models.CreateEventResponseBody;
import com.aliyun.teautil.models.RuntimeOptions;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.StringUtils;
import inks.service.sa.uts.constant.SchedulePatternTypeConstant;
import inks.service.sa.uts.domain.dingSchedulePojos.ScheduleParamPojo;

public class DingScheduleUtil {

    /**
     * 使用 Token 初始化账号Client
     *
     * @return Client
     * @throws Exception
     */
    public static Client createClient() throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config();
        config.protocol = "https";
        config.regionId = "central";
        return new Client(config);
    }

    public static void createSchedule(ScheduleParamPojo scheduleParamPojo) {
        try {
            Client client = createClient();
            CreateEventHeaders createEventHeaders = new CreateEventHeaders();
            createEventHeaders.xAcsDingtalkAccessToken = AccessTokenUtil.getToken(); //在请求头中加入token
            if (StringUtils.isNotEmpty(scheduleParamPojo.getOnlineMeetingInfo().getType())) {
                CreateEventRequest.CreateEventRequestOnlineMeetingInfo requestOnlineMeetingInfo = new CreateEventRequest.CreateEventRequestOnlineMeetingInfo();
                requestOnlineMeetingInfo.setType(scheduleParamPojo.getOnlineMeetingInfo().getType());
            }

            CreateEventRequest.CreateEventRequestStart start = new CreateEventRequest.CreateEventRequestStart().setTimeZone("Asia/Shanghai");
            CreateEventRequest.CreateEventRequestEnd end = new CreateEventRequest.CreateEventRequestEnd().setTimeZone("Asia/Shanghai");
            //根据isAllDay判断是否全天日程来设置start和end时间  默认Asia/Shanghai时区
            if (!scheduleParamPojo.getAllDay()) {
                start.setDateTime(DateUtils.parseDateToStr("yyyy-MM-dd'T'HH:mm:ss+08:00", scheduleParamPojo.getStart().getDateTime()));
                end.setDateTime(DateUtils.parseDateToStr("yyyy-MM-dd'T'HH:mm:ss+08:00", scheduleParamPojo.getEnd().getDateTime()));
            } else {
                start.setDateTime(DateUtils.parseDateToStr("yyyy-MM-dd", scheduleParamPojo.getStart().getData()));
                end.setDateTime(DateUtils.parseDateToStr("yyyy-MM-dd", scheduleParamPojo.getEnd().getDateTime()));
            }

            CreateEventRequest.CreateEventRequestRecurrencePattern recurrencePattern = new CreateEventRequest.CreateEventRequestRecurrencePattern();

            String type = scheduleParamPojo.getRecurrence().getSchedulePattern().getType();
            Integer interval = scheduleParamPojo.getRecurrence().getSchedulePattern().getInterval();
            if (interval == 0) {
                throw new BaseBusinessException("请输入interval");
            } else {
                recurrencePattern.setType(type).setInterval(interval);
                if (SchedulePatternTypeConstant.WEEKLY.equals(type)) {
                    if (StringUtils.isNotEmpty(scheduleParamPojo.getRecurrence().getSchedulePattern().getDaysOfWeek())) {
                        recurrencePattern.setDaysOfWeek(scheduleParamPojo.getRecurrence().getSchedulePattern().getDaysOfWeek());
                    }
                } else if (SchedulePatternTypeConstant.ABSOLUTE_MONTHLY.equals(type)) {
                    if (scheduleParamPojo.getRecurrence().getSchedulePattern().getDayOfMonth() != 0) {
                        recurrencePattern.setDayOfMonth(scheduleParamPojo.getRecurrence().getSchedulePattern().getDayOfMonth());
                    }
                } else if (SchedulePatternTypeConstant.RELATIVE_MONTHLY.equals(type)) {
                    if (StringUtils.isNotEmpty(scheduleParamPojo.getRecurrence().getSchedulePattern().getIndex()) && StringUtils.isNotEmpty(scheduleParamPojo.getRecurrence().getSchedulePattern().getDaysOfWeek())) {
                        recurrencePattern.setIndex(scheduleParamPojo.getRecurrence().getSchedulePattern().getIndex()).setDaysOfWeek(scheduleParamPojo.getRecurrence().getSchedulePattern().getDaysOfWeek());
                    }
                }
            }

            CreateEventRequest.CreateEventRequestRecurrenceRange recurrenceRange = new CreateEventRequest.CreateEventRequestRecurrenceRange();
            recurrenceRange.setType(scheduleParamPojo.getRecurrence().getScheduleRange().getType());
            recurrenceRange.setEndDate(scheduleParamPojo.getRecurrence().getScheduleRange().getEndDate());


            CreateEventRequest.CreateEventRequestRecurrence recurrence = new CreateEventRequest.CreateEventRequestRecurrence();
            recurrence.setPattern(recurrencePattern).setRange(recurrenceRange);

            CreateEventRequest eventRequest = new CreateEventRequest();
            eventRequest
                    .setSummary(scheduleParamPojo.getSummary())
                    .setStart(start)
                    .setEnd(end)
                    .setDescription(scheduleParamPojo.getDescription())
                    .setIsAllDay(scheduleParamPojo.getAllDay())
                    .setRecurrence(recurrence);

            CreateEventResponse response = client.createEventWithOptions(
                    scheduleParamPojo.getUserId(),
                    scheduleParamPojo.getCalendarId(),
                    eventRequest,
                    createEventHeaders,
                    new RuntimeOptions());
            CreateEventResponseBody body = response.getBody();
            System.out.println(JSON.toJSON(body));
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

}
