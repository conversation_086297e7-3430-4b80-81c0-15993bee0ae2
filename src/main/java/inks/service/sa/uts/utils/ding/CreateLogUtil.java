package inks.service.sa.uts.utils.ding;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.request.OapiReportCreateRequest;
import com.dingtalk.api.response.OapiReportCreateResponse;
import com.taobao.api.ApiException;
import inks.common.core.exception.BaseBusinessException;

import java.util.List;

public class CreateLogUtil {
    /**
     * 创建日志
     *
     * @param userId     创建人id
     * @param templateId 使用的日志模板id
     * @param params     日志填写的内容
     * @param toUids     日志发送到的员工id
     * @param toCids     日志发送到的群id
     * @return
     */
    public static String createLog(String userId, String templateId, List<OapiReportCreateRequest.OapiReportContentVo> params, List<String> toUids, List<String> toCids) {
        //获取token
        String accessToken = AccessTokenUtil.getToken();
        DefaultDingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/report/create");
        OapiReportCreateRequest request = new OapiReportCreateRequest();
        OapiReportCreateRequest.OapiCreateReportParam reportParam = new OapiReportCreateRequest.OapiCreateReportParam();

        reportParam.setContents(params);
        reportParam.setToUserids(toUids);
        reportParam.setTemplateId(templateId);
        reportParam.setToChat(true);
        reportParam.setDdFrom("report");
        reportParam.setUserid(userId);
        reportParam.setToCids(toCids);

        request.setCreateReportParam(reportParam);
        try {
            OapiReportCreateResponse response = client.execute(request, accessToken);
            return response.getBody();
        } catch (ApiException e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }
}
