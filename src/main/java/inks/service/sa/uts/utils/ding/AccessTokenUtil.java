package inks.service.sa.uts.utils.ding;

import com.alibaba.fastjson.JSON;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.request.OapiGettokenRequest;
import com.dingtalk.api.response.OapiGettokenResponse;
import com.taobao.api.ApiException;
import inks.service.sa.uts.domain.dingSchedulePojos.ScheduleParamPojo;

public class AccessTokenUtil {
    public static String AppKey = "dingahf3tczoujqoo2kg";
    public static String AppSecret = "Lz6KqNC69JIO94G1riUp1MXbMruGywG01pfLZmNYHv8GMzS_vOaYdnRb3FUTQ8zP";

    public static String getToken() throws RuntimeException {
        try {
            DefaultDingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/gettoken");
            OapiGettokenRequest request = new OapiGettokenRequest();

            request.setAppkey(AppKey);
            request.setAppsecret(AppSecret);
            request.setHttpMethod("GET");
            OapiGettokenResponse response = client.execute(request);
            String accessToken = response.getAccessToken();
            return accessToken;
        } catch (ApiException e) {
            throw new RuntimeException();
        }

    }


    public static void main(String[] args) throws ApiException {
        String test = "{\n" +
                "\t\"summary\":\"日程测试1\",\n" +
                "\t\"userId\":\"I18xtuiihAgxLU5zLy5ZqGAiEiE\",\n" +
                "\t\"calendarId\":\"primary\",\n" +
                "\t\"recurrence\":{\n" +
                "\t\t\"pattern\":{\n" +
                "\t\t\t\"type\":\"daily\",\n" +
                "\t\t\t\"interval\":10\n" +
                "\t\t},\n" +
                "\t\t\"range\":{\n" +
                "\t\t\t\"type\":\"daily\"\n" +
                "\t\t}\n" +
                "\t},\n" +
                "\t\"isAllDay\":false,\n" +
                "\t\"start\":{\n" +
                "\t\t\"dateTime\":\"2022-09-19 19:00:00\",\n" +
                "\t\t\"timeZone\":\"Asia/Shanghai\"\n" +
                "\t},\n" +
                "\t\"description\":\"描述测试1\",\n" +
                "\t\"end\":{\n" +
                "\t\t\"dateTime\":\"2022-09-19 20:00:00\",\n" +
                "\t\t\"timeZone\":\"Asia/Shanghai\"\n" +
                "\t},\n" +
                "\t\"onlineMeetingInfo\":{\n" +
                "\t\t\"type\":\"daily\"\n" +
                "\t}\n" +
                "}";
        ScheduleParamPojo scheduleParamPojo = JSON.parseObject(test, ScheduleParamPojo.class);
        DingScheduleUtil.createSchedule(scheduleParamPojo);


    }
}
