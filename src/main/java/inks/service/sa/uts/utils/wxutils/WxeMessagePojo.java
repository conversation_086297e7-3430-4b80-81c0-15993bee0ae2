package inks.service.sa.uts.utils.wxutils;


//官方文档： https://developer.work.weixin.qq.com/document/path/90236#markdown%E6%B6%88%E6%81%AF
public class WxeMessagePojo {
    private String touser;
    private String msgtype;
    private Integer agentid;
    private Integer safe;
    private WxeTextPojo text;
    private String toparty;
    private String totag;
    private WxeTextcardPojo textcard;
    private WxeMarkdownPojo markdown; // 新增：支持 markdown 消息类型
    private Integer enable_id_trans;
    private Integer enable_duplicate_check;
    private Integer duplicate_check_interval;


    /**
     * 根据消息类型智能设置内容
     * 根据当前的 msgtype 自动判断并设置到对应的字段（text 或 markdown）
     * text需要格式是  "text": { "content":
     * markdown需要格式是  "markdown": { "content":
     * @param content 消息内容
     */
    public void setContentByType(String content) {
        if ("markdown".equals(this.msgtype)) {
            // 设置 markdown 消息内容
            WxeMarkdownPojo wxeMarkdownPojo = new WxeMarkdownPojo();
            wxeMarkdownPojo.setContent(content);
            this.setMarkdown(wxeMarkdownPojo);
            // 清空 text 字段，避免冲突
            this.setText(null);
        } else {
            // 默认设置 text 消息内容（包括 msgtype 为空或其他值的情况）
            WxeTextPojo wxeTextPojo = new WxeTextPojo();
            wxeTextPojo.setContent(content);
            this.setText(wxeTextPojo);
            // 清空 markdown 字段，避免冲突
            this.setMarkdown(null);
        }
    }

    public String getTouser() {
        return touser;
    }

    public void setTouser(String touser) {
        this.touser = touser;
    }

    public String getMsgtype() {
        return msgtype;
    }

    public void setMsgtype(String msgtype) {
        this.msgtype = msgtype;
    }

    public Integer getAgentid() {
        return agentid;
    }

    public void setAgentid(Integer agentid) {
        this.agentid = agentid;
    }

    public Integer getSafe() {
        return safe;
    }

    public void setSafe(Integer safe) {
        this.safe = safe;
    }

    public WxeTextPojo getText() {
        return text;
    }

    public void setText(WxeTextPojo text) {
        this.text = text;
    }

    public String getToparty() {
        return toparty;
    }

    public void setToparty(String toparty) {
        this.toparty = toparty;
    }

    public String getTotag() {
        return totag;
    }

    public void setTotag(String totag) {
        this.totag = totag;
    }

    public WxeTextcardPojo getTextcard() {
        return textcard;
    }

    public void setTextcard(WxeTextcardPojo textcard) {
        this.textcard = textcard;
    }

    public Integer getEnable_id_trans() {
        return enable_id_trans;
    }

    public void setEnable_id_trans(Integer enable_id_trans) {
        this.enable_id_trans = enable_id_trans;
    }

    public Integer getEnable_duplicate_check() {
        return enable_duplicate_check;
    }

    public void setEnable_duplicate_check(Integer enable_duplicate_check) {
        this.enable_duplicate_check = enable_duplicate_check;
    }

    public Integer getDuplicate_check_interval() {
        return duplicate_check_interval;
    }

    public void setDuplicate_check_interval(Integer duplicate_check_interval) {
        this.duplicate_check_interval = duplicate_check_interval;
    }

    public WxeMarkdownPojo getMarkdown() {
        return markdown;
    }

    public void setMarkdown(WxeMarkdownPojo markdown) {
        this.markdown = markdown;
    }

}
