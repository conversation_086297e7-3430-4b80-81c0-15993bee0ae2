package inks.service.sa.uts.utils.wxutils; /*
 *功能描述
 * <AUTHOR>
 * @date  2022/1/12
 * @param 审批返回XML转换对象类
 */

public class ResultPojo {
    private Long createTime;
    private String toUserName;
    private String msgType;
    private String event;
    private String fromUserName;
    private Long agentID;
    private ApprovalInfoPojo approvalInfo;

    public ApprovalInfoPojo getApprovalInfo() {
        return approvalInfo;
    }

    public void setApprovalInfo(ApprovalInfoPojo approvalInfo) {
        this.approvalInfo = approvalInfo;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public String getToUserName() {
        return toUserName;
    }

    public void setToUserName(String toUserName) {
        this.toUserName = toUserName;
    }

    public String getMsgType() {
        return msgType;
    }

    public void setMsgType(String msgType) {
        this.msgType = msgType;
    }

    public String getEvent() {
        return event;
    }

    public void setEvent(String event) {
        this.event = event;
    }

    public String getFromUserName() {
        return fromUserName;
    }

    public void setFromUserName(String fromUserName) {
        this.fromUserName = fromUserName;
    }

    public Long getAgentID() {
        return agentID;
    }

    public void setAgentID(Long agentID) {
        this.agentID = agentID;
    }

    @Override
    public String toString() {
        String sb = "{" + "\"createTime\":" +
                createTime +
                ",\"toUserName\":\"" +
                toUserName + '\"' +
                ",\"msgType\":\"" +
                msgType + '\"' +
                ",\"event\":\"" +
                event + '\"' +
                ",\"fromUserName\":\"" +
                fromUserName + '\"' +
                ",\"agentID\":" +
                agentID +
                ",\"approvalInfo\":" +
                approvalInfo +
                '}';
        return sb;
    }
}
