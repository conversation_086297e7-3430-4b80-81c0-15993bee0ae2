package inks.service.sa.uts.utils.quartz;

import inks.service.sa.uts.domain.pojo.SaJobPojo;
import inks.service.sa.uts.domain.pojo.SaJobPojo;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionContext;

/**
 * 定时任务处理（禁止并发执行）
 * 
 * <AUTHOR>
 *
 */
@DisallowConcurrentExecution
public class QuartzDisallowConcurrentExecution extends AbstractQuartzJob
{
    @Override
    protected void doExecute(JobExecutionContext context, SaJobPojo sysJob) throws Exception
    {
        JobInvokeUtil.invokeMethod(sysJob);
    }
}
