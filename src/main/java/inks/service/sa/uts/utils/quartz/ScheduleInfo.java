package inks.service.sa.uts.utils.quartz;

import lombok.Data;

/**
 * 用于承载查询结果的 DTO
 */
@Data
public class ScheduleInfo {
    //任务分组名，对应 SaJobPojo.getJobgroup()，用于区分不同类型的任务。
    private String jobgroup;
    //任务名称/描述，不是 Quartz 的唯一标识，通常来源于业务字段。
    private String jobname;
    //Quartz 生成的触发器名称，规则为 "TASK_CLASS_NAME" + jobId。
    private String triggername;
    //触发器当前状态（NORMAL=正常运行、PAUSED=暂停、ERROR=错误...）
    private String triggerstate;
    //上次执行时间，null 表示未执行过。
    private String previousfiretime;
    //下一次预计执行时间
    private String nextfiretime;
    //CRON 表达式，定义任务的执行周期规则
    private String cronexpression;
}
