package inks.service.sa.uts.utils.wxutils; /*
 *功能描述
 * <AUTHOR>
 * @date  2022/1/12
 * @param 审批回调转换对象
 */

import java.util.List;

public class ApprovalInfoPojo {
    private Integer spStatus;
    private Long applyTime;
    private Integer statuChangeEvent;
    private Long spNo;
    private String spName;
    private String templateId;
    private ResultUserPojo applyer;
    private List<ResultUserPojo> notifyer;
    private List<SpRecordPojo> spRecord;

    public Long getSpNo() {
        return spNo;
    }

    public void setSpNo(Long spNo) {
        this.spNo = spNo;
    }

    public String getSpName() {
        return spName;
    }

    public void setSpName(String spName) {
        this.spName = spName;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public Integer getSpStatus() {
        return spStatus;
    }

    public void setSpStatus(Integer spStatus) {
        this.spStatus = spStatus;
    }

    public Long getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(Long applyTime) {
        this.applyTime = applyTime;
    }

    public Integer getStatuChangeEvent() {
        return statuChangeEvent;
    }

    public void setStatuChangeEvent(Integer statuChangeEvent) {
        this.statuChangeEvent = statuChangeEvent;
    }

    public ResultUserPojo getApplyer() {
        return applyer;
    }

    public void setApplyer(ResultUserPojo applyer) {
        this.applyer = applyer;
    }

    public List<ResultUserPojo> getNotifyer() {
        return notifyer;
    }

    public void setNotifyer(List<ResultUserPojo> notifyer) {
        this.notifyer = notifyer;
    }

    public List<SpRecordPojo> getSpRecord() {
        return spRecord;
    }

    public void setSpRecord(List<SpRecordPojo> spRecord) {
        this.spRecord = spRecord;
    }

    @Override
    public String toString() {
        String sb = "{" + "\"spStatus\":" +
                spStatus +
                ",\"applyTime\":" +
                applyTime +
                ",\"statuChangeEvent\":" +
                statuChangeEvent +
                ",\"spNo\":" +
                spNo +
                ",\"spName\":\"" +
                spName + '\"' +
                ",\"templateId\":\"" +
                templateId + '\"' +
                ",\"applyer\":" +
                applyer +
                ",\"notifyer\":" +
                notifyer +
                ",\"spRecord\":" +
                spRecord +
                '}';
        return sb;
    }
}
