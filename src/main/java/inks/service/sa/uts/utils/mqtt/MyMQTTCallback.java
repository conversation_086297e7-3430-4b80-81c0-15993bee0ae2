package inks.service.sa.uts.utils.mqtt;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import inks.service.sa.uts.utils.PrintColor;
import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.MqttCallbackExtended;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/6/29 20:43
 */
public class MyMQTTCallback implements MqttCallbackExtended {

    private static final Logger log = LoggerFactory.getLogger(MyMQTTCallback.class);
    private static boolean firstMessagePrinted = false;
    //手动注入
    private final MqttConfiguration mqttConfiguration = SpringUtilsMQTT.getBean(MqttConfiguration.class);
    private final MyMQTTClient myMQTTClient;


    public MyMQTTCallback(MyMQTTClient myMQTTClient) {
        this.myMQTTClient = myMQTTClient;
    }

    /**
     * 丢失连接，可在这里做重连
     * 只会调用一次
     *
     * @param throwable
     */
    @Override
    public void connectionLost(Throwable throwable) {
        log.error("mqtt connectionLost 连接断开，5S之后尝试重连: {}", throwable.getMessage());
        long reconnectTimes = 1;
        while (true) {
            try {
                if (MyMQTTClient.getClient().isConnected()) {
                    //判断已经重新连接成功  需要重新订阅主题 可以在这个if里面订阅主题  或者 connectComplete（方法里面）  看你们自己选择
                    log.warn("mqtt reconnect success end  重新连接  重新订阅成功");
                    return;
                }
                reconnectTimes += 1;
                log.warn("mqtt reconnect times = {} try again...  mqtt重新连接时间 {}", reconnectTimes, reconnectTimes);
                MyMQTTClient.getClient().reconnect();
            } catch (MqttException e) {
                log.error("mqtt断连异常", e);
            }
            try {
                Thread.sleep(5000);
            } catch (InterruptedException e1) {
            }
        }
    }

    /**
     * @param topic
     * @param mqttMessage
     * @throws Exception subscribe后得到的消息会执行到这里面
     */
    @Override
    public void messageArrived(String topic, MqttMessage mqttMessage) throws Exception {
        String msg = new String(mqttMessage.getPayload());
        if (!firstMessagePrinted) {
            PrintColor.jin("(只打印一次证明有接收能力)messageArrived方法： 【接收消息topic】 : " + topic + " 【接收消息内容】 : " + msg);
            firstMessagePrinted = true;
        }        //发布消息主题
        if (topic.equals("embed/resp")) {
            Map maps = (Map) JSON.parse(new String(mqttMessage.getPayload(), StandardCharsets.UTF_8));
            //你自己的业务接口
//            insertCmdResults(maps);
        }
        //接收报警主题
        if (topic.equals("embed/warn")) {
            Map maps = (Map) JSON.parse(new String(mqttMessage.getPayload(), StandardCharsets.UTF_8));
            //你自己的业务接口
//            insertPushAlarm(maps);
        }
        try {
            // 判断是否为JSON格式数据
            if (isValidJson(msg)) {
                JSONObject jsonObject = JSON.parseObject(msg);
                //Todo 业务逻辑
//                PrintColor.color("green", "【接收】到的消息为JSON格式数据：msg=" + msg);
            } else {
                //Todo 业务逻辑
//                PrintColor.color("green","【接收】到的消息为非JSON格式数据：msg="+msg);
            }

            //客户端上线下线提醒 endsWith这俩后缀时msg必定为json格式,且会传来clientId
            if (topic.endsWith("disconnected")) {
                JSONObject jsonObject = JSON.parseObject(msg);
                String clientId = String.valueOf(jsonObject.get("clientid"));
                //Todo 掉线业务逻辑
                PrintColor.color("red", "客户端已掉线：clientId=" + clientId);
            } else if (topic.endsWith("connected")) {
                JSONObject jsonObject = JSON.parseObject(msg);
                String clientId = String.valueOf(jsonObject.get("clientid"));
                PrintColor.color("blue", "客户端已上线：clientId=" + clientId);
            }
        } catch (JSONException e) {
            log.error("JSON Format Parsing Exception : {}", msg);
        }
    }


    /**
     * 连接成功后的回调 可以在这个方法执行 订阅主题  生成Bean的 MqttConfiguration方法中订阅主题 出现bug
     * 重新连接后  主题也需要再次订阅  将重新订阅主题放在连接成功后的回调 比较合理
     *
     * @param reconnect
     * @param serverURI
     */
    @Override
    public void connectComplete(boolean reconnect, String serverURI) {
        log.info("MQTT 连接成功，连接方式：{}", reconnect ? "重连" : "直连");
        //订阅主题 配合yml配置文件:需要订阅几个主题就订阅几个
        // 解析订阅主题 enableTopic=1,2,3 表示只开启主题1,2,3
        String[] topics = mqttConfiguration.enableTopic.split(",");
        // 逐个订阅主题
        for (String topic : topics) {
            switch (topic) {
                case "1":
                    myMQTTClient.subscribe(mqttConfiguration.topic1, 1);
                    PrintColor.jin("【订阅】主题1：" + mqttConfiguration.topic1);
                    break;
                case "2":
                    myMQTTClient.subscribe(mqttConfiguration.topic2, 1);
                    PrintColor.jin("【订阅】主题2：" + mqttConfiguration.topic2);
                    break;
                case "3":
                    myMQTTClient.subscribe(mqttConfiguration.topic3, 1);
                    PrintColor.jin("【订阅】主题3：" + mqttConfiguration.topic3);
                    break;
                case "4":
                    myMQTTClient.subscribe(mqttConfiguration.topic4, 1);
                    PrintColor.jin("【订阅】主题4：" + mqttConfiguration.topic4);
                    break;
                default:
                    PrintColor.jin("未知的主题：" + topic);
            }
        }
    }

    /**
     * 消息到达后
     * subscribe后，执行的回调函数
     *
     * @param s
     * @param mqttMessage
     * @throws Exception
     */
    /**
     * publish后，配送完成后回调的方法
     *
     * @param iMqttDeliveryToken
     */
    @Override
    public void deliveryComplete(IMqttDeliveryToken iMqttDeliveryToken) {
        log.info("==========deliveryComplete={}==========", iMqttDeliveryToken.isComplete());
    }


    // 编写一个方法来验证JSON字符串的有效性
    private boolean isValidJson(String jsonStr) {
        try {
            JSON.parse(jsonStr);
            return true;
        } catch (JSONException e) {
            return false;
        }
    }
}

