package inks.service.sa.uts.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;

import java.io.StringWriter;

public class VelocityUtils {

    /**
     * 根据模板和数据生成最终渲染结果 在Velocity模板中用 $data.XXX 来获取信息
     *
     * @param template 模板字符串，支持 ${} 占位符
     * @param msgtext String数据对象，用于填充模板
     * @return 渲染后的字符串结果
     */
    public static String renderTemplateToData(String template, String msgtext) {
        // 创建 Velocity 上下文
        VelocityContext context = new VelocityContext();
        JSONObject jsonData = JSON.parseObject(msgtext);

        context.put("data", jsonData);  //在Velocity模板中用 $data.XXX 来获取信息

        // 初始化 Velocity 引擎
        VelocityEngine ve = new VelocityEngine();
        ve.init();

        // 渲染模板
        StringWriter writer = new StringWriter();
        ve.evaluate(context, writer, "VelocityUtils", template);

        return writer.toString();
    }
}
