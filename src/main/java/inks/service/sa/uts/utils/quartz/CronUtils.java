package inks.service.sa.uts.utils.quartz;

import org.quartz.CronExpression;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * cron表达式工具类
 * 
 * <AUTHOR>
 *
 */
public class CronUtils
{
    /**
     * 返回一个布尔值代表一个给定的Cron表达式的有效性
     *
     * @param cronExpression Cron表达式
     * @return boolean 表达式是否有效
     */
    public static boolean isValid(String cronExpression)
    {
        return CronExpression.isValidExpression(cronExpression);
    }

    /**
     * 返回一个字符串值,表示该消息无效Cron表达式给出有效性
     *
     * @param cronExpression Cron表达式
     * @return String 无效时返回表达式错误描述,如果有效返回null
     */
    public static String getInvalidMessage(String cronExpression)
    {
        try
        {
            new CronExpression(cronExpression);
            return null;
        }
        catch (ParseException pe)
        {
            return pe.getMessage();
        }
    }

    /**
     * 返回下一个执行时间根据给定的Cron表达式
     *
     * @param cronExpression Cron表达式
     * @return Date 下次Cron表达式执行时间
     */
    public static Date getNextExecution(String cronExpression)
    {
        try
        {
            CronExpression cron = new CronExpression(cronExpression);
            return cron.getNextValidTimeAfter(new Date(System.currentTimeMillis()));
        }
        catch (ParseException e)
        {
            throw new IllegalArgumentException(e.getMessage());
        }
    }
    private static final SimpleDateFormat FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * 根据 cron 表达式返回接下来的 3 次执行时间（格式化）
     *
     * @param cronExpression cron 表达式
     * @return List<String> 下 3 次执行时间（格式化字符串）
     * @throws IllegalArgumentException 如果 cron 表达式非法
     */
    public static List<String> getNextThreeExecutionTimes(String cronExpression) {
        try {
            CronExpression cron = new CronExpression(cronExpression);
            List<String> result = new ArrayList<>();
            Date next = new Date();
            for (int i = 0; i < 3; i++) {
                next = cron.getNextValidTimeAfter(next);
                if (next == null) {
                    break;
                }
                result.add(FORMAT.format(next));
            }
            return result;
        } catch (ParseException e) {
            throw new IllegalArgumentException("非法的 cron 表达式: " + cronExpression, e);
        }
    }

    public static void main(String[] args) {
        String cron = "0p 0..5a * * * ?"; // 这样也有效
        List<String> nextThreeExecutionTimes = CronUtils.getNextThreeExecutionTimes(cron);
        System.out.println(nextThreeExecutionTimes);
        boolean valid = isValid(cron);
        System.out.println("Cron表达式有效性: " + valid);
    }
}
