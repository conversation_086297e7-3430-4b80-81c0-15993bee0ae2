package inks.service.sa.uts.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;
import java.util.regex.Matcher;

/**
 * 正则表达式匹配工具类
 * 提供高级的正则表达式匹配功能，支持多种匹配模式和详细的匹配结果
 *
 * <AUTHOR>
 * @since 2025-07-19
 */
public class PatternMatcherUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(PatternMatcherUtil.class);
    
    /**
     * 匹配结果类
     */
    public static class MatchResult {
        private boolean matched;
        private String matchedText;
        private String errorMessage;
        private MatchType matchType;
        
        public MatchResult(boolean matched, String matchedText, String errorMessage, MatchType matchType) {
            this.matched = matched;
            this.matchedText = matchedText;
            this.errorMessage = errorMessage;
            this.matchType = matchType;
        }
        
        // Getters
        public boolean isMatched() { return matched; }
        public String getMatchedText() { return matchedText; }
        public String getErrorMessage() { return errorMessage; }
        public MatchType getMatchType() { return matchType; }
    }
    
    /**
     * 匹配类型枚举
     */
    public enum MatchType {
        EXACT_MATCH,    // 完全匹配 ^...$
        CONTAINS,       // 包含匹配 .*....*
        FIND,           // 查找匹配（默认）
        STARTS_WITH,    // 开头匹配 ^...
        ENDS_WITH       // 结尾匹配 ...$
    }
    
    /**
     * 智能正则匹配
     * 根据正则表达式的格式自动选择最合适的匹配模式
     *
     * @param pattern 正则表达式
     * @param text 要匹配的文本
     * @param caseSensitive 是否区分大小写
     * @return 匹配结果
     */
    public static MatchResult smartMatch(String pattern, String text, boolean caseSensitive) {
        if (pattern == null || pattern.trim().isEmpty()) {
            return new MatchResult(true, "", "Pattern is empty, treating as match", MatchType.FIND);
        }
        
        if (text == null) {
            text = "";
        }
        
        try {
            // 确定匹配类型
            MatchType matchType = determineMatchType(pattern);
            
            // 设置正则标志
            int flags = Pattern.DOTALL | Pattern.MULTILINE;
            if (!caseSensitive) {
                flags |= Pattern.CASE_INSENSITIVE;
            }
            
            Pattern compiledPattern = Pattern.compile(pattern, flags);
            Matcher matcher = compiledPattern.matcher(text);
            
            boolean matched = false;
            String matchedText = "";
            
            switch (matchType) {
                case EXACT_MATCH:
                    matched = matcher.matches();
                    if (matched) {
                        matchedText = text;
                    }
                    break;
                    
                case CONTAINS:
                case FIND:
                case STARTS_WITH:
                case ENDS_WITH:
                default:
                    matched = matcher.find();
                    if (matched) {
                        matchedText = matcher.group();
                    }
                    break;
            }
            
            logger.debug("Pattern matching: pattern='{}', text_length={}, type={}, matched={}", 
                    pattern, text.length(), matchType, matched);
            
            return new MatchResult(matched, matchedText, null, matchType);
            
        } catch (PatternSyntaxException e) {
            logger.error("Invalid regex pattern: {}", pattern, e);
            return new MatchResult(false, "", "Invalid regex pattern: " + e.getMessage(), MatchType.FIND);
        } catch (Exception e) {
            logger.error("Error during pattern matching: {}", pattern, e);
            return new MatchResult(false, "", "Pattern matching error: " + e.getMessage(), MatchType.FIND);
        }
    }
    
    /**
     * 根据正则表达式格式确定匹配类型
     */
    private static MatchType determineMatchType(String pattern) {
        if (pattern.startsWith("^") && pattern.endsWith("$")) {
            return MatchType.EXACT_MATCH;
        } else if (pattern.startsWith("^")) {
            return MatchType.STARTS_WITH;
        } else if (pattern.endsWith("$")) {
            return MatchType.ENDS_WITH;
        } else if (pattern.startsWith(".*") && pattern.endsWith(".*")) {
            return MatchType.CONTAINS;
        } else {
            return MatchType.FIND;
        }
    }
    
    /**
     * 成功模式匹配
     * 专门用于匹配命令执行成功的模式
     */
    public static MatchResult matchSuccessPattern(String pattern, String output, String error) {
        String fullText = combineOutputAndError(output, error);
        return smartMatch(pattern, fullText, false); // 成功模式通常不区分大小写
    }
    
    /**
     * 错误模式匹配
     * 专门用于匹配命令执行错误的模式
     */
    public static MatchResult matchErrorPattern(String pattern, String output, String error) {
        String fullText = combineOutputAndError(output, error);
        return smartMatch(pattern, fullText, false); // 错误模式通常不区分大小写
    }
    
    /**
     * 组合输出和错误信息
     */
    private static String combineOutputAndError(String output, String error) {
        StringBuilder sb = new StringBuilder();
        if (output != null && !output.trim().isEmpty()) {
            sb.append(output.trim());
        }
        if (error != null && !error.trim().isEmpty()) {
            if (sb.length() > 0) {
                sb.append("\n");
            }
            sb.append(error.trim());
        }
        return sb.toString();
    }
    
    /**
     * 验证正则表达式是否有效
     */
    public static boolean isValidPattern(String pattern) {
        if (pattern == null || pattern.trim().isEmpty()) {
            return true; // 空模式被认为是有效的
        }
        
        try {
            Pattern.compile(pattern);
            return true;
        } catch (PatternSyntaxException e) {
            logger.warn("Invalid regex pattern: {}", pattern, e);
            return false;
        }
    }
    
    /**
     * 获取正则表达式的建议
     * 为常见的匹配需求提供正则表达式建议
     */
    public static String getPatternSuggestion(String description) {
        switch (description.toLowerCase()) {
            case "success":
            case "成功":
                return "(?i).*(success|successful|completed|done|ok).*";
                
            case "error":
            case "错误":
                return "(?i).*(error|fail|failed|exception|denied|not found).*";
                
            case "timeout":
            case "超时":
                return "(?i).*(timeout|timed out|time out).*";
                
            case "permission":
            case "权限":
                return "(?i).*(permission denied|access denied|unauthorized).*";
                
            case "network":
            case "网络":
                return "(?i).*(network|connection|unreachable|host).*";
                
            default:
                return ".*" + description + ".*";
        }
    }
}
