package inks.service.sa.uts.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.SaSshserversPojo;
import inks.service.sa.uts.domain.SaSshserversEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * SSH服务器配置(SaSshservers)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-19 11:11:06
 */
@Mapper
public interface SaSshserversMapper {


    SaSshserversPojo getEntity(@Param("key") String key);

    List<SaSshserversPojo> getPageList(QueryParam queryParam);

    int insert(SaSshserversEntity saSshserversEntity);

    int update(SaSshserversEntity saSshserversEntity);

    int delete(@Param("key") String key);
    
}

