package inks.service.sa.uts.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.UtsDatabaseEntity;
import inks.service.sa.uts.domain.pojo.UtsDatabasePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据库连接池(Uts_Database)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-11 15:28:20
 */
@Mapper
public interface UtsDatabaseMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    UtsDatabasePojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<UtsDatabasePojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param utsDatabaseEntity 实例对象
     * @return 影响行数
     */
    int insert(UtsDatabaseEntity utsDatabaseEntity);

    
    /**
     * 修改数据
     *
     * @param utsDatabaseEntity 实例对象
     * @return 影响行数
     */
    int update(UtsDatabaseEntity utsDatabaseEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    String getUrlDatabaseType(String databaseid, String tid);
}

