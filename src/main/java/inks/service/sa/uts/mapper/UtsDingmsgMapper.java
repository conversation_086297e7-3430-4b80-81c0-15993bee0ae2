package inks.service.sa.uts.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.UtsDingmsgEntity;
import inks.service.sa.uts.domain.pojo.UtsDingmsgPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 钉钉信息(UtsDingmsg)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-01-10 11:11:53
 */
@Mapper
public interface UtsDingmsgMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    UtsDingmsgPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<UtsDingmsgPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param utsDingmsgEntity 实例对象
     * @return 影响行数
     */
    int insert(UtsDingmsgEntity utsDingmsgEntity);


    /**
     * 修改数据
     *
     * @param utsDingmsgEntity 实例对象
     * @return 影响行数
     */
    int update(UtsDingmsgEntity utsDingmsgEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    UtsDingmsgPojo getEntityByMsgCode(@Param("key") String key, @Param("tid") String tid);

    String getDingUserIdByOmsUserid(String omsUserid, String tid);
}

