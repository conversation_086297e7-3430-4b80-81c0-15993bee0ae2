package inks.service.sa.uts.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.SaJoblogPojo;
import inks.service.sa.uts.domain.SaJoblogEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 定时任务调度日志表(SaJoblog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-07 16:07:35
 */
@Mapper
public interface SaJoblogMapper {


    SaJoblogPojo getEntity(@Param("key") String key);

    List<SaJoblogPojo> getPageList(QueryParam queryParam);

    int insert(SaJoblogEntity saJoblogEntity);

    int update(SaJoblogEntity saJoblogEntity);

    int delete(@Param("key") String key);

    int deleteJobLogByIds(@Param("jobLogIds") String[] jobLogIds);

    int cleanJobLog();
}

