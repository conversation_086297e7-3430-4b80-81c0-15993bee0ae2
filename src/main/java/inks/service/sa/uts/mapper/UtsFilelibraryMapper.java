package inks.service.sa.uts.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.UtsFilelibraryPojo;
import inks.service.sa.uts.domain.UtsFilelibraryEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 文件库(UtsFilelibrary)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-17 16:51:51
 */
@Mapper
public interface UtsFilelibraryMapper {


    UtsFilelibraryPojo getEntity(@Param("key") String key);

    List<UtsFilelibraryPojo> getPageList(QueryParam queryParam);

    int insert(UtsFilelibraryEntity utsFilelibraryEntity);

    int update(UtsFilelibraryEntity utsFilelibraryEntity);

    int delete(@Param("key") String key);
    
}

