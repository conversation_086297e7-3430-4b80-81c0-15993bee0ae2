package inks.service.sa.uts.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.UtsWxemsgEntity;
import inks.service.sa.uts.domain.pojo.UtsWxemsgPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 企业微信信息(UtsWxemsg)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-01-10 11:12:38
 */
@Mapper
public interface UtsWxemsgMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    UtsWxemsgPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<UtsWxemsgPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param utsWxemsgEntity 实例对象
     * @return 影响行数
     */
    int insert(UtsWxemsgEntity utsWxemsgEntity);


    /**
     * 修改数据
     *
     * @param utsWxemsgEntity 实例对象
     * @return 影响行数
     */
    int update(UtsWxemsgEntity utsWxemsgEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    UtsWxemsgPojo getEntityByMsgCode(@Param("key") String key, @Param("tid") String tid);

    String getWxeUserIdByOmsUserid(String omsUserid, String tid);
}

