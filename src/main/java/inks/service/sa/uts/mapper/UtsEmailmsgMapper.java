package inks.service.sa.uts.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.UtsEmailmsgPojo;
import inks.service.sa.uts.domain.UtsEmailmsgEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 邮件信息(UtsEmailmsg)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-02-28 14:01:05
 */
@Mapper
public interface UtsEmailmsgMapper {


    UtsEmailmsgPojo getEntity(@Param("key") String key);

    List<UtsEmailmsgPojo> getPageList(QueryParam queryParam);

    int insert(UtsEmailmsgEntity utsEmailmsgEntity);

    int update(UtsEmailmsgEntity utsEmailmsgEntity);

    int delete(@Param("key") String key);

    UtsEmailmsgPojo getEntityByMsgCode(String msgCode, String tid);
}

