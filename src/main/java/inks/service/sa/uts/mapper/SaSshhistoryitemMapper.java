package inks.service.sa.uts.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.SaSshhistoryitemPojo;
import inks.service.sa.uts.domain.SaSshhistoryitemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * SSH流水线历史步骤(SaSshhistoryitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-19 11:11:44
 */
 @Mapper
public interface SaSshhistoryitemMapper {

    SaSshhistoryitemPojo getEntity(@Param("key") String key);

    List<SaSshhistoryitemPojo> getPageList(QueryParam queryParam);

    List<SaSshhistoryitemPojo> getList(@Param("Pid") String Pid);    

    int insert(SaSshhistoryitemEntity saSshhistoryitemEntity);

    int update(SaSshhistoryitemEntity saSshhistoryitemEntity);

    int delete(@Param("key") String key);

}

