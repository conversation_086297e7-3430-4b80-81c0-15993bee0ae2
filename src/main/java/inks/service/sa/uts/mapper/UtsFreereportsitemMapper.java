package inks.service.sa.uts.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.UtsFreereportsitemEntity;
import inks.service.sa.uts.domain.pojo.UtsFreereportsitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 自由报表项目(UtsFreereportsitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-15 11:29:57
 */
 @Mapper
public interface UtsFreereportsitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    UtsFreereportsitemPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<UtsFreereportsitemPojo> getPageList(QueryParam queryParam);

     /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<UtsFreereportsitemPojo> getList(@Param("Pid") String Pid,@Param("tid") String tid);    
     
    
    /**
     * 新增数据
     *
     * @param utsFreereportsitemEntity 实例对象
     * @return 影响行数
     */
    int insert(UtsFreereportsitemEntity utsFreereportsitemEntity);

    
    /**
     * 修改数据
     *
     * @param utsFreereportsitemEntity 实例对象
     * @return 影响行数
     */
    int update(UtsFreereportsitemEntity utsFreereportsitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    List<UtsFreereportsitemPojo> getListByReportCode(String reportcode, String tid);
}

