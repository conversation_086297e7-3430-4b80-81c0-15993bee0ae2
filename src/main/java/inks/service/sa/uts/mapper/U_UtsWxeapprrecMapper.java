package inks.service.sa.uts.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.UtsWxeapprrecPojo;
import inks.service.sa.uts.domain.UtsWxeapprrecEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 微信审批记录(UtsWxeapprrec)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-12-16 14:12:14
 */
@Mapper
public interface U_UtsWxeapprrecMapper {


    UtsWxeapprrecPojo getEntity(@Param("key") String key);

    List<UtsWxeapprrecPojo> getPageList(QueryParam queryParam);

    int insert(UtsWxeapprrecEntity utsWxeapprrecEntity);

    int update(UtsWxeapprrecEntity utsWxeapprrecEntity);

    int delete(@Param("key") String key);


    UtsWxeapprrecPojo getEntityBySpno(@Param("key") String key, @Param("tid") String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    List<UtsWxeapprrecPojo> getOnlineByBillid(@Param("key") String key, @Param("tid") String tid);
}

