package inks.service.sa.uts.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.UtsWxeapprPojo;
import inks.service.sa.uts.domain.UtsWxeapprEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 企业微审核(UtsWxeappr)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-12-16 14:12:14
 */
@Mapper
public interface UtsWxeapprMapper {


    UtsWxeapprPojo getEntity(@Param("key") String key);

    List<UtsWxeapprPojo> getPageList(QueryParam queryParam);

    int insert(UtsWxeapprEntity utsWxeapprEntity);

    int update(UtsWxeapprEntity utsWxeapprEntity);

    int delete(@Param("key") String key);

    List<UtsWxeapprPojo> getListByModuleCode(String moduleCode, String tid);
}

