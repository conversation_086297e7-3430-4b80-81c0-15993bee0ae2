package inks.service.sa.uts.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.UtsSqlactuatorEntity;
import inks.service.sa.uts.domain.pojo.UtsSqlactuatorPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * SQL执行器(UtsSqlactuator)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-10-06 16:23:35
 */
@Mapper
public interface UtsSqlactuatorMapper {


    UtsSqlactuatorPojo getEntity(@Param("key") String key);

    List<UtsSqlactuatorPojo> getPageList(QueryParam queryParam);

    int insert(UtsSqlactuatorEntity utsSqlactuatorEntity);

    int update(UtsSqlactuatorEntity utsSqlactuatorEntity);

    int delete(@Param("key") String key);

    UtsSqlactuatorPojo getMaxEntity(String code);
}

