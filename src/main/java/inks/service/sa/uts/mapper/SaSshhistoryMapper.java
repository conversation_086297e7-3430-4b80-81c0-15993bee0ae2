package inks.service.sa.uts.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.SaSshhistoryPojo;
import inks.service.sa.uts.domain.pojo.SaSshhistoryitemdetailPojo;
import inks.service.sa.uts.domain.SaSshhistoryEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * SSH流水线执行历史(SaSshhistory)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-19 11:11:27
 */
@Mapper
public interface SaSshhistoryMapper {

    SaSshhistoryPojo getEntity(@Param("key") String key);

    List<SaSshhistoryitemdetailPojo> getPageList(QueryParam queryParam);

    List<SaSshhistoryPojo> getPageTh(QueryParam queryParam);

    int insert(SaSshhistoryEntity saSshhistoryEntity);

    int update(SaSshhistoryEntity saSshhistoryEntity);

    int delete(@Param("key") String key);

     List<String> getDelItemIds(SaSshhistoryPojo saSshhistoryPojo);
}

