package inks.service.sa.uts.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.UtsVideolibraryPojo;
import inks.service.sa.uts.domain.UtsVideolibraryEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 视频信息表(UtsVideolibrary)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-29 14:16:34
 */
@Mapper
public interface UtsVideolibraryMapper {


    UtsVideolibraryPojo getEntity(@Param("key") String key);

    List<UtsVideolibraryPojo> getPageList(QueryParam queryParam);

    int insert(UtsVideolibraryEntity utsVideolibraryEntity);

    int update(UtsVideolibraryEntity utsVideolibraryEntity);

    int delete(@Param("key") String key);
    

    int approval(UtsVideolibraryEntity utsVideolibraryEntity);
}

