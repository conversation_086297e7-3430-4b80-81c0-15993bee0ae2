package inks.service.sa.uts.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.UtsMqttmsgEntity;
import inks.service.sa.uts.domain.pojo.UtsMqttmsgPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * MQTT信息(UtsMqttmsg)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-07-25 16:07:02
 */
@Mapper
public interface UtsMqttmsgMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    UtsMqttmsgPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<UtsMqttmsgPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param utsMqttmsgEntity 实例对象
     * @return 影响行数
     */
    int insert(UtsMqttmsgEntity utsMqttmsgEntity);


    /**
     * 修改数据
     *
     * @param utsMqttmsgEntity 实例对象
     * @return 影响行数
     */
    int update(UtsMqttmsgEntity utsMqttmsgEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    List<UtsMqttmsgPojo> getListByModuleCode(@Param("moduleCode") String moduleCode, @Param("tid") String tid);

    UtsMqttmsgPojo getEntityByMsgCode(@Param("msgCode") String msgCode, @Param("tid") String tid);
}

