package inks.service.sa.uts.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.UtsBackupconfigPojo;
import inks.service.sa.uts.domain.UtsBackupconfigEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 数据库备份配置(UtsBackupconfig)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-02-11 16:52:45
 */
@Mapper
public interface UtsBackupconfigMapper {


    UtsBackupconfigPojo getEntity(@Param("key") String key);

    List<UtsBackupconfigPojo> getPageList(QueryParam queryParam);

    int insert(UtsBackupconfigEntity utsBackupconfigEntity);

    int update(UtsBackupconfigEntity utsBackupconfigEntity);

    int delete(@Param("key") String key);

    List<UtsBackupconfigPojo> getEnableList();

    List<UtsBackupconfigPojo> getEnableAndCronList();
}

