package inks.service.sa.uts.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.UtsInfoagentPojo;
import inks.service.sa.uts.domain.UtsInfoagentEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 信息代理中心(UtsInfoagent)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-02-28 13:12:39
 */
@Mapper
public interface UtsInfoagentMapper {


    UtsInfoagentPojo getEntity(@Param("key") String key);

    List<UtsInfoagentPojo> getPageList(QueryParam queryParam);

    int insert(UtsInfoagentEntity utsInfoagentEntity);

    int update(UtsInfoagentEntity utsInfoagentEntity);

    int delete(@Param("key") String key);

    UtsInfoagentPojo getEntityByMsgCode(String code, String tid);
}

