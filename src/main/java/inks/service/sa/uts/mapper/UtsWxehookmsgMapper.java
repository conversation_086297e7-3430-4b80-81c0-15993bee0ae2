package inks.service.sa.uts.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.UtsWxehookmsgPojo;
import inks.service.sa.uts.domain.UtsWxehookmsgEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 企业微信群机器人信息(UtsWxehookmsg)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-02-28 13:12:38
 */
@Mapper
public interface UtsWxehookmsgMapper {


    UtsWxehookmsgPojo getEntity(@Param("key") String key);

    List<UtsWxehookmsgPojo> getPageList(QueryParam queryParam);

    int insert(UtsWxehookmsgEntity utsWxehookmsgEntity);

    int update(UtsWxehookmsgEntity utsWxehookmsgEntity);

    int delete(@Param("key") String key);

    UtsWxehookmsgPojo getEntityByMsgCode(String msgCode, String tid);
}

