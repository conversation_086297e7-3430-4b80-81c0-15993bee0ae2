package inks.service.sa.uts.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.UtsFreereportsEntity;
import inks.service.sa.uts.domain.pojo.UtsFreereportsPojo;
import inks.service.sa.uts.domain.pojo.UtsFreereportsitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 自由报表(UtsFreereports)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-14 10:14:36
 */
@Mapper
public interface UtsFreereportsMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    UtsFreereportsPojo getEntity(@Param("key") String key,@Param("tid") String tid);
    UtsFreereportsPojo getEntityByReportcode(String reportcode, String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<UtsFreereportsitemdetailPojo> getPageList(QueryParam queryParam);


        /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<UtsFreereportsPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param utsFreereportsEntity 实例对象
     * @return 影响行数
     */
    int insert(UtsFreereportsEntity utsFreereportsEntity);


    /**
     * 修改数据
     *
     * @param utsFreereportsEntity 实例对象
     * @return 影响行数
     */
    int update(UtsFreereportsEntity utsFreereportsEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

     /**
     * 查询 被删除的Item
     *
     * @param utsFreereportsPojo 筛选条件
     * @return 查询结果
     */
     List<String> getDelItemIds(UtsFreereportsPojo utsFreereportsPojo);


    List<UtsFreereportsPojo> getListBySelf(@Param("domainnum") int domainnum,@Param("userid") String userid, @Param("tid") String tid);

    List<UtsFreereportsPojo> myFreeReports(String userid, String tid);

    int countReportcode(String reportcode,String id, String tid);
}

