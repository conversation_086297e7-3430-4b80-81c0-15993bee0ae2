package inks.service.sa.uts.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.UtsDinghookmsgPojo;
import inks.service.sa.uts.domain.UtsDinghookmsgEntity;
import inks.service.sa.uts.domain.pojo.UtsWxehookmsgPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 钉钉群机器人信息(UtsDinghookmsg)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-02-28 13:12:38
 */
@Mapper
public interface UtsDinghookmsgMapper {


    UtsDinghookmsgPojo getEntity(@Param("key") String key);

    List<UtsDinghookmsgPojo> getPageList(QueryParam queryParam);

    int insert(UtsDinghookmsgEntity utsDinghookmsgEntity);

    int update(UtsDinghookmsgEntity utsDinghookmsgEntity);

    int delete(@Param("key") String key);

    UtsWxehookmsgPojo getEntityByMsgCode(String msgCode, String tid);
}

