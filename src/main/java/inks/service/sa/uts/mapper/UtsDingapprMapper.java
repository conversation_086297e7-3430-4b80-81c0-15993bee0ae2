package inks.service.sa.uts.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.UtsDingapprPojo;
import inks.service.sa.uts.domain.UtsDingapprEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 钉钉审批(UtsDingappr)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-12-16 14:12:14
 */
@Mapper
public interface UtsDingapprMapper {


    UtsDingapprPojo getEntity(@Param("key") String key);

    List<UtsDingapprPojo> getPageList(QueryParam queryParam);

    int insert(UtsDingapprEntity utsDingapprEntity);

    int update(UtsDingapprEntity utsDingapprEntity);

    int delete(@Param("key") String key);

    List<UtsDingapprPojo> getListByModuleCode(String moduleCode, String tid);
}

