package inks.service.sa.uts.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.SaSshpipelinesitemPojo;
import inks.service.sa.uts.domain.SaSshpipelinesitemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * SSH流水线步骤(SaSshpipelinesitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-19 11:11:44
 */
 @Mapper
public interface SaSshpipelinesitemMapper {

    SaSshpipelinesitemPojo getEntity(@Param("key") String key);

    List<SaSshpipelinesitemPojo> getPageList(QueryParam queryParam);

    List<SaSshpipelinesitemPojo> getList(@Param("Pid") String Pid);    

    int insert(SaSshpipelinesitemEntity saSshpipelinesitemEntity);

    int update(SaSshpipelinesitemEntity saSshpipelinesitemEntity);

    int delete(@Param("key") String key);

}

