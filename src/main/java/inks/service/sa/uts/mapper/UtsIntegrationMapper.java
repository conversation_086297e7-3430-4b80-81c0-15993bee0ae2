package inks.service.sa.uts.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.UtsIntegrationPojo;
import inks.service.sa.uts.domain.UtsIntegrationEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * API整合转发(UtsIntegration)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-20 13:06:40
 */
@Mapper
public interface UtsIntegrationMapper {


    UtsIntegrationPojo getEntity(@Param("key") String key);

    List<UtsIntegrationPojo> getPageList(QueryParam queryParam);

    int insert(UtsIntegrationEntity utsIntegrationEntity);

    int update(UtsIntegrationEntity utsIntegrationEntity);

    int delete(@Param("key") String key);

    UtsIntegrationPojo getEntityByCodeAndType(String code, Integer proxytype);
}

