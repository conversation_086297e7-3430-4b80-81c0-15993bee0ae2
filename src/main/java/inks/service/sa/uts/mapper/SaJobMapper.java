package inks.service.sa.uts.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.SaJobPojo;
import inks.service.sa.uts.domain.SaJobEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 定时任务调度表(SaJob)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-07 16:07:17
 */
@Mapper
public interface SaJobMapper {


    SaJobPojo getEntity(@Param("key") String key);

    List<SaJobPojo> getPageList(QueryParam queryParam);

    int insert(SaJobEntity saJobEntity);

    int update(SaJobEntity saJobEntity);

    int delete(@Param("key") String key);

    List<SaJobPojo> selectJobAll();
}

