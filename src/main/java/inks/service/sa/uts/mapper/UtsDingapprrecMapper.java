package inks.service.sa.uts.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.UtsDingapprrecPojo;
import inks.service.sa.uts.domain.UtsDingapprrecEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 钉钉审批记录(UtsDingapprrec)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-12-16 14:12:14
 */
@Mapper
public interface UtsDingapprrecMapper {


    UtsDingapprrecPojo getEntity(@Param("key") String key);

    List<UtsDingapprrecPojo> getPageList(QueryParam queryParam);

    int insert(UtsDingapprrecEntity utsDingapprrecEntity);

    int update(UtsDingapprrecEntity utsDingapprrecEntity);

    int delete(@Param("key") String key);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    UtsDingapprrecPojo getEntityBySpno(@Param("key") String key, @Param("tid") String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    List<UtsDingapprrecPojo> getOnlineByBillid(@Param("key") String key, @Param("tid") String tid);
}

