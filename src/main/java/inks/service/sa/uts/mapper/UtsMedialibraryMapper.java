package inks.service.sa.uts.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.UtsMedialibraryPojo;
import inks.service.sa.uts.domain.UtsMedialibraryEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 素材库(UtsMedialibrary)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-13 15:45:12
 */
@Mapper
public interface UtsMedialibraryMapper {


    UtsMedialibraryPojo getEntity(@Param("key") String key);

    List<UtsMedialibraryPojo> getPageList(QueryParam queryParam);

    int insert(UtsMedialibraryEntity utsMedialibraryEntity);

    int update(UtsMedialibraryEntity utsMedialibraryEntity);

    int delete(@Param("key") String key);
    
}

