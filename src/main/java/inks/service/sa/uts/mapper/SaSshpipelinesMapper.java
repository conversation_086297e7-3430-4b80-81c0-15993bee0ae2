package inks.service.sa.uts.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.SaSshpipelinesPojo;
import inks.service.sa.uts.domain.pojo.SaSshpipelinesitemdetailPojo;
import inks.service.sa.uts.domain.SaSshpipelinesEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * SSH流水线(SaSshpipelines)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-19 11:11:27
 */
@Mapper
public interface SaSshpipelinesMapper {

    SaSshpipelinesPojo getEntity(@Param("key") String key);

    List<SaSshpipelinesitemdetailPojo> getPageList(QueryParam queryParam);

    List<SaSshpipelinesPojo> getPageTh(QueryParam queryParam);

    int insert(SaSshpipelinesEntity saSshpipelinesEntity);

    int update(SaSshpipelinesEntity saSshpipelinesEntity);

    int delete(@Param("key") String key);

     List<String> getDelItemIds(SaSshpipelinesPojo saSshpipelinesPojo);

    List<SaSshpipelinesPojo> getListByAll(SaSshpipelinesPojo saSshpipelinesPojo);
}

