package inks.service.sa.uts.config;

import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.config.impl.WxCpDefaultConfigImpl;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class WxCpServiceConfig {

    @Value("${inks.wx.cp.webhookUrl:https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=fdef02ed-52e9-48ec-b2f4-7deb58c3e470}")
    private String webhookUrl;
    @Bean
    public WxCpService wxCpService() {
        WxCpDefaultConfigImpl config = new WxCpDefaultConfigImpl();
        //只要key=后面的
        String webhookKey = webhookUrl.substring(webhookUrl.lastIndexOf("key=") + 4);
        config.setWebhookKey(webhookKey);
        WxCpService service = new WxCpServiceImpl();
        service.setWxCpConfigStorage(config);
        return service;
    }
}