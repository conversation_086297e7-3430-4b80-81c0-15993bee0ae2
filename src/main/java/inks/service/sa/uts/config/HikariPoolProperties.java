package inks.service.sa.uts.config;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.pool.HikariPool;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Created by raodeming on 2021/8/6.
 */
@Component
@ConfigurationProperties(prefix = "spring.datasource.hikari")
//@Data
public class HikariPoolProperties extends HikariConfig {

    // 构造方法中设置默认值
    public HikariPoolProperties() {
        // 设置默认值
        setConnectionTimeout(30000);   // 默认连接超时30秒
        setValidationTimeout(5000);    // 默认验证超时5秒
        setIdleTimeout(600000);        // 默认闲置超时10分钟
        setMaxLifetime(1800000);       // 默认连接存活时间30分钟
        setMaximumPoolSize(10);        // 默认最大连接数10
        setMinimumIdle(5);             // 默认最小空闲连接数5
    }

    public HikariPool dataSource(String url, String username, String password, String driverClassName) {
        HikariConfig hikariConfig = new HikariConfig();
        hikariConfig.setJdbcUrl(url);
        hikariConfig.setUsername(username);
        hikariConfig.setPassword(password);
        hikariConfig.setDriverClassName(driverClassName);

        hikariConfig.setConnectionTimeout(getConnectionTimeout());
        hikariConfig.setValidationTimeout(getValidationTimeout());
        hikariConfig.setIdleTimeout(getIdleTimeout());
        hikariConfig.setMaxLifetime(getMaxLifetime());
        hikariConfig.setMaximumPoolSize(getMaximumPoolSize());
        hikariConfig.setMinimumIdle(getMinimumIdle());
        return new HikariPool(hikariConfig);
    }

}
