package inks.service.sa.uts.config.constant;

/**
 * Created by raodeming on 2021/3/19.
 */
public class JdbcConstants {
    /**
     * 已支持的数据源
     */
    public final static String KUDU_IMAPLA = "kudu_impala";
    public final static String HTTP = "http";
    public final static String MYSQL = "mysql";
    public final static String ORACLE = "oracle";
    public final static String ELASTIC_SEARCH_SQL = "elasticsearch_sql";
    public final static String SQL_SERVER = "mssqlserver";
    public final static String JDBC = "jdbc";
    public final static String POSTGRESQL = "postgresql";


    public final static String JTDS = "jtds";
    public final static String MOCK = "mock";
    public final static String HSQL = "hsql";
    public final static String DB2 = "db2";
    public final static String DB2_DRIVER = "COM.ibm.db2.jdbc.app.DB2Driver";
    public final static String POSTGRESQL_DRIVER = "org.postgresql.Driver";
    public final static String SYBASE = "sybase";
    public final static String SQL_SERVER_DRIVER = "com.microsoft.jdbc.sqlserver.SQLServerDriver";
    public final static String SQL_SERVER_DRIVER_SQLJDBC4 = "com.microsoft.sqlserver.jdbc.SQLServerDriver";
    public final static String SQL_SERVER_DRIVER_JTDS = "net.sourceforge.jtds.jdbc.Driver";
    public final static String ORACLE_DRIVER = "oracle.jdbc.OracleDriver";
    public final static String ORACLE_DRIVER2 = "oracle.jdbc.driver.OracleDriver";
    public final static String ALI_ORACLE = "AliOracle";
    public final static String ALI_ORACLE_DRIVER = "com.alibaba.jdbc.AlibabaDriver";
    public final static String MYSQL_DRIVER = "com.mysql.jdbc.Driver";
    public final static String MYSQL_DRIVER_6 = "com.mysql.cj.jdbc.Driver";
    public final static String MYSQL_DRIVER_REPLICATE = "com.mysql.jdbc.";
    public final static String MARIADB = "mariadb";
    public final static String MARIADB_DRIVER = "org.mariadb.jdbc.Driver";
    public final static String DERBY = "derby";
    public final static String HBASE = "hbase";
    public final static String HIVE = "hive";
    public final static String HIVE_DRIVER = "org.apache.hive.jdbc.HiveDriver";
    public final static String H2 = "h2";
    public final static String H2_DRIVER = "org.h2.Driver";
    public final static String DM = "dm";
    public final static String DM_DRIVER = "dm.jdbc.driver.DmDriver";
    public final static String KINGBASE = "kingbase";
    public final static String KINGBASE_DRIVER = "com.kingbase.Driver";
    public final static String GBASE = "gbase";
    public final static String GBASE_DRIVER = "com.gbase.jdbc.Driver";
    public final static String XUGU = "xugu";
    public final static String XUGU_DRIVER = "com.xugu.cloudjdbc.Driver";
    public final static String OCEANBASE = "oceanbase";
    public final static String OCEANBASE_DRIVER = "com.mysql.jdbc.Driver";
    public final static String INFORMIX = "informix";
    public final static String ODPS = "odps";
    public final static String ODPS_DRIVER = "com.aliyun.odps.jdbc.OdpsDriver";
    public final static String TERADATA = "teradata";
    public final static String TERADATA_DRIVER = "com.teradata.jdbc.TeraDriver";
    public final static String LOG4JDBC = "log4jdbc";
    public final static String LOG4JDBC_DRIVER = "net.sf.log4jdbc.DriverSpy";
    public final static String PHOENIX = "phoenix";
    public final static String PHOENIX_DRIVER = "org.apache.phoenix.jdbc.PhoenixDriver";
    public final static String ENTERPRISEDB = "edb";
    public final static String ENTERPRISEDB_DRIVER = "com.edb.Driver";
    public final static String KYLIN = "kylin";
    public final static String KYLIN_DRIVER = "org.apache.kylin.jdbc.Driver";
    public final static String SQLITE = "sqlite";
    public final static String SQLITE_DRIVER = "org.sqlite.JDBC";
    public final static String ALIYUN_ADS = "aliyun_ads";
    public final static String ALIYUN_DRDS = "aliyun_drds";
    public final static String PRESTO = "presto";
    public final static String ELASTIC_SEARCH = "elasticsearch";
    public final static String ELASTIC_SEARCH_DRIVER = "com.alibaba.xdriver.elastic.jdbc.ElasticDriver";
    public final static String CLICKHOUSE = "clickhouse";
    public final static String CLICKHOUSE_DRIVER = "ru.yandex.clickhouse.ClickHouseDriver";

}
