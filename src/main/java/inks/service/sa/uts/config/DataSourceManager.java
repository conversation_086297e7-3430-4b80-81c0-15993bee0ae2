package inks.service.sa.uts.config;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import inks.sa.common.core.utils.PrintColor;
import inks.service.sa.uts.domain.database.DataSourceConfig;
import inks.service.sa.uts.domain.pojo.UtsDatabasePojo;
import inks.service.sa.uts.mapper.UtsDatabaseMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.sql.DataSource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

// 数据源管理器
// DataSourceManager.java
@Component
public class DataSourceManager {
    @Resource
    private UtsDatabaseMapper utsDatabaseMapper;
    // 本机数据库连接信息:
    @Value("${spring.datasource.url}")
    private String LocalUrl;
    @Value("${spring.datasource.username}")
    private String LocalUsername;
    @Value("${spring.datasource.password}")
    private String LocalPassword;
    @Value("${spring.datasource.driver-class-name}")
    private String LocalDriverClassName;
    private final Map<String, DataSource> dataSources = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        // 初始化默认数据源
        DataSourceConfig defaultConfig = new DataSourceConfig();
        defaultConfig.setUrl(LocalUrl);
        defaultConfig.setUsername(LocalUsername);
        defaultConfig.setPassword(LocalPassword);
        defaultConfig.setDriverClassName(LocalDriverClassName);

        //// 设置连接池参数 默认值
        //defaultConfig.setMaxPoolSize(10);
        //defaultConfig.setMinIdle(5);
        //defaultConfig.setMaxLifetime(1800000); // 30分钟

        // 添加默认数据源
        this.addDataSource("default", defaultConfig);
    }


    public DataSource getDataSource(String dataSourceKey) {
        if (StringUtils.isBlank(dataSourceKey)) {
            dataSourceKey = "default"; // 如果数据源key为空，则使用默认数据源
        }
        // 尝试取出数据源
        DataSource dataSource = dataSources.get(dataSourceKey);

        // 如果数据源存在
        if (dataSource != null) {
            PrintColor.zi("取出已有数据源【databaseid:" + dataSourceKey + "】");
            return dataSource; // 直接返回已存在的数据源
        }

        // 如果数据源不存在：新建数据源
        UtsDatabasePojo databasePojo = utsDatabaseMapper.getEntity(dataSourceKey, null);
        // 检查查询结果是否有效
        if (databasePojo == null) {
            PrintColor.zi("数据源未找到【databaseid:" + dataSourceKey + "】");
            return null; // 返回null表示数据源未找到
        }
        // 创建新的数据源配置
        DataSourceConfig dataSourceConfig = new DataSourceConfig();
        dataSourceConfig.setUrl(databasePojo.getUrl());
        dataSourceConfig.setUsername(databasePojo.getUsername());
        dataSourceConfig.setPassword(databasePojo.getPassword());
        dataSourceConfig.setDriverClassName(databasePojo.getDriverclassname());
        // 尝试添加新的数据源，处理可能的异常
        try {
            this.addDataSource(dataSourceKey, dataSourceConfig);
        } catch (Exception e) {
            PrintColor.zi("创建数据源失败【databaseid:" + dataSourceKey + "】, 异常信息：" + e.getMessage());
            return null; // 如果添加数据源失败，返回null
        }
        PrintColor.zi("创建新数据源【databaseid:" + dataSourceKey + "】");
        // 再次尝试获取新添加的数据源
        return dataSources.get(dataSourceKey);
    }


    public void addDataSource(String key, DataSourceConfig config) {
        HikariConfig hikariConfig = new HikariConfig();
        hikariConfig.setJdbcUrl(config.getUrl());
        hikariConfig.setUsername(config.getUsername());
        hikariConfig.setPassword(config.getPassword());
        hikariConfig.setDriverClassName(config.getDriverClassName());

        hikariConfig.setMaximumPoolSize(config.getMaxPoolSize());
        hikariConfig.setMinimumIdle(config.getMinIdle());
        hikariConfig.setMaxLifetime(config.getMaxLifetime());

        dataSources.put(key, new HikariDataSource(hikariConfig));
    }
}