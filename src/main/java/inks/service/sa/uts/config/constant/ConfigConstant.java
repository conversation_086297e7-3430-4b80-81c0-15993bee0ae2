package inks.service.sa.uts.config.constant;

public class ConfigConstant {
    // 钉钉 企业CorpId
    public static String DING_CORPID = "system.ding.corpid";
    // 钉钉 应用agentid
    public static String DING_AGENTID = "system.ding.agentid";
    // 钉钉 应用key
    public static String DING_APPKEY = "system.ding.appkey";
    // 钉钉应用appsecret
    public static String DING_APPSECRET = "system.ding.appsecret";
    // 钉钉 免登 APP loginurl
    public static String DING_APPLOGINURL = "system.ding.apploginurl";
    // 钉钉 免登  Pc web loginurl
    public static String DING_WEBLOGINURL = "system.ding.webloginurl";
    // 钉钉 应用回调callbacktoken
    public static String DING_CALLBACK_TOKEN = "system.ding.callbacktoken";
    // 钉钉 应用回调callbackaesKey
    public static String DING_CALLBACK_AESKEY = "system.ding.callbackaeskey";

}
