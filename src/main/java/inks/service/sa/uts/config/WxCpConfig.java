package inks.service.sa.uts.config;

import me.chanjar.weixin.cp.api.WxCpGroupRobotService;
import me.chanjar.weixin.cp.api.WxCpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class WxCpConfig {

    @Autowired
    private WxCpService wxCpService; // 确保 WxCpService 已经是 Spring 管理的 Bean

    @Bean
    public WxCpGroupRobotService wxCpGroupRobotService() {
        return wxCpService.getGroupRobotService();
    }
}