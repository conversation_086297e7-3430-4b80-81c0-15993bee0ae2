package inks.service.sa.uts.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * RestTemplate配置类
 * 解决多个RestTemplate Bean冲突问题
 *
 * <AUTHOR>
 * @since 2025-01-29
 */
@Configuration
public class RestTemplateConfig {

    /**
     * 默认的RestTemplate Bean
     * 使用@Primary注解标记为主要Bean，当有多个同类型Bean时优先使用此Bean
     */
    @Bean
    @Primary
    public RestTemplate restTemplate() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(10000); // 连接超时10秒
        factory.setReadTimeout(30000);    // 读取超时30秒
        return new RestTemplate(factory);
    }

    /**
     * 数据源专用的RestTemplate Bean
     * 用于数据源相关的HTTP请求，配置更长的超时时间
     */
    @Bean("dataSourceRestTemplate")
    public RestTemplate dataSourceRestTemplate() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(15000); // 连接超时15秒
        factory.setReadTimeout(60000);    // 读取超时60秒（数据源查询可能需要更长时间）
        return new RestTemplate(factory);
    }
}
