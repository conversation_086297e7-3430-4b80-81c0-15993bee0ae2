package inks.service.sa.uts.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * SSH执行配置类
 * 管理SSH流水线执行的默认配置参数
 *
 * <AUTHOR>
 * @since 2025-07-19
 */
@Component
@ConfigurationProperties(prefix = "ssh.execution")
public class SshExecutionConfig {
    
    /**
     * 默认命令超时时间（毫秒）
     */
    private int defaultTimeoutMs = 30000;
    
    /**
     * 默认连接超时时间（毫秒）
     */
    private int defaultConnectionTimeoutMs = 15000;
    
    /**
     * 默认最大重试次数
     */
    private int defaultMaxRetries = 3;
    
    /**
     * 重试延迟基数（毫秒）
     */
    private int retryDelayBaseMs = 1000;
    
    /**
     * 最大重试延迟（毫秒）
     */
    private int maxRetryDelayMs = 10000;
    
    /**
     * 是否启用指数退避重试策略
     */
    private boolean enableExponentialBackoff = true;
    
    /**
     * 默认是否在错误时继续执行
     */
    private boolean defaultContinueOnError = false;
    
    /**
     * 流水线执行最大等待时间（毫秒）
     */
    private long maxPipelineWaitMs = 7200000; // 2小时
    
    /**
     * 状态检查间隔（毫秒）
     */
    private long statusCheckIntervalMs = 10000; // 10秒
    
    /**
     * 是否启用详细日志
     */
    private boolean enableVerboseLogging = true;
    
    /**
     * 命令输出最大长度（字符）
     */
    private int maxOutputLength = 10000;
    
    /**
     * 是否自动清理过期会话
     */
    private boolean autoCleanupExpiredSessions = true;
    
    /**
     * 会话过期时间（毫秒）
     */
    private long sessionExpirationMs = 86400000; // 24小时
    
    // Getters and Setters
    public int getDefaultTimeoutMs() {
        return defaultTimeoutMs;
    }
    
    public void setDefaultTimeoutMs(int defaultTimeoutMs) {
        this.defaultTimeoutMs = defaultTimeoutMs;
    }
    
    public int getDefaultConnectionTimeoutMs() {
        return defaultConnectionTimeoutMs;
    }
    
    public void setDefaultConnectionTimeoutMs(int defaultConnectionTimeoutMs) {
        this.defaultConnectionTimeoutMs = defaultConnectionTimeoutMs;
    }
    
    public int getDefaultMaxRetries() {
        return defaultMaxRetries;
    }
    
    public void setDefaultMaxRetries(int defaultMaxRetries) {
        this.defaultMaxRetries = defaultMaxRetries;
    }
    
    public int getRetryDelayBaseMs() {
        return retryDelayBaseMs;
    }
    
    public void setRetryDelayBaseMs(int retryDelayBaseMs) {
        this.retryDelayBaseMs = retryDelayBaseMs;
    }
    
    public int getMaxRetryDelayMs() {
        return maxRetryDelayMs;
    }
    
    public void setMaxRetryDelayMs(int maxRetryDelayMs) {
        this.maxRetryDelayMs = maxRetryDelayMs;
    }
    
    public boolean isEnableExponentialBackoff() {
        return enableExponentialBackoff;
    }
    
    public void setEnableExponentialBackoff(boolean enableExponentialBackoff) {
        this.enableExponentialBackoff = enableExponentialBackoff;
    }
    
    public boolean isDefaultContinueOnError() {
        return defaultContinueOnError;
    }
    
    public void setDefaultContinueOnError(boolean defaultContinueOnError) {
        this.defaultContinueOnError = defaultContinueOnError;
    }
    
    public long getMaxPipelineWaitMs() {
        return maxPipelineWaitMs;
    }
    
    public void setMaxPipelineWaitMs(long maxPipelineWaitMs) {
        this.maxPipelineWaitMs = maxPipelineWaitMs;
    }
    
    public long getStatusCheckIntervalMs() {
        return statusCheckIntervalMs;
    }
    
    public void setStatusCheckIntervalMs(long statusCheckIntervalMs) {
        this.statusCheckIntervalMs = statusCheckIntervalMs;
    }
    
    public boolean isEnableVerboseLogging() {
        return enableVerboseLogging;
    }
    
    public void setEnableVerboseLogging(boolean enableVerboseLogging) {
        this.enableVerboseLogging = enableVerboseLogging;
    }
    
    public int getMaxOutputLength() {
        return maxOutputLength;
    }
    
    public void setMaxOutputLength(int maxOutputLength) {
        this.maxOutputLength = maxOutputLength;
    }
    
    public boolean isAutoCleanupExpiredSessions() {
        return autoCleanupExpiredSessions;
    }
    
    public void setAutoCleanupExpiredSessions(boolean autoCleanupExpiredSessions) {
        this.autoCleanupExpiredSessions = autoCleanupExpiredSessions;
    }
    
    public long getSessionExpirationMs() {
        return sessionExpirationMs;
    }
    
    public void setSessionExpirationMs(long sessionExpirationMs) {
        this.sessionExpirationMs = sessionExpirationMs;
    }
    
    /**
     * 计算重试延迟时间
     * @param retryCount 当前重试次数
     * @return 延迟时间（毫秒）
     */
    public int calculateRetryDelay(int retryCount) {
        if (!enableExponentialBackoff) {
            return retryDelayBaseMs;
        }
        
        int delay = retryDelayBaseMs * (int) Math.pow(2, retryCount - 1);
        return Math.min(delay, maxRetryDelayMs);
    }
    
    /**
     * 获取有效的超时时间
     * @param configuredTimeout 配置的超时时间
     * @return 有效的超时时间
     */
    public int getEffectiveTimeout(Integer configuredTimeout) {
        return configuredTimeout != null && configuredTimeout > 0 ? 
                configuredTimeout : defaultTimeoutMs;
    }
    
    /**
     * 获取有效的重试次数
     * @param configuredRetries 配置的重试次数
     * @return 有效的重试次数
     */
    public int getEffectiveRetries(Integer configuredRetries) {
        return configuredRetries != null && configuredRetries >= 0 ? 
                configuredRetries : defaultMaxRetries;
    }
    
    /**
     * 获取有效的继续错误标志
     * @param configuredContinueOnError 配置的继续错误标志
     * @return 有效的继续错误标志
     */
    public boolean getEffectiveContinueOnError(Integer configuredContinueOnError) {
        if (configuredContinueOnError == null) {
            return defaultContinueOnError;
        }
        return configuredContinueOnError == 1;
    }
    
    /**
     * 截断输出内容
     * @param output 原始输出
     * @return 截断后的输出
     */
    public String truncateOutput(String output) {
        if (output == null || output.length() <= maxOutputLength) {
            return output;
        }
        
        return output.substring(0, maxOutputLength) + 
                "\n... [输出被截断，总长度: " + output.length() + " 字符]";
    }
}
