package inks.service.sa.uts.controller;

import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.uts.service.UtsInfoagentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 信息代理中心(Uts_InfoAgent)表控制层
 *
 * <AUTHOR>
 * @since 2025-02-28 13:12:39
 */
@RestController
@RequestMapping("S34M09B1")
@Api(tags = "S34M09B1:信息代理中心")
public class S34M09B1Controller extends UtsInfoagentController {
    @Resource
    private UtsInfoagentService utsInfoagentService;
    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = "代理信息发送到：ding,wxe,wxmp,email,mqtt  入参json格式：code、data", notes = "", produces = "application/json")
    @RequestMapping(value = "/sendAgent", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_InfoAgent.List")
    public R<String> sendAgent(@RequestBody String json, @RequestParam(required = false) String tid) {
        try {
            if (StringUtils.isBlank(tid)) {
                tid = saRedisService.getLoginUser(ServletUtils.getRequest()).getTenantid();
            }
            //// 从json中获取code和content
            //JSONObject jsonObject = JSONObject.parseObject(json);
            //String msgCode = jsonObject.getString("code");
            //String data = jsonObject.getString("data");
            //// 根据code获取信息代理
            String result = utsInfoagentService.sendAgent(json, tid);
            return R.ok(result);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
