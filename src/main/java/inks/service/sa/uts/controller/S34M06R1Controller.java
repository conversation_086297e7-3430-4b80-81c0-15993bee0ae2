package inks.service.sa.uts.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import inks.common.core.constant.ConfigConstant;
import inks.common.core.domain.R;
import inks.sa.common.core.service.SaConfigService;
import inks.sa.common.core.service.SaRedisService;
import inks.sa.common.core.utils.HttpRequestUtil;
import inks.service.sa.uts.utils.wxutils.QyWeChat;
import inks.service.sa.uts.utils.wxutils.QyWeChatUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 企业微信官方API
 *
 * <AUTHOR>
 * @since 2023-01-10 11:12:08
 */
@RestController
@RequestMapping("S34M06R1")
@Api(tags = "S34M06R1:企业微信官方API")
public class S34M06R1Controller {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(S34M06R1Controller.class);
    @Resource
    private SaRedisService saRedisService;
    @Resource
    private SaConfigService saConfigService;


    @ApiOperation(value = "通过template_id获取审批模板详情", notes = "通过template_id获取审批模板详情", produces = "application/json")
    @RequestMapping(value = "/getTemplateDetail", method = RequestMethod.GET)
    public R<JSONObject> getTemplateDetail(String key) {
        String tid = saRedisService.getLoginUser().getTenantid();
        //获取企业微信参数
        Map<String, String> mapcfg = saConfigService.getConfigAll();
        QyWeChat.corpId = mapcfg.get(ConfigConstant.WXE_CORPID);
        logger.info("企业微信 corpId:" + QyWeChat.corpId);
        QyWeChat.agentSecret = mapcfg.get(ConfigConstant.WXE_APPSECRET);
        logger.info("企业微信 agentSecret:" + QyWeChat.agentSecret);
        QyWeChat.agentId = Integer.parseInt(mapcfg.get(ConfigConstant.WXE_AGENTID));
        logger.info("企业微信 agentID:" + QyWeChat.agentId);
        //获取企业微信TOKEN
        String token = QyWeChatUtils.refreshToken("agentToken");
        //设置参数template_id
        String url = QyWeChat.templateDetailURL.replace("{access_token}", token);
        Map<String, String> map = new HashMap<>();
        map.put("template_id", key);
        //发送模板请求                                      请求体map转为JSON
        JSONObject jsonObject = HttpRequestUtil.sendPost(url, JSONObject.toJSONString(map));
        System.out.println(JSONArray.parseObject(jsonObject.toString()));
        return R.ok(jsonObject);
    }


    /**
     * 获取企业微信部门列表
     *
     * @param departmentId (可选) 部门ID，不传则获取所有部门
     * @return 部门列表JSON
     */
    @ApiOperation(value = "获取企业微信部门列表", notes = "获取企业微信部门列表", produces = "application/json")
    @RequestMapping(value = "/getDepartmentList", method = RequestMethod.GET)
    public R getDepartmentList(@RequestParam(required = false) Long departmentId) {
        try {
            // 1. 获取企业微信参数
            Map<String, String> mapcfg = saConfigService.getConfigAll();
            QyWeChat.corpId = mapcfg.get(ConfigConstant.WXE_CORPID);
            logger.info("企业微信 corpId:" + QyWeChat.corpId);
            QyWeChat.agentSecret = mapcfg.get(ConfigConstant.WXE_APPSECRET);
            logger.info("企业微信 agentSecret:" + QyWeChat.agentSecret);

            // 2. 获取企业微信TOKEN
            String token = QyWeChatUtils.refreshToken("agentToken");
            if (token == null || token.isEmpty()) {
                return R.fail("获取企业微信Token失败");
            }

            // 3. 构建请求URL
            String url = QyWeChat.getDepartmentList.replace("{access_token}", token);
            if (departmentId != null) {
                url += "&id=" + departmentId;
            }

            // 4. 发送请求获取部门列表
            JSONObject response = HttpRequestUtil.sendGet(url);

            // 5. 检查响应错误
            if (response.getInteger("errcode") != 0) {
                logger.error("获取部门列表失败: {}", response);
                return R.fail(response.getString("errmsg"));
            }

            // 6. 返回部门列表
            JSONArray departments = response.getJSONArray("department");
            logger.info("成功获取部门数据，数量: {}", departments.size());
            return R.ok(departments);
        } catch (Exception e) {
            logger.error("获取部门列表失败", e);
            return R.fail("服务异常: " + e.getMessage());
        }
    }

    /**
     * 获取企业微信部门成员列表
     *
     * @param departmentId 部门ID (必填)
     * @param fetchChild   (可选) 是否获取子部门成员，默认0
     * @return 成员列表JSON
     */
    @ApiOperation(value = "获取企业微信部门成员列表", notes = "获取企业微信部门成员列表", produces = "application/json")
    @RequestMapping(value = "/getDepartmentMembers", method = RequestMethod.GET)
    public R getDepartmentMembers(
            @RequestParam(required = true) Long departmentId,
            @RequestParam(required = false, defaultValue = "0") Integer fetchChild) {
        try {
            // 1. 获取企业微信参数
            Map<String, String> mapcfg = saConfigService.getConfigAll();
            QyWeChat.corpId = mapcfg.get(ConfigConstant.WXE_CORPID);
            logger.info("企业微信 corpId:" + QyWeChat.corpId);
            QyWeChat.agentSecret = mapcfg.get(ConfigConstant.WXE_APPSECRET);
            logger.info("企业微信 agentSecret:" + QyWeChat.agentSecret);
            QyWeChat.agentId = Integer.parseInt(mapcfg.get(ConfigConstant.WXE_AGENTID));
            logger.info("企业微信 agentID:" + QyWeChat.agentId);

            // 2. 获取企业微信TOKEN
            String token = QyWeChatUtils.refreshToken("agentToken");
            if (token == null || token.isEmpty()) {
                return R.fail("获取企业微信Token失败");
            }

            // 3. 构建请求URL
            String url = QyWeChat.getSimpleList
                    .replace("{access_token}", token)
                    .replace("{department_id}", departmentId.toString());
            url += "&fetch_child=" + fetchChild;

            // 4. 发送请求获取成员列表
            JSONObject response = HttpRequestUtil.sendGet(url);

            // 5. 检查响应错误
            if (response.getInteger("errcode") != 0) {
                logger.error("获取部门成员失败: {}", response);

                // 特殊处理IP白名单错误
                if (response.getInteger("errcode") == 60020) {
                    return R.fail("企业微信接口拒绝访问: 请将服务器IP添加到企业微信后台的可信IP白名单");
                }

                return R.fail(response.getString("errmsg"));
            }

            // 6. 返回成员列表
            JSONArray members = response.getJSONArray("userlist");
            logger.info("成功获取成员数据，数量: {}", members.size());
            return R.ok(members);
        } catch (Exception e) {
            logger.error("获取部门成员失败", e);
            return R.fail("服务异常: " + e.getMessage());
        }
    }

    /**
     * 获取企业微信成员详情
     *
     * @param userId 成员ID (必填)
     * @return 成员详情JSON
     */
    @ApiOperation(value = "【暂不使用，getDepartmentList+getDepartmentMembers即可获取部门及部门下人员信息】获取企业微信成员详情", notes = "获取企业微信成员详情", produces = "application/json")
    @RequestMapping(value = "/getMemberDetail", method = RequestMethod.GET)
    public R<JSONObject> getMemberDetail(
            @RequestParam(required = true) String userId) {
        try {
            // 1. 获取企业微信参数
            Map<String, String> mapcfg = saConfigService.getConfigAll();
            QyWeChat.corpId = mapcfg.get(ConfigConstant.WXE_CORPID);
            logger.info("企业微信 corpId:" + QyWeChat.corpId);
            QyWeChat.agentSecret = mapcfg.get(ConfigConstant.WXE_APPSECRET);
            logger.info("企业微信 agentSecret:" + QyWeChat.agentSecret);
            QyWeChat.agentId = Integer.parseInt(mapcfg.get(ConfigConstant.WXE_AGENTID));
            logger.info("企业微信 agentID:" + QyWeChat.agentId);

            // 2. 获取企业微信TOKEN
            String token = QyWeChatUtils.refreshToken("agentToken");
            if (token == null || token.isEmpty()) {
                return R.fail("获取企业微信Token失败");
            }

            // 3. 构建请求URL
            String url = "https://qyapi.weixin.qq.com/cgi-bin/user/get?access_token=" + token + "&userid=" + userId;

            // 4. 发送请求获取成员详情
            JSONObject response = HttpRequestUtil.sendGet(url);

            // 5. 检查响应错误
            if (response.getInteger("errcode") != 0) {
                logger.error("获取成员详情失败: {}", response);
                return R.fail(response.getString("errmsg"));
            }

            // 6. 返回成员详情
            logger.info("成功获取成员详情: {}", userId);
            return R.ok(response);
        } catch (Exception e) {
            logger.error("获取成员详情失败", e);
            return R.fail("服务异常: " + e.getMessage());
        }
    }
}
