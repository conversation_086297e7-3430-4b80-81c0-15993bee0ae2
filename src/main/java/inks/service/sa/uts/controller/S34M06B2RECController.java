package inks.service.sa.uts.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.uts.domain.pojo.UtsDingapprrecPojo;
import inks.service.sa.uts.service.UtsDingapprrecService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 钉钉审批记录(Uts_DingApprRec)表控制层
 *
 * <AUTHOR>
 * @since 2023-01-10 11:11:42
 */
@RestController
@RequestMapping("S34M06B2REC")
@Api(tags = "S34M06B2REC:钉钉审批记录")
public class S34M06B2RECController extends UtsDingapprrecController {
    /**
     * 服务对象
     */
    @Resource
    private UtsDingapprrecService utsDingapprrecService;
    @Resource
    private SaRedisService saRedisService;

    /**
     * 按模块编码查询报表
     *
     * @param key 主键
     * @return 查询结果
     */
    @ApiOperation(value = "按模块编码查询报表", notes = "按模块编码查询报表", produces = "application/json")
    @RequestMapping(value = "/getOnlineNumByBillid", method = RequestMethod.GET)
    public R<Integer> getOnlineNumByBillid(String key, String tid) {
        try {
            //有Tid参
            if (tid == null || tid.equals("")) {
                // 获得用户数据
                LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
                tid = loginUser.getTenantid();
            }
            List<UtsDingapprrecPojo> list = this.utsDingapprrecService.getOnlineByBillid(key, tid);
            return R.ok(list.size());
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
