package inks.service.sa.uts.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.uts.domain.pojo.SaSshpipelinesPojo;
import inks.service.sa.uts.domain.pojo.SaSshpipelinesitemPojo;
import inks.service.sa.uts.domain.pojo.SaSshpipelinesitemdetailPojo;
import inks.service.sa.uts.service.SaSshpipelinesService;
import inks.service.sa.uts.service.SaSshpipelinesitemService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * SSH流水线(Sa_SshPipelines)表控制层
 *
 * <AUTHOR>
 * @since 2025-05-19 11:11:27
 */
//@RestController
//@RequestMapping("saSshpipelines")
public class SaSshpipelinesController {

    private final static Logger logger = LoggerFactory.getLogger(SaSshpipelinesController.class);
    @Resource
    private SaSshpipelinesService saSshpipelinesService;
    @Resource
    private SaSshpipelinesitemService saSshpipelinesitemService;
    @Resource
    private SaRedisService saRedisService;
    @Resource
    private SaBillcodeService saBillcodeService;

    @ApiOperation(value = " 获取SSH流水线详细信息", notes = "获取SSH流水线详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_SshPipelines.List")
    public R<SaSshpipelinesPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saSshpipelinesService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_SshPipelines.List")
    public R<PageInfo<SaSshpipelinesitemdetailPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_SshPipelines.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saSshpipelinesService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 获取SSH流水线详细信息", notes = "获取SSH流水线详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_SshPipelines.List")
    public R<SaSshpipelinesPojo> getBillEntity(String key) {
        try {
            // 获得用户数据
            //LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saSshpipelinesService.getBillEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_SshPipelines.List")
    public R<PageInfo<SaSshpipelinesPojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_SshPipelines.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saSshpipelinesService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_SshPipelines.List")
    public R<PageInfo<SaSshpipelinesPojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_SshPipelines.CreateDate");
            // 获得用户数据
            //LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saSshpipelinesService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增SSH流水线", notes = "新增SSH流水线", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_SshPipelines.Add")
    public R<SaSshpipelinesPojo> create(@RequestBody String json) {
        try {
            SaSshpipelinesPojo saSshpipelinesPojo = JSONArray.parseObject(json, SaSshpipelinesPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saSshpipelinesPojo.setCreateby(loginUser.getRealName());   // 创建者
            saSshpipelinesPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saSshpipelinesPojo.setCreatedate(new Date());   // 创建时间
            saSshpipelinesPojo.setLister(loginUser.getRealname());   // 制表
            saSshpipelinesPojo.setListerid(loginUser.getUserid());    // 制表id            
            saSshpipelinesPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saSshpipelinesService.insert(saSshpipelinesPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改SSH流水线", notes = "修改SSH流水线", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_SshPipelines.Edit")
    public R<SaSshpipelinesPojo> update(@RequestBody String json) {
        try {
            SaSshpipelinesPojo saSshpipelinesPojo = JSONArray.parseObject(json, SaSshpipelinesPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saSshpipelinesPojo.setLister(loginUser.getRealname());   // 制表
            saSshpipelinesPojo.setListerid(loginUser.getUserid());    // 制表id   
            saSshpipelinesPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saSshpipelinesService.update(saSshpipelinesPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除SSH流水线", notes = "删除SSH流水线", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_SshPipelines.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saSshpipelinesService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    @ApiOperation(value = " 新增SSH流水线Item", notes = "新增SSH流水线Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_SshPipelines.Add")
    public R<SaSshpipelinesitemPojo> createItem(@RequestBody String json) {
        try {
            SaSshpipelinesitemPojo saSshpipelinesitemPojo = JSONArray.parseObject(json, SaSshpipelinesitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saSshpipelinesitemService.insert(saSshpipelinesitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 修改SSH流水线Item", notes = "修改SSH流水线Item", produces = "application/json")
    @RequestMapping(value = "/updateItem", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_SshPipelines.Edit")
    public R<SaSshpipelinesitemPojo> updateItem(@RequestBody String json) {
        try {
            SaSshpipelinesitemPojo saSshpipelinesitemPojo = JSONArray.parseObject(json, SaSshpipelinesitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saSshpipelinesitemService.update(saSshpipelinesitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "删除SSH流水线Item", notes = "删除SSH流水线Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_SshPipelines.Delete")
    public R<Integer> deleteItem(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saSshpipelinesitemService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_SshPipelines.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaSshpipelinesPojo saSshpipelinesPojo = this.saSshpipelinesService.getBillEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saSshpipelinesPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = saSshpipelinesPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    SaSshpipelinesitemPojo saSshpipelinesitemPojo = new SaSshpipelinesitemPojo();
                    saSshpipelinesPojo.getItem().add(saSshpipelinesitemPojo);
                }
            }
        }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(saSshpipelinesPojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

