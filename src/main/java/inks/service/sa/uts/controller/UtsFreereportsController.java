package inks.service.sa.uts.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.uts.domain.pojo.UtsFreereportsPojo;
import inks.service.sa.uts.domain.pojo.UtsFreereportsitemPojo;
import inks.service.sa.uts.domain.pojo.UtsFreereportsitemdetailPojo;
import inks.service.sa.uts.service.UtsFreereportsService;
import inks.service.sa.uts.service.UtsFreereportsitemService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 自由报表(Uts_FreeReports)表控制层
 *
 * <AUTHOR>
 * @since 2024-05-14 10:14:36
 */
//@RestController
//@RequestMapping("utsFreereports")
public class UtsFreereportsController {

    @Resource
    private UtsFreereportsService utsFreereportsService;
    @Resource
    private UtsFreereportsitemService utsFreereportsitemService;

    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = " 获取自由报表详细信息", notes = "获取自由报表详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Uts_FreeReports.List")
    public R<UtsFreereportsPojo> getEntity(String key) {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.utsFreereportsService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_FreeReports.List")
    public R<PageInfo<UtsFreereportsitemdetailPojo>> getPageList(@RequestBody String json) {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Uts_FreeReports.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.utsFreereportsService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 获取自由报表详细信息", notes = "获取自由报表详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Uts_FreeReports.List")
    public R<UtsFreereportsPojo> getBillEntity(String key) {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.utsFreereportsService.getBillEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_FreeReports.List")
    public R<PageInfo<UtsFreereportsPojo>> getBillList(@RequestBody String json) {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Uts_FreeReports.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            return R.ok(this.utsFreereportsService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_FreeReports.List")
    public R<PageInfo<UtsFreereportsPojo>> getPageTh(@RequestBody String json) {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Uts_FreeReports.CreateDate");
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.utsFreereportsService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增自由报表", notes = "新增自由报表", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_FreeReports.Add")
    public R<UtsFreereportsPojo> create(@RequestBody String json) {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        try {
            UtsFreereportsPojo utsFreereportsPojo = JSONArray.parseObject(json, UtsFreereportsPojo.class);
            utsFreereportsPojo.setCreateby(loginUser.getRealName());   // 创建者
            utsFreereportsPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            utsFreereportsPojo.setCreatedate(new Date());   // 创建时间
            utsFreereportsPojo.setLister(loginUser.getRealname());   // 制表
            utsFreereportsPojo.setListerid(loginUser.getUserid());    // 制表id            
            utsFreereportsPojo.setModifydate(new Date());   //修改时间
            utsFreereportsPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.utsFreereportsService.insert(utsFreereportsPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改自由报表", notes = "修改自由报表", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_FreeReports.Edit")
    public R<UtsFreereportsPojo> update(@RequestBody String json) {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        try {
            UtsFreereportsPojo utsFreereportsPojo = JSONArray.parseObject(json, UtsFreereportsPojo.class);
            utsFreereportsPojo.setLister(loginUser.getRealname());   // 制表
            utsFreereportsPojo.setListerid(loginUser.getUserid());    // 制表id   
            utsFreereportsPojo.setModifydate(new Date());   //修改时间
            utsFreereportsPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.utsFreereportsService.update(utsFreereportsPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除自由报表", notes = "删除自由报表", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Uts_FreeReports.Delete")
    public R<Integer> delete(String key) {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.utsFreereportsService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    @ApiOperation(value = " 新增自由报表Item", notes = "新增自由报表Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_FreeReports.Add")
    public R<UtsFreereportsitemPojo> createItem(@RequestBody String json) {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        try {
            UtsFreereportsitemPojo utsFreereportsitemPojo = JSONArray.parseObject(json, UtsFreereportsitemPojo.class);
            utsFreereportsitemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.utsFreereportsitemService.insert(utsFreereportsitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 修改自由报表Item", notes = "修改自由报表Item", produces = "application/json")
    @RequestMapping(value = "/updateItem", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_FreeReports.Edit")
    public R<UtsFreereportsitemPojo> updateItem(@RequestBody String json) {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        try {
            UtsFreereportsitemPojo utsFreereportsitemPojo = JSONArray.parseObject(json, UtsFreereportsitemPojo.class);
            utsFreereportsitemPojo.setTenantid(loginUser.getTenantid());
            return R.ok(this.utsFreereportsitemService.update(utsFreereportsitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "删除自由报表Item", notes = "删除自由报表Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Uts_FreeReports.Delete")
    public R<Integer> deleteItem(String key) {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.utsFreereportsitemService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Uts_FreeReports.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        UtsFreereportsPojo utsFreereportsPojo = this.utsFreereportsService.getBillEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(utsFreereportsPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = utsFreereportsPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    UtsFreereportsitemPojo utsFreereportsitemPojo = new UtsFreereportsitemPojo();
                    utsFreereportsPojo.getItem().add(utsFreereportsitemPojo);
                }
            }
        }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(utsFreereportsPojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

