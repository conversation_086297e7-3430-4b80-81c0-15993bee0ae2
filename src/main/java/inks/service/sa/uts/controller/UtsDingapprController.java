package inks.service.sa.uts.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.uts.domain.pojo.UtsDingapprPojo;
import inks.service.sa.uts.service.UtsDingapprService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 钉钉审批(Uts_DingAppr)表控制层
 *
 * <AUTHOR>
 * @since 2024-12-16 14:12:14
 */
//@RestController
//@RequestMapping("utsDingappr")
public class UtsDingapprController {
    private final static Logger logger = LoggerFactory.getLogger(UtsDingapprController.class);
    @Resource
    private UtsDingapprService utsDingapprService;
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = " 获取钉钉审批详细信息", notes = "获取钉钉审批详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Uts_DingAppr.List")
    public R<UtsDingapprPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.utsDingapprService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_DingAppr.List")
    public R<PageInfo<UtsDingapprPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Uts_DingAppr.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.utsDingapprService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增钉钉审批", notes = "新增钉钉审批", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_DingAppr.Add")
    public R<UtsDingapprPojo> create(@RequestBody String json) {
        try {
            UtsDingapprPojo utsDingapprPojo = JSONArray.parseObject(json, UtsDingapprPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            utsDingapprPojo.setCreateby(loginUser.getRealName());   // 创建者
            utsDingapprPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            utsDingapprPojo.setCreatedate(new Date());   // 创建时间
            utsDingapprPojo.setLister(loginUser.getRealname());   // 制表
            utsDingapprPojo.setListerid(loginUser.getUserid());    // 制表id
            utsDingapprPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.utsDingapprService.insert(utsDingapprPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改钉钉审批", notes = "修改钉钉审批", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_DingAppr.Edit")
    public R<UtsDingapprPojo> update(@RequestBody String json) {
        try {
            UtsDingapprPojo utsDingapprPojo = JSONArray.parseObject(json, UtsDingapprPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            utsDingapprPojo.setLister(loginUser.getRealname());   // 制表
            utsDingapprPojo.setListerid(loginUser.getUserid());    // 制表id
            utsDingapprPojo.setModifydate(new Date());   //修改时间
            //            utsDingapprPojo.setAssessor(""); // 审核员
            //            utsDingapprPojo.setAssessorid(""); // 审核员id
            //            utsDingapprPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.utsDingapprService.update(utsDingapprPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除钉钉审批", notes = "删除钉钉审批", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Uts_DingAppr.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.utsDingapprService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Uts_DingAppr.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        UtsDingapprPojo utsDingapprPojo = this.utsDingapprService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(utsDingapprPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

