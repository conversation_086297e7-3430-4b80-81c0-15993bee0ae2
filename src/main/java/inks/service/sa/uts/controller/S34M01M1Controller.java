package inks.service.sa.uts.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.sa.common.core.service.SaConfigService;
import inks.sa.common.core.service.SaRedisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 通用发送钉钉或企业微信消息
 * <p>
 * 加入D96M14M1控制口，加入统一SendTextMsg. 由后端结合参数，确认用企业微信还是钉钉来发信息。达到前端统一代码；
 * 场景：安灯系统；
 * 用以下参数可以拿到本系统，客户是用钉钉还是企业微信；
 * 是否启用钉钉审批 system.ding.apprstart : true：启用；false：不启用；
 * 是否启用微信审批 system.wxe.apprstart : true：启用；false：不启用；
 *
 * <AUTHOR>
 * @since 2024-06-20 16:12:51
 */
@RestController
@RequestMapping("S34M01M1")
@Api(tags = "S34M01M1:发送钉钉或企业微信消息")
public class S34M01M1Controller extends UtsDingmsgController {
    @Resource
    private SaConfigService saConfigService;

    @Resource
    private SaRedisService saRedisService;
    @Resource
    private S34M01B1Controller d96M14B1Controller;//企业微信信息发送
    @Resource
    private S34M01B2Controller s34M01B2Controller;//钉钉信息发送
    @Resource
    private S34M06B1Controller a_saWxeapprController;//企业微信审批
    @Resource
    private S34M06B2Controller a_saDingapprController;//钉钉审批
    @ApiOperation(value = "通用发送信息接口 读取系统参数决定发送钉钉还是微信消息: system.ding.apprstart、system.wxe.apprstart ", notes = "", produces = "application/json")
    @RequestMapping(value = "/sendMessage", method = RequestMethod.POST)
    public R<String> sendMessage(@RequestBody String json, @RequestParam(required = false) String tid) {
        LoginUser loginUser = saRedisService.getLoginUser();
        // 读取指定系统参数 是否启用钉钉审批/是否启用企业微信审批
        String isDingDing = saConfigService.getConfigValue("system.ding.apprstart", null);
//        String isWxe = systemFeignService.getConfigValue("system.wxe.apprstart", loginUser.getTenantid(), loginUser.getToken()).getData();
        if ("true".equalsIgnoreCase(isDingDing)) {
            return s34M01B2Controller.sendTextMsg(json, tid);//钉钉信息发送
        } else if ("true".equalsIgnoreCase(saConfigService.getConfigValue("system.wxe.apprstart", null))) {
            return d96M14B1Controller.sendTextMsg(json, tid);//企业微信信息发送
        } else {
            return R.fail("未启用钉钉或企业微信消息功能！");
        }
    }
    @ApiOperation(value = "发送钉钉审批", notes = "", produces = "application/json")
    @RequestMapping(value = "/dingapprovel", method = RequestMethod.GET)
    public R dingapprovel(String apprid, @RequestParam(required = false) String tid) {
        return a_saDingapprController.sendapprovel(apprid, tid);
    }


    @ApiOperation(value = "发送企业微信审批", notes = "", produces = "application/json")
    @RequestMapping(value = "/wxeapprovel", method = RequestMethod.GET)
    public R wxeapprovel(String apprid, @RequestParam(required = false) String tid) {
        return a_saWxeapprController.sendapprovel(apprid, tid);
    }

}
