package inks.service.sa.uts.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.uts.service.SaJoblogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 定时任务调度日志表(Sa_JobLog)表控制层
 *
 * <AUTHOR>
 * @since 2025-03-07 16:07:35
 */
@RestController
@RequestMapping("S34M10S1")
@Api(tags = "S34M10S1:定时任务调度日志表")
public class S34M10S1Controller extends SaJoblogController {

    @Resource
    private SaJoblogService jobLogService;

    @Resource
    private SaRedisService saRedisService;

    ///**
    // * 查询定时任务调度日志列表
    // */
    ////@PreAuthorize("@ss.hasPermi('monitor:job:list')")
    //@GetMapping("/list")
    //public TableDataInfo list(SaJoblogPojo sysJobLog)
    //{
    //    startPage();
    //    List<SaJoblogPojo> list = jobLogService.selectJobLogList(sysJobLog);
    //    return getDataTable(list);
    //}

    ///**
    // * 导出定时任务调度日志列表
    // */
    ////@PreAuthorize("@ss.hasPermi('monitor:job:export')")
    //@Log(title = "任务调度日志", businessType = BusinessType.EXPORT)
    //@PostMapping("/export")
    //public void export(HttpServletResponse response, SaJoblogPojo sysJobLog)
    //{
    //    List<SaJoblogPojo> list = jobLogService.selectJobLogList(sysJobLog);
    //    ExcelUtil<SaJoblogPojo> util = new ExcelUtil<SaJoblogPojo>(SaJoblogPojo.class);
    //    util.exportExcel(response, list, "调度日志");
    //}

    /**
     * 根据调度编号获取详细信息
     */
    //@PreAuthorize("@ss.hasPermi('monitor:job:query')")
    @GetMapping(value = "/{jobLogId}")
    public R getInfo(@PathVariable String jobLogId) {
        LoginUser loginUser = saRedisService.getLoginUser();
        return R.ok(jobLogService.getEntity(jobLogId));
    }


    /**
     * 删除定时任务调度日志
     */
    //@PreAuthorize("@ss.hasPermi('monitor:job:remove')")
    @ApiOperation(value = "批量删除定时任务调度日志", notes = "", produces = "application/json")
    @DeleteMapping("/{jobLogIds}")
    public R remove(@PathVariable String[] jobLogIds) {
        LoginUser loginUser = saRedisService.getLoginUser();
        return R.ok(jobLogService.deleteJobLogByIds(jobLogIds));
    }

    /**
     * 清空定时任务调度日志
     */
    //@PreAuthorize("@ss.hasPermi('monitor:job:remove')")
    //@Log(title = "调度日志", businessType = BusinessType.CLEAN)
    @DeleteMapping("/clean")
    @ApiOperation(value = "【清空全部】定时任务调度日志", notes = "", produces = "application/json")
    public R clean() {
        LoginUser loginUser = saRedisService.getLoginUser();
        jobLogService.cleanJobLog();
        return R.ok();
    }
}
