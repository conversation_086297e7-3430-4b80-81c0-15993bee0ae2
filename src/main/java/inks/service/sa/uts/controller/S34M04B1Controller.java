package inks.service.sa.uts.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.uts.service.UtsDatabaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 数据库连接池(Uts_Database)表控制层
 * Hikari 数据库连接池，会自动管理连接的生命周期，包括断开闲置的连接。在默认情况下，HikariCP 会在连接空闲10分钟后断开连接。
 *
 * <AUTHOR>
 * @since 2024-05-11 15:28:20
 */
@RestController
@RequestMapping("S34M04B1")
@Api(tags = "S34M04B1:数据库连接池")
public class S34M04B1Controller extends UtsDatabaseController {
    @Resource
    private SaRedisService saRedisService;
    @Resource
    private UtsDatabaseService utsDatabaseService;

    @ApiOperation(value = " 测试连接数据库 databaseid:数据库连接id", notes = "", produces = "application/json")
    @RequestMapping(value = "/testConnection", method = RequestMethod.GET)
    public R<String> testConnection(String databaseid) {
        LoginUser loginUser = saRedisService.getLoginUser();
        return R.ok(utsDatabaseService.testConnection(databaseid, loginUser.getTenantid()));
    }

    @ApiOperation(value = " 测试连接数据库 databaseid:数据库连接id", notes = "", produces = "application/json")
    @RequestMapping(value = "/testConnectionBackup", method = RequestMethod.POST)
    public R<String> testConnectionBackup(@RequestBody Map<String, String> params) {
        //String url_pre, String username_pre, String password_pre,String drivename_pre
        String url = params.get("url");
        String username = params.get("username");
        String password = params.get("password");
        String driverclassname = params.get("driver");
        LoginUser loginUser = saRedisService.getLoginUser();
        return R.ok(utsDatabaseService.testConnectionBackup(url, username, password, driverclassname, loginUser.getTenantid()));
    }

    /**
     * 返回的格式:
     * [
     * {
     * "tablename": "Sa_Dict",
     * "tableitemname": "Sa_DictItem",
     * "tablecomment": "数据字典",
     * "createtime": "2024-03-24T21:17:16.000+00:00",
     * "updatetime": null
     * },...
     * ]
     */
    @ApiOperation(value = " 获取Url数据库下所有数据表 databaseid:数据库连接id", notes = "", produces = "application/json")
    @RequestMapping(value = "/table", method = RequestMethod.GET)
    public R<List<Map<String, Object>>> table(String databaseid, @RequestParam(required = false) String tableName,
                                              @RequestParam(required = false) boolean itemFirst) {
        LoginUser loginUser = saRedisService.getLoginUser();
        List<Map<String, Object>> result = utsDatabaseService.table(databaseid, tableName, itemFirst, loginUser.getTenantid());
        return R.ok(result);
    }

    @ApiOperation(value = "获取一个数据表所有字段信息 传入数据库连接id和数据表的名字，返回该表所有字段以及字段对应的注释和类型 tableNamePrefix:是否需要表名前缀 默认false", notes = "", produces = "application/json")
    @RequestMapping(value = "/tableFields", method = RequestMethod.GET)
    public R<List<Map<String, Object>>> tableFields(String databaseid, String tableName, @RequestParam(required = false) boolean tableNamePrefix) {
        LoginUser loginUser = saRedisService.getLoginUser();
        List<Map<String, Object>> result = utsDatabaseService.tableFields(databaseid, tableName, tableNamePrefix, loginUser.getTenantid());
        return R.ok(result);
    }


    //    // 创建一个HashMap来存储每个数据库URL对应的HikariDataSource实例
//    // 对于dataSources这个字段来说，我们确实不希望它被更改。我们只希望在dataSources这个HashMap中添加或删除元素，
//    // 而不是将dataSources本身指向另一个HashMap。因此，我们可以将dataSources声明为final
//    private final HashMap<String, HikariDataSource> dataSources = new HashMap<>();
//
//    // 当Spring容器中的Bean被销毁前，会自动调用被@PreDestroy注解修饰的方法 用于做一些[清理工作]，如关闭数据库连接、停止后台线程、释放资源等。
//    @PreDestroy
//    public void preDestroy() {
//        for (HikariDataSource dataSource : dataSources.values()) {
//            if (dataSource != null && !dataSource.isClosed()) {//避免尝试关闭已经被关闭的数据源
//                dataSource.close();
//            }
//        }
//    }

    //通过Sa_Database.id获取数据库连接dataSource和数据库名字
//    public Map<String, Object> getDataSource(String id, String tid) {
//        UtsDatabasePojo databasePojo = utsDatabaseService.getEntity(id, tid);
//        String url = databasePojo.getUrl();
//        // 检查我们是否已经为这个数据库URL创建了一个HikariDataSource实例
//        HikariDataSource dataSource = dataSources.get(url);
//        if (dataSource == null) {
//            // 如果没有，那么创建一个新的HikariDataSource实例
//            dataSource = new HikariDataSource();
//            dataSource.setJdbcUrl(url);
//            dataSource.setUsername(databasePojo.getUsername());
//            dataSource.setPassword(databasePojo.getPassword());
//            dataSource.setDriverClassName("com.mysql.cj.jdbc.Driver");//默认mysql 驱动
//            String driverClassName = databasePojo.getDriverclassname();
//            if (StringUtils.isNotBlank(driverClassName)) {
//                dataSource.setDriverClassName(driverClassName);
//                // 设置连接测试查询 sqlserver连接用jtds老版本驱动只能用这个 (耗时)
//                if (driverClassName.contains("jtds")) {
//                    dataSource.setConnectionTestQuery("SELECT 1");
//                }
//            }
//            // 将新创建的HikariDataSource实例保存到HashMap中
//            dataSources.put(url, dataSource);
//        }
//        // 获取数据库名字 格式为*****************************************
//        int lastIndex = url.lastIndexOf("/");
//        String databaseName = url.substring(lastIndex + 1);
//        HashMap<String, Object> map = new HashMap<>();
//        map.put("dataSource", dataSource);
//        map.put("databaseName", databaseName);
//        // 数据库类型:url包含mysql就是mysql数据库,包含sqlserver就是sqlserver数据库 默认mysql
//        String databaseType = MyConstant.MYSQL;
//        if (url.contains(MyConstant.SQLSERVER)) {
//            databaseType = MyConstant.SQLSERVER;
//        }
//        map.put("databaseType", databaseType);
//        return map;
//    }


}
