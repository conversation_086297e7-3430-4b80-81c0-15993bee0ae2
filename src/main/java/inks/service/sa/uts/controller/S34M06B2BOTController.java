package inks.service.sa.uts.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.taobao.api.ApiException;
import inks.common.core.domain.DingmsgPojo;
import inks.common.core.domain.R;
import inks.service.sa.uts.domain.pojo.UtsWxehookmsgPojo;
import inks.service.sa.uts.domain.vo.DingTalkRobotMessages;
import inks.service.sa.uts.service.UtsDinghookmsgService;
import inks.service.sa.uts.service.UtsDingmsgService;
import inks.service.sa.uts.utils.PrintColor;
import inks.service.sa.uts.utils.VelocityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URI;
import java.net.URL;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 钉钉:群机器人
 * 官方文档： https://open.dingtalk.com/document/orgapp/custom-bot-to-send-group-chat-messages
 * 官方文档(消息类型、参数格式)： https://open.dingtalk.com/document/orgapp/custom-bot-send-message-type?spm=ding_open_doc.document.0.0.ff327f7fXQ1Xvq
 * 群机器人调用频率限制：
 * 由于消息发送太频繁会严重影响群的使用体验，因此自定义机器人发送消息的频率限制如下：
 * 每个机器人每分钟最多发送20条消息到群里，如果超过20条，会限流10分钟。
 * 如果你有大量发消息的场景（譬如系统监控报警）可以将这些信息进行整合，通过markdown消息以摘要的形式发送到群里。
 */

@RestController
@RequestMapping("/S34M06B2BOT")
@Api(tags = "S34M06B2BOT:钉钉群机器人信息")
// 拷贝微服务utils/D96M14B4
public class S34M06B2BOTController extends UtsDinghookmsgController {

    @Value("${dingsuite.robot.token:8cc05b0481f6ed739a9c90a52b241e79725e2b6d3c50c5cc8ddc93146d9e7233}")
    private String defaultDingBotToken; // 从配置文件读取默认token

    @Value("${dingsuite.robot.secret:SEC5f203cacfaaa2420ff0b46135e4bb9c075715a74d496cb8f9da68fbff987dde2}")
    private String defaultDingBotSecret; // 默认签名密钥
    @Resource
    private UtsDinghookmsgService utsDinghookmsgService;
    @Resource
    private UtsDingmsgService utsDingmsgService;
    //------------------------- 文本消息 ----------------------------

    //作用：解析钉钉群机器人webhookUrl，获取access_token和secret
    public static Map<String, String> parseDingTalkWebhookUrl(String webhookUrl) throws Exception {
        URL url = new URL(webhookUrl);
        URI uri = url.toURI();
        String query = uri.getQuery();
        Map<String, String> paramMap = new HashMap<>();

        for (String param : query.split("&")) {
            String[] keyValue = param.split("=");
            if (keyValue.length == 2) {
                paramMap.put(keyValue[0], URLDecoder.decode(keyValue[1], String.valueOf(StandardCharsets.UTF_8)));
            }
        }
        return paramMap;
    }

    @ApiOperation(value = "发送文本消息", notes = "支持@指定用户或全体")
    @PostMapping("/sendText")
    public R sendText(@RequestBody DingTalkRobotMessages.TextMessageRequest request) throws Exception {
        DingTalkClient client = createClient(request.getToken(), request.getSecret());
        OapiRobotSendRequest req = new OapiRobotSendRequest();
        req.setMsgtype("text");

        OapiRobotSendRequest.Text text = new OapiRobotSendRequest.Text();
        text.setContent(request.getContent());
        req.setText(text);

        OapiRobotSendRequest.At at = new OapiRobotSendRequest.At();
        if (request.getAtuserids() != null && !request.getAtuserids().isEmpty()) {
            at.setAtUserIds(request.getAtuserids());
        }
        if (request.getIsatall() != null) {
            at.setIsAtAll(request.getIsatall());
        }
        req.setAt(at);

        return executeAndHandle(client, req);
    }

    public R<String> sendTextMsg(@RequestBody String json, @RequestParam(required = false) String tid) {
        //if (StringUtils.isBlank(tid)) {
        //    LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //    tid = loginUser.getTenantid();
        //}
        // 获得用户数据 DingmsgPojo是工具类，接参通用于钉钉和企业微信
        DingmsgPojo dingmsgPojoReq = JSONArray.parseObject(json, DingmsgPojo.class);
        String msgCode = dingmsgPojoReq.getMsgcode();
        String msgText = dingmsgPojoReq.getMsgtext();
        UtsWxehookmsgPojo dinghookmsgDB = this.utsDinghookmsgService.getEntityByMsgCode(msgCode, tid);
        if (dinghookmsgDB == null) {
            PrintColor.lv("dingbot：未找到对应的消息模板，跳出");
            return R.fail(msgCode + "信息模板未找到");
        }

        // 开始构建发送人列表: 使用Set来保证唯一性，避免重复
        Set<String> userListSet = new HashSet<>();
        // 1.先添加数据库中的用户列表
        String userListDb = dinghookmsgDB.getUserlist();
        if (StringUtils.isNotBlank(userListDb)) {
            Collections.addAll(userListSet, userListDb.split(","));
        }
        // 2.(代理中心特有)追加前端传入的用户列表
        String userListReq = dingmsgPojoReq.getUserlist();
        if (StringUtils.isNotBlank(userListReq)) {
            Collections.addAll(userListSet, userListReq.split(","));
        }
        PrintColor.zi("发送人列表1,2：本次前端传入的userListReq:" + userListReq + ", 消息模板数据库已有的.UserList:" + userListDb);

        // 3.发送人列表3处理ObjJson数组 转换钉钉userid
        String objJsonStr = dinghookmsgDB.getObjjson();
        if (StringUtils.isNotBlank(objJsonStr)) {
            JSONArray objJsonArray = JSONArray.parseArray(objJsonStr);
            for (Object objObj : objJsonArray) {
                JSONObject obj = (JSONObject) objObj;
                String oms_userid = obj.getString("objcode");

                if (oms_userid.trim().startsWith("$")) { // 如果是VM模板，进行渲染
                    oms_userid = VelocityUtils.renderTemplateToData(oms_userid, msgText);
                }

                // 根据oms_userid查询对应的钉钉用户ID
                String ding_userid = utsDingmsgService.getDingUserIdByOmsUserid(oms_userid, tid);

                // 如果查到了钉钉用户ID，添加到发送人列表
                if (StringUtils.isNotBlank(ding_userid)) {
                    userListSet.add(ding_userid); // 添加到Set中，避免重复
                }
            }
        }
        PrintColor.zi("发送人列表3：消息模板数据库.ObjJson:" + objJsonStr);
        // 将Set转换为String，逗号分隔
        String finalUserListString = String.join(",", userListSet);
        // 打印最终的发送人列表
        PrintColor.zi("替换后追加到发送人列表1,2之后： 最终发送人列表：" + finalUserListString);

        // 4.是否转换信息Vm模版
        String msgVM = dinghookmsgDB.getMsgtemplate();
        if (StringUtils.isNotBlank(msgVM)) {
            // 根据模版和内容 返回填充后数据
            msgText = VelocityUtils.renderTemplateToData(msgVM, msgText);
            PrintColor.zi("Vm模版替换后的消息内容：" + msgText);
        }

        // 5.要发送的所有群机器人webhookurl、@的所有人员
        String webhookListString = dinghookmsgDB.getWebhooklist().trim();
        List<String> userList = Arrays.asList(finalUserListString.split(","));
        String[] webhookList = webhookListString.split(",");
        // 钉钉群机器人webhook格式：https://oapi.dingtalk.com/robot/send?access_token=xxx&secret=yyy,https://oapi.dingtalk.com/robot/send?access_token=xxx&secret=yyy
        // 需要拆分access_token和签名secret(根据这两个参数才能发送消息)
        for (String webhook : webhookList) {
            try {
                Map<String, String> webhookMap = parseDingTalkWebhookUrl(webhook);
                DingTalkRobotMessages.TextMessageRequest textRequest = new DingTalkRobotMessages.TextMessageRequest();
                textRequest.setToken(webhookMap.get("access_token"));
                textRequest.setSecret(webhookMap.get("secret"));
                textRequest.setContent(msgText);
                textRequest.setAtuserids(userList);
                // 如果前端传入的userListReq为all或者消息模板数据库已有的.UserList为all，则设置为@所有人
                if ("all".equals(userListReq) || "all".equals(userListDb)) {
                    textRequest.setIsatall(true);
                }
                // 发送群机器人消息
                this.sendText(textRequest);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        PrintColor.zi("本次共发送了" + webhookList.length + "个群机器人，消息内容：" + msgText);
        return R.ok("本次共发送了" + webhookList.length + "个群机器人，消息内容：" + msgText);
    }
    //------------------------- 链接消息 ----------------------------

    @ApiOperation("发送链接消息（图文卡片）")
    @PostMapping("/sendLink")
    public R sendLink(@RequestBody DingTalkRobotMessages.LinkMessageRequest request) throws Exception {
        DingTalkClient client = createClient(request.getToken(), request.getSecret());
        OapiRobotSendRequest req = new OapiRobotSendRequest();
        req.setMsgtype("link");

        OapiRobotSendRequest.Link link = new OapiRobotSendRequest.Link();
        link.setTitle(request.getTitle());
        link.setText(request.getText());
        link.setMessageUrl(request.getMessageurl());
        link.setPicUrl(request.getPicurl());
        req.setLink(link);

        return executeAndHandle(client, req);
    }

    //------------------------ Markdown消息 ------------------------


    @ApiOperation("发送Markdown格式消息")
    @PostMapping("/sendMarkdown")
    public R sendMarkdown(@RequestBody DingTalkRobotMessages.MarkdownMessageRequest request) throws Exception {
        DingTalkClient client = createClient(request.getToken(), request.getSecret());
        OapiRobotSendRequest req = new OapiRobotSendRequest();
        req.setMsgtype("markdown");

        OapiRobotSendRequest.Markdown md = new OapiRobotSendRequest.Markdown();
        md.setTitle(request.getTitle());
        md.setText(request.getText());
        req.setMarkdown(md);

        if (request.getAtuserids() != null && !request.getAtuserids().isEmpty()) {
            OapiRobotSendRequest.At at = new OapiRobotSendRequest.At();
            at.setAtUserIds(request.getAtuserids());
            req.setAt(at);
        }

        return executeAndHandle(client, req);
    }

    @ApiOperation("发送Markdown格式消息（模板驱动）")
    @PostMapping("/sendMarkdownMsg")
    public R<String> sendMarkdownMsg(@RequestBody String json, @RequestParam(required = false) String tid) {
        //if (StringUtils.isBlank(tid)) {
        //    LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //    tid = loginUser.getTenantid();
        //}
        // 获得用户数据 DingmsgPojo是工具类，接参通用于钉钉和企业微信
        DingmsgPojo dingmsgPojoReq = JSONArray.parseObject(json, DingmsgPojo.class);
        String msgCode = dingmsgPojoReq.getMsgcode();
        String msgText = dingmsgPojoReq.getMsgtext();
        String msgTitle = "应凯钉钉API"; // 获取Markdown标题（标题在发出的信息中不会展示，可忽略）
        UtsWxehookmsgPojo dinghookmsgDB = this.utsDinghookmsgService.getEntityByMsgCode(msgCode, tid);
        if (dinghookmsgDB == null) {
            PrintColor.lv("dingbot：未找到对应的消息模板，跳出");
            return R.fail(msgCode + "信息模板未找到");
        }


        // 开始构建发送人列表: 使用Set来保证唯一性，避免重复
        Set<String> userListSet = new HashSet<>();

        // 1.先添加数据库中的用户列表
        String userListDb = dinghookmsgDB.getUserlist();
        if (StringUtils.isNotBlank(userListDb)) {
            Collections.addAll(userListSet, userListDb.split(","));
        }

        // 2.(代理中心特有)追加前端传入的用户列表
        String userListReq = dingmsgPojoReq.getUserlist();
        if (StringUtils.isNotBlank(userListReq)) {
            Collections.addAll(userListSet, userListReq.split(","));
        }
        PrintColor.zi("发送人列表1,2：前端传入" + userListReq + "，模板已有" + userListDb);

        // 3.发送人列表3处理ObjJson数组 转换钉钉userid
        String objJsonStr = dinghookmsgDB.getObjjson();
        if (StringUtils.isNotBlank(objJsonStr)) {
            JSONArray objJsonArray = JSONArray.parseArray(objJsonStr);
            for (Object objObj : objJsonArray) {
                JSONObject obj = (JSONObject) objObj;
                String oms_userid = obj.getString("objcode");

                if (oms_userid.trim().startsWith("$")) { // 如果是VM模板，进行渲染
                    oms_userid = VelocityUtils.renderTemplateToData(oms_userid, msgText);
                }

                // 根据oms_userid查询对应的钉钉用户ID
                String ding_userid = utsDingmsgService.getDingUserIdByOmsUserid(oms_userid, tid);

                // 如果查到了钉钉用户ID，添加到发送人列表
                if (StringUtils.isNotBlank(ding_userid)) {
                    userListSet.add(ding_userid); // 添加到Set中，避免重复
                }
            }
        }
        PrintColor.zi("发送人列表3：消息模板数据库.ObjJson:" + objJsonStr);
        // 将Set转换为String，逗号分隔
        String finalUserListString = String.join(",", userListSet);
        // 打印最终的发送人列表
        PrintColor.zi("替换后追加到发送人列表1,2之后： 最终发送人列表：" + finalUserListString);

        // 4.是否转换信息Vm模版
        String msgVM = dinghookmsgDB.getMsgtemplate();
        if (StringUtils.isNotBlank(msgVM)) {
            // 根据模版和内容 返回填充后数据
            msgText = VelocityUtils.renderTemplateToData(msgVM, msgText);

            PrintColor.zi("Vm模版替换后的消息内容：" + msgText);
        }

        // 处理@提醒用户列表

        String mentions;
        // 处理@all情况
        boolean isAtAll = "all".equals(userListReq) || "all".equals(userListDb);
        if (isAtAll) {
            mentions = "@所有人";
        } else {
            // 区分处理用户ID和手机号
            List<String> mentionsList = new ArrayList<>();
            for (String user : userListSet) {
                // 用户ID格式
                mentionsList.add("@" + user);

            }
            mentions = String.join(" ", mentionsList);
        }

        // 将@提醒追加到Markdown内容末尾
        msgText += "\n\n" + mentions;

        // 5.要发送的所有群机器人webhookurl、@的所有人员
        String webhookListString = dinghookmsgDB.getWebhooklist().trim();
        List<String> userList = Arrays.asList(finalUserListString.split(","));
        String[] webhookList = webhookListString.split(",");
        // 钉钉群机器人webhook格式：https://oapi.dingtalk.com/robot/send?access_token=xxx&secret=yyy,https://oapi.dingtalk.com/robot/send?access_token=xxx&secret=yyy
        // 需要拆分access_token和签名secret(根据这两个参数才能发送消息)
        for (String webhookUrl : webhookList) {
            try {
                // 解析Webhook参数
                Map<String, String> webhookParams = parseDingTalkWebhookUrl(webhookUrl);

                // 构造请求参数
                DingTalkRobotMessages.MarkdownMessageRequest req = new DingTalkRobotMessages.MarkdownMessageRequest();
                req.setToken(webhookParams.get("access_token"));
                req.setSecret(webhookParams.get("secret"));
                req.setTitle(msgTitle);
                req.setText(msgText);
                req.setAtuserids(userList);
                // 如果前端传入的userListReq为all或者消息模板数据库已有的.UserList为all，则设置为@所有人
                if ("all".equals(userListReq) || "all".equals(userListDb)) {
                    req.setIsatall(true);
                }
                // 发送群机器人消息
                // 发送MD消息
                sendMarkdown(req);
            } catch (Exception e) {
                throw new RuntimeException("发送Markdown消息失败：" + webhookUrl, e);
            }
        }
        PrintColor.zi("本次共发送了" + webhookList.length + "个群机器人，消息内容：" + msgText);
        return R.ok("本次共发送了" + webhookList.length + "个群机器人，消息内容：" + msgText);
    }

    //---------------------- ActionCard消息 ------------------------


    @ApiOperation("发送ActionCard卡片消息（支持单/多按钮）")
    @PostMapping("/sendActionCard")
    public R sendActionCard(@RequestBody DingTalkRobotMessages.ActionCardRequest request) throws Exception {
        DingTalkClient client = createClient(request.getToken(), request.getSecret());
        OapiRobotSendRequest req = new OapiRobotSendRequest();
        req.setMsgtype("actionCard");

        OapiRobotSendRequest.Actioncard card = new OapiRobotSendRequest.Actioncard();
        card.setTitle(request.getTitle());
        card.setText(request.getText());

        // 单按钮模式
        if (StringUtils.isNotBlank(request.getSingletitle()) && StringUtils.isNotBlank(request.getSingleurl())) {
            card.setSingleTitle(request.getSingletitle());
            card.setSingleURL(request.getSingleurl());
        }
        // 多按钮模式
        else if (request.getButtons() != null && !request.getButtons().isEmpty()) {
            card.setBtnOrientation(request.getBtnorientation());
            card.setBtns(request.getButtons().stream().map(b -> {
                OapiRobotSendRequest.Btns btn = new OapiRobotSendRequest.Btns();
                btn.setTitle(b.getTitle());
                btn.setActionURL(b.getActionurl());
                return btn;
            }).collect(Collectors.toList()));
        }

        req.setActionCard(card);
        return executeAndHandle(client, req);
    }

    //--------------------- FeedCard消息 --------------------------

    @ApiOperation("发送FeedCard信息流卡片")
    @PostMapping("/sendFeedCard")
    public R sendFeedCard(@RequestBody DingTalkRobotMessages.FeedCardRequest request) throws Exception {
        DingTalkClient client = createClient(request.getToken(), request.getSecret());
        OapiRobotSendRequest req = new OapiRobotSendRequest();
        req.setMsgtype("feedCard");

        OapiRobotSendRequest.Feedcard feedCard = new OapiRobotSendRequest.Feedcard();
        feedCard.setLinks(request.getLinks().stream().map(link -> {
            OapiRobotSendRequest.Links l = new OapiRobotSendRequest.Links();
            l.setTitle(link.getTitle());
            l.setPicURL(link.getPicurl());
            l.setMessageURL(link.getMessageurl());
            return l;
        }).collect(Collectors.toList()));

        req.setFeedCard(feedCard);
        return executeAndHandle(client, req);
    }

    //------------------------ 工具方法 ---------------------------
    private DingTalkClient createClient(String token, String secret) throws Exception {
        String finalToken = StringUtils.isBlank(token) ? defaultDingBotToken : token;
        String finalSecret = StringUtils.isBlank(secret) ? defaultDingBotSecret : secret;

        // 生成签名URL
        Long timestamp = System.currentTimeMillis();
        String stringToSign = timestamp + "\n" + finalSecret;
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(finalSecret.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
        byte[] signData = mac.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));
        String sign = URLEncoder.encode(Base64.encodeBase64String(signData), "UTF-8");

        String url = "https://oapi.dingtalk.com/robot/send?access_token=" + finalToken
                + "&timestamp=" + timestamp + "&sign=" + sign;
        return new DefaultDingTalkClient(url);
    }

    private R executeAndHandle(DingTalkClient client, OapiRobotSendRequest request) {
        try {
            client.execute(request);
            return R.ok("消息发送成功");
        } catch (ApiException e) {
            return R.fail("钉钉API异常: " + e.getErrCode() + " - " + e.getErrMsg());
        }
    }
}