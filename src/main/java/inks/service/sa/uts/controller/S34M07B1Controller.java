package inks.service.sa.uts.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.uts.domain.vo.TableInfoDTO;
import inks.service.sa.uts.service.UtsBackupconfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 数据库备份配置(Uts_BackupConfig)表控制层
 *
 * <AUTHOR>
 * @since 2025-02-11 16:52:45
 */
@RestController
@RequestMapping("S34M07B1")
@Api(tags = "S34M07B1:数据库备份配置")
public class S34M07B1Controller extends UtsBackupconfigController {
    private final static Logger log = LoggerFactory.getLogger(S34M07B1Controller.class);
    @Resource
    private UtsBackupconfigService utsBackupconfigService;
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = "执行备份 （EnabledMark=1的备份配置都执行）", notes = "", produces = "application/json")
    @RequestMapping(value = "/backupAll", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Uts_BackupConfig.List")
    public R backupAll() {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

            return R.ok(this.utsBackupconfigService.backup(null));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "执行备份", notes = "", produces = "application/json")
    @RequestMapping(value = "/backup", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Uts_BackupConfig.List")
    public R backup(String key) {
        try {
            // 获得用户数据
            //LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

            return R.ok(this.utsBackupconfigService.backup(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "还原备份", notes = "上传 .sql 文件并执行还原", produces = "application/json")
    @RequestMapping(value = "/restore", method = RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<String> restore(
            @RequestParam("url") String url,
            @RequestParam("username") String username,
            @RequestParam("password") String password,
            @RequestParam(value = "driver", required = false, defaultValue = "com.mysql.cj.jdbc.Driver") String driver,
            @RequestParam(value = "email", required = false) String email,
            @RequestPart("file") MultipartFile sqlFile) {
        LoginUser loginUser = saRedisService.getLoginUser();
        if (sqlFile == null || sqlFile.isEmpty()) {
            return R.fail("请上传文件");
        }
        try {
            // 读取文件内容为字符串
            String sqlContent;
            try (InputStream in = sqlFile.getInputStream();
                 InputStreamReader isr = new InputStreamReader(in, StandardCharsets.UTF_8);
                 BufferedReader reader = new BufferedReader(isr)) {
                StringBuilder sb = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    sb.append(line).append("\n");
                }
                sqlContent = sb.toString();
            }
            // 将 SQL 内容传给服务层执行还原
            String result = utsBackupconfigService.restore(loginUser, url, username, password, driver, sqlContent, email);
            return R.ok(result);
        } catch (Exception e) {
            return R.fail("还原失败：" + e.getMessage());
        }
    }


    @ApiOperation(value = "查询表信息", notes = "查询当前应用连接数据库(inkssaas)的所有表、注释及行数，按行数降序排列", produces = "application/json")
    @GetMapping("/tablesInfo") // 使用 GetMapping
    public R<List<TableInfoDTO>> getTablesInfo(String key) {
        try {
            List<TableInfoDTO> tableInfos = this.utsBackupconfigService.getTablesInfo(key);
            return R.ok(tableInfos);
        } catch (Exception e) {
            log.error("查询表信息Failed to get tables info: {}", e.getMessage(), e);
            return R.fail("查询表信息失败: " + e.getMessage());
        }
    }

}
