package inks.service.sa.uts.controller;

import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * SSH流水线执行历史(Sa_SshHistory)表控制层
 *
 * <AUTHOR>
 * @since 2025-05-19 11:11:27
 */
@RestController
@RequestMapping("S34M11B3")
@Api(tags = "S34M11B3:SSH流水线执行历史")
public class S34M11B3Controller extends SaSshhistoryController {

}
