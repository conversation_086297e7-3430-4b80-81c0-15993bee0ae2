package inks.service.sa.uts.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.uts.domain.pojo.SaSshserversPojo;
import inks.service.sa.uts.service.SaSshserversService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * SSH服务器配置(Sa_SshServers)表控制层
 *
 * <AUTHOR>
 * @since 2025-05-19 11:11:06
 */
//@RestController
//@RequestMapping("saSshservers")
public class SaSshserversController {
    private final static Logger logger = LoggerFactory.getLogger(SaSshserversController.class);
    @Resource
    private SaSshserversService saSshserversService;
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = " 获取SSH服务器配置详细信息", notes = "获取SSH服务器配置详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_SshServers.List")
    public R<SaSshserversPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saSshserversService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_SshServers.List")
    public R<PageInfo<SaSshserversPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_SshServers.CreateDate");
            // 获得用户数据
            //LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saSshserversService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增SSH服务器配置", notes = "新增SSH服务器配置", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_SshServers.Add")
    public R<SaSshserversPojo> create(@RequestBody String json) {
        try {
            SaSshserversPojo saSshserversPojo = JSONArray.parseObject(json, SaSshserversPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saSshserversPojo.setCreateby(loginUser.getRealName());   // 创建者
            saSshserversPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saSshserversPojo.setCreatedate(new Date());   // 创建时间
            saSshserversPojo.setLister(loginUser.getRealname());   // 制表
            saSshserversPojo.setListerid(loginUser.getUserid());    // 制表id
            saSshserversPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saSshserversService.insert(saSshserversPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改SSH服务器配置", notes = "修改SSH服务器配置", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_SshServers.Edit")
    public R<SaSshserversPojo> update(@RequestBody String json) {
        try {
            SaSshserversPojo saSshserversPojo = JSONArray.parseObject(json, SaSshserversPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saSshserversPojo.setLister(loginUser.getRealname());   // 制表
            saSshserversPojo.setListerid(loginUser.getUserid());    // 制表id
            saSshserversPojo.setModifydate(new Date());   //修改时间
            //            saSshserversPojo.setAssessor(""); // 审核员
            //            saSshserversPojo.setAssessorid(""); // 审核员id
            //            saSshserversPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saSshserversService.update(saSshserversPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除SSH服务器配置", notes = "删除SSH服务器配置", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_SshServers.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saSshserversService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_SshServers.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaSshserversPojo saSshserversPojo = this.saSshserversService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saSshserversPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

