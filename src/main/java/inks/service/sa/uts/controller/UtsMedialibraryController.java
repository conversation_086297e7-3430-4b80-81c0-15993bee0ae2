package inks.service.sa.uts.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.domain.R;
import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.UtsMedialibraryPojo;
import inks.service.sa.uts.service.UtsMedialibraryService;
import inks.sa.common.core.service.SaRedisService;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import net.sf.jasperreports.engine.*;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;
/**
 * 素材库(Uts_MediaLibrary)表控制层
 *
 * <AUTHOR>
 * @since 2025-06-13 15:45:12
 */
//@RestController
//@RequestMapping("utsMedialibrary")
public class UtsMedialibraryController {
    @Resource
    private UtsMedialibraryService utsMedialibraryService;
    
    @Resource
    private SaRedisService saRedisService;
    
    private final static Logger logger = LoggerFactory.getLogger(UtsMedialibraryController.class);


    @ApiOperation(value=" 获取素材库详细信息", notes="获取素材库详细信息", produces="application/json")
    @RequestMapping(value="/getEntity",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Uts_MediaLibrary.List")
    public R<UtsMedialibraryPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.utsMedialibraryService.getEntity(key));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    
    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageList",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_MediaLibrary.List")
    public R<PageInfo<UtsMedialibraryPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
            if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Uts_MediaLibrary.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.utsMedialibraryService.getPageList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    
    @ApiOperation(value=" 新增素材库", notes="新增素材库", produces="application/json")
    @RequestMapping(value="/create",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_MediaLibrary.Add")
    public R<UtsMedialibraryPojo> create(@RequestBody String json) {
        try {
            UtsMedialibraryPojo utsMedialibraryPojo = JSONArray.parseObject(json,UtsMedialibraryPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            utsMedialibraryPojo.setCreateby(loginUser.getRealName());   // 创建者
            utsMedialibraryPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            utsMedialibraryPojo.setCreatedate(new Date());   // 创建时间
            utsMedialibraryPojo.setLister(loginUser.getRealname());   // 制表
            utsMedialibraryPojo.setListerid(loginUser.getUserid());    // 制表id
            utsMedialibraryPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.utsMedialibraryService.insert(utsMedialibraryPojo));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    @ApiOperation(value="修改素材库", notes="修改素材库", produces="application/json")
    @RequestMapping(value="/update",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_MediaLibrary.Edit")
    public R<UtsMedialibraryPojo> update(@RequestBody String json) {
        try {
            UtsMedialibraryPojo utsMedialibraryPojo = JSONArray.parseObject(json,UtsMedialibraryPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            utsMedialibraryPojo.setLister(loginUser.getRealname());   // 制表
            utsMedialibraryPojo.setListerid(loginUser.getUserid());    // 制表id
            utsMedialibraryPojo.setModifydate(new Date());   //修改时间
    //            utsMedialibraryPojo.setAssessor(""); // 审核员
    //            utsMedialibraryPojo.setAssessorid(""); // 审核员id
    //            utsMedialibraryPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.utsMedialibraryService.update(utsMedialibraryPojo));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    @ApiOperation(value="删除素材库", notes="删除素材库", produces="application/json")
    @RequestMapping(value="/delete",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Uts_MediaLibrary.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.utsMedialibraryService.delete(key));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Uts_MediaLibrary.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        UtsMedialibraryPojo utsMedialibraryPojo = this.utsMedialibraryService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(utsMedialibraryPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content ;
        if (reportsPojo != null ) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response= ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
    }

