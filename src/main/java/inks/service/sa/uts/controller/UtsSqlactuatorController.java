package inks.service.sa.uts.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.uts.domain.pojo.UtsSqlactuatorPojo;
import inks.service.sa.uts.service.UtsSqlactuatorService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * SQL执行器(Uts_SqlActuator)表控制层
 *
 * <AUTHOR>
 * @since 2024-10-06 16:23:35
 */
//@RestController
//@RequestMapping("utsSqlactuator")
public class UtsSqlactuatorController {
    private final static Logger logger = LoggerFactory.getLogger(UtsSqlactuatorController.class);
    @Resource
    private UtsSqlactuatorService utsSqlactuatorService;
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = " 获取SQL执行器详细信息", notes = "获取SQL执行器详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Uts_SqlActuator.List")
    public R<UtsSqlactuatorPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.utsSqlactuatorService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_SqlActuator.List")
    public R<PageInfo<UtsSqlactuatorPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Uts_SqlActuator.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.utsSqlactuatorService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增SQL执行器", notes = "新增SQL执行器", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_SqlActuator.Add")
    public R<UtsSqlactuatorPojo> create(@RequestBody String json) {
        try {
            UtsSqlactuatorPojo utsSqlactuatorPojo = JSONArray.parseObject(json, UtsSqlactuatorPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            utsSqlactuatorPojo.setCreateby(loginUser.getRealName());   // 创建者
            utsSqlactuatorPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            utsSqlactuatorPojo.setCreatedate(new Date());   // 创建时间
            utsSqlactuatorPojo.setLister(loginUser.getRealname());   // 制表
            utsSqlactuatorPojo.setListerid(loginUser.getUserid());    // 制表id
            utsSqlactuatorPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.utsSqlactuatorService.insert(utsSqlactuatorPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改SQL执行器", notes = "修改SQL执行器", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_SqlActuator.Edit")
    public R<UtsSqlactuatorPojo> update(@RequestBody String json) {
        try {
            UtsSqlactuatorPojo utsSqlactuatorPojo = JSONArray.parseObject(json, UtsSqlactuatorPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            utsSqlactuatorPojo.setLister(loginUser.getRealname());   // 制表
            utsSqlactuatorPojo.setListerid(loginUser.getUserid());    // 制表id
            utsSqlactuatorPojo.setModifydate(new Date());   //修改时间
            //            utsSqlactuatorPojo.setAssessor(""); // 审核员
            //            utsSqlactuatorPojo.setAssessorid(""); // 审核员id
            //            utsSqlactuatorPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.utsSqlactuatorService.update(utsSqlactuatorPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除SQL执行器", notes = "删除SQL执行器", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Uts_SqlActuator.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.utsSqlactuatorService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Uts_SqlActuator.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        UtsSqlactuatorPojo utsSqlactuatorPojo = this.utsSqlactuatorService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(utsSqlactuatorPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

