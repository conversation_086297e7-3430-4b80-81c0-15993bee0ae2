package inks.service.sa.uts.controller;

import com.alibaba.fastjson.JSONObject;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.uts.domain.pojo.UtsEmailmsgPojo;
import inks.service.sa.uts.mapper.UtsEmailmsgMapper;
import inks.service.sa.uts.utils.PrintColor;
import inks.service.sa.uts.utils.VelocityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.MailException;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.mail.MessagingException;
import javax.mail.internet.AddressException;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.util.Arrays;
import java.util.Objects;

import static org.apache.commons.lang3.StringUtils.*;

/**
 * 邮件信息(Uts_EmailMsg)表控制层
 *
 * <AUTHOR>
 * @since 2025-02-28 14:01:05
 */
@RestController
@RequestMapping("S34M08B1")
@Api(tags = "S34M08B1:邮件信息")
public class S34M08B1Controller extends UtsEmailmsgController {
    @Resource
    private UtsEmailmsgMapper utsEmailmsgMapper;
    @Resource
    private JavaMailSender javaMailSender;
    //@Value("${spring.mail.toEmail}")
    //private String toEmail;
    @Value("${spring.mail.username:}")
    private String fromEmail;
    @Resource
    private SaRedisService saRedisService;

    @PostMapping("/sendEmailByCode")
    @ResponseBody
    @ApiOperation(value = "发送邮件,接收json格式数据,参数:msgcode,msgtext", notes = "", produces = "application/json")
    public synchronized R<String> sendEmailByCode(@RequestBody String json, @RequestParam(required = false) String tid) {
        if (StringUtils.isBlank(tid)) {
            tid = saRedisService.getLoginUser(ServletUtils.getRequest()).getTenantid();
        }
        // 从json中获取code和content
        JSONObject jsonObject = JSONObject.parseObject(json);
        String msgCode = jsonObject.getString("code");
        String emailContent = jsonObject.getString("data");
        // 根据code获取邮件信息
        UtsEmailmsgPojo emailMsgPojo = utsEmailmsgMapper.getEntityByMsgCode(msgCode, tid);
        if (emailMsgPojo == null) {
            PrintColor.lv("email：未找到对应的消息模板，跳出");
            return R.fail("email：未找到对应的消息模板，跳出");
        }
        // 如果有vm模板，则渲染模板 无论是text还是html
        String msgVM = emailMsgPojo.getMsgtemplate();
        if (isNotBlank(msgVM)) {
            // 根据模版和内容 返回填充后数据
            emailContent = VelocityUtils.renderTemplateToData(msgVM, emailContent);
        }
        // 发送邮件
        try {
            sendEmailCC(emailMsgPojo.getRecipient(), emailMsgPojo.getCcpeople(), emailMsgPojo.getMsgname(), emailContent);
        } catch (MessagingException e) {
            e.printStackTrace();
            return R.fail("发送失败");
        }
        //PrintColor.red("发送成功.收件人:" + emailMsgPojo.getRecipient() + ",\n抄送人:" + emailMsgPojo.getCcpeople() + ",\n主题:" + emailMsgPojo.getMsgname() + ",\n内容:" + emailContent);
        PrintColor.red("发送成功.收件人:" + emailMsgPojo.getRecipient() + ",\n抄送人:" + emailMsgPojo.getCcpeople() + ",\n主题:" + emailMsgPojo.getMsgname());
        return R.ok("发送成功.收件人:" + emailMsgPojo.getRecipient() + ",\n抄送人:" + emailMsgPojo.getCcpeople() + ",\n主题:" + emailMsgPojo.getMsgname() + ",\n内容:" + emailContent);
    }

    /**
     * @Description 原始简陋版发邮件
     * <AUTHOR>
     * @param[1] to 收件人邮箱
     * @param[2] subject 主题
     * @param[3] content 内容
     * @time 2023/5/24 22:08
     */
    public void sendEmail(String to, String subject, String content) throws MessagingException {
        MimeMessage message = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
        helper.setFrom(fromEmail);
        helper.setTo(to);
        helper.setSubject(subject);
        //String content111 = "<h1>一号html</h1><h3>3号html</h3>";
        helper.setText(content, true); // true表示使用HTML格式
        javaMailSender.send(message);
    }


    /**
     * @param toEmail  收件人邮箱
     * @param ccEmails 抄送人邮箱，多个邮箱使用逗号分隔
     * @param subject  主题
     * @param content  内容
     * @throws MessagingException 发送邮件时可能抛出的异常
     * @Description 发邮件 附加抄送人
     */
    public void sendEmailCC(String toEmail, String ccEmails, String subject, String content) throws MessagingException {
        try {
            if (isBlank(toEmail)) throw new IllegalArgumentException("收件人邮箱不能为空");

            MimeMessage message = javaMailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
            helper.setFrom(fromEmail);
            helper.setTo(toEmail);
            helper.setSubject(subject);
            helper.setText(content, true); // true表示使用HTML格式
            // 添加抄送人
            if (isNotBlank(ccEmails)) {
                String[] ccEmailArray = ccEmails.split(",");
                InternetAddress[] ccAddresses = Arrays.stream(ccEmailArray)
                        .map(String::trim)
                        .map(email -> {
                            try {
                                return new InternetAddress(email);
                            } catch (AddressException e) {   //抄送人邮件地址不合法时可以跳过 列如：100@@qq.com,**********可以发送给**********
                                e.printStackTrace();
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        .toArray(InternetAddress[]::new);
                if (ccAddresses.length > 0) {
                    helper.setCc(ccAddresses);
                }
            }
            javaMailSender.send(message);
        } catch (MailException e) {
            e.printStackTrace();
            throw new MessagingException("发送邮件失败");
        }
    }


    /**
     * @param emailString 包含多个邮箱地址的字符串，使用逗号分隔 格式为: 收件人邮箱,抄送人邮箱1,抄送人邮箱2
     * @param subject     主题
     * @param content     内容
     * @throws MessagingException 发送邮件时可能抛出的异常
     * @Description 发邮件 附加抄送人
     */
    public void sendEmailCC(String emailString, String subject, String content) throws MessagingException {
        String[] emailAddresses = emailString.split(",");
        if (emailAddresses.length < 1) {
            throw new IllegalArgumentException("邮箱地址不能为空");
        }
        String to = emailAddresses[0].trim(); // 第一个邮箱地址设置为收件人
        // 设置抄送人（之后的邮箱都为抄送人）
        InternetAddress[] ccAddresses = new InternetAddress[emailAddresses.length - 1];
        for (int i = 1; i < emailAddresses.length; i++) {
            ccAddresses[i - 1] = new InternetAddress(emailAddresses[i].trim());
        }

        MimeMessage message = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
        helper.setFrom(fromEmail);
        helper.setTo(to);
        helper.setSubject(subject);
        helper.setText(content, true); // true表示使用HTML格式
        // 添加抄送人
        if (isNotBlank(ccAddresses.toString())) {
            PrintColor.zi("cc:" + ccAddresses.toString());
            helper.setCc(ccAddresses);
        }
        javaMailSender.send(message);
    }

    @ApiOperation(value = " sendEmailTest", notes = "", produces = "application/json")
    @RequestMapping(value = "/sendEmailTest", method = RequestMethod.POST)
    public String sendEmailTest() throws MessagingException {
//        LoginUser loginUser = tokenService.getLoginUser();
        String emailString = "<EMAIL>,<EMAIL>,<EMAIL>";
        // 获取系统参数里的[收件人&抄送人字符串]
//        String emails = wkWipnoteMapper.getEmails("system.email.people", loginUser.getTenantid());
        String[] emailAddresses = emailString.split(",");
        if (emailAddresses.length < 1) {
            throw new IllegalArgumentException("邮箱地址不能为空");
        }

        String to = emailAddresses[0].trim(); // 第一个邮箱地址设置为收件人
        // 设置抄送人（之后的邮箱都为抄送人）
        InternetAddress[] ccAddresses = new InternetAddress[emailAddresses.length - 1];
        for (int i = 1; i < emailAddresses.length; i++) {
            ccAddresses[i - 1] = new InternetAddress(emailAddresses[i].trim());
        }

        MimeMessage message = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
        helper.setFrom("<EMAIL>");
        helper.setTo(to);
        helper.setSubject("subject");
        helper.setText("content", true); // true表示使用HTML格式


        // 添加抄送人
        if (isNotBlank(ccAddresses.toString())) {
            PrintColor.zi("cc:" + ccAddresses.toString());
            helper.setCc(ccAddresses);
        }
        javaMailSender.send(message);
        return "success";
    }


}
