package inks.service.sa.uts.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.domain.R;
import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.UtsVideolibraryPojo;
import inks.service.sa.uts.service.UtsVideolibraryService;
import inks.sa.common.core.service.SaRedisService;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import net.sf.jasperreports.engine.*;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;
/**
 * 视频信息表(Uts_VideoLibrary)表控制层
 *
 * <AUTHOR>
 * @since 2025-05-29 14:16:34
 */
//@RestController
//@RequestMapping("utsVideolibrary")
public class UtsVideolibraryController {
    @Resource
    private UtsVideolibraryService utsVideolibraryService;
    
    @Resource
    private SaRedisService saRedisService;
    
    private final static Logger logger = LoggerFactory.getLogger(UtsVideolibraryController.class);


    @ApiOperation(value=" 获取视频信息表详细信息", notes="获取视频信息表详细信息", produces="application/json")
    @RequestMapping(value="/getEntity",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Uts_VideoLibrary.List")
    public R<UtsVideolibraryPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.utsVideolibraryService.getEntity(key));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    
    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageList",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_VideoLibrary.List")
    public R<PageInfo<UtsVideolibraryPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
            if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Uts_VideoLibrary.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.utsVideolibraryService.getPageList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    
    @ApiOperation(value=" 新增视频信息表", notes="新增视频信息表", produces="application/json")
    @RequestMapping(value="/create",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_VideoLibrary.Add")
    public R<UtsVideolibraryPojo> create(@RequestBody String json) {
        try {
            UtsVideolibraryPojo utsVideolibraryPojo = JSONArray.parseObject(json,UtsVideolibraryPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            utsVideolibraryPojo.setCreateby(loginUser.getRealName());   // 创建者
            utsVideolibraryPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            utsVideolibraryPojo.setCreatedate(new Date());   // 创建时间
            utsVideolibraryPojo.setLister(loginUser.getRealname());   // 制表
            utsVideolibraryPojo.setListerid(loginUser.getUserid());    // 制表id
            utsVideolibraryPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.utsVideolibraryService.insert(utsVideolibraryPojo));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    @ApiOperation(value="修改视频信息表", notes="修改视频信息表", produces="application/json")
    @RequestMapping(value="/update",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_VideoLibrary.Edit")
    public R<UtsVideolibraryPojo> update(@RequestBody String json) {
        try {
            UtsVideolibraryPojo utsVideolibraryPojo = JSONArray.parseObject(json,UtsVideolibraryPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            utsVideolibraryPojo.setLister(loginUser.getRealname());   // 制表
            utsVideolibraryPojo.setListerid(loginUser.getUserid());    // 制表id
            utsVideolibraryPojo.setModifydate(new Date());   //修改时间
    //            utsVideolibraryPojo.setAssessor(""); // 审核员
    //            utsVideolibraryPojo.setAssessorid(""); // 审核员id
    //            utsVideolibraryPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.utsVideolibraryService.update(utsVideolibraryPojo));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    @ApiOperation(value="删除视频信息表", notes="删除视频信息表", produces="application/json")
    @RequestMapping(value="/delete",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Uts_VideoLibrary.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.utsVideolibraryService.delete(key));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    @ApiOperation(value = "审核视频信息表", notes = "审核视频信息表", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Uts_VideoLibrary.Approval")
    public R<UtsVideolibraryPojo> approval(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            UtsVideolibraryPojo utsVideolibraryPojo = this.utsVideolibraryService.getEntity(key);
            if (utsVideolibraryPojo.getAssessor().equals(""))
            {
                utsVideolibraryPojo.setAssessor(loginUser.getRealname()); //审核员
                utsVideolibraryPojo.setAssessorid(loginUser.getUserid()); //审核员id
            }
                else
            {
                utsVideolibraryPojo.setAssessor(""); //审核员
                utsVideolibraryPojo.setAssessorid(""); //审核员
            }
            utsVideolibraryPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.utsVideolibraryService.approval(utsVideolibraryPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
    
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Uts_VideoLibrary.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        UtsVideolibraryPojo utsVideolibraryPojo = this.utsVideolibraryService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(utsVideolibraryPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content ;
        if (reportsPojo != null ) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response= ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
    }

