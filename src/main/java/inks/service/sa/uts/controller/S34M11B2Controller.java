package inks.service.sa.uts.controller;

import inks.common.core.domain.R;
import inks.service.sa.uts.domain.pojo.SshCommandResult;
import inks.service.sa.uts.domain.pojo.SshConnectionInfo;
import inks.service.sa.uts.domain.pojo.SshExecutionResult;
import inks.service.sa.uts.service.SshExecutorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * SSH流水线(Sa_SshPipelines)表控制层
 *
 * <AUTHOR>
 * @since 2025-05-19 11:11:27
 */
@RestController
@RequestMapping("S34M11B2")
@Api(tags = "S34M11B2:SSH流水线")
public class S34M11B2Controller extends SaSshpipelinesController {

    /**
     * SSH执行器服务，用于处理所有SSH连接和命令执行
     */
    @Resource
    private SshExecutorService sshExecutorService;

    /**
     * 测试SSH连接
     * <p>
     * 尝试连接到指定服务器并进行身份验证，不执行任何命令
     *
     * @param serverId 服务器ID，用于从数据库获取连接信息
     * @return 包含连接测试结果的响应对象
     */
    @ApiOperation(value = "测试SSH连接", notes = "测试指定服务器的SSH连接是否可用", produces = "application/json")
    @RequestMapping(value = "/testConnection", method = RequestMethod.GET)
    public R<Boolean> testConnection(@RequestParam String serverId) {
        try {
            // 获取当前登录用户
            //LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

            // 测试连接
            boolean result = sshExecutorService.testConnection(serverId);
            if (result) {
                return R.ok(result, "连接成功");
            } else {
                return R.fail(result, "连接失败");
            }
        } catch (Exception e) {
            return R.fail("连接测试失败: " + e.getMessage());
        }
    }


    @ApiOperation(value = "增量执行SSH流水线", notes = "增量执行SSH流水线并返回会话ID，每步执行完后可通过会话ID查询实时状态", produces = "application/json")
    @RequestMapping(value = "/executeIncrementalPipeline", method = RequestMethod.POST)
    public R<String> executeIncrementalPipeline(@RequestParam String pipelineId, @RequestParam String serverId) {
        try {
            // 获取当前登录用户
            //LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

            // 增量执行流水线
            String sessionId = sshExecutorService.executeIncrementalPipeline(pipelineId, serverId);
            return R.ok(sessionId, "增量执行任务已提交");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }



    @ApiOperation(value = "批量执行SSH流水线", notes = "在多台服务器上执行同一SSH流水线", produces = "application/json")
    @RequestMapping(value = "/executePipelineMultiple", method = RequestMethod.POST)
    public R<List<SshExecutionResult>> executePipelineMultiple(@RequestParam String pipelineId, @RequestBody List<String> serverIds) {
        try {
            // 获取当前登录用户
            //LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

            // 执行流水线到多台服务器
            List<SshExecutionResult> results = sshExecutorService.executePipelineToMultipleServers(pipelineId, serverIds);
            return R.ok(results);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "快速关机", notes = "对指定服务器执行安全关机操作", produces = "application/json")
    @RequestMapping(value = "/quickShutdown", method = RequestMethod.POST)
    public R<SshExecutionResult> quickShutdown(@RequestParam String serverId) {
        try {
            // 获取当前登录用户
            //LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

            // 执行关机流水线
            SshExecutionResult result = sshExecutorService.quickShutdown(serverId);
            return R.ok(result);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "快速重启", notes = "对指定服务器执行系统重启操作", produces = "application/json")
    @RequestMapping(value = "/quickRestart", method = RequestMethod.POST)
    public R<SshExecutionResult> quickRestart(@RequestParam String serverId) {
        try {
            // 获取当前登录用户
            //LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

            // 执行重启流水线
            SshExecutionResult result = sshExecutorService.quickRestart(serverId);
            return R.ok(result);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "异步执行SSH流水线", notes = "异步执行SSH流水线并返回会话ID", produces = "application/json")
    @RequestMapping(value = "/executeAsyncPipeline", method = RequestMethod.POST)
    public R<String> executeAsyncPipeline(@RequestParam String pipelineId, @RequestParam String serverId) {
        try {
            // 获取当前登录用户
            //LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

            // 异步执行流水线
            String sessionId = sshExecutorService.executeAsyncPipeline(pipelineId, serverId);
            return R.ok(sessionId, "异步执行任务已提交");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "取消SSH流水线执行", notes = "取消正在执行的SSH流水线", produces = "application/json")
    @RequestMapping(value = "/cancelExecution", method = RequestMethod.POST)
    public R<Boolean> cancelExecution(@RequestParam String sessionId) {
        try {
            // 获取当前登录用户
            //LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

            // 取消执行
            boolean result = sshExecutorService.cancelExecution(sessionId);
            return R.ok(result, result ? "取消成功" : "取消失败或会话已结束");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "获取SSH流水线执行状态", notes = "获取指定会话ID的执行状态", produces = "application/json")
    @RequestMapping(value = "/getExecutionStatus", method = RequestMethod.GET)
    public R<SshExecutionResult> getExecutionStatus(@RequestParam String sessionId) {
        try {
            // 获取当前登录用户
            //LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

            // 获取执行状态
            SshExecutionResult result = sshExecutorService.getExecutionStatus(sessionId);
            return R.ok(result);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "执行单个SSH命令", notes = "在指定服务器上执行单个SSH命令", produces = "application/json")
    @RequestMapping(value = "/executeCommand", method = RequestMethod.POST)
    public R<SshCommandResult> executeCommand(@RequestBody Map<String, Object> params) {
        try {
            // 获取当前登录用户
            //LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

            // 构建连接信息
            SshConnectionInfo connectionInfo = new SshConnectionInfo();
            connectionInfo.setServerId((String) params.get("serverId"));
            connectionInfo.setHost((String) params.get("host"));
            connectionInfo.setPort(Integer.parseInt(params.get("port").toString()));
            connectionInfo.setUsername((String) params.get("username"));
            connectionInfo.setPassword((String) params.get("password"));

            // 执行命令
            String command = (String) params.get("command");
            String successPattern = (String) params.get("successPattern");
            String errorPattern = (String) params.get("errorPattern");
            int timeoutMs = params.get("timeoutMs") != null ?
                    Integer.parseInt(params.get("timeoutMs").toString()) : 60000;

            SshCommandResult result = sshExecutorService.executeCommand(
                    connectionInfo, command, successPattern, errorPattern, timeoutMs);

            return R.ok(result);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
