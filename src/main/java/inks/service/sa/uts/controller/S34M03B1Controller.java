package inks.service.sa.uts.controller;

import com.alibaba.fastjson.JSON;
import inks.common.core.domain.LoginUser;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.uts.domain.pojo.UtsSqlactuatorPojo;
import inks.service.sa.uts.service.UtsSqlactuatorService;
import inks.service.sa.uts.service.sql.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * SQL执行器(Uts_SqlActuator)表控制层
 *
 * <AUTHOR>
 * @since 2024-10-06 16:23:35
 */
@Slf4j
@RestController
@RequestMapping("S34M03B1")
@Api(tags = "S34M03B1:SQL执行器")
public class S34M03B1Controller extends UtsSqlactuatorController {
    @Resource
    private SqlExecutorService2 sqlExecutorService2;
    @Resource
    private SqlExecutorService sqlExecutorService;
    @Resource
    private UtsSqlactuatorService utsSqlactuatorService;
    @Resource
    private SaRedisService saRedisService;

    @PostMapping("/execute")
    @ApiOperation("执行单条或多条SQL语句 （按分号分隔获取多个SQL语句）")
    public R<List<SqlExecutionResult>> executeSql(String key) {
        try {
            // 获取 SQL 执行器数据库对象
            UtsSqlactuatorPojo sqlActuatorDB = utsSqlactuatorService.getEntity(key);
            // 需要执行的 SQL语句
            String sqlData = sqlActuatorDB.getSqldata();
            String databaseid = sqlActuatorDB.getDatabaseid();

            // 将 SQL 数据分割成单个 SQL 语句，并过滤空白项
            List<SqlExecuteRequest2> sqlRequests = Arrays.stream(sqlData.split(";"))
                    .map(String::trim)
                    .filter(sql -> !sql.isEmpty())
                    .map(sql -> {
                        SqlExecuteRequest2 request = new SqlExecuteRequest2();
                        request.setSql(sql);
                        request.setDataSourceKey(databaseid);
                        return request;
                    })
                    .collect(Collectors.toList());

            log.info("收到SQL执行请求: {}", sqlRequests);

            // 执行批量 SQL
            List<SqlExecutionResult> results = sqlExecutorService2.executeBatchSql(sqlRequests);

            // 更新执行结果
            sqlActuatorDB.setResultjson(JSON.toJSONString(results));
            utsSqlactuatorService.update(sqlActuatorDB);

            return R.ok(results);
        } catch (SqlExecutionException e) {
            log.error("SQL执行失败", e);
            return R.fail(e.getMessage());
        } catch (Exception e) {
            log.error("未知错误发生", e);
            return R.fail("SQL执行过程中发生未知错误");
        }
    }


    @PostMapping("/executeTest")
    @ApiOperation("执行单条SQL语句")
    public R<SqlExecutionResult> executeTest(@RequestBody SqlExecuteRequest request) {
        try {
            // 检查并设置默认值
            if (request.getTimeout() == null) {
                request.setTimeout(30); // 默认超时时间
            }
            if (request.getMaxRows() == null) {
                request.setMaxRows(1000); // 默认最大返回行数
            }
            log.info("收到SQL执行请求: {}", request);
            SqlExecutionResult result = sqlExecutorService.executeSql(request);
            return R.ok(result);
        } catch (SqlExecutionException e) {
            log.error("SQL执行失败", e);
            return R.fail(e.getMessage());
        }
    }


    @PostMapping("/async")
    @ApiOperation("异步执行SQL语句")
    public R<String> executeAsyncSql(@RequestBody SqlExecuteRequest request) {
        try {
            // 检查并设置默认值
            if (request.getTimeout() == null) {
                request.setTimeout(30); // 默认超时时间
            }
            if (request.getMaxRows() == null) {
                request.setMaxRows(1000); // 默认最大返回行数
            }
            log.info("收到异步SQL执行请求: {}", request);
            sqlExecutorService.executeAsyncSql(request)
                    .thenAccept(result -> log.info("异步SQL执行完成: {}", result));
            return R.ok("异步执行已提交");
        } catch (Exception e) {
            log.error("异步SQL提交失败", e);
            return R.fail(e.getMessage());
        }
    }


    @PostMapping("/batch")
    @ApiOperation("批量执行SQL语句")
    public R<List<SqlExecutionResult>> executeBatchSql(@RequestBody List<SqlExecuteRequest> requests) {
        try {

            log.info("收到批量SQL执行请求, 数量: {}", requests.size());
            List<SqlExecutionResult> results = sqlExecutorService.executeBatchSql(requests);
            return R.ok(results);
        } catch (SqlExecutionException e) {
            log.error("批量SQL执行失败", e);
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 获取最新时间戳Entity", notes = "获取SQL执行器详细信息", produces = "application/json")
    @RequestMapping(value = "/getMaxEntity", method = RequestMethod.GET)
    public inks.common.core.domain.R<UtsSqlactuatorPojo> getMaxEntity(String code) {
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        try {
            return inks.common.core.domain.R.ok(this.utsSqlactuatorService.getMaxEntity(code));
        } catch (Exception e) {
            return inks.common.core.domain.R.fail(e.getMessage());
        }
    }
}
