package inks.service.sa.uts.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkworkflow_1_0.models.*;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.Common;
import com.aliyun.teautil.models.RuntimeOptions;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiV2DepartmentListsubRequest;
import com.dingtalk.api.request.OapiV2UserListRequest;
import com.dingtalk.api.response.OapiV2DepartmentListsubResponse;
import com.dingtalk.api.response.OapiV2UserListResponse;
import com.taobao.api.ApiException;
import inks.common.core.constant.CacheConstants;
import inks.common.core.constant.DingConstant;
import inks.common.core.domain.ApprrecPojo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaConfigService;
import inks.sa.common.core.service.SaRedisService;
import inks.sa.common.core.utils.HttpRequestUtil;
import inks.service.sa.uts.constant.ConfigConstant;
import inks.service.sa.uts.domain.pojo.UtsDingapprPojo;
import inks.service.sa.uts.domain.pojo.UtsDingapprrecPojo;
import inks.service.sa.uts.service.UtsDingapprService;
import inks.service.sa.uts.service.UtsDingapprrecService;
import inks.service.sa.uts.utils.ding.AccessTokenUtil;
import inks.service.sa.uts.utils.ding.DingCallbackCrypto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 钉钉审批(Uts_DingAppr)表控制层
 *
 * <AUTHOR>
 * @since 2023-01-10 11:11:31
 */
@RestController
@RequestMapping("S34M06B2")
@Api(tags = "S34M06B2:钉钉:审批模板")
public class S34M06B2Controller extends UtsDingapprController {

    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(S34M06B2Controller.class);
    /**
     * 服务对象
     */
    @Resource
    private UtsDingapprService utsDingapprService;
    /**
     * 服务对象
     */
    @Resource
    private UtsDingapprrecService utsDingapprrecService;
    @Resource
    private SaRedisService saRedisService;
    @Resource
    private SaConfigService saConfigService;

    /**
     * 使用 Token 初始化账号Client
     *
     * @return Client
     * @throws Exception
     */
    public static com.aliyun.dingtalkworkflow_1_0.Client createClient() throws Exception {
        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        return new com.aliyun.dingtalkworkflow_1_0.Client(config);
    }

    /**
     * 按模块编码查询报表
     *
     * @param code 主键
     * @return 查询结果
     */
    @ApiOperation(value = "按模块编码查询报表", notes = "按模块编码查询报表", produces = "application/json")
    @RequestMapping(value = "/getListByModuleCode", method = RequestMethod.GET)
    public R<List<UtsDingapprPojo>> getListByModuleCode(String code, String tid) {
        try {
            //有Tid参
            if (tid == null || tid.equals("")) {
                // 获得用户数据
                LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
                tid = loginUser.getTenantid();
            }
            List<UtsDingapprPojo> list = this.utsDingapprService.getListByModuleCode(code, tid);
            if (list != null) {
                for (int i = 0; i < list.size(); i++) {
                    String verifyKey = CacheConstants.APPR_CODES_KEY + list.get(i).getId();
                    ApprrecPojo apprrecPojo = new ApprrecPojo();
                    org.springframework.beans.BeanUtils.copyProperties(list.get(i), apprrecPojo);
                    apprrecPojo.setId("");
                    saRedisService.setCacheObject(verifyKey, apprrecPojo, 60 * 12, TimeUnit.MINUTES);
                    list.get(i).setDatatemp(null);
                }
            }
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "发出审批", notes = "发出审批", produces = "application/json")
    @RequestMapping(value = "/sendapprovel", method = RequestMethod.GET)
    public R sendapprovel(String key, String tid) {
        try {
            logger.info("-----开始发出审批-----");

            Map<String, String> mapcfg = saConfigService.getConfigAll();
            AccessTokenUtil.AppKey = mapcfg.get(ConfigConstant.DING_APPKEY);
            logger.info("钉钉免登 AppKey:" + AccessTokenUtil.AppKey);
            AccessTokenUtil.AppSecret = mapcfg.get(ConfigConstant.DING_APPSECRET);
            logger.info("钉钉免登 AppSecret:" + AccessTokenUtil.AppSecret);

            //获取企业微信审批TOKEN
            String token = AccessTokenUtil.getToken();
            logger.info("发出审批:token:" + token);

            com.aliyun.dingtalkworkflow_1_0.Client client = createClient();
            StartProcessInstanceHeaders startProcessInstanceHeaders = new StartProcessInstanceHeaders();
            startProcessInstanceHeaders.xAcsDingtalkAccessToken = token;

            // 读取redis审批报文josn
            String CachKey = CacheConstants.APPR_CODES_KEY + key;
            ApprrecPojo apprrecPojo = saRedisService.getCacheObject(CachKey, ApprrecPojo.class);
            // 将审批模版转实体类
            StartProcessInstanceRequest startProcessInstanceRequest = JSONArray.parseObject(apprrecPojo.getDatatemp(), StartProcessInstanceRequest.class);
            // 开始执行审批
            try {
                StartProcessInstanceResponse rsp = client.startProcessInstanceWithOptions(startProcessInstanceRequest, startProcessInstanceHeaders, new RuntimeOptions());
                // get审批id
                String sp_no = rsp.getBody().getInstanceId();
                logger.info("发出审批:sp_no:" + sp_no);
                // 存入审批id到 审批记录
                apprrecPojo.setApprsn(sp_no);
                UtsDingapprrecPojo utsDingapprrecPojo = new UtsDingapprrecPojo();
                org.springframework.beans.BeanUtils.copyProperties(apprrecPojo, utsDingapprrecPojo);
                this.utsDingapprrecService.insert(utsDingapprrecPojo);
                return R.ok(rsp.getBody());
            } catch (TeaException err) {

                if (!Common.empty(err.code) && !Common.empty(err.message)) {
                    // err 中含有 code 和 message 属性，可帮助开发定位问题
                }
                return R.fail(err.getMessage());

            } catch (Exception _err) {
                TeaException err = new TeaException(_err.getMessage(), _err);
                if (!Common.empty(err.code) && !Common.empty(err.message)) {
                    // err 中含有 code 和 message 属性，可帮助开发定位问题
                }
                return R.fail(_err.getMessage());
            }

        } catch (Exception e) {
            logger.info("发出审批:出错:" + e.getMessage());
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "事件回调", notes = "事件回调", produces = "application/json")
    @RequestMapping(value = "/callback", method = RequestMethod.POST)
    public Map<String, String> callback(
            @RequestParam(value = "msg_signature", required = false) String msg_signature,
            @RequestParam(value = "timestamp", required = false) String timeStamp,
            @RequestParam(value = "nonce", required = false) String nonce,
            @RequestBody(required = false) JSONObject json, String tid) {
        try {
            // 1. 从http请求中获取加解密参数

            // 2. 使用加解密类型
            // Constant.OWNER_KEY 说明：
            // 1、开发者后台配置的订阅事件为应用级事件推送，
            //      此时OWNER_KEY为应用的APP_KEY（企业内部应用）或SUITE_KEY（三方应用）。
            // 2、调用订阅事件接口订阅的事件为企业级事件推送，
            //      此时OWNER_KEY为：企业的CORP_ID（企业内部应用）或SUITE_KEY（三方应用）

            logger.info("-----开始签名校验-----");
            Map<String, String> mapcfg = saConfigService.getConfigAll();
            DingConstant.APP_KEY = mapcfg.get(ConfigConstant.DING_APPKEY);
            logger.info("钉钉免登 AppKey:" + DingConstant.APP_KEY);
            DingConstant.AES_TOKEN = mapcfg.get(ConfigConstant.DING_CALLBACK_TOKEN);
            logger.info("钉钉回调 Token:" + DingConstant.AES_TOKEN);
            DingConstant.AES_KEY = mapcfg.get(ConfigConstant.DING_CALLBACK_AESKEY);
            logger.info("钉钉回调 AESKEY:" + DingConstant.AES_KEY);

//            R r = new R();
//            //获取企业ID
//            r = systemFeignService.getConfigValue("D08M12.appkey", tid, "");
//            DingConstant.APP_KEY = r.getData().toString();
//            if (r.getCode() != 200) {
//                logger.info("注册回调:读取企业id失败 " + r.getMsg());
//            } else {
//                logger.info("注册回调: 应用key " + DingConstant.APP_KEY);
//            }
//            //获取回调token
//            r = systemFeignService.getConfigValue("D08M12.callbacktoken", tid, "");
//            DingConstant.AES_TOKEN = r.getData().toString();
//            if (r.getCode() != 200) {
//                logger.info("注册回调:读取callbacktoken失败 " + r.getMsg());
//            } else {
//                logger.info("注册回调:回调token:" + DingConstant.AES_TOKEN);
//            }
//
//            //获取回调密钥
//            r = systemFeignService.getConfigValue("D08M12.callbackaesKey", tid, "");
//            DingConstant.AES_KEY = r.getData().toString();
//            if (r.getCode() != 200) {
//                logger.info("注册回调:读取callbackAESKey失败 " + r.getMsg());
//            } else {
//                logger.info("注册回调:回调密钥:" + DingConstant.AES_KEY);
//            }

            DingCallbackCrypto callbackCrypto = new DingCallbackCrypto(DingConstant.AES_TOKEN, DingConstant.AES_KEY, DingConstant.APP_KEY);
            String encryptMsg = json.getString("encrypt");
            logger.info("注册回调:encryptMsg:" + encryptMsg);
            String decryptMsg = callbackCrypto.getDecryptMsg(msg_signature, timeStamp, nonce, encryptMsg);

            // 3. 反序列化回调事件json数据
            JSONObject eventJson = JSON.parseObject(decryptMsg);
            String eventType = eventJson.getString("EventType");
            // 4. 根据EventType分类处理
            if ("check_url".equals(eventType)) {
                // 测试回调url的正确性
                logger.info("测试回调url的正确性");
            } else if ("bpms_instance_change".equals(eventType)) {
                // 处理通讯录用户增加事件
                String type = eventJson.getString("type");
                // 如果是审批完成
                if ("finish".equals(type)) {
                    finishAppr(eventJson, tid);
                }
            } else {
                // 添加其他已注册的
                logger.info("发生了：" + eventType + "事件");
            }

            // 5. 返回success的加密数据
            Map<String, String> successMap = callbackCrypto.getEncryptedMap("success");
            return successMap;

        } catch (DingCallbackCrypto.DingTalkEncryptException e) {
            e.printStackTrace();
        }
        return null;
    }

    // 完成审批
    public void finishAppr(JSONObject eventJson, String tid) {

        String spno = eventJson.getString("processInstanceId");  // 审批实例id。
        //查询审批记录
        UtsDingapprrecPojo utsDingapprrecPojo = utsDingapprrecService.getEntityBySpno(spno, tid);
        utsDingapprrecPojo.setCallbackdate(new Date());
        utsDingapprrecPojo.setCallbackuuid(eventJson.getString("staffId"));  // 审批员工id
        utsDingapprrecPojo.setCallbackmsg(eventJson.toString());
        utsDingapprrecPojo.setModifydate(new Date());
        utsDingapprrecPojo.setRemark("");
        //审批状态
        String result = eventJson.getString("result");  // result为agree，拒绝时result为refuse

// 审批结果
        String callbackResult;
        boolean needCallback = false;
        boolean approved = false;

        if ("agree".equals(result)) {
            logger.info("审批回调：审核通过");
            callbackResult = "审批通过";
            needCallback = true;
            approved = true;
        } else if ("refuse".equals(result)) {
            logger.info("审批回调：审批拒绝");
            callbackResult = "审批拒绝";
            needCallback = true;
        } else {
            logger.info("审批回调：审批终止");
            callbackResult = "审批终止";
        }

        // 1. 写入数据库
        utsDingapprrecPojo.setCallbackresult(callbackResult);
        utsDingapprrecService.update(utsDingapprrecPojo);
        logger.info("审批回调：写入数据库完成");

        // 2. 只有 `agree` 和 `refuse` 需要写入 Redis
        if (needCallback) {
            ApprrecPojo apprrecPojo = new ApprrecPojo();
            org.springframework.beans.BeanUtils.copyProperties(utsDingapprrecPojo, apprrecPojo);
            String cacheKey = CacheConstants.APPR_CODES_KEY + apprrecPojo.getId();
            saRedisService.setCacheObject(cacheKey, apprrecPojo, 60L * 12, TimeUnit.MINUTES);
            logger.info("审批回调：写入 Redis 完成 " + cacheKey);
        }

        // 3. 只有 `agree` 和 `refuse` 需要回调
        if (needCallback) {
            String url = utsDingapprrecPojo.getCallbackurl().replace("{key}", utsDingapprrecPojo.getId()) + "&type=ding";
            if (!approved) {
                url += "&approved=false";
            }
            logger.info("审批回调：开始执行回调 URL -> " + url);
            HttpRequestUtil.sendGet(url);
        }


    }

    @ApiOperation(value = "根据审批key获得模版", notes = "根据审批key获得模版", produces = "application/json")
    @RequestMapping(value = "/getschema", method = RequestMethod.GET)
    public R getschema(String key, String tid) {
        try {
            logger.info("-----开始获得模版初始化-----");
            Map<String, String> mapcfg = saConfigService.getConfigAll();
            AccessTokenUtil.AppKey = mapcfg.get(ConfigConstant.DING_APPKEY);
            logger.info("钉钉免登 AppKey:" + AccessTokenUtil.AppKey);
            AccessTokenUtil.AppSecret = mapcfg.get(ConfigConstant.DING_APPSECRET);
            logger.info("钉钉免登 AppSecret:" + AccessTokenUtil.AppSecret);

            String CachKey = CacheConstants.APPR_CODES_KEY + key;
            ApprrecPojo apprrecPojo = saRedisService.getCacheObject(CachKey, ApprrecPojo.class);
            //获取企业微信审批TOKEN
            String token = AccessTokenUtil.getToken();
            logger.info("发出审批:token:" + token);

            com.aliyun.dingtalkworkflow_1_0.Client client = createClient();
            QuerySchemaByProcessCodeHeaders querySchemaByProcessCodeHeaders = new QuerySchemaByProcessCodeHeaders();
            querySchemaByProcessCodeHeaders.xAcsDingtalkAccessToken = token;
            QuerySchemaByProcessCodeRequest querySchemaByProcessCodeRequest = new QuerySchemaByProcessCodeRequest()
                    .setProcessCode(key);
            try {
                QuerySchemaByProcessCodeResponse rsp = client.querySchemaByProcessCodeWithOptions(querySchemaByProcessCodeRequest, querySchemaByProcessCodeHeaders, new RuntimeOptions());
                logger.info(rsp.getBody().getResult().toString());
                return R.ok(rsp.getBody());

            } catch (TeaException err) {
                if (!Common.empty(err.code) && !Common.empty(err.message)) {
                    // err 中含有 code 和 message 属性，可帮助开发定位问题

                }
                return R.fail(err.getMessage());
            } catch (Exception _err) {
                TeaException err = new TeaException(_err.getMessage(), _err);
                if (!Common.empty(err.code) && !Common.empty(err.message)) {
                    // err 中含有 code 和 message 属性，可帮助开发定位问题

                }
                return R.fail(_err.getMessage());
            }


        } catch (Exception e) {
            logger.info("发出审批:出错:" + e.getMessage());
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "获取钉钉部门及成员列表(旧版SDK)", notes = "使用旧版SDK获取所有一级部门及各部门下的成员列表", produces = "application/json")
    @RequestMapping(value = "/getDeptAndUsersLegacy", method = RequestMethod.GET)
    public R<JSONArray> getDeptAndUsersLegacy(String tid) {
        try {
            logger.info("-----开始获取钉钉部门及成员列表 (旧版SDK)-----");

            Map<String, String> mapcfg = saConfigService.getConfigAll();
            AccessTokenUtil.AppKey = mapcfg.get(ConfigConstant.DING_APPKEY);
            AccessTokenUtil.AppSecret = mapcfg.get(ConfigConstant.DING_APPSECRET);
            String token = AccessTokenUtil.getToken();
            logger.info("获取钉钉部门及成员列表: token:" + token);

            JSONArray resultJsonArray = new JSONArray();

            // 1. 获取部门列表
            DingTalkClient deptClient = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/department/listsub");
            OapiV2DepartmentListsubRequest deptRequest = new OapiV2DepartmentListsubRequest();
            deptRequest.setDeptId(1L); // 根部门ID为1
            OapiV2DepartmentListsubResponse deptsResponse = deptClient.execute(deptRequest, token);

            if (!deptsResponse.isSuccess()) {
                logger.error("获取钉钉部门列表失败: " + deptsResponse.getErrmsg());
                return R.fail("获取钉钉部门列表失败: " + deptsResponse.getErrmsg());
            }

            List<OapiV2DepartmentListsubResponse.DeptBaseResponse> deptList = deptsResponse.getResult();
            if (deptList == null || deptList.isEmpty()) {
                return R.ok(new JSONArray());
            }

            // 2. 遍历部门获取成员
            DingTalkClient userClient = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/list");
            for (OapiV2DepartmentListsubResponse.DeptBaseResponse dept : deptList) {
                JSONObject deptJson = new JSONObject();
                deptJson.put("deptId", dept.getDeptId());
                deptJson.put("deptName", dept.getName());

                OapiV2UserListRequest userRequest = new OapiV2UserListRequest();
                userRequest.setDeptId(dept.getDeptId());
                userRequest.setCursor(0L);
                userRequest.setSize(100L);

                OapiV2UserListResponse userResponse = userClient.execute(userRequest, token);

                JSONArray userJsonArray = new JSONArray();
                if (userResponse.isSuccess() && userResponse.getResult() != null && userResponse.getResult().getList() != null) {
                    // 修正了此处的类型名称
                    for (OapiV2UserListResponse.ListUserResponse user : userResponse.getResult().getList()) {
                        JSONObject userJson = new JSONObject();
                        userJson.put("userId", user.getUserid());
                        userJson.put("userName", user.getName());
                        userJsonArray.add(userJson);
                    }
                }
                deptJson.put("users", userJsonArray);
                resultJsonArray.add(deptJson);
            }

            return R.ok(resultJsonArray);

        } catch (ApiException e) {
            logger.error("调用钉钉旧版SDK时发生API异常", e);
            return R.fail("系统异常: " + e.getErrMsg());
        } catch (Exception e) {
            logger.error("获取钉钉部门及成员列表时发生未知错误", e);
            return R.fail("系统异常: " + e.getMessage());
        }
    }

}
