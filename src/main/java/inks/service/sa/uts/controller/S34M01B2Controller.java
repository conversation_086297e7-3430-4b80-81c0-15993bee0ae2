package inks.service.sa.uts.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkworkflow_1_0.models.QuerySchemaByProcessCodeResponseBody;
import com.aliyun.tea.TeaException;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiMessageCorpconversationAsyncsendV2Request;
import com.dingtalk.api.request.OapiMessageCorpconversationGetsendresultRequest;
import com.dingtalk.api.request.OapiV2DepartmentListsubRequest;
import com.dingtalk.api.request.OapiV2UserListRequest;
import com.dingtalk.api.response.OapiMessageCorpconversationAsyncsendV2Response;
import com.dingtalk.api.response.OapiMessageCorpconversationGetsendresultResponse;
import com.dingtalk.api.response.OapiV2DepartmentListsubResponse;
import com.dingtalk.api.response.OapiV2UserListResponse;
import com.taobao.api.ApiException;
import inks.common.core.domain.DingmsgPojo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaConfigService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.uts.constant.ConfigConstant;
import inks.service.sa.uts.domain.pojo.UtsDingmsgPojo;
import inks.service.sa.uts.service.UtsDingmsgService;
import inks.service.sa.uts.utils.PrintColor;
import inks.service.sa.uts.utils.VelocityUtils;
import inks.service.sa.uts.utils.ding.AccessTokenUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

import static inks.service.sa.uts.controller.S34M06B2Controller.createClient;

/**
 * 钉钉信息(Uts_DingMsg)表控制层
 *
 * <AUTHOR>
 * @since 2023-01-10 11:11:53
 */
@RestController
@RequestMapping("S34M01B2")
@Api(tags = "S34M01B2:钉钉信息")
public class S34M01B2Controller extends UtsDingmsgController {


    private final static Logger logger = LoggerFactory.getLogger(S34M01B2Controller.class);
    @Resource
    private UtsDingmsgService utsDingmsgService;

    @Resource
    private SaRedisService saRedisService;
    @Resource
    private SaConfigService saConfigService;

    public static void main(String[] args) throws ApiException {

        List<String> departmentLis = getDepartmentListIdTest();
        getUserListTest(departmentLis);

    }

    public static List<String> getDepartmentListIdTest() throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/department/listsub");
        OapiV2DepartmentListsubRequest req = new OapiV2DepartmentListsubRequest();
        req.setDeptId(1L);
        OapiV2DepartmentListsubResponse rsp = client.execute(req, "8927732c95633532833b6dbaf66226b7");
        JSONObject jsonObject = JSONArray.parseObject(rsp.getBody());
        // rsp.getBody()的值为：{"errcode":0,"errmsg":"ok","result":{"dept_id_list":[614051279,613394515,614110180]},"request_id":"15sbc62t8bjcy"}
        Object result = jsonObject.get("result");
        List<String> deptIdList = JSONArray.parseArray(result.toString(), String.class);
        deptIdList.forEach(System.out::println);
        for (String s : deptIdList) {
            String deptId = JSONArray.parseObject(s).getString("dept_id");
            System.out.println("deptId = " + deptId);
        }
        return deptIdList;
    }

    public static List<Map<String, Object>> getUserListTest(List<String> deptIdList) throws ApiException {
        for (String s : deptIdList) {
            //获取dept_id
            String deptIdString = JSONArray.parseObject(s).getString("dept_id");
            Long deptId = Long.valueOf(deptIdString);
            try {
                DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/list");
                OapiV2UserListRequest req = new OapiV2UserListRequest();
                req.setDeptId(deptId);
                req.setCursor(0L);
                req.setSize(10L);
                req.setOrderField("modify_desc");
                req.setContainAccessLimit(false);
                req.setLanguage("zh_CN");
                OapiV2UserListResponse rsp = client.execute(req, "8927732c95633532833b6dbaf66226b7");
                System.out.println(rsp.getBody());
            } catch (ApiException e) {
                e.printStackTrace();
            }
            System.out.println("deptId = " + deptId);
        }
        //根部门下员工信息
        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/list");
            OapiV2UserListRequest req = new OapiV2UserListRequest();
            req.setDeptId(1L);
            req.setCursor(0L);
            req.setSize(10L);
            req.setOrderField("modify_desc");
            req.setContainAccessLimit(false);
            req.setLanguage("zh_CN");
            OapiV2UserListResponse rsp = client.execute(req, "8927732c95633532833b6dbaf66226b7");
            System.out.println(rsp.getBody());
        } catch (ApiException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取钉钉信息详细信息", notes = "获取钉钉信息详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntityByMsgCode", method = RequestMethod.GET)
    public R<UtsDingmsgPojo> getBillEntityByMsgCode(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.utsDingmsgService.getBillEntityByMsgCode(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 钉钉第三方应用发送信息", notes = "钉钉第三方应用发送信息", produces = "application/json")
    @RequestMapping(value = "/sendCardMsg", method = RequestMethod.GET)
    public R<String> sendCardMsg(String uuid, String msg, String tid) {

        logger.info("开始免登 uuid：" + uuid);
        logger.info("开始免登 tid：" + tid);
        Long agentId = 0L;

        R r = new R();
        logger.info("------开始钉钉免登-------");
        //获取企业ID
        AccessTokenUtil.AppKey = saConfigService.getConfigValue("S34M06.appkey", null);

        logger.info("钉钉免登 AppKey:" + AccessTokenUtil.AppKey);

        //获取应用密钥
        AccessTokenUtil.AppSecret = saConfigService.getConfigValue("S34M06.agentsecret", null);
        logger.info("钉钉免登 AppSecret:" + AccessTokenUtil.AppSecret);

        //获取应用密钥
        agentId = Long.parseLong(saConfigService.getConfigValue("S34M06.agentid", null));
        logger.info("钉钉免登 agentId:" + agentId);


        // 获取access_token，注意正式代码要有异常流处理
        String access_token = AccessTokenUtil.getToken();
        logger.info("钉钉免登 Token:" + access_token);

        try {

            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");
            OapiMessageCorpconversationAsyncsendV2Request req = new OapiMessageCorpconversationAsyncsendV2Request();
            req.setAgentId(agentId);
            req.setUseridList("manager3081");

            OapiMessageCorpconversationAsyncsendV2Request.Msg obj1 = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
            obj1.setMsgtype("action_card");

            OapiMessageCorpconversationAsyncsendV2Request.ActionCard obj5 = new OapiMessageCorpconversationAsyncsendV2Request.ActionCard();
            obj5.setSingleUrl("https://open.dingtalk.com");
            obj5.setSingleTitle("查看详情");
            obj5.setMarkdown("支持markdown格式的正文内容");
            obj5.setTitle("是透出到会话列表和通知的文案");
            obj1.setActionCard(obj5);

            req.setMsg(obj1);
            OapiMessageCorpconversationAsyncsendV2Response rsp = client.execute(req, access_token);
            logger.info(rsp.getBody());
            return R.ok(rsp.getBody());
        } catch (ApiException e) {
            e.printStackTrace();
            return R.fail(e.getErrMsg());
        }
    }

    @ApiOperation(value = " 企业微信第三方应用发送信息", notes = "企业微信第三方应用发送信息", produces = "application/json")
    @RequestMapping(value = "/sendOaMsg", method = RequestMethod.POST)
    public R<String> sendOaMsg(@RequestBody String json) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

            String tid = loginUser.getTenantid();
            DingmsgPojo dingmsgPojo = JSONArray.parseObject(json, DingmsgPojo.class);
            UtsDingmsgPojo utsDingmsgPojo = this.utsDingmsgService.getBillEntityByMsgCode(dingmsgPojo.getMsgcode(), loginUser.getTenantid());


            String UserList = utsDingmsgPojo.getUserlist();

            logger.info("开始免登 uuid：" + UserList);
            logger.info("开始免登 tid：" + loginUser.getTenantid());
            Long agentId = 0L;

            R r = new R();
            logger.info("------开始钉钉免登-------");
            //获取企业ID
            AccessTokenUtil.AppKey = saConfigService.getConfigValue("S34M06.appkey", null);
            if (r.getCode() == 200) {
                AccessTokenUtil.AppKey = r.getData().toString();
                logger.info("钉钉免登 AppKey:" + AccessTokenUtil.AppKey);
            } else {
                logger.info("获取应用AppKey失败" + r);
            }
            //获取应用密钥
            AccessTokenUtil.AppSecret = saConfigService.getConfigValue("S34M06.agentsecret", null);
            logger.info("钉钉免登 AppSecret:" + AccessTokenUtil.AppSecret);

            //获取应用密钥
            agentId = Long.parseLong(saConfigService.getConfigValue("S34M06.agentid", null));
            logger.info("钉钉免登 agentId:" + agentId);


            // 获取access_token，注意正式代码要有异常流处理
            String access_token = AccessTokenUtil.getToken();
            logger.info("钉钉免登 Token:" + access_token);


            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");
            OapiMessageCorpconversationAsyncsendV2Request req = new OapiMessageCorpconversationAsyncsendV2Request();
            req.setAgentId(agentId);
            req.setUseridList(UserList);
            OapiMessageCorpconversationAsyncsendV2Request.Msg obj1 = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
            obj1.setMsgtype("oa");
            OapiMessageCorpconversationAsyncsendV2Request.Link obj2 = new OapiMessageCorpconversationAsyncsendV2Request.Link();
            obj2.setTitle("OA订单");
            obj1.setLink(obj2);
            OapiMessageCorpconversationAsyncsendV2Request.OA obj3 = new OapiMessageCorpconversationAsyncsendV2Request.OA();
            OapiMessageCorpconversationAsyncsendV2Request.Body obj4 = new OapiMessageCorpconversationAsyncsendV2Request.Body();
            obj4.setAuthor("Eric");
            List<OapiMessageCorpconversationAsyncsendV2Request.Form> list6 = new ArrayList<OapiMessageCorpconversationAsyncsendV2Request.Form>();
            OapiMessageCorpconversationAsyncsendV2Request.Form obj7 = new OapiMessageCorpconversationAsyncsendV2Request.Form();
            list6.add(obj7);
            obj7.setValue("PO1225458");
            obj7.setKey("订单");
            obj4.setForm(list6);
            obj4.setTitle("OA订单");
            obj3.setBody(obj4);
            OapiMessageCorpconversationAsyncsendV2Request.Head obj8 = new OapiMessageCorpconversationAsyncsendV2Request.Head();
            obj8.setBgcolor("FFBBBBBB");
            obj8.setText("头部");
            obj3.setHead(obj8);
            obj1.setOa(obj3);
            OapiMessageCorpconversationAsyncsendV2Request.Markdown obj9 = new OapiMessageCorpconversationAsyncsendV2Request.Markdown();
            obj9.setTitle("OA订单");
            obj1.setMarkdown(obj9);
            OapiMessageCorpconversationAsyncsendV2Request.ActionCard obj10 = new OapiMessageCorpconversationAsyncsendV2Request.ActionCard();
            List<OapiMessageCorpconversationAsyncsendV2Request.BtnJsonList> list12 = new ArrayList<OapiMessageCorpconversationAsyncsendV2Request.BtnJsonList>();
            OapiMessageCorpconversationAsyncsendV2Request.BtnJsonList obj13 = new OapiMessageCorpconversationAsyncsendV2Request.BtnJsonList();
            list12.add(obj13);
            obj13.setTitle("OA订单");
            obj10.setBtnJsonList(list12);
            obj10.setTitle("OA订单");
            obj1.setActionCard(obj10);
            req.setMsg(obj1);
            OapiMessageCorpconversationAsyncsendV2Response rsp = client.execute(req, access_token);
            logger.info(rsp.getBody());
            return R.ok(rsp.getBody());
        } catch (ApiException e) {
            e.printStackTrace();
            return R.fail(e.getErrMsg());
        }
    }

    @ApiOperation(value = " 钉钉第三方应用发送信息 通过MsgCode", notes = "钉钉第三方应用发送信息", produces = "application/json")
    @RequestMapping(value = "/sendTextMsg", method = RequestMethod.POST)
    public R<String> sendTextMsg(@RequestBody String json, @RequestParam(required = false) String tid) {
        try {
            if (StringUtils.isBlank(tid)) {
                LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
                tid = loginUser.getTenantid();
            }
            DingmsgPojo dingmsgPojoReq = JSONArray.parseObject(json, DingmsgPojo.class);
            String msgCode = dingmsgPojoReq.getMsgcode();
            String msgText = dingmsgPojoReq.getMsgtext();//最终发送信息内容

            UtsDingmsgPojo utsDingmsgDB = this.utsDingmsgService.getBillEntityByMsgCode(msgCode, tid);
            if (utsDingmsgDB == null) {
                PrintColor.lv("ding：未找到对应的消息模板，跳出");
                return R.fail(msgCode + "信息模板未找到");
            }


            // 开始构建发送人列表: 使用Set来保证唯一性，避免重复
            Set<String> userListSet = new HashSet<>();
            // 1.先添加数据库中的用户列表
            String userListDb = utsDingmsgDB.getUserlist();
            if (StringUtils.isNotBlank(userListDb)) {
                Collections.addAll(userListSet, userListDb.split(","));
            }
            // 2.(代理中心特有)追加前端传入的用户列表
            String userListReq = dingmsgPojoReq.getUserlist();
            if (StringUtils.isNotBlank(userListReq)) {
                Collections.addAll(userListSet, userListReq.split(","));
            }
            PrintColor.zi("发送人列表1,2：本次前端传入的userListReq:" + userListReq + ", 消息模板数据库已有的.UserList:" + userListDb);

            // 3.发送人列表3处理ObjJson数组 转换钉钉userid
            String objJsonStr = utsDingmsgDB.getObjjson();
            if (StringUtils.isNotBlank(objJsonStr)) {
                JSONArray objJsonArray = JSONArray.parseArray(objJsonStr);
                for (int i = 0; i < objJsonArray.size(); i++) {
                    JSONObject obj = objJsonArray.getJSONObject(i);
                    String oms_userid = obj.getString("objcode");

                    if (oms_userid.trim().startsWith("$")) { // 如果是VM模板，进行渲染
                        oms_userid = VelocityUtils.renderTemplateToData(oms_userid, msgText);
                    }

                    // 根据oms_userid查询对应的钉钉用户ID
                    String ding_userid = utsDingmsgService.getDingUserIdByOmsUserid(oms_userid, tid);

                    // 如果查到了钉钉用户ID，添加到发送人列表
                    if (StringUtils.isNotBlank(ding_userid)) {
                        userListSet.add(ding_userid); // 添加到Set中，避免重复
                    }
                }
            }
            PrintColor.zi("发送人列表3：消息模板数据库.ObjJson:" + objJsonStr);
            // 将Set转换为String，逗号分隔
            String finalUserList = String.join(",", userListSet);
            // 打印最终的发送人列表
            PrintColor.zi("替换后追加到发送人列表1,2之后： 最终发送人列表：" + finalUserList);


            // 是否转换信息Vm模版
            String msgVM = utsDingmsgDB.getMsgtemplate();
            if (StringUtils.isNotBlank(msgVM)) {
                // 根据模版和内容 返回填充后数据
                msgText = VelocityUtils.renderTemplateToData(msgVM, msgText);
                PrintColor.zi("Vm模版替换后的消息内容：" + msgText);
            }


            logger.info("开始免登 uuid：" + finalUserList);
            logger.info("开始免登 tid：" + tid);
            Long agentId = 0L;

            logger.info("------开始钉钉发信息,获取参数-------");
            Map<String, String> mapcfg = saConfigService.getConfigAll();
            agentId = Long.parseLong(mapcfg.get(ConfigConstant.DING_AGENTID));
            logger.info("钉钉免登 agentId:" + agentId);
            AccessTokenUtil.AppKey = mapcfg.get(ConfigConstant.DING_APPKEY);
            logger.info("钉钉免登 AppKey:" + AccessTokenUtil.AppKey);
            AccessTokenUtil.AppSecret = mapcfg.get(ConfigConstant.DING_APPSECRET);
            logger.info("钉钉免登 AppSecret:" + AccessTokenUtil.AppSecret);

            // 获取access_token，注意正式代码要有异常流处理
            String access_token = AccessTokenUtil.getToken();
            logger.info("钉钉免登 Token:" + access_token);

            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");
            OapiMessageCorpconversationAsyncsendV2Request req = new OapiMessageCorpconversationAsyncsendV2Request();
            req.setAgentId(agentId);
            req.setUseridList(finalUserList);
            OapiMessageCorpconversationAsyncsendV2Request.Msg obj1 = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
            obj1.setMsgtype("text");
            OapiMessageCorpconversationAsyncsendV2Request.Text obj2 = new OapiMessageCorpconversationAsyncsendV2Request.Text();
            obj2.setContent(msgText);
            obj1.setText(obj2);
            req.setMsg(obj1);
            OapiMessageCorpconversationAsyncsendV2Response rsp = client.execute(req, access_token);
            logger.info(rsp.getBody());
            return R.ok(rsp.getBody());
        } catch (ApiException e) {
            e.printStackTrace();
            return R.fail(e.getErrMsg());
        }
    }

    @ApiOperation(value = " 企业微信第三方应用发送信息", notes = "企业微信第三方应用发送信息", produces = "application/json")
    @RequestMapping(value = "/chkMsgState", method = RequestMethod.GET)
    public R<String> chkMsgState(Long taskid, String tid) {

        logger.info("开始免登 taskid：" + taskid);
        logger.info("开始免登 tid：" + tid);
        Long agentId = 0L;

        R r = new R();
        logger.info("------开始钉钉免登-------");
        //获取企业ID
        AccessTokenUtil.AppKey = saConfigService.getConfigValue("S34M06.appkey", null);
        logger.info("钉钉免登 AppKey:" + AccessTokenUtil.AppKey);
        //获取应用密钥
        AccessTokenUtil.AppSecret = saConfigService.getConfigValue("S34M06.agentsecret", null);
        logger.info("钉钉免登 AppSecret:" + AccessTokenUtil.AppSecret);

        //获取应用密钥
        agentId = Long.parseLong(saConfigService.getConfigValue("S34M06.agentid", null));
        logger.info("钉钉免登 agentId:" + agentId);


        // 获取access_token，注意正式代码要有异常流处理
        String access_token = AccessTokenUtil.getToken();
        logger.info("钉钉免登 Token:" + access_token);

        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/getsendresult");
            OapiMessageCorpconversationGetsendresultRequest req = new OapiMessageCorpconversationGetsendresultRequest();
            req.setAgentId(agentId);
            req.setTaskId(taskid);
            OapiMessageCorpconversationGetsendresultResponse rsp = client.execute(req, access_token);
            logger.info(rsp.getBody());
            return R.ok(rsp.getBody());
        } catch (ApiException e) {
            e.printStackTrace();
            return R.fail(e.getErrMsg());
        }
    }

    @ApiOperation(value = "钉钉第三方应用发送信息测试", notes = "钉钉第三方应用发送信息测试", produces = "application/json")
    @RequestMapping(value = "/sendMsgTest", method = RequestMethod.POST)
    public R<String> sendMsgTest(@RequestBody String json) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String tid = loginUser.getTenantid();
            DingmsgPojo dingmsgPojo = JSONArray.parseObject(json, DingmsgPojo.class);
            UtsDingmsgPojo utsDingmsgPojo = this.utsDingmsgService.getBillEntityByMsgCode(dingmsgPojo.getMsgcode(), loginUser.getTenantid());
            if (utsDingmsgPojo == null) {
                return R.fail(dingmsgPojo.getMsgcode() + "信息模板未找到");
            }
            String UserList = utsDingmsgPojo.getUserlist();
            if (dingmsgPojo.getUserlist() != null && dingmsgPojo.getUserlist().length() > 0) {
                UserList = "," + dingmsgPojo.getUserlist();
            }
            logger.info("开始免登 uuid：" + UserList);
            logger.info("开始免登 tid：" + loginUser.getTenantid());
            Long agentId = 0L;

            logger.info("------开始钉钉发信息,获取参数-------");
            Map<String, String> mapcfg = saConfigService.getConfigAll();
            agentId = Long.parseLong(mapcfg.get(ConfigConstant.DING_AGENTID));
            logger.info("钉钉免登 agentId:" + agentId);
            AccessTokenUtil.AppKey = mapcfg.get(ConfigConstant.DING_APPKEY);
            logger.info("钉钉免登 AppKey:" + AccessTokenUtil.AppKey);
            AccessTokenUtil.AppSecret = mapcfg.get(ConfigConstant.DING_APPSECRET);
            logger.info("钉钉免登 AppSecret:" + AccessTokenUtil.AppSecret);
            // 获取access_token，注意正式代码要有异常流处理
            String access_token = AccessTokenUtil.getToken();
            logger.info("钉钉免登 Token:" + access_token);
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");
            OapiMessageCorpconversationAsyncsendV2Request req = new OapiMessageCorpconversationAsyncsendV2Request();
            req.setAgentId(agentId);
            req.setUseridList(UserList);
            OapiMessageCorpconversationAsyncsendV2Request.Msg obj1 = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
            obj1.setMsgtype("text");
            OapiMessageCorpconversationAsyncsendV2Request.Text obj2 = new OapiMessageCorpconversationAsyncsendV2Request.Text();
            obj2.setContent(dingmsgPojo.getMsgtext());
            obj1.setText(obj2);
            req.setMsg(obj1);
            OapiMessageCorpconversationAsyncsendV2Response rsp = client.execute(req, access_token);
            logger.info(rsp.getBody());
            return R.ok(rsp.getBody());
        } catch (ApiException e) {
            e.printStackTrace();
            return R.fail(e.getErrMsg());
        }
    }

    @ApiOperation(value = "获取部门ID列表和user列表钉钉第三方应用", notes = "钉钉第三方应用获取子部门ID列表", produces = "application/json")
    @RequestMapping(value = "/getDepartmentListId", method = RequestMethod.GET)
    public List<Map> getDepartmentListId() throws ApiException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        String tid = loginUser.getTenantid();
        logger.info("------开始钉钉发信息,获取参数-------");
        Map<String, String> mapcfg = saConfigService.getConfigAll();
        Long agentId = 0L;
        agentId = Long.parseLong(mapcfg.get(ConfigConstant.DING_AGENTID));
//            agentId = Long.parseLong("1494595840");
        logger.info("钉钉免登 agentId:" + agentId);
        AccessTokenUtil.AppKey = mapcfg.get(ConfigConstant.DING_APPKEY);
//            AccessTokenUtil.AppKey = "dingrr8hb7enjeqrxvye";
        logger.info("钉钉免登 AppKey:" + AccessTokenUtil.AppKey);
        AccessTokenUtil.AppSecret = mapcfg.get(ConfigConstant.DING_APPSECRET);
//            AccessTokenUtil.AppSecret = "x767FiHpDPQUKOKiSv_ZqRer2nGxDLSYXBLaSP1A6FzU_c0-8WTXoRNPsoXxX0qv";
        logger.info("钉钉免登 AppSecret:" + AccessTokenUtil.AppSecret);
        String access_token = AccessTokenUtil.getToken();
        logger.info("钉钉免登 Token:" + access_token);
        //获取部门信息列表
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/department/listsub");
        OapiV2DepartmentListsubRequest req = new OapiV2DepartmentListsubRequest();
        req.setDeptId(1L);
        OapiV2DepartmentListsubResponse rsp = client.execute(req, access_token);
        JSONObject jsonObject = JSONArray.parseObject(rsp.getBody());
        Object result = jsonObject.get("result");
        List<Map> deptAndUser = JSONArray.parseArray(result.toString(), Map.class);
        //添加根部门id=1
        Map<String, Object> rootDept = new HashMap<>();
        rootDept.put("dept_id", 1);
        rootDept.put("name", "根部门");
        deptAndUser.add(rootDept);
        List<Map<String, Object>> mapList = new ArrayList<>();
        for (Map<String, Object> dept : deptAndUser) {
            //获取dept_id
            Object deptIdString = dept.get("dept_id");
            Long deptId = Long.valueOf(deptIdString.toString());
            try {
                //获取用户详情(包括userid,name...)
                DingTalkClient client2 = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/list");
                OapiV2UserListRequest req2 = new OapiV2UserListRequest();
                req2.setDeptId(deptId);
                req2.setCursor(0L);
                req2.setSize(10L);
                req2.setOrderField("modify_desc");
                req2.setContainAccessLimit(false);
                req2.setLanguage("zh_CN");
                OapiV2UserListResponse rsp2 = client2.execute(req2, access_token);
                JSONObject jsonObject2 = JSONArray.parseObject(rsp2.getBody());
                Object result2 = jsonObject2.get("result");
                result2 = JSONArray.parseObject(result2.toString()).get("list");
                List<String> userIdList = JSONArray.parseArray(result2.toString(), String.class);
                dept.put("userIdList", userIdList);

            } catch (ApiException e) {
                e.printStackTrace();
            }
        }
        logger.info("部门下所有user:" + deptAndUser);
        return deptAndUser;
    }


    @ApiOperation(value = "根据processCode获取表单schema信息", notes = "通过processCode获取钉钉工作流的表单schema信息", produces = "application/json")
    @RequestMapping(value = "/getFormSchemaByProcessCode", method = RequestMethod.GET)
    public R<Map<String, Object>> getFormSchemaByProcessCode(@RequestParam String processCode) throws ApiException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        String tid = loginUser.getTenantid();
        logger.info("------开始钉钉获取表单schema信息,获取参数-------");

        // 获取配置
        Map<String, String> mapcfg = saConfigService.getConfigAll();
        Long agentId = 0L;
        agentId = Long.parseLong(mapcfg.get(ConfigConstant.DING_AGENTID));
        logger.info("钉钉免登 agentId:" + agentId);
        AccessTokenUtil.AppKey = mapcfg.get(ConfigConstant.DING_APPKEY);
        logger.info("钉钉免登 AppKey:" + AccessTokenUtil.AppKey);
        AccessTokenUtil.AppSecret = mapcfg.get(ConfigConstant.DING_APPSECRET);
        logger.info("钉钉免登 AppSecret:" + AccessTokenUtil.AppSecret);

        // 获取access_token
        String access_token = AccessTokenUtil.getToken();
        logger.info("钉钉免登 Token:" + access_token);

        // 创建钉钉工作流客户端
        com.aliyun.dingtalkworkflow_1_0.Client client = null;
        try {
            client = createClient();
        } catch (Exception e) {
            logger.error("创建钉钉工作流客户端失败", e);
            throw new ApiException("创建钉钉工作流客户端失败");
        }

        // 创建请求头和请求体
        com.aliyun.dingtalkworkflow_1_0.models.QuerySchemaByProcessCodeHeaders querySchemaByProcessCodeHeaders = new com.aliyun.dingtalkworkflow_1_0.models.QuerySchemaByProcessCodeHeaders();
        querySchemaByProcessCodeHeaders.xAcsDingtalkAccessToken = access_token;
        com.aliyun.dingtalkworkflow_1_0.models.QuerySchemaByProcessCodeRequest querySchemaByProcessCodeRequest = new com.aliyun.dingtalkworkflow_1_0.models.QuerySchemaByProcessCodeRequest();
        querySchemaByProcessCodeRequest.processCode = processCode;

        // 调用API获取表单schema
        try {
            com.aliyun.dingtalkworkflow_1_0.models.QuerySchemaByProcessCodeResponse response = client.querySchemaByProcessCodeWithOptions(
                    querySchemaByProcessCodeRequest, querySchemaByProcessCodeHeaders, new com.aliyun.teautil.models.RuntimeOptions());
            // 处理返回数据
            QuerySchemaByProcessCodeResponseBody body = response.body;
            QuerySchemaByProcessCodeResponseBody.QuerySchemaByProcessCodeResponseBodyResult result1 = body.getResult();

            // 将 result1 对象转为 JSON 字符串
            String resultJsonString = JSONObject.toJSONString(result1);
            // 然后解析 JSON 字符串
            JSONObject resultJson = JSONObject.parseObject(resultJsonString);
            // 返回或进一步处理
            return R.ok(resultJson);
        } catch (TeaException err) {
            logger.error("钉钉工作流API调用失败", err);
            throw new ApiException("钉钉工作流API调用失败: " + err.getMessage());
        } catch (Exception e) {
            logger.error("钉钉工作流API调用异常", e);
            throw new ApiException("钉钉工作流API调用异常");
        }
    }


}
