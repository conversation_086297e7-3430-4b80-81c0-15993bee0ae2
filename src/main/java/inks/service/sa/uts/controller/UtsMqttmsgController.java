package inks.service.sa.uts.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.uts.domain.pojo.UtsMqttmsgPojo;
import inks.service.sa.uts.service.UtsMqttmsgService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * MQTT信息(Uts_MqttMsg)表控制层
 *
 * <AUTHOR>
 * @since 2023-07-25 16:07:02
 */
public class UtsMqttmsgController {

    private final static Logger logger = LoggerFactory.getLogger(UtsMqttmsgController.class);

    @Resource
    private UtsMqttmsgService utsMqttmsgService;

    @Resource
    private SaRedisService saRedisService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取MQTT信息详细信息", notes = "获取MQTT信息详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Uts_MqttMsg.List")
    public R<UtsMqttmsgPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.utsMqttmsgService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_MqttMsg.List")
    public R<PageInfo<UtsMqttmsgPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Uts_MqttMsg.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.utsMqttmsgService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增MQTT信息", notes = "新增MQTT信息", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_MqttMsg.Add")
    public R<UtsMqttmsgPojo> create(@RequestBody String json) {
        try {
            UtsMqttmsgPojo utsMqttmsgPojo = JSONArray.parseObject(json, UtsMqttmsgPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            utsMqttmsgPojo.setCreateby(loginUser.getRealName());   // 创建者
            utsMqttmsgPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            utsMqttmsgPojo.setCreatedate(new Date());   // 创建时间
            utsMqttmsgPojo.setLister(loginUser.getRealname());   // 制表
            utsMqttmsgPojo.setListerid(loginUser.getUserid());    // 制表id  
            utsMqttmsgPojo.setModifydate(new Date());   //修改时间
            utsMqttmsgPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.utsMqttmsgService.insert(utsMqttmsgPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改MQTT信息", notes = "修改MQTT信息", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_MqttMsg.Edit")
    public R<UtsMqttmsgPojo> update(@RequestBody String json) {
        try {
            UtsMqttmsgPojo utsMqttmsgPojo = JSONArray.parseObject(json, UtsMqttmsgPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            utsMqttmsgPojo.setLister(loginUser.getRealname());   // 制表
            utsMqttmsgPojo.setListerid(loginUser.getUserid());    // 制表id  
            utsMqttmsgPojo.setTenantid(loginUser.getTenantid());   //租户id
            utsMqttmsgPojo.setModifydate(new Date());   //修改时间
//            utsMqttmsgPojo.setAssessor(""); // 审核员
//            utsMqttmsgPojo.setAssessorid(""); // 审核员id
//            utsMqttmsgPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.utsMqttmsgService.update(utsMqttmsgPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除MQTT信息", notes = "删除MQTT信息", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Uts_MqttMsg.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.utsMqttmsgService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Uts_MqttMsg.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        UtsMqttmsgPojo utsMqttmsgPojo = this.utsMqttmsgService.getEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(utsMqttmsgPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    /**
     * 按模块编码查询报表
     *
     * @param code 主键
     * @return 查询结果
     */
    @ApiOperation(value = "按模块编码查询报表", notes = "按模块编码查询报表", produces = "application/json")
    @RequestMapping(value = "/getListByModuleCode", method = RequestMethod.GET)
    public R<List<UtsMqttmsgPojo>> getListByModuleCode(String code) {
        try {
            String tid = saRedisService.getLoginUser().getTenantid();
            List<UtsMqttmsgPojo> list = this.utsMqttmsgService.getListByModuleCode(code, tid);
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}

