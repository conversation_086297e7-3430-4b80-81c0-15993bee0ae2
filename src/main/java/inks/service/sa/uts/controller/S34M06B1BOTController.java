package inks.service.sa.uts.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import inks.common.core.domain.DingmsgPojo;
import inks.common.core.domain.R;
import inks.service.sa.uts.domain.pojo.UtsWxehookmsgPojo;
import inks.service.sa.uts.domain.vo.WxeRobotMessages;
import inks.service.sa.uts.service.UtsWxehookmsgService;
import inks.service.sa.uts.service.UtsWxemsgService;
import inks.service.sa.uts.utils.PrintColor;
import inks.service.sa.uts.utils.VelocityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpGroupRobotService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;


/**
 * 企业微信群机器人信息(Uts_WxeHookMsg)表控制层
 * 官方文档： https://developer.work.weixin.qq.com/document/path/91770#%E6%96%87%E6%9C%AC%E7%B1%BB%E5%9E%8B
 * 接口参考： WxJava项目的企业微信CP部分
 *
 * <AUTHOR>
 * @since 2025-02-27 15:33:56
 */
@RestController
@RequestMapping("S34M06B1BOT")
@Api(tags = "S34M06B1BOT:企业微信群机器人信息")
// 拷贝微服务utils/D96M14B3
public class S34M06B1BOTController extends UtsWxehookmsgController {
    @Resource
    private WxCpGroupRobotService wxCpGroupRobotService;

    @Resource
    private UtsWxehookmsgService utsWxehookmsgService;
    @Resource
    private UtsWxemsgService utsWxemsgService;
    @Value("${inks.wx.cp.webhookUrl:https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b1271f39-fa4d-487a-9324-1fb42f850e6f}")
    private String webhookUrl;

    /**
     * 发送text类型的消息
     * content       文本内容，最长不超过2048个字节，必须是utf8编码
     * mentionedlist userId的列表，提醒群中的指定成员(@某个成员)，@all表示提醒所有人，如果开发者获取不到userId，可以使用mentioned_mobile_list
     * mobilelist    手机号列表，提醒手机号对应的群成员(@某个成员)，@all表示提醒所有人
     *
     * @throws WxErrorException 异常
     */
    @ApiOperation(value = "发送文本消息", notes = "webhookurl: 选填群机器人webhookurl，不填则使用yml中的" +
            "<br>content：文本内容，最长不超过2048个字节，必须是utf8编码" +
            "<br>mentionedlist userId的列表，提醒群中的指定成员(@某个成员)，@all表示提醒所有人，如果开发者获取不到userId，可以使用mentioned_mobile_list" +
            "<br>mobilelist 手机号列表，提醒手机号对应的群成员(@某个成员)，@all表示提醒所有人")
    @PostMapping("/sendText")
    public R<Void> sendText(@RequestBody WxeRobotMessages.TextMessageRequest request) {
        try {
            if (StringUtils.isNotBlank(request.getWebhookurl())) {
                webhookUrl = request.getWebhookurl();
            }
            wxCpGroupRobotService.sendText(webhookUrl, request.getContent(), request.getMentionedlist(), request.getMobilelist());
            return R.ok();
        } catch (WxErrorException e) {
            return R.fail(e.getMessage());
        }
    }

    public R<String> sendTextMsg(@RequestBody String json, @RequestParam(required = false) String tid) {
        //if (StringUtils.isBlank(tid)) {
        //    LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //    tid = loginUser.getTenantid();
        //}
        // 获得用户数据 DingmsgPojo是工具类，接参通用于钉钉和企业微信
        DingmsgPojo wxemsgPojoReq = JSONArray.parseObject(json, DingmsgPojo.class);
        String msgCode = wxemsgPojoReq.getMsgcode();
        String msgText = wxemsgPojoReq.getMsgtext();
        UtsWxehookmsgPojo wxehookmsgDB = this.utsWxehookmsgService.getEntityByMsgCode(msgCode, tid);
        if (wxehookmsgDB == null) {
            PrintColor.lv("wxebot：未找到对应的消息模板，跳出");
            return R.fail(msgCode + "信息模板未找到");
        }

        // 开始构建发送人列表: 使用Set来保证唯一性，避免重复
        Set<String> userListSet = new HashSet<>();
        // 1.先添加数据库中的用户列表
        String userListDb = wxehookmsgDB.getUserlist();
        if (StringUtils.isNotBlank(userListDb)) {
            Collections.addAll(userListSet, userListDb.split(","));
        }
        // 2.(代理中心特有)追加前端传入的用户列表
        String userListReq = wxemsgPojoReq.getUserlist();
        if (StringUtils.isNotBlank(userListReq)) {
            Collections.addAll(userListSet, userListReq.split(","));
        }
        PrintColor.zi("发送人列表1,2：本次前端传入的userListReq:" + userListReq + ", 消息模板数据库已有的.UserList:" + userListDb);

        // 3. 处理 ObjJson 数组，加入微信用户ID
        String objJsonStr = wxehookmsgDB.getObjjson();
        if (StringUtils.isNotBlank(objJsonStr)) {
            JSONArray objJsonArray = JSONArray.parseArray(objJsonStr);
            for (int i = 0; i < objJsonArray.size(); i++) {
                JSONObject obj = objJsonArray.getJSONObject(i);
                String oms_userid = obj.getString("objcode");

                // 如果是VM模板，进行渲染
                if (oms_userid.trim().startsWith("$")) {
                    oms_userid = VelocityUtils.renderTemplateToData(oms_userid, msgText);
                }

                // 根据 oms_userid 查询对应的微信用户ID
                String wx_userid = utsWxemsgService.getWxeUserIdByOmsUserid(oms_userid, tid);

                // 如果查到了微信用户ID，添加到发送人列表
                if (StringUtils.isNotBlank(wx_userid)) {
                    userListSet.add(wx_userid); // 添加到Set中，避免重复
                }
            }
        }
        PrintColor.zi("发送人列表2：消息模板数据库.ObjJson:" + objJsonStr);
        // 将Set转换为String，逗号分隔
        String finalUserListString = String.join(",", userListSet);
        // 打印最终的发送人列表
        PrintColor.zi("替换后追加到发送人列表1,2之后： 最终发送人列表：" + finalUserListString);

        // 4.是否转换信息Vm模版
        String msgVM = wxehookmsgDB.getMsgtemplate();
        if (StringUtils.isNotBlank(msgVM)) {
            // 根据模版和内容 返回填充后数据
            msgText = VelocityUtils.renderTemplateToData(msgVM, msgText);
            PrintColor.zi("Vm模版替换后的消息内容：" + msgText);
        }

        // 5.要发送的所有群机器人webhookurl、@的所有人员
        String webhookListString = wxehookmsgDB.getWebhooklist().trim();
        List<String> userList = Arrays.asList(finalUserListString.split(","));
        String[] webhookList = webhookListString.split(",");
        // 企业微信群机器人webhook格式：https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=fdef02ed-52e9-48ec-b2f4-7deb58c3e470,https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx
        // 直接传入整个webhook url，不需要拆分
        for (String webhook : webhookList) {
            try {
                // 如果前端传入的userListReq为"all"或者数据库中的userListDb为"all" 则发送给所有人 构建userList格式：[ "@all" ]
                if ("all".equals(userListReq) || "all".equals(userListDb)) {
                    userList = Collections.singletonList("@all");
                }
                wxCpGroupRobotService.sendText(webhook, msgText, userList, null);
            } catch (WxErrorException e) {
                return R.fail(e.getMessage());
            }
        }
        PrintColor.zi("本次共发送了" + webhookList.length + "个群机器人，消息内容：" + msgText);
        return R.ok("本次共发送了" + webhookList.length + "个群机器人，消息内容：" + msgText);
    }

    /**
     * 发送markdown类型的消息
     * content markdown内容，最长不超过4096个字节，必须是utf8编码
     *
     * @throws WxErrorException 异常
     */
    @ApiOperation(value = "发送markdown消息", notes = "webhookurl: 选填群机器人webhookurl，不填则使用yml中的" +
            "<br>content：markdown内容，最长不超过4096个字节，必须是utf8编码")
    @PostMapping("/sendMarkdown")
    public R<Void> sendMarkdown(@RequestBody WxeRobotMessages.MarkdownMessageRequest request) {
        try {
            if (StringUtils.isNotBlank(request.getWebhookurl())) {
                webhookUrl = request.getWebhookurl();
            }
            wxCpGroupRobotService.sendMarkdown(webhookUrl, request.getContent());
            return R.ok();
        } catch (WxErrorException e) {
            return R.fail(e.getMessage());
        }
    }


    //@ApiOperation(value = "发送Markdown消息", notes = "webhookurl列表：从数据库获取（通过msgCode），可配置多个用逗号分隔<br>" +
    //        "msgCode：消息模板编码，用于关联企业微信机器人webhookurl列表和消息模板<br>" +
    //        "msgtext：消息内容，支持Velocity模板语法<br>" +
    //        "userlist：需要@提醒的用户ID列表（可选，逗号分隔），会叠加到数据库配置的userlist上<br>" +
    //        "objjson：对象列表，用于关联oms用户到微信用户（见字段说明）")
    //@PostMapping("/sendMarkdownMsg")

    //Markdown消息 代码中没有参数去支持@人
    //支持@功能但需手动拼接语法：无法直接通过API参数@成员，需在【Markdown内容中】手动输入<@userid>或@手机号实现
    public R<String> sendMarkdownMsg(@RequestBody String json, @RequestParam(required = false) String tid) {
        //if (StringUtils.isBlank(tid)) {
        //    LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        //    tid = loginUser.getTenantid();
        //}
        // 获得用户数据 DingmsgPojo是工具类，接参通用于钉钉和企业微信
        DingmsgPojo wxemsgPojoReq = JSONArray.parseObject(json, DingmsgPojo.class);
        String msgCode = wxemsgPojoReq.getMsgcode();
        String msgText = wxemsgPojoReq.getMsgtext();
        UtsWxehookmsgPojo wxehookmsgDB = this.utsWxehookmsgService.getEntityByMsgCode(msgCode, tid);
        if (wxehookmsgDB == null) {
            PrintColor.lv("wxebot：未找到对应的消息模板，跳出");
            return R.fail(msgCode + "信息模板未找到");
        }

        // 开始构建发送人列表: 使用Set来保证唯一性，避免重复
        Set<String> userListSet = new HashSet<>();
        // 1.先添加数据库中的用户列表
        String userListDb = wxehookmsgDB.getUserlist();
        if (StringUtils.isNotBlank(userListDb)) {
            Collections.addAll(userListSet, userListDb.split(","));
        }
        // 2.(代理中心特有)追加前端传入的用户列表
        String userListReq = wxemsgPojoReq.getUserlist();
        if (StringUtils.isNotBlank(userListReq)) {
            Collections.addAll(userListSet, userListReq.split(","));
        }
        PrintColor.zi("发送人列表1,2：本次前端传入的userListReq:" + userListReq + ", 消息模板数据库已有的.UserList:" + userListDb);

        // 3. 处理 ObjJson 数组，加入微信用户ID
        String objJsonStr = wxehookmsgDB.getObjjson();
        if (StringUtils.isNotBlank(objJsonStr)) {
            JSONArray objJsonArray = JSONArray.parseArray(objJsonStr);
            for (int i = 0; i < objJsonArray.size(); i++) {
                JSONObject obj = objJsonArray.getJSONObject(i);
                String oms_userid = obj.getString("objcode");
                // 如果是VM模板，进行渲染
                if (oms_userid.trim().startsWith("$")) {
                    oms_userid = VelocityUtils.renderTemplateToData(oms_userid, msgText);
                }
                // 根据 oms_userid 查询对应的微信用户ID
                String wx_userid = utsWxemsgService.getWxeUserIdByOmsUserid(oms_userid, tid);
                if (StringUtils.isNotBlank(wx_userid)) {
                    userListSet.add(wx_userid);
                }
            }
        }
        PrintColor.zi("发送人列表2：消息模板数据库.ObjJson:" + objJsonStr);

        // 将Set转换为String，逗号分隔
        String finalUserListString = String.join(",", userListSet);
        PrintColor.zi("替换后追加到发送人列表1,2之后： 最终发送人列表：" + finalUserListString);

        // 4.是否转换信息Vm模版
        String msgVM = wxehookmsgDB.getMsgtemplate();
        if (StringUtils.isNotBlank(msgVM)) {
            msgText = VelocityUtils.renderTemplateToData(msgVM, msgText);
            PrintColor.zi("Vm模版替换后的消息内容：" + msgText);
        }

        // 处理@提醒用户列表

        String mentions;
        // 处理@all情况
        boolean isAtAll = "all".equals(userListReq) || "all".equals(userListDb);
        if (isAtAll) {
            mentions = "<@all>";
        } else {
            // 区分处理用户ID和手机号
            List<String> mentionsList = new ArrayList<>();
            for (String user : userListSet) {
                // 用户ID格式
                mentionsList.add("<@" + user + ">");

            }
            mentions = String.join(" ", mentionsList);
        }

        // 将@提醒追加到Markdown内容末尾
        msgText += "\n\n" + mentions;

        // 5.要发送的所有群机器人webhookurl
        String webhookListString = wxehookmsgDB.getWebhooklist().trim();
        List<String> webhookList = Arrays.asList(webhookListString.split(","));

        // 发送消息到所有webhook
        for (String webhook : webhookList) {
            try {
                wxCpGroupRobotService.sendMarkdown(webhook, msgText);
            } catch (WxErrorException e) {
                return R.fail(e.getMessage());
            }
        }
        PrintColor.zi("本次共发送了" + webhookList.size() + "个群机器人，消息内容：" + msgText);
        return R.ok("本次共发送了" + webhookList.size() + "个群机器人，消息内容：" + msgText);
    }


    /**
     * 发送image类型的消息
     * base64 图片内容的base64编码
     * md5    图片内容（base64编码前）的md5值
     *
     * @throws WxErrorException 异常
     */
    @ApiOperation(value = "发送图片消息", notes = "webhookurl: 选填群机器人webhookurl，不填则使用yml中的" +
            "<br>base64：图片内容的base64编码" +
            "<br>md5：图片内容（base64编码前）的md5值")
    @PostMapping("/sendImage")
    public R<Void> sendImage(@RequestBody WxeRobotMessages.ImageMessageRequest request) {
        try {
            if (StringUtils.isNotBlank(request.getWebhookurl())) {
                webhookUrl = request.getWebhookurl();
            }
            wxCpGroupRobotService.sendImage(webhookUrl, request.getBase64(), request.getMd5());
            return R.ok();
        } catch (WxErrorException e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 发送news类型的消息
     * articlelist 图文消息，支持1到8条图文
     *
     * @throws WxErrorException 异常
     */
    @ApiOperation(value = "发送图文消息", notes = "webhookurl: 选填群机器人webhookurl，不填则使用yml中的" +
            "<br>articlelist：图文消息，支持1到8条图文")
    @PostMapping("/sendNews")
    public R<Void> sendNews(@RequestBody WxeRobotMessages.NewsMessageRequest request) {
        try {
            if (StringUtils.isNotBlank(request.getWebhookurl())) {
                webhookUrl = request.getWebhookurl();
            }
            wxCpGroupRobotService.sendNews(webhookUrl, request.getArticlelist());
            return R.ok();
        } catch (WxErrorException e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 发送文件类型的消息
     * mediaid 文件id
     *
     * @throws WxErrorException 异常
     */
    @ApiOperation(value = "发送文件消息", notes = "webhookurl: 选填群机器人webhookurl，不填则使用yml中的" +
            "<br>mediaid：文件id")
    @PostMapping("/sendFile")
    public R<Void> sendFile(@RequestBody WxeRobotMessages.FileMessageRequest request) {
        try {
            if (StringUtils.isNotBlank(request.getWebhookurl())) {
                webhookUrl = request.getWebhookurl();
            }
            wxCpGroupRobotService.sendFile(webhookUrl, request.getMediaid());
            return R.ok();
        } catch (WxErrorException e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 发送语音类型的消息
     * mediaid 语音文件id
     *
     * @throws WxErrorException 异常
     */
    @ApiOperation(value = "发送语音消息", notes = "mediaid：语音文件id")
    @PostMapping("/sendVoice")
    public R<Void> sendVoice(@RequestBody WxeRobotMessages.VoiceMessageRequest request) {
        try {
            if (StringUtils.isNotBlank(request.getWebhookurl())) {
                webhookUrl = request.getWebhookurl();
            }
            wxCpGroupRobotService.sendVoice(webhookUrl, request.getMediaid());
            return R.ok();
        } catch (WxErrorException e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 发送模板卡片消息
     * wxcpgrouprobotmessage 模板卡片消息内容
     *
     * @throws WxErrorException 异常
     */
    @ApiOperation(value = "发送模板卡片消息", notes = "webhookurl: 选填群机器人webhookurl，不填则使用yml中的" +
            "<br>wxcpgrouprobotmessage：模板卡片消息内容")
    @PostMapping("/sendTemplateCardMessage")
    public R<Void> sendTemplateCardMessage(@RequestBody WxeRobotMessages.TemplateCardMessageRequest request) {
        try {
            if (StringUtils.isNotBlank(request.getWebhookurl())) {
                webhookUrl = request.getWebhookurl();
            }
            wxCpGroupRobotService.sendTemplateCardMessage(webhookUrl, request.getWxcpgrouprobotmessage());
            return R.ok();
        } catch (WxErrorException e) {
            return R.fail(e.getMessage());
        }
    }


}
