package inks.service.sa.uts.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.DateRange;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.uts.domain.pojo.UtsFreereportsPojo;
import inks.service.sa.uts.domain.pojo.UtsFreereportsitemPojo;
import inks.service.sa.uts.service.UtsFreereportsService;
import inks.service.sa.uts.service.impl.UtsFreereportsServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 自由报表(Uts_FreeReports)表控制层
 *
 * <AUTHOR>
 * @since 2024-05-14 10:14:36
 */
@RestController
@RequestMapping("S34M05B1")
@Api(tags = "S34M05B1:自由报表")
public class S34M05B1Controller extends UtsFreereportsController {
    @Resource
    private UtsFreereportsService utsFreereportsService;
    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = "执行查询:SELECT...(TOP)...FROM...WHERE...GROUP BY...HAVING...ORDER BY...(LIMIT)...", notes = "", produces = "application/json")
    @RequestMapping(value = "/select", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Uts_FreeReports.List")
    public R<List<Map<String, Object>>> select(String key) {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.utsFreereportsService.select(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "执行查询SQL或HTTP-->构建SQL占位符4个：${tenantid},${startdate},${enddate},${filterstr}", notes = "", produces = "application/json")
    @RequestMapping(value = "/selectEdb", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_FreeReports.List")
    public R<Object> selectEdb(String key, String datapath, @RequestBody(required = false) String json) {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);

            // 构如果是sql类型： 建SQL占位符4个：${tenantid},${startdate},${enddate},${filterstr}
            Map<String, Object> params = buildSqlPlaceholders(queryParam, loginUser);
            return R.ok(this.utsFreereportsService.selectEdb(key, params, datapath, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    //   json params是前端传入的查询参数，举例：{
    //  "PageNum": 1,
    //  "PageSize": 20,
    //  "DateRange": {
    //    "StartDate": "2023-11-01 00:00:00",
    //    "EndDate": "2023-11-30 23:59:59"
    //  },
    //  "scenedata": [
    //    {
    //      "field": "App_Workgroup.Seller",
    //      "fieldtype": 1,
    //      "math": "equal",
    //      "value": "李四"
    //    }
    //  ]
    //}
    @ApiOperation(value = "code和key2选一，按条件分页查询 执行查询SQL或HTTP-->构建SQL占位符4个：${tenantid},${startdate},${enddate},${filterstr}", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/pageListEdb", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_FreeReports.List")
    public R<Object> pageListEdb(@RequestParam(required = false) String code,
                                 @RequestParam(required = false) String key,
                                 @RequestParam(required = false) String datapath,
                                 @RequestBody String json) {
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 构如果是sql类型： 建SQL占位符4个：${tenantid},${startdate},${enddate},${filterstr}
            Map<String, Object> params = buildSqlPlaceholders(queryParam, loginUser);
            //额外分页参数
            params.put("PageNum", queryParam.getPageNum());
            params.put("PageSize", queryParam.getPageSize());
            return R.ok(this.utsFreereportsService.pageListEdb(code, key, params, datapath, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    // 封装到Map中，构建SQL占位符4个：${tenantid},${startdate},${enddate},${filterstr}
    private Map<String, Object> buildSqlPlaceholders(QueryParam queryParam, LoginUser loginUser) {

        // 创建存储占位符的 Map
        Map<String, Object> params = new HashMap<>();

        // 获取租户ID，如果前端未传入，则使用当前登录用户的租户ID
        String tidFront = queryParam.getTenantid();
        params.put("tenantid", StringUtils.isBlank(tidFront) ? loginUser.getTenantid() : tidFront);

        // 获取并格式化日期范围
        DateRange dateRange = queryParam.getDateRange();
        if (dateRange != null) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            params.put("startdate", dateFormat.format(dateRange.getStartDate()));
            params.put("enddate", dateFormat.format(dateRange.getEndDate()));
        }

        // 设置过滤字符串
        String qpfilter = queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        params.put("filterstr", qpfilter);

        return params;
    }


    /**
     * @return R<PageInfo < Map < Object>>>
     * @Description (按条件分页)执行查询:SELECT...(TOP)...FROM...WHERE...GROUP BY...HAVING...ORDER BY...(LIMIT)...
     * 注意聚合函数必须有AS别名(sum/count/avg/max/min)
     * <AUTHOR>
     * @param[1] code
     * @param[2] json
     * @time 2024/5/17 上午8:55
     */
    @ApiOperation(value = "(按条件分页)执行查询:SELECT...(TOP)...FROM...WHERE...GROUP BY...HAVING...ORDER BY...(LIMIT)...注意聚合函数必须有AS别名(sum/count/avg/max/min)", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/pageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_FreeReports.List")
    public R<PageInfo<Map<String, Object>>> pageList(String code, @RequestBody String json) {
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.utsFreereportsService.pageList(code, queryParam, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "根据编码拿到自由报表子表列的List", notes = "", produces = "application/json")
    @RequestMapping(value = "/getListByReportCode", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Uts_FreeReports.List")
    public R<List<UtsFreereportsitemPojo>> getListByReportCode(String code) {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.utsFreereportsService.getListByReportCode(code, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "RMS拥有权限的的报表", notes = "", produces = "application/json")
    @RequestMapping(value = "/getRmsListBySelf", method = RequestMethod.GET)
    public R<List<UtsFreereportsPojo>> getRmsListBySelf() {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.utsFreereportsService.getListBySelf(1, loginUser.getUserid(), loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "OMS拥有权限的的报表", notes = "", produces = "application/json")
    @RequestMapping(value = "/getOmsListBySelf", method = RequestMethod.GET)
    public R<List<UtsFreereportsPojo>> getOmsListBySelf() {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.utsFreereportsService.getListBySelf(0, loginUser.getUserid(), loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "我拥有权限的的报表", notes = "", produces = "application/json")
    @RequestMapping(value = "/myFreeReports", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Uts_FreeReports.List")
    public R<List<UtsFreereportsPojo>> myFreeReports() {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        try {
            return R.ok(this.utsFreereportsService.myFreeReports(loginUser.getUserid(), loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "SqlSelect查询字段信息", notes = "", produces = "application/json")
    @RequestMapping(value = "/getSqlSelectFieldList", method = RequestMethod.GET)
    public R<List<Map<String, Object>>> getSqlSelectFieldList(String key) {
        try {
            String tid = saRedisService.getLoginUser(ServletUtils.getRequest()).getTenantid();
            List<Map<String, Object>> maps = utsFreereportsService.getSqlSelectFieldList(key, tid);
            return R.ok(maps);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增自由报表 默认MySQL语法;若为sqlserver,在json加入databaseType:sqlserver", notes = "新增自由报表", produces = "application/json")
    @RequestMapping(value = "/saveSqlFull", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_FreeReports.Add")
    public R<UtsFreereportsPojo> saveSqlFull(@RequestBody UtsFreereportsPojo freeReportPojo) {
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        try {
//            UtsFreereportsPojo freeReportPojo = JSONArray.parseObject(json,UtsFreereportsPojo.class);
            // 获得用户数据
            String sqlFull = freeReportPojo.getSqlfull();
//            String databasetype = StringUtils.isBlank(freeReportPojo.getDatabasetype()) ? "mysql" : freeReportPojo.getDatabasetype();
            // 拆分 完整的SQL为 SELECT...(TOP)...FROM...WHERE...GROUP BY...HAVING...ORDER BY...(LIMIT)...
            UtsFreereportsPojo parseFreeReports = UtsFreereportsServiceImpl.parseSqlFull(sqlFull);
            // 拆分部分赋值
            freeReportPojo.setSqlselect(parseFreeReports.getSqlselect());
            freeReportPojo.setSqlfrom(parseFreeReports.getSqlfrom());
            freeReportPojo.setSqlwhere(parseFreeReports.getSqlwhere());
            freeReportPojo.setSqlgroupby(parseFreeReports.getSqlgroupby());
            freeReportPojo.setSqlhaving(parseFreeReports.getSqlhaving());
            freeReportPojo.setSqlorderby(parseFreeReports.getSqlorderby());
            freeReportPojo.setSqllimit(parseFreeReports.getSqllimit());

            freeReportPojo.setLister(loginUser.getRealname());   // 制表
            freeReportPojo.setListerid(loginUser.getUserid());    // 制表id
            freeReportPojo.setModifydate(new Date());   //修改时间
            freeReportPojo.setTenantid(loginUser.getTenantid());   //租户id
            if (StringUtils.isBlank(freeReportPojo.getId())) {
                freeReportPojo.setCreateby(loginUser.getRealName());   // 创建者
                freeReportPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
                freeReportPojo.setCreatedate(new Date());
                return R.ok(this.utsFreereportsService.insert(freeReportPojo));
            } else {
                return R.ok(this.utsFreereportsService.update(freeReportPojo));
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
