package inks.service.sa.uts.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.domain.R;
import inks.common.core.domain.QueryParam;
import inks.service.sa.uts.domain.pojo.UtsIntegrationPojo;
import inks.service.sa.uts.service.UtsIntegrationService;
import inks.sa.common.core.service.SaRedisService;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import net.sf.jasperreports.engine.*;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;
/**
 * API整合转发(Uts_Integration)表控制层
 *
 * <AUTHOR>
 * @since 2025-06-20 13:06:38
 */
//@RestController
//@RequestMapping("utsIntegration")
public class UtsIntegrationController {
    @Resource
    private UtsIntegrationService utsIntegrationService;
    
    @Resource
    private SaRedisService saRedisService;
    
    private final static Logger logger = LoggerFactory.getLogger(UtsIntegrationController.class);


    @ApiOperation(value=" 获取API整合转发详细信息", notes="获取API整合转发详细信息", produces="application/json")
    @RequestMapping(value="/getEntity",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Uts_Integration.List")
    public R<UtsIntegrationPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.utsIntegrationService.getEntity(key));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    
    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageList",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_Integration.List")
    public R<PageInfo<UtsIntegrationPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
            if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Uts_Integration.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.utsIntegrationService.getPageList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    
    @ApiOperation(value=" 新增API整合转发", notes="新增API整合转发", produces="application/json")
    @RequestMapping(value="/create",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_Integration.Add")
    public R<UtsIntegrationPojo> create(@RequestBody String json) {
        try {
            UtsIntegrationPojo utsIntegrationPojo = JSONArray.parseObject(json,UtsIntegrationPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            utsIntegrationPojo.setCreateby(loginUser.getRealName());   // 创建者
            utsIntegrationPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            utsIntegrationPojo.setCreatedate(new Date());   // 创建时间
            utsIntegrationPojo.setLister(loginUser.getRealname());   // 制表
            utsIntegrationPojo.setListerid(loginUser.getUserid());    // 制表id
            utsIntegrationPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.utsIntegrationService.insert(utsIntegrationPojo));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    @ApiOperation(value="修改API整合转发", notes="修改API整合转发", produces="application/json")
    @RequestMapping(value="/update",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_Integration.Edit")
    public R<UtsIntegrationPojo> update(@RequestBody String json) {
        try {
            UtsIntegrationPojo utsIntegrationPojo = JSONArray.parseObject(json,UtsIntegrationPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            utsIntegrationPojo.setLister(loginUser.getRealname());   // 制表
            utsIntegrationPojo.setListerid(loginUser.getUserid());    // 制表id
            utsIntegrationPojo.setModifydate(new Date());   //修改时间
    //            utsIntegrationPojo.setAssessor(""); // 审核员
    //            utsIntegrationPojo.setAssessorid(""); // 审核员id
    //            utsIntegrationPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.utsIntegrationService.update(utsIntegrationPojo));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    @ApiOperation(value="删除API整合转发", notes="删除API整合转发", produces="application/json")
    @RequestMapping(value="/delete",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Uts_Integration.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.utsIntegrationService.delete(key));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Uts_Integration.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        UtsIntegrationPojo utsIntegrationPojo = this.utsIntegrationService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(utsIntegrationPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content ;
        if (reportsPojo != null ) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response= ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
    }

