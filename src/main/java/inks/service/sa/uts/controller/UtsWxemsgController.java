package inks.service.sa.uts.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.uts.domain.pojo.UtsWxemsgPojo;
import inks.service.sa.uts.service.UtsWxemsgService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 企业微信信息(Uts_WxeMsg)表控制层
 *
 * <AUTHOR>
 * @since 2023-01-10 11:12:38
 */
@RestController
@RequestMapping("utsWxemsg")
public class UtsWxemsgController {

    private final static Logger logger = LoggerFactory.getLogger(UtsWxemsgController.class);

    @Resource
    private UtsWxemsgService utsWxemsgService;

    @Resource
    private SaRedisService saRedisService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取企业微信信息详细信息", notes = "获取企业微信信息详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Uts_WxeMsg.List")
    public R<UtsWxemsgPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.utsWxemsgService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_WxeMsg.List")
    public R<PageInfo<UtsWxemsgPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Uts_WxeMsg.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.utsWxemsgService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增企业微信信息", notes = "新增企业微信信息", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_WxeMsg.Add")
    public R<UtsWxemsgPojo> create(@RequestBody String json) {
        try {
            UtsWxemsgPojo utsWxemsgPojo = JSONArray.parseObject(json, UtsWxemsgPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            utsWxemsgPojo.setCreateby(loginUser.getRealName());   // 创建者
            utsWxemsgPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            utsWxemsgPojo.setCreatedate(new Date());   // 创建时间
            utsWxemsgPojo.setLister(loginUser.getRealname());   // 制表
            utsWxemsgPojo.setListerid(loginUser.getUserid());    // 制表id  
            utsWxemsgPojo.setModifydate(new Date());   //修改时间
            utsWxemsgPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.utsWxemsgService.insert(utsWxemsgPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改企业微信信息", notes = "修改企业微信信息", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_WxeMsg.Edit")
    public R<UtsWxemsgPojo> update(@RequestBody String json) {
        try {
            UtsWxemsgPojo utsWxemsgPojo = JSONArray.parseObject(json, UtsWxemsgPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            utsWxemsgPojo.setLister(loginUser.getRealname());   // 制表
            utsWxemsgPojo.setListerid(loginUser.getUserid());    // 制表id  
            utsWxemsgPojo.setTenantid(loginUser.getTenantid());   //租户id
            utsWxemsgPojo.setModifydate(new Date());   //修改时间
//            utsWxemsgPojo.setAssessor(""); // 审核员
//            utsWxemsgPojo.setAssessorid(""); // 审核员id
//            utsWxemsgPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.utsWxemsgService.update(utsWxemsgPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除企业微信信息", notes = "删除企业微信信息", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Uts_WxeMsg.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.utsWxemsgService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Uts_WxeMsg.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        UtsWxemsgPojo utsWxemsgPojo = this.utsWxemsgService.getEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(utsWxemsgPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

