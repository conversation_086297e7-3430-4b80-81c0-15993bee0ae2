package inks.service.sa.uts.controller;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.dingtalk.api.response.OapiRobotSendResponse;
import com.taobao.api.ApiException;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class DingRobotGroupMessageTest {
    //、、https://oapi.dingtalk.com/robot/send?access_token=138e79985de3f508cd89073ec35c1318fdad85204443b2b7736fb1c54aad90cf
//    public static final String CUSTOM_ROBOT_TOKEN = "8cc05b0481f6ed739a9c90a52b241e79725e2b6d3c50c5cc8ddc93146d9e7233";
    public static final String CUSTOM_ROBOT_TOKEN = "138e79985de3f508cd89073ec35c1318fdad85204443b2b7736fb1c54aad90cf";
    //public static final String SECRET = "SEC5f203cacfaaa2420ff0b46135e4bb9c075715a74d496cb8f9da68fbff987dde2";
    public static final String SECRET = "SEC8c532756825845e631d1a3e23a31817a1aae6440d788e7daaa4114e283124d0e";
    public static final String USER_ID = "01262948650133270328,500365206536788348,28281420031052397";

    public static void main(String[] args) {
        try {
            String webhook = buildSignedUrl();
            DingTalkClient client = new DefaultDingTalkClient(webhook);

            //// 发送文本消息
            //sendTextMessage(client);
            //
            //// 发送链接消息
            //sendLinkMessage(client);

            // 发送 Markdown 消息
            sendMarkdownMessage(client);

            //// 发送整体跳转 ActionCard
            //sendSingleActionCard(client);
            //
            //// 发送独立跳转 ActionCard
            //sendMultiActionCard(client);
            //
            //// 发送 FeedCard
            //sendFeedCard(client);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static String buildSignedUrl() throws Exception {
        Long timestamp = System.currentTimeMillis();
        String stringToSign = timestamp + "\n" + SECRET;
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(SECRET.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
        byte[] signData = mac.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));
        String sign = URLEncoder.encode(new String(Base64.encodeBase64(signData)), "UTF-8");
        return "https://oapi.dingtalk.com/robot/send?sign=" + sign + "&timestamp=" + timestamp;
    }

    // ================ 消息发送方法 ================ 

    /**
     * 1. 发送文本消息
     */
    private static void sendTextMessage(DingTalkClient client) throws ApiException {
        OapiRobotSendRequest req = new OapiRobotSendRequest();
        req.setMsgtype("text");
        OapiRobotSendRequest.Text text = new OapiRobotSendRequest.Text();
        text.setContent("测试-发送文本消息 钉钉，让进步发生");
        OapiRobotSendRequest.At at = new OapiRobotSendRequest.At();
        at.setAtUserIds(Arrays.asList(USER_ID.split(",")));
        req.setText(text);
        req.setAt(at);
        executeAndPrint(client, req);
    }

    /**
     * 2. 发送链接消息
     */
    private static void sendLinkMessage(DingTalkClient client) throws ApiException {
        OapiRobotSendRequest req = new OapiRobotSendRequest();
        req.setMsgtype("link");
        OapiRobotSendRequest.Link link = new OapiRobotSendRequest.Link();
        link.setTitle("测试-发送链接消息 故障通告");
        link.setText("服务器CPU使用率超过90%");
        link.setMessageUrl("https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRK1SLSsr4LrproUD1qhJPeD2pE6uPbfLk8dQ&s");
        link.setPicUrl("https://meet.eslite.com/CMS/Files/@M091/2022-11/bocchi_the_rock/003_bocchi_the_rock.jpg");
        req.setLink(link);
        executeAndPrint(client, req);
    }

    /**
     * 3. 发送Markdown消息
     */
    private static void sendMarkdownMessage(DingTalkClient client) throws ApiException {
        OapiRobotSendRequest req = new OapiRobotSendRequest();
        req.setMsgtype("markdown");
        OapiRobotSendRequest.Markdown markdown = new OapiRobotSendRequest.Markdown();
        markdown.setTitle("测试- 发送Markdown消息 更新日志");
        markdown.setText("### 2024-03更新\n- 新增性能监控模块\n- 修复登录BUG @01262948650133270328");
        //String text = "### 2024-03更新\n- 新增性能监控模块\n- 修复登录BUG @01262948650133270328";
        String text = "#### 杭州天气 @15012345678\n" +
                "> ​**实时天气**  \n" +  // 引用块包裹整体内容
                "> 9度，西北风1级，空气良89，相对温度73%\n\n" +  // 空行分隔段落
                "![天气截图](https://img.alicdn.com/tfs/TB1NwmBEL9TBuNjy1zbXXXpepXa-2400-1218.png)\n\n" +
                "###### 10点20分发布 [查看详情](https://www.dingtalk.com)";
        markdown.setText(text);
        OapiRobotSendRequest.At at = new OapiRobotSendRequest.At();
        at.setAtUserIds(Collections.singletonList("01262948650133270328")); // 指定被@用户
        req.setMarkdown(markdown);
        req.setAt(at);
        executeAndPrint(client, req);
    }

    /**
     * 4. 整体跳转ActionCard（单按钮）
     */
    private static void sendSingleActionCard(DingTalkClient client) throws ApiException {
        OapiRobotSendRequest req = new OapiRobotSendRequest();
        req.setMsgtype("actionCard");
        OapiRobotSendRequest.Actioncard actionCard = new OapiRobotSendRequest.Actioncard();
        actionCard.setTitle("任务通知");
        actionCard.setText("测试-整体跳转ActionCard（单按钮）");
        actionCard.setSingleTitle("前往111");
        actionCard.setSingleURL("https://e.gitee.com/");
        req.setActionCard(actionCard);
        executeAndPrint(client, req);
    }

    /**
     * 5. 独立跳转ActionCard（多按钮）
     */
    private static void sendMultiActionCard(DingTalkClient client) throws ApiException {
        OapiRobotSendRequest req = new OapiRobotSendRequest();
        req.setMsgtype("actionCard");
        OapiRobotSendRequest.Actioncard actionCard = new OapiRobotSendRequest.Actioncard();
        actionCard.setTitle("操作菜单");
        actionCard.setText("测试-独立跳转ActionCard（多按钮）");
        actionCard.setBtnOrientation("1");

        // 创建按钮列表(使用 Btns 类)
        List<OapiRobotSendRequest.Btns> buttons = new ArrayList<>();

        OapiRobotSendRequest.Btns btn1 = new OapiRobotSendRequest.Btns();
        btn1.setTitle("前往1");  // 按钮文案
        btn1.setActionURL("https://chat.deepseek.com/");
        buttons.add(btn1);

        OapiRobotSendRequest.Btns btn2 = new OapiRobotSendRequest.Btns();
        btn2.setTitle("前往2");
        btn2.setActionURL("https://kaifa.baidu.com/");  // 按钮跳转链接
        buttons.add(btn2);

        actionCard.setBtns(buttons);
        req.setActionCard(actionCard);
        executeAndPrint(client, req);
    }

    /**
     * 6. 发送FeedCard（信息流）
     */
    private static void sendFeedCard(DingTalkClient client) throws ApiException {
        OapiRobotSendRequest req = new OapiRobotSendRequest();
        req.setMsgtype("feedCard");
        OapiRobotSendRequest.Feedcard feedCard = new OapiRobotSendRequest.Feedcard();

        List<OapiRobotSendRequest.Links> linkList = new ArrayList<>();

        // 使用无参构造 + Setter 设置链接字段
        OapiRobotSendRequest.Links link1 = new OapiRobotSendRequest.Links();
        link1.setTitle("新闻1");
        link1.setPicURL("https://meet.eslite.com/CMS/Files/@M091/2022-11/bocchi_the_rock/003_bocchi_the_rock.jpg");
        link1.setMessageURL("https://kaifa.baidu.com/");
        linkList.add(link1);

        OapiRobotSendRequest.Links link2 = new OapiRobotSendRequest.Links();
        link2.setTitle("新闻2");
        link2.setPicURL("https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRK1SLSsr4LrproUD1qhJPeD2pE6uPbfLk8dQ&s");
        link2.setMessageURL("https://www.baidu.com/");
        linkList.add(link2);

        feedCard.setLinks(linkList);
        req.setFeedCard(feedCard);
        executeAndPrint(client, req);
    }

    /**
     * 公共执行方法
     */
    private static void executeAndPrint(DingTalkClient client, OapiRobotSendRequest req) throws ApiException {
        OapiRobotSendResponse rsp = client.execute(req, CUSTOM_ROBOT_TOKEN);
        System.out.println("Response: " + rsp.getBody());
    }
}