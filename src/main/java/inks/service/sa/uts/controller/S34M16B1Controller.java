package inks.service.sa.uts.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.sa.common.core.utils.PrintColor;
import inks.service.sa.uts.domain.pojo.UtsIntegrationPojo;
import inks.service.sa.uts.service.UtsIntegrationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * API整合转发(Uts_Integration)表控制层
 *
 * <AUTHOR>
 * @since 2025-06-20 13:06:38
 */
@RestController
@RequestMapping("S34M16B1")
@Api(tags = "S34M16B1:API整合转发")
public class S34M16B1Controller extends UtsIntegrationController {

    @Resource
    private UtsIntegrationService utsIntegrationService;

    @Resource
    private SaRedisService saRedisService;

    @Resource
    private RestTemplate restTemplate;

    private final static Logger logger = LoggerFactory.getLogger(S34M16B1Controller.class);

    // 代理类型枚举
    private enum ProxyType {
        PAGE_LIST(0, "PageList"),
        PAGE_TH(1, "PageTh"),
        ENTITY(2, "Entity"),
        BILL_ENTITY(3, "BillEntity"),
        BILL_LIST(4, "BillList"),
        UPDATE(5, "Update");

        private final int code;
        private final String name;

        ProxyType(int code, String name) {
            this.code = code;
            this.name = name;
        }

        public int getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static ProxyType fromCode(int code) {
            for (ProxyType type : values()) {
                if (type.code == code) return type;
            }
            return null;
        }
    }

    // ====================== 前端接口 ======================

    /**
     * 代理分页列表接口
     * 示例: /S34M16B1/proxyPageList?code=inkscrm.S07M01B1
     */
    @ApiOperation(value = "代理分页列表", notes = "通过集成编码代理获取外部系统分页数据", produces = "application/json")
    @RequestMapping(value = "/proxyPageList", method = RequestMethod.POST)
    public R<Object> proxyPageList(@RequestParam String code, @RequestBody(required = false) String json) {
        return proxyApi(code, ProxyType.PAGE_LIST.getCode(), null, json, ServletUtils.getRequest());
    }

    /**
     * 代理分页表头接口
     * 示例: /S34M16B1/proxyPageTh?code=inkscrm.S07M01B1
     */
    @ApiOperation(value = "代理分页表头", notes = "通过集成编码代理获取外部系统分页表头数据", produces = "application/json")
    @RequestMapping(value = "/proxyPageTh", method = RequestMethod.POST)
    public R<Object> proxyPageTh(@RequestParam String code, @RequestBody(required = false) String json) {
        return proxyApi(code, ProxyType.PAGE_TH.getCode(), null, json, ServletUtils.getRequest());
    }

    /**
     * 代理实体详情接口
     * 示例: /S34M16B1/proxyEntity?code=inkscrm.S07M03B1&key=123
     */
    @ApiOperation(value = "代理实体详情", notes = "通过集成编码代理获取外部系统实体详情", produces = "application/json")
    @RequestMapping(value = "/proxyEntity", method = RequestMethod.GET)
    public R<Object> proxyEntity(@RequestParam String code, @RequestParam String key) {
        return proxyApi(code, ProxyType.ENTITY.getCode(), key, null, ServletUtils.getRequest());
    }

    /**
     * 代理单据详情接口
     * 示例: /S34M16B1/proxyBillEntity?code=inkscrm.S07M03B1&key=123
     */
    @ApiOperation(value = "代理单据详情", notes = "通过集成编码代理获取外部系统单据详情", produces = "application/json")
    @RequestMapping(value = "/proxyBillEntity", method = RequestMethod.GET)
    public R<Object> proxyBillEntity(@RequestParam String code, @RequestParam String key) {
        return proxyApi(code, ProxyType.BILL_ENTITY.getCode(), key, null, ServletUtils.getRequest());
    }

    /**
     * 代理单据列表接口
     * 示例: /S34M16B1/proxyBillList?code=inkscrm.S07M01B1
     */
    @ApiOperation(value = "代理单据列表", notes = "通过集成编码代理获取外部系统单据列表数据", produces = "application/json")
    @RequestMapping(value = "/proxyBillList", method = RequestMethod.POST)
    public R<Object> proxyBillList(@RequestParam String code, @RequestBody(required = false) String json) {
        return proxyApi(code, ProxyType.BILL_LIST.getCode(), null, json, ServletUtils.getRequest());
    }

    /**
     * 代理更新接口
     * 示例: /S34M16B1/proxyUpdate?code=inkscrm.S07M03B1
     */
    @ApiOperation(value = "代理更新", notes = "通过集成编码代理更新外部系统数据", produces = "application/json")
    @RequestMapping(value = "/proxyUpdate", method = RequestMethod.POST)
    public R<Object> proxyUpdate(@RequestParam String code, @RequestBody String json) {
        return proxyApi(code, ProxyType.UPDATE.getCode(), null, json, ServletUtils.getRequest());
    }

    /**
     * 测试连接接口
     */
    @ApiOperation(value = "测试连接", notes = "测试外部API连接是否正常", produces = "application/json")
    @RequestMapping(value = "/testConnection", method = RequestMethod.GET)
    public R<String> testConnection(@RequestParam String code, @RequestParam Integer proxytype) {
        try {
            ProxyType type = ProxyType.fromCode(proxytype);
            if (type == null) {
                return R.fail("不支持的代理类型: " + proxytype);
            }

            UtsIntegrationPojo config = utsIntegrationService.getEntityByCodeAndType(code, proxytype);
            if (config == null) {
                return R.fail("未找到集成配置: " + code + ", 代理类型: " + type.getName());
            }

            // 发送测试请求
            String targetUrl = config.getApiurl();
            Object response = sendExternalRequest(config, targetUrl, null);

            return R.ok("连接测试成功", "API连接正常");

        } catch (Exception e) {
            logger.error("测试连接失败", e);
            return R.fail("连接测试失败: " + e.getMessage());
        }
    }

    // ====================== 核心代理方法 ======================

    /**
     * 统一代理接口 - 核心方法
     * @param code 集成编码
     * @param proxytype 代理类型
     * @param key 实体查询key（可选）
     * @param json 请求体数据（可选）
     * @param request HTTP请求对象
     * @return 代理结果
     */
    private R<Object> proxyApi(String code, Integer proxytype, String key, String json, HttpServletRequest request) {
        try {
            ProxyType type = ProxyType.fromCode(proxytype);
            if (type == null) {
                return R.fail("不支持的代理类型: " + proxytype);
            }

            UtsIntegrationPojo config = utsIntegrationService.getEntityByCodeAndType(code, proxytype);
            if (config == null) {
                return R.fail("未找到集成配置: " + code + ", 代理类型: " + type.getName());
            }

            // --- 修改开始 ---
            // 1. 使用 StringBuilder 构建URL，以便动态添加参数
            StringBuilder targetUrlBuilder = new StringBuilder(config.getApiurl());

            // 2. 附加 key (如果适用)
            if ((type == ProxyType.ENTITY || type == ProxyType.BILL_ENTITY) && StringUtils.hasText(key)) {
                targetUrlBuilder.append(targetUrlBuilder.indexOf("?") == -1 ? "?" : "&").append("key=").append(key);
            }

            // 3. 获取并附加所有其他查询参数
            Map<String, String[]> parameterMap = request.getParameterMap();
            boolean hasQuery = targetUrlBuilder.indexOf("?") != -1;

            for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
                String paramKey = entry.getKey();
                // 排除已处理或内部使用的参数
                if ("code".equals(paramKey) || "key".equals(paramKey)) {
                    continue;
                }
                for (String paramValue : entry.getValue()) {
                    if (!hasQuery) {
                        targetUrlBuilder.append('?');
                        hasQuery = true;
                    } else {
                        targetUrlBuilder.append('&');
                    }
                    targetUrlBuilder.append(paramKey).append('=').append(paramValue);
                }
            }
            String targetUrl = targetUrlBuilder.toString();
            // --- 修改结束 ---

            Object requestData = null;
            if (StringUtils.hasText(json)) {
                requestData = JSONObject.parse(json);
            }

            Object response = sendExternalRequest(config, targetUrl, requestData);

            PrintColor.zi("http请求返回：" + JSONObject.toJSONString(response));
            return R.ok(response);

        } catch (Exception e) {
            logger.error("代理请求失败", e);
            return R.fail("代理请求失败: " + e.getMessage());
        }
    }
    // ====================== 私有辅助方法 ======================



    /**
     * 根据代理类型名称获取代理类型码
     */
    private Integer getProxyTypeCode(String proxyTypeName) {
        for (ProxyType type : ProxyType.values()) {
            if (type.getName().equals(proxyTypeName)) {
                return type.getCode();
            }
        }
        return null;
    }

    /**
     * 构建带key参数的URL
     */
    private String buildUrlWithKey(String apiUrl, String key) {
        if (apiUrl.contains("?")) {
            return apiUrl + "&key=" + key;
        } else {
            return apiUrl + "?key=" + key;
        }
    }

    /**
     * 发送外部请求
     */
    private Object sendExternalRequest(UtsIntegrationPojo config, String targetUrl, Object requestData) {
        try {
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 添加认证信息
            addAuthentication(headers, config);

            // 根据请求方法发送请求
            if ("POST".equalsIgnoreCase(config.getReqmethod())) {
                HttpEntity<Object> entity = new HttpEntity<>(requestData, headers);
                ResponseEntity<Object> response = restTemplate.exchange(targetUrl, HttpMethod.POST, entity, Object.class);
                return response.getBody();
            } else {
                HttpEntity<Object> entity = new HttpEntity<>(headers);
                ResponseEntity<Object> response = restTemplate.exchange(targetUrl, HttpMethod.GET, entity, Object.class);
                return response.getBody();
            }

        } catch (Exception e) {
            logger.error("发送外部请求失败: " + targetUrl, e);
            throw new RuntimeException("外部API调用失败: " + e.getMessage());
        }
    }

    /**
     * 添加认证信息
     */
    private void addAuthentication(HttpHeaders headers, UtsIntegrationPojo config) {
        Integer authType = config.getAuthtype();
        if (authType == null) return;

        switch (authType) {
            case 0: // 请求头加入authcode
                if (StringUtils.hasText(config.getAuthcode())) {
                    headers.set("authcode", config.getAuthcode());
                }
                break;
            case 1: // Basic 用户名+密码
                if (StringUtils.hasText(config.getAuthname()) && StringUtils.hasText(config.getAuthsecret())) {
                    String raw = config.getAuthname() + ":" + config.getAuthsecret();
                    String encoded = Base64.getEncoder()
                            .encodeToString(raw.getBytes(StandardCharsets.UTF_8));
                    headers.set(HttpHeaders.AUTHORIZATION, "Basic " + encoded);
                }
                break;
            default:
                break;
        }
    }
}