package inks.service.sa.uts.controller;

import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * SSH服务器配置(Sa_SshServers)表控制层
 *
 * <AUTHOR>
 * @since 2025-05-19 11:11:06
 */
@RestController
@RequestMapping("S34M11B1")
@Api(tags = "S34M11B1:SSH服务器配置")
public class S34M11B1Controller extends SaSshserversController {

}

