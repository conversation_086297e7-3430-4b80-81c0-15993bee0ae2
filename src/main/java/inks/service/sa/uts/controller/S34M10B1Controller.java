package inks.service.sa.uts.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.exception.job.TaskException;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.uts.domain.pojo.SaJobPojo;
import inks.service.sa.uts.service.SaJobService;
import inks.service.sa.uts.utils.quartz.Constants;
import inks.service.sa.uts.utils.quartz.CronUtils;
import inks.service.sa.uts.utils.quartz.ScheduleInfo;
import inks.service.sa.uts.utils.quartz.ScheduleUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.quartz.SchedulerException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 定时任务调度表(Sa_Job)表控制层
 *
 * <AUTHOR>
 * @since 2025-03-07 16:07:17
 */
@RestController
@RequestMapping("S34M10B1")
@Api(tags = "S34M10B1:定时任务调度表")
public class S34M10B1Controller extends SaJobController {
    @Resource
    private SaJobService jobService;

    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = "校验Cron并返回下次触发时间(3次)", notes = "校验 cron 表达式是否合法，并返回接下来的三次触发时间", produces = "application/json")
    @GetMapping("/checkCron")
    public R checkCron(@RequestParam String cron) {
        try {
            List<String> next3Times = CronUtils.getNextThreeExecutionTimes(cron);
            return R.ok(next3Times);
        } catch (IllegalArgumentException e) {
            return R.fail("cron 表达式非法: " + e.getMessage());
        }
    }

    @ApiOperation(value = "返回调度器中所有的定时任务 triggerstat=NORMAL正常；triggerstat=PAUSED暂停；triggerstat=COMPLETE完成；triggerstat=ERROR错误；triggerstat=BLOCKED阻塞；triggerstat=NONE无状态", notes = "返回所有系统中的定时任务", produces = "application/json")
    @GetMapping("/getAllScheduledJobs")
    public R getAllScheduledJobs() throws SchedulerException {
        List<ScheduleInfo> allScheduledJobs = jobService.getAllScheduledJobs();
        return R.ok(allScheduledJobs);
    }

    /**
     * 新增定时任务
     */
    //@PreAuthorize("@ss.hasPermi('monitor:job:add')")
    @ApiOperation(value = "创建定时任务", notes = "创建新的定时任务调度", produces = "application/json")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public R add(@RequestBody SaJobPojo job) throws SchedulerException, TaskException {
        LoginUser loginUser = saRedisService.getLoginUser();
        if (!CronUtils.isValid(job.getCronexpression())) {
            return R.fail("新增任务'" + job.getJobname() + "'失败，Cron表达式不正确");
        } else if (StringUtils.containsIgnoreCase(job.getInvoketarget(), Constants.LOOKUP_RMI)) {
            return R.fail("新增任务'" + job.getJobname() + "'失败，目标字符串不允许'rmi'调用");
        } else if (StringUtils.containsAnyIgnoreCase(job.getInvoketarget(), new String[]{Constants.LOOKUP_LDAP, Constants.LOOKUP_LDAPS})) {
            return R.fail("新增任务'" + job.getJobname() + "'失败，目标字符串不允许'ldap(s)'调用");
        } else if (StringUtils.containsAnyIgnoreCase(job.getInvoketarget(), new String[]{Constants.HTTP, Constants.HTTPS})) {
            return R.fail("新增任务'" + job.getJobname() + "'失败，目标字符串不允许'http(s)'调用");
        } else if (StringUtils.containsAnyIgnoreCase(job.getInvoketarget(), Constants.JOB_ERROR_STR)) {
            return R.fail("新增任务'" + job.getJobname() + "'失败，目标字符串存在违规");
        }
        //else if (!ScheduleUtils.whiteList(job.getInvoketarget())) {
        //    return R.fail("新增任务'" + job.getJobname() + "'失败，目标字符串不在白名单内");
        //}
        job.setCreateby(loginUser.getRealname());
        job.setCreatetime(new Date());
        return R.ok(jobService.insertJob(job));
    }

    /**
     * 修改定时任务
     */
    @ApiOperation(value = "修改定时任务", notes = "更新现有定时任务信息", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.PUT)
    public R edit(@RequestBody SaJobPojo job) throws SchedulerException, TaskException {
        LoginUser loginUser = saRedisService.getLoginUser();
        if (!CronUtils.isValid(job.getCronexpression())) {
            return R.fail("修改任务'" + job.getJobname() + "'失败，Cron表达式不正确");
        } else if (StringUtils.containsIgnoreCase(job.getInvoketarget(), Constants.LOOKUP_RMI)) {
            return R.fail("修改任务'" + job.getJobname() + "'失败，目标字符串不允许'rmi'调用");
        } else if (StringUtils.containsAnyIgnoreCase(job.getInvoketarget(), new String[]{Constants.LOOKUP_LDAP, Constants.LOOKUP_LDAPS})) {
            return R.fail("修改任务'" + job.getJobname() + "'失败，目标字符串不允许'ldap(s)'调用");
        } else if (StringUtils.containsAnyIgnoreCase(job.getInvoketarget(), new String[]{Constants.HTTP, Constants.HTTPS})) {
            return R.fail("修改任务'" + job.getJobname() + "'失败，目标字符串不允许'http(s)'调用");
        } else if (StringUtils.containsAnyIgnoreCase(job.getInvoketarget(), Constants.JOB_ERROR_STR)) {
            return R.fail("修改任务'" + job.getJobname() + "'失败，目标字符串存在违规");
        } else if (!ScheduleUtils.whiteList(job.getInvoketarget())) {
            return R.fail("修改任务'" + job.getJobname() + "'失败，目标字符串不在白名单内");
        }
        job.setUpdateby(loginUser.getRealname());
        job.setUpdatetime(new Date());
        return R.ok(jobService.updateJob(job));
    }

    /**
     * 定时任务状态修改
     */
    //@PreAuthorize("@ss.hasPermi('monitor:job:changeStatus')")
    @ApiOperation(value = "修改任务状态", notes = "启用/禁用定时任务", produces = "application/json")
    @RequestMapping(value = "/changeStatus", method = RequestMethod.PUT)
    public R changeStatus(@RequestBody SaJobPojo job) throws SchedulerException {
        LoginUser loginUser = saRedisService.getLoginUser();
        SaJobPojo newJob = jobService.getEntity(job.getId());
        newJob.setStatus(job.getStatus());
        return R.ok(jobService.changeStatus(newJob));
    }

    /**
     * 定时任务立即执行一次
     */
    //@PreAuthorize("@ss.hasPermi('monitor:job:changeStatus')")
    @ApiOperation(value = "立即执行任务 传入id、group (其中group不传则默认为DEFAULT分组)", notes = "立即触发执行定时任务", produces = "application/json")
    @RequestMapping(value = "/run", method = RequestMethod.PUT)
    public R run(@RequestBody SaJobPojo job) throws SchedulerException {
        LoginUser loginUser = saRedisService.getLoginUser();
        boolean result = jobService.run(job);
        return result ? R.ok() : R.fail("任务不存在或已过期！");
    }

    /**
     * 删除定时任务
     */
    //@PreAuthorize("@ss.hasPermi('monitor:job:remove')")
    @ApiOperation(value = "删除定时任务", notes = "删除定时任务", produces = "application/json")
    @DeleteMapping("/{jobIds}")
    public R remove(@PathVariable String[] jobIds) throws SchedulerException, TaskException {
        jobService.deleteJobByIds(jobIds);
        return R.ok();
    }

}
