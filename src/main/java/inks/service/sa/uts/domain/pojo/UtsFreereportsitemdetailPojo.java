package inks.service.sa.uts.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.DatailPojo;

import java.io.Serializable;

/**
 * 自由报表项目(UtsFreereportsitem)Pojo
 *
 * <AUTHOR>
 * @since 2024-05-15 11:29:57
 */
public class UtsFreereportsitemdetailPojo extends DatailPojo implements Serializable {
    private static final long serialVersionUID = 959113599841711784L;
     // id
  @Excel(name = "id")    
  private String id;
     // Pid
  @Excel(name = "Pid")    
  private String pid;
     // 字段类型
  @Excel(name = "字段类型")    
  private String fieldtype;
     // 字段名称
  @Excel(name = "字段名称")    
  private String fieldname;
     // 列头显示
  @Excel(name = "列头显示")    
  private String headertext;
     // 数据邦定
  @Excel(name = "数据邦定")    
  private String datapropertyname;
     // 排序字符
  @Excel(name = "排序字符")    
  private String orderstr;
     // 列宽
  @Excel(name = "列宽")    
  private Integer colwidth;
     // 列对齐
  @Excel(name = "列对齐")    
  private String colalign;
     // 显示编号
  @Excel(name = "显示编号")    
  private Integer displayno;
     // 显示状态
  @Excel(name = "显示状态")    
  private Integer displaystate;
     // 文本格式
  @Excel(name = "文本格式")    
  private String formatstring;
     // 默认宽度
  @Excel(name = "默认宽度")    
  private String defwidth;
     // 最小宽度
  @Excel(name = "最小宽度")    
  private String minwidth;
     // 1固定0否
  @Excel(name = "1固定0否")    
  private Integer fixed;
     // 1可排序
  @Excel(name = "1可排序")    
  private Integer sortable;
     // 排序表.字段
  @Excel(name = "排序表.字段")    
  private String orderfield;
     // 1溢出隐藏
  @Excel(name = "1溢出隐藏")    
  private Integer overflow;
     // 格式化
  @Excel(name = "格式化")    
  private String formatter;
     // 自定义类
  @Excel(name = "自定义类")    
  private String classname;
     // left/center/right
  @Excel(name = "left/center/right")    
  private String aligntype;
     // 事件名称
  @Excel(name = "事件名称")    
  private String eventname;
     // 可编辑
  @Excel(name = "可编辑")    
  private Integer editmark;
     // 可操作
  @Excel(name = "可操作")    
  private Integer operationmark;
     // 显示位
  @Excel(name = "显示位")    
  private Integer displayindex;
     // RowNum
  @Excel(name = "RowNum")    
  private Integer rownum;
     // 自定义1
  @Excel(name = "自定义1")    
  private String custom1;
     // 自定义2
  @Excel(name = "自定义2")    
  private String custom2;
     // 自定义3
  @Excel(name = "自定义3")    
  private String custom3;
     // 自定义4
  @Excel(name = "自定义4")    
  private String custom4;
     // 自定义5
  @Excel(name = "自定义5")    
  private String custom5;
     // 租户id
  @Excel(name = "租户id")    
  private String tenantid;
     // 租户名称
  @Excel(name = "租户名称")    
  private String tenantname;
     // 乐观锁
  @Excel(name = "乐观锁")    
  private Integer revision;

  //DatabaseTitle 数据库标题
  @Excel(name = "数据库标题")
  private String databasetitle;

   // id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }

    public String getDatabasetitle() {
        return databasetitle;
    }

    public void setDatabasetitle(String databasetitle) {
        this.databasetitle = databasetitle;
    }

    // Pid
    public String getPid() {
        return pid;
    }
    
    public void setPid(String pid) {
        this.pid = pid;
    }
        
   // 字段类型
    public String getFieldtype() {
        return fieldtype;
    }
    
    public void setFieldtype(String fieldtype) {
        this.fieldtype = fieldtype;
    }
        
   // 字段名称
    public String getFieldname() {
        return fieldname;
    }
    
    public void setFieldname(String fieldname) {
        this.fieldname = fieldname;
    }
        
   // 列头显示
    public String getHeadertext() {
        return headertext;
    }
    
    public void setHeadertext(String headertext) {
        this.headertext = headertext;
    }
        
   // 数据邦定
    public String getDatapropertyname() {
        return datapropertyname;
    }
    
    public void setDatapropertyname(String datapropertyname) {
        this.datapropertyname = datapropertyname;
    }
        
   // 排序字符
    public String getOrderstr() {
        return orderstr;
    }
    
    public void setOrderstr(String orderstr) {
        this.orderstr = orderstr;
    }
        
   // 列宽
    public Integer getColwidth() {
        return colwidth;
    }
    
    public void setColwidth(Integer colwidth) {
        this.colwidth = colwidth;
    }
        
   // 列对齐
    public String getColalign() {
        return colalign;
    }
    
    public void setColalign(String colalign) {
        this.colalign = colalign;
    }
        
   // 显示编号
    public Integer getDisplayno() {
        return displayno;
    }
    
    public void setDisplayno(Integer displayno) {
        this.displayno = displayno;
    }
        
   // 显示状态
    public Integer getDisplaystate() {
        return displaystate;
    }
    
    public void setDisplaystate(Integer displaystate) {
        this.displaystate = displaystate;
    }
        
   // 文本格式
    public String getFormatstring() {
        return formatstring;
    }
    
    public void setFormatstring(String formatstring) {
        this.formatstring = formatstring;
    }
        
   // 默认宽度
    public String getDefwidth() {
        return defwidth;
    }
    
    public void setDefwidth(String defwidth) {
        this.defwidth = defwidth;
    }
        
   // 最小宽度
    public String getMinwidth() {
        return minwidth;
    }
    
    public void setMinwidth(String minwidth) {
        this.minwidth = minwidth;
    }
        
   // 1固定0否
    public Integer getFixed() {
        return fixed;
    }
    
    public void setFixed(Integer fixed) {
        this.fixed = fixed;
    }
        
   // 1可排序
    public Integer getSortable() {
        return sortable;
    }
    
    public void setSortable(Integer sortable) {
        this.sortable = sortable;
    }
        
   // 排序表.字段
    public String getOrderfield() {
        return orderfield;
    }
    
    public void setOrderfield(String orderfield) {
        this.orderfield = orderfield;
    }
        
   // 1溢出隐藏
    public Integer getOverflow() {
        return overflow;
    }
    
    public void setOverflow(Integer overflow) {
        this.overflow = overflow;
    }
        
   // 格式化
    public String getFormatter() {
        return formatter;
    }
    
    public void setFormatter(String formatter) {
        this.formatter = formatter;
    }
        
   // 自定义类
    public String getClassname() {
        return classname;
    }
    
    public void setClassname(String classname) {
        this.classname = classname;
    }
        
   // left/center/right
    public String getAligntype() {
        return aligntype;
    }
    
    public void setAligntype(String aligntype) {
        this.aligntype = aligntype;
    }
        
   // 事件名称
    public String getEventname() {
        return eventname;
    }
    
    public void setEventname(String eventname) {
        this.eventname = eventname;
    }
        
   // 可编辑
    public Integer getEditmark() {
        return editmark;
    }
    
    public void setEditmark(Integer editmark) {
        this.editmark = editmark;
    }
        
   // 可操作
    public Integer getOperationmark() {
        return operationmark;
    }
    
    public void setOperationmark(Integer operationmark) {
        this.operationmark = operationmark;
    }
        
   // 显示位
    public Integer getDisplayindex() {
        return displayindex;
    }
    
    public void setDisplayindex(Integer displayindex) {
        this.displayindex = displayindex;
    }
        
   // RowNum
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
   // 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
   // 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
   // 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
   // 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
   // 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
   // 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

