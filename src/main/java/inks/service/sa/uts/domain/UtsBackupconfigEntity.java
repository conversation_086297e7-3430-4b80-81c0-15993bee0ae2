package inks.service.sa.uts.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 数据库备份配置(UtsBackupconfig)实体类
 *
 * <AUTHOR>
 * @since 2025-04-30 09:22:59
 */
@Data
public class UtsBackupconfigEntity implements Serializable {
    private static final long serialVersionUID = 812181412948166899L;
     // id
    private String id;
     // 配置名称（唯一标识）
    private String configname;
     // 本机数据库备份
    private Integer localmark;
     // 数据库驱动
    private String dbdriver;
     // 数据库连接地址
    private String dburl;
     // 数据库用户名
    private String dbusername;
     // 数据库密码
    private String dbpassword;
     // 存储本地路径(不填则不存储)
    private String localpath;
     // 指定备份的表名
    private String includetables;
     // 排除备份的表名
    private String excludetables;
     // 存储路径前缀,标识客户名
    private String uploadprefix;
     // 解压密码
    private String zippassword;
     // 定时任务cron表达式
    private String cronexpression;
     // 上传接口(不走OSS)
    private String cloudurl;
     // 授权码
    private String authcode;
     // 通知邮箱
    private String email;
     // 有效
    private Integer enabledmark;
     // 存储类型minio/aliyun
    private String osstype;
     // Oss存储桶
    private String ossbucket;
     // Oss Access Key
    private String ossaccesskey;
     // Oss Secret Key
    private String osssecretkey;
     // Oss Endpoint
    private String ossendpoint;
     // 序号
    private Integer rownum;
     // 备注说明
    private String remark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;



}

