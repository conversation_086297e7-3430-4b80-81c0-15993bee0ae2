package inks.service.sa.uts.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 企业微信信息(UtsWxemsg)实体类
 *
 * <AUTHOR>
 * @since 2023-01-10 11:12:38
 */
public class UtsWxemsgEntity implements Serializable {
    private static final long serialVersionUID = -77971847517080493L;
    // id
    private String id;
    // 信息分组
    private String msggroupid;
    // 服务_功能_动作
    private String msgcode;
    // 信息名称
    private String msgname;
    // Text/OA/Crad
    private String msgtype;
    // VM模版
    private String msgtemplate;
    // 功能模块(备用)
    private String modulecode;
    // 用户表
    private String userlist;
    // 部门表
    private String deptlist;
    // 接受对象 ObjTypeCodeNameRowNum
    private String objjson;
    // 行号
    private Integer rownum;
    // 详情Url模版
    private String urltemplate;
    // 有效性
    private Integer enabledmark;
    // 摘要
    private String summary;
    // 创建者
    private String createby;
    // 创建者id
    private String createbyid;
    // 新建日期
    private Date createdate;
    // 制表
    private String lister;
    // 制表id
    private String listerid;
    // 修改日期
    private Date modifydate;
    // 自定义1
    private String custom1;
    // 自定义2
    private String custom2;
    // 自定义3
    private String custom3;
    // 自定义4
    private String custom4;
    // 自定义5
    private String custom5;
    // 租户id
    private String tenantid;
    // 租户名称
    private String tenantname;
    // 乐观锁
    private Integer revision;

    // id
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // 信息分组
    public String getMsggroupid() {
        return msggroupid;
    }

    public void setMsggroupid(String msggroupid) {
        this.msggroupid = msggroupid;
    }

    // 服务_功能_动作
    public String getMsgcode() {
        return msgcode;
    }

    public void setMsgcode(String msgcode) {
        this.msgcode = msgcode;
    }

    // 信息名称
    public String getMsgname() {
        return msgname;
    }

    public void setMsgname(String msgname) {
        this.msgname = msgname;
    }

    // Text/OA/Crad
    public String getMsgtype() {
        return msgtype;
    }

    public void setMsgtype(String msgtype) {
        this.msgtype = msgtype;
    }

    // VM模版
    public String getMsgtemplate() {
        return msgtemplate;
    }

    public void setMsgtemplate(String msgtemplate) {
        this.msgtemplate = msgtemplate;
    }

    // 功能模块(备用)
    public String getModulecode() {
        return modulecode;
    }

    public void setModulecode(String modulecode) {
        this.modulecode = modulecode;
    }

    // 用户表
    public String getUserlist() {
        return userlist;
    }

    public void setUserlist(String userlist) {
        this.userlist = userlist;
    }

    // 部门表
    public String getDeptlist() {
        return deptlist;
    }

    public void setDeptlist(String deptlist) {
        this.deptlist = deptlist;
    }

    // 接受对象 ObjTypeCodeNameRowNum
    public String getObjjson() {
        return objjson;
    }

    public void setObjjson(String objjson) {
        this.objjson = objjson;
    }

    // 行号
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // 详情Url模版
    public String getUrltemplate() {
        return urltemplate;
    }

    public void setUrltemplate(String urltemplate) {
        this.urltemplate = urltemplate;
    }

    // 有效性
    public Integer getEnabledmark() {
        return enabledmark;
    }

    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }

    // 摘要
    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 制表id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 租户名称
    public String getTenantname() {
        return tenantname;
    }

    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


}

