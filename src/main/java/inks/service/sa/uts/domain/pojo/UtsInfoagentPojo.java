package inks.service.sa.uts.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * 信息代理中心(UtsInfoagent)实体类
 *
 * <AUTHOR>
 * @since 2025-02-28 13:12:39
 */
@Data
public class UtsInfoagentPojo implements Serializable {
    private static final long serialVersionUID = 270396533677696954L;
     // id
    @Excel(name = "id") 
    private String id;
     // 信息分组
    @Excel(name = "信息分组") 
    private String msggroupid;
     // 服务_功能_动作
    @Excel(name = "服务_功能_动作") 
    private String msgcode;
     // 信息名称
    @Excel(name = "信息名称") 
    private String msgname;
     // 代理工具(多选)
    @Excel(name = "代理工具(多选)") 
    private String agenttools;
     // 行号
    @Excel(name = "行号") 
    private Integer rownum;
     // 有效性
    @Excel(name = "有效性") 
    private Integer enabledmark;
     // 摘要
    @Excel(name = "摘要") 
    private String remark;
     // 创建者
    @Excel(name = "创建者") 
    private String createby;
     // 创建者id
    @Excel(name = "创建者id") 
    private String createbyid;
     // 新建日期
    @Excel(name = "新建日期") 
    private Date createdate;
     // 制表
    @Excel(name = "制表") 
    private String lister;
     // 制表id
    @Excel(name = "制表id") 
    private String listerid;
     // 修改日期
    @Excel(name = "修改日期") 
    private Date modifydate;
     // 自定义1
    @Excel(name = "自定义1") 
    private String custom1;
     // 自定义2
    @Excel(name = "自定义2") 
    private String custom2;
     // 自定义3
    @Excel(name = "自定义3") 
    private String custom3;
     // 自定义4
    @Excel(name = "自定义4") 
    private String custom4;
     // 自定义5
    @Excel(name = "自定义5") 
    private String custom5;
     // 租户id
    @Excel(name = "租户id") 
    private String tenantid;
     // 租户名称
    @Excel(name = "租户名称") 
    private String tenantname;
     // 乐观锁
    @Excel(name = "乐观锁") 
    private Integer revision;



}

