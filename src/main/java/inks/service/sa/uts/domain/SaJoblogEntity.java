package inks.service.sa.uts.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 定时任务调度日志表(SaJoblog)实体类
 *
 * <AUTHOR>
 * @since 2025-03-07 16:07:35
 */
@Data
public class SaJoblogEntity implements Serializable {
    private static final long serialVersionUID = -89277051463712982L;
     // 主键
    private String id;
     // 任务名称
    private String jobname;
     // 任务组名
    private String jobgroup;
     // 调用目标字符串
    private String invoketarget;
     // 日志信息
    private String jobmessage;
     // 执行状态（0正常 1失败）
    private String status;
     // 异常信息
    private String exceptioninfo;
     // 创建时间
    private Date createtime;



}

