package inks.service.sa.uts.domain.vo;

import me.chanjar.weixin.cp.bean.article.NewArticle;
import me.chanjar.weixin.cp.bean.message.WxCpGroupRobotMessage;

import java.util.List;

/**
 * 企业微信机器人接口消息请求DTO集合
 */
public class WxeRobotMessages {

    public static class TextMessageRequest {
        private String webhookurl;
        private String content;
        private List<String> mentionedlist;
        private List<String> mobilelist;

        public String getWebhookurl() {
            return webhookurl;
        }

        public void setWebhookurl(String webhookurl) {
            this.webhookurl = webhookurl;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public List<String> getMentionedlist() {
            return mentionedlist;
        }

        public void setMentionedlist(List<String> mentionedlist) {
            this.mentionedlist = mentionedlist;
        }

        public List<String> getMobilelist() {
            return mobilelist;
        }

        public void setMobilelist(List<String> mobilelist) {
            this.mobilelist = mobilelist;
        }
    }

    public static class MarkdownMessageRequest {
        private String webhookurl;
        private String content;

        public String getWebhookurl() {
            return webhookurl;
        }

        public void setWebhookurl(String webhookurl) {
            this.webhookurl = webhookurl;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }
    }

    public static class ImageMessageRequest {
        private String webhookurl;
        private String base64;
        private String md5;

        public String getWebhookurl() {
            return webhookurl;
        }

        public void setWebhookurl(String webhookurl) {
            this.webhookurl = webhookurl;
        }

        public String getBase64() {
            return base64;
        }

        public void setBase64(String base64) {
            this.base64 = base64;
        }

        public String getMd5() {
            return md5;
        }

        public void setMd5(String md5) {
            this.md5 = md5;
        }
    }

    public static class NewsMessageRequest {
        private String webhookurl;
        private List<NewArticle> articlelist;

        public String getWebhookurl() {
            return webhookurl;
        }

        public void setWebhookurl(String webhookurl) {
            this.webhookurl = webhookurl;
        }

        public List<NewArticle> getArticlelist() {
            return articlelist;
        }

        public void setArticlelist(List<NewArticle> articlelist) {
            this.articlelist = articlelist;
        }
    }


    public static class FileMessageRequest {
        private String webhookurl;
        private String mediaid;

        public String getWebhookurl() {
            return webhookurl;
        }

        public void setWebhookurl(String webhookurl) {
            this.webhookurl = webhookurl;
        }

        public String getMediaid() {
            return mediaid;
        }

        public void setMediaid(String mediaid) {
            this.mediaid = mediaid;
        }
    }

    public static class VoiceMessageRequest {
        private String webhookurl;
        private String mediaid;

        public String getWebhookurl() {
            return webhookurl;
        }

        public void setWebhookurl(String webhookurl) {
            this.webhookurl = webhookurl;
        }

        public String getMediaid() {
            return mediaid;
        }

        public void setMediaid(String mediaid) {
            this.mediaid = mediaid;
        }
    }

    public static class TemplateCardMessageRequest {
        private String webhookurl;
        private WxCpGroupRobotMessage wxcpgrouprobotmessage;

        public String getWebhookurl() {
            return webhookurl;
        }

        public void setWebhookurl(String webhookurl) {
            this.webhookurl = webhookurl;
        }

        public WxCpGroupRobotMessage getWxcpgrouprobotmessage() {
            return wxcpgrouprobotmessage;
        }

        public void setWxcpgrouprobotmessage(WxCpGroupRobotMessage wxcpgrouprobotmessage) {
            this.wxcpgrouprobotmessage = wxcpgrouprobotmessage;
        }
    }


}
