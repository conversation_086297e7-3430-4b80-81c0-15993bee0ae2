package inks.service.sa.uts.domain.pojo;

import java.io.Serializable;

/**
 * SSH连接信息类
 * 封装连接到SSH服务器所需的全部信息
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
public class SshConnectionInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 服务器ID
     */
    private String serverId;

    /**
     * 服务器名称
     */
    private String serverName;

    /**
     * 主机地址
     */
    private String host;

    /**
     * 端口号
     */
    private int port;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 连接超时时间（毫秒）
     */
    private int connectTimeoutMs = 30000;

    /**
     * 是否使用私钥认证
     */
    private boolean useKeyAuth;

    /**
     * 私钥文件路径（仅当useKeyAuth=true时有效）
     */
    private String privateKeyPath;
    
    /**
     * 私钥密码（如果有）
     */
    private String privateKeyPassphrase;

    // Get<PERSON> and Setters
    public String getServerId() {
        return serverId;
    }

    public void setServerId(String serverId) {
        this.serverId = serverId;
    }

    public String getServerName() {
        return serverName;
    }

    public void setServerName(String serverName) {
        this.serverName = serverName;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public int getConnectTimeoutMs() {
        return connectTimeoutMs;
    }

    public void setConnectTimeoutMs(int connectTimeoutMs) {
        this.connectTimeoutMs = connectTimeoutMs;
    }

    public boolean isUseKeyAuth() {
        return useKeyAuth;
    }

    public void setUseKeyAuth(boolean useKeyAuth) {
        this.useKeyAuth = useKeyAuth;
    }

    public String getPrivateKeyPath() {
        return privateKeyPath;
    }

    public void setPrivateKeyPath(String privateKeyPath) {
        this.privateKeyPath = privateKeyPath;
    }

    public String getPrivateKeyPassphrase() {
        return privateKeyPassphrase;
    }

    public void setPrivateKeyPassphrase(String privateKeyPassphrase) {
        this.privateKeyPassphrase = privateKeyPassphrase;
    }
}
