package inks.service.sa.uts.domain.dingSchedulePojos;

import java.util.Date;

public class ScheduleTimePojo {
    //日期 格式yyyy-MM-dd 当为全天日程时必须设置，非全天日程是必须留空
    private Date data;
    //格式为 yyyy-MM-ddThh:mm:ss+08:00  非全天日程时必须设置，全天日程是必须留空
    private Date dateTime;
    //时区
    private String timeZone;

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }

    public Date getDateTime() {
        return dateTime;
    }

    public void setDateTime(Date dateTime) {
        this.dateTime = dateTime;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }
}
