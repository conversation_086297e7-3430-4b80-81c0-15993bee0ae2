package inks.service.sa.uts.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * API整合转发(UtsIntegration)实体类
 *
 * <AUTHOR>
 * @since 2025-06-21 15:01:02
 */
@Data
public class UtsIntegrationEntity implements Serializable {
    private static final long serialVersionUID = 150929084664109453L;
     // 主键ID
    private String id;
     // 集成编码
    private String intecode;
     // 集成名称
    private String intename;
     // 代理类型0PageList,1PageTh,2Entity,3BillEntity,4BillList,5Update
    private Integer proxytype;
     // 目标API地址
    private String apiurl;
     // 请求方法(GET、POST等)
    private String reqmethod;
     // 请求参数
    private String reqparam;
     // 请求体
    private String reqbody;
     // 返回格式转换配置
    private String respformat;
     // 鉴权方式 0:authcode 1:用户名密码 
    private Integer authtype;
     // 授权码
    private String authcode;
     // 用户名
    private String authname;
     // 密钥
    private String authsecret;
     // 行号
    private Integer rownum;
     // 备注
    private String remark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;



}

