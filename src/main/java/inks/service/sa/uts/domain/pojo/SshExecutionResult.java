package inks.service.sa.uts.domain.pojo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * SSH流水线执行结果类
 * 封装整个流水线的执行结果，包含多个命令执行的结果集合
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
public class SshExecutionResult implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 流水线ID
     */
    private String pipelineId;

    /**
     * 流水线名称
     */
    private String pipelineName;

    /**
     * 服务器ID
     */
    private String serverId;

    /**
     * 服务器名称
     */
    private String serverName;

    /**
     * 执行状态 (Running/Completed/Failed/Cancelled)
     */
    private String status;

    /**
     * 开始执行时间
     */
    private Date startTime;

    /**
     * 结束执行时间
     */
    private Date endTime;

    /**
     * 执行耗时(毫秒)
     */
    private long durationMs;

    /**
     * 总步骤数
     */
    private int totalSteps;

    /**
     * 已完成的步骤数
     */
    private int completedSteps;

    /**
     * 成功步骤数
     */
    private int successSteps;

    /**
     * 失败步骤数
     */
    private int failedSteps;

    /**
     * 当前执行的步骤索引
     */
    private int currentStepIndex;

    /**
     * 当前执行的步骤名称
     */
    private String currentStepName;

    /**
     * 历史记录ID（用于关联数据库记录）
     */
    private String historyId;

    /**
     * 各步骤执行结果列表
     */
    private List<SshCommandResult> stepResults = new ArrayList<>();

    // Constructors
    public SshExecutionResult() {
        this.startTime = new Date();
        this.status = "Running";
    }

    // Getters and Setters
    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getPipelineId() {
        return pipelineId;
    }

    public void setPipelineId(String pipelineId) {
        this.pipelineId = pipelineId;
    }

    public String getPipelineName() {
        return pipelineName;
    }

    public void setPipelineName(String pipelineName) {
        this.pipelineName = pipelineName;
    }

    public String getServerId() {
        return serverId;
    }

    public void setServerId(String serverId) {
        this.serverId = serverId;
    }

    public String getServerName() {
        return serverName;
    }

    public void setServerName(String serverName) {
        this.serverName = serverName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
        if (startTime != null && endTime != null) {
            this.durationMs = endTime.getTime() - startTime.getTime();
        }
    }

    public long getDurationMs() {
        return durationMs;
    }

    public void setDurationMs(long durationMs) {
        this.durationMs = durationMs;
    }

    public int getTotalSteps() {
        return totalSteps;
    }

    public void setTotalSteps(int totalSteps) {
        this.totalSteps = totalSteps;
    }

    public int getCompletedSteps() {
        return completedSteps;
    }

    public void setCompletedSteps(int completedSteps) {
        this.completedSteps = completedSteps;
    }

    public int getSuccessSteps() {
        return successSteps;
    }

    public void setSuccessSteps(int successSteps) {
        this.successSteps = successSteps;
    }

    public int getFailedSteps() {
        return failedSteps;
    }

    public void setFailedSteps(int failedSteps) {
        this.failedSteps = failedSteps;
    }

    public int getCurrentStepIndex() {
        return currentStepIndex;
    }

    public void setCurrentStepIndex(int currentStepIndex) {
        this.currentStepIndex = currentStepIndex;
    }

    public String getCurrentStepName() {
        return currentStepName;
    }

    public void setCurrentStepName(String currentStepName) {
        this.currentStepName = currentStepName;
    }

    public String getHistoryId() {
        return historyId;
    }

    public void setHistoryId(String historyId) {
        this.historyId = historyId;
    }

    public List<SshCommandResult> getStepResults() {
        return stepResults;
    }

    public void setStepResults(List<SshCommandResult> stepResults) {
        this.stepResults = stepResults;
    }

    /**
     * 添加一个步骤执行结果
     * @param result 步骤执行结果
     */
    public void addStepResult(SshCommandResult result) {
        if (this.stepResults == null) {
            this.stepResults = new ArrayList<>();
        }
        this.stepResults.add(result);
        
        // 更新统计信息
        this.completedSteps = this.stepResults.size();
        
        // 计算成功和失败的步骤数（排除系统日志）
        int success = 0;
        int failed = 0;
        for (SshCommandResult r : this.stepResults) {
            // 排除系统日志
            if (r.getIsSystemLog() != null && r.getIsSystemLog()) {
                continue;
            }
            if ("Success".equals(r.getStatus())) {
                success++;
            } else if ("Failed".equals(r.getStatus()) || "Timeout".equals(r.getStatus())) {
                failed++;
            }
        }
        
        this.successSteps = success;
        this.failedSteps = failed;
    }

    /**
     * 完成整个流水线执行
     */
    public void complete() {
        this.endTime = new Date();
        this.durationMs = this.endTime.getTime() - this.startTime.getTime();
        
        // 根据失败的步骤数确定最终状态
        if (this.failedSteps > 0) {
            this.status = "Failed";
        } else {
            this.status = "Completed";
        }
    }
    
    /**
     * 取消流水线执行
     */
    public void cancel() {
        this.endTime = new Date();
        this.durationMs = this.endTime.getTime() - this.startTime.getTime();
        this.status = "Cancelled";
    }
    
    /**
     * 获取执行进度百分比
     * @return 进度百分比(0-100)
     */
    public int getProgressPercentage() {
        if (totalSteps == 0) return 0;
        return (int) (((double) completedSteps / totalSteps) * 100);
    }
    
    /**
     * 添加日志信息
     * 创建一个新的SshCommandResult作为日志消息，但不计入步骤统计
     * @param message 日志消息
     */
    public void addLog(String message) {
        // 创建系统日志，但不添加到stepResults中，避免影响步骤计数
        // 系统日志应该有单独的存储机制
        if (this.logs == null) {
            this.logs = new ArrayList<>();
        }
        this.logs.add(String.format("[%s] %s",
                new java.text.SimpleDateFormat("HH:mm:ss").format(new Date()),
                message));
    }

    // 添加日志列表字段
    private List<String> logs;

    public List<String> getLogs() {
        return logs;
    }

    public void setLogs(List<String> logs) {
        this.logs = logs;
    }
}
