package inks.service.sa.uts.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * 企业微审核(UtsWxeappr)实体类
 *
 * <AUTHOR>
 * @since 2024-12-16 14:12:14
 */
@Data
public class UtsWxeapprPojo implements Serializable {
    private static final long serialVersionUID = -94433873799655431L;
     // id
    @Excel(name = "id") 
    private String id;
     // 模块编码
    @Excel(name = "模块编码") 
    private String modulecode;
     // 模版id
    @Excel(name = "模版id") 
    private String templateid;
     // 审批名称
    @Excel(name = "审批名称") 
    private String apprname;
     // 数据模版
    @Excel(name = "数据模版") 
    private String datatemp;
     // 回调Url
    @Excel(name = "回调Url") 
    private String callbackurl;
     // 回调Bean
    @Excel(name = "回调Bean") 
    private String callbackbean;
     // 执行条件
    @Excel(name = "执行条件") 
    private String resultcode;
     // 类型(备用)
    @Excel(name = "类型(备用)") 
    private String apprtype;
     // 序号
    @Excel(name = "序号") 
    private Integer rownum;
     // 审批申请测试数据
    @Excel(name = "审批申请测试数据") 
    private String testdata;
     // 有效标识
    @Excel(name = "有效标识") 
    private Integer enabledmark;
     // 备注
    @Excel(name = "备注") 
    private String remark;
     // 创建者
    @Excel(name = "创建者") 
    private String createby;
     // 创建者id
    @Excel(name = "创建者id") 
    private String createbyid;
     // 新建日期
    @Excel(name = "新建日期") 
    private Date createdate;
     // 制表
    @Excel(name = "制表") 
    private String lister;
     // 制表id
    @Excel(name = "制表id") 
    private String listerid;
     // 修改日期
    @Excel(name = "修改日期") 
    private Date modifydate;
     // 自定义1
    @Excel(name = "自定义1") 
    private String custom1;
     // 自定义2
    @Excel(name = "自定义2") 
    private String custom2;
     // 自定义3
    @Excel(name = "自定义3") 
    private String custom3;
     // 自定义4
    @Excel(name = "自定义4") 
    private String custom4;
     // 自定义5
    @Excel(name = "自定义5") 
    private String custom5;
     // 租户id
    @Excel(name = "租户id") 
    private String tenantid;
     // 租户名称
    @Excel(name = "租户名称") 
    private String tenantname;
     // 乐观锁
    @Excel(name = "乐观锁") 
    private Integer revision;



}

