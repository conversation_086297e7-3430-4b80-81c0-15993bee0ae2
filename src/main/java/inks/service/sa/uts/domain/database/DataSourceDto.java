package inks.service.sa.uts.domain.database;

import java.io.Serializable;
import java.util.Map;


/**
 * <AUTHOR>
 * @description 数据源 dto
 * @date 2021-03-18 12:09:57.728203200
 **/
public class DataSourceDto implements Serializable {
    private String id;
    /**
     * 数据源编码
     */
    private String sourcecode;

    /**
     * 数据源名称
     */
    private String sourcename;

    /**
     * 数据源描述
     */
    private String sourcedesc;

    /**
     * 数据源类型 DIC_NAME=SOURCE_TYPE; mysql，orace，sqlserver，elasticsearch，接口，javaBean，数据源类型字典中item-extend动态生成表单
     */
    private String sourcetype;

    /**
     * 数据源连接配置json：关系库{ jdbcUrl:'', username:'', password:'','driverName':''}ES-sql{ apiUrl:'http://127.0.0.1:9092/_xpack/sql?format=json','method':'POST','body':'{"query":"select 1"}' }  接口{ apiUrl:'http://ip:port/url', method:'' } javaBean{ beanNamw:'xxx' }
     */
    private String sourceconfig;

    /**
     * 0--已禁用 1--已启用  DIC_NAME=ENABLE_FLAG
     */
    private Integer enableflag;

    /**
     * 0--未删除 1--已删除 DIC_NAME=DELETE_FLAG
     */
    private Integer deleteflag;

    /**************************************************************/
    /**
     * 关系型数据库jdbcUrl
     */
    private String jdbcUrl;

    /**
     * 关系型数据库用户名
     */
    private String username;

    /**
     * 关系型数据库密码
     */
    private String password;

    /**
     * 关系型数据库驱动类
     */
    private String driverName;

    /**
     * 关系型数据库sql
     */
    private String sql;

    /**
     * http requestUrl
     */
    private String apiUrl;

    /**
     * http method
     */
    private String method;

    /**
     * http header
     */
    private String header;

    /**
     * http 请求体
     */
    private String body;

    /**
     * 动态查询sql或者接口中的请求体
     */
    private String dynSentence;

    /**
     * 传入的自定义参数，解决url中存在的动态参数
     */
    private Map<String, Object> contextData;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSourcecode() {
        return sourcecode;
    }

    public void setSourcecode(String sourcecode) {
        this.sourcecode = sourcecode;
    }

    public String getSourcename() {
        return sourcename;
    }

    public void setSourcename(String sourcename) {
        this.sourcename = sourcename;
    }

    public String getSourcedesc() {
        return sourcedesc;
    }

    public void setSourcedesc(String sourcedesc) {
        this.sourcedesc = sourcedesc;
    }

    public String getSourcetype() {
        return sourcetype;
    }

    public void setSourcetype(String sourcetype) {
        this.sourcetype = sourcetype;
    }

    public String getSourceconfig() {
        return sourceconfig;
    }

    public void setSourceconfig(String sourceconfig) {
        this.sourceconfig = sourceconfig;
    }

    public Integer getEnableflag() {
        return enableflag;
    }

    public void setEnableflag(Integer enableflag) {
        this.enableflag = enableflag;
    }

    public Integer getDeleteflag() {
        return deleteflag;
    }

    public void setDeleteflag(Integer deleteflag) {
        this.deleteflag = deleteflag;
    }

    public String getJdbcUrl() {
        return jdbcUrl;
    }

    public void setJdbcUrl(String jdbcUrl) {
        this.jdbcUrl = jdbcUrl;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getDriverName() {
        return driverName;
    }

    public void setDriverName(String driverName) {
        this.driverName = driverName;
    }

    public String getSql() {
        return sql;
    }

    public void setSql(String sql) {
        this.sql = sql;
    }

    public String getApiUrl() {
        return apiUrl;
    }

    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getHeader() {
        return header;
    }

    public void setHeader(String header) {
        this.header = header;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public String getDynSentence() {
        return dynSentence;
    }

    public void setDynSentence(String dynSentence) {
        this.dynSentence = dynSentence;
    }

    public Map<String, Object> getContextData() {
        return contextData;
    }

    public void setContextData(Map<String, Object> contextData) {
        this.contextData = contextData;
    }
}
