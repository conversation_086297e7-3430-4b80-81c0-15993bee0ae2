package inks.service.sa.uts.domain.dingSchedulePojos;

import java.util.List;

public class ScheduleParamPojo {
    //unionid
    private String userId;
    //日历id
    private String calendarId;
    //日程标题 <=2048字符
    private String summary;
    //描述   <=5000字符
    private String description;
    //开始时间
    private ScheduleTimePojo start;
    //结束时间
    private ScheduleTimePojo end;
    //是否全天日程
    private Boolean isAllDay;
    //日程循环规则
    private ScheduleRecurrencePojo recurrence;
    //日程参与人员列表，最多500人
    private List<ScheduleAttendeePojo> attendees;
    //日程地点
    private ScheduleLocationPojo location;
    //日程提醒，可以添加多个，默认非全天日程：开始前15分钟提醒。全天日程开始前一天9点提醒
    private List<ScheduleReminderPojo> reminders;
    //创建日程时创建线上会议
    private ScheduleOnlineMeetingInfoPojo onlineMeetingInfo;
    //JSON格式的扩展能力开关，选填， onPushNotification:创建日程时是否向关联人员发送钉钉推送    true-不发送 false-发送
    //onChatNotification:创建日程是是否向参与人员发送单聊卡片 true-不发送， false-发送
    private String extra;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getCalendarId() {
        return calendarId;
    }

    public void setCalendarId(String calendarId) {
        this.calendarId = calendarId;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public ScheduleTimePojo getStart() {
        return start;
    }

    public void setStart(ScheduleTimePojo start) {
        this.start = start;
    }

    public ScheduleTimePojo getEnd() {
        return end;
    }

    public void setEnd(ScheduleTimePojo end) {
        this.end = end;
    }

    public Boolean getAllDay() {
        return isAllDay;
    }

    public void setAllDay(Boolean allDay) {
        isAllDay = allDay;
    }

    public ScheduleRecurrencePojo getRecurrence() {
        return recurrence;
    }

    public void setRecurrence(ScheduleRecurrencePojo recurrence) {
        this.recurrence = recurrence;
    }

    public List<ScheduleAttendeePojo> getAttendees() {
        return attendees;
    }

    public void setAttendees(List<ScheduleAttendeePojo> attendees) {
        this.attendees = attendees;
    }

    public ScheduleLocationPojo getLocation() {
        return location;
    }

    public void setLocation(ScheduleLocationPojo location) {
        this.location = location;
    }

    public List<ScheduleReminderPojo> getReminders() {
        return reminders;
    }

    public void setReminders(List<ScheduleReminderPojo> reminders) {
        this.reminders = reminders;
    }

    public ScheduleOnlineMeetingInfoPojo getOnlineMeetingInfo() {
        return onlineMeetingInfo;
    }

    public void setOnlineMeetingInfo(ScheduleOnlineMeetingInfoPojo onlineMeetingInfo) {
        this.onlineMeetingInfo = onlineMeetingInfo;
    }

    public String getExtra() {
        return extra;
    }

    public void setExtra(String extra) {
        this.extra = extra;
    }
}
