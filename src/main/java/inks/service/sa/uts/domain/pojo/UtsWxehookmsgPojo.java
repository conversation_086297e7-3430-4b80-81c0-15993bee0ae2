package inks.service.sa.uts.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * 企业微信群机器人信息(UtsWxehookmsg)实体类
 *
 * <AUTHOR>
 * @since 2025-02-28 13:12:38
 */
@Data
public class UtsWxehookmsgPojo implements Serializable {
    private static final long serialVersionUID = -92931910844565558L;
     // id
    @Excel(name = "id") 
    private String id;
     // 信息分组
    @Excel(name = "信息分组") 
    private String msggroupid;
     // 服务_功能_动作
    @Excel(name = "服务_功能_动作") 
    private String msgcode;
     // 信息名称
    @Excel(name = "信息名称") 
    private String msgname;
     // Text/OA/Crad
    @Excel(name = "Text/OA/Crad") 
    private String msgtype;
     // VM模版
    @Excel(name = "VM模版") 
    private String msgtemplate;
     // 功能模块(备用)
    @Excel(name = "功能模块(备用)") 
    private String modulecode;
     // 群机器人Webhook列表
    @Excel(name = "群机器人Webhook列表") 
    private String webhooklist;
     // @用户列表
    @Excel(name = "@用户列表") 
    private String userlist;
     // 部门表
    @Excel(name = "部门表") 
    private String deptlist;
     // 接受对象 ObjTypeCodeNameRowNum
    @Excel(name = "接受对象 ObjTypeCodeNameRowNum") 
    private String objjson;
     // 行号
    @Excel(name = "行号") 
    private Integer rownum;
     // 详情Url模版
    @Excel(name = "详情Url模版") 
    private String urltemplate;
     // 有效性
    @Excel(name = "有效性") 
    private Integer enabledmark;
     // 摘要
    @Excel(name = "摘要") 
    private String remark;
     // 创建者
    @Excel(name = "创建者") 
    private String createby;
     // 创建者id
    @Excel(name = "创建者id") 
    private String createbyid;
     // 新建日期
    @Excel(name = "新建日期") 
    private Date createdate;
     // 制表
    @Excel(name = "制表") 
    private String lister;
     // 制表id
    @Excel(name = "制表id") 
    private String listerid;
     // 修改日期
    @Excel(name = "修改日期") 
    private Date modifydate;
     // 自定义1
    @Excel(name = "自定义1") 
    private String custom1;
     // 自定义2
    @Excel(name = "自定义2") 
    private String custom2;
     // 自定义3
    @Excel(name = "自定义3") 
    private String custom3;
     // 自定义4
    @Excel(name = "自定义4") 
    private String custom4;
     // 自定义5
    @Excel(name = "自定义5") 
    private String custom5;
     // 租户id
    @Excel(name = "租户id") 
    private String tenantid;
     // 租户名称
    @Excel(name = "租户名称") 
    private String tenantname;
     // 乐观锁
    @Excel(name = "乐观锁") 
    private Integer revision;



}

