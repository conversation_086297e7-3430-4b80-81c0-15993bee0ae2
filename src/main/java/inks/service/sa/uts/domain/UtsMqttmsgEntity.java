package inks.service.sa.uts.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * MQTT信息(UtsMqttmsg)实体类
 *
 * <AUTHOR>
 * @since 2023-10-11 14:32:09
 */
public class UtsMqttmsgEntity implements Serializable {
    private static final long serialVersionUID = 326841738153484057L;
    // id
    private String id;
    // 信息分组
    private String msggroupid;
    // 服务_功能_动作
    private String msgcode;
    // 信息名称
    private String msgname;
    // Text/OA/Crad
    private String msgtype;
    // VM模版
    private String msgtemplate;
    // 图标0info1success2warning3error
    private Integer msgicon;
    // 功能模块(备用)
    private String modulecode;
    // 持续时间
    private Integer duration;
    // 用户表
    private String useridlist;
    // 用户/部门信息json
    private String itemjson;
    // 部门表
    private String deptidlist;
    // To全员
    private Integer toalluser;
    // 行号
    private Integer rownum;
    // 有效性
    private Integer enabledmark;
    // 消息显示的位置
    private String position;
    // 摘要
    private String remark;
    // 创建者
    private String createby;
    // 创建者id
    private String createbyid;
    // 新建日期
    private Date createdate;
    // 制表
    private String lister;
    // 制表id
    private String listerid;
    // 修改日期
    private Date modifydate;
    // 自定义1
    private String custom1;
    // 自定义2
    private String custom2;
    // 自定义3
    private String custom3;
    // 自定义4
    private String custom4;
    // 自定义5
    private String custom5;
    // 租户id
    private String tenantid;
    // 租户名称
    private String tenantname;
    // 乐观锁
    private Integer revision;

    // id
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // 信息分组
    public String getMsggroupid() {
        return msggroupid;
    }

    public void setMsggroupid(String msggroupid) {
        this.msggroupid = msggroupid;
    }

    // 服务_功能_动作
    public String getMsgcode() {
        return msgcode;
    }

    public void setMsgcode(String msgcode) {
        this.msgcode = msgcode;
    }

    // 信息名称
    public String getMsgname() {
        return msgname;
    }

    public void setMsgname(String msgname) {
        this.msgname = msgname;
    }

    // Text/OA/Crad
    public String getMsgtype() {
        return msgtype;
    }

    public void setMsgtype(String msgtype) {
        this.msgtype = msgtype;
    }

    // VM模版
    public String getMsgtemplate() {
        return msgtemplate;
    }

    public void setMsgtemplate(String msgtemplate) {
        this.msgtemplate = msgtemplate;
    }

    // 图标0info1success2warning3error
    public Integer getMsgicon() {
        return msgicon;
    }

    public void setMsgicon(Integer msgicon) {
        this.msgicon = msgicon;
    }

    // 功能模块(备用)
    public String getModulecode() {
        return modulecode;
    }

    public void setModulecode(String modulecode) {
        this.modulecode = modulecode;
    }

    // 持续时间
    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    // 用户表
    public String getUseridlist() {
        return useridlist;
    }

    public void setUseridlist(String useridlist) {
        this.useridlist = useridlist;
    }

    // 用户/部门信息json
    public String getItemjson() {
        return itemjson;
    }

    public void setItemjson(String itemjson) {
        this.itemjson = itemjson;
    }

    // 部门表
    public String getDeptidlist() {
        return deptidlist;
    }

    public void setDeptidlist(String deptidlist) {
        this.deptidlist = deptidlist;
    }

    // To全员
    public Integer getToalluser() {
        return toalluser;
    }

    public void setToalluser(Integer toalluser) {
        this.toalluser = toalluser;
    }

    // 行号
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // 有效性
    public Integer getEnabledmark() {
        return enabledmark;
    }

    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }

    // 消息显示的位置
    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    // 摘要
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 制表id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 租户名称
    public String getTenantname() {
        return tenantname;
    }

    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


}

