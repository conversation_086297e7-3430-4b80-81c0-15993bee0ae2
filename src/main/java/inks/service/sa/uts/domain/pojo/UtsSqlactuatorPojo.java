package inks.service.sa.uts.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * SQL执行器(UtsSqlactuator)实体类
 *
 * <AUTHOR>
 * @since 2024-10-06 16:23:35
 */
@Data
public class UtsSqlactuatorPojo implements Serializable {
    private static final long serialVersionUID = -22171319425273240L;
     // id
    @Excel(name = "id") 
    private String id;
     // sql名称
    @Excel(name = "sql名称") 
    private String sqlname;
     // SQL语句
    @Excel(name = "SQL语句") 
    private String sqldata;
     // 功能模块
    @Excel(name = "功能模块") 
    private String modulename;
     // 版本
    @Excel(name = "版本") 
    private String version;
     // 数据库连接id
    @Excel(name = "数据库连接id")
    private String databaseid;
     // 返回
    @Excel(name = "返回") 
    private String resultjson;
     // 业务类型（0列表 1新增 2修改 3删除）
    @Excel(name = "业务类型（0列表 1新增 2修改 3删除）") 
    private Integer bustype;
     // 操作状态（0正常 1异常）
    @Excel(name = "操作状态（0正常 1异常）") 
    private Integer statusnum;
     // 所属系统编码
    @Excel(name = "所属系统编码")
    private String code;
     // Sql变更的时间戳
    @Excel(name = "Sql变更的时间戳")
    private Long sqlecntimestamp;
     // 备注
    @Excel(name = "备注") 
    private String remark;
     // 创建者
    @Excel(name = "创建者") 
    private String createby;
     // 创建者id
    @Excel(name = "创建者id") 
    private String createbyid;
     // 新建日期
    @Excel(name = "新建日期") 
    private Date createdate;
     // 制表
    @Excel(name = "制表") 
    private String lister;
     // 制表id
    @Excel(name = "制表id") 
    private String listerid;
     // 修改日期
    @Excel(name = "修改日期") 
    private Date modifydate;
     // 自定义1
    @Excel(name = "自定义1") 
    private String custom1;
     // 自定义2
    @Excel(name = "自定义2") 
    private String custom2;
     // 自定义3
    @Excel(name = "自定义3") 
    private String custom3;
     // 自定义4
    @Excel(name = "自定义4") 
    private String custom4;
     // 自定义5
    @Excel(name = "自定义5") 
    private String custom5;
     // 租户id
    @Excel(name = "租户id") 
    private String tenantid;
     // 租户名称
    @Excel(name = "租户名称") 
    private String tenantname;
     // 乐观锁
    @Excel(name = "乐观锁") 
    private Integer revision;



}

