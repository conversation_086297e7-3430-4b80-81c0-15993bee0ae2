package inks.service.sa.uts.domain.other;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * (SaConfig)实体类
 *
 * <AUTHOR>
 * @since 2023-01-30 16:36:58
 */
public class SaConfigPojo implements Serializable {
    private static final long serialVersionUID = 385608699564816985L;
    @Excel(name = "")
    private String id;
    // 父级主键
    @Excel(name = "父级主键")
    private String parentid;
    // 参数名称
    @Excel(name = "参数名称")
    private String cfgname;
    // 模块.key
    @Excel(name = "模块.key")
    private String cfgkey;
    // value
    @Excel(name = "value")
    private String cfgvalue;
    // 0System/1Module
    @Excel(name = "0System/1Module")
    private Integer cfgtype;
    // 0平台/1租户/2用户
    @Excel(name = "0平台/1租户/2用户")
    private Integer cfglevel;
    // 控件类型 0文本1数字2下拉框3开关
    @Excel(name = "控件类型 0文本1数字2下拉框3开关")
    private Integer ctrltype;
    // 可选值
    @Excel(name = "可选值")
    private String cfgoption;
    // 图标
    @Excel(name = "图标")
    private String cfgicon;
    // 允许前端应用1
    @Excel(name = "允许前端应用1")
    private Integer allowui;
    // 行号
    @Excel(name = "行号")
    private Integer rownum;
    // 有效性1
    @Excel(name = "有效性1")
    private Integer enabledmark;
    // 允许删除1
    @Excel(name = "允许删除1")
    private Integer allowdelete;
    // 摘要
    @Excel(name = "摘要")
    private String remark;
    // 创建者
    @Excel(name = "创建者")
    private String createby;
    // 创建者id
    @Excel(name = "创建者id")
    private String createbyid;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 制表id
    @Excel(name = "制表id")
    private String listerid;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 用户id
    @Excel(name = "用户id")
    private String userid;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;
    // 自定义6
    @Excel(name = "自定义6")
    private String custom6;
    // 自定义7
    @Excel(name = "自定义7")
    private String custom7;
    // 自定义8
    @Excel(name = "自定义8")
    private String custom8;
    // 自定义9
    @Excel(name = "自定义9")
    private String custom9;
    // 自定义10
    @Excel(name = "自定义10")
    private String custom10;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // 父级主键
    public String getParentid() {
        return parentid;
    }

    public void setParentid(String parentid) {
        this.parentid = parentid;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    // 参数名称
    public String getCfgname() {
        return cfgname;
    }

    public void setCfgname(String cfgname) {
        this.cfgname = cfgname;
    }

    // 模块.key
    public String getCfgkey() {
        return cfgkey;
    }

    public void setCfgkey(String cfgkey) {
        this.cfgkey = cfgkey;
    }

    // value
    public String getCfgvalue() {
        return cfgvalue;
    }

    public void setCfgvalue(String cfgvalue) {
        this.cfgvalue = cfgvalue;
    }

    // 0System/1Module
    public Integer getCfgtype() {
        return cfgtype;
    }

    public void setCfgtype(Integer cfgtype) {
        this.cfgtype = cfgtype;
    }

    // 0平台/1租户/2用户
    public Integer getCfglevel() {
        return cfglevel;
    }

    public void setCfglevel(Integer cfglevel) {
        this.cfglevel = cfglevel;
    }

    // 控件类型 0文本1数字2下拉框3开关
    public Integer getCtrltype() {
        return ctrltype;
    }

    public void setCtrltype(Integer ctrltype) {
        this.ctrltype = ctrltype;
    }

    // 可选值
    public String getCfgoption() {
        return cfgoption;
    }

    public void setCfgoption(String cfgoption) {
        this.cfgoption = cfgoption;
    }

    // 图标
    public String getCfgicon() {
        return cfgicon;
    }

    public void setCfgicon(String cfgicon) {
        this.cfgicon = cfgicon;
    }

    // 允许前端应用1
    public Integer getAllowui() {
        return allowui;
    }

    public void setAllowui(Integer allowui) {
        this.allowui = allowui;
    }

    // 行号
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // 有效性1
    public Integer getEnabledmark() {
        return enabledmark;
    }

    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }

    // 允许删除1
    public Integer getAllowdelete() {
        return allowdelete;
    }

    public void setAllowdelete(Integer allowdelete) {
        this.allowdelete = allowdelete;
    }

    // 摘要
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 制表id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

    // 自定义6
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    // 自定义7
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }

    // 自定义8
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }

    // 自定义9
    public String getCustom9() {
        return custom9;
    }

    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }

    // 自定义10
    public String getCustom10() {
        return custom10;
    }

    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }


}

