package inks.service.sa.uts.domain.pojo;

import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * SSH流水线步骤(SaSshpipelinesitem)Pojo
 *
 * <AUTHOR>
 * @since 2025-05-19 11:11:44
 */
@Data
public class SaSshpipelinesitemPojo implements Serializable {
    private static final long serialVersionUID = 478288085714647190L;
     // id
  @Excel(name = "id")    
  private String id;
     // Pid
  @Excel(name = "Pid")    
  private String pid;
     // 步骤名称
  @Excel(name = "步骤名称")    
  private String stepname;
     // 执行命令
  @Excel(name = "执行命令")    
  private String commandtext;
     // 成功匹配正则
  @Excel(name = "成功匹配正则")    
  private String successpattern;
     // 失败匹配正则
  @Excel(name = "失败匹配正则")    
  private String errorpattern;
     // 超时时间（毫秒）
  @Excel(name = "超时时间（毫秒）")    
  private Integer timeoutms;
     // 是否出错继续
  @Excel(name = "是否出错继续")    
  private Integer continueonerror;
     // 失败重试次数
  @Excel(name = "失败重试次数")    
  private Integer retrycount;
     // 行号
  @Excel(name = "行号")    
  private Integer rownum;
     // 备注
  @Excel(name = "备注")    
  private String remark;
     // 自定义1
  @Excel(name = "自定义1")    
  private String custom1;
     // 自定义2
  @Excel(name = "自定义2")    
  private String custom2;
     // 自定义3
  @Excel(name = "自定义3")    
  private String custom3;
     // 自定义4
  @Excel(name = "自定义4")    
  private String custom4;
     // 自定义5
  @Excel(name = "自定义5")    
  private String custom5;
     // 租户id
  @Excel(name = "租户id")    
  private String tenantid;
     // 乐观锁
  @Excel(name = "乐观锁")    
  private Integer revision;


}

