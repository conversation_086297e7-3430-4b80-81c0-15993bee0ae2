package inks.service.sa.uts.domain;

import java.io.Serializable;
import lombok.Data;

/**
 * SSH流水线步骤(SaSshpipelinesitem)Entity
 *
 * <AUTHOR>
 * @since 2025-05-19 11:11:44
 */
@Data
public class SaSshpipelinesitemEntity implements Serializable {
    private static final long serialVersionUID = -45621617288402009L;
     // id
    private String id;
     // Pid
    private String pid;
     // 步骤名称
    private String stepname;
     // 执行命令
    private String commandtext;
     // 成功匹配正则
    private String successpattern;
     // 失败匹配正则
    private String errorpattern;
     // 超时时间（毫秒）
    private Integer timeoutms;
     // 是否出错继续
    private Integer continueonerror;
     // 失败重试次数
    private Integer retrycount;
     // 行号
    private Integer rownum;
     // 备注
    private String remark;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 租户id
    private String tenantid;
     // 乐观锁
    private Integer revision;


}

