package inks.service.sa.uts.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * SSH流水线(SaSshpipelines)实体类
 *
 * <AUTHOR>
 * @since 2025-05-19 11:11:27
 */
@Data
public class SaSshpipelinesEntity implements Serializable {
    private static final long serialVersionUID = -56499245074883987L;
     // id
    private String id;
     // 流水线名称
    private String pipelinename;
     // 描述
    private String description;
     // 分类：系统安装、安全配置、应用部署等
    private String category;
     // 是否系统性（1为系统机制，禁止删除）
    private Integer issystem;
     // 行号
    private Integer rownum;
     // 备注
    private String remark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 租户id
    private String tenantid;
     // 乐观锁
    private Integer revision;


}

