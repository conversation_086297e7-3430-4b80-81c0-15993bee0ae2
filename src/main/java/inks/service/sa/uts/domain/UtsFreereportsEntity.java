package inks.service.sa.uts.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 自由报表(UtsFreereports)实体类
 *
 * <AUTHOR>
 * @since 2024-09-27 12:43:02
 */
public class UtsFreereportsEntity implements Serializable {
    private static final long serialVersionUID = 901763744765187336L;
     // id
    private String id;
     // 报表Type
    private String frtype;
     // 报表分组id
    private String frgroupid;
     // 本地数据库
    private Integer localmark;
     // 报表编码
    private String reportcode;
     // 报表名称
    private String reportname;
     // 动态查询类型sql、http
    private String dyntype;
     // 动态查询sql或者接口中的请求体
    private String dynsentence;
     // 拼接的完整SQL
    private String sqlfull;
     // 查询字段
    private String sqlselect;
     // 表名join
    private String sqlfrom;
     // 查询条件
    private String sqlwhere;
     // 分组操作
    private String sqlgroupby;
     // 聚合筛选
    private String sqlhaving;
     // 排序
    private String sqlorderby;
     // 限制输出
    private String sqllimit;
     // 主表
    private String maintable;
     // 有效性
    private Integer enabledmark;
     // 公共报表
    private Integer publicmark;
     // 数据库连接id
    private String databaseid;
     //  Userid来源:0本地,1rms
    private Integer domainnum;
     // 所属用户id
    private String userid;
     // 图标
    private String imageindex;
     // 许可编码
    private String permissioncode;
     // 图标样式
    private String imagestyle;
     // 提取数据的路径
    private String datepath;
     // authcode授权码
    private String authcode;
     // 结果案例
    private String caseresult;
     // 报表类型
    private String reporttype;
     // 图表类型
    private String charttype;
     // RowNum
    private Integer rownum;
     // 摘要
    private String summary;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;

   // id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
   // 报表Type
    public String getFrtype() {
        return frtype;
    }
    
    public void setFrtype(String frtype) {
        this.frtype = frtype;
    }
        
   // 报表分组id
    public String getFrgroupid() {
        return frgroupid;
    }
    
    public void setFrgroupid(String frgroupid) {
        this.frgroupid = frgroupid;
    }
        
   // 本地数据库
    public Integer getLocalmark() {
        return localmark;
    }
    
    public void setLocalmark(Integer localmark) {
        this.localmark = localmark;
    }
        
   // 报表编码
    public String getReportcode() {
        return reportcode;
    }
    
    public void setReportcode(String reportcode) {
        this.reportcode = reportcode;
    }
        
   // 报表名称
    public String getReportname() {
        return reportname;
    }
    
    public void setReportname(String reportname) {
        this.reportname = reportname;
    }
        
   // 动态查询类型sql、http
    public String getDyntype() {
        return dyntype;
    }
    
    public void setDyntype(String dyntype) {
        this.dyntype = dyntype;
    }
        
   // 动态查询sql或者接口中的请求体
    public String getDynsentence() {
        return dynsentence;
    }
    
    public void setDynsentence(String dynsentence) {
        this.dynsentence = dynsentence;
    }
        
   // 拼接的完整SQL
    public String getSqlfull() {
        return sqlfull;
    }
    
    public void setSqlfull(String sqlfull) {
        this.sqlfull = sqlfull;
    }
        
   // 查询字段
    public String getSqlselect() {
        return sqlselect;
    }
    
    public void setSqlselect(String sqlselect) {
        this.sqlselect = sqlselect;
    }
        
   // 表名join
    public String getSqlfrom() {
        return sqlfrom;
    }
    
    public void setSqlfrom(String sqlfrom) {
        this.sqlfrom = sqlfrom;
    }
        
   // 查询条件
    public String getSqlwhere() {
        return sqlwhere;
    }
    
    public void setSqlwhere(String sqlwhere) {
        this.sqlwhere = sqlwhere;
    }
        
   // 分组操作
    public String getSqlgroupby() {
        return sqlgroupby;
    }
    
    public void setSqlgroupby(String sqlgroupby) {
        this.sqlgroupby = sqlgroupby;
    }
        
   // 聚合筛选
    public String getSqlhaving() {
        return sqlhaving;
    }
    
    public void setSqlhaving(String sqlhaving) {
        this.sqlhaving = sqlhaving;
    }
        
   // 排序
    public String getSqlorderby() {
        return sqlorderby;
    }
    
    public void setSqlorderby(String sqlorderby) {
        this.sqlorderby = sqlorderby;
    }
        
   // 限制输出
    public String getSqllimit() {
        return sqllimit;
    }
    
    public void setSqllimit(String sqllimit) {
        this.sqllimit = sqllimit;
    }
        
   // 主表
    public String getMaintable() {
        return maintable;
    }
    
    public void setMaintable(String maintable) {
        this.maintable = maintable;
    }
        
   // 有效性
    public Integer getEnabledmark() {
        return enabledmark;
    }
    
    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
        
   // 公共报表
    public Integer getPublicmark() {
        return publicmark;
    }
    
    public void setPublicmark(Integer publicmark) {
        this.publicmark = publicmark;
    }
        
   // 数据库连接id
    public String getDatabaseid() {
        return databaseid;
    }
    
    public void setDatabaseid(String databaseid) {
        this.databaseid = databaseid;
    }
        
   //  Userid来源:0本地,1rms
    public Integer getDomainnum() {
        return domainnum;
    }
    
    public void setDomainnum(Integer domainnum) {
        this.domainnum = domainnum;
    }
        
   // 所属用户id
    public String getUserid() {
        return userid;
    }
    
    public void setUserid(String userid) {
        this.userid = userid;
    }
        
   // 图标
    public String getImageindex() {
        return imageindex;
    }
    
    public void setImageindex(String imageindex) {
        this.imageindex = imageindex;
    }
        
   // 许可编码
    public String getPermissioncode() {
        return permissioncode;
    }
    
    public void setPermissioncode(String permissioncode) {
        this.permissioncode = permissioncode;
    }
        
   // 图标样式
    public String getImagestyle() {
        return imagestyle;
    }
    
    public void setImagestyle(String imagestyle) {
        this.imagestyle = imagestyle;
    }
        
   // 提取数据的路径
    public String getDatepath() {
        return datepath;
    }
    
    public void setDatepath(String datepath) {
        this.datepath = datepath;
    }
        
   // authcode授权码
    public String getAuthcode() {
        return authcode;
    }
    
    public void setAuthcode(String authcode) {
        this.authcode = authcode;
    }
        
   // 结果案例
    public String getCaseresult() {
        return caseresult;
    }
    
    public void setCaseresult(String caseresult) {
        this.caseresult = caseresult;
    }
        
   // 报表类型
    public String getReporttype() {
        return reporttype;
    }
    
    public void setReporttype(String reporttype) {
        this.reporttype = reporttype;
    }
        
   // 图表类型
    public String getCharttype() {
        return charttype;
    }
    
    public void setCharttype(String charttype) {
        this.charttype = charttype;
    }
        
   // RowNum
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
   // 摘要
    public String getSummary() {
        return summary;
    }
    
    public void setSummary(String summary) {
        this.summary = summary;
    }
        
   // 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
   // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
   // 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
   // 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
   // 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
   // 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
   // 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
   // 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
   // 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
   // 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
   // 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
   // 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

