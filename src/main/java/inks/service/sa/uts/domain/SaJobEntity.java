package inks.service.sa.uts.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 定时任务调度表(SaJob)实体类
 *
 * <AUTHOR>
 * @since 2025-03-07 16:07:17
 */
@Data
public class SaJobEntity implements Serializable {
    private static final long serialVersionUID = -40869057213042075L;
     // 主键
    private String id;
     // 任务名称
    private String jobname;
     // 任务组名
    private String jobgroup;
     // 调用目标字符串
    private String invoketarget;
     // cron执行表达式
    private String cronexpression;
     // 计划执行错误策略（1立即执行 2执行一次 3放弃执行）
    private String misfirepolicy;
     // 是否并发执行（0允许 1禁止）
    private String concurrent;
     // 状态（0正常 1暂停）
    private String status;
     // 创建者
    private String createby;
     // 创建时间
    private Date createtime;
     // 更新者
    private String updateby;
     // 更新时间
    private Date updatetime;
     // 备注信息
    private String remark;



}

