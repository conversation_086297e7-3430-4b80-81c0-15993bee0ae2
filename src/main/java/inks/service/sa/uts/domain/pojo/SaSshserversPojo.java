package inks.service.sa.uts.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * SSH服务器配置(SaSshservers)实体类
 *
 * <AUTHOR>
 * @since 2025-05-19 11:11:06
 */
@Data
public class SaSshserversPojo implements Serializable {
    private static final long serialVersionUID = -34827955304887673L;
     // id
    @Excel(name = "id") 
    private String id;
     // 名称
    @Excel(name = "名称") 
    private String servername;
     // 主机
    @Excel(name = "主机") 
    private String host;
     // 端口
    @Excel(name = "端口") 
    private Integer port;
     // 用户名
    @Excel(name = "用户名") 
    private String username;
     // 密码（加密存储）
    @Excel(name = "密码（加密存储）") 
    private String password;
     // 分组名（如：dev、prod、cust）
    @Excel(name = "分组名（如：dev、prod、cust）") 
    private String groupname;
     // 行号
    @Excel(name = "行号") 
    private Integer rownum;
     // 备注
    @Excel(name = "备注") 
    private String remark;
     // 创建者
    @Excel(name = "创建者") 
    private String createby;
     // 创建者id
    @Excel(name = "创建者id") 
    private String createbyid;
     // 新建日期
    @Excel(name = "新建日期") 
    private Date createdate;
     // 制表
    @Excel(name = "制表") 
    private String lister;
     // 制表id
    @Excel(name = "制表id") 
    private String listerid;
     // 修改日期
    @Excel(name = "修改日期") 
    private Date modifydate;
     // 自定义1
    @Excel(name = "自定义1") 
    private String custom1;
     // 自定义2
    @Excel(name = "自定义2") 
    private String custom2;
     // 自定义3
    @Excel(name = "自定义3") 
    private String custom3;
     // 自定义4
    @Excel(name = "自定义4") 
    private String custom4;
     // 自定义5
    @Excel(name = "自定义5") 
    private String custom5;
     // 租户id
    @Excel(name = "租户id") 
    private String tenantid;
     // 乐观锁
    @Excel(name = "乐观锁") 
    private Integer revision;



}

