package inks.service.sa.uts.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * 素材库(UtsMedialibrary)实体类
 *
 * <AUTHOR>
 * @since 2025-06-13 15:45:12
 */
@Data
public class UtsMedialibraryPojo implements Serializable {
    private static final long serialVersionUID = 989534192885058518L;
     // ID
    @Excel(name = "ID") 
    private String id;
     // 通用分组
    @Excel(name = "通用分组") 
    private String gengroupid;
     // 原文件名
    @Excel(name = "原文件名") 
    private String fileoriname;
     // 文件桶
    @Excel(name = "文件桶") 
    private String bucketname;
     // 目录
    @Excel(name = "目录") 
    private String dirname;
     // 文件名
    @Excel(name = "文件名") 
    private String filename;
     // 文件大小
    @Excel(name = "文件大小") 
    private Long filesize;
     // 文件格式
    @Excel(name = "文件格式") 
    private String contenttype;
     // 文件后缀 扩展名
    @Excel(name = "文件后缀 扩展名") 
    private String filesuffix;
     // 存储方式
    @Excel(name = "存储方式") 
    private String storage;
     // OSS位置
    @Excel(name = "OSS位置") 
    private String fileurl;
     // 类型 1图片2音频3视频
    @Excel(name = "类型 1图片2音频3视频") 
    private Integer mediatype;
     // 有效
    @Excel(name = "有效") 
    private Integer enabledmark;
     // 行号
    @Excel(name = "行号") 
    private Integer rownum;
     // 使用计数
    @Excel(name = "使用计数") 
    private Integer usecount;
     // 备注
    @Excel(name = "备注") 
    private String remark;
     // 创建者
    @Excel(name = "创建者") 
    private String createby;
     // 创建者id
    @Excel(name = "创建者id") 
    private String createbyid;
     // 新建日期
    @Excel(name = "新建日期") 
    private Date createdate;
     // 制表
    @Excel(name = "制表") 
    private String lister;
     // 制表id
    @Excel(name = "制表id") 
    private String listerid;
     // 修改日期
    @Excel(name = "修改日期") 
    private Date modifydate;
     // 自定义1
    @Excel(name = "自定义1") 
    private String custom1;
     // 自定义2
    @Excel(name = "自定义2") 
    private String custom2;
     // 自定义3
    @Excel(name = "自定义3") 
    private String custom3;
     // 自定义4
    @Excel(name = "自定义4") 
    private String custom4;
     // 自定义5
    @Excel(name = "自定义5") 
    private String custom5;
     // 自定义6
    @Excel(name = "自定义6") 
    private String custom6;
     // 自定义7
    @Excel(name = "自定义7") 
    private String custom7;
     // 自定义8
    @Excel(name = "自定义8") 
    private String custom8;
     // 自定义9
    @Excel(name = "自定义9") 
    private String custom9;
     // 自定义10
    @Excel(name = "自定义10") 
    private String custom10;
     // 部门ID
    @Excel(name = "部门ID") 
    private String deptid;
     // 租户id
    @Excel(name = "租户id") 
    private String tenantid;
     // 租户名称
    @Excel(name = "租户名称") 
    private String tenantname;
     // 乐观锁
    @Excel(name = "乐观锁") 
    private Integer revision;



}

