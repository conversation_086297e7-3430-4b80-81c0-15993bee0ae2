package inks.service.sa.uts.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import java.util.List;
import lombok.Data;

/**
 * SSH流水线执行历史(SaSshhistory)实体类
 *
 * <AUTHOR>
 * @since 2025-05-19 11:11:27
 */
@Data
public class SaSshhistoryPojo implements Serializable {
    private static final long serialVersionUID = 758080719729938740L;
     // id
     @Excel(name = "id")
    private String id;
     // 流水线id
     @Excel(name = "流水线id")
    private String pipelineid;
     // 流水线名称
     @Excel(name = "流水线名称")
    private String pipelinename;
     // 服务器id
     @Excel(name = "服务器id")
    private String serverid;
     // 服务器名称
     @Excel(name = "服务器名称")
    private String servername;
     // 会话id（备用）
     @Excel(name = "会话id（备用）")
    private String sessionid;
     // 执行状态：Running, Completed, Failed, Cancelled
     @Excel(name = "执行状态：Running, Completed, Failed, Cancelled")
    private String status;
     // 开始时间
     @Excel(name = "开始时间")
    private Date starttime;
     // 结束时间
     @Excel(name = "结束时间")
    private Date endtime;
     // 行号
     @Excel(name = "行号")
    private Integer rownum;
     // 备注
     @Excel(name = "备注")
    private String remark;
     // 创建者
     @Excel(name = "创建者")
    private String createby;
     // 创建者id
     @Excel(name = "创建者id")
    private String createbyid;
     // 新建日期
     @Excel(name = "新建日期")
    private Date createdate;
     // 制表
     @Excel(name = "制表")
    private String lister;
     // 制表id
     @Excel(name = "制表id")
    private String listerid;
     // 修改日期
     @Excel(name = "修改日期")
    private Date modifydate;
     // 自定义1
     @Excel(name = "自定义1")
    private String custom1;
     // 自定义2
     @Excel(name = "自定义2")
    private String custom2;
     // 自定义3
     @Excel(name = "自定义3")
    private String custom3;
     // 自定义4
     @Excel(name = "自定义4")
    private String custom4;
     // 自定义5
     @Excel(name = "自定义5")
    private String custom5;
     // 租户id
     @Excel(name = "租户id")
    private String tenantid;
     // 乐观锁
     @Excel(name = "乐观锁")
    private Integer revision;
    // 子表
    private List<SaSshhistoryitemPojo> item;
    

}

