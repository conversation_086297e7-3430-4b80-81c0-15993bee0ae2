package inks.service.sa.uts.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * SQL执行器(UtsSqlactuator)实体类
 *
 * <AUTHOR>
 * @since 2025-05-29 14:07:30
 */
@Data
public class UtsSqlactuatorEntity implements Serializable {
    private static final long serialVersionUID = 264389694269407385L;
     // id
    private String id;
     // sql名称
    private String sqlname;
     // SQL语句
    private String sqldata;
     // 功能模块
    private String modulename;
     // 版本
    private String version;
     // 数据库连接id
    private String databaseid;
     // 返回
    private String resultjson;
     // 业务类型（0列表 1新增 2修改 3删除）
    private Integer bustype;
     // 操作状态（0正常 1异常）
    private Integer statusnum;
     // 所属系统编码
    private String code;
     // Sql变更的时间戳
    private Long sqlecntimestamp;
     // 备注
    private String remark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;



}

