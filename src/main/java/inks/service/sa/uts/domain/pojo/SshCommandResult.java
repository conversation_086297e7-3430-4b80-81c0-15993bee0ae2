package inks.service.sa.uts.domain.pojo;

import java.io.Serializable;
import java.util.Date;

/**
 * SSH命令执行结果类
 * 封装单个SSH命令执行的结果信息
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
public class SshCommandResult implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 执行的命令文本
     */
    private String commandText;

    /**
     * 标准输出
     */
    private String output;

    /**
     * 错误输出
     */
    private String error;

    /**
     * 命令退出状态码 (0 表示成功)
     */
    private int exitStatus;

    /**
     * 执行状态 (Success/Failed/Timeout)
     */
    private String status;

    /**
     * 是否为系统日志条目
     */
    private Boolean isSystemLog = false;

    /**
     * 开始执行时间
     */
    private Date startTime;

    /**
     * 结束执行时间
     */
    private Date endTime;

    /**
     * 执行耗时(毫秒)
     */
    private long durationMs;

    /**
     * 重试次数
     */
    private int retryCount;

    /**
     * 是否成功匹配了成功模式
     */
    private boolean matchedSuccessPattern;

    /**
     * 是否匹配了错误模式
     */
    private boolean matchedErrorPattern;

    /**
     * 步骤名称（从数据库中获取）
     */
    private String stepName;

    // Constructors
    public SshCommandResult() {
        this.startTime = new Date();
        this.status = "Running";
    }

    public SshCommandResult(String commandText) {
        this();
        this.commandText = commandText;
    }

    // Getters and Setters
    public String getCommandText() {
        return commandText;
    }

    public void setCommandText(String commandText) {
        this.commandText = commandText;
    }

    public String getOutput() {
        return output;
    }

    public void setOutput(String output) {
        this.output = output;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public int getExitStatus() {
        return exitStatus;
    }

    public void setExitStatus(int exitStatus) {
        this.exitStatus = exitStatus;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Boolean getIsSystemLog() {
        return isSystemLog;
    }

    public void setIsSystemLog(Boolean isSystemLog) {
        this.isSystemLog = isSystemLog;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
        if (startTime != null && endTime != null) {
            this.durationMs = endTime.getTime() - startTime.getTime();
        }
    }

    public long getDurationMs() {
        return durationMs;
    }

    public void setDurationMs(long durationMs) {
        this.durationMs = durationMs;
    }

    public int getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(int retryCount) {
        this.retryCount = retryCount;
    }

    public boolean isMatchedSuccessPattern() {
        return matchedSuccessPattern;
    }

    public void setMatchedSuccessPattern(boolean matchedSuccessPattern) {
        this.matchedSuccessPattern = matchedSuccessPattern;
    }

    public boolean isMatchedErrorPattern() {
        return matchedErrorPattern;
    }

    public void setMatchedErrorPattern(boolean matchedErrorPattern) {
        this.matchedErrorPattern = matchedErrorPattern;
    }

    public String getStepName() {
        return stepName;
    }

    public void setStepName(String stepName) {
        this.stepName = stepName;
    }

    /**
     * 完成命令执行，设置结束时间和状态
     * @param exitCode 退出码
     * @param output 标准输出
     * @param error 错误输出
     */
    public void complete(int exitCode, String output, String error) {
        this.exitStatus = exitCode;
        this.output = output;
        this.error = error;
        this.endTime = new Date();
        this.status = (exitCode == 0) ? "Success" : "Failed";
        this.durationMs = this.endTime.getTime() - this.startTime.getTime();
    }

    /**
     * 设置超时状态
     */
    public void timeout() {
        this.status = "Timeout";
        this.endTime = new Date();
        this.durationMs = this.endTime.getTime() - this.startTime.getTime();
    }
}
