package inks.service.sa.uts.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * SSH服务器配置(SaSshservers)实体类
 *
 * <AUTHOR>
 * @since 2025-05-19 11:11:06
 */
@Data
public class SaSshserversEntity implements Serializable {
    private static final long serialVersionUID = -62704284070145976L;
     // id
    private String id;
     // 名称
    private String servername;
     // 主机
    private String host;
     // 端口
    private Integer port;
     // 用户名
    private String username;
     // 密码（加密存储）
    private String password;
     // 分组名（如：dev、prod、cust）
    private String groupname;
     // 行号
    private Integer rownum;
     // 备注
    private String remark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 租户id
    private String tenantid;
     // 乐观锁
    private Integer revision;



}

