package inks.service.sa.uts.domain.vo;

import java.util.List;

/**
 * 钉钉机器人接口消息请求DTO集合（合并到一个文件）
 */
public class DingTalkRobotMessages {

    public static class TextMessageRequest {
        private String token;       // 可选（不填使用默认值）
        private String secret;      // 可选（不填使用默认值）
        private String content;     // 必填，消息内容
        private List<String> atuserids; // @用户ID列表
        private Boolean isatall;    // 是否@所有人

        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }

        public String getSecret() {
            return secret;
        }

        public void setSecret(String secret) {
            this.secret = secret;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public List<String> getAtuserids() {
            return atuserids;
        }

        public void setAtuserids(List<String> atuserids) {
            this.atuserids = atuserids;
        }

        public Boolean getIsatall() {
            return isatall;
        }

        public void setIsatall(Boolean isatall) {
            this.isatall = isatall;
        }
    }

    public static class LinkMessageRequest {
        private String token;
        private String secret;
        private String title;       // 链接标题
        private String text;        // 内容摘要
        private String messageurl;  // 跳转URL
        private String picurl;      // 图片URL

        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }

        public String getSecret() {
            return secret;
        }

        public void setSecret(String secret) {
            this.secret = secret;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public String getMessageurl() {
            return messageurl;
        }

        public void setMessageurl(String messageurl) {
            this.messageurl = messageurl;
        }

        public String getPicurl() {
            return picurl;
        }

        public void setPicurl(String picurl) {
            this.picurl = picurl;
        }
    }

    public static class MarkdownMessageRequest {
        private String token;
        private String secret;
        private String title;       // 首屏标题
        private String text;        // Markdown内容
        private List<String> atuserids; // @用户列表
        private Boolean isatall;    // 是否@所有人

        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }

        public String getSecret() {
            return secret;
        }

        public void setSecret(String secret) {
            this.secret = secret;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public List<String> getAtuserids() {
            return atuserids;
        }

        public void setAtuserids(List<String> atuserids) {
            this.atuserids = atuserids;
        }

        public Boolean getIsatall() {
            return isatall;
        }

        public void setIsatall(Boolean isatall) {
            this.isatall = isatall;
        }
    }

    public static class ActionCardRequest {
        private String token;
        private String secret;
        private String title;         // 卡片标题
        private String text;          // 正文内容
        private String singletitle;   // 单按钮标题
        private String singleurl;     // 单按钮链接
        private String btnorientation;// 按钮布局 0-垂直 1-水平
        private List<CardButton> buttons; // 多按钮配置

        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }

        public String getSecret() {
            return secret;
        }

        public void setSecret(String secret) {
            this.secret = secret;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public String getSingletitle() {
            return singletitle;
        }

        public void setSingletitle(String singletitle) {
            this.singletitle = singletitle;
        }

        public String getSingleurl() {
            return singleurl;
        }

        public void setSingleurl(String singleurl) {
            this.singleurl = singleurl;
        }

        public String getBtnorientation() {
            return btnorientation;
        }

        public void setBtnorientation(String btnorientation) {
            this.btnorientation = btnorientation;
        }

        public List<CardButton> getButtons() {
            return buttons;
        }

        public void setButtons(List<CardButton> buttons) {
            this.buttons = buttons;
        }
    }

    public static class CardButton {
        private String title;   // 按钮文案
        private String actionurl; // 点击URL

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getActionurl() {
            return actionurl;
        }

        public void setActionurl(String actionurl) {
            this.actionurl = actionurl;
        }
    }

    public static class FeedCardRequest {
        private String token;
        private String secret;
        private List<FeedLink> links; // 图文链接列表（最多10条）

        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }

        public String getSecret() {
            return secret;
        }

        public void setSecret(String secret) {
            this.secret = secret;
        }

        public List<FeedLink> getLinks() {
            return links;
        }

        public void setLinks(List<FeedLink> links) {
            this.links = links;
        }
    }

    public static class FeedLink {
        private String title;      // 标题
        private String picurl;     // 图片URL
        private String messageurl; // 跳转URL

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getPicurl() {
            return picurl;
        }

        public void setPicurl(String picurl) {
            this.picurl = picurl;
        }

        public String getMessageurl() {
            return messageurl;
        }

        public void setMessageurl(String messageurl) {
            this.messageurl = messageurl;
        }
    }
}
