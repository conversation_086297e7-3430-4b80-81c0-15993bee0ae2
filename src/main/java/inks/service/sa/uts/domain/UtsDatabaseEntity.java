package inks.service.sa.uts.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 数据库连接池(UtsDatabase)实体类
 *
 * <AUTHOR>
 * @since 2024-05-11 15:28:20
 */
public class UtsDatabaseEntity implements Serializable {
    private static final long serialVersionUID = 581611539397254296L;
     // id
    private String id;
     // 标题
    private String title;
     // 数据库url格式：********************************
    private String url;
     // 数据库username
    private String username;
     // 数据库password
    private String password;
     // 数据库驱动com.mysql.cj.jdbc.Driver
    private String driverclassname;
     // 有效性
    private Integer enabledmark;
     // 行号
    private Integer rownum;
     // 备注
    private String remark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;

// id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
// 标题
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
        
// 数据库url格式：********************************
    public String getUrl() {
        return url;
    }
    
    public void setUrl(String url) {
        this.url = url;
    }
        
// 数据库username
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
        
// 数据库password
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
        
// 数据库驱动com.mysql.cj.jdbc.Driver
    public String getDriverclassname() {
        return driverclassname;
    }
    
    public void setDriverclassname(String driverclassname) {
        this.driverclassname = driverclassname;
    }
        
// 有效性
    public Integer getEnabledmark() {
        return enabledmark;
    }
    
    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
        
// 行号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
// 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
// 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
// 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
// 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
// 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
// 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
// 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
// 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
// 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
// 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

