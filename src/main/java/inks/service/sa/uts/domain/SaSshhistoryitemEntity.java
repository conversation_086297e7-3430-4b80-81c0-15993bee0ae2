package inks.service.sa.uts.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * SSH流水线历史步骤(SaSshhistoryitem)Entity
 *
 * <AUTHOR>
 * @since 2025-05-19 11:11:44
 */
@Data
public class SaSshhistoryitemEntity implements Serializable {
    private static final long serialVersionUID = 540437116017016205L;
     // id
    private String id;
     // Pid
    private String pid;
     // 步骤id
    private String stepid;
     // 步骤名称
    private String stepname;
     // 步骤顺序号
    private Integer steprownum;
     // 执行命令
    private String commandtext;
     // 标准输出
    private String output;
     // 错误输出
    private String error;
     // 退出状态码：0=成功，非0=失败
    private Integer exitstatus;
     // 状态：Pending, Running, Success, Failed, Skipped
    private String status;
     // 开始时间
    private Date starttime;
     // 结束时间
    private Date endtime;
     // 执行耗时（毫秒）
    private Integer durationms;
     // 当前重试次数
    private Integer retrycount;
     // 行号
    private Integer rownum;
     // 备注
    private String remark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 租户id
    private String tenantid;
     // 乐观锁
    private Integer revision;


}

