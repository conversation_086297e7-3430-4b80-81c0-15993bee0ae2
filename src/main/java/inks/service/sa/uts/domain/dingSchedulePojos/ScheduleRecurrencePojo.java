package inks.service.sa.uts.domain.dingSchedulePojos;

import com.alibaba.fastjson.annotation.JSONField;

public class ScheduleRecurrencePojo {
    @JSONField(name = "pattern")
    private ScheduleRecurrencePatternPojo schedulePattern;

    @JSONField(name = "range")
    private ScheduleRecurrenceRangePojo scheduleRange;

    public ScheduleRecurrencePatternPojo getSchedulePattern() {
        return schedulePattern;
    }

    public void setSchedulePattern(ScheduleRecurrencePatternPojo schedulePattern) {
        this.schedulePattern = schedulePattern;
    }

    public ScheduleRecurrenceRangePojo getScheduleRange() {
        return scheduleRange;
    }

    public void setScheduleRange(ScheduleRecurrenceRangePojo scheduleRange) {
        this.scheduleRange = scheduleRange;
    }
}
