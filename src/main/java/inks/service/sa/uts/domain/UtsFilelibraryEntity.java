package inks.service.sa.uts.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 文件库(UtsFilelibrary)实体类
 *
 * <AUTHOR>
 * @since 2025-06-17 16:51:51
 */
@Data
public class UtsFilelibraryEntity implements Serializable {
    private static final long serialVersionUID = 489464408747594541L;
     // ID
    private String id;
     // 通用分组
    private String gengroupid;
     // 原文件名
    private String fileoriname;
     // 文件桶
    private String bucketname;
     // 目录
    private String dirname;
     // 文件名
    private String filename;
     // 文件大小
    private Long filesize;
     // 文件格式
    private String contenttype;
     // 文件后缀 扩展名
    private String filesuffix;
     // 存储方式
    private String storage;
     // 关联id 单据id
    private String relateid;
     // OSS位置
    private String fileurl;
     // 功能编码
    private String modulecode;
     // 模块
    private String module;
     // 有效
    private Integer enabledmark;
     // 公共
    private Integer publicmark;
     // 行号
    private Integer rownum;
     // 备注
    private String remark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 部门ID
    private String deptid;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;



}

