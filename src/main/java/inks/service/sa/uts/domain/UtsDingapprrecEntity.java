package inks.service.sa.uts.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 钉钉审批记录(UtsDingapprrec)实体类
 *
 * <AUTHOR>
 * @since 2024-12-16 14:12:14
 */
@Data
public class UtsDingapprrecEntity implements Serializable {
    private static final long serialVersionUID = -14485099314244901L;
     // id
    private String id;
     // 模块编码
    private String modulecode;
     // 模版id
    private String templateid;
     // 审批名称
    private String apprname;
     // 数据模版
    private String datatemp;
     // 回调Url
    private String callbackurl;
     // 回调Bean
    private String callbackbean;
     // 执行条件
    private String resultcode;
     // 类型(备用)
    private String apprtype;
     // 审批Sn
    private String apprsn;
     // 单据id
    private String billid;
     // 回调Uuid
    private String callbackuuid;
     // 回调人员
    private String callbackname;
     // 回调日期
    private Date callbackdate;
     // 回调结果
    private String callbackresult;
     // 回调信息
    private String callbackmsg;
     // 用户id
    private String userid;
     // 姓名
    private String realname;
     // 备注
    private String remark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;



}

