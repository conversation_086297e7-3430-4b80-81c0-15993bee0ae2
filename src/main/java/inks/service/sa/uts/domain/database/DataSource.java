package inks.service.sa.uts.domain.database;

// 拷贝sa-edb项目
public class DataSource  {
    private String id;
    // 数据源编码
    private String sourcecode;
    // 数据源名称
    private String sourcename;
    // 数据源描述
    private String sourcedesc;
    // 数据源类型1-数据库 2-接口
    private String sourcetype;
    // 数据源配置
    private String sourceconfig;
    // 0--已禁用 1--已启用  DIC_NAME=ENABLE_FLAG
    private Integer enableflag;
    // 0--未删除 1--已删除 DIC_NAME=DELETE_FLAG
    private Integer deleteflag;
    // 备注
    private String remark;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSourcecode() {
        return sourcecode;
    }

    public void setSourcecode(String sourcecode) {
        this.sourcecode = sourcecode;
    }

    public String getSourcename() {
        return sourcename;
    }

    public void setSourcename(String sourcename) {
        this.sourcename = sourcename;
    }

    public String getSourcedesc() {
        return sourcedesc;
    }

    public void setSourcedesc(String sourcedesc) {
        this.sourcedesc = sourcedesc;
    }

    public String getSourcetype() {
        return sourcetype;
    }

    public void setSourcetype(String sourcetype) {
        this.sourcetype = sourcetype;
    }

    public String getSourceconfig() {
        return sourceconfig;
    }

    public void setSourceconfig(String sourceconfig) {
        this.sourceconfig = sourceconfig;
    }

    public Integer getEnableflag() {
        return enableflag;
    }

    public void setEnableflag(Integer enableflag) {
        this.enableflag = enableflag;
    }

    public Integer getDeleteflag() {
        return deleteflag;
    }

    public void setDeleteflag(Integer deleteflag) {
        this.deleteflag = deleteflag;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
