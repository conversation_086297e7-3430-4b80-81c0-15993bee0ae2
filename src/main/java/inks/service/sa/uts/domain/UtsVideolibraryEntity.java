package inks.service.sa.uts.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 视频信息表(UtsVideolibrary)实体类
 *
 * <AUTHOR>
 * @since 2025-05-29 14:19:49
 */
@Data
public class UtsVideolibraryEntity implements Serializable {
    private static final long serialVersionUID = 169728861656052817L;
     // ID
    private String id;
     // 视频标题
    private String videotitle;
     // 视频原始文件名
    private String videofilename;
     // minio的ObjectName
    private String objectname;
     // 视频封面地址
    private String videocoverurl;
     // 视频时长(秒)
    private Integer videoduration;
     // 播放次数
    private Integer videoplaytimes;
     // 简介
    private String videodesc;
     // 通用分组
    private String gengroupid;
     // 文件大小
    private Long filesize;
     // 密钥
    private String secretkey;
     // 上传时间
    private Date uploadtime;
     // 文字教程
    private String texttutorial;
     // 标签
    private String videotag;
     // 底色
    private String backcolorargb;
     // 底色
    private String forecolorargb;
     // 公共
    private Integer publicmark;
     // 有效
    private Integer enabledmark;
     // 排序码
    private Integer rownum;
     // 备注
    private String remark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 审核人
    private String assessor;
     // 审核人id
    private String assessorid;
     // 审核时间
    private Date assessdate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 租户id
    private String tenantid;
     // 租户
    private String tenantname;
     // 乐观锁
    private Integer revision;



}

