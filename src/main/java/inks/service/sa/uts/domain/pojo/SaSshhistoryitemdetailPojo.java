package inks.service.sa.uts.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.DatailPojo;
import lombok.Data;

/**
 * SSH流水线历史步骤(SaSshhistoryitem)Pojo
 *
 * <AUTHOR>
 * @since 2025-05-19 11:11:44
 */
@Data
public class SaSshhistoryitemdetailPojo extends DatailPojo implements Serializable {
    private static final long serialVersionUID = -31437747692440849L;
     // id
  @Excel(name = "id")    
  private String id;
     // Pid
  @Excel(name = "Pid")    
  private String pid;
     // 步骤id
  @Excel(name = "步骤id")    
  private String stepid;
     // 步骤名称
  @Excel(name = "步骤名称")    
  private String stepname;
     // 步骤顺序号
  @Excel(name = "步骤顺序号")    
  private Integer steprownum;
     // 执行命令
  @Excel(name = "执行命令")    
  private String commandtext;
     // 标准输出
  @Excel(name = "标准输出")    
  private String output;
     // 错误输出
  @Excel(name = "错误输出")    
  private String error;
     // 退出状态码：0=成功，非0=失败
  @Excel(name = "退出状态码：0=成功，非0=失败")    
  private Integer exitstatus;
     // 状态：Pending, Running, Success, Failed, Skipped
  @Excel(name = "状态：Pending, Running, Success, Failed, Skipped")    
  private String status;
     // 开始时间
  @Excel(name = "开始时间")    
  private Date starttime;
     // 结束时间
  @Excel(name = "结束时间")    
  private Date endtime;
     // 执行耗时（毫秒）
  @Excel(name = "执行耗时（毫秒）")    
  private Integer durationms;
     // 当前重试次数
  @Excel(name = "当前重试次数")    
  private Integer retrycount;
     // 行号
  @Excel(name = "行号")    
  private Integer rownum;
     // 备注
  @Excel(name = "备注")    
  private String remark;
     // 创建者
  @Excel(name = "创建者")    
  private String createby;
     // 创建者id
  @Excel(name = "创建者id")    
  private String createbyid;
     // 新建日期
  @Excel(name = "新建日期")    
  private Date createdate;
     // 制表
  @Excel(name = "制表")    
  private String lister;
     // 制表id
  @Excel(name = "制表id")    
  private String listerid;
     // 修改日期
  @Excel(name = "修改日期")    
  private Date modifydate;
     // 自定义1
  @Excel(name = "自定义1")    
  private String custom1;
     // 自定义2
  @Excel(name = "自定义2")    
  private String custom2;
     // 自定义3
  @Excel(name = "自定义3")    
  private String custom3;
     // 自定义4
  @Excel(name = "自定义4")    
  private String custom4;
     // 自定义5
  @Excel(name = "自定义5")    
  private String custom5;
     // 租户id
  @Excel(name = "租户id")    
  private String tenantid;
     // 乐观锁
  @Excel(name = "乐观锁")    
  private Integer revision;



}

