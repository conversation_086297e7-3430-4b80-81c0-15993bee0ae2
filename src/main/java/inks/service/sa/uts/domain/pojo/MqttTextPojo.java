package inks.service.sa.uts.domain.pojo;

import java.io.Serializable;

public class MqttTextPojo implements Serializable {
    private static final long serialVersionUID = 1234242545645L;
    private Msg msg;
    private Boolean toAllUser;
    private String userIdList;
    private String moduleCode;


    public MqttTextPojo(Msg msg, Boolean toAllUser, String userIdList, String moduleCode) {
        this.msg = msg;
        this.toAllUser = toAllUser;
        this.userIdList = userIdList;
        this.moduleCode = moduleCode;
    }

    // 添加默认构造方法
    public MqttTextPojo() {
    }

    // 添加 getter 和 setter 方法

    public Msg getMsg() {
        return msg;
    }

    public void setMsg(Msg msg) {
        this.msg = msg;
    }

    public Boolean getToAllUser() {
        return toAllUser;
    }

    public void setToAllUser(Boolean toAllUser) {
        this.toAllUser = toAllUser;
    }

    public String getUserIdList() {
        return userIdList;
    }

    public void setUserIdList(String userIdList) {
        this.userIdList = userIdList;
    }

    public String getModuleCode() {
        return moduleCode;
    }

    public void setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode;
    }

    // 静态内部类 Msg
    public static class Msg {
        private Text text;
        private String msgType;
        private int duration;
        private String position;
        private int msgicon;

        public Msg(Text text, String msgType, int duration, String position, int msgicon) {
            this.text = text;
            this.msgType = msgType;
            this.duration = duration;
            this.position = position;
            this.msgicon = msgicon;
        }

        // 添加默认构造方法
        public Msg() {
        }

        // 添加 getter 和 setter 方法

        public Text getText() {
            return text;
        }

        public void setText(Text text) {
            this.text = text;
        }

        public String getMsgType() {
            return msgType;
        }

        public void setMsgType(String msgType) {
            this.msgType = msgType;
        }

        public int getDuration() {
            return duration;
        }

        public void setDuration(int duration) {
            this.duration = duration;
        }

        public String getPosition() {
            return position;
        }

        public void setPosition(String position) {
            this.position = position;
        }

        public int getMsgicon() {
            return msgicon;
        }

        public void setMsgicon(int msgicon) {
            this.msgicon = msgicon;
        }
    }

    // 静态内部类 Text
    public static class Text {
        private String title;
        private String content;

        public Text(String title, String content) {
            this.title = title;
            this.content = content;
        }

        // 添加默认构造方法
        public Text() {
        }

        // 添加 getter 和 setter 方法

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }
    }
}

