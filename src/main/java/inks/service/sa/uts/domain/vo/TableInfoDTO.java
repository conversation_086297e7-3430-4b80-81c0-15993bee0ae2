package inks.service.sa.uts.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "数据库表信息")
public class TableInfoDTO {

    @ApiModelProperty(value = "表名")
    private String tablename;

    @ApiModelProperty(value = "表注释/描述")
    private String tablecomment;

    @ApiModelProperty(value = "数据行数")
    private Long totalrows; // 使用 Long 避免整数溢出
}
