package inks.service.sa.uts.backup.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.sql.Connection;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 免 mysqldump 命令备份 SQL
 *
 * <AUTHOR> <EMAIL>
 */
public class MysqlExport {
    private final static Logger LOGGER = LoggerFactory.getLogger(MysqlExport.class);


    public static void main(String[] args) throws SQLException, IOException {
        //用户主目录：C:\Users\<USER>\nanno\WORK-CODE\inks-cloud-master  Linux: /当前目录路径
        String currentWorkingDirectory = System.getProperty("user.dir");
        System.out.println("userHome = " + userHome);
        System.out.println("currentWorkingDirectory = " + currentWorkingDirectory);
//        Connection connection = JdbcConnection.getConnection("192.168.99.240:53308","inkssaas","root", "asd@123456");
//        Connection connection = JdbcConnection.getConnection("192.168.99.240:53308", "test2", "root", "asd@123456");
        Connection connection = JdbcConnection.getConnection("192.168.99.111:53308", "inkssaas", "root", "asd@123456");
//        String jdbcUrl = "******************************************************************************************************************************************************************************************************************************";
//        Connection connection = JdbcConnection.getConnection(jdbcUrl);

//        MysqlExport mysqlExport = new MysqlExport(connection,"inkssaas","D:\\nanno\\SQL-Buckup");
        // 保存到当前工作目录
        MysqlExport mysqlExport = new MysqlExport(connection, currentWorkingDirectory);
        String exportFilePath = mysqlExport.export(null); //导出zip到指定目录(文件路径)
        System.out.println("exportFilePath = " + exportFilePath);

        // 上传.zip压缩文件到minio
        // 设置上传响应等待时间为5分钟(不设置会响应超时报错)
        OkHttpClient client = new OkHttpClient.Builder()
                .readTimeout(5, TimeUnit.MINUTES)
                .build();
        // 创建请求体
        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("filepath", exportFilePath)
                .addFormDataPart("bucket", "utils")
                .addFormDataPart("dir", "mysql_backup")
                .addFormDataPart("osstype", "minio")
                .build();
        // 构建请求
        Request request = new Request.Builder()
                .url("http://dev.inksyun.com:31080/utils/D96M16B1/uploadByPath")
                .headers(Headers.of("Authorization", "bcdb"))
                .post(requestBody)
                .build();
        // 发送请求并获取响应
        Response response = client.newCall(request).execute();
        // 处理响应结果
        if (response.isSuccessful()) {
            String responseBody = response.body().string();
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode responseJson = objectMapper.readTree(responseBody);
            String fileUrl = responseJson.get("data").get("fileurl").asText();
            System.out.println("fileUrl = " + fileUrl);
        } else {
            throw new IOException("Unexpected code " + response);
        }
        // 上传完后：删除压缩后的文件名(全路径)
//        FileHelper.delete(exportFilePath);
    }

    public static void main222(String[] args) throws IOException {
        OkHttpClient client = new OkHttpClient();
        // 创建请求体
        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("filepath", "D:\\nanno\\WORK-CODE\\inks-cloud-master\\db-dump-2023-06-01-inkssaas.zip")
                .addFormDataPart("bucket", "utils")
                .addFormDataPart("dir", "mysql_backup")
                .addFormDataPart("osstype", "minio")
                .build();
        // 构建请求
        Request request = new Request.Builder()
                .url("http://dev.inksyun.com:31080/utils/D96M16B1/uploadByPath")
                .headers(Headers.of("Authorization", "bcdb"))
                .post(requestBody)
                .build();
        // 发送请求并获取响应
        Response response = client.newCall(request).execute();
        // 处理响应结果
        if (response.isSuccessful()) {
            String responseBody = response.body().string();
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode responseJson = objectMapper.readTree(responseBody);
            String fileUrl = responseJson.get("data").get("fileurl").asText();
            System.out.println("fileUrl = " + fileUrl);
        } else {
            throw new IOException("Unexpected code " + response);
        }
    }

    /**
     * 创建 MysqlExport 对象
     *
     * @param conn       数据库连接对象
     * @param saveFolder 保存目录
     */
    public MysqlExport(Connection conn, String saveFolder) throws SQLException {
//        String[] arr = conn.toString().split("\\?")[0].split("/");
//        databaseName = arr[arr.length - 1];
        //拿到连接的数据库名字
        this.databaseName = conn.getCatalog();
        this.saveFolder = saveFolder;

        try {
            stmt = conn.createStatement(ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
        } catch (SQLException e) {
            LOGGER.error(String.valueOf(e));
        }
    }

    private static final String SQL_START_PATTERN = "-- start";

    private static final String SQL_END_PATTERN = "-- end";

    /**
     * 执行语句
     */
    private Statement stmt;

    /**
     * 数据库名
     */
    private String databaseName;

    /**
     * 导出 SQL 的目录
     */
    private String saveFolder;

    /**
     * 获取当前数据库下的所有表名称
     *
     * @return List\<String\> 所有表名称
     */
    private List<String> getAllTables() {
        List<String> tables = new ArrayList<>();

        JdbcReader.rsHandle(stmt, "SHOW TABLE STATUS FROM `" + databaseName + "`;", rs -> {
            try {
                while (rs.next())
                    tables.add(rs.getString("Name"));
            } catch (SQLException e) {
                LOGGER.error(String.valueOf(e));
            }
        });

        return tables;
    }

    /**
     * 生成 create 语句
     *
     * @param table 表名
     * @return String
     */
    private String getTableInsertStatement(String table) {
        StringBuilder sql = new StringBuilder();

        JdbcReader.rsHandle(stmt, "SHOW CREATE TABLE `" + table + "`;", rs -> {
            try {
                while (rs.next()) {
                    String qtbl = rs.getString(1), query = rs.getString(2);
                    //如果表已经存在，则不会执行创建操作，而是跳过
                    query = query.trim().replace("CREATE TABLE", "CREATE TABLE IF NOT EXISTS");
                    //// 20240222 加入DROP TABLE IF EXISTS先删除表,再创建表
                    //query = query.trim().replace("CREATE TABLE", "DROP TABLE IF EXISTS `" + table + "`;\n\nCREATE TABLE IF NOT EXISTS");

                    sql.append("\n\n--");
                    sql.append("\n").append(SQL_START_PATTERN).append(" table dump: ").append(qtbl);
                    sql.append("\n--\n\n");
                    sql.append(query).append(";\n\n");
                }

                sql.append("\n\n--\n").append(SQL_END_PATTERN).append(" table dump: ").append(table).append("\n--\n\n");
            } catch (SQLException e) {
                LOGGER.error(String.valueOf(e));
            }
        });

        return sql.toString();
    }

    /**
     * 生成insert语句
     *
     * @param table the table to get inserts statement for
     * @return String generated SQL insert
     */
    private String getDataInsertStatement(String table) {
        StringBuilder sql = new StringBuilder();

        JdbcReader.rsHandle(stmt, "SELECT * FROM " + "`" + table + "`;", rs -> {
            try {
                rs.last();
//				int rowCount = rs.getRow();
//			if (rowCount <= 0)
//				return sql.toString();
                int rowCount = rs.getRow();
                if (rowCount <= 0) {
                    // 如果结果集为空，直接返回空字符串
                    return;
                }

                sql.append("\n--").append("\n-- Inserts of ").append(table).append("\n--\n\n");
                sql.append("\n/*!40000 ALTER TABLE `").append(table).append("` DISABLE KEYS */;\n");
                sql.append("\n--\n").append(SQL_START_PATTERN).append(" table insert : ").append(table).append("\n--\n");
                sql.append("INSERT INTO `").append(table).append("`(");

                ResultSetMetaData metaData = rs.getMetaData();
                int columnCount = metaData.getColumnCount();
                for (int i = 0; i < columnCount; i++) {
                    sql.append("`").append(metaData.getColumnName(i + 1)).append("`, ");
                }

                sql.deleteCharAt(sql.length() - 1).deleteCharAt(sql.length() - 1).append(") VALUES \n");
                rs.beforeFirst();

                while (rs.next()) {
                    sql.append("(");
                    for (int i = 0; i < columnCount; i++) {
                        int columnType = metaData.getColumnType(i + 1), columnIndex = i + 1;

                        if (Objects.isNull(rs.getObject(columnIndex)))
                            sql.append(rs.getObject(columnIndex)).append(", ");
                        else if (columnType == Types.INTEGER || columnType == Types.TINYINT || columnType == Types.BIT)
                            sql.append(rs.getInt(columnIndex)).append(", ");
                        else {
                            String val = rs.getString(columnIndex).replace("'", "\\'");
                            sql.append("'").append(val).append("', ");
                        }
                    }

                    sql.deleteCharAt(sql.length() - 1).deleteCharAt(sql.length() - 1);
                    sql.append(rs.isLast() ? ")" : "),\n");
                }
            } catch (SQLException e) {
                LOGGER.error(String.valueOf(e));
            }
        });

        sql.append(";\n--\n").append(SQL_END_PATTERN).append(" table insert : ").append(table).append("\n--\n");
        // enable FK constraint
        sql.append("\n/*!40000 ALTER TABLE `").append(table).append("` ENABLE KEYS */;\n");

        return sql.toString();
    }

    /**
     * 导出所有表的结构和数据
     *
     * @return 完整的 SQL 备份内容
     */
    private String exportToSql() {
        StringBuilder sql = new StringBuilder();
        sql.append("--\n-- Generated by AJAXJS-Data");
        sql.append("\n-- Date: ").append(DateUtil.now("d-M-Y H:m:s")).append("\n--");
        System.out.println("------------------开始---------------------");
        // these declarations are extracted from HeidiSQL
        sql.append("\n\n/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;").append("\n/*!40101 SET NAMES utf8 */;\n/*!50503 SET NAMES utf8mb4 */;")
                .append("\n/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;")
                .append("\n/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;");

        List<String> tables = getAllTables();
        int i = 0;
        for (String s : tables) {
            i++;
            System.out.println("------------------" + i + s + ":开始1getTableInsertStatement---------------------");
            sql.append(getTableInsertStatement(s.trim()));
            System.out.println("------------------" + i + s + ":开始2getDataInsertStatement---------------------");
            sql.append(getDataInsertStatement(s.trim()));
        }

        try {
            stmt.close();
        } catch (SQLException e) {
            LOGGER.error(String.valueOf(e));
        }

        sql.append("\n/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;")
                .append("\n/*!40014 SET FOREIGN_KEY_CHECKS=IF(@OLD_FOREIGN_KEY_CHECKS IS NULL, 1, @OLD_FOREIGN_KEY_CHECKS) */;")
                .append("\n/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;");

        return sql.toString();
    }

    /**
     * 执行导出
     *
     * @return 打包的文件名(将导出SQL保存到.sql文件中, 并压缩为.zip文件, 删除.sql文件, 返回.zip文件名)
     */
    public String export(String zipPassword) {
        String fileName = "db-dump-" + DateUtil.now("yyyy-MM-dd") + "-" + databaseName + ".sql";
        String sqlFile = saveFolder + FileHelper.SEPARATOR + fileName;


        StringBuilder sql = new StringBuilder();
        sql.append("--\n-- Generated by AJAXJS-Data");
        sql.append("\n-- Date: ").append(DateUtil.now("d-M-Y H:m:s")).append("\n--");
        System.out.println("------------------开始---------------------");
        // these declarations are extracted from HeidiSQL
        sql.append("\n\n/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;").append("\n/*!40101 SET NAMES utf8 */;\n/*!50503 SET NAMES utf8mb4 */;")
                .append("\n/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;")
                .append("\n/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;");

        List<String> tables = getAllTables();
        int i = 0;
        for (String s : tables) {
            i++;
            System.out.println("------------------" + i + s + ":开始1getTableInsertStatement---------------------");
            sql.append(getTableInsertStatement(s.trim()));
            System.out.println("------------------" + i + s + ":开始2getDataInsertStatement---------------------");
            sql.append(getDataInsertStatement(s.trim()));
            // 每次循环保存到文件中（以追加的方式）
            FileHelper.saveText(sqlFile, sql.toString());
            // 清空 stringBuilder
            sql.setLength(0);
        }
        try {
            stmt.close();
        } catch (SQLException e) {
            LOGGER.error(String.valueOf(e));
        }
        sql.append("\n/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;")
                .append("\n/*!40014 SET FOREIGN_KEY_CHECKS=IF(@OLD_FOREIGN_KEY_CHECKS IS NULL, 1, @OLD_FOREIGN_KEY_CHECKS) */;")
                .append("\n/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;");

//        FileHelper.saveText(sqlFile, exportToSql());
        // 压缩 zip
        if (StringUtils.isNotBlank(zipPassword)) {
            ZipHelper.zipWithPassword(sqlFile, sqlFile.replace(".sql", ".zip"), zipPassword);
        } else {
            ZipHelper.zip(sqlFile, sqlFile.replace(".sql", ".zip"));
        }
        FileHelper.delete(sqlFile);

//        return fileName.replace(".sql", ".zip");
        // 返回压缩后的文件名(全路径)
        return sqlFile.replace(".sql", ".zip");
    }

}