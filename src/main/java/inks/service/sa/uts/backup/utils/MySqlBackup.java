package inks.service.sa.uts.backup.utils;

import inks.common.core.exception.BaseBusinessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;

public class MySqlBackup {


    private final static Logger logger = LoggerFactory.getLogger(MySqlBackup.class);

    private final static String BACKUP_SUFFIX = ".sql";

    public static String backup(String dbname, String backupName, String backupPath) {
        String path = backupPath + "\\" + backupName + BACKUP_SUFFIX;
        String bakSQL = "mysqldump --opt --default-character-set=utf8 --hex-blob weekend_vms --skip-triggers --skip-lock-tables --set-gtid-purged=OFF > " + backupPath;
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        try {
            connection = new DatabaseUtil().getSqlServerConnection();
            preparedStatement = connection.prepareStatement(bakSQL);
//            preparedStatement.setString(1, path);
            preparedStatement.execute();
            logger.info("{},获取备份文件成功", dbname);
            return path;
        } catch (SQLException e) {
            e.printStackTrace();
            throw new BaseBusinessException("备份失败");
        } finally {
            try {
                preparedStatement.close();

            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }
}
