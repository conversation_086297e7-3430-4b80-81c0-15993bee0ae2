package inks.service.sa.uts.backup.controller;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.util.Arrays;

@RestController
public class MailController {
    @Resource
    private JavaMailSender javaMailSender;
    @Value("${spring.mail.toEmail}")
    private String toEmail;

    /**
     * @Description 发邮件
     * <AUTHOR>
     * @param[1] to 收件人邮箱 多个用逗号或空格分隔
     * @param[2] subject 主题
     * @param[3] content 内容
     * @time 2023/5/24 22:08
     */
    public void sendEmail(String to, String subject, String content) throws MessagingException {
        MimeMessage message = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
        helper.setFrom("<EMAIL>");

        // 支持逗号或空格分隔，过滤掉空字符串
        String[] tos = Arrays.stream(to.split("[,\\s]+"))
                .filter(StringUtils::isNotBlank)
                .map(String::trim)
                .toArray(String[]::new);
        helper.setTo(tos);

        helper.setSubject(subject);
        helper.setText(content, true); // true 表示使用 HTML 格式
        javaMailSender.send(message);
    }


}
