package inks.service.sa.uts.backup.controller;


import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zaxxer.hikari.HikariDataSource;
import inks.common.core.domain.FileInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.IpUtils;
import inks.sa.common.core.config.oss.service.FileController;
import inks.sa.common.core.config.oss.service.OSSConfigManager;
import inks.sa.common.core.domain.pojo.SaLogPojo;
import inks.sa.common.core.service.SaLogService;
import inks.sa.common.core.service.SaRedisService;
import inks.sa.common.core.utils.PrintColor;
import inks.service.sa.uts.backup.utils.DateUtil;
import inks.service.sa.uts.backup.utils.FileHelper;
import inks.service.sa.uts.backup.utils.JdbcConnection;
import inks.service.sa.uts.backup.utils.MysqlExport;
import inks.service.sa.uts.backup.utils.back.DatabaseExport;
import inks.service.sa.uts.config.constant.MyConstant;
import inks.service.sa.uts.service.UtsDatabaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import okhttp3.*;
import okhttp3.RequestBody;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * 数据备份
 *
 * <AUTHOR>
 * @since 2021-11-08 13:55:37
 */
@RestController
@Api(tags = "Mysql备份")
@RequestMapping("/Backup")
public class DB_BackupServer {
    @Resource
    private SaRedisService saRedisService;
    @Resource
    private SaLogService saLogService;


    //@Resource
    //private RedisTemplate<String, Object> redisTemplate;
    @Value("${spring.datasource.url}")
    private String dbUrl;

    @Value("${spring.datasource.username}")
    private String dbUsername;

    @Value("${spring.datasource.password}")
    private String dbPassword;
    @Value("${spring.datasource.driver-class-name}")
    private String dbDriverClassName;
    @Value("${spring.mail.toEmail}")
    private String toEmail;
    @Value("${spring.mail.ipAddress}")
    private String ipAddress;
    @Autowired
    private OSSConfigManager configManager;

    @Resource
    private FileController fileController;
    @Resource
    private MailController mailController;
    private final static Logger log = LoggerFactory.getLogger(DB_BackupServer.class);


    public static void main(String[] args) {
        String currentWorkingDirectory = System.getProperty("user.dir");
        System.out.println("===================System.getProperty(\"user.dir\")" + currentWorkingDirectory);
        // 获取上一级目录
        File currentDirectory = new File(currentWorkingDirectory);
        String parentDirectory = currentDirectory.getParent();
        System.out.println("===================parentDirectory" + parentDirectory);
    }

    @ApiOperation(value = "System.getProperty(\"user.dir\")", notes = "", produces = "application/json")
    @RequestMapping(value = "/userDir", method = RequestMethod.GET)
    public R<HashMap<String, String>> userDir() {
        HashMap<String, String> map = new HashMap<>();
        String currentWorkingDirectory = System.getProperty("user.dir");
        // 获取上一级目录
        File currentDirectory = new File(currentWorkingDirectory);
        String parentDirectory = currentDirectory.getParent();
        map.put("System.getProperty(user.dir)", currentWorkingDirectory);
        map.put("System.getProperty(user.dir)的上一级", parentDirectory);
        return R.ok(map);
    }

    //@ApiOperation(value = " 本机Mysql备份 【发送Feign请求上传minio(yml的上传配置)】 未指定文件夹前缀则放入“通用”文件夹", notes = "Mysql备份", produces = "application/json")
    //@RequestMapping(value = "/mysqlBackup", method = RequestMethod.GET)
    //public R<String> mysqlBackup(@RequestParam(defaultValue = "通用(未指定前缀)") String uplaodPrefix, @RequestParam(required = false) String zipPassword) {
    //    return mysqlBackupByUrl(dbUrl, dbUsername, dbPassword, dbDriverClassName, null, null, uplaodPrefix, zipPassword, null, null, null, null, null, null, null, null, null);
    //}
    //
    public String mysqlBackup(@RequestParam(defaultValue = "通用(未指定前缀)") String uplaodPrefix, @RequestParam(required = false) String zipPassword,
                              String toEmail,
                              String osstype, String ossbucket, //osstype开始的5个参数如果无值，则走默认yml配置上传；有值则走传入的值
                              String ossaccesskey, String osssecretkey,
                              String ossendpoint, String cloudurl, String authcod, String localsavepath, String configname) {
        return mysqlBackupByUrl(dbUrl, dbUsername, dbPassword, "", null, null, uplaodPrefix, zipPassword, toEmail, osstype, ossbucket, ossaccesskey, osssecretkey, ossendpoint, cloudurl, authcod, localsavepath, configname);
    }

    //@ApiOperation(value = "Mysql备份By连接名和密码【发送Feign请求上传minio(yml的上传配置)】", notes = "Mysql备份", produces = "application/json")
    //@RequestMapping(value = "/mysqlBackupByUrl", method = RequestMethod.GET)
    //public R<String> mysqlBackupByUrl(String dbUrl, String dbUsername, String dbPassword, String dbDriverClassName, String uplaodPrefix, String zipPassword) {
    //    return mysqlBackupByUrl(dbUrl, dbUsername, dbPassword, dbDriverClassName, null, null, uplaodPrefix, zipPassword, null, null, null, null, null, null, null, null, null);
    //}

    @Resource
    private UtsDatabaseService utsDatabaseService;

    /**
     * 通过 JDBC URL 备份 MySQL 数据库并上传，支持多种上传方式，失败或成功都会发送邮件通知
     */
    public String mysqlBackupByUrl(
            String dbUrl, String dbUsername, String dbPassword, String dbDriverClassName, String includetables, String excludetables,
            String uploadPrefix, String zipPassword, String toEmail,
            String osstype, String ossBucket, String ossAccessKey,
            String ossSecretKey, String ossEndpoint,
            String cloudUrl, String authCode, String localSavePath, String configname) {
        LoginUser loginUser = null;
        try {
            loginUser = saRedisService.getLoginUser();
        } catch (Exception e) {
            log.info("吞掉这个LoginUser异常，不进行任何处理！" + e.getMessage());
        }
        long startTime = System.currentTimeMillis();           // 记录总耗时
        String exportFilePath = null;                          // 备份文件路径
        String targetEmail = StringUtils.defaultIfBlank(toEmail, this.toEmail);  // 最终邮件接收者
        boolean success = false;                               // 标记是否成功
        String fileUrl = null;                                 // 上传后文件 URL
        String fileSizeMB = null;                              // 备份文件大小（MB）
        String downloadUrl = null;                             // 最终下载地址

        // 新增: 用于保存备份信息的Map
        Map<String, Object> backupInfo = new HashMap<>();

        try {
            // 1. 构造数据源
            PrintColor.red("本次备份数据库地址: " + dbUrl);
            Map<String, Object> dataSource = utsDatabaseService.getDataSource(
                    null, true, null, dbUrl, dbUsername, dbPassword, dbDriverClassName);
            HikariDataSource hikariDataSource = (HikariDataSource) dataSource.get("dataSource");
            // 2. 确定本地保存路径
            String savePath = "";//保存路径，可能来自前端指定；否则使用默认系统路径
            if (StringUtils.isBlank(localSavePath)) {
                savePath = System.getProperty("user.dir");
            }

            // 如果目录不存在或不可写，则立即抛错
            File saveDir = new File(savePath);
            if (!saveDir.exists() || !saveDir.isDirectory() || !saveDir.canWrite()) {
                throw new RuntimeException("本地保存目录不可用，请检查路径是否存在且具备写权限：" + savePath);
            }

            // 3. 执行备份到 ZIP（自动关闭 Connection）
            long backupStart = System.currentTimeMillis();
            //MysqlExport exporter = new MysqlExport(conn, savePath);
            //exportFilePath = exporter.export(zipPassword);
            try (Connection conn = hikariDataSource.getConnection()) {
                if (conn == null) {
                    throw new RuntimeException("获取数据库连接失败，请检查 URL、用户名或密码");
                }
                PrintColor.green("------------------成功建立连接，开始备份-------------------");

                DatabaseExport.DbType dbType = DatabaseExport.DbType.MYSQL;
                if (dbUrl.contains(MyConstant.SQLSERVER)) {
                    dbType = DatabaseExport.DbType.SQL_SERVER;
                }
                DatabaseExport mysqlExport = new DatabaseExport(conn, savePath, dbType);
                // 修改：调用导出方法并获取详细信息
                Map<String, Object> exportResult = mysqlExport.exportWithDetails(zipPassword, includetables, excludetables);
                exportFilePath = (String) exportResult.get("filePath");
                // 保存备份详情
                backupInfo.putAll(exportResult);
            }
            long backupEnd = System.currentTimeMillis();
            long backupTime = backupEnd - backupStart;
            backupInfo.put("backupTime", backupTime);
            PrintColor.red("备份完成，耗时 " + backupTime + " 毫秒，文件路径：" + exportFilePath);

            // 4. 计算文件大小
            long fileSizeBytes = Files.size(Paths.get(exportFilePath));
            fileSizeMB = String.format("%.2f", fileSizeBytes / 1024.0 / 1024.0);
            backupInfo.put("fileSizeMB", fileSizeMB);
            PrintColor.red("备份文件大小：" + fileSizeMB + " MB");

            // 5. 构造上传客户端
            OkHttpClient client = new OkHttpClient.Builder()
                    .readTimeout(5, TimeUnit.MINUTES)
                    .build();
            String weekday = LocalDate.now().getDayOfWeek().name();  // 星期几英文
            if (StringUtils.isBlank(uploadPrefix)) {
                uploadPrefix = "通用(未指定前缀)";
            }

            // 6. 选择上传方式
            long uploadStart = System.currentTimeMillis();
            if (StringUtils.isNotBlank(cloudUrl)) {
                // 6.1 调用 HTTP 接口上传
                HttpUrl.Builder urlB = Objects.requireNonNull(HttpUrl.parse(cloudUrl)).newBuilder();
                if (!cloudUrl.contains("dir=")) {
                    urlB.addQueryParameter("dir", "backup/" + uploadPrefix);
                }
                if (!cloudUrl.contains("osstype=")) {
                    urlB.addQueryParameter("osstype", "minio");
                }
                if (!cloudUrl.contains("filename=")) {
                    urlB.addQueryParameter("filename", weekday);
                }
                String apiUrl = urlB.build().toString();

                File f = new File(exportFilePath);
                RequestBody fileBody = RequestBody.create(okhttp3.MediaType.parse("application/zip"), f);
                RequestBody body = new MultipartBody.Builder()
                        .setType(MultipartBody.FORM)
                        .addFormDataPart("file", f.getName(), fileBody)
                        .build();
                Request req = new Request.Builder()
                        .url(apiUrl)
                        .header("authcode", StringUtils.defaultString(authCode))
                        .post(body)
                        .build();
                Response resp = client.newCall(req).execute();
                if (!resp.isSuccessful()) {
                    throw new RuntimeException("云上传 HTTP 失败：" + resp.message());
                }
                JSONObject jo = JSONObject.parseObject(resp.body().string());
                if (jo.getIntValue("code") != 200) {
                    throw new RuntimeException("云上传接口返回失败：" + jo);
                }
                JSONObject data = jo.getJSONObject("data");
                fileUrl = data.getString("fileurl");
                PrintColor.red("上传完成(指定 API)： " + fileUrl);

            } else if (StringUtils.isNotBlank(osstype)) {
                // 6.2 自定义 OSS 上传
                R<String> r = fileController.uploadByPathCustom(
                        exportFilePath, ossBucket,
                        "backup/" + uploadPrefix, weekday,
                        osstype, ossAccessKey, ossSecretKey, ossEndpoint
                );
                fileUrl = r.getData();
                PrintColor.red("上传完成(自定义 OSS)： " + fileUrl);
            }
            long uploadEnd = System.currentTimeMillis();
            long uploadTime = uploadEnd - uploadStart;
            backupInfo.put("uploadTime", uploadTime);
            PrintColor.red("上传耗时：" + uploadTime + " 毫秒");

            // 7. 构造下载地址（仅针对 HTTP API 上传）
            if (StringUtils.isNotBlank(cloudUrl)) {
                //String pre = cloudUrl.replace("/upload", "/proxy");
                //String path = new URL(fileUrl).getPath();
                ////String rel = path.startsWith("/") ? path.substring(1) : path;
                //downloadUrl = pre + path;
                downloadUrl = fileUrl;
            } else {
                downloadUrl = fileUrl;
            }

            // 8. 构建 HTML 邮件内容
            String dbInfo = dbUrl.split("//")[1].split("\\?")[0];
            String subject = "MySQL 备份成功 - " + configname + " - " + dbInfo;

            // 使用收集的信息构建更丰富的邮件内容
            StringBuilder html = new StringBuilder("<html><body")
                    .append(" style='font-family:Arial,sans-serif;'>")
                    .append("<h2 style='color:#2E8B57;'>备份成功通知</h2>")
                    .append("<table style='border-collapse:collapse;width:100%;font-size:14px;'>")
                    .append(tr("数据库地址：", dbInfo));

            // 添加备份详情
            html.append(tr("备份时间：", DateUtil.now("yyyy-MM-dd HH:mm:ss")))
                    .append(tr("总耗时：", formatDuration(System.currentTimeMillis() - startTime)))
                    .append(tr("备份阶段耗时：", formatDuration((long) backupInfo.get("backupTime"))))
                    .append(tr("上传阶段耗时：", formatDuration((long) backupInfo.get("uploadTime"))));

            // 显示表信息
            @SuppressWarnings("unchecked")
            List<String> tableList = (List<String>) backupInfo.get("exportedTables");
            int tableCount = tableList != null ? tableList.size() : 0;

            html.append(tr("备份表数量：", String.valueOf(tableCount)));

            // 如果表比较多，只显示前10个并添加"等"
            if (tableList != null && !tableList.isEmpty()) {
                String tablesStr;
                if (tableList.size() <= 10) {
                    tablesStr = String.join(", ", tableList);
                } else {
                    tablesStr = String.join(", ", tableList.subList(0, 10)) + " 等" + tableList.size() + "张表";
                }
                html.append(tr("备份表列表：", tablesStr));
            }

            // 文件信息
            html.append(tr("文件路径：", exportFilePath))
                    .append(tr("文件大小：", fileSizeMB + " MB"));

            // 如果有原始文件大小信息，添加压缩比
            if (backupInfo.containsKey("originalSizeMB")) {
                String originalSizeMB = (String) backupInfo.get("originalSizeMB");
                html.append(tr("压缩前大小：", originalSizeMB + " MB"));

                // 计算压缩比
                try {
                    double original = Double.parseDouble(originalSizeMB);
                    double compressed = Double.parseDouble(fileSizeMB);
                    double ratio = (1 - compressed / original) * 100;
                    if (ratio > 0) {
                        html.append(tr("压缩率：", String.format("%.2f%%", ratio)));
                    }
                } catch (NumberFormatException e) {
                    // 忽略解析异常
                }
            }

            // 下载链接
            html.append(tr("下载地址：", "<a href='" + downloadUrl + "'>" + downloadUrl + "</a>"));

            // 添加备份配置信息
            html.append("<tr><td colspan='2'><hr/></td></tr>")
                    .append("<tr><td colspan='2'><b>备份配置信息</b></td></tr>");

            if (StringUtils.isNotBlank(includetables)) {
                html.append(tr("包含的表：", includetables));
            }
            if (StringUtils.isNotBlank(excludetables)) {
                html.append(tr("排除的表：", excludetables));
            }

            html.append("</table>")
                    .append("<p style='font-size:12px;color:#999;'>此邮件为自动发送，请勿回复。</p>")
                    .append("</body></html>");

            // 9. 发送成功邮件
            mailController.sendEmail(targetEmail, subject, html.toString());
            log.info("MySQL 备份成功邮件：{}", html);

            // 10. 埋点事件跟踪上报到 https://regapi.inksyun.com
            try {
                JSONObject trackJson = new JSONObject();
                //trackJson.put("app", configname);
                trackJson.put("eventcode", "backup_complete");
                //trackJson.put("eventname", "数据库备份成功"); //eventname eventdesc是在/tracking时通过eventcode关联查出来的不用设置
                trackJson.put("eventdesc", subject);
                trackJson.put("pagepath", "/S34M07B1/backup");
                trackJson.put("description", downloadUrl);
                trackJson.put("env", "sa");
                if (loginUser != null) {
                    trackJson.put("userid", loginUser.getUserid());
                    trackJson.put("username", loginUser.getUsername());
                    trackJson.put("realname", loginUser.getRealname());
                    trackJson.put("company", loginUser.getTenantinfo().getCompany());
                }
                // 构造上报请求体
                RequestBody trackBody = RequestBody.create(
                        okhttp3.MediaType.parse("application/json;charset=UTF-8"), trackJson.toJSONString());

                // 构造请求
                Request trackReq = new Request.Builder()
                        .url("https://regapi.inksyun.com/S21M07B1/tracking")
                        .header("Content-Type", "application/json;charset=UTF-8")
                        .post(trackBody)
                        .build();
                // 打印最终发送内容
                log.info("发送事件追踪body: " + trackJson.toJSONString());
                try (Response resp = client.newCall(trackReq).execute()) {
                    log.info("事件追踪HTTP状态: " + resp.code());
                    String respBody = resp.body() != null ? resp.body().string() : null;
                    log.info("事件追踪响应体: " + respBody);
                    if (!resp.isSuccessful()) {
                        log.info("事件追踪请求未成功！");
                    }
                } catch (Exception ex) {
                    log.info("事件追踪请求异常: " + ex.getMessage());
                }

            } catch (Exception ex) {
                PrintColor.red("事件跟踪上报失败：" + ex.getMessage());
            }

            long endTime = System.currentTimeMillis();
            PrintColor.zi("备份完成，总耗时：" + (endTime - startTime) + " 毫秒\n下载地址：" + downloadUrl);
            return "备份完成，总耗时：" + (endTime - startTime) + " 毫秒\n下载地址：" + downloadUrl;

        } catch (Exception e) {
            // 进入异常说明备份失败，必须删除该文件
            if (StringUtils.isNotBlank(exportFilePath) && Files.exists(Paths.get(exportFilePath))) {
                try {
                    Files.delete(Paths.get(exportFilePath));
                    PrintColor.red("备份失败，已删除临时文件：" + exportFilePath);
                } catch (IOException ioEx) {
                    PrintColor.red("删除临时文件失败：" + ioEx.getMessage());
                }
            }
            // 异常时记录日志并发失败邮件
            PrintColor.red("备份失败：" + e.getMessage());
            try {
                // 构建失败邮件内容
                String dbInfo = dbUrl.split("//")[1].split("\\?")[0];
                String subject = "MySQL 备份失败 - " + configname + " - " + dbInfo;
                StringBuilder html = new StringBuilder("<html><body")
                        .append(" style='font-family:Arial,sans-serif;'>")
                        .append("<h2 style='color:#B22222;'>备份失败通知</h2>")
                        .append("<table style='border-collapse:collapse;width:100%;font-size:14px;'>")
                        .append(tr("数据库地址：", dbInfo))
                        .append(tr("错误信息：", e.getMessage()))
                        .append(tr("失败时间：", DateUtil.now("yyyy-MM-dd HH:mm:ss")));

                // 添加已收集的信息（如果有）
                if (!backupInfo.isEmpty()) {
                    html.append("<tr><td colspan='2'><hr/></td></tr>")
                            .append("<tr><td colspan='2'><b>部分执行信息</b></td></tr>");

                    @SuppressWarnings("unchecked")
                    List<String> tableList = (List<String>) backupInfo.get("exportedTables");
                    if (tableList != null && !tableList.isEmpty()) {
                        html.append(tr("已处理表数量：", String.valueOf(tableList.size())));
                    }

                    if (backupInfo.containsKey("backupTime")) {
                        html.append(tr("备份阶段耗时：", formatDuration((long) backupInfo.get("backupTime"))));
                    }
                }

                html.append("</table>")
                        .append("<p style='font-size:12px;color:#999;'>此邮件为自动发送，请勿回复。</p>")
                        .append("</body></html>");
                mailController.sendEmail(targetEmail, subject, html.toString());
                log.error("MySQL 备份失败邮件：{}", html);
                // 失败事件跟踪上报 埋点事件跟踪上报到 https://regapi.inksyun.com
                try {
                    OkHttpClient client = new OkHttpClient();
                    JSONObject trackJson = new JSONObject();
                    //trackJson.put("app", configname);
                    trackJson.put("eventcode", "backup_fail");
                    //trackJson.put("eventname", "数据库备份失败"); //eventname eventdesc是在/tracking时通过eventcode关联查出来的不用设置
                    trackJson.put("eventdesc", subject);
                    trackJson.put("pagepath", "/S34M07B1/backup");
                    trackJson.put("description", downloadUrl);
                    trackJson.put("env", "sa");
                    if (loginUser != null) {
                        trackJson.put("userid", loginUser.getUserid());
                        trackJson.put("username", loginUser.getUsername());
                        trackJson.put("realname", loginUser.getRealname());
                        trackJson.put("company", loginUser.getTenantinfo().getCompany());
                    }

                    RequestBody trackBody = RequestBody.create(
                            okhttp3.MediaType.parse("application/json;charset=UTF-8"), trackJson.toJSONString());
                    Request trackReq = new Request.Builder()
                            .url("https://regapi.inksyun.com/S21M07B1/tracking")
                            .header("Content-Type", "application/json;charset=UTF-8")
                            .post(trackBody)
                            .build();
                    client.newCall(trackReq).execute().close();
                } catch (Exception ex) {
                    PrintColor.red("事件跟踪上报失败：" + ex.getMessage());
                }
            } catch (Exception mailEx) {
                PrintColor.red("发送失败邮件异常：" + mailEx.getMessage());
            }
            return e.getMessage();
        } finally {
            // 10. 删除临时文件（如果使用默认路径）
            if (StringUtils.isNotBlank(exportFilePath) && StringUtils.isBlank(localSavePath) && Files.exists(Paths.get(exportFilePath))) {
                FileHelper.delete(exportFilePath);
            }
            // 11. 打印总耗时
            long totalEnd = System.currentTimeMillis();
            PrintColor.red("整个备份任务总耗时：" + (totalEnd - startTime) + " 毫秒");
        }
    }


    /**
     * 通过SQL内容还原数据库，支持MySQL和SQLServer，执行过程和结果会发送邮件通知
     *
     * @param url             数据库连接URL
     * @param username        数据库用户名
     * @param password        数据库密码
     * @param driverclassname 数据库驱动类名
     * @param sqlContent      SQL内容字符串（备份的SQL语句）
     * @return 还原结果信息
     */
    public String restoreBySql(LoginUser loginUser, String url, String username, String password, String driverclassname, String sqlContent, String toEmail) {
        long startTime = System.currentTimeMillis();           // 记录总耗时
        // 邮件接收者

        // 用于保存还原信息的Map
        Map<String, Object> restoreInfo = new HashMap<>();

        // 用于跟踪创建的表
        Set<String> createdTables = new HashSet<>();

        List<String> failedStatements = null;
        try {
            // 1. 验证参数
            if (StringUtils.isBlank(sqlContent)) {
                throw new RuntimeException("SQL内容不能为空");
            }

            // 2. 构造数据源
            PrintColor.red("本次还原数据库地址: " + url);
            Map<String, Object> dataSource = utsDatabaseService.getDataSource(
                    null, true, null, url, username, password, driverclassname);
            HikariDataSource hikariDataSource = (HikariDataSource) dataSource.get("dataSource");

            // 3. 执行SQL还原
            long restoreStart = System.currentTimeMillis();

            // 分割SQL语句（按;分割，但忽略引号内的分号）
            List<String> sqlStatements = splitSqlStatements(sqlContent);
            int totalStatements = sqlStatements.size();
            int executedStatements = 0;
            failedStatements = new ArrayList<>();

            try (Connection conn = hikariDataSource.getConnection()) {
                if (conn == null) {
                    throw new RuntimeException("获取数据库连接失败，请检查 URL、用户名或密码");
                }

                // 设置自动提交为false，使用事务执行
                boolean originalAutoCommit = conn.getAutoCommit();
                conn.setAutoCommit(false);

                PrintColor.green("------------------成功建立连接，开始还原-------------------");

                try (Statement stmt = conn.createStatement()) {
                    // 设置超时时间（5分钟）
                    stmt.setQueryTimeout(300);

                    for (String sql : sqlStatements) {
                        if (StringUtils.isBlank(sql.trim())) {
                            continue;
                        }

                        try {
                            stmt.execute(sql);
                            executedStatements++;

                            // 检测是否为创建表的SQL
                            String upperSql = sql.toUpperCase().trim();
                            if (upperSql.startsWith("CREATE TABLE")) {
                                // 提取表名
                                String tableName = extractTableName(sql);
                                if (tableName != null) {
                                    createdTables.add(tableName);
                                    PrintColor.green("检测到创建表: " + tableName);
                                }
                            }

                            // 每50条语句打印一次进度
                            if (executedStatements % 50 == 0) {
                                PrintColor.green("已执行 " + executedStatements + "/" + totalStatements + " 条SQL语句");
                            }
                        } catch (SQLException e) {
                            // 记录失败的SQL
                            failedStatements.add(sql + " --错误: " + e.getMessage());
                            PrintColor.red("执行SQL失败: " + e.getMessage());
                        }
                    }

                    // 如果有失败的语句，回滚事务
                    if (!failedStatements.isEmpty()) {
                        conn.rollback();
                        throw new RuntimeException("有 " + failedStatements.size() + " 条SQL语句执行失败，已回滚所有操作");
                    }

                    // 提交事务
                    conn.commit();

                    // 恢复原始自动提交设置
                    conn.setAutoCommit(originalAutoCommit);
                }
            }

            long restoreEnd = System.currentTimeMillis();
            long restoreTime = restoreEnd - restoreStart;
            restoreInfo.put("restoreTime", restoreTime);
            restoreInfo.put("totalStatements", totalStatements);
            restoreInfo.put("executedStatements", executedStatements);
            restoreInfo.put("createdTables", createdTables);

            PrintColor.red("还原完成，耗时 " + restoreTime + " 毫秒，共执行 " + executedStatements + " 条SQL语句，创建了 " + createdTables.size() + " 张表");

            // 4. 构建邮件内容
            String dbInfo = url.split("//")[1].split("\\?")[0];
            String subject = ipAddress + "：数据库还原成功 - " + dbInfo;

            StringBuilder html = new StringBuilder("<html><body")
                    .append(" style='font-family:Arial,sans-serif;'>")
                    .append("<h2 style='color:#2E8B57;'>还原成功通知</h2>")
                    .append("<table style='border-collapse:collapse;width:100%;font-size:14px;'>")
                    .append(tr("数据库地址：", dbInfo))
                    .append(tr("还原时间：", DateUtil.now("yyyy-MM-dd HH:mm:ss")))
                    .append(tr("总耗时：", formatDuration(System.currentTimeMillis() - startTime)))
                    //.append(tr("还原阶段耗时：", formatDuration(restoreTime)))
                    //.append(tr("总SQL语句数：", String.valueOf(totalStatements)))
                    //.append(tr("创建表数量：", String.valueOf(createdTables.size())));
                    .append(tr("执行SQL语句数：", String.valueOf(executedStatements)));

            // 添加创建的表列表（如果有）
            if (!createdTables.isEmpty()) {
                html.append("<tr><td style='padding:8px;border:1px solid #ddd;' valign='top'><b>创建的表：</b></td>")
                        .append("<td style='padding:8px;border:1px solid #ddd;'>");

                // 按字母顺序排序表名
                List<String> sortedTables = new ArrayList<>(createdTables);
                Collections.sort(sortedTables);

                for (int i = 0; i < sortedTables.size(); i++) {
                    if (i > 0) {
                        html.append(", ");
                        // 每10个表名换一行
                        if (i % 10 == 0) {
                            html.append("<br/>");
                        }
                    }
                    html.append(sortedTables.get(i));
                }
                html.append("</td></tr>");
            }

            html.append("</table>")
                    .append("<p style='font-size:12px;color:#999;'>此邮件为自动发送，请勿回复。</p>")
                    .append("</body></html>");

            // 5. 发送成功邮件
            mailController.sendEmail(toEmail, subject, html.toString());
            log.info("数据库还原成功邮件：{}", html);

            long endTime = System.currentTimeMillis();
            PrintColor.zi("还原完成，总耗时：" + (endTime - startTime) + " 毫秒");
            // 記錄日志到Sa_Log
            // 记录拉取日志记录
            SaLogPojo saLogPojo = new SaLogPojo();
            saLogPojo.setLogtype("DB_Backup");
            saLogPojo.setLoglevel("INFO");
            saLogPojo.setModule("sauts");//所属模块
            saLogPojo.setUserid(loginUser.getUserid());
            saLogPojo.setRealname(loginUser.getRealname());
            // 动态获取request
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes())
                    .getRequest();
            saLogPojo.setIpaddress(IpUtils.getIpAddr(request)); // 外网IP
            saLogPojo.setHttpmethod("POST");
            //请求的URL地址
            saLogPojo.setRequesturl("/S34M07B1/restore");
            // 在“发送成功邮件”之前，构建完整的备份信息 message
            String message = "还原结果：成功" +
                    "；数据库：" + dbInfo +
                    "；总耗时：" + formatDuration(endTime - startTime) +
                    "；执行SQL语句数：" + executedStatements;
            saLogPojo.setMessage(message);
            saLogService.insert(saLogPojo);
            return "还原完成，总耗时：" + (endTime - startTime) + " 毫秒，共执行 " + executedStatements + " 条SQL语句";


        } catch (Exception e) {
            // 异常时记录日志并发送失败邮件
            PrintColor.red("还原失败：" + e.getMessage());
            // ① 构建失败日志对象
            SaLogPojo saLogPojo = new SaLogPojo();
            saLogPojo.setLogtype("DB_Backup");
            saLogPojo.setLoglevel("ERROR");
            saLogPojo.setModule("sauts");
            saLogPojo.setUserid(loginUser.getUserid());
            saLogPojo.setRealname(loginUser.getRealname());
            HttpServletRequest currentRequest = ((ServletRequestAttributes) RequestContextHolder
                    .currentRequestAttributes()).getRequest();
            saLogPojo.setIpaddress(IpUtils.getIpAddr(currentRequest));
            saLogPojo.setHttpmethod("POST");
            saLogPojo.setRequesturl("/S34M07B1/restore");

// ② 构建失败 message
            String dbInfoFail = url.split("//")[1].split("\\?")[0];
            StringBuilder failMsgSb = new StringBuilder();
            failMsgSb.append("还原结果：失败")
                    .append("；数据库：").append(dbInfoFail)
                    .append("；错误信息：").append(e.getMessage())
                    .append("；已执行语句数：")
                    .append(restoreInfo.getOrDefault("executedStatements", 0));

// 如果有失败语句，逐条加入
            if (!failedStatements.isEmpty()) {
                failMsgSb.append("；失败语句数：").append(failedStatements.size())
                        .append("；失败语句列表(前十条)：")
                        // 限制最多打印前10条示例，防止过长
                        .append(failedStatements.stream()
                                .limit(10)
                                .collect(Collectors.joining(" || ")));
            }

            String failMessage = failMsgSb.toString();
            saLogPojo.setMessage(failMessage);
            // ③ 插入失败日志
            saLogService.insert(saLogPojo);
            try {
                // 构建失败邮件内容
                String dbInfo = url.split("//")[1].split("\\?")[0];
                String subject = ipAddress + "：数据库还原失败 - " + dbInfo;
                StringBuilder html = new StringBuilder("<html><body")
                        .append(" style='font-family:Arial,sans-serif;'>")
                        .append("<h2 style='color:#B22222;'>还原失败通知</h2>")
                        .append("<table style='border-collapse:collapse;width:100%;font-size:14px;'>")
                        .append(tr("数据库地址：", dbInfo))
                        .append(tr("错误信息：", e.getMessage()))
                        .append(tr("失败时间：", DateUtil.now("yyyy-MM-dd HH:mm:ss")))
                        .append(tr("失败语句数：", String.valueOf(failedStatements.size())))
                        .append(tr("失败语句列表(前十条)：", failedStatements.stream()
                                .limit(10)
                                .collect(Collectors.joining(" || "))));

                // 添加已收集的信息（如果有）
                if (!restoreInfo.isEmpty()) {
                    html.append("<tr><td colspan='2'><hr/></td></tr>")
                            .append("<tr><td colspan='2'><b>部分执行信息</b></td></tr>");

                    if (restoreInfo.containsKey("restoreTime")) {
                        html.append(tr("还原阶段耗时：", formatDuration((long) restoreInfo.get("restoreTime"))));
                    }

                    if (restoreInfo.containsKey("totalStatements") && restoreInfo.containsKey("executedStatements")) {
                        html.append(tr("总SQL语句数：", String.valueOf(restoreInfo.get("totalStatements"))))
                                .append(tr("已执行SQL语句数：", String.valueOf(restoreInfo.get("executedStatements"))));
                    }

                    // 添加创建表的信息（如果有）
                    if (restoreInfo.containsKey("createdTables")) {
                        @SuppressWarnings("unchecked")
                        Set<String> tables = (Set<String>) restoreInfo.get("createdTables");
                        if (!tables.isEmpty()) {
                            html.append(tr("已创建表数量：", String.valueOf(tables.size())));
                        }
                    }
                }

                html.append("</table>")
                        .append("<p style='font-size:12px;color:#999;'>此邮件为自动发送，请勿回复。</p>")
                        .append("</body></html>");

                mailController.sendEmail(toEmail, subject, html.toString());
                log.error("数据库还原失败邮件：{}", html);
            } catch (Exception mailEx) {
                PrintColor.red("发送失败邮件异常：" + mailEx.getMessage());
            }
            return e.getMessage();
        } finally {
            long totalEnd = System.currentTimeMillis();
            PrintColor.red("整个还原任务总耗时：" + (totalEnd - startTime) + " 毫秒");

        }
    }

    /**
     * 分割SQL语句，考虑引号内的分号不作为分隔符
     *
     * @param sqlContent 完整的SQL内容
     * @return SQL语句列表
     */
    private List<String> splitSqlStatements(String sqlContent) {
        List<String> statements = new ArrayList<>();
        StringBuilder currentStatement = new StringBuilder();
        boolean inQuote = false;
        char quoteChar = 0;

        for (int i = 0; i < sqlContent.length(); i++) {
            char c = sqlContent.charAt(i);

            // 处理引号
            if ((c == '\'' || c == '"') && (i == 0 || sqlContent.charAt(i - 1) != '\\')) {
                if (!inQuote) {
                    inQuote = true;
                    quoteChar = c;
                } else if (c == quoteChar) {
                    inQuote = false;
                }
            }

            // 处理分号
            if (c == ';' && !inQuote) {
                statements.add(currentStatement.toString().trim());
                currentStatement = new StringBuilder();
            } else {
                currentStatement.append(c);
            }
        }

        // 添加最后一条语句（如果有）
        if (currentStatement.length() > 0) {
            statements.add(currentStatement.toString().trim());
        }

        return statements;
    }

    /**
     * 从CREATE TABLE语句中提取表名
     *
     * @param sql CREATE TABLE SQL语句
     * @return 表名
     */
    private String extractTableName(String sql) {
        try {
            String upperSql = sql.toUpperCase().trim();
            if (!upperSql.startsWith("CREATE TABLE")) {
                return null;
            }

            String afterCreateTable = upperSql.substring("CREATE TABLE".length()).trim();
            if (afterCreateTable.startsWith("IF NOT EXISTS")) {
                afterCreateTable = afterCreateTable.substring("IF NOT EXISTS".length()).trim();
            }

            String tableName;
            if (afterCreateTable.startsWith("`") || afterCreateTable.startsWith("\"") || afterCreateTable.startsWith("[")) {
                char quoteChar = afterCreateTable.charAt(0);
                char endQuoteChar;
                switch (quoteChar) {
                    case '`':
                        endQuoteChar = '`';
                        break;
                    case '"':
                        endQuoteChar = '"';
                        break;
                    case '[':
                        endQuoteChar = ']';
                        break;
                    default:
                        endQuoteChar = quoteChar;
                }

                // 使用正则表达式提取最后一个被引号包裹的内容
                String regex = "\\Q" + quoteChar + "\\E(.*?)\\Q" + endQuoteChar + "\\E";
                Pattern pattern = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
                Matcher matcher = pattern.matcher(afterCreateTable);
                String lastMatch = null;
                while (matcher.find()) {
                    lastMatch = matcher.group(1); // 取最后一个匹配项
                }

                if (lastMatch != null) {
                    tableName = lastMatch;
                } else {
                    int spacePos = afterCreateTable.indexOf(' ');
                    tableName = spacePos > 0 ? afterCreateTable.substring(0, spacePos) : afterCreateTable;
                }
            } else {
                int spacePos = afterCreateTable.indexOf(' ');
                tableName = spacePos > 0 ? afterCreateTable.substring(0, spacePos) : afterCreateTable;

                int dotPos = tableName.indexOf('.');
                if (dotPos > 0) {
                    tableName = tableName.substring(dotPos + 1);
                }
            }

            return tableName;
        } catch (Exception e) {
            PrintColor.red("提取表名时出错: " + e.getMessage());
            return null;
        }
    }

    // 辅助：生成表格行，统一样式
    private String tr(String key, String value) {
        return "<tr>"
                + "<td style='border:1px solid #ddd;padding:8px;background:#f9f9f9;'>" + key + "</td>"
                + "<td style='border:1px solid #ddd;padding:8px;'>" + value + "</td>"
                + "</tr>";
    }

    /**
     * 格式化耗时
     *
     * @param millis 毫秒值
     * @return 格式化后的时间
     */
    private String formatDuration(long millis) {
        if (millis < 1000) {
            return millis + " 毫秒";
        } else if (millis < 60000) {
            return String.format("%.2f 秒", millis / 1000.0);
        } else {
            long minutes = millis / 60000;
            long seconds = (millis % 60000) / 1000;
            return minutes + " 分 " + seconds + " 秒";
        }
    }

    //public R<String> mysqlBackupByUrl(String dbUrl, String dbUsername, String dbPassword, String uplaodPrefix, String zipPassword,
    //                                  String toEmail,
    //                                  String osstype, String ossbucket, //osstype开始的5个参数如果无值，则走默认yml配置上传；有值则走传入的值
    //                                  String ossaccesskey, String osssecretkey,
    //                                  String ossendpoint, String cloudurl, String authcod, // 本地保存路径
    //                                  String localsavepath) {
    //    // 定义锁、获取redis锁等逻辑略，以下为核心备份逻辑
    //    String exportFilePath = null; // 将exportFilePath声明在try块外部
    //    long startTime = System.currentTimeMillis(); // 记录整个方法开始时间
    //    try {
    //        String jdbcUrl = dbUrl + "&user=" + dbUsername + "&password=" + dbPassword;
    //        PrintColor.red("本次备份数据库地址:" + jdbcUrl);
    //        Connection connection = JdbcConnection.getConnection(jdbcUrl);
    //        // 若未传入本地保存地址，则默认保存到当前工作目录(inks-service-std-uts下，且上传后将文件删除），有传入savePath则不删除
    //        if (StringUtils.isBlank(localsavepath)) {
    //            localsavepath = System.getProperty("user.dir");
    //        }
    //        PrintColor.green("------------------成功建立连接,开始备份-------------------");
    //
    //        // 备份操作耗时
    //        long backupStartTime = System.currentTimeMillis();
    //        MysqlExport mysqlExport = new MysqlExport(connection, localsavepath);
    //        // 导出数据库到文件  .zip文件
    //        exportFilePath = mysqlExport.export(zipPassword);
    //        long backupEndTime = System.currentTimeMillis();
    //        PrintColor.red("备份完成，耗时：" + (backupEndTime - backupStartTime) + " 毫秒");
    //
    //        // 上传操作耗时
    //        long uploadStartTime = System.currentTimeMillis();
    //        OkHttpClient client = new OkHttpClient.Builder()
    //                .readTimeout(5, TimeUnit.MINUTES)
    //                .build();
    //        // 获取文件大小 单位字节转MB
    //        long fileSize = Files.size(Paths.get(exportFilePath));
    //        String fileSizeInMB = String.format("%.2f", fileSize / 1024.0 / 1024.0);
    //        PrintColor.red("备份文件大小：" + fileSizeInMB + " MB");
    //        PrintColor.red("上传開始 文件地址exportFilePath:" + exportFilePath);
    //
    //        // 获取星期几的英文全称
    //        String objectname = LocalDate.now().getDayOfWeek().name();
    //        // 默认前缀
    //        if (StringUtils.isBlank(uplaodPrefix)) {
    //            uplaodPrefix = "通用(未指定前缀)";
    //        }
    //
    //        String fileurl = "";
    //        // 如果cloudurl有值，则使用指定API接口上传，且在请求头中加入授权码authcod
    //        if (StringUtils.isNotBlank(cloudurl)) {//假设：http://*************:10684/File/upload
    //            boolean hasBucket = cloudurl.contains("bucket=");
    //            boolean hasDir = cloudurl.contains("dir=");
    //            boolean hasOsstype = cloudurl.contains("osstype=");
    //            boolean hasObjectname = cloudurl.contains("objectname=");
    //            // 若缺少任一必要参数则拼接默认值：&bucket=utils&dir=mysql_backup&osstype=minio
    //            HttpUrl.Builder urlBuilder = Objects.requireNonNull(HttpUrl.parse(cloudurl)).newBuilder();
    //            //if (!hasBucket) { //bucket参数还是用默认的，否则可能和/downloadUrl的桶不匹配
    //            //    urlBuilder.addQueryParameter("bucket", "utils");
    //            //}
    //            if (!hasDir) {
    //                urlBuilder.addQueryParameter("dir", "backup" + "/" + uplaodPrefix);
    //            } else {
    //                // 获取原始参数中的dir值并追加uploadPrefix
    //                String dirValue = HttpUrl.parse(cloudurl).queryParameter("dir");
    //                if (StringUtils.isNotBlank(dirValue) && !dirValue.contains("/" + uplaodPrefix)) {
    //                    dirValue = dirValue.endsWith("/") ? dirValue + uplaodPrefix : dirValue + "/" + uplaodPrefix;
    //                    urlBuilder.setQueryParameter("dir", dirValue);
    //                }
    //            }
    //            if (!hasOsstype) {
    //                urlBuilder.addQueryParameter("osstype", "minio");
    //            }
    //            if (!hasObjectname) {
    //                urlBuilder.addQueryParameter("objectname", objectname);
    //            }
    //            cloudurl = urlBuilder.build().toString();
    //            // 读取文件内容 根据文件地址
    //            File file = new File(exportFilePath);
    //            RequestBody fileBody = RequestBody.create(okhttp3.MediaType.parse("application/zip"), file);
    //
    //            RequestBody requestBody = new MultipartBody.Builder()
    //                    .setType(MultipartBody.FORM)
    //                    .addFormDataPart("file", file.getName(), fileBody)
    //                    .build();
    //            Request request = new Request.Builder()
    //                    .url(cloudurl)
    //                    .header("authcode", authcod == null ? "" : authcod)
    //                    .post(requestBody)
    //                    .build();
    //            Response response = client.newCall(request).execute();
    //            if (response.isSuccessful()) {
    //                String fileResponse = response.body().string();//返回示例：{"code":200,"msg":"上传成功","data":{"bucketname":"utils","dirname":"mysql_backup/20250422","filename":"5a861fe77c046abb6bf9.zip","fileoriname":"db-dump-2025-04-22-inksuts.zip","filesize":35697,"contenttype":"application/zip","filesuffix":"zip","storage":"minio","fileurl":"http://dev.inksyun.com:9080/utils/mysql_backup/20250422/5a861fe77c046abb6bf9.zip","content":null,"img":null}}
    //                JSONObject jsonObject = JSONObject.parseObject(fileResponse);
    //                if (Integer.valueOf(200).equals(jsonObject.getInteger("code"))) {
    //                    fileurl = jsonObject.getJSONObject("data").getString("fileurl");
    //                    PrintColor.red("上传完成(指定API上传)：" + fileurl);
    //                } else {
    //                    PrintColor.red("云上传接口返回失败：" + jsonObject);
    //                    fileurl = "云上传接口返回失败：" + jsonObject;
    //                }
    //            } else {
    //                throw new Exception("云上传HTTP失败：" + response.message());
    //            }
    //
    //        }
    //        // cloudurl为空，osstype不为空则走传入的自定义上传配置
    //        else if (StringUtils.isNotBlank(osstype)) {
    //            R<String> stringR = fileController.uploadByPathCustom(exportFilePath, ossbucket, "backup/" + uplaodPrefix, objectname,
    //                    osstype, ossaccesskey, osssecretkey, ossendpoint);
    //            fileurl = stringR.getData();
    //            PrintColor.red("上传完成(自定义OSS上传配置)：" + fileurl);
    //        }
    //
    //        // 提取cloudurl的前缀 只要?之前的部分 http://*************:10684/File/upload?bucket=utils&dir=mysql_backup&osstype=minio
    //        String downloadPrefix = cloudurl.substring(0, cloudurl.indexOf("?")).replace("/upload", "/downloadUrl");
    //        // 提取 fileurl 中的相对路径部分，从 backup 开始 http://dev.inksyun.com:9080/utils/backup/inks001/TUESDAY.zip
    //        String path = new URL(fileurl).getPath(); // /utils/backup/inks001/TUESDAY.zip
    //        String relativePath = path.substring(path.indexOf("/", 1)); // 去掉第一个路径段 获取相对路径 /backup/inks001/TUESDAY.zip
    //        // 拼接 downloadurl 结果为 http://*************:10684/File/downloadUrl/backup/inks001/TUESDAY.zip
    //        String downloadurl = downloadPrefix + relativePath;
    //        long uploadEndTime = System.currentTimeMillis();
    //        PrintColor.red("上传完成，耗时：" + (uploadEndTime - uploadStartTime) + " 毫秒");
    //
    //        // 发送邮件操作耗时
    //        long mailStartTime = System.currentTimeMillis();
    //        String[] urlParts = dbUrl.split("//"); // 拆分URL
    //        String dbInfo = urlParts[1].split("\\?")[0]; // 提取主机名、端口号和数据库名部分
    //        String subject = ipAddress + ": Mysql备份--" + dbInfo;
    //        String content = "<html>" +
    //                "<body style='font-family: Arial, sans-serif;'>" +
    //                "<h2 style='color: #2E8B57;'>MySQL备份通知</h2>" +
    //                "<p>您好，</p>" +
    //                "<p style='font-size: 14px;'>MySQL备份已经成功完成，详情如下：</p>" +
    //                "<table style='border-collapse: collapse; width: 100%; font-size: 14px;'>" +
    //                "<tr>" +
    //                "<td style='border: 1px solid #dddddd; padding: 8px; background-color: #f9f9f9;'>数据库地址：</td>" +
    //                "<td style='border: 1px solid #dddddd; padding: 8px;'>" + dbInfo + "</td>" +
    //                "</tr>" +
    //                "<tr>" +
    //                "<td style='border: 1px solid #dddddd; padding: 8px; background-color: #f9f9f9;'>文件名：</td>" +
    //                "<td style='border: 1px solid #dddddd; padding: 8px;'>" + exportFilePath + "</td>" +
    //                "</tr>" +
    //                "<tr>" +
    //                "<td style='border: 1px solid #dddddd; padding: 8px; background-color: #f9f9f9;'>文件大小：</td>" +
    //                "<td style='border: 1px solid #dddddd; padding: 8px;'>" + fileSizeInMB + " MB</td>" +
    //                "</tr>" +
    //                "<tr>" +
    //                "<td style='border: 1px solid #dddddd; padding: 8px; background-color: #f9f9f9;'>下载地址：</td>" +
    //                "<td style='border: 1px solid #dddddd; padding: 8px;'><a href='" + downloadurl + "'>" + downloadurl + "</a></td>" +
    //                "</tr>" +
    //                "</table>" +
    //                "<br>" +
    //                "<p style='font-size: 12px; color: #999999;'>此邮件为自动发送，请勿回复。</p>" +
    //                "</body>" +
    //                "</html>";
    //
    //
    //        //默认发送给yml${spring.mail.toEmail} 如果传入toEmail则发送给传入的邮箱
    //        if (StringUtils.isBlank(toEmail)) {
    //            toEmail = this.toEmail;
    //        }
    //        mailController.sendEmail(toEmail, subject, content);
    //        long mailEndTime = System.currentTimeMillis();
    //        PrintColor.zi("邮件发送成功，耗时：" + (mailEndTime - mailStartTime) + " 毫秒");
    //
    //        String resultMsg = "备份完成，耗时：" + (backupEndTime - backupStartTime) + " 毫秒"
    //                + "\n\n文件大小：" + fileSizeInMB + " MB"
    //                + "\n\nminio或云上传地址为：" + fileurl
    //                + "\n\n本次备份数据库地址:" + jdbcUrl;
    //        PrintColor.zi(resultMsg);
    //        return R.ok(resultMsg);
    //    } catch (Exception e) {
    //        return R.fail(e.getMessage());
    //    } finally {
    //        // 释放锁（若有）
    //        // 删除备份文件 (若不是指定保存到本地才删除)
    //        if (exportFilePath != null && StringUtils.isBlank(localsavepath)) {
    //            FileHelper.delete(exportFilePath);
    //        }
    //        long endTime = System.currentTimeMillis();
    //        PrintColor.red("整个备份任务耗时：" + (endTime - startTime) + " 毫秒");
    //    }
    //}


    //public R<String> mysqlBackupByUrl(String dbUrl, String dbUsername, String dbPassword, String uplaodPrefix, String zipPassword,
    //                                  String toEmail,
    //                                  String osstype, String ossbucket, //osstype开始的5个参数如果无值，则走默认yml配置上传；有值则走传入的值
    //                                  String ossaccesskey, String osssecretkey,
    //                                  String ossendpoint, String cloudurl, String authcod) {
    //    //// 定义锁的名称和锁过期时间3000秒
    //    //String lockName = "mysql_backup_lock";
    //    //long expireTime = 3000000L;
    //    //PrintColor.green("------------------停在这里说明redis锁死-------------------");
    //    //// 尝试获取锁
    //    //Boolean success = redisTemplate.opsForValue().setIfAbsent(lockName, "locked", Duration.ofMillis(expireTime));
    //    //if (Boolean.FALSE.equals(success)) {
    //    //    return R.fail("有其他备份任务正在进行，请稍后再试。");
    //    //}
    //    String exportFilePath = null; // 将exportFilePath声明在try块外部
    //    long startTime = System.currentTimeMillis(); // 记录整个方法开始时间
    //    try {
    //        String jdbcUrl = dbUrl + "&user=" + dbUsername + "&password=" + dbPassword;
    //        PrintColor.red("本次备份数据库地址:" + jdbcUrl);
    //        Connection connection = JdbcConnection.getConnection(jdbcUrl);
    //        // 保存到当前工作目录
    //        String currentWorkingDirectory = System.getProperty("user.dir");
    //        PrintColor.green("------------------成功建立连接,开始备份-------------------");
    //
    //        // 备份操作耗时
    //        long backupStartTime = System.currentTimeMillis();
    //        MysqlExport mysqlExport = new MysqlExport(connection, currentWorkingDirectory);
    //        // 导出数据库到文件  .zip文件
    //        exportFilePath = mysqlExport.export(zipPassword);
    //        long backupEndTime = System.currentTimeMillis();
    //        PrintColor.red("备份完成，耗时：" + (backupEndTime - backupStartTime) + " 毫秒");
    //
    //        // 上传操作耗时
    //        long uploadStartTime = System.currentTimeMillis();
    //        OkHttpClient client = new OkHttpClient.Builder()
    //                .readTimeout(5, TimeUnit.MINUTES)
    //                .build();
    //        // 获取文件大小 单位字节转MB
    //        long fileSize = Files.size(Paths.get(exportFilePath));
    //        String fileSizeInMB = String.format("%.2f", fileSize / 1024.0 / 1024.0);
    //        PrintColor.red("备份文件大小：" + fileSizeInMB + " MB");
    //        PrintColor.red("上传開始 文件地址exportFilePath:" + exportFilePath);
    //
    //
    //        // 获取星期几的英文全称 例如：星期一 Monday 星期二 Tuesday 星期三 Wednesday 星期四 Thursday 星期五 Friday 星期六 Saturday 星期日 Sunday
    //        String objectname = LocalDate.now().getDayOfWeek().name();
    //        // 上传文件到minio 自定义前缀 如 n001  则存到minio地址为 http://dev.inksyun.com:9080/utils/mysql/n001/W112.zip
    //        String fileUrl = null;//上传后的文件网址
    //        if (StringUtils.isBlank(uplaodPrefix)) {
    //            uplaodPrefix = "通用(未指定前缀)";
    //        }
    //        if (StringUtils.isBlank(osstype)) {//osstype为空则走默认yml配置上传
    //            R<FileInfo> fileInfoR = fileController.uploadByPath(exportFilePath, "utils", "mysql_backup/" + uplaodPrefix, objectname, "minio");//token不需要传
    //            FileInfo fileInfo = fileInfoR.getData();
    //            PrintColor.red("上传完成：" + fileInfo);
    //            fileUrl = fileInfo.getFileurl();
    //        } else {
    //            //osstype不为空则走传入的值上传
    //            R<String> stringR = fileController.uploadByPathCustom(exportFilePath, ossbucket, "mysql_backup/" + uplaodPrefix, objectname, osstype, ossaccesskey, osssecretkey, ossendpoint);
    //            fileUrl = stringR.getData();
    //            PrintColor.red("上传完成(自定义上传配置)：" + fileUrl);
    //        }
    //        long uploadEndTime = System.currentTimeMillis();
    //        PrintColor.red("上传完成，耗时：" + (uploadEndTime - uploadStartTime) + " 毫秒");
    //
    //        // 发送邮件操作耗时
    //        long mailStartTime = System.currentTimeMillis();
    //        String[] urlParts = dbUrl.split("//"); // 拆分URL
    //        String dbInfo = urlParts[1].split("\\?")[0]; // 提取主机名、端口号和数据库名部分
    //        String subject = ipAddress + ": Mysql备份--" + dbInfo;
    //        String content = "<html>" +
    //                "<body style='font-family: Arial, sans-serif;'>" +
    //                "<h2 style='color: #2E8B57;'>MySQL备份通知</h2>" +
    //                "<p>您好，</p>" +
    //                "<p style='font-size: 14px;'>MySQL备份已经成功完成，详情如下：</p>" +
    //                "<table style='border-collapse: collapse; width: 100%; font-size: 14px;'>" +
    //                "<tr>" +
    //                "<td style='border: 1px solid #dddddd; padding: 8px; background-color: #f9f9f9;'>数据库地址：</td>" +
    //                "<td style='border: 1px solid #dddddd; padding: 8px;'>" + dbInfo + "</td>" +
    //                "</tr>" +
    //                "<tr>" +
    //                "<td style='border: 1px solid #dddddd; padding: 8px; background-color: #f9f9f9;'>文件名：</td>" +
    //                "<td style='border: 1px solid #dddddd; padding: 8px;'>" + exportFilePath + "</td>" +
    //                "</tr>" +
    //                "<tr>" +
    //                "<td style='border: 1px solid #dddddd; padding: 8px; background-color: #f9f9f9;'>文件大小：</td>" +
    //                "<td style='border: 1px solid #dddddd; padding: 8px;'>" + fileSizeInMB + " MB</td>" +
    //                "</tr>" +
    //                "<tr>" +
    //                "<td style='border: 1px solid #dddddd; padding: 8px; background-color: #f9f9f9;'>Minio地址：</td>" +
    //                "<td style='border: 1px solid #dddddd; padding: 8px;'><a href='" + fileUrl + "'>" + fileUrl + "</a></td>" +
    //                "</tr>" +
    //                "</table>" +
    //                "<br>" +
    //                "<p style='font-size: 12px; color: #999999;'>此邮件为自动发送，请勿回复。</p>" +
    //                "</body>" +
    //                "</html>";
    //
    //
    //        //默认发送给yml${spring.mail.toEmail} 如果传入toEmail则发送给传入的邮箱
    //        if (StringUtils.isBlank(toEmail)) {
    //            toEmail = this.toEmail;
    //        }
    //        mailController.sendEmail(toEmail, subject, content);
    //        long mailEndTime = System.currentTimeMillis();
    //        PrintColor.zi("邮件发送成功，耗时：" + (mailEndTime - mailStartTime) + " 毫秒");
    //
    //        String resultMsg = "备份完成，耗时：" + (backupEndTime - backupStartTime) + " 毫秒"
    //                + "\n\n文件大小：" + fileSizeInMB + " MB"
    //                + "\n\nminio地址为：" + fileUrl
    //                + "\n\n本次备份数据库地址:" + jdbcUrl;
    //        PrintColor.zi(resultMsg);
    //        return R.ok(resultMsg);
    //    } catch (Exception e) {
    //        return R.fail(e.getMessage());
    //    } finally {
    //        //// 释放锁
    //        //redisTemplate.delete(lockName);
    //        // 删除备份文件
    //        if (exportFilePath != null) {
    //            FileHelper.delete(exportFilePath);
    //        }
    //        long endTime = System.currentTimeMillis();
    //        PrintColor.red("整个备份任务耗时：" + (endTime - startTime) + " 毫秒");
    //    }
    //}


    @ApiOperation(value = "Mysql备份(Linux:仅将备份文件放入此jar包同级backup文件夹中 Win:和src同级,即inks-service-sa-fw下一级)", notes = "Mysql备份", produces = "application/json")
    @RequestMapping(value = "/mysqlBackupOnly", method = RequestMethod.GET)
    public R<String> mysqlBackupOnly() {
        PrintColor.green("------------------准备建立连接,停在这里说明连接失败-------------------");
        try {
            // 建立(要备份)数据库连接
            String jdbcUrl = dbUrl + "&user=" + dbUsername + "&password=" + dbPassword;
            Connection connection = JdbcConnection.getConnection(jdbcUrl);
            // 创建导出对象，传入参数：数据库连接对象，要导出的数据库名，导出的路径
            // 保存到当前工作目录 TODO 注意!currentWorkingDirectory当前工作目录: win:D:\nanno\WORK-CODE\GitLab\inks-service-sa-fw  Linux好像在上一层 和jar包同级
            String currentWorkingDirectory = System.getProperty("user.dir");

            // 构建 "backup" 文件夹的路径
            String backupFolderPath = currentWorkingDirectory + File.separator + "backup";
            PrintColor.green("------------------成功建立连接,开始备份-------------------");
            MysqlExport mysqlExport = new MysqlExport(connection, backupFolderPath);
            // 导出数据库到文件 exportFilePath是zip文件的全路径
            String exportFilePath = mysqlExport.export(null);
//            // 删除生成的备份.sql文件
//            FileHelper.delete(exportFilePath);

            return R.ok("备份成功,文件名为:" + exportFilePath);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "Mysql备份返回文件流,返回后删除文件(Linux:仅将备份文件放入此jar包同级backup文件夹中 Win:和src同级,即inks-service-sa-fw下一级)", notes = "Mysql备份", produces = "application/json")
    @RequestMapping(value = "/mysqlBackupStream", method = RequestMethod.GET)
    public ResponseEntity<byte[]> mysqlBackupStream() {
        PrintColor.green("------------------准备建立连接,停在这里说明连接失败-------------------");
        try {
            // 建立(要备份)数据库连接
            String jdbcUrl = dbUrl + "&user=" + dbUsername + "&password=" + dbPassword;
            Connection connection = JdbcConnection.getConnection(jdbcUrl);
            // 创建导出对象，传入参数：数据库连接对象，要导出的数据库名，导出的路径
            // 保存到当前工作目录 TODO 注意!currentWorkingDirectory当前工作目录: win:D:\nanno\WORK-CODE\GitLab\inks-service-sa-fw  Linux好像在上一层 和jar包同级
            String currentWorkingDirectory = System.getProperty("user.dir");

            // 构建 "backup" 文件夹的路径
            String backupFolderPath = currentWorkingDirectory + File.separator + "backup";
            PrintColor.green("------------------成功建立连接,开始备份-------------------");
            MysqlExport mysqlExport = new MysqlExport(connection, backupFolderPath);
            // 导出数据库到文件 exportFilePath是zip文件的全路径
            String exportFilePath = mysqlExport.export(null);

            // 读取备份文件内容为字节流
            File file = new File(exportFilePath);
            byte[] fileContent = Files.readAllBytes(file.toPath());

            // 转为流后删除生成的备份.sql文件
            FileHelper.delete(exportFilePath);

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", file.getName());

            // 返回响应实体
            return new ResponseEntity<>(fileContent, headers, HttpStatus.OK);
        } catch (Exception e) {
            // 将异常消息转换为字节数组
            byte[] errorMessageBytes = e.getMessage().getBytes(StandardCharsets.UTF_8);
            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.TEXT_PLAIN);
            // 返回异常消息的字节数组作为响应实体
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).headers(headers).body(errorMessageBytes);
        }
    }

    @ApiOperation(value = "手动执行数据库备份文件以恢复备份", notes = "", produces = "application/json")
    @GetMapping("/restoreBackup")
    public String restoreBackup() {
        PrintColor.green("------------------准备建立连接,停在这里说明连接失败-------------------");
        try {
            // 建立(要备份)数据库连接
            String jdbcUrl = dbUrl + "&user=" + dbUsername + "&password=" + dbPassword;
            Connection connection = JdbcConnection.getConnection(jdbcUrl);

            // 读取备份文件内容
            String backupFilePath = "backup" + File.separator + "db-dump-inkscmr4.sql";
            StringBuilder sqlBuilder = new StringBuilder();
            Files.lines(Paths.get(backupFilePath), StandardCharsets.UTF_8)
                    .filter(line -> !line.trim().startsWith("--") && !line.trim().startsWith("/*"))
                    .forEach(sqlBuilder::append);
            String sql = sqlBuilder.toString();

            // 创建 SQL 语句执行对象
            Statement stmt = connection.createStatement();

            // 执行备份文件中的 SQL 语句
            boolean result = stmt.execute(sql);

            connection.close();

            if (result) {
                return "备份文件成功恢复！";
            } else {
                return "备份文件恢复失败！";
            }
        } catch (SQLException | IOException e) {
            e.printStackTrace();
            return "恢复备份过程中出现错误：" + e.getMessage();
        }
    }

    @GetMapping("/restoreBackup22")
    public ResponseEntity<String> restoreBackup22() {
        try {
            // 建立(要备份)数据库连接
            String jdbcUrl = dbUrl + "&user=" + dbUsername + "&password=" + dbPassword;
            Connection connection = JdbcConnection.getConnection(jdbcUrl);

            // 读取备份文件内容
            String backupFilePath = "backup/db-dump-inkscmr4.sql";
            StringBuilder sqlBuilder = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new FileReader(backupFilePath))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    sqlBuilder.append(line).append("\n");
                }
            }

            // 执行备份文件中的 SQL 语句
            Statement statement = connection.createStatement();
            String[] sqlStatements = sqlBuilder.toString().split(";");
            for (String sql : sqlStatements) {
                statement.addBatch(sql);
            }
            int[] results = statement.executeBatch();
            connection.close();

            // 检查执行结果
            for (int result : results) {
                if (result == Statement.EXECUTE_FAILED) {
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                            .body("备份文件恢复失败！");
                }
            }

            return ResponseEntity.ok("备份文件成功恢复！");
        } catch (IOException | SQLException e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("恢复备份过程中出现错误：" + e.getMessage());
        }
    }

//    @ApiOperation(value = "Mysql备份By连接名和密码", notes = "Mysql备份", produces = "application/json")
//    @RequestMapping(value = "/mysqlBackupByUrl", method = RequestMethod.GET)
//// //@PreAuthorize(hasPermi = "CiInitLog.List")
//    public R<String> mysqlBackupByUrl(String dbUrl, String dbUsername, String dbPassword, String ossType) {
//        // 定义锁的名称和锁过期时间3000秒
//        String lockName = "mysql_backup_lock";
//        long expireTime = 3000000L; //
//        PrintColor.green("------------------停在这里说明redis锁死-------------------");
//        // 尝试获取锁
//        //Boolean success = redisTemplate.opsForValue().setIfAbsent(lockName, "locked", Duration.ofMillis(expireTime));
//        Boolean success = saRedisService.setIfAbsent(lockName, "locked", expireTime, TimeUnit.MILLISECONDS);
//        if (!success) {
//            return R.fail("Sa_Redis表锁死：有其他备份任务正在进行，请稍后再试。");
//        }
//        PrintColor.green("------------------准备建立连接,停在这里说明连接失败-------------------");
//        try {
//            // 建立(要备份)数据库连接
////            Connection connection = JdbcConnection.getConnection("192.168.99.111:53308", "inkssaas", "root", "asd@123456");
////            String jdbcUrl = "******************************************************************************************************************************************************************************************************************************";
//            String jdbcUrl = dbUrl + "&user=" + dbUsername + "&password=" + dbPassword;
//            PrintColor.red("本次备份数据库地址:" + jdbcUrl);
//            Connection connection = JdbcConnection.getConnection(jdbcUrl);
//            // 创建导出对象，传入参数：数据库连接对象，要导出的数据库名，导出的路径
//            // 保存到当前工作目录
//            String currentWorkingDirectory = System.getProperty("user.dir");
//            PrintColor.green("------------------成功建立连接,开始备份-------------------");
//            MysqlExport mysqlExport = new MysqlExport(connection, currentWorkingDirectory);
//            // 导出数据库到文件 exportFilePath是zip文件的全路径
//            String exportFilePath = mysqlExport.export();
//            // 上传.zip压缩文件到minio 先设置上传响应等待时间为5分钟
//            OkHttpClient client = new OkHttpClient.Builder()
//                    .readTimeout(5, TimeUnit.MINUTES)
//                    .build();
//            if (StringUtils.isBlank(ossType)) {
//                ossType = osstype;
//            }
//            R<FileInfo> fileInfoR = fileController.uploadByPath(exportFilePath, "utils", "mysql", ossType);
//            String fileUrl = fileInfoR.getData().getFileurl();
//            // .zip也删除 上传后再删除！！
//            FileHelper.delete(exportFilePath);
//            PrintColor.green("------------------.zip也删除:" + exportFilePath + "-------------------");
//            PrintColor.color("备份成功,文件名为:" + exportFilePath + "\nminio地址为：" + fileUrl);
//            // 发送邮件
//            String[] urlParts = dbUrl.split("//"); // 拆分URL
//            String dbInfo = urlParts[1].split("\\?")[0]; // 提取主机名、端口号和数据库名部分
//            String subject = ipAddress + ": Mysql备份--" + dbInfo;
//            //String content = "Mysql备份成功，文件名为：" + exportFilePath + "<br><br>minio地址为：" + fileUrl;
//            String content = "<html>" +
//                    "<body style='font-family: Arial, sans-serif;'>" +
//                    "<h2 style='color: #2E8B57;'>MySQL备份通知</h2>" +
//                    "<p>您好，</p>" +
//                    "<p style='font-size: 14px;'>MySQL备份已经成功完成，详情如下：</p>" +
//                    "<table style='border-collapse: collapse; width: 100%; font-size: 14px;'>" +
//                    "<tr>" +
//                    "<td style='border: 1px solid #dddddd; padding: 8px; background-color: #f9f9f9;'>文件名：</td>" +
//                    "<td style='border: 1px solid #dddddd; padding: 8px;'>" + exportFilePath + "</td>" +
//                    "</tr>" +
//                    "<tr>" +
//                    "<td style='border: 1px solid #dddddd; padding: 8px; background-color: #f9f9f9;'>Minio地址：</td>" +
//                    "<td style='border: 1px solid #dddddd; padding: 8px;'><a href='" + fileUrl + "'>" + fileUrl + "</a></td>" +
//                    "</tr>" +
//                    "</table>" +
//                    "<br>" +
//                    "<p style='font-size: 12px; color: #999999;'>此邮件为自动发送，请勿回复。</p>" +
//                    "</body>" +
//                    "</html>";
//            mailController.sendEmail(toEmail, subject, content);

    /// /            mailController.sendEmail("<EMAIL>", subject, content);
//            PrintColor.zi("邮件发送成功");
//            return R.ok("备份成功,文件名为:" + exportFilePath + "\nminio地址为：" + fileUrl);
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//        finally {
//            // 释放锁
//            saRedisService.delete(lockName);
//        }
//    }
    @ApiOperation(value = "上传Mysql备份文件By路径", notes = "Mysql备份", produces = "application/json")
    @RequestMapping(value = "/uploadMysql200MOkHttp", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "CiInitLog.List")
    public R<String> uploadMysql200MOkHttp(String path) throws IOException {

        // 上传.zip压缩文件到minio 设置响应等待时间为5分钟
        OkHttpClient client = new OkHttpClient.Builder()
                .readTimeout(5, TimeUnit.MINUTES)
                .build();
        // 创建请求体
        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("filepath", path)
                .addFormDataPart("bucket", "utils")
                .addFormDataPart("dir", "mysql_backup")
                .addFormDataPart("osstype", "minio")
                .build();
        // 构建请求
        Request request = new Request.Builder()
                .url("http://dev.inksyun.com:31080/utils/D96M16B1/uploadByPath")
                .headers(Headers.of("Authorization", "bcdb"))
                .post(requestBody)
                .build();
        // 发送请求并获取响应
        Response response = client.newCall(request).execute();
        // 处理响应结果
        String fileUrl = "";
        if (response.isSuccessful()) {
            String responseBody = response.body().string();
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode responseJson = objectMapper.readTree(responseBody);
            fileUrl = responseJson.get("data").get("fileurl").asText();
            System.out.println("fileUrl = " + fileUrl);
        } else {
            throw new IOException("Unexpected code " + response);
        }
        System.out.println("备份成功,文件名为:" + path + "\n\nminio地址为：" + fileUrl);
        return R.ok("备份成功,文件名为:" + path + "\n\nminio地址为：" + fileUrl);
    }

    @ApiOperation(value = "上传Mysql备份文件By路径", notes = "Mysql备份", produces = "application/json")
    @RequestMapping(value = "/uploadMysql200M", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "CiInitLog.List")
    public R<String> uploadMysql200M(String path, String ossType) {
        // 上传.zip压缩文件到minio
//        R<FileInfo> fileInfoR = fileController.uploadByPath(path, "utils", "mysql", "minio");
        if (StringUtils.isBlank(ossType)) {
            ossType = configManager.getOssType();
        }
        // 获取星期几的英文全称 例如：星期一 Monday 星期二 Tuesday 星期三 Wednesday 星期四 Thursday 星期五 Friday 星期六 Saturday 星期日 Sunday
        String objectname = LocalDate.now().getDayOfWeek().name();
        R<FileInfo> fileInfoR = fileController.uploadByPath(path, "mysql_backup", objectname, null);
        String fileUrl = fileInfoR.getData().getFileurl();
        System.out.println("备份成功,文件名为:" + path + "\n\nminio地址为：" + fileUrl);
        return R.ok("备份成功,文件名为:" + path + "\n\nminio地址为：" + fileUrl);
    }


}
