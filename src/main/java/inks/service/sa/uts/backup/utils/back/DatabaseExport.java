package inks.service.sa.uts.backup.utils.back;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import inks.service.sa.uts.backup.utils.ZipHelper;
import okhttp3.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.sql.*;
import java.sql.Connection;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 通用数据库备份工具，支持 MySQL 和 SQL Server
 * 免 mysqldump 和 sqlcmd 命令备份 SQL
 *
 * <AUTHOR> <EMAIL>
 * <AUTHOR> [你的邮箱]
 */
public class DatabaseExport {
    private final static Logger LOGGER = LoggerFactory.getLogger(DatabaseExport.class);

    /**
     * 数据库类型枚举
     */
    public enum DbType {
        MYSQL,
        SQL_SERVER
    }

    public static void main(String[] args) throws SQLException, IOException {
        //用户主目录：C:\Users\<USER>\nanno\WORK-CODE\inks-cloud-master  Linux: /当前目录路径
        String currentWorkingDirectory = System.getProperty("user.dir");
        System.out.println("userHome = " + userHome);
        System.out.println("currentWorkingDirectory = " + currentWorkingDirectory);

        // MySQL 连接示例
        Connection mysqlConnection = JdbcConnection.getConnection("192.168.99.111:53308", "inkssaas", "root", "asd@123456");
        DatabaseExport mysqlExport = new DatabaseExport(mysqlConnection, currentWorkingDirectory, DbType.MYSQL);
        String mysqlExportFilePath = mysqlExport.export(null, null, null);
        System.out.println("MySQL export file path = " + mysqlExportFilePath);

        // SQL Server 连接示例
        // Connection sqlServerConnection = JdbcConnection.getSqlServerConnection("192.168.99.100", "1433", "mydb", "sa", "password");
        // DatabaseExport sqlServerExport = new DatabaseExport(sqlServerConnection, currentWorkingDirectory, DbType.SQL_SERVER);
        // String sqlServerExportFilePath = sqlServerExport.export(null);
        // System.out.println("SQL Server export file path = " + sqlServerExportFilePath);

        // 上传备份文件到 MinIO 示例
        uploadToMinio(mysqlExportFilePath);
    }

    /**
     * 上传文件到MinIO服务器
     *
     * @param filePath 要上传的文件路径
     * @throws IOException 如果上传过程中发生错误
     */
    private static void uploadToMinio(String filePath) throws IOException {
        // 设置上传响应等待时间为5分钟(不设置会响应超时报错)
        OkHttpClient client = new OkHttpClient.Builder()
                .readTimeout(5, TimeUnit.MINUTES)
                .build();
        // 创建请求体
        RequestBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("filepath", filePath)
                .addFormDataPart("bucket", "utils")
                .addFormDataPart("dir", "database_backup")
                .addFormDataPart("osstype", "minio")
                .build();
        // 构建请求
        Request request = new Request.Builder()
                .url("http://dev.inksyun.com:31080/utils/D96M16B1/uploadByPath")
                .headers(Headers.of("Authorization", "bcdb"))
                .post(requestBody)
                .build();
        // 发送请求并获取响应
        Response response = client.newCall(request).execute();
        // 处理响应结果
        if (response.isSuccessful()) {
            String responseBody = response.body().string();
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode responseJson = objectMapper.readTree(responseBody);
            String fileUrl = responseJson.get("data").get("fileurl").asText();
            System.out.println("fileUrl = " + fileUrl);
        } else {
            throw new IOException("Unexpected code " + response);
        }
    }

    /**
     * 执行语句
     */
    private Statement stmt;

    /**
     * 数据库名
     */
    private String databaseName;

    /**
     * 导出 SQL 的目录
     */
    private String saveFolder;

    /**
     * 数据库类型
     */
    private DbType dbType;

    /**
     * 数据库连接
     */
    private Connection connection;

    /**
     * 创建数据库导出对象
     *
     * @param conn       数据库连接对象
     * @param saveFolder 保存目录
     * @param dbType     数据库类型
     */
    public DatabaseExport(Connection conn, String saveFolder, DbType dbType) throws SQLException {
        this.connection = conn;
        this.databaseName = conn.getCatalog();
        this.saveFolder = saveFolder;
        this.dbType = dbType;

        try {
            stmt = conn.createStatement(ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
        } catch (SQLException e) {
            LOGGER.error("创建Statement失败", e);
            throw e;
        }
    }

    /**
     * 创建 MySQL 数据库导出对象
     *
     * @param conn       数据库连接对象
     * @param saveFolder 保存目录
     */
    public DatabaseExport(Connection conn, String saveFolder) throws SQLException {
        this(conn, saveFolder, DbType.MYSQL);
    }

    private static final String SQL_START_PATTERN = "-- start";
    private static final String SQL_END_PATTERN = "-- end";

    /**
     * 获取当前数据库下的所有表名称
     *
     * @return List\<String\> 所有表名称
     */
    private List<String> getAllTables() {
        List<String> tables = new ArrayList<>();

        try {
            String sql;
            switch (dbType) {
                case MYSQL:
                    sql = "SHOW TABLE STATUS FROM `" + databaseName + "`;";
                    break;
                case SQL_SERVER:
                    // SQL Server查询所有用户表
                    sql = "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_CATALOG = '" + databaseName + "'";
                    break;
                default:
                    throw new UnsupportedOperationException("不支持的数据库类型: " + dbType);
            }

            JdbcReader.rsHandle(stmt, sql, rs -> {
                try {
                    while (rs.next()) {
                        if (dbType == DbType.MYSQL) {
                            tables.add(rs.getString("Name"));
                        } else { // SQL_SERVER
                            tables.add(rs.getString("TABLE_NAME"));
                        }
                    }
                } catch (SQLException e) {
                    LOGGER.error("获取表名失败", e);
                }
            });
        } catch (Exception e) {
            LOGGER.error("获取所有表失败", e);
        }

        return tables;
    }

    /**
     * 生成表创建语句
     *
     * @param table 表名
     * @return String 创建表的SQL语句
     */
    private String getTableInsertStatement(String table) {
        StringBuilder sql = new StringBuilder();

        try {
            switch (dbType) {
                case MYSQL:
                    JdbcReader.rsHandle(stmt, "SHOW CREATE TABLE `" + table + "`;", rs -> {
                        try {
                            while (rs.next()) {
                                String qtbl = rs.getString(1), query = rs.getString(2);
                                //如果表已经存在，则不会执行创建操作，而是跳过
                                query = query.trim().replace("CREATE TABLE", "CREATE TABLE IF NOT EXISTS");

                                sql.append("\n\n--");
                                sql.append("\n").append(SQL_START_PATTERN).append(" table dump: ").append(qtbl);
                                sql.append("\n--\n\n");
                                sql.append(query).append(";\n\n");
                            }

                            sql.append("\n\n--\n").append(SQL_END_PATTERN).append(" table dump: ").append(table).append("\n--\n\n");
                        } catch (SQLException e) {
                            LOGGER.error("获取MySQL表创建语句失败", e);
                        }
                    });
                    break;
                case SQL_SERVER:
                    // SQL Server 获取表结构
                    String sqlServerQuery = "SELECT OBJECT_DEFINITION(OBJECT_ID('" + table + "')) AS CreateStatement;";
                    JdbcReader.rsHandle(stmt, sqlServerQuery, rs -> {
                        try {
                            if (rs.next()) {
                                String createStatement = rs.getString("CreateStatement");
                                if (createStatement == null) {
                                    // 如果没有直接获取到创建语句，使用替代方法
                                    createStatement = generateSqlServerTableScript(table);
                                }

                                sql.append("\n\n--");
                                sql.append("\n").append(SQL_START_PATTERN).append(" table dump: ").append(table);
                                sql.append("\n--\n\n");
                                sql.append("IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[")
                                        .append(table).append("]') AND type in (N'U'))\nBEGIN\n");
                                sql.append(createStatement);
                                sql.append("\nEND;\n\n");
                            }

                            sql.append("\n\n--\n").append(SQL_END_PATTERN).append(" table dump: ").append(table).append("\n--\n\n");
                        } catch (SQLException e) {
                            LOGGER.error("获取SQL Server表创建语句失败", e);
                        }
                    });
                    break;
            }
        } catch (Exception e) {
            LOGGER.error("生成表创建语句失败: " + table, e);
        }

        return sql.toString();
    }

    /**
     * 生成SQL Server表创建脚本
     *
     * @param tableName 表名
     * @return 创建表的SQL脚本
     */
    private String generateSqlServerTableScript(String tableName) {
        StringBuilder script = new StringBuilder();
        script.append("CREATE TABLE [dbo].[").append(tableName).append("] (\n");

        try {
            // 获取表列信息
            String columnQuery = "SELECT c.name AS ColumnName, t.name AS TypeName, " +
                    "c.max_length, c.precision, c.scale, c.is_nullable, " +
                    "ISNULL(i.is_primary_key, 0) AS IsPrimaryKey " +
                    "FROM sys.columns c " +
                    "JOIN sys.types t ON c.user_type_id = t.user_type_id " +
                    "LEFT JOIN sys.index_columns ic ON ic.object_id = c.object_id AND ic.column_id = c.column_id " +
                    "LEFT JOIN sys.indexes i ON ic.object_id = i.object_id AND ic.index_id = i.index_id AND i.is_primary_key = 1 " +
                    "WHERE c.object_id = OBJECT_ID('" + tableName + "') " +
                    "ORDER BY c.column_id";

            ResultSet rs = stmt.executeQuery(columnQuery);

            boolean firstColumn = true;
            List<String> primaryKeys = new ArrayList<>();

            while (rs.next()) {
                if (!firstColumn) {
                    script.append(",\n");
                }
                firstColumn = false;

                String columnName = rs.getString("ColumnName");
                String typeName = rs.getString("TypeName");
                int maxLength = rs.getInt("max_length");
                int precision = rs.getInt("precision");
                int scale = rs.getInt("scale");
                boolean isNullable = rs.getBoolean("is_nullable");
                boolean isPrimaryKey = rs.getBoolean("IsPrimaryKey");

                if (isPrimaryKey) {
                    primaryKeys.add(columnName);
                }

                script.append("\t[").append(columnName).append("] [").append(typeName).append("]");

                // 添加类型属性
                if (typeName.equals("varchar") || typeName.equals("nvarchar") || typeName.equals("char") || typeName.equals("nchar")) {
                    if (maxLength == -1) {
                        script.append("(MAX)");
                    } else {
                        script.append("(").append(maxLength / (typeName.startsWith("n") ? 2 : 1)).append(")");
                    }
                } else if (typeName.equals("decimal") || typeName.equals("numeric")) {
                    script.append("(").append(precision).append(", ").append(scale).append(")");
                }

                // 添加可空性
                script.append(isNullable ? " NULL" : " NOT NULL");
            }
            rs.close();

            // 添加主键约束
            if (!primaryKeys.isEmpty()) {
                script.append(",\n\tCONSTRAINT [PK_").append(tableName).append("] PRIMARY KEY CLUSTERED (\n\t\t");
                for (int i = 0; i < primaryKeys.size(); i++) {
                    if (i > 0) {
                        script.append(",\n\t\t");
                    }
                    script.append("[").append(primaryKeys.get(i)).append("] ASC");
                }
                script.append("\n\t)");
            }

            script.append("\n)");

        } catch (SQLException e) {
            LOGGER.error("生成SQL Server表脚本失败: " + tableName, e);
            script.append("-- 无法生成表结构, 错误: ").append(e.getMessage());
        }

        return script.toString();
    }

    /**
     * 生成insert语句
     *
     * @param table 表名
     * @return String 插入数据的SQL语句
     */
    private String getDataInsertStatement(String table) {
        StringBuilder sql = new StringBuilder();

        try {
            String selectQuery;
            switch (dbType) {
                case MYSQL:
                    selectQuery = "SELECT * FROM `" + table + "`;";
                    break;
                case SQL_SERVER:
                    selectQuery = "SELECT * FROM [" + table + "];";
                    break;
                default:
                    return sql.toString();
            }

            JdbcReader.rsHandle(stmt, selectQuery, rs -> {
                try {
                    rs.last();
                    int rowCount = rs.getRow();
                    if (rowCount <= 0) {
                        // 如果结果集为空，直接返回
                        return;
                    }

                    sql.append("\n--").append("\n-- Inserts of ").append(table).append("\n--\n\n");

                    if (dbType == DbType.MYSQL) {
                        sql.append("\n/*!40000 ALTER TABLE `").append(table).append("` DISABLE KEYS */;\n");
                    } else {
                        sql.append("\n-- Disable constraints for faster bulk insert\n");
                        sql.append("SET IDENTITY_INSERT [").append(table).append("] ON;\n");
                    }

                    sql.append("\n--\n").append(SQL_START_PATTERN).append(" table insert : ").append(table).append("\n--\n");

                    ResultSetMetaData metaData = rs.getMetaData();
                    int columnCount = metaData.getColumnCount();

                    // 准备列名部分
                    if (dbType == DbType.MYSQL) {
                        sql.append("INSERT INTO `").append(table).append("`(");
                        for (int i = 0; i < columnCount; i++) {
                            sql.append("`").append(metaData.getColumnName(i + 1)).append("`, ");
                        }
                    } else { // SQL_SERVER
                        sql.append("INSERT INTO [").append(table).append("] (");
                        for (int i = 0; i < columnCount; i++) {
                            sql.append("[").append(metaData.getColumnName(i + 1)).append("], ");
                        }
                    }

                    sql.deleteCharAt(sql.length() - 1).deleteCharAt(sql.length() - 1).append(") VALUES \n");
                    rs.beforeFirst();

                    int batchSize = 0;
                    while (rs.next()) {
                        sql.append("(");
                        for (int i = 0; i < columnCount; i++) {
                            int columnType = metaData.getColumnType(i + 1), columnIndex = i + 1;

                            if (Objects.isNull(rs.getObject(columnIndex))) {
                                sql.append("NULL, ");
                            } else if (columnType == Types.INTEGER || columnType == Types.TINYINT || columnType == Types.BIT ||
                                    columnType == Types.NUMERIC || columnType == Types.DECIMAL ||
                                    columnType == Types.DOUBLE || columnType == Types.FLOAT) {
                                sql.append(rs.getObject(columnIndex)).append(", ");
                            } else if (columnType == Types.DATE || columnType == Types.TIME || columnType == Types.TIMESTAMP) {
                                if (dbType == DbType.MYSQL) {
                                    String val = rs.getString(columnIndex);
                                    if (val != null) {
                                        sql.append("'").append(val).append("', ");
                                    } else {
                                        sql.append("NULL, ");
                                    }
                                } else { // SQL_SERVER
                                    java.sql.Timestamp timestamp = rs.getTimestamp(columnIndex);
                                    if (timestamp != null) {
                                        sql.append("'").append(timestamp).append("', ");
                                    } else {
                                        sql.append("NULL, ");
                                    }
                                }
                            } else {
                                String val = rs.getString(columnIndex);
                                if (val != null) {
                                    if (dbType == DbType.MYSQL) {
                                        val = val.replace("'", "\\'");
                                    } else { // SQL_SERVER
                                        val = val.replace("'", "''");
                                    }
                                    sql.append("'").append(val).append("', ");
                                } else {
                                    sql.append("NULL, ");
                                }
                            }
                        }

                        sql.deleteCharAt(sql.length() - 1).deleteCharAt(sql.length() - 1);

                        // SQL Server批量插入语法与MySQL不同
                        if (dbType == DbType.MYSQL) {
                            sql.append(rs.isLast() ? ");" : "),\n");
                        } else { // SQL_SERVER
                            sql.append(");\n");
                            batchSize++;

                            // SQL Server有行数限制，每500行提交一次
                            if (batchSize >= 500 && !rs.isLast()) {
                                sql.append("\n-- Continuing inserts for ").append(table).append("\n");
                                sql.append("INSERT INTO [").append(table).append("] (");
                                for (int i = 0; i < columnCount; i++) {
                                    sql.append("[").append(metaData.getColumnName(i + 1)).append("], ");
                                }
                                sql.deleteCharAt(sql.length() - 1).deleteCharAt(sql.length() - 1).append(") VALUES \n");
                                batchSize = 0;
                            }
                        }
                    }
                } catch (SQLException e) {
                    LOGGER.error("生成数据插入语句失败: " + table, e);
                }
            });

            sql.append("\n--\n").append(SQL_END_PATTERN).append(" table insert : ").append(table).append("\n--\n");

            // 启用约束
            if (dbType == DbType.MYSQL) {
                sql.append("\n/*!40000 ALTER TABLE `").append(table).append("` ENABLE KEYS */;\n");
            } else { // SQL_SERVER
                sql.append("\nSET IDENTITY_INSERT [").append(table).append("] OFF;\n");
            }

        } catch (Exception e) {
            LOGGER.error("生成数据插入语句失败: " + table, e);
        }

        return sql.toString();
    }

    /**
     * 执行导出
     *
     * @param zipPassword ZIP压缩密码，可为null表示不加密
     * @return 打包的文件路径(将导出SQL保存到.sql文件中, 并压缩为.zip文件, 删除.sql文件, 返回.zip文件路径)
     */
    public String export(String zipPassword, String includetables, String excludetables) {
        String dbTypeStr = dbType == DbType.MYSQL ? "mysql" : "sqlserver";
        String fileName = "db-dump-" + DateUtil.now("yyyy-MM-dd") + "-" + databaseName + "-" + dbTypeStr + ".sql";
        String sqlFile = saveFolder + FileHelper_Claude.SEPARATOR + fileName;

        // 构建头部信息
        StringBuilder header = new StringBuilder();
        header.append("--\n-- Generated by Database Export Utility");
        header.append("\n-- Database Type: ").append(dbType);
        header.append("\n-- Date: ").append(DateUtil.now("d-M-Y H:m:s")).append("\n--");

        // 数据库特定的头部
        if (dbType == DbType.MYSQL) {
            header.append("\n\n/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;")
                    .append("\n/*!40101 SET NAMES utf8 */;\n/*!50503 SET NAMES utf8mb4 */;")
                    .append("\n/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;")
                    .append("\n/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;");
        } else { // SQL_SERVER
            header.append("\n\n-- SQL Server Export");
            header.append("\nUSE [").append(databaseName).append("];\n");
            header.append("\nSET NOCOUNT ON;\n");
            header.append("SET XACT_ABORT ON;\n");
            header.append("SET ANSI_NULLS ON;\n");
            header.append("SET QUOTED_IDENTIFIER ON;\n");
            header.append("SET ANSI_PADDING ON;\n");
            header.append("BEGIN TRANSACTION;\n");
        }

        // 保存头部到文件
        FileHelper_Claude.saveText(sqlFile, header.toString());

        System.out.println("------------------开始导出数据库 " + databaseName + " (" + dbType + ")---------------------");

        // 获取并过滤表列表
        List<String> tables = getAllTables();

        // 处理包含/排除的表名列表
        List<String> includeList = Optional.ofNullable(includetables)
                .map(s -> Arrays.stream(s.split(","))
                        .map(String::trim)
                        .collect(Collectors.toList()))
                .orElse(new ArrayList<>());

        List<String> excludeList = Optional.ofNullable(excludetables)
                .map(s -> Arrays.stream(s.split(","))
                        .map(String::trim)
                        .collect(Collectors.toList()))
                .orElse(new ArrayList<>());

        // 应用过滤条件
        List<String> filteredTables = tables;

        if (!includeList.isEmpty()) {
            filteredTables = tables.stream()
                    .filter(includeList::contains)
                    .collect(Collectors.toList());
        }

        if (!excludeList.isEmpty()) {
            filteredTables = filteredTables.stream()
                    .filter(tableName -> !excludeList.contains(tableName))
                    .collect(Collectors.toList());
        }

        // 输出过滤后的表信息
        System.out.println("应用过滤条件：包含表：" + includeList + "，排除表：" + excludeList);
        System.out.println("最终导出的表数量：" + filteredTables.size());

        // 开始导出数据
        int i = 0;
        StringBuilder sql = new StringBuilder();

        for (String tableName : filteredTables) {
            i++;
            System.out.println("------------------" + i + ". " + tableName + ": 开始导出表结构---------------------");
            sql.append(getTableInsertStatement(tableName.trim()));

            System.out.println("------------------" + i + ". " + tableName + ": 开始导出表数据---------------------");
            sql.append(getDataInsertStatement(tableName.trim()));

            // 追加内容到文件（每次循环保存）
            FileHelper_Claude.saveText(sqlFile, sql.toString(), true);
            sql.setLength(0); // 清空缓冲区
        }

        // 关闭数据库连接
        try {
            if (stmt != null) stmt.close();
        } catch (SQLException e) {
            LOGGER.error("关闭Statement失败", e);
        }

        // 添加数据库特定的尾部语句
        StringBuilder footer = new StringBuilder();
        if (dbType == DbType.MYSQL) {
            footer.append("\n/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;")
                    .append("\n/*!40014 SET FOREIGN_KEY_CHECKS=IF(@OLD_FOREIGN_KEY_CHECKS IS NULL, 1, @OLD_FOREIGN_KEY_CHECKS) */;")
                    .append("\n/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;");
        } else { // SQL_SERVER
            footer.append("\nCOMMIT TRANSACTION;\n");
            footer.append("SET ANSI_PADDING OFF;\n");
        }

        // 保存尾部到文件
        FileHelper_Claude.saveText(sqlFile, footer.toString(), true);

        // 压缩文件并清理
        String zipFilePath = sqlFile.replace(".sql", ".zip");
        if (StringUtils.isNotBlank(zipPassword)) {
            ZipHelper.zipWithPassword(sqlFile, zipFilePath, zipPassword);
        } else {
            ZipHelper.zip(sqlFile, zipFilePath);
        }

        // 删除SQL文件，只保留ZIP
        FileHelper_Claude.delete(sqlFile);

        System.out.println("------------------数据库 " + databaseName + " (" + dbType + ") 导出完成---------------------");

        // 返回压缩后的文件路径
        return zipFilePath;
    }

    /**
     * 执行导出并返回详细信息
     *
     * @param zipPassword ZIP压缩密码，可为null表示不加密
     * @param includetables 包含的表，逗号分隔
     * @param excludetables 排除的表，逗号分隔
     * @return 包含导出信息的Map
     */
    public Map<String, Object> exportWithDetails(String zipPassword, String includetables, String excludetables) {
        Map<String, Object> result = new HashMap<>();
        String dbTypeStr = dbType == DbType.MYSQL ? "mysql" : "sqlserver";
        String fileName = "db-dump-" + DateUtil.now("yyyy-MM-dd") + "-" + databaseName + "-" + dbTypeStr + ".sql";
        String sqlFile = saveFolder + FileHelper_Claude.SEPARATOR + fileName;

        // 记录原始SQL文件大小
        long originalSizeBytes = 0;

        // 构建头部信息
        StringBuilder header = new StringBuilder();
        header.append("--\n-- Generated by Database Export Utility");
        header.append("\n-- Database Type: ").append(dbType);
        header.append("\n-- Date: ").append(DateUtil.now("d-M-Y H:m:s")).append("\n--");

        // 数据库特定的头部
        if (dbType == DbType.MYSQL) {
            header.append("\n\n/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;")
                    .append("\n/*!40101 SET NAMES utf8 */;\n/*!50503 SET NAMES utf8mb4 */;")
                    .append("\n/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;")
                    .append("\n/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;");
        } else { // SQL_SERVER
            header.append("\n\n-- SQL Server Export");
            header.append("\nUSE [").append(databaseName).append("];\n");
            header.append("\nSET NOCOUNT ON;\n");
            header.append("SET XACT_ABORT ON;\n");
            header.append("SET ANSI_NULLS ON;\n");
            header.append("SET QUOTED_IDENTIFIER ON;\n");
            header.append("SET ANSI_PADDING ON;\n");
            header.append("BEGIN TRANSACTION;\n");
        }

        // 保存头部到文件
        FileHelper_Claude.saveText(sqlFile, header.toString());

        System.out.println("------------------开始导出数据库 " + databaseName + " (" + dbType + ")---------------------");

        // 获取并过滤表列表
        List<String> tables = getAllTables();
        result.put("totalTables", tables.size());

        // 处理包含/排除的表名列表
        List<String> includeList = Optional.ofNullable(includetables)
                .filter(StringUtils::isNotBlank)              // 只有非空白字符串才拆分
                .map(s -> Arrays.stream(s.split(","))
                        .map(String::trim)
                        .filter(StringUtils::isNotBlank)    // 过滤掉“,”导致的空元素
                        .collect(Collectors.toList()))
                .orElse(new ArrayList<>());

        List<String> excludeList = Optional.ofNullable(excludetables)
                .filter(StringUtils::isNotBlank)              // 只有非空白字符串才拆分
                .map(s -> Arrays.stream(s.split(","))
                        .map(String::trim)
                        .filter(StringUtils::isNotBlank)    // 过滤掉“,”导致的空元素
                        .collect(Collectors.toList()))
                .orElse(new ArrayList<>());

        // 应用过滤条件
        List<String> filteredTables = tables;

        if (CollectionUtils.isNotEmpty(includeList)) {
            filteredTables = tables.stream()
                    .filter(includeList::contains)
                    .collect(Collectors.toList());
        }

        if (CollectionUtils.isNotEmpty(excludeList)) {
            filteredTables = filteredTables.stream()
                    .filter(tableName -> !excludeList.contains(tableName))
                    .collect(Collectors.toList());
        }

        // 保存过滤信息
        result.put("includeFilter", includeList);
        result.put("excludeFilter", excludeList);

        // 输出过滤后的表信息
        System.out.println("应用过滤条件：包含表：" + includeList + "，排除表：" + excludeList);
        System.out.println("最终导出的表数量：" + filteredTables.size());

        // 记录每个表的行数
        Map<String, Integer> tableRowCounts = new HashMap<>();
        List<String> exportedTables = new ArrayList<>();

        // 开始导出数据
        int i = 0;
        StringBuilder sql = new StringBuilder();

        for (String tableName : filteredTables) {
            i++;
            exportedTables.add(tableName);

            System.out.println("------------------" + i + ". " + tableName + ": 开始导出表结构---------------------");
            String tableStructure = getTableInsertStatement(tableName.trim());
            sql.append(tableStructure);

            System.out.println("------------------" + i + ". " + tableName + ": 开始导出表数据---------------------");
            String tableData = getDataInsertStatement(tableName.trim());
            sql.append(tableData);

            // 统计表的行数（根据INSERT语句数量）
            int rowCount = countInsertStatements(tableData);
            tableRowCounts.put(tableName, rowCount);

            // 追加内容到文件（每次循环保存）
            FileHelper_Claude.saveText(sqlFile, sql.toString(), true);
            sql.setLength(0); // 清空缓冲区
        }

        // 关闭数据库连接
        try {
            if (stmt != null) stmt.close();
        } catch (SQLException e) {
            LOGGER.error("关闭Statement失败", e);
        }

        // 添加数据库特定的尾部语句
        StringBuilder footer = new StringBuilder();
        if (dbType == DbType.MYSQL) {
            footer.append("\n/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;")
                    .append("\n/*!40014 SET FOREIGN_KEY_CHECKS=IF(@OLD_FOREIGN_KEY_CHECKS IS NULL, 1, @OLD_FOREIGN_KEY_CHECKS) */;")
                    .append("\n/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;");
        } else { // SQL_SERVER
            footer.append("\nCOMMIT TRANSACTION;\n");
            footer.append("SET ANSI_PADDING OFF;\n");
        }

        // 保存尾部到文件
        FileHelper_Claude.saveText(sqlFile, footer.toString(), true);

        // 获取SQL文件大小
        try {
            originalSizeBytes = Files.size(Paths.get(sqlFile));
            String originalSizeMB = String.format("%.2f", originalSizeBytes / 1024.0 / 1024.0);
            result.put("originalSizeMB", originalSizeMB);
        } catch (IOException e) {
            LOGGER.error("获取SQL文件大小失败", e);
        }

        // 压缩文件并清理
        String zipFilePath = sqlFile.replace(".sql", ".zip");
        if (StringUtils.isNotBlank(zipPassword)) {
            ZipHelper.zipWithPassword(sqlFile, zipFilePath, zipPassword);
        } else {
            ZipHelper.zip(sqlFile, zipFilePath);
        }

        // 删除SQL文件，只保留ZIP
        FileHelper_Claude.delete(sqlFile);

        System.out.println("------------------数据库 " + databaseName + " (" + dbType + ") 导出完成---------------------");

        // 保存结果
        result.put("filePath", zipFilePath);
        result.put("exportedTables", exportedTables);
        result.put("tableRowCounts", tableRowCounts);
        result.put("totalExportedTables", exportedTables.size());
        result.put("totalRows", tableRowCounts.values().stream().mapToInt(Integer::intValue).sum());

        return result;
    }

    /**
     * 统计INSERT语句的数量
     * @param sql SQL语句
     * @return INSERT语句数量
     */
    private int countInsertStatements(String sql) {
        if (sql == null || sql.isEmpty()) {
            return 0;
        }

        // 简单计数法，可能不够精确但足够估算
        String[] insertStatements = sql.split("INSERT INTO");
        return insertStatements.length - 1; // 减去第一个空字符串
    }

}

/**
 * JDBC连接工具类
 */
class JdbcConnection {
    private static final Logger LOGGER = LoggerFactory.getLogger(JdbcConnection.class);

    /**
     * 获取MySQL连接
     */
    public static Connection getConnection(String host, String dbName, String username, String password) throws SQLException {
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
            String url = "jdbc:mysql://" + host + "/" + dbName
                    + "?characterEncoding=utf-8&useSSL=false&autoReconnect=true"
                    + "&zeroDateTimeBehavior=convertToNull&rewriteBatchedStatements=true"
                    + "&serverTimezone=Asia/Shanghai";
            return DriverManager.getConnection(url, username, password);
        } catch (ClassNotFoundException e) {
            LOGGER.error("MySQL驱动加载失败", e);
            throw new SQLException("MySQL驱动加载失败: " + e.getMessage());
        }
    }

    /**
     * 获取MySQL连接（通过JDBC URL）
     */
    public static Connection getConnection(String jdbcUrl) throws SQLException {
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
            return DriverManager.getConnection(jdbcUrl);
        } catch (ClassNotFoundException e) {
            LOGGER.error("MySQL驱动加载失败", e);
            throw new SQLException("MySQL驱动加载失败: " + e.getMessage());
        }
    }

    /**
     * 获取SQL Server连接
     */
    public static Connection getSqlServerConnection(String server, String port, String database, String username, String password) throws SQLException {
        try {
            Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
            String url = "jdbc:sqlserver://" + server + ":" + port + ";databaseName=" + database
                    + ";encrypt=false;trustServerCertificate=true";
            return DriverManager.getConnection(url, username, password);
        } catch (ClassNotFoundException e) {
            LOGGER.error("SQL Server驱动加载失败", e);
            throw new SQLException("SQL Server驱动加载失败: " + e.getMessage());
        }
    }
}

/**
 * JDBC结果集处理工具
 */
class JdbcReader {
    /**
     * 处理结果集的函数式接口
     */
    @FunctionalInterface
    public interface ResultSetHandler {
        void handle(ResultSet rs);
    }

    /**
     * 执行SQL查询并处理结果集
     */
    public static void rsHandle(Statement stmt, String sql, ResultSetHandler handler) {
        try (ResultSet rs = stmt.executeQuery(sql)) {
            handler.handle(rs);
        } catch (SQLException e) {
            LoggerFactory.getLogger(JdbcReader.class).error("执行SQL失败: " + sql, e);
        }
    }
}


/**
 * 日期工具类
 */
class DateUtil {
    /**
     * 获取当前时间的格式化字符串
     *
     * @param pattern 日期格式模式
     * @return 格式化后的当前时间字符串
     */
    public static String now(String pattern) {
        return new java.text.SimpleDateFormat(pattern).format(new java.util.Date());
    }
}

/**
 * 文件操作工具类
 */
class FileHelper_Claude {
    private static final Logger LOGGER = LoggerFactory.getLogger(FileHelper_Claude.class);

    /**
     * 系统文件分隔符
     */
    public static final String SEPARATOR = System.getProperty("file.separator");

    /**
     * 保存文本到文件
     *
     * @param filePath 文件路径
     * @param content  文件内容
     */
    public static void saveText(String filePath, String content) {
        saveText(filePath, content, false);
    }

    /**
     * 保存文本到文件，可选择是否追加模式
     *
     * @param filePath 文件路径
     * @param content  文件内容
     * @param append   是否追加到文件末尾
     */
    public static void saveText(String filePath, String content, boolean append) {
        try (java.io.FileWriter writer = new java.io.FileWriter(filePath, append)) {
            writer.write(content);
            writer.flush();
        } catch (Exception e) {
            LOGGER.error("保存文件失败: " + filePath, e);
        }
    }

    /**
     * 删除文件
     *
     * @param filePath 文件路径
     * @return 是否删除成功
     */
    public static boolean delete(String filePath) {
        File file = new File(filePath);
        if (file.exists()) {
            boolean result = file.delete();
            if (!result) {
                LOGGER.warn("删除文件失败: " + filePath);
            }
            return result;
        }
        return false;
    }
}
