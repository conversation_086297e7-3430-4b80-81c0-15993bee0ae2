package inks.service.sa.uts.backup.utils.back;

import org.junit.Test;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;

public class DualDriverSqlServerTest {

    // 官方驱动（SQL Server 2019，TLS1.2）
    private static final String MS_URL =
            "***********************************************************************************************************;";
    private static final String MS_USER = "beta";
    private static final String MS_PASSWORD = "inks@135";

    // JTDS 驱动（SQL Server 2008，TLS1.0）
    private static final String JTDS_URL =
            "**********************************************************************;";
    private static final String JTDS_USER = "beta";
    private static final String JTDS_PASSWORD = "inks@135";

    @Test
    public void testMicrosoftDriver() throws Exception {
        // 显式加载微软官方驱动（JDBC4+ 可省略）
        Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");

        try (Connection conn = DriverManager.getConnection(MS_URL, MS_USER, MS_PASSWORD);
             Statement stmt = conn.createStatement()) {
            ResultSet rs = stmt.executeQuery("SELECT GETDATE() AS NowTime");
            if (rs.next()) {
                System.out.println("[MS] 连接成功，当前时间：" + rs.getString("NowTime"));
            } else {
                System.err.println("[MS] 查询无结果");
            }
        }
    }

    @Test
    public void testJtdsDriver() throws Exception {
        // 显式加载 JTDS 驱动
        Class.forName("net.sourceforge.jtds.jdbc.Driver");

        try (Connection conn = DriverManager.getConnection(JTDS_URL, JTDS_USER, JTDS_PASSWORD);
             Statement stmt = conn.createStatement()) {
            ResultSet rs = stmt.executeQuery("SELECT GETDATE() AS NowTime");
            if (rs.next()) {
                System.out.println("[JTDS] 连接成功，当前时间：" + rs.getString("NowTime"));
            } else {
                System.err.println("[JTDS] 查询无结果");
            }
        }
    }
}
