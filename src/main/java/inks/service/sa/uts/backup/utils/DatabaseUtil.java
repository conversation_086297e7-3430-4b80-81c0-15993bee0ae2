package inks.service.sa.uts.backup.utils;

import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.uts.backup.pojo.LoggerQueue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;

@Component
public class DatabaseUtil implements ApplicationContextAware {


    private final static Logger logger = LoggerFactory.getLogger(DatabaseUtil.class);
    private static ApplicationContext applicationContext;
    private final Connection sqlServerConnection = null;
    private Connection mySqlConnection = null;

    public Connection getSqlServerConnection() {
        try {
            //从容器中拿到sqlServer连接
            DataSource dataSource = (DataSource) applicationContext.getBean("dbDataSource");
//            if (this.sqlServerConnection != null) {
//                return this.sqlServerConnection;
//            }
            return dataSource.getConnection();
        } catch (Exception e) {
            LoggerQueue.toLogPojo(Thread.currentThread(), "获取数据源失败", "message", "error");
            throw new RuntimeException(e.getMessage());
        }
//        return sqlServerConnection;
    }

    public Connection getMySqlConnection() {
        try {
            DataSource mySqlDataSource = (DataSource) applicationContext.getBean("mySqlDataSource");
            if (this.mySqlConnection != null) {
                return this.mySqlConnection;
            }
            this.mySqlConnection = mySqlDataSource.getConnection();
            return mySqlConnection;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }

    }


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        DatabaseUtil.applicationContext = applicationContext;
    }
}
