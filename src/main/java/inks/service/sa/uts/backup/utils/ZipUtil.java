package inks.service.sa.uts.backup.utils;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import inks.common.core.exception.BaseBusinessException;
import org.apache.commons.compress.archivers.zip.ParallelScatterZipCreator;
import org.apache.commons.compress.archivers.zip.UnixStat;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveOutputStream;
import org.apache.commons.compress.parallel.InputStreamSupplier;
import org.apache.commons.io.input.NullInputStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.*;
import java.util.concurrent.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Component
public class ZipUtil {
    private static final String ZIP_SUFFIX = ".zip";


    private final static Logger logger = LoggerFactory.getLogger(ZipUtil.class);

    /**
     * @param srcPath 要压缩的文件路径
     * @param dstPath 压缩后的zip路径
     * @param dstName 压缩文件名
     * @return void
     * @Description:
     * <AUTHOR>
     * @date 2021/11/16 11:41
     */
    public static String compress(String srcPath, String dstPath, String dstName) throws IOException {
        File srcFile = new File(srcPath);
        File dstFile = new File(dstPath + "\\" + dstName + ZIP_SUFFIX);
        if (!srcFile.exists()) {
            throw new FileNotFoundException(srcPath + "不存在！");
        }
        ZipOutputStream zipOut = null;
        try {
            zipOut = new ZipOutputStream(new BufferedOutputStream(new FileOutputStream(dstFile)));
            String baseDir = "";
            compressPro(srcFile, zipOut, baseDir);
            logger.info("压缩成功");
            return dstFile.getPath();
        } finally {
            if (null != zipOut) {
                zipOut.close();
            }
        }
    }

    private static void compressPro(File srcFile, ZipOutputStream zipOut, String baseDir) throws IOException {
        if (srcFile.isDirectory()) {
            compressDirectory(srcFile, zipOut, baseDir);
        } else {
            compressFile(srcFile, zipOut, baseDir);
        }
    }

    /**
     * 压缩一个目录
     */
    private static void compressDirectory(File dir, ZipOutputStream zipOut, String baseDir) throws IOException {
        File[] files = dir.listFiles();
        for (int i = 0; i < files.length; i++) {
            compressPro(files[i], zipOut, baseDir + dir.getName() + "/");
        }
    }

    /**
     * 压缩一个文件
     */
    private static void compressFile(File file, ZipOutputStream zipOut, String baseDir) throws IOException {
        if (!file.exists()) {
            return;
        }
        BufferedInputStream bis = null;
        try {
            bis = new BufferedInputStream(new FileInputStream(file));
            zipOut.putNextEntry(new ZipEntry(baseDir + file.getName()));
            int len;
            byte[] bytes = new byte[4096];
            while ((len = bis.read()) != -1) {
                zipOut.write(bytes, 0, len);
            }
        } finally {
            if (null != bis) {
                bis.close();
            }
        }
    }

    public String parallelZip(String srcPath, String dstPath, String dstName) {
        File srcFile = new File(srcPath);
        File dstFile = new File(dstPath + "\\" + dstName + ZIP_SUFFIX);
        ThreadFactory factory = new ThreadFactoryBuilder().setNameFormat("compressFileList-pool-").build();
        ExecutorService executor = new ThreadPoolExecutor(5, 10, 60, TimeUnit.SECONDS, new LinkedBlockingQueue<>(20), factory);
        ParallelScatterZipCreator parallelScatterZipCreator = new ParallelScatterZipCreator(executor);
        OutputStream outputStream = null;
        ZipArchiveOutputStream zipArchiveOutputStream = null;
        try {
            outputStream = new FileOutputStream(dstFile);
            zipArchiveOutputStream = new ZipArchiveOutputStream(outputStream);
            zipArchiveOutputStream.setEncoding("UTF-8");
            final InputStreamSupplier inputStreamSupplier = () -> {
                try {
                    return new FileInputStream(srcFile);
                } catch (FileNotFoundException e) {
                    e.printStackTrace();
                    return new NullInputStream(0);
                }
            };
            ZipArchiveEntry zipArchiveEntry = new ZipArchiveEntry(srcFile.getName());
            zipArchiveEntry.setMethod(ZipArchiveEntry.DEFLATED);
            zipArchiveEntry.setSize(srcFile.length());
            zipArchiveEntry.setUnixMode(UnixStat.FILE_FLAG | 436);
            parallelScatterZipCreator.addArchiveEntry(zipArchiveEntry, inputStreamSupplier);
            parallelScatterZipCreator.writeTo(zipArchiveOutputStream);
            return dstFile.getPath();
        } catch (Exception e) {
            logger.error("{}压缩失败", srcFile.getPath());
            e.printStackTrace();
            throw new BaseBusinessException(srcFile.getPath() + "压缩失败");
        } finally {
            try {
                zipArchiveOutputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            try {
                outputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

}
