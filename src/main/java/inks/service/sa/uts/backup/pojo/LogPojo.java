package inks.service.sa.uts.backup.pojo;

public class LogPojo {
    private String message;
    private String type;
    private String threadName;
    private String level;
    private String className;
    private String timestamp;
    private String body;

    public LogPojo() {
    }


    public LogPojo(String message, String type, String threadName, String level, String className, String timestamp, String body) {
        this.message = message;
        this.type = type;
        this.threadName = threadName;
        this.level = level;
        this.className = className;
        this.timestamp = timestamp;
        this.body = body;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getThreadName() {
        return threadName;
    }

    public void setThreadName(String threadName) {
        this.threadName = threadName;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }
}
