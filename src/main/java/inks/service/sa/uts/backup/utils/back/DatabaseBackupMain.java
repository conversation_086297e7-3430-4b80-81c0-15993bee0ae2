package inks.service.sa.uts.backup.utils.back;

import inks.sa.common.core.utils.PrintColor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.sql.Connection;
import java.sql.DriverManager; /**
 * 备份数据库的主程序
 */
public class DatabaseBackupMain {
    private static final Logger LOGGER = LoggerFactory.getLogger(DatabaseBackupMain.class);
    
    public static String backupDatabase(String dbUrl, String dbUsername, String dbPassword, String localSavePath, String zipPassword) {
        try {
            // 1. 确定数据库类型
            DatabaseExport.DbType dbType;
            if (dbUrl.contains("mysql") || dbUrl.contains("mariadb")) {
                dbType = DatabaseExport.DbType.MYSQL;
                PrintColor.red("本次备份MySQL数据库地址: " + dbUrl);
            } else if (dbUrl.contains("sqlserver")) {
                dbType = DatabaseExport.DbType.SQL_SERVER;
                PrintColor.red("本次备份SQL Server数据库地址: " + dbUrl);
            } else {
                throw new RuntimeException("不支持的数据库类型，URL: " + dbUrl);
            }
            
            // 2. 构造JDBC连接
            Connection conn = DriverManager.getConnection(dbUrl, dbUsername, dbPassword);
            if (conn == null) {
                throw new RuntimeException("获取数据库连接失败，请检查 URL、用户名或密码");
            }
            PrintColor.green("------------------成功建立连接，开始备份-------------------");

            // 3. 确定本地保存路径
            if (StringUtils.isBlank(localSavePath)) {
                localSavePath = System.getProperty("user.dir");
            }
            // 如果目录不存在或不可写，则立即抛错
            File saveDir = new File(localSavePath);
            if (!saveDir.exists() || !saveDir.isDirectory() || !saveDir.canWrite()) {
                throw new RuntimeException("本地保存目录不可用，请检查路径是否存在且具备写权限：" + localSavePath);
            }

            // 4. 执行备份到 ZIP
            long backupStart = System.currentTimeMillis();
            DatabaseExport exporter = new DatabaseExport(conn, localSavePath, dbType);
            String exportFilePath = exporter.export(zipPassword,null,null);
            long backupEnd = System.currentTimeMillis();
            PrintColor.red("备份完成，耗时 " + (backupEnd - backupStart) + " 毫秒，文件路径：" + exportFilePath);

            return exportFilePath;
        } catch (Exception e) {
            LOGGER.error("数据库备份失败", e);
            throw new RuntimeException("数据库备份失败: " + e.getMessage(), e);
        }
    }

    public static void main(String[] args) {
        try {
            // 命令行参数处理
            if (args.length < 3) {
                System.out.println("用法: java DatabaseBackupMain <数据库URL> <用户名> <密码> [保存路径] [ZIP密码]");
                System.out.println("MySQL示例: java DatabaseBackupMain ******************************** root password");
                System.out.println("SQL Server示例: java DatabaseBackupMain ************************************************* sa password");
                return;
            }

            String dbUrl = args[0];
            String dbUsername = args[1];
            String dbPassword = args[2];
            String localSavePath = args.length > 3 ? args[3] : null;
            String zipPassword = args.length > 4 ? args[4] : null;

            String filePath = backupDatabase(dbUrl, dbUsername, dbPassword, localSavePath, zipPassword);
            System.out.println("备份文件路径: " + filePath);

        } catch (Exception e) {
            System.err.println("备份失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
