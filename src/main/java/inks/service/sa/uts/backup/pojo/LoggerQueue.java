package inks.service.sa.uts.backup.pojo;

import inks.common.core.utils.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

@Component
public class LoggerQueue {


    //队列大小
    public static final int QUEUE_MAX_SIZE = 10000;

    private final static Logger logger = LoggerFactory.getLogger(LoggerQueue.class);
    private static final LoggerQueue alarmMessageQueue = new LoggerQueue();

    private final BlockingQueue<LogPojo> blockingQueue = new LinkedBlockingQueue<>(QUEUE_MAX_SIZE);

    public LoggerQueue() {
    }

    public static LoggerQueue getInstance() {
        return alarmMessageQueue;
    }

    public static void toLogPojo(Thread thread, String message, String type, String level) {
        String threadName = thread.getName();
        String className = thread.getStackTrace()[2].getClassName();
        synchronized (alarmMessageQueue) {
            getInstance().push(new LogPojo(message, type, threadName, level, className, DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", new Date()), null));
        }
    }

    public boolean push(LogPojo log) {
        if (blockingQueue.size() == QUEUE_MAX_SIZE) {
            logger.info("日志队列已满，进行日志存档");
            blockingQueue.clear();
        }
        return this.blockingQueue.add(log);
    }

    public LogPojo poll() {
        LogPojo result = null;
        try {
            result = this.blockingQueue.take();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return result;
    }
}
