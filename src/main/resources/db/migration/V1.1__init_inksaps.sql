/*
 Navicat Premium Data Transfer

 Source Server         : ---240MySQL8.0.27
 Source Server Type    : MySQL
 Source Server Version : 80031 (8.0.31)
 Source Host           : **************:53308
 Source Schema         : inksuts

 Target Server Type    : MySQL
 Target Server Version : 80031 (8.0.31)
 File Encoding         : 65001

 Date: 30/04/2025 08:59:02
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for Sa_AuthCode
-- ----------------------------
DROP TABLE IF EXISTS `Sa_AuthCode`;
CREATE TABLE `Sa_AuthCode`  (
                                `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
                                `AuthCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '授权Code',
                                `AuthDesc` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '授权作用',
                                `UserName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '登录名',
                                `UserPassword` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '密码',
                                `RowNum` int NOT NULL COMMENT '行号',
                                `EnabledMark` int NOT NULL COMMENT '有效标识',
                                `Remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                                `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                                `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                                `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                                `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                                `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                                `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                                `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                                `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                                `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                `TenantName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户Name',
                                `Revision` int NOT NULL COMMENT '乐观锁',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '授权码' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sa_BillCode
-- ----------------------------
DROP TABLE IF EXISTS `Sa_BillCode`;
CREATE TABLE `Sa_BillCode`  (
                                `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'id',
                                `ModuleCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '功能编码',
                                `BillName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '单据名称',
                                `Prefix1` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '前缀1',
                                `Suffix1` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '后缀1',
                                `Prefix2` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '前缀2',
                                `Suffix2` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '后缀2',
                                `Prefix3` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '前缀3',
                                `Suffix3` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '后缀3',
                                `Prefix4` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '前缀4',
                                `Suffix4` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '后缀4',
                                `Prefix5` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '前缀5',
                                `Suffix5` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '后缀5',
                                `CountType` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '年月日',
                                `Step` int NOT NULL COMMENT '跳步',
                                `CurrentNum` int NOT NULL COMMENT '当前序号',
                                `TableName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '数据表',
                                `DateColumn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '时间字段',
                                `ColumnName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '列名',
                                `DbFilter` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '数据过滤',
                                `AllowEdit` int NOT NULL COMMENT '编号允许修改',
                                `AllowDelete` int NOT NULL COMMENT '允许删除复用',
                                `Param1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '参数1',
                                `Param2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '参数2',
                                `Param3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '参数3',
                                `Param4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '参数4',
                                `Param5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '参数5',
                                `Remark` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                                `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '单据编码' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sa_BillGroup
-- ----------------------------
DROP TABLE IF EXISTS `Sa_BillGroup`;
CREATE TABLE `Sa_BillGroup`  (
                                 `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'id',
                                 `Parentid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'Parentid',
                                 `ModuleCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '功能编码',
                                 `GroupCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '分组编码',
                                 `GroupName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '分组名称',
                                 `EnabledMark` int NOT NULL COMMENT '有效性',
                                 `RowNum` int NOT NULL COMMENT '行号',
                                 `Remark` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                                 `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                 `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                 `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                 `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '通用分组' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sa_Company
-- ----------------------------
DROP TABLE IF EXISTS `Sa_Company`;
CREATE TABLE `Sa_Company`  (
                               `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
                               `Name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
                               `EnglishName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '英文名',
                               `CreditCode` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '信用代码',
                               `Address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '地址',
                               `BankAccount` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '银行账号',
                               `BankOfDeposit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '开户银行',
                               `ContactPerson` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '联系人',
                               `Tel` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '电话',
                               `Remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '备注',
                               `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建者',
                               `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建者id',
                               `CreateDate` datetime NULL DEFAULT NULL COMMENT '新建日期',
                               `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '制表',
                               `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '制表id',
                               `ModifyDate` datetime NULL DEFAULT NULL COMMENT '修改日期',
                               `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户id',
                               `TenantName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户名称',
                               `Revision` int NOT NULL COMMENT '乐观锁',
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '公司信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sa_Config
-- ----------------------------
DROP TABLE IF EXISTS `Sa_Config`;
CREATE TABLE `Sa_Config`  (
                              `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
                              `Parentid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '父级主键',
                              `CfgName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '参数名称',
                              `CfgKey` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '模块.key',
                              `CfgValue` varchar(10000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'value',
                              `CfgType` int NOT NULL COMMENT '0System/1Module',
                              `CfgLevel` int NOT NULL COMMENT '0平台/1租户/2用户',
                              `CtrlType` int NOT NULL COMMENT '控件类型 0文本1数字2下拉框3开关',
                              `CfgOption` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '可选值',
                              `CfgIcon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '图标',
                              `AllowUi` int NOT NULL DEFAULT 0 COMMENT '允许前端应用1',
                              `Userid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
                              `RowNum` int NOT NULL COMMENT '行号',
                              `EnabledMark` int NOT NULL DEFAULT 1 COMMENT '有效性1',
                              `AllowDelete` int NOT NULL DEFAULT 1 COMMENT '允许删除1',
                              `Remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '摘要',
                              `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                              `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                              `CreateDate` datetime NOT NULL COMMENT '新建日期',
                              `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                              `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                              `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                              `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                              `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                              `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                              `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                              `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                              `Custom6` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义6',
                              `Custom7` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义7',
                              `Custom8` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义8',
                              `Custom9` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义9',
                              `Custom10` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义10',
                              `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                              `Revision` int NOT NULL COMMENT '乐观锁',
                              PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sa_DgFormat
-- ----------------------------
DROP TABLE IF EXISTS `Sa_DgFormat`;
CREATE TABLE `Sa_DgFormat`  (
                                `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'id',
                                `FormGroupid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '窗体分组',
                                `FormCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '窗体编码',
                                `FormName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '窗体名称',
                                `RowNum` int NOT NULL DEFAULT 0 COMMENT '序号',
                                `EnabledMark` int NOT NULL COMMENT '有效标识',
                                `DefMark` int NULL DEFAULT NULL COMMENT '租户默认',
                                `Summary` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '简述',
                                `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                                `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                                `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                                `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                                `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                                `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                                `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                                `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                                `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                `TenantName` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户名称',
                                `Revision` int NOT NULL COMMENT '乐观锁',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '列表格式' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sa_DgFormatItem
-- ----------------------------
DROP TABLE IF EXISTS `Sa_DgFormatItem`;
CREATE TABLE `Sa_DgFormatItem`  (
                                    `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'id',
                                    `Pid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'Pid',
                                    `ItemCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '编码',
                                    `ItemName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '名称',
                                    `DefWidth` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '默认宽度',
                                    `MinWidth` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '最小宽度',
                                    `DisplayMark` int NOT NULL COMMENT '1为显示',
                                    `Fixed` int NOT NULL COMMENT '1固定0否',
                                    `Sortable` int NOT NULL COMMENT '1可排序',
                                    `OrderField` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '排序表.字段',
                                    `Overflow` int NOT NULL COMMENT '1溢出隐藏',
                                    `Formatter` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '格式化',
                                    `ClassName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义类',
                                    `AlignType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'left/center/right',
                                    `EventName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '事件名称',
                                    `EditMark` int NULL DEFAULT NULL COMMENT '可编辑',
                                    `OperationMark` int NULL DEFAULT NULL COMMENT '可操作',
                                    `RowNum` int NOT NULL COMMENT '行号',
                                    `Remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                                    `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                    `Revision` int NOT NULL COMMENT '乐观锁',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '列表项目' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sa_Dict
-- ----------------------------
DROP TABLE IF EXISTS `Sa_Dict`;
CREATE TABLE `Sa_Dict`  (
                            `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
                            `DictGroupid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '分组id',
                            `DictCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '字典编码',
                            `DictName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '字典名称',
                            `ModuleCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '功能编码',
                            `EnabledMark` int NOT NULL COMMENT '有效',
                            `RowNum` int NOT NULL COMMENT '排序码',
                            `Summary` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                            `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                            `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                            `CreateDate` datetime NOT NULL COMMENT '新建日期',
                            `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                            `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                            `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                            `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                            `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                            `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                            `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                            `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                            `Custom6` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义6',
                            `Custom7` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义7',
                            `Custom8` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义8',
                            `Custom9` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义9',
                            `Custom10` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义10',
                            `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                            `TenantName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户名',
                            `Revision` int NOT NULL COMMENT '乐观锁',
                            PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '数据字典' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sa_DictItem
-- ----------------------------
DROP TABLE IF EXISTS `Sa_DictItem`;
CREATE TABLE `Sa_DictItem`  (
                                `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
                                `Pid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '父id',
                                `DictCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '字典编码',
                                `DictValue` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '值',
                                `Essential` int NOT NULL COMMENT '必要',
                                `CssClass` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'css样式',
                                `EnabledMark` int NOT NULL COMMENT '有效',
                                `RowNum` int NOT NULL COMMENT '排序码',
                                `Remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                                `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                                `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                                `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                                `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                                `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                                `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                                `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                                `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                                `Custom6` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义6',
                                `Custom7` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义7',
                                `Custom8` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义8',
                                `Custom9` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义9',
                                `Custom10` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义10',
                                `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                `TenantName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户名',
                                `Revision` int NOT NULL COMMENT '乐观锁',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '字典子表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sa_FormCustom
-- ----------------------------
DROP TABLE IF EXISTS `Sa_FormCustom`;
CREATE TABLE `Sa_FormCustom`  (
                                  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'id',
                                  `GenGroupid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '通用分组',
                                  `ModuleCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '模块编码',
                                  `FrmCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '界面编码',
                                  `FrmName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '界面名称',
                                  `FrmContent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '界面内容',
                                  `RowNum` int NOT NULL COMMENT '序号',
                                  `EnabledMark` int NOT NULL COMMENT '有效标识',
                                  `Remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '摘要',
                                  `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                                  `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                                  `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                  `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                  `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                                  `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                  `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                                  `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                                  `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                                  `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                                  `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                                  `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                  `TenantName` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户名称',
                                  `Revision` int NOT NULL COMMENT '乐观锁',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '自定义界面' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sa_IndexImg
-- ----------------------------
DROP TABLE IF EXISTS `Sa_IndexImg`;
CREATE TABLE `Sa_IndexImg`  (
                                `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
                                `PictureUrl` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
                                `DirName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '目录名',
                                `FileName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'minio文件名',
                                `Prodid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '关联产品id',
                                `Demandid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
                                `Seq` int NULL DEFAULT NULL COMMENT '轮播图显示顺序',
                                `Status` int NULL DEFAULT NULL COMMENT '是否展示',
                                `Type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '轮播图类型',
                                `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                                `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                                `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                                `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                                `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                                `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                                `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                                `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                                `Custom6` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义6',
                                `Custom7` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义7',
                                `Custom8` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义8',
                                `Custom9` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义9',
                                `Custom10` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义10',
                                `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                `Revision` int NOT NULL COMMENT '乐观锁'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sa_Job
-- ----------------------------
DROP TABLE IF EXISTS `Sa_Job`;
CREATE TABLE `Sa_Job`  (
                           `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '主键',
                           `JobName` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '任务名称',
                           `JobGroup` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'DEFAULT' COMMENT '任务组名',
                           `InvokeTarget` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '调用目标字符串',
                           `CronExpression` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT 'cron执行表达式',
                           `MisfirePolicy` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '3' COMMENT '计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
                           `Concurrent` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '1' COMMENT '是否并发执行（0允许 1禁止）',
                           `Status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '0' COMMENT '状态（0正常 1暂停）',
                           `CreateBy` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT '创建者',
                           `CreateTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
                           `UpdateBy` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT '更新者',
                           `UpdateTime` datetime NULL DEFAULT NULL COMMENT '更新时间',
                           `Remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT '备注信息',
                           PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '定时任务调度表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sa_JobLog
-- ----------------------------
DROP TABLE IF EXISTS `Sa_JobLog`;
CREATE TABLE `Sa_JobLog`  (
                              `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '主键',
                              `JobName` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '任务名称',
                              `JobGroup` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '任务组名',
                              `InvokeTarget` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '调用目标字符串',
                              `JobMessage` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '日志信息',
                              `Status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '0' COMMENT '执行状态（0正常 1失败）',
                              `ExceptionInfo` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT '异常信息',
                              `CreateTime` datetime NULL DEFAULT NULL COMMENT '创建时间',
                              PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '定时任务调度日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sa_JustAuth
-- ----------------------------
DROP TABLE IF EXISTS `Sa_JustAuth`;
CREATE TABLE `Sa_JustAuth`  (
                                `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
                                `Userid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '用户id',
                                `UserName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '登录名',
                                `RealName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '姓名',
                                `NickName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '昵称',
                                `AuthType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ding/wxe/openid',
                                `AuthUuid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'uuid',
                                `Unionid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'Unionid',
                                `AuthAvatar` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'avatar',
                                `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                                `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                                `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                                `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                                `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                                `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                                `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                                `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                                `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                `TenantName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户名称',
                                `Revision` int NOT NULL COMMENT '乐观锁',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '第三方登录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sa_MenuApp
-- ----------------------------
DROP TABLE IF EXISTS `Sa_MenuApp`;
CREATE TABLE `Sa_MenuApp`  (
                               `Navid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'Navid',
                               `NavPid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '父级id',
                               `NavType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '导航类型',
                               `NavCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '导航编码',
                               `NavName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '导航名称',
                               `NavGroup` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '分组(备用)',
                               `RowNum` int NOT NULL COMMENT '排列序号',
                               `ImageCss` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'Css图标',
                               `IconUrl` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'Web图标',
                               `NavigateUrl` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'Web位置',
                               `MvcUrl` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'MVC位置',
                               `ModuleType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '模块类型',
                               `ModuleCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '模块编码',
                               `RoleCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '角色编码',
                               `ImageIndex` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '图标',
                               `ImageStyle` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '图标样式',
                               `EnabledMark` int NOT NULL COMMENT '有效标识',
                               `Remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                               `PermissionCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '许可编码',
                               `Functionid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '服务id',
                               `FunctionCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '服务编码',
                               `FunctionName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '服务名称',
                               `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                               `CreateDate` datetime NOT NULL COMMENT '新建日期',
                               `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                               `DeleteMark` int NOT NULL COMMENT '删除标识',
                               `DeleteLister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                               `DeleteDate` datetime NULL DEFAULT NULL COMMENT '新建日期',
                               PRIMARY KEY (`Navid`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for Sa_MenuWeb
-- ----------------------------
DROP TABLE IF EXISTS `Sa_MenuWeb`;
CREATE TABLE `Sa_MenuWeb`  (
                               `Navid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'Navid',
                               `NavPid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '父级id',
                               `NavType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '导航类型',
                               `NavCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '导航编码',
                               `NavName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '导航名称',
                               `NavGroup` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '分组(备用)',
                               `RowNum` int NOT NULL COMMENT '排列序号',
                               `ImageCss` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'Css图标',
                               `IconUrl` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'Web图标',
                               `NavigateUrl` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'Web位置',
                               `MvcUrl` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'MVC位置',
                               `ModuleType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '模块类型',
                               `ModuleCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '模块编码',
                               `RoleCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '角色编码',
                               `ImageIndex` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '图标',
                               `ImageStyle` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '图标样式',
                               `EnabledMark` int NOT NULL COMMENT '有效标识',
                               `Remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                               `PermissionCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '许可编码',
                               `Functionid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '服务id',
                               `FunctionCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '服务编码',
                               `FunctionName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '服务名称',
                               `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                               `CreateDate` datetime NOT NULL COMMENT '新建日期',
                               `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                               `DeleteMark` int NOT NULL COMMENT '删除标识',
                               `DeleteLister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                               `DeleteDate` datetime NULL DEFAULT NULL COMMENT '新建日期',
                               PRIMARY KEY (`Navid`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for Sa_Notice
-- ----------------------------
DROP TABLE IF EXISTS `Sa_Notice`;
CREATE TABLE `Sa_Notice`  (
                              `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
                              `Title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '公告标题',
                              `Type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '公告类型',
                              `Content` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '公告内容',
                              `Status` int NULL DEFAULT NULL COMMENT '公告状态1关闭',
                              `Remark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                              `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                              `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                              `CreateDate` datetime NOT NULL COMMENT '新建日期',
                              `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                              `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                              `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                              `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                              `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                              `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                              `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                              `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                              `Custom6` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义6',
                              `Custom7` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义7',
                              `Custom8` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义8',
                              `Custom9` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义9',
                              `Custom10` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义10',
                              `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                              `Revision` int NOT NULL COMMENT '乐观锁',
                              PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sa_OperLog
-- ----------------------------
DROP TABLE IF EXISTS `Sa_OperLog`;
CREATE TABLE `Sa_OperLog`  (
                               `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'id',
                               `OperTitle` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '模块标题',
                               `BusinessType` int NULL DEFAULT NULL COMMENT '业务类型（0其它 1新增 2修改 3删除）',
                               `Method` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '方法名称',
                               `RequestMethod` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求方式',
                               `OperatorType` int NULL DEFAULT NULL COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
                               `OperUserid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '操作人员id',
                               `OperName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '操作人员',
                               `DeptName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '部门名称',
                               `OperUrl` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求URL',
                               `OperIp` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '主机地址',
                               `OperLocation` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '操作地点',
                               `OperParam` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '请求参数',
                               `JsonResult` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '返回参数',
                               `Status` int NULL DEFAULT NULL COMMENT '操作状态（0正常 1异常）',
                               `ErrorMsg` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '错误消息',
                               `OperTime` datetime NULL DEFAULT NULL COMMENT '操作时间',
                               `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sa_PermCode
-- ----------------------------
DROP TABLE IF EXISTS `Sa_PermCode`;
CREATE TABLE `Sa_PermCode`  (
                                `Permid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'Id',
                                `Parentid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '父级主键',
                                `PermType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '权限类型',
                                `PermCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '权限编码',
                                `PermName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '权限名称',
                                `RowNum` int NOT NULL COMMENT '排列序号',
                                `IsPublic` int NOT NULL COMMENT '是否公开',
                                `EnabledMark` int NOT NULL COMMENT '是否有效',
                                `AllowDelete` int NOT NULL COMMENT '允许删除',
                                `Remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                                `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                                `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建者id',
                                `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '制表id',
                                `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                `Revision` int NOT NULL COMMENT '乐观锁'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '权限编码' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sa_Permission
-- ----------------------------
DROP TABLE IF EXISTS `Sa_Permission`;
CREATE TABLE `Sa_Permission`  (
                                  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'id',
                                  `ResourceType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '对象类别',
                                  `Resourceid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '对象id',
                                  `Permid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '权限id',
                                  `PermCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '权限编码',
                                  `PermName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '权限名称',
                                  `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                                  `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                                  `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                  `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                  `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                                  `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                  `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                  `TenantName` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户名称',
                                  `Revision` int NOT NULL COMMENT '乐观锁'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '权限关系' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sa_Redis
-- ----------------------------
DROP TABLE IF EXISTS `Sa_Redis`;
CREATE TABLE `Sa_Redis`  (
                             `RedisKey` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'key是MySQL关键字',
                             `RedisValue` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'value',
                             `ExpireTime` bigint NOT NULL COMMENT '过期时间戳(-1永不过期),每天23点执行SQL清除过期key:DELETE FROM Sa_Redis WHERE UNIX_TIMESTAMP(NOW()) * 1000 > expiretime AND expiretime != -1',
                             `CreateTime` bigint NULL DEFAULT NULL COMMENT '创建时间戳',
                             `Hkey` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '哈希内部的键',
                             PRIMARY KEY (`RedisKey`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = 'MySQL暂替Redis' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sa_Reports
-- ----------------------------
DROP TABLE IF EXISTS `Sa_Reports`;
CREATE TABLE `Sa_Reports`  (
                               `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'id',
                               `GenGroupid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '通用分组',
                               `ModuleCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '模块编码',
                               `RptType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '报表类型',
                               `RptName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '报表名称',
                               `RptData` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '报表数据',
                               `PageRow` int NOT NULL DEFAULT 0 COMMENT '单页行数',
                               `TempUrl` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '远程打印模版Url',
                               `FileName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '模版文件名',
                               `PrinterSn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '远程打印机SN',
                               `RowNum` int NOT NULL DEFAULT 0 COMMENT '序号',
                               `EnabledMark` int NOT NULL COMMENT '有效标识',
                               `GrfData` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT 'grf文本备用',
                               `PaperLength` decimal(18, 2) NULL DEFAULT NULL COMMENT '长',
                               `PaperWidth` decimal(18, 2) NULL DEFAULT NULL COMMENT '宽',
                               `Remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                               `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                               `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                               `CreateDate` datetime NOT NULL COMMENT '新建日期',
                               `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                               `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                               `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                               `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                               `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                               `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                               `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                               `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                               `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                               `TenantName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户名称',
                               `Revision` int NOT NULL DEFAULT 0 COMMENT '乐观锁',
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '报表中心' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sa_Role
-- ----------------------------
DROP TABLE IF EXISTS `Sa_Role`;
CREATE TABLE `Sa_Role`  (
                            `Roleid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '角色ID',
                            `RoleCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '编码',
                            `RoleName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '名称',
                            `Functionid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '服务id',
                            `FunctionCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '服务编码',
                            `FunctionName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '服务名称',
                            `EnabledMark` int NOT NULL COMMENT '是否有效',
                            `RowNum` int NOT NULL COMMENT '排列序号',
                            `Remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                            `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                            `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                            `CreateDate` datetime NOT NULL COMMENT '新建日期',
                            `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                            `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                            `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                            `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                            `TenantName` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户名称',
                            `Revision` int NOT NULL COMMENT '乐观锁'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '角色' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sa_RoleMenuApp
-- ----------------------------
DROP TABLE IF EXISTS `Sa_RoleMenuApp`;
CREATE TABLE `Sa_RoleMenuApp`  (
                                   `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
                                   `Roleid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '角色ID',
                                   `Navid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '菜单ID',
                                   `RowNum` int NOT NULL COMMENT '排列序号',
                                   `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                                   `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                                   `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                   `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                   `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                                   `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                   `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                   `TenantName` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户名称',
                                   `Revision` int NOT NULL COMMENT '乐观锁',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for Sa_RoleMenuWeb
-- ----------------------------
DROP TABLE IF EXISTS `Sa_RoleMenuWeb`;
CREATE TABLE `Sa_RoleMenuWeb`  (
                                   `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
                                   `Roleid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '角色ID',
                                   `Navid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '菜单ID',
                                   `RowNum` int NOT NULL COMMENT '排列序号',
                                   `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                                   `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                                   `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                   `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                   `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                                   `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                   `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                   `TenantName` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户名称',
                                   `Revision` int NOT NULL COMMENT '乐观锁',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for Sa_Scene
-- ----------------------------
DROP TABLE IF EXISTS `Sa_Scene`;
CREATE TABLE `Sa_Scene`  (
                             `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'id',
                             `ModuleCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '模块编码',
                             `SceneName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '场景名称',
                             `SceneData` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '场景数据',
                             `RowNum` int NOT NULL DEFAULT 0 COMMENT '序号',
                             `EnabledMark` int NOT NULL COMMENT '有效标识',
                             `Remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                             `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建者',
                             `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建者id',
                             `CreateDate` datetime NOT NULL COMMENT '新建日期',
                             `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                             `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '制表id',
                             `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                             `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                             `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                             `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                             `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                             `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                             `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                             `TenantName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户名称',
                             `Revision` int NOT NULL COMMENT '乐观锁',
                             PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '场景管理' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sa_SceneField
-- ----------------------------
DROP TABLE IF EXISTS `Sa_SceneField`;
CREATE TABLE `Sa_SceneField`  (
                                  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'id',
                                  `GenGroupid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '通用分组',
                                  `ModuleCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '模块编码',
                                  `FieldCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '编码',
                                  `FieldName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '名称',
                                  `FieldType` int NOT NULL COMMENT '0文本1为数字',
                                  `SearchMark` int NULL DEFAULT NULL COMMENT '搜索中应用',
                                  `RowNum` int NOT NULL DEFAULT 0 COMMENT '序号',
                                  `Remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                                  `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建者',
                                  `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '创建者id',
                                  `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                  `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                  `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '制表id',
                                  `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                  `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                                  `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                                  `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                                  `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                                  `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                                  `Revision` int NOT NULL COMMENT '乐观锁',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '场景字段' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sa_User
-- ----------------------------
DROP TABLE IF EXISTS `Sa_User`;
CREATE TABLE `Sa_User`  (
                            `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
                            `UserName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '用户名',
                            `RealName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '真实姓名',
                            `Password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '密码',
                            `Phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '手机号',
                            `Email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '电子邮箱',
                            `EmailAuthCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '阿里邮箱授权码',
                            `Sex` int NULL DEFAULT NULL COMMENT '性别',
                            `Avatar` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '邮箱',
                            `DirName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '目录名',
                            `FileName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'minio文件名',
                            `RoleType` int NOT NULL COMMENT '0新注册1技术员',
                            `AdminMark` int NOT NULL COMMENT '是否管理员',
                            `UserState` int NOT NULL COMMENT '用户状态',
                            `Remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                            `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建人',
                            `CreateDate` datetime NOT NULL COMMENT '创建时间',
                            `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表人',
                            `ModifyDate` datetime NOT NULL COMMENT '修改时间',
                            `Revision` int NOT NULL COMMENT '锁',
                            PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '用户' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sa_UserRole
-- ----------------------------
DROP TABLE IF EXISTS `Sa_UserRole`;
CREATE TABLE `Sa_UserRole`  (
                                `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ID',
                                `Roleid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '角色ID',
                                `Userid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '用户ID',
                                `RowNum` int NOT NULL COMMENT '排列序号',
                                `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                                `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                                `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                                `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                `TenantName` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户名称',
                                `Revision` int NOT NULL COMMENT '乐观锁'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '用户角色关联' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Sa_WebNav
-- ----------------------------
DROP TABLE IF EXISTS `Sa_WebNav`;
CREATE TABLE `Sa_WebNav`  (
                              `Navid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'Navid',
                              `NavCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '导航编码',
                              `NavName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '导航名称',
                              `NavContent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '导航内容',
                              `RowNum` int NOT NULL COMMENT '排列序号',
                              `EnabledMark` int NOT NULL COMMENT '有效标识',
                              `PermissionCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '许可编码',
                              `Remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                              `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                              `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                              `CreateDate` datetime NOT NULL COMMENT '新建日期',
                              `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                              `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                              `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                              `Revision` int NOT NULL COMMENT '乐观锁',
                              PRIMARY KEY (`Navid`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = 'Pc导航' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Uts_BackupConfig
-- ----------------------------
DROP TABLE IF EXISTS `Uts_BackupConfig`;
CREATE TABLE `Uts_BackupConfig`  (
                                     `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'id',
                                     `ConfigName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '配置名称（唯一标识）',
                                     `LocalMark` int NOT NULL COMMENT '本机数据库备份',
                                     `DbDriver` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '数据库驱动',
                                     `DbUrl` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '数据库连接地址',
                                     `DbUsername` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '数据库用户名',
                                     `DbPassword` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '数据库密码',
                                     `LocalPath` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '存储本地路径(不填则不存储)',
                                     `UploadPrefix` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '存储路径前缀,标识客户名',
                                     `ZipPassword` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '解压密码',
                                     `CronExpression` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '定时任务cron表达式',
                                     `CloudUrl` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '上传接口(不走OSS)',
                                     `AuthCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '授权码',
                                     `Email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '通知邮箱',
                                     `EnabledMark` int NOT NULL COMMENT '有效',
                                     `OssType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '存储类型minio/aliyun',
                                     `OssBucket` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'Oss存储桶',
                                     `OssAccessKey` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'Oss Access Key',
                                     `OssSecretKey` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'Oss Secret Key',
                                     `OssEndpoint` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'Oss Endpoint',
                                     `RowNum` int NOT NULL COMMENT '序号',
                                     `Remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注说明',
                                     `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                                     `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                                     `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                     `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                     `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                                     `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                     `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                                     `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                                     `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                                     `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                                     `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                                     `Custom6` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义6',
                                     `Custom7` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义7',
                                     `Custom8` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义8',
                                     `Custom9` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义9',
                                     `Custom10` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义10',
                                     `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                     `TenantName` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户名称',
                                     `Revision` int NOT NULL COMMENT '乐观锁',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '数据库备份配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Uts_Database
-- ----------------------------
DROP TABLE IF EXISTS `Uts_Database`;
CREATE TABLE `Uts_Database`  (
                                 `id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'id',
                                 `Title` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '标题',
                                 `Url` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '数据库url',
                                 `UserName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '数据库username',
                                 `Password` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '数据库password',
                                 `DriverClassName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '数据库驱动',
                                 `EnabledMark` int NOT NULL COMMENT '有效性',
                                 `RowNum` int NOT NULL COMMENT '行号',
                                 `Remark` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '备注',
                                 `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                                 `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                                 `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                 `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                 `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                                 `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                 `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                 `TenantName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户名称',
                                 `Revision` int NOT NULL COMMENT '乐观锁',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '数据库连接池' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for Uts_DingAppr
-- ----------------------------
DROP TABLE IF EXISTS `Uts_DingAppr`;
CREATE TABLE `Uts_DingAppr`  (
                                 `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'id',
                                 `ModuleCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '模块编码',
                                 `Templateid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '模版id',
                                 `ApprName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '审批名称',
                                 `DataTemp` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '数据模版',
                                 `CallbackUrl` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '回调Url',
                                 `CallbackBean` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '回调Bean',
                                 `ResultCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '执行条件',
                                 `ApprType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '类型(备用)',
                                 `RowNum` int NOT NULL DEFAULT 0 COMMENT '序号',
                                 `EnabledMark` int NOT NULL COMMENT '有效标识',
                                 `Remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                                 `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                                 `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                                 `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                 `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                 `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                                 `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                 `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                                 `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                                 `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                                 `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                                 `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                                 `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                 `TenantName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户名称',
                                 `Revision` int NULL DEFAULT NULL COMMENT '乐观锁',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '钉钉审批' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Uts_DingApprRec
-- ----------------------------
DROP TABLE IF EXISTS `Uts_DingApprRec`;
CREATE TABLE `Uts_DingApprRec`  (
                                    `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'id',
                                    `ModuleCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '模块编码',
                                    `Templateid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '模版id',
                                    `ApprName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '审批名称',
                                    `DataTemp` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '数据模版',
                                    `CallbackUrl` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '回调Url',
                                    `CallbackBean` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '回调Bean',
                                    `ResultCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '执行条件',
                                    `ApprType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '类型(备用)',
                                    `ApprSn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '审批Sn',
                                    `Billid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '单据id',
                                    `CallbackUuid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '回调Uuid',
                                    `CallbackName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '回调人员',
                                    `CallbackDate` datetime NULL DEFAULT NULL COMMENT '回调日期',
                                    `CallbackResult` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '回调结果',
                                    `CallbackMsg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '回调信息',
                                    `Userid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '用户id',
                                    `RealName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '姓名',
                                    `Remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                                    `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                                    `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                                    `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                    `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                    `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                                    `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                    `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                                    `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                                    `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                                    `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                                    `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                                    `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                    `TenantName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户名称',
                                    `Revision` int NULL DEFAULT NULL COMMENT '乐观锁',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '钉钉审批记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Uts_DingHookMsg
-- ----------------------------
DROP TABLE IF EXISTS `Uts_DingHookMsg`;
CREATE TABLE `Uts_DingHookMsg`  (
                                    `id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'id',
                                    `MsgGroupid` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '信息分组',
                                    `MsgCode` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '服务_功能_动作',
                                    `MsgName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '信息名称',
                                    `MsgType` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'Text/OA/Crad',
                                    `MsgTemplate` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'VM模版',
                                    `ModuleCode` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '功能模块(备用)',
                                    `WebhookList` varchar(2000) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '群机器人Webhook列表',
                                    `UserList` varchar(2000) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '@用户列表',
                                    `DeptList` varchar(2000) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '部门表',
                                    `ObjJson` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL COMMENT '接受对象 ObjTypeCodeNameRowNum',
                                    `RowNum` int NOT NULL COMMENT '行号',
                                    `UrlTemplate` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '详情Url模版',
                                    `EnabledMark` int NOT NULL COMMENT '有效性',
                                    `Remark` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '摘要',
                                    `CreateBy` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '创建者',
                                    `CreateByid` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '创建者id',
                                    `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                    `Lister` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '制表',
                                    `Listerid` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '制表id',
                                    `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                    `Custom1` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '自定义1',
                                    `Custom2` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '自定义2',
                                    `Custom3` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '自定义3',
                                    `Custom4` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '自定义4',
                                    `Custom5` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '自定义5',
                                    `Tenantid` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '租户id',
                                    `TenantName` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '租户名称',
                                    `Revision` int NOT NULL COMMENT '乐观锁',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_bin COMMENT = '钉钉群机器人信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for Uts_DingMsg
-- ----------------------------
DROP TABLE IF EXISTS `Uts_DingMsg`;
CREATE TABLE `Uts_DingMsg`  (
                                `id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'id',
                                `MsgGroupid` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '信息分组',
                                `MsgCode` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '服务_功能_动作',
                                `MsgName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '信息名称',
                                `MsgType` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'Text/OA/Crad',
                                `MsgTemplate` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'VM模版',
                                `ModuleCode` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '功能模块(备用)',
                                `UserList` varchar(2000) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '用户表',
                                `DeptList` varchar(2000) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '部门表',
                                `ObjJson` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL COMMENT '接受对象 ObjTypeCodeNameRowNum',
                                `RowNum` int NOT NULL COMMENT '行号',
                                `UrlTemplate` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '详情Url模版',
                                `EnabledMark` int NOT NULL COMMENT '有效性',
                                `Remark` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '摘要',
                                `CreateBy` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '创建者',
                                `CreateByid` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '创建者id',
                                `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                `Lister` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '制表',
                                `Listerid` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '制表id',
                                `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                `Custom1` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '自定义1',
                                `Custom2` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '自定义2',
                                `Custom3` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '自定义3',
                                `Custom4` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '自定义4',
                                `Custom5` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '自定义5',
                                `Tenantid` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '租户id',
                                `TenantName` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '租户名称',
                                `Revision` int NOT NULL COMMENT '乐观锁',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_bin COMMENT = '钉钉信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for Uts_EmailMsg
-- ----------------------------
DROP TABLE IF EXISTS `Uts_EmailMsg`;
CREATE TABLE `Uts_EmailMsg`  (
                                 `id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'id',
                                 `MsgCode` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '服务_功能_动作',
                                 `MsgName` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '邮件主题',
                                 `MsgType` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'Text/Html',
                                 `MsgTemplate` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'Html的模版',
                                 `ModuleCode` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '功能模块(备用)',
                                 `MsgGroupid` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '信息分组',
                                 `Recipient` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '收件人',
                                 `CcPeople` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '抄送人们',
                                 `ItemJson` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL COMMENT '用户json',
                                 `RowNum` int NOT NULL COMMENT '行号',
                                 `EnabledMark` int NOT NULL COMMENT '有效性',
                                 `Remark` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '摘要',
                                 `CreateBy` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '创建者',
                                 `CreateByid` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '创建者id',
                                 `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                 `Lister` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '制表',
                                 `Listerid` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '制表id',
                                 `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                 `Custom1` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '自定义1',
                                 `Custom2` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '自定义2',
                                 `Custom3` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '自定义3',
                                 `Custom4` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '自定义4',
                                 `Custom5` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '自定义5',
                                 `Tenantid` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '租户id',
                                 `TenantName` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '租户名称',
                                 `Revision` int NOT NULL COMMENT '乐观锁',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_bin COMMENT = '邮件信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for Uts_FreeReports
-- ----------------------------
DROP TABLE IF EXISTS `Uts_FreeReports`;
CREATE TABLE `Uts_FreeReports`  (
                                    `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'id',
                                    `FRType` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '报表Type',
                                    `FRGroupid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '报表分组id',
                                    `LocalMark` int NOT NULL COMMENT '本地数据库',
                                    `ReportCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '报表编码',
                                    `ReportName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '报表名称',
                                    `DynType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '动态查询类型sql、http',
                                    `DynSentence` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '动态查询sql或者接口中的请求体',
                                    `SqlFull` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '拼接的完整SQL',
                                    `SqlSelect` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '查询字段',
                                    `SqlFrom` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '表名join',
                                    `SqlWhere` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '查询条件',
                                    `SqlGroupBy` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '分组操作',
                                    `SqlHaving` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '聚合筛选',
                                    `SqlOrderBy` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '排序',
                                    `SqlLimit` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '限制输出',
                                    `MainTable` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主表',
                                    `EnabledMark` int NOT NULL COMMENT '有效性',
                                    `PublicMark` int NOT NULL COMMENT '公共报表',
                                    `Databaseid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '数据库连接id',
                                    `DomainNum` int NULL DEFAULT NULL COMMENT ' Userid来源:0本地,1rms',
                                    `Userid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '所属用户id',
                                    `ImageIndex` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '图标',
                                    `PermissionCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '许可编码',
                                    `ImageStyle` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '图标样式',
                                    `DatePath` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '提取数据的路径',
                                    `AuthCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'authcode授权码',
                                    `CaseResult` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '结果案例',
                                    `ReportType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '报表类型',
                                    `ChartType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '图表类型',
                                    `RowNum` int NOT NULL COMMENT 'RowNum',
                                    `Summary` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '摘要',
                                    `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建者',
                                    `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建者id',
                                    `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                    `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '制表',
                                    `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '制表id',
                                    `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                    `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '自定义1',
                                    `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '自定义2',
                                    `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '自定义3',
                                    `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '自定义4',
                                    `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '自定义5',
                                    `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '租户id',
                                    `TenantName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '租户名称',
                                    `Revision` int NOT NULL COMMENT '乐观锁',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '自由报表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Uts_FreeReportsItem
-- ----------------------------
DROP TABLE IF EXISTS `Uts_FreeReportsItem`;
CREATE TABLE `Uts_FreeReportsItem`  (
                                        `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'id',
                                        `Pid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'Pid',
                                        `FieldType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '字段类型',
                                        `FieldName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '字段名称',
                                        `HeaderText` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '列头显示',
                                        `DataPropertyName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '数据邦定',
                                        `OrderStr` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '排序字符',
                                        `ColWidth` int NOT NULL COMMENT '列宽',
                                        `ColAlign` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '列对齐',
                                        `DisplayNo` int NOT NULL COMMENT '显示编号',
                                        `DisplayState` int NOT NULL COMMENT '显示状态',
                                        `FormatString` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '文本格式',
                                        `DefWidth` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '默认宽度',
                                        `MinWidth` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '最小宽度',
                                        `Fixed` int NOT NULL COMMENT '1固定0否',
                                        `Sortable` int NOT NULL COMMENT '1可排序',
                                        `OrderField` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '排序表.字段',
                                        `Overflow` int NOT NULL COMMENT '1溢出隐藏',
                                        `Formatter` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '格式化',
                                        `ClassName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义类',
                                        `AlignType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'left/center/right',
                                        `EventName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '事件名称',
                                        `EditMark` int NULL DEFAULT NULL COMMENT '可编辑',
                                        `OperationMark` int NULL DEFAULT NULL COMMENT '可操作',
                                        `DisplayIndex` int NULL DEFAULT NULL COMMENT '显示位',
                                        `RowNum` int NOT NULL COMMENT 'RowNum',
                                        `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                                        `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                                        `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                                        `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                                        `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                                        `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                        `TenantName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户名称',
                                        `Revision` int NOT NULL COMMENT '乐观锁',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '自由报表项目' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Uts_InfoAgent
-- ----------------------------
DROP TABLE IF EXISTS `Uts_InfoAgent`;
CREATE TABLE `Uts_InfoAgent`  (
                                  `id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'id',
                                  `MsgGroupid` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '信息分组',
                                  `MsgCode` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '服务_功能_动作',
                                  `MsgName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '信息名称',
                                  `AgentTools` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '代理工具(多选)',
                                  `RowNum` int NOT NULL COMMENT '行号',
                                  `EnabledMark` int NOT NULL COMMENT '有效性',
                                  `Remark` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '摘要',
                                  `CreateBy` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '创建者',
                                  `CreateByid` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '创建者id',
                                  `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                  `Lister` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '制表',
                                  `Listerid` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '制表id',
                                  `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                  `Custom1` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '自定义1',
                                  `Custom2` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '自定义2',
                                  `Custom3` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '自定义3',
                                  `Custom4` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '自定义4',
                                  `Custom5` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '自定义5',
                                  `Tenantid` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '租户id',
                                  `TenantName` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '租户名称',
                                  `Revision` int NOT NULL COMMENT '乐观锁',
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_bin COMMENT = '信息代理中心' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for Uts_MqttMsg
-- ----------------------------
DROP TABLE IF EXISTS `Uts_MqttMsg`;
CREATE TABLE `Uts_MqttMsg`  (
                                `id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'id',
                                `MsgGroupid` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '信息分组',
                                `MsgCode` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '服务_功能_动作',
                                `MsgName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '信息名称',
                                `MsgType` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'Text/OA/Crad',
                                `MsgTemplate` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'VM模版',
                                `MsgIcon` int NULL DEFAULT NULL COMMENT '图标0info1success2warning3error',
                                `ModuleCode` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '功能模块(备用)',
                                `Duration` int NULL DEFAULT NULL COMMENT '持续时间',
                                `UseridList` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL COMMENT '用户表',
                                `ItemJson` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL COMMENT '用户/部门信息json',
                                `DeptidList` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL COMMENT '部门表',
                                `ToAllUser` int NULL DEFAULT NULL COMMENT 'To全员',
                                `RowNum` int NOT NULL COMMENT '行号',
                                `EnabledMark` int NOT NULL COMMENT '有效性',
                                `Position` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '消息显示的位置',
                                `Remark` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '摘要',
                                `CreateBy` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '创建者',
                                `CreateByid` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '创建者id',
                                `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                `Lister` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '制表',
                                `Listerid` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '制表id',
                                `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                `Custom1` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '自定义1',
                                `Custom2` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '自定义2',
                                `Custom3` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '自定义3',
                                `Custom4` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '自定义4',
                                `Custom5` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '自定义5',
                                `Tenantid` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '租户id',
                                `TenantName` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '租户名称',
                                `Revision` int NOT NULL COMMENT '乐观锁',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_bin COMMENT = 'MQTT信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for Uts_SqlActuator
-- ----------------------------
DROP TABLE IF EXISTS `Uts_SqlActuator`;
CREATE TABLE `Uts_SqlActuator`  (
                                    `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'id',
                                    `SqlName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'sql名称',
                                    `SqlData` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT 'SQL语句',
                                    `ModuleName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '功能模块',
                                    `Version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '版本',
                                    `Databaseid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '数据库连接id',
                                    `ResultJson` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '返回',
                                    `BusType` int NULL DEFAULT NULL COMMENT '业务类型（0列表 1新增 2修改 3删除）',
                                    `StatusNum` int NULL DEFAULT NULL COMMENT '操作状态（0正常 1异常）',
                                    `Remark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                                    `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                                    `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                                    `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                    `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                    `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                                    `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                    `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                                    `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                                    `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                                    `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                                    `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                                    `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                    `TenantName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户名称',
                                    `Revision` int NOT NULL COMMENT '乐观锁',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = 'SQL执行器' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Uts_WxeAppr
-- ----------------------------
DROP TABLE IF EXISTS `Uts_WxeAppr`;
CREATE TABLE `Uts_WxeAppr`  (
                                `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'id',
                                `ModuleCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '模块编码',
                                `Templateid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '模版id',
                                `ApprName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '审批名称',
                                `DataTemp` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '数据模版',
                                `CallbackUrl` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '回调Url',
                                `CallbackBean` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '回调Bean',
                                `ResultCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '执行条件',
                                `ApprType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '类型(备用)',
                                `RowNum` int NOT NULL DEFAULT 0 COMMENT '序号',
                                `TestData` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '审批申请测试数据',
                                `EnabledMark` int NOT NULL COMMENT '有效标识',
                                `Remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                                `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                                `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                                `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                                `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                                `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                                `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                                `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                                `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                                `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                `TenantName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户名称',
                                `Revision` int NULL DEFAULT NULL COMMENT '乐观锁',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '企业微审核' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Uts_WxeApprRec
-- ----------------------------
DROP TABLE IF EXISTS `Uts_WxeApprRec`;
CREATE TABLE `Uts_WxeApprRec`  (
                                   `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'id',
                                   `ModuleCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '模块编码',
                                   `Templateid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '模版id',
                                   `ApprName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '审批名称',
                                   `DataTemp` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '数据模版',
                                   `CallbackUrl` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '回调Url',
                                   `CallbackBean` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '回调Bean',
                                   `ResultCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '执行条件',
                                   `ApprType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '类型(备用)',
                                   `ApprSn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '审批Sn',
                                   `Billid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '单据id',
                                   `CallbackUuid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '回调Uuid',
                                   `CallbackName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '回调人员',
                                   `CallbackDate` datetime NULL DEFAULT NULL COMMENT '回调日期',
                                   `CallbackResult` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '回调结果',
                                   `CallbackMsg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '回调信息',
                                   `Userid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '用户id',
                                   `RealName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '姓名',
                                   `Remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
                                   `CreateBy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者',
                                   `CreateByid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建者id',
                                   `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                   `Lister` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表',
                                   `Listerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '制表id',
                                   `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                   `Custom1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义1',
                                   `Custom2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义2',
                                   `Custom3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义3',
                                   `Custom4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义4',
                                   `Custom5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '自定义5',
                                   `Tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '租户id',
                                   `TenantName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '租户名称',
                                   `Revision` int NULL DEFAULT NULL COMMENT '乐观锁',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '微信审批记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for Uts_WxeHookMsg
-- ----------------------------
DROP TABLE IF EXISTS `Uts_WxeHookMsg`;
CREATE TABLE `Uts_WxeHookMsg`  (
                                   `id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'id',
                                   `MsgGroupid` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '信息分组',
                                   `MsgCode` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '服务_功能_动作',
                                   `MsgName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '信息名称',
                                   `MsgType` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'Text/OA/Crad',
                                   `MsgTemplate` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'VM模版',
                                   `ModuleCode` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '功能模块(备用)',
                                   `WebhookList` varchar(2000) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '群机器人Webhook列表',
                                   `UserList` varchar(2000) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '@用户列表',
                                   `DeptList` varchar(2000) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '部门表',
                                   `ObjJson` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL COMMENT '接受对象 ObjTypeCodeNameRowNum',
                                   `RowNum` int NOT NULL COMMENT '行号',
                                   `UrlTemplate` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '详情Url模版',
                                   `EnabledMark` int NOT NULL COMMENT '有效性',
                                   `Remark` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '摘要',
                                   `CreateBy` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '创建者',
                                   `CreateByid` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '创建者id',
                                   `CreateDate` datetime NOT NULL COMMENT '新建日期',
                                   `Lister` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '制表',
                                   `Listerid` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '制表id',
                                   `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                                   `Custom1` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '自定义1',
                                   `Custom2` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '自定义2',
                                   `Custom3` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '自定义3',
                                   `Custom4` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '自定义4',
                                   `Custom5` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '自定义5',
                                   `Tenantid` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '租户id',
                                   `TenantName` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '租户名称',
                                   `Revision` int NOT NULL COMMENT '乐观锁',
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_bin COMMENT = '企业微信群机器人信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for Uts_WxeMsg
-- ----------------------------
DROP TABLE IF EXISTS `Uts_WxeMsg`;
CREATE TABLE `Uts_WxeMsg`  (
                               `id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'id',
                               `MsgGroupid` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '信息分组',
                               `MsgCode` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '服务_功能_动作',
                               `MsgName` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '信息名称',
                               `MsgType` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'Text/OA/Crad',
                               `MsgTemplate` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'VM模版',
                               `ModuleCode` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '功能模块(备用)',
                               `UserList` varchar(2000) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '用户表',
                               `DeptList` varchar(2000) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '部门表',
                               `ObjJson` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL COMMENT '接受对象 ObjTypeCodeNameRowNum',
                               `RowNum` int NOT NULL COMMENT '行号',
                               `UrlTemplate` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '详情Url模版',
                               `EnabledMark` int NOT NULL COMMENT '有效性',
                               `Summary` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '摘要',
                               `CreateBy` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '创建者',
                               `CreateByid` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '创建者id',
                               `CreateDate` datetime NOT NULL COMMENT '新建日期',
                               `Lister` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '制表',
                               `Listerid` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '制表id',
                               `ModifyDate` datetime NOT NULL COMMENT '修改日期',
                               `Custom1` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '自定义1',
                               `Custom2` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '自定义2',
                               `Custom3` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '自定义3',
                               `Custom4` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '自定义4',
                               `Custom5` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NULL DEFAULT NULL COMMENT '自定义5',
                               `Tenantid` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '租户id',
                               `TenantName` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '租户名称',
                               `Revision` int NOT NULL COMMENT '乐观锁',
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb3 COLLATE = utf8mb3_bin COMMENT = '企业微信信息' ROW_FORMAT = DYNAMIC;

INSERT INTO inksuts.Sa_Company (id, Name, EnglishName, CreditCode, Address, BankAccount, BankOfDeposit, ContactPerson, Tel, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Tenantid, TenantName, Revision) VALUES ('1', '嘉兴应凯科技有限公司', 'Jiaxing Yingkai Technology Co., Ltd.', '91330421MA2JE8W42K', '浙江嘉善农村商业银行营业部(编码:************)', '***************', '任爱军', '任爱军', '***********', '测试发消息', 'admin', ' ', '2024-02-27 10:34:05', '汪洋', '1', '2024-03-23 17:01:43', ' ', ' ', 69);
INSERT INTO inksuts.Sa_User (id, UserName, RealName, Password, Phone, Email, EmailAuthCode, Sex, Avatar, DirName, FileName, RoleType, AdminMark, UserState, Remark, CreateBy, CreateDate, Lister, ModifyDate, Revision) VALUES ('1', 'admin', 'admin', '14bEvA7CeyIoopYkYqtNWw==', '', '', '', 0, 'picture/********/d3a0a08f2ea447ba9f1a.jpg', 'picture/********', '874127ff1044cd5bad3a.jpg', 1, 2, 0, '默认超级管理员', '', '2022-12-05 17:12:29', 'admin', '2024-02-20 10:48:43', 9);

INSERT INTO inksuts.Sa_Config (id, Parentid, CfgName, CfgKey, CfgValue, CfgType, CfgLevel, CtrlType, CfgOption, CfgIcon, AllowUi, Userid, RowNum, EnabledMark, AllowDelete, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision) VALUES ('11', ' ', '应用回调token', 'system.ding.callbacktoken', 'AtHEn3KB77wCfdd6Byd3IXGu6FdK', 0, 1, 0, null, null, 0, ' ', 7, 1, 0, ' ', 'admin', '1', '2024-09-13 10:10:49', 'admin', '1', '2024-09-13 10:10:49', ' ', null, null, null, null, null, '', ' ', ' ', ' ', ' ', 1);
INSERT INTO inksuts.Sa_Config (id, Parentid, CfgName, CfgKey, CfgValue, CfgType, CfgLevel, CtrlType, CfgOption, CfgIcon, AllowUi, Userid, RowNum, EnabledMark, AllowDelete, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision) VALUES ('111', '', '', 'system.registrkey', 'kbHQqulRtSj0b/QtyNp7dnT7811mj3jPzrI97Ul6jlymjSdDdt5XmH1Bw6sLg90XRix7legEP+QaCzd4tXL7i3G7PGaUpoxKpCN+3ExKP3a/wssgbVN21o4CuVUSRVpPhosvHvWJwfgw+CmKOBIUd6p9/bjtfaAZFqWtbDgjdp6mhXD+EXtbebmzKrAEXuIMyp2Kh2/7qxU5F8/ijcgilBIQxDL1DATMDOxkjPA9lFaKPaAghhkIb5cFYHv1eyIl4CvAz7viXWi8O+rzF1Y1PDhtQeOFEM9gVYi6//YNkBObOHg4zU7QXerNMky1ZnJSJZl56qGl17HXgnUyjy7JAg==', 0, 0, 0, '', '', 0, '', 0, 0, 0, '', 'admin', '1', '2024-01-08 17:16:36', 'admin', '1', '2024-01-08 17:16:36', '', '', '', '', '', '', '', '', '', '', '', 1);
INSERT INTO inksuts.Sa_Config (id, Parentid, CfgName, CfgKey, CfgValue, CfgType, CfgLevel, CtrlType, CfgOption, CfgIcon, AllowUi, Userid, RowNum, EnabledMark, AllowDelete, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision) VALUES ('111111', '', '', 'system.oss.type', 'minio', 0, 0, 0, '', '', 0, '', 0, 0, 0, '', '', '', '2024-12-13 10:59:14', '', '', '2024-12-13 10:59:14', '', '', '', '', '', '', '', '', '', '', '', 0);
INSERT INTO inksuts.Sa_Config (id, Parentid, CfgName, CfgKey, CfgValue, CfgType, CfgLevel, CtrlType, CfgOption, CfgIcon, AllowUi, Userid, RowNum, EnabledMark, AllowDelete, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision) VALUES ('111112', '', '', 'system.oss.minio.bucket', 'utils', 0, 0, 0, '', '', 0, '', 0, 0, 0, '', '', '', '2024-12-13 10:59:14', '', '', '2024-12-13 10:59:14', '', '', '', '', '', '', '', '', '', '', '', 0);
INSERT INTO inksuts.Sa_Config (id, Parentid, CfgName, CfgKey, CfgValue, CfgType, CfgLevel, CtrlType, CfgOption, CfgIcon, AllowUi, Userid, RowNum, EnabledMark, AllowDelete, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision) VALUES ('111113', '', '', 'system.oss.minio.accesskey', 'lnkmarkdown', 0, 0, 0, '', '', 0, '', 0, 0, 0, '', '', '', '2024-12-13 10:59:14', '', '', '2024-12-13 10:59:14', '', '', '', '', '', '', '', '', '', '', '', 0);
INSERT INTO inksuts.Sa_Config (id, Parentid, CfgName, CfgKey, CfgValue, CfgType, CfgLevel, CtrlType, CfgOption, CfgIcon, AllowUi, Userid, RowNum, EnabledMark, AllowDelete, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision) VALUES ('111114', '', '', 'system.oss.minio.accesssecret', 'lnkmarkdown', 0, 0, 0, '', '', 0, '', 0, 0, 0, '', '', '', '2024-12-13 10:59:14', '', '', '2024-12-13 10:59:14', '', '', '', '', '', '', '', '', '', '', '', 0);
INSERT INTO inksuts.Sa_Config (id, Parentid, CfgName, CfgKey, CfgValue, CfgType, CfgLevel, CtrlType, CfgOption, CfgIcon, AllowUi, Userid, RowNum, EnabledMark, AllowDelete, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision) VALUES ('111115', '', '', 'system.oss.minio.endpoint', 'http://dev.inksyun.com:9080', 0, 0, 0, '', '', 0, '', 0, 0, 0, '', '', '', '2024-12-13 10:59:14', '', '', '2024-12-13 10:59:14', '', '', '', '', '', '', '', '', '', '', '', 0);
INSERT INTO inksuts.Sa_Config (id, Parentid, CfgName, CfgKey, CfgValue, CfgType, CfgLevel, CtrlType, CfgOption, CfgIcon, AllowUi, Userid, RowNum, EnabledMark, AllowDelete, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision) VALUES ('111116', '', '', 'system.oss.minio.urlprefix', 'http://dev.inksyun.com:9080/', 0, 0, 0, '', '', 0, '', 0, 0, 0, '', '', '', '2024-12-13 10:59:14', '', '', '2024-12-13 10:59:14', '', '', '', '', '', '', '', '', '', '', '', 0);
INSERT INTO inksuts.Sa_Config (id, Parentid, CfgName, CfgKey, CfgValue, CfgType, CfgLevel, CtrlType, CfgOption, CfgIcon, AllowUi, Userid, RowNum, EnabledMark, AllowDelete, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision) VALUES ('1111192', '', '', 'system.oss.aliyun.bucket', 'inksoms', 0, 0, 0, '', '', 0, '', 0, 0, 0, '', '', '', '2024-12-13 10:59:14', '', '', '2024-12-13 10:59:14', '', '', '', '', '', '', '', '', '', '', '', 0);
INSERT INTO inksuts.Sa_Config (id, Parentid, CfgName, CfgKey, CfgValue, CfgType, CfgLevel, CtrlType, CfgOption, CfgIcon, AllowUi, Userid, RowNum, EnabledMark, AllowDelete, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision) VALUES ('1111193', '', '', 'system.oss.aliyun.accesskey', 'LTAI5tL76QGhNx5eSkzkLnbv', 0, 0, 0, '', '', 0, '', 0, 0, 0, '', '', '', '2024-12-13 10:59:14', '', '', '2024-12-13 10:59:14', '', '', '', '', '', '', '', '', '', '', '', 0);
INSERT INTO inksuts.Sa_Config (id, Parentid, CfgName, CfgKey, CfgValue, CfgType, CfgLevel, CtrlType, CfgOption, CfgIcon, AllowUi, Userid, RowNum, EnabledMark, AllowDelete, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision) VALUES ('1111194', '', '', 'system.oss.aliyun.accesssecret', '******************************', 0, 0, 0, '', '', 0, '', 0, 0, 0, '', '', '', '2024-12-13 10:59:14', '', '', '2024-12-13 10:59:14', '', '', '', '', '', '', '', '', '', '', '', 0);
INSERT INTO inksuts.Sa_Config (id, Parentid, CfgName, CfgKey, CfgValue, CfgType, CfgLevel, CtrlType, CfgOption, CfgIcon, AllowUi, Userid, RowNum, EnabledMark, AllowDelete, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision) VALUES ('1111195', '', '', 'system.oss.aliyun.endpoint', 'http://oss.oms.inksyun.com', 0, 0, 0, '', '', 0, '', 0, 0, 0, '', '', '', '2024-12-13 10:59:14', '', '', '2024-12-13 10:59:14', '', '', '', '', '', '', '', '', '', '', '', 0);
INSERT INTO inksuts.Sa_Config (id, Parentid, CfgName, CfgKey, CfgValue, CfgType, CfgLevel, CtrlType, CfgOption, CfgIcon, AllowUi, Userid, RowNum, EnabledMark, AllowDelete, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision) VALUES ('1111196', '', '', 'system.oss.aliyun.urlprefix', 'http://oss.oms.inksyun.com/', 0, 0, 0, '', '', 0, '', 0, 0, 0, '', '', '', '2024-12-13 10:59:14', '', '', '2024-12-13 10:59:14', '', '', '', '', '', '', '', '', '', '', '', 0);
INSERT INTO inksuts.Sa_Config (id, Parentid, CfgName, CfgKey, CfgValue, CfgType, CfgLevel, CtrlType, CfgOption, CfgIcon, AllowUi, Userid, RowNum, EnabledMark, AllowDelete, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision) VALUES ('12', ' ', '企业CorpId', 'system.ding.corpid', 'dinge1de5c7119b54c4fa39a90f97fcb1e09', 0, 1, 0, null, null, 0, ' ', 1, 1, 0, ' ', 'admin', '1', '2024-09-13 10:10:49', 'admin', '1', '2024-09-13 10:10:49', ' ', null, null, null, null, null, '', ' ', ' ', ' ', ' ', 1);
INSERT INTO inksuts.Sa_Config (id, Parentid, CfgName, CfgKey, CfgValue, CfgType, CfgLevel, CtrlType, CfgOption, CfgIcon, AllowUi, Userid, RowNum, EnabledMark, AllowDelete, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision) VALUES ('13', ' ', '应用agentid', 'system.ding.agentid', '1494595840', 0, 1, 0, null, null, 0, ' ', 3, 1, 0, ' ', 'admin', '1', '2024-09-13 10:10:49', 'admin', '1', '2024-09-13 10:10:49', ' ', null, null, null, null, null, '', ' ', ' ', ' ', ' ', 1);
INSERT INTO inksuts.Sa_Config (id, Parentid, CfgName, CfgKey, CfgValue, CfgType, CfgLevel, CtrlType, CfgOption, CfgIcon, AllowUi, Userid, RowNum, EnabledMark, AllowDelete, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision) VALUES ('14', ' ', '开启审批', 'system.ding.apprstart', 'true', 0, 0, 2, '[{"name":"开启","value":"true"},{"name":"关闭","value":"false"}]', '', 1, ' ', 0, 1, 0, ' ', 'admin', '1', '2024-09-13 10:10:49', 'admin', '1', '2024-09-13 10:10:49', ' ', '', '', '', '', '', '', ' ', ' ', ' ', ' ', 1);
INSERT INTO inksuts.Sa_Config (id, Parentid, CfgName, CfgKey, CfgValue, CfgType, CfgLevel, CtrlType, CfgOption, CfgIcon, AllowUi, Userid, RowNum, EnabledMark, AllowDelete, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision) VALUES ('15', ' ', '应用appsecret', 'system.ding.appsecret', 'x767FiHpDPQUKOKiSv_ZqRer2nGxDLSYXBLaSP1A6FzU_c0-8WTXoRNPsoXxX0qv', 0, 1, 0, null, null, 0, ' ', 4, 1, 0, ' ', 'admin', '1', '2024-09-13 10:10:49', 'admin', '1', '2024-09-13 10:10:49', ' ', null, null, null, null, null, '', ' ', ' ', ' ', ' ', 1);
INSERT INTO inksuts.Sa_Config (id, Parentid, CfgName, CfgKey, CfgValue, CfgType, CfgLevel, CtrlType, CfgOption, CfgIcon, AllowUi, Userid, RowNum, EnabledMark, AllowDelete, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision) VALUES ('16', ' ', '应用回调aesKey', 'system.ding.callbackaeskey', 'NcFfSs1pYFicJznv4CMQGpcNmGdmCOIMASA28ZFi47d', 0, 1, 0, null, null, 0, ' ', 8, 1, 0, ' ', 'admin', '1', '2024-09-13 10:10:49', 'admin', '1', '2024-09-13 10:10:49', ' ', null, null, null, null, null, '', ' ', ' ', ' ', ' ', 1);
INSERT INTO inksuts.Sa_Config (id, Parentid, CfgName, CfgKey, CfgValue, CfgType, CfgLevel, CtrlType, CfgOption, CfgIcon, AllowUi, Userid, RowNum, EnabledMark, AllowDelete, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision) VALUES ('17', ' ', '免登Pc地址', 'system.ding.webloginurl', 'http://dev.inksyun.com:31202/#/?key={key};http://dev.inksyun.com:31102/#/loginwxe?key={key}', 0, 1, 0, null, null, 0, ' ', 6, 1, 0, ' ', 'admin', '1', '2024-09-13 10:10:49', 'admin', '1', '2024-09-13 10:10:49', ' ', null, null, null, null, null, '', ' ', ' ', ' ', ' ', 1);
INSERT INTO inksuts.Sa_Config (id, Parentid, CfgName, CfgKey, CfgValue, CfgType, CfgLevel, CtrlType, CfgOption, CfgIcon, AllowUi, Userid, RowNum, EnabledMark, AllowDelete, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision) VALUES ('171', ' ', '免登手机地址', 'system.ding.apploginurl', 'http://dev.inksyun.com:31102/#/loginwxe?key={key};http://dev.inksyun.com:31202/#/?key={key}', 0, 1, 0, null, null, 0, ' ', 5, 1, 0, ' ', 'admin', '1', '2024-09-13 10:10:49', 'admin', '1', '2024-09-13 10:10:49', ' ', null, null, null, null, null, '', ' ', ' ', ' ', ' ', 1);
INSERT INTO inksuts.Sa_Config (id, Parentid, CfgName, CfgKey, CfgValue, CfgType, CfgLevel, CtrlType, CfgOption, CfgIcon, AllowUi, Userid, RowNum, EnabledMark, AllowDelete, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision) VALUES ('172', ' ', '应用key', 'system.ding.appkey', 'dingrr8hb7enjeqrxvye', 0, 1, 0, null, null, 0, ' ', 2, 1, 0, ' ', 'admin', '1', '2024-09-13 10:10:49', 'admin', '1', '2024-09-13 10:10:49', ' ', null, null, null, null, null, '', ' ', ' ', ' ', ' ', 1);

