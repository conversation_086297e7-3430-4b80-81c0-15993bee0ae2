<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.uts.mapper.UtsSqlactuatorMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.uts.domain.pojo.UtsSqlactuatorPojo">
        <include refid="selectUtsSqlactuatorVo"/>
        where Uts_SqlActuator.id = #{key} 
    </select>
    <sql id="selectUtsSqlactuatorVo">
         select
id, SqlName, SqlData, ModuleName, Version, Databaseid, ResultJson, BusType, StatusNum, Code, SqlEcnTimestamp, Remark, CreateBy, <PERSON>reate<PERSON>yid, <PERSON>reate<PERSON>ate, <PERSON>er, <PERSON><PERSON>d, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, <PERSON>anti<PERSON>, Tenant<PERSON><PERSON>, Revision        from Uts_SqlActuator
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.uts.domain.pojo.UtsSqlactuatorPojo">
        <include refid="selectUtsSqlactuatorVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Uts_SqlActuator.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.sqlname != null ">
   and Uts_SqlActuator.SqlName like concat('%', #{SearchPojo.sqlname}, '%')
</if>
<if test="SearchPojo.sqldata != null ">
   and Uts_SqlActuator.SqlData like concat('%', #{SearchPojo.sqldata}, '%')
</if>
<if test="SearchPojo.modulename != null ">
   and Uts_SqlActuator.ModuleName like concat('%', #{SearchPojo.modulename}, '%')
</if>
<if test="SearchPojo.version != null ">
   and Uts_SqlActuator.Version like concat('%', #{SearchPojo.version}, '%')
</if>
<if test="SearchPojo.databaseid != null ">
   and Uts_SqlActuator.Databaseid like concat('%', #{SearchPojo.databaseid}, '%')
</if>
<if test="SearchPojo.resultjson != null ">
   and Uts_SqlActuator.ResultJson like concat('%', #{SearchPojo.resultjson}, '%')
</if>
<if test="SearchPojo.code != null ">
   and Uts_SqlActuator.Code like concat('%', #{SearchPojo.code}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Uts_SqlActuator.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Uts_SqlActuator.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Uts_SqlActuator.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Uts_SqlActuator.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Uts_SqlActuator.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Uts_SqlActuator.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Uts_SqlActuator.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Uts_SqlActuator.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Uts_SqlActuator.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Uts_SqlActuator.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Uts_SqlActuator.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.sqlname != null ">
   or Uts_SqlActuator.SqlName like concat('%', #{SearchPojo.sqlname}, '%')
</if>
<if test="SearchPojo.sqldata != null ">
   or Uts_SqlActuator.SqlData like concat('%', #{SearchPojo.sqldata}, '%')
</if>
<if test="SearchPojo.modulename != null ">
   or Uts_SqlActuator.ModuleName like concat('%', #{SearchPojo.modulename}, '%')
</if>
<if test="SearchPojo.version != null ">
   or Uts_SqlActuator.Version like concat('%', #{SearchPojo.version}, '%')
</if>
<if test="SearchPojo.databaseid != null ">
   or Uts_SqlActuator.Databaseid like concat('%', #{SearchPojo.databaseid}, '%')
</if>
<if test="SearchPojo.resultjson != null ">
   or Uts_SqlActuator.ResultJson like concat('%', #{SearchPojo.resultjson}, '%')
</if>
<if test="SearchPojo.code != null ">
   or Uts_SqlActuator.Code like concat('%', #{SearchPojo.code}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Uts_SqlActuator.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Uts_SqlActuator.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Uts_SqlActuator.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Uts_SqlActuator.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Uts_SqlActuator.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Uts_SqlActuator.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Uts_SqlActuator.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Uts_SqlActuator.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Uts_SqlActuator.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Uts_SqlActuator.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Uts_SqlActuator.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Uts_SqlActuator(id, SqlName, SqlData, ModuleName, Version, Databaseid, ResultJson, BusType, StatusNum, Code, SqlEcnTimestamp, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision)
        values (#{id}, #{sqlname}, #{sqldata}, #{modulename}, #{version}, #{databaseid}, #{resultjson}, #{bustype}, #{statusnum}, #{code}, #{sqlecntimestamp}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Uts_SqlActuator
        <set>
            <if test="sqlname != null ">
                SqlName =#{sqlname},
            </if>
            <if test="sqldata != null ">
                SqlData =#{sqldata},
            </if>
            <if test="modulename != null ">
                ModuleName =#{modulename},
            </if>
            <if test="version != null ">
                Version =#{version},
            </if>
            <if test="databaseid != null ">
                Databaseid =#{databaseid},
            </if>
            <if test="resultjson != null ">
                ResultJson =#{resultjson},
            </if>
            <if test="bustype != null">
                BusType =#{bustype},
            </if>
            <if test="statusnum != null">
                StatusNum =#{statusnum},
            </if>
            <if test="code != null ">
                Code =#{code},
            </if>
            <if test="sqlecntimestamp != null">
                SqlEcnTimestamp =#{sqlecntimestamp},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Uts_SqlActuator where id = #{key} 
    </delete>

    <select id="getMaxEntity" resultType="inks.service.sa.uts.domain.pojo.UtsSqlactuatorPojo">
        <include refid="selectUtsSqlactuatorVo"/>
        where Uts_SqlActuator.Tenantid = #{tenantid}
        and Uts_SqlActuator.SqlEcnTimestamp != 0
        order by Uts_SqlActuator.SqlEcnTimestamp desc
        limit 1
    </select>
</mapper>

