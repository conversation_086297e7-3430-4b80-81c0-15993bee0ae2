<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.uts.mapper.UtsMedialibraryMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.uts.domain.pojo.UtsMedialibraryPojo">
        <include refid="selectUtsMedialibraryVo"/>
        where Uts_MediaLibrary.id = #{key} 
    </select>
    <sql id="selectUtsMedialibraryVo">
         select
id, GenGroupid, FileOriName, BucketName, DirName, FileName, FileSize, ContentType, FileSuffix, Storage, FileUrl, MediaType, EnabledMark, RowNum, UseCount, Remark, CreateBy, CreateByid, <PERSON>reateDate, Lister, <PERSON><PERSON>d, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Dept<PERSON>, Tenanti<PERSON>, Tenant<PERSON><PERSON>, Revision        from Uts_MediaLibrary
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.uts.domain.pojo.UtsMedialibraryPojo">
        <include refid="selectUtsMedialibraryVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Uts_MediaLibrary.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.gengroupid != null ">
   and Uts_MediaLibrary.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
</if>
<if test="SearchPojo.fileoriname != null ">
   and Uts_MediaLibrary.FileOriName like concat('%', #{SearchPojo.fileoriname}, '%')
</if>
<if test="SearchPojo.bucketname != null ">
   and Uts_MediaLibrary.BucketName like concat('%', #{SearchPojo.bucketname}, '%')
</if>
<if test="SearchPojo.dirname != null ">
   and Uts_MediaLibrary.DirName like concat('%', #{SearchPojo.dirname}, '%')
</if>
<if test="SearchPojo.filename != null ">
   and Uts_MediaLibrary.FileName like concat('%', #{SearchPojo.filename}, '%')
</if>
<if test="SearchPojo.contenttype != null ">
   and Uts_MediaLibrary.ContentType like concat('%', #{SearchPojo.contenttype}, '%')
</if>
<if test="SearchPojo.filesuffix != null ">
   and Uts_MediaLibrary.FileSuffix like concat('%', #{SearchPojo.filesuffix}, '%')
</if>
<if test="SearchPojo.storage != null ">
   and Uts_MediaLibrary.Storage like concat('%', #{SearchPojo.storage}, '%')
</if>
<if test="SearchPojo.fileurl != null ">
   and Uts_MediaLibrary.FileUrl like concat('%', #{SearchPojo.fileurl}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Uts_MediaLibrary.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Uts_MediaLibrary.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Uts_MediaLibrary.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Uts_MediaLibrary.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Uts_MediaLibrary.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Uts_MediaLibrary.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Uts_MediaLibrary.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Uts_MediaLibrary.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Uts_MediaLibrary.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Uts_MediaLibrary.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Uts_MediaLibrary.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Uts_MediaLibrary.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Uts_MediaLibrary.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Uts_MediaLibrary.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Uts_MediaLibrary.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   and Uts_MediaLibrary.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Uts_MediaLibrary.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.gengroupid != null ">
   or Uts_MediaLibrary.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
</if>
<if test="SearchPojo.fileoriname != null ">
   or Uts_MediaLibrary.FileOriName like concat('%', #{SearchPojo.fileoriname}, '%')
</if>
<if test="SearchPojo.bucketname != null ">
   or Uts_MediaLibrary.BucketName like concat('%', #{SearchPojo.bucketname}, '%')
</if>
<if test="SearchPojo.dirname != null ">
   or Uts_MediaLibrary.DirName like concat('%', #{SearchPojo.dirname}, '%')
</if>
<if test="SearchPojo.filename != null ">
   or Uts_MediaLibrary.FileName like concat('%', #{SearchPojo.filename}, '%')
</if>
<if test="SearchPojo.contenttype != null ">
   or Uts_MediaLibrary.ContentType like concat('%', #{SearchPojo.contenttype}, '%')
</if>
<if test="SearchPojo.filesuffix != null ">
   or Uts_MediaLibrary.FileSuffix like concat('%', #{SearchPojo.filesuffix}, '%')
</if>
<if test="SearchPojo.storage != null ">
   or Uts_MediaLibrary.Storage like concat('%', #{SearchPojo.storage}, '%')
</if>
<if test="SearchPojo.fileurl != null ">
   or Uts_MediaLibrary.FileUrl like concat('%', #{SearchPojo.fileurl}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Uts_MediaLibrary.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Uts_MediaLibrary.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Uts_MediaLibrary.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Uts_MediaLibrary.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Uts_MediaLibrary.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Uts_MediaLibrary.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Uts_MediaLibrary.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Uts_MediaLibrary.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Uts_MediaLibrary.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Uts_MediaLibrary.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Uts_MediaLibrary.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Uts_MediaLibrary.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Uts_MediaLibrary.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Uts_MediaLibrary.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Uts_MediaLibrary.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   or Uts_MediaLibrary.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Uts_MediaLibrary.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Uts_MediaLibrary(id, GenGroupid, FileOriName, BucketName, DirName, FileName, FileSize, ContentType, FileSuffix, Storage, FileUrl, MediaType, EnabledMark, RowNum, UseCount, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Deptid, Tenantid, TenantName, Revision)
        values (#{id}, #{gengroupid}, #{fileoriname}, #{bucketname}, #{dirname}, #{filename}, #{filesize}, #{contenttype}, #{filesuffix}, #{storage}, #{fileurl}, #{mediatype}, #{enabledmark}, #{rownum}, #{usecount}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{deptid}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Uts_MediaLibrary
        <set>
            <if test="gengroupid != null ">
                GenGroupid =#{gengroupid},
            </if>
            <if test="fileoriname != null ">
                FileOriName =#{fileoriname},
            </if>
            <if test="bucketname != null ">
                BucketName =#{bucketname},
            </if>
            <if test="dirname != null ">
                DirName =#{dirname},
            </if>
            <if test="filename != null ">
                FileName =#{filename},
            </if>
            <if test="filesize != null">
                FileSize =#{filesize},
            </if>
            <if test="contenttype != null ">
                ContentType =#{contenttype},
            </if>
            <if test="filesuffix != null ">
                FileSuffix =#{filesuffix},
            </if>
            <if test="storage != null ">
                Storage =#{storage},
            </if>
            <if test="fileurl != null ">
                FileUrl =#{fileurl},
            </if>
            <if test="mediatype != null">
                MediaType =#{mediatype},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="usecount != null">
                UseCount =#{usecount},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="deptid != null ">
                Deptid =#{deptid},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Uts_MediaLibrary where id = #{key} 
    </delete>
</mapper>

