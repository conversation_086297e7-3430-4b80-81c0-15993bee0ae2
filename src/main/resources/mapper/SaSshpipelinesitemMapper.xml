<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.uts.mapper.SaSshpipelinesitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.uts.domain.pojo.SaSshpipelinesitemPojo">
        <include refid="selectSaSshpipelinesitemVo"/>
        where Sa_SshPipelinesItem.id = #{key} 
    </select>
    <sql id="selectSaSshpipelinesitemVo">
         select
id, Pid, StepName, CommandText, SuccessPattern, ErrorPattern, TimeoutMs, ContinueOnError, RetryCount, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision        from Sa_SshPipelinesItem
    </sql>
         <!--查询List-->
    <select id="getList" resultType="inks.service.sa.uts.domain.pojo.SaSshpipelinesitemPojo">
        <include refid="selectSaSshpipelinesitemVo"/>
        where Sa_SshPipelinesItem.Pid = #{Pid}
        order by RowNum 
    </select>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.uts.domain.pojo.SaSshpipelinesitemPojo">
        <include refid="selectSaSshpipelinesitemVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_SshPipelinesItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Sa_SshPipelinesItem.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.stepname != null and SearchPojo.stepname != ''">
   and Sa_SshPipelinesItem.stepname like concat('%', #{SearchPojo.stepname}, '%')
</if>
<if test="SearchPojo.commandtext != null and SearchPojo.commandtext != ''">
   and Sa_SshPipelinesItem.commandtext like concat('%', #{SearchPojo.commandtext}, '%')
</if>
<if test="SearchPojo.successpattern != null and SearchPojo.successpattern != ''">
   and Sa_SshPipelinesItem.successpattern like concat('%', #{SearchPojo.successpattern}, '%')
</if>
<if test="SearchPojo.errorpattern != null and SearchPojo.errorpattern != ''">
   and Sa_SshPipelinesItem.errorpattern like concat('%', #{SearchPojo.errorpattern}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and Sa_SshPipelinesItem.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Sa_SshPipelinesItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Sa_SshPipelinesItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Sa_SshPipelinesItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Sa_SshPipelinesItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Sa_SshPipelinesItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Sa_SshPipelinesItem.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.stepname != null and SearchPojo.stepname != ''">
   or Sa_SshPipelinesItem.StepName like concat('%', #{SearchPojo.stepname}, '%')
</if>
<if test="SearchPojo.commandtext != null and SearchPojo.commandtext != ''">
   or Sa_SshPipelinesItem.CommandText like concat('%', #{SearchPojo.commandtext}, '%')
</if>
<if test="SearchPojo.successpattern != null and SearchPojo.successpattern != ''">
   or Sa_SshPipelinesItem.SuccessPattern like concat('%', #{SearchPojo.successpattern}, '%')
</if>
<if test="SearchPojo.errorpattern != null and SearchPojo.errorpattern != ''">
   or Sa_SshPipelinesItem.ErrorPattern like concat('%', #{SearchPojo.errorpattern}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Sa_SshPipelinesItem.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Sa_SshPipelinesItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Sa_SshPipelinesItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Sa_SshPipelinesItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Sa_SshPipelinesItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Sa_SshPipelinesItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
</trim>
     </sql>

    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_SshPipelinesItem(id, Pid, StepName, CommandText, SuccessPattern, ErrorPattern, TimeoutMs, ContinueOnError, RetryCount, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision)
        values (#{id}, #{pid}, #{stepname}, #{commandtext}, #{successpattern}, #{errorpattern}, #{timeoutms}, #{continueonerror}, #{retrycount}, #{rownum}, #{remark}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_SshPipelinesItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="stepname != null ">
                StepName = #{stepname},
            </if>
            <if test="commandtext != null ">
                CommandText = #{commandtext},
            </if>
            <if test="successpattern != null ">
                SuccessPattern = #{successpattern},
            </if>
            <if test="errorpattern != null ">
                ErrorPattern = #{errorpattern},
            </if>
            <if test="timeoutms != null">
                TimeoutMs = #{timeoutms},
            </if>
            <if test="continueonerror != null">
                ContinueOnError = #{continueonerror},
            </if>
            <if test="retrycount != null">
                RetryCount = #{retrycount},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_SshPipelinesItem where id = #{key} 
    </delete>

</mapper>

