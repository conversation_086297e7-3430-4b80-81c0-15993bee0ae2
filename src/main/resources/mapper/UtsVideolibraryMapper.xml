<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.uts.mapper.UtsVideolibraryMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.uts.domain.pojo.UtsVideolibraryPojo">
        <include refid="selectUtsVideolibraryVo"/>
        where Uts_VideoLibrary.id = #{key} 
    </select>
    <sql id="selectUtsVideolibraryVo">
         select
id, VideoTitle, VideoFileName, ObjectName, VideoCoverUrl, VideoDuration, VideoPlayTimes, VideoDesc, GenGroupid, FileSize, SecretKey, UploadTime, TextTutorial, VideoTag, BackColorArgb, ForeColorArgb, PublicMark, EnabledMark, RowN<PERSON>, <PERSON>mark, <PERSON><PERSON><PERSON>y, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>er, Listerid, ModifyDate, Assessor, Assessorid, AssessDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision        from Uts_VideoLibrary
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.uts.domain.pojo.UtsVideolibraryPojo">
        <include refid="selectUtsVideolibraryVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Uts_VideoLibrary.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.videotitle != null ">
   and Uts_VideoLibrary.VideoTitle like concat('%', #{SearchPojo.videotitle}, '%')
</if>
<if test="SearchPojo.videofilename != null ">
   and Uts_VideoLibrary.VideoFileName like concat('%', #{SearchPojo.videofilename}, '%')
</if>
<if test="SearchPojo.objectname != null ">
   and Uts_VideoLibrary.ObjectName like concat('%', #{SearchPojo.objectname}, '%')
</if>
<if test="SearchPojo.videocoverurl != null ">
   and Uts_VideoLibrary.VideoCoverUrl like concat('%', #{SearchPojo.videocoverurl}, '%')
</if>
<if test="SearchPojo.videodesc != null ">
   and Uts_VideoLibrary.VideoDesc like concat('%', #{SearchPojo.videodesc}, '%')
</if>
<if test="SearchPojo.gengroupid != null ">
   and Uts_VideoLibrary.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
</if>
<if test="SearchPojo.secretkey != null ">
   and Uts_VideoLibrary.SecretKey like concat('%', #{SearchPojo.secretkey}, '%')
</if>
<if test="SearchPojo.texttutorial != null ">
   and Uts_VideoLibrary.TextTutorial like concat('%', #{SearchPojo.texttutorial}, '%')
</if>
<if test="SearchPojo.videotag != null ">
   and Uts_VideoLibrary.VideoTag like concat('%', #{SearchPojo.videotag}, '%')
</if>
<if test="SearchPojo.backcolorargb != null ">
   and Uts_VideoLibrary.BackColorArgb like concat('%', #{SearchPojo.backcolorargb}, '%')
</if>
<if test="SearchPojo.forecolorargb != null ">
   and Uts_VideoLibrary.ForeColorArgb like concat('%', #{SearchPojo.forecolorargb}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Uts_VideoLibrary.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Uts_VideoLibrary.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Uts_VideoLibrary.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Uts_VideoLibrary.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Uts_VideoLibrary.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Uts_VideoLibrary.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Uts_VideoLibrary.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Uts_VideoLibrary.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Uts_VideoLibrary.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Uts_VideoLibrary.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Uts_VideoLibrary.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Uts_VideoLibrary.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Uts_VideoLibrary.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Uts_VideoLibrary.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Uts_VideoLibrary.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Uts_VideoLibrary.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Uts_VideoLibrary.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Uts_VideoLibrary.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.videotitle != null ">
   or Uts_VideoLibrary.VideoTitle like concat('%', #{SearchPojo.videotitle}, '%')
</if>
<if test="SearchPojo.videofilename != null ">
   or Uts_VideoLibrary.VideoFileName like concat('%', #{SearchPojo.videofilename}, '%')
</if>
<if test="SearchPojo.objectname != null ">
   or Uts_VideoLibrary.ObjectName like concat('%', #{SearchPojo.objectname}, '%')
</if>
<if test="SearchPojo.videocoverurl != null ">
   or Uts_VideoLibrary.VideoCoverUrl like concat('%', #{SearchPojo.videocoverurl}, '%')
</if>
<if test="SearchPojo.videodesc != null ">
   or Uts_VideoLibrary.VideoDesc like concat('%', #{SearchPojo.videodesc}, '%')
</if>
<if test="SearchPojo.gengroupid != null ">
   or Uts_VideoLibrary.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
</if>
<if test="SearchPojo.secretkey != null ">
   or Uts_VideoLibrary.SecretKey like concat('%', #{SearchPojo.secretkey}, '%')
</if>
<if test="SearchPojo.texttutorial != null ">
   or Uts_VideoLibrary.TextTutorial like concat('%', #{SearchPojo.texttutorial}, '%')
</if>
<if test="SearchPojo.videotag != null ">
   or Uts_VideoLibrary.VideoTag like concat('%', #{SearchPojo.videotag}, '%')
</if>
<if test="SearchPojo.backcolorargb != null ">
   or Uts_VideoLibrary.BackColorArgb like concat('%', #{SearchPojo.backcolorargb}, '%')
</if>
<if test="SearchPojo.forecolorargb != null ">
   or Uts_VideoLibrary.ForeColorArgb like concat('%', #{SearchPojo.forecolorargb}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Uts_VideoLibrary.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Uts_VideoLibrary.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Uts_VideoLibrary.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Uts_VideoLibrary.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Uts_VideoLibrary.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Uts_VideoLibrary.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Uts_VideoLibrary.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Uts_VideoLibrary.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Uts_VideoLibrary.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Uts_VideoLibrary.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Uts_VideoLibrary.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Uts_VideoLibrary.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Uts_VideoLibrary.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Uts_VideoLibrary.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Uts_VideoLibrary.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Uts_VideoLibrary.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Uts_VideoLibrary.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Uts_VideoLibrary.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Uts_VideoLibrary(id, VideoTitle, VideoFileName, ObjectName, VideoCoverUrl, VideoDuration, VideoPlayTimes, VideoDesc, GenGroupid, FileSize, SecretKey, UploadTime, TextTutorial, VideoTag, BackColorArgb, ForeColorArgb, PublicMark, EnabledMark, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{videotitle}, #{videofilename}, #{objectname}, #{videocoverurl}, #{videoduration}, #{videoplaytimes}, #{videodesc}, #{gengroupid}, #{filesize}, #{secretkey}, #{uploadtime}, #{texttutorial}, #{videotag}, #{backcolorargb}, #{forecolorargb}, #{publicmark}, #{enabledmark}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Uts_VideoLibrary
        <set>
            <if test="videotitle != null ">
                VideoTitle =#{videotitle},
            </if>
            <if test="videofilename != null ">
                VideoFileName =#{videofilename},
            </if>
            <if test="objectname != null ">
                ObjectName =#{objectname},
            </if>
            <if test="videocoverurl != null ">
                VideoCoverUrl =#{videocoverurl},
            </if>
            <if test="videoduration != null">
                VideoDuration =#{videoduration},
            </if>
            <if test="videoplaytimes != null">
                VideoPlayTimes =#{videoplaytimes},
            </if>
            <if test="videodesc != null ">
                VideoDesc =#{videodesc},
            </if>
            <if test="gengroupid != null ">
                GenGroupid =#{gengroupid},
            </if>
            <if test="filesize != null">
                FileSize =#{filesize},
            </if>
            <if test="secretkey != null ">
                SecretKey =#{secretkey},
            </if>
            <if test="uploadtime != null">
                UploadTime =#{uploadtime},
            </if>
            <if test="texttutorial != null ">
                TextTutorial =#{texttutorial},
            </if>
            <if test="videotag != null ">
                VideoTag =#{videotag},
            </if>
            <if test="backcolorargb != null ">
                BackColorArgb =#{backcolorargb},
            </if>
            <if test="forecolorargb != null ">
                ForeColorArgb =#{forecolorargb},
            </if>
            <if test="publicmark != null">
                PublicMark =#{publicmark},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Uts_VideoLibrary where id = #{key} 
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Uts_VideoLibrary SET
            Assessor = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision+1
        where id = #{id}
    </update>
</mapper>

