<?xml version="1.0" encoding="UTF-8"?>
        <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.uts.mapper.UtsFreereportsMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.uts.domain.pojo.UtsFreereportsPojo">
        <include refid="selectbillVo"/>
        where Uts_FreeReports.id = #{key}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select Uts_FreeReports.id,
               Uts_FreeReports.FRType,
               Uts_FreeReports.FRGroupid,
               Uts_FreeReports.LocalMark,
               Uts_FreeReports.ReportCode,
               Uts_FreeReports.ReportName,
               Uts_FreeReports.DynType,
               Uts_FreeReports.DynSentence,
               Uts_FreeReports.SqlFull,
               Uts_FreeReports.SqlSelect,
               Uts_FreeReports.SqlFrom,
               Uts_FreeReports.SqlWhere,
               Uts_FreeReports.SqlGroupBy,
               Uts_FreeReports.SqlHaving,
               Uts_FreeReports.SqlOrderBy,
               Uts_FreeReports.SqlLimit,
               Uts_FreeReports.MainTable,
               Uts_FreeReports.EnabledMark,
               Uts_FreeReports.PublicMark,
               Uts_FreeReports.Databaseid,
               Uts_FreeReports.DomainNum,
               Uts_FreeReports.Userid,
               Uts_FreeReports.ImageIndex,
               Uts_FreeReports.PermissionCode,
               Uts_FreeReports.ImageStyle,
               Uts_FreeReports.DatePath,
               Uts_FreeReports.AuthCode,
               Uts_FreeReports.CaseResult,
               Uts_FreeReports.ReportType,
               Uts_FreeReports.ChartType,
               Uts_FreeReports.RowNum,
               Uts_FreeReports.Summary,
               Uts_FreeReports.CreateBy,
               Uts_FreeReports.CreateByid,
               Uts_FreeReports.CreateDate,
               Uts_FreeReports.Lister,
               Uts_FreeReports.Listerid,
               Uts_FreeReports.ModifyDate,
               Uts_FreeReports.Custom1,
               Uts_FreeReports.Custom2,
               Uts_FreeReports.Custom3,
               Uts_FreeReports.Custom4,
               Uts_FreeReports.Custom5,
               Uts_FreeReports.Tenantid,
               Uts_FreeReports.TenantName,
               Uts_FreeReports.Revision,
               Uts_Database.Title as DatabaseTitle
        from Uts_FreeReports
                 left join Uts_Database on Uts_Database.id = Uts_FreeReports.Databaseid
    </sql>
    <sql id="selectdetailVo">
        select
               Uts_FreeReports.CreateBy,
               Uts_FreeReports.Lister,
               Uts_FreeReportsItem.*
        from Uts_FreeReportsItem left join Uts_FreeReports on Uts_FreeReports.id = Uts_FreeReportsItem.Pid
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.uts.domain.pojo.UtsFreereportsitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Uts_FreeReports.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.frtype != null ">
            and Uts_FreeReports.FRType like concat('%', #{SearchPojo.frtype}, '%')
        </if>
        <if test="SearchPojo.frgroupid != null ">
            and Uts_FreeReports.FRGroupid like concat('%', #{SearchPojo.frgroupid}, '%')
        </if>
        <if test="SearchPojo.reportcode != null ">
            and Uts_FreeReports.ReportCode like concat('%', #{SearchPojo.reportcode}, '%')
        </if>
        <if test="SearchPojo.reportname != null ">
            and Uts_FreeReports.ReportName like concat('%', #{SearchPojo.reportname}, '%')
        </if>
        <if test="SearchPojo.dynsentence != null ">
            and Uts_FreeReports.DynSentence like concat('%', #{SearchPojo.dynsentence}, '%')
        </if>
        <if test="SearchPojo.sqlfull != null ">
            and Uts_FreeReports.SqlFull like concat('%', #{SearchPojo.sqlfull}, '%')
        </if>
        <if test="SearchPojo.sqlselect != null ">
            and Uts_FreeReports.SqlSelect like concat('%', #{SearchPojo.sqlselect}, '%')
        </if>
        <if test="SearchPojo.sqlfrom != null ">
            and Uts_FreeReports.SqlFrom like concat('%', #{SearchPojo.sqlfrom}, '%')
        </if>
        <if test="SearchPojo.sqlwhere != null ">
            and Uts_FreeReports.SqlWhere like concat('%', #{SearchPojo.sqlwhere}, '%')
        </if>
        <if test="SearchPojo.sqlgroupby != null ">
            and Uts_FreeReports.SqlGroupBy like concat('%', #{SearchPojo.sqlgroupby}, '%')
        </if>
        <if test="SearchPojo.sqlhaving != null ">
            and Uts_FreeReports.SqlHaving like concat('%', #{SearchPojo.sqlhaving}, '%')
        </if>
        <if test="SearchPojo.sqlorderby != null ">
            and Uts_FreeReports.SqlOrderBy like concat('%', #{SearchPojo.sqlorderby}, '%')
        </if>
        <if test="SearchPojo.sqllimit != null ">
            and Uts_FreeReports.SqlLimit like concat('%', #{SearchPojo.sqllimit}, '%')
        </if>
        <if test="SearchPojo.maintable != null ">
            and Uts_FreeReports.MainTable like concat('%', #{SearchPojo.maintable}, '%')
        </if>
        <if test="SearchPojo.databaseid != null ">
            and Uts_FreeReports.Databaseid like concat('%', #{SearchPojo.databaseid}, '%')
        </if>
        <if test="SearchPojo.userid != null ">
            and Uts_FreeReports.Userid like concat('%', #{SearchPojo.userid}, '%')
        </if>
        <if test="SearchPojo.imageindex != null ">
            and Uts_FreeReports.ImageIndex like concat('%', #{SearchPojo.imageindex}, '%')
        </if>
        <if test="SearchPojo.permissioncode != null ">
            and Uts_FreeReports.PermissionCode like concat('%', #{SearchPojo.permissioncode}, '%')
        </if>
        <if test="SearchPojo.imagestyle != null ">
            and Uts_FreeReports.ImageStyle like concat('%', #{SearchPojo.imagestyle}, '%')
        </if>
        <if test="SearchPojo.datepath != null ">
            and Uts_FreeReports.DatePath like concat('%', #{SearchPojo.datepath}, '%')
        </if>
        <if test="SearchPojo.caseresult != null ">
            and Uts_FreeReports.CaseResult like concat('%', #{SearchPojo.caseresult}, '%')
        </if>
        <if test="SearchPojo.reporttype != null ">
            and Uts_FreeReports.ReportType like concat('%', #{SearchPojo.reporttype}, '%')
        </if>
        <if test="SearchPojo.charttype != null ">
            and Uts_FreeReports.ChartType like concat('%', #{SearchPojo.charttype}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Uts_FreeReports.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Uts_FreeReports.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Uts_FreeReports.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Uts_FreeReports.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Uts_FreeReports.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Uts_FreeReports.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Uts_FreeReports.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Uts_FreeReports.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Uts_FreeReports.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Uts_FreeReports.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Uts_FreeReports.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.frtype != null ">
                or Uts_FreeReports.FRType like concat('%', #{SearchPojo.frtype}, '%')
            </if>
            <if test="SearchPojo.frgroupid != null ">
                or Uts_FreeReports.FRGroupid like concat('%', #{SearchPojo.frgroupid}, '%')
            </if>
            <if test="SearchPojo.reportcode != null ">
                or Uts_FreeReports.ReportCode like concat('%', #{SearchPojo.reportcode}, '%')
            </if>
            <if test="SearchPojo.reportname != null ">
                or Uts_FreeReports.ReportName like concat('%', #{SearchPojo.reportname}, '%')
            </if>
            <if test="SearchPojo.dynsentence != null ">
                or Uts_FreeReports.DynSentence like concat('%', #{SearchPojo.dynsentence}, '%')
            </if>
            <if test="SearchPojo.sqlfull != null ">
                or Uts_FreeReports.SqlFull like concat('%', #{SearchPojo.sqlfull}, '%')
            </if>
            <if test="SearchPojo.sqlselect != null ">
                or Uts_FreeReports.SqlSelect like concat('%', #{SearchPojo.sqlselect}, '%')
            </if>
            <if test="SearchPojo.sqlfrom != null ">
                or Uts_FreeReports.SqlFrom like concat('%', #{SearchPojo.sqlfrom}, '%')
            </if>
            <if test="SearchPojo.sqlwhere != null ">
                or Uts_FreeReports.SqlWhere like concat('%', #{SearchPojo.sqlwhere}, '%')
            </if>
            <if test="SearchPojo.sqlgroupby != null ">
                or Uts_FreeReports.SqlGroupBy like concat('%', #{SearchPojo.sqlgroupby}, '%')
            </if>
            <if test="SearchPojo.sqlhaving != null ">
                or Uts_FreeReports.SqlHaving like concat('%', #{SearchPojo.sqlhaving}, '%')
            </if>
            <if test="SearchPojo.sqlorderby != null ">
                or Uts_FreeReports.SqlOrderBy like concat('%', #{SearchPojo.sqlorderby}, '%')
            </if>
            <if test="SearchPojo.sqllimit != null ">
                or Uts_FreeReports.SqlLimit like concat('%', #{SearchPojo.sqllimit}, '%')
            </if>
            <if test="SearchPojo.maintable != null ">
                or Uts_FreeReports.MainTable like concat('%', #{SearchPojo.maintable}, '%')
            </if>
            <if test="SearchPojo.databaseid != null ">
                or Uts_FreeReports.Databaseid like concat('%', #{SearchPojo.databaseid}, '%')
            </if>
            <if test="SearchPojo.userid != null ">
                or Uts_FreeReports.Userid like concat('%', #{SearchPojo.userid}, '%')
            </if>
            <if test="SearchPojo.imageindex != null ">
                or Uts_FreeReports.ImageIndex like concat('%', #{SearchPojo.imageindex}, '%')
            </if>
            <if test="SearchPojo.permissioncode != null ">
                or Uts_FreeReports.PermissionCode like concat('%', #{SearchPojo.permissioncode}, '%')
            </if>
            <if test="SearchPojo.imagestyle != null ">
                or Uts_FreeReports.ImageStyle like concat('%', #{SearchPojo.imagestyle}, '%')
            </if>
            <if test="SearchPojo.datepath != null ">
                or Uts_FreeReports.DatePath like concat('%', #{SearchPojo.datepath}, '%')
            </if>
            <if test="SearchPojo.caseresult != null ">
                or Uts_FreeReports.CaseResult like concat('%', #{SearchPojo.caseresult}, '%')
            </if>
            <if test="SearchPojo.reporttype != null ">
                or Uts_FreeReports.ReportType like concat('%', #{SearchPojo.reporttype}, '%')
            </if>
            <if test="SearchPojo.charttype != null ">
                or Uts_FreeReports.ChartType like concat('%', #{SearchPojo.charttype}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Uts_FreeReports.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Uts_FreeReports.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Uts_FreeReports.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Uts_FreeReports.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Uts_FreeReports.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Uts_FreeReports.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Uts_FreeReports.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Uts_FreeReports.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Uts_FreeReports.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Uts_FreeReports.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Uts_FreeReports.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.uts.domain.pojo.UtsFreereportsPojo">
        <include refid="selectbillVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Uts_FreeReports.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="thand"></include>
            </if>
            <if test="SearchType==1">
                <include refid="thor"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.frtype != null ">
            and Uts_FreeReports.FRType like concat('%', #{SearchPojo.frtype}, '%')
        </if>
        <if test="SearchPojo.frgroupid != null ">
            and Uts_FreeReports.FRGroupid like concat('%', #{SearchPojo.frgroupid}, '%')
        </if>
        <if test="SearchPojo.reportcode != null ">
            and Uts_FreeReports.ReportCode like concat('%', #{SearchPojo.reportcode}, '%')
        </if>
        <if test="SearchPojo.reportname != null ">
            and Uts_FreeReports.ReportName like concat('%', #{SearchPojo.reportname}, '%')
        </if>
        <if test="SearchPojo.sqlfull != null ">
            and Uts_FreeReports.SqlFull like concat('%', #{SearchPojo.sqlfull}, '%')
        </if>
        <if test="SearchPojo.sqlselect != null ">
            and Uts_FreeReports.SqlSelect like concat('%', #{SearchPojo.sqlselect}, '%')
        </if>
        <if test="SearchPojo.sqlfrom != null ">
            and Uts_FreeReports.SqlFrom like concat('%', #{SearchPojo.sqlfrom}, '%')
        </if>
        <if test="SearchPojo.sqlwhere != null ">
            and Uts_FreeReports.SqlWhere like concat('%', #{SearchPojo.sqlwhere}, '%')
        </if>
        <if test="SearchPojo.sqlgroupby != null ">
            and Uts_FreeReports.SqlGroupBy like concat('%', #{SearchPojo.sqlgroupby}, '%')
        </if>
        <if test="SearchPojo.sqlhaving != null ">
            and Uts_FreeReports.SqlHaving like concat('%', #{SearchPojo.sqlhaving}, '%')
        </if>
        <if test="SearchPojo.sqlorderby != null ">
            and Uts_FreeReports.SqlOrderBy like concat('%', #{SearchPojo.sqlorderby}, '%')
        </if>
        <if test="SearchPojo.sqllimit != null ">
            and Uts_FreeReports.SqlLimit like concat('%', #{SearchPojo.sqllimit}, '%')
        </if>
        <if test="SearchPojo.databaseid != null ">
            and Uts_FreeReports.Databaseid like concat('%', #{SearchPojo.databaseid}, '%')
        </if>
        <if test="SearchPojo.userid != null ">
            and Uts_FreeReports.Userid like concat('%', #{SearchPojo.userid}, '%')
        </if>
        <if test="SearchPojo.imageindex != null ">
            and Uts_FreeReports.ImageIndex like concat('%', #{SearchPojo.imageindex}, '%')
        </if>
        <if test="SearchPojo.permissioncode != null ">
            and Uts_FreeReports.PermissionCode like concat('%', #{SearchPojo.permissioncode}, '%')
        </if>
        <if test="SearchPojo.summary != null ">
            and Uts_FreeReports.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Uts_FreeReports.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Uts_FreeReports.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Uts_FreeReports.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Uts_FreeReports.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null ">
            and Uts_FreeReports.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null ">
            and Uts_FreeReports.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null ">
            and Uts_FreeReports.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null ">
            and Uts_FreeReports.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null ">
            and Uts_FreeReports.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Uts_FreeReports.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.frtype != null ">
                or Uts_FreeReports.FRType like concat('%', #{SearchPojo.frtype}, '%')
            </if>
            <if test="SearchPojo.frgroupid != null ">
                or Uts_FreeReports.FRGroupid like concat('%', #{SearchPojo.frgroupid}, '%')
            </if>
            <if test="SearchPojo.reportcode != null ">
                or Uts_FreeReports.ReportCode like concat('%', #{SearchPojo.reportcode}, '%')
            </if>
            <if test="SearchPojo.reportname != null ">
                or Uts_FreeReports.ReportName like concat('%', #{SearchPojo.reportname}, '%')
            </if>
            <if test="SearchPojo.sqlfull != null ">
                or Uts_FreeReports.SqlFull like concat('%', #{SearchPojo.sqlfull}, '%')
            </if>
            <if test="SearchPojo.sqlselect != null ">
                or Uts_FreeReports.SqlSelect like concat('%', #{SearchPojo.sqlselect}, '%')
            </if>
            <if test="SearchPojo.sqlfrom != null ">
                or Uts_FreeReports.SqlFrom like concat('%', #{SearchPojo.sqlfrom}, '%')
            </if>
            <if test="SearchPojo.sqlwhere != null ">
                or Uts_FreeReports.SqlWhere like concat('%', #{SearchPojo.sqlwhere}, '%')
            </if>
            <if test="SearchPojo.sqlgroupby != null ">
                or Uts_FreeReports.SqlGroupBy like concat('%', #{SearchPojo.sqlgroupby}, '%')
            </if>
            <if test="SearchPojo.sqlhaving != null ">
                or Uts_FreeReports.SqlHaving like concat('%', #{SearchPojo.sqlhaving}, '%')
            </if>
            <if test="SearchPojo.sqlorderby != null ">
                or Uts_FreeReports.SqlOrderBy like concat('%', #{SearchPojo.sqlorderby}, '%')
            </if>
            <if test="SearchPojo.sqllimit != null ">
                or Uts_FreeReports.SqlLimit like concat('%', #{SearchPojo.sqllimit}, '%')
            </if>
            <if test="SearchPojo.databaseid != null ">
                or Uts_FreeReports.Databaseid like concat('%', #{SearchPojo.databaseid}, '%')
            </if>
            <if test="SearchPojo.userid != null ">
                or Uts_FreeReports.Userid like concat('%', #{SearchPojo.userid}, '%')
            </if>
            <if test="SearchPojo.imageindex != null ">
                or Uts_FreeReports.ImageIndex like concat('%', #{SearchPojo.imageindex}, '%')
            </if>
            <if test="SearchPojo.permissioncode != null ">
                or Uts_FreeReports.PermissionCode like concat('%', #{SearchPojo.permissioncode}, '%')
            </if>
            <if test="SearchPojo.summary != null ">
                or Uts_FreeReports.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Uts_FreeReports.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Uts_FreeReports.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Uts_FreeReports.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Uts_FreeReports.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null ">
                or Uts_FreeReports.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null ">
                or Uts_FreeReports.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null ">
                or Uts_FreeReports.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null ">
                or Uts_FreeReports.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null ">
                or Uts_FreeReports.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Uts_FreeReports.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert" >
        insert into Uts_FreeReports(id, FRType, FRGroupid, LocalMark, ReportCode, ReportName, DynType, DynSentence, SqlFull, SqlSelect, SqlFrom, SqlWhere, SqlGroupBy, SqlHaving, SqlOrderBy, SqlLimit, MainTable, EnabledMark, PublicMark, Databaseid, DomainNum, Userid, ImageIndex, PermissionCode, ImageStyle, DatePath, AuthCode, CaseResult, ReportType, ChartType, RowNum, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision)
        values (#{id}, #{frtype}, #{frgroupid}, #{localmark}, #{reportcode}, #{reportname}, #{dyntype}, #{dynsentence}, #{sqlfull}, #{sqlselect}, #{sqlfrom}, #{sqlwhere}, #{sqlgroupby}, #{sqlhaving}, #{sqlorderby}, #{sqllimit}, #{maintable}, #{enabledmark}, #{publicmark}, #{databaseid}, #{domainnum}, #{userid}, #{imageindex}, #{permissioncode}, #{imagestyle}, #{datepath}, #{authcode}, #{caseresult}, #{reporttype}, #{charttype}, #{rownum}, #{summary}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Uts_FreeReports
        <set>
            <if test="frtype != null ">
            FRType =#{frtype},
        </if>
            <if test="frgroupid != null ">
            FRGroupid =#{frgroupid},
        </if>
            <if test="localmark != null">
            LocalMark =#{localmark},
        </if>
            <if test="reportcode != null ">
            ReportCode =#{reportcode},
        </if>
            <if test="reportname != null ">
            ReportName =#{reportname},
        </if>
            <if test="dyntype != null ">
            DynType =#{dyntype},
        </if>
            <if test="dynsentence != null ">
            DynSentence =#{dynsentence},
        </if>
            <if test="sqlfull != null ">
            SqlFull =#{sqlfull},
        </if>
            <if test="sqlselect != null ">
            SqlSelect =#{sqlselect},
        </if>
            <if test="sqlfrom != null ">
            SqlFrom =#{sqlfrom},
        </if>
            <if test="sqlwhere != null ">
            SqlWhere =#{sqlwhere},
        </if>
            <if test="sqlgroupby != null ">
            SqlGroupBy =#{sqlgroupby},
        </if>
            <if test="sqlhaving != null ">
            SqlHaving =#{sqlhaving},
        </if>
            <if test="sqlorderby != null ">
            SqlOrderBy =#{sqlorderby},
        </if>
            <if test="sqllimit != null ">
            SqlLimit =#{sqllimit},
        </if>
            <if test="maintable != null ">
            MainTable =#{maintable},
        </if>
            <if test="enabledmark != null">
            EnabledMark =#{enabledmark},
        </if>
            <if test="publicmark != null">
            PublicMark =#{publicmark},
        </if>
            <if test="databaseid != null ">
            Databaseid =#{databaseid},
        </if>
            <if test="domainnum != null">
            DomainNum =#{domainnum},
        </if>
            <if test="userid != null ">
            Userid =#{userid},
        </if>
            <if test="imageindex != null ">
            ImageIndex =#{imageindex},
        </if>
            <if test="permissioncode != null ">
            PermissionCode =#{permissioncode},
        </if>
            <if test="imagestyle != null ">
            ImageStyle =#{imagestyle},
        </if>
            <if test="datepath != null ">
            DatePath =#{datepath},
        </if>
            <if test="authcode != null ">
            AuthCode =#{authcode},
        </if>
            <if test="caseresult != null ">
            CaseResult =#{caseresult},
        </if>
            <if test="reporttype != null ">
            ReportType =#{reporttype},
        </if>
            <if test="charttype != null ">
            ChartType =#{charttype},
        </if>
            <if test="rownum != null">
            RowNum =#{rownum},
        </if>
            <if test="summary != null ">
            Summary =#{summary},
        </if>
            <if test="createby != null ">
            CreateBy =#{createby},
        </if>
            <if test="createbyid != null ">
            CreateByid =#{createbyid},
        </if>
            <if test="createdate != null">
            CreateDate =#{createdate},
        </if>
            <if test="lister != null ">
            Lister =#{lister},
        </if>
            <if test="listerid != null ">
            Listerid =#{listerid},
        </if>
            <if test="modifydate != null">
            ModifyDate =#{modifydate},
        </if>
            <if test="custom1 != null ">
            Custom1 =#{custom1},
        </if>
            <if test="custom2 != null ">
            Custom2 =#{custom2},
        </if>
            <if test="custom3 != null ">
            Custom3 =#{custom3},
        </if>
            <if test="custom4 != null ">
            Custom4 =#{custom4},
        </if>
            <if test="custom5 != null ">
            Custom5 =#{custom5},
        </if>
            <if test="tenantname != null ">
            TenantName =#{tenantname},
        </if>
        Revision=Revision+1
    </set>
    where id = #{id}
</update>

        <!--通过主键删除-->
<delete id="delete">
delete from Uts_FreeReports where id = #{key}
</delete>
        <!--查询DelListIds-->
<select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.service.sa.uts.domain.pojo.UtsFreereportsPojo">
select
id
from Uts_FreeReportsItem
where Pid = #{id}
<if test="item !=null and item.size()>0">
    and id not in
    <foreach collection="item" open="(" close=")" separator="," item="item">
        <if test="item.id != null">
            #{item.id}
        </if>
        <if test="item.id == null">
            ''
        </if>
    </foreach>
</if>
</select>

    <select id="getListBySelf" resultType="inks.service.sa.uts.domain.pojo.UtsFreereportsPojo">
        <include refid="selectbillVo"/>
        where (Uts_FreeReports.PublicMark = 1 or Uts_FreeReports.Userid = #{userid}) and Uts_FreeReports.DomainNum=#{domainnum}
    </select>

    <select id="myFreeReports" resultType="inks.service.sa.uts.domain.pojo.UtsFreereportsPojo">
        <include refid="selectbillVo"/>
        where (Uts_FreeReports.PublicMark = 1 or Uts_FreeReports.Userid = #{userid})
    </select>

    <select id="countReportcode" resultType="int">
        select count(1) from Uts_FreeReports where ReportCode = #{reportcode}
        <if test="id != null and id != ''">
            and id != #{id}
        </if>
    </select>

    <select id="getEntityByReportcode" resultType="inks.service.sa.uts.domain.pojo.UtsFreereportsPojo">
        <include refid="selectbillVo"/>
        where Uts_FreeReports.ReportCode = #{reportcode}
    </select>
</mapper>

