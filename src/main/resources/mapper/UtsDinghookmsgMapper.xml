<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.uts.mapper.UtsDinghookmsgMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.uts.domain.pojo.UtsDinghookmsgPojo">
        <include refid="selectUtsDinghookmsgVo"/>
        where Uts_DingHookMsg.id = #{key} 
    </select>
    <sql id="selectUtsDinghookmsgVo">
         select
id, MsgGroupid, MsgCode, MsgName, MsgType, MsgTemplate, ModuleCode, WebhookList, UserList, DeptList, Obj<PERSON><PERSON>, RowNum, UrlTemplate, EnabledMark, Remark, CreateBy, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ate, <PERSON>er, <PERSON><PERSON>d, Modify<PERSON>ate, Custom1, Custom2, Custom3, Custom4, Custom5, <PERSON>antid, TenantName, Revision        from Uts_DingHookMsg
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.uts.domain.pojo.UtsDinghookmsgPojo">
        <include refid="selectUtsDinghookmsgVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Uts_DingHookMsg.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.msggroupid != null ">
   and Uts_DingHookMsg.MsgGroupid like concat('%', #{SearchPojo.msggroupid}, '%')
</if>
<if test="SearchPojo.msgcode != null ">
   and Uts_DingHookMsg.MsgCode like concat('%', #{SearchPojo.msgcode}, '%')
</if>
<if test="SearchPojo.msgname != null ">
   and Uts_DingHookMsg.MsgName like concat('%', #{SearchPojo.msgname}, '%')
</if>
<if test="SearchPojo.msgtype != null ">
   and Uts_DingHookMsg.MsgType like concat('%', #{SearchPojo.msgtype}, '%')
</if>
<if test="SearchPojo.msgtemplate != null ">
   and Uts_DingHookMsg.MsgTemplate like concat('%', #{SearchPojo.msgtemplate}, '%')
</if>
<if test="SearchPojo.modulecode != null ">
   and Uts_DingHookMsg.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
</if>
<if test="SearchPojo.webhooklist != null ">
   and Uts_DingHookMsg.WebhookList like concat('%', #{SearchPojo.webhooklist}, '%')
</if>
<if test="SearchPojo.userlist != null ">
   and Uts_DingHookMsg.UserList like concat('%', #{SearchPojo.userlist}, '%')
</if>
<if test="SearchPojo.deptlist != null ">
   and Uts_DingHookMsg.DeptList like concat('%', #{SearchPojo.deptlist}, '%')
</if>
<if test="SearchPojo.objjson != null ">
   and Uts_DingHookMsg.ObjJson like concat('%', #{SearchPojo.objjson}, '%')
</if>
<if test="SearchPojo.urltemplate != null ">
   and Uts_DingHookMsg.UrlTemplate like concat('%', #{SearchPojo.urltemplate}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Uts_DingHookMsg.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Uts_DingHookMsg.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Uts_DingHookMsg.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Uts_DingHookMsg.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Uts_DingHookMsg.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Uts_DingHookMsg.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Uts_DingHookMsg.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Uts_DingHookMsg.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Uts_DingHookMsg.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Uts_DingHookMsg.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Uts_DingHookMsg.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.msggroupid != null ">
   or Uts_DingHookMsg.MsgGroupid like concat('%', #{SearchPojo.msggroupid}, '%')
</if>
<if test="SearchPojo.msgcode != null ">
   or Uts_DingHookMsg.MsgCode like concat('%', #{SearchPojo.msgcode}, '%')
</if>
<if test="SearchPojo.msgname != null ">
   or Uts_DingHookMsg.MsgName like concat('%', #{SearchPojo.msgname}, '%')
</if>
<if test="SearchPojo.msgtype != null ">
   or Uts_DingHookMsg.MsgType like concat('%', #{SearchPojo.msgtype}, '%')
</if>
<if test="SearchPojo.msgtemplate != null ">
   or Uts_DingHookMsg.MsgTemplate like concat('%', #{SearchPojo.msgtemplate}, '%')
</if>
<if test="SearchPojo.modulecode != null ">
   or Uts_DingHookMsg.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
</if>
<if test="SearchPojo.webhooklist != null ">
   or Uts_DingHookMsg.WebhookList like concat('%', #{SearchPojo.webhooklist}, '%')
</if>
<if test="SearchPojo.userlist != null ">
   or Uts_DingHookMsg.UserList like concat('%', #{SearchPojo.userlist}, '%')
</if>
<if test="SearchPojo.deptlist != null ">
   or Uts_DingHookMsg.DeptList like concat('%', #{SearchPojo.deptlist}, '%')
</if>
<if test="SearchPojo.objjson != null ">
   or Uts_DingHookMsg.ObjJson like concat('%', #{SearchPojo.objjson}, '%')
</if>
<if test="SearchPojo.urltemplate != null ">
   or Uts_DingHookMsg.UrlTemplate like concat('%', #{SearchPojo.urltemplate}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Uts_DingHookMsg.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Uts_DingHookMsg.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Uts_DingHookMsg.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Uts_DingHookMsg.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Uts_DingHookMsg.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Uts_DingHookMsg.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Uts_DingHookMsg.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Uts_DingHookMsg.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Uts_DingHookMsg.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Uts_DingHookMsg.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Uts_DingHookMsg.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Uts_DingHookMsg(id, MsgGroupid, MsgCode, MsgName, MsgType, MsgTemplate, ModuleCode, WebhookList, UserList, DeptList, ObjJson, RowNum, UrlTemplate, EnabledMark, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision)
        values (#{id}, #{msggroupid}, #{msgcode}, #{msgname}, #{msgtype}, #{msgtemplate}, #{modulecode}, #{webhooklist}, #{userlist}, #{deptlist}, #{objjson}, #{rownum}, #{urltemplate}, #{enabledmark}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Uts_DingHookMsg
        <set>
            <if test="msggroupid != null ">
                MsgGroupid =#{msggroupid},
            </if>
            <if test="msgcode != null ">
                MsgCode =#{msgcode},
            </if>
            <if test="msgname != null ">
                MsgName =#{msgname},
            </if>
            <if test="msgtype != null ">
                MsgType =#{msgtype},
            </if>
            <if test="msgtemplate != null ">
                MsgTemplate =#{msgtemplate},
            </if>
            <if test="modulecode != null ">
                ModuleCode =#{modulecode},
            </if>
            <if test="webhooklist != null ">
                WebhookList =#{webhooklist},
            </if>
            <if test="userlist != null ">
                UserList =#{userlist},
            </if>
            <if test="deptlist != null ">
                DeptList =#{deptlist},
            </if>
            <if test="objjson != null ">
                ObjJson =#{objjson},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="urltemplate != null ">
                UrlTemplate =#{urltemplate},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Uts_DingHookMsg where id = #{key} 
    </delete>

    <select id="getEntityByMsgCode" resultType="inks.service.sa.uts.domain.pojo.UtsWxehookmsgPojo">
        <include refid="selectUtsDinghookmsgVo"/>
        where Uts_DingHookMsg.MsgCode = #{msgCode}
    </select>
</mapper>

