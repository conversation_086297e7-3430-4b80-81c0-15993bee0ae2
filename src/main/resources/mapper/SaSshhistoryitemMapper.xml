<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.uts.mapper.SaSshhistoryitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.uts.domain.pojo.SaSshhistoryitemPojo">
        <include refid="selectSaSshhistoryitemVo"/>
        where Sa_SshHistoryItem.id = #{key} 
    </select>
    <sql id="selectSaSshhistoryitemVo">
         select
id, Pid, Stepid, StepName, StepRowNum, CommandText, Output, Error, ExitStatus, Status, StartTime, EndTime, DurationMs, RetryCount, RowNum, Remark, CreateBy, CreateByid, <PERSON>reateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision        from Sa_SshHistoryItem
    </sql>
         <!--查询List-->
    <select id="getList" resultType="inks.service.sa.uts.domain.pojo.SaSshhistoryitemPojo">
        <include refid="selectSaSshhistoryitemVo"/>
        where Sa_SshHistoryItem.Pid = #{Pid}
        order by RowNum 
    </select>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.uts.domain.pojo.SaSshhistoryitemPojo">
        <include refid="selectSaSshhistoryitemVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_SshHistoryItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Sa_SshHistoryItem.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.stepid != null and SearchPojo.stepid != ''">
   and Sa_SshHistoryItem.stepid like concat('%', #{SearchPojo.stepid}, '%')
</if>
<if test="SearchPojo.stepname != null and SearchPojo.stepname != ''">
   and Sa_SshHistoryItem.stepname like concat('%', #{SearchPojo.stepname}, '%')
</if>
<if test="SearchPojo.commandtext != null and SearchPojo.commandtext != ''">
   and Sa_SshHistoryItem.commandtext like concat('%', #{SearchPojo.commandtext}, '%')
</if>
<if test="SearchPojo.output != null and SearchPojo.output != ''">
   and Sa_SshHistoryItem.output like concat('%', #{SearchPojo.output}, '%')
</if>
<if test="SearchPojo.error != null and SearchPojo.error != ''">
   and Sa_SshHistoryItem.error like concat('%', #{SearchPojo.error}, '%')
</if>
<if test="SearchPojo.status != null and SearchPojo.status != ''">
   and Sa_SshHistoryItem.status like concat('%', #{SearchPojo.status}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and Sa_SshHistoryItem.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null and SearchPojo.createby != ''">
   and Sa_SshHistoryItem.createby like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
   and Sa_SshHistoryItem.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null and SearchPojo.lister != ''">
   and Sa_SshHistoryItem.lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
   and Sa_SshHistoryItem.listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Sa_SshHistoryItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Sa_SshHistoryItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Sa_SshHistoryItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Sa_SshHistoryItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Sa_SshHistoryItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Sa_SshHistoryItem.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.stepid != null and SearchPojo.stepid != ''">
   or Sa_SshHistoryItem.Stepid like concat('%', #{SearchPojo.stepid}, '%')
</if>
<if test="SearchPojo.stepname != null and SearchPojo.stepname != ''">
   or Sa_SshHistoryItem.StepName like concat('%', #{SearchPojo.stepname}, '%')
</if>
<if test="SearchPojo.commandtext != null and SearchPojo.commandtext != ''">
   or Sa_SshHistoryItem.CommandText like concat('%', #{SearchPojo.commandtext}, '%')
</if>
<if test="SearchPojo.output != null and SearchPojo.output != ''">
   or Sa_SshHistoryItem.Output like concat('%', #{SearchPojo.output}, '%')
</if>
<if test="SearchPojo.error != null and SearchPojo.error != ''">
   or Sa_SshHistoryItem.Error like concat('%', #{SearchPojo.error}, '%')
</if>
<if test="SearchPojo.status != null and SearchPojo.status != ''">
   or Sa_SshHistoryItem.Status like concat('%', #{SearchPojo.status}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Sa_SshHistoryItem.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null and SearchPojo.createby != ''">
   or Sa_SshHistoryItem.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
   or Sa_SshHistoryItem.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null and SearchPojo.lister != ''">
   or Sa_SshHistoryItem.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
   or Sa_SshHistoryItem.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Sa_SshHistoryItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Sa_SshHistoryItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Sa_SshHistoryItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Sa_SshHistoryItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Sa_SshHistoryItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
</trim>
     </sql>

    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_SshHistoryItem(id, Pid, Stepid, StepName, StepRowNum, CommandText, Output, Error, ExitStatus, Status, StartTime, EndTime, DurationMs, RetryCount, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision)
        values (#{id}, #{pid}, #{stepid}, #{stepname}, #{steprownum}, #{commandtext}, #{output}, #{error}, #{exitstatus}, #{status}, #{starttime}, #{endtime}, #{durationms}, #{retrycount}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_SshHistoryItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="stepid != null ">
                Stepid = #{stepid},
            </if>
            <if test="stepname != null ">
                StepName = #{stepname},
            </if>
            <if test="steprownum != null">
                StepRowNum = #{steprownum},
            </if>
            <if test="commandtext != null ">
                CommandText = #{commandtext},
            </if>
            <if test="output != null ">
                Output = #{output},
            </if>
            <if test="error != null ">
                Error = #{error},
            </if>
            <if test="exitstatus != null">
                ExitStatus = #{exitstatus},
            </if>
            <if test="status != null ">
                Status = #{status},
            </if>
            <if test="starttime != null">
                StartTime = #{starttime},
            </if>
            <if test="endtime != null">
                EndTime = #{endtime},
            </if>
            <if test="durationms != null">
                DurationMs = #{durationms},
            </if>
            <if test="retrycount != null">
                RetryCount = #{retrycount},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="createby != null ">
                CreateBy = #{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid = #{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate = #{createdate},
            </if>
            <if test="lister != null ">
                Lister = #{lister},
            </if>
            <if test="listerid != null ">
                Listerid = #{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate = #{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_SshHistoryItem where id = #{key} 
    </delete>

</mapper>

