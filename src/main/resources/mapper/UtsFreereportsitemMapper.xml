<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.uts.mapper.UtsFreereportsitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.uts.domain.pojo.UtsFreereportsitemPojo">
        <include refid="selectUtsFreereportsitemVo"/>
        where Uts_FreeReportsItem.id = #{key}
    </select>
    <sql id="selectUtsFreereportsitemVo">
         select
id, Pid, FieldType, FieldName, HeaderText, DataPropertyName, OrderStr, ColWidth, ColAlign, DisplayNo, DisplayState, FormatString, DefWidth, <PERSON>Width, Fixed, Sortable, OrderField, Overflow, Formatter, ClassName, AlignType, EventName, EditMark, OperationMark, DisplayIndex, RowNum, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision        from Uts_FreeReportsItem
    </sql>

         <!--查询List-->
    <select id="getList" resultType="inks.service.sa.uts.domain.pojo.UtsFreereportsitemPojo">
        <include refid="selectUtsFreereportsitemVo"/>
        where Uts_FreeReportsItem.Pid = #{Pid}
        order by RowNum 
    </select>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.uts.domain.pojo.UtsFreereportsitemPojo">
        <include refid="selectUtsFreereportsitemVo"/>
         where 1 = 1
         <if test="filterstr != null ">
            ${filterstr}
        </if>

         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Uts_FreeReportsItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Uts_FreeReportsItem.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.fieldtype != null and SearchPojo.fieldtype != ''">
   and Uts_FreeReportsItem.fieldtype like concat('%', #{SearchPojo.fieldtype}, '%')
</if>
<if test="SearchPojo.fieldname != null and SearchPojo.fieldname != ''">
   and Uts_FreeReportsItem.fieldname like concat('%', #{SearchPojo.fieldname}, '%')
</if>
<if test="SearchPojo.headertext != null and SearchPojo.headertext != ''">
   and Uts_FreeReportsItem.headertext like concat('%', #{SearchPojo.headertext}, '%')
</if>
<if test="SearchPojo.datapropertyname != null and SearchPojo.datapropertyname != ''">
   and Uts_FreeReportsItem.datapropertyname like concat('%', #{SearchPojo.datapropertyname}, '%')
</if>
<if test="SearchPojo.orderstr != null and SearchPojo.orderstr != ''">
   and Uts_FreeReportsItem.orderstr like concat('%', #{SearchPojo.orderstr}, '%')
</if>
<if test="SearchPojo.colalign != null and SearchPojo.colalign != ''">
   and Uts_FreeReportsItem.colalign like concat('%', #{SearchPojo.colalign}, '%')
</if>
<if test="SearchPojo.formatstring != null and SearchPojo.formatstring != ''">
   and Uts_FreeReportsItem.formatstring like concat('%', #{SearchPojo.formatstring}, '%')
</if>
<if test="SearchPojo.defwidth != null and SearchPojo.defwidth != ''">
   and Uts_FreeReportsItem.defwidth like concat('%', #{SearchPojo.defwidth}, '%')
</if>
<if test="SearchPojo.minwidth != null and SearchPojo.minwidth != ''">
   and Uts_FreeReportsItem.minwidth like concat('%', #{SearchPojo.minwidth}, '%')
</if>
<if test="SearchPojo.orderfield != null and SearchPojo.orderfield != ''">
   and Uts_FreeReportsItem.orderfield like concat('%', #{SearchPojo.orderfield}, '%')
</if>
<if test="SearchPojo.formatter != null and SearchPojo.formatter != ''">
   and Uts_FreeReportsItem.formatter like concat('%', #{SearchPojo.formatter}, '%')
</if>
<if test="SearchPojo.classname != null and SearchPojo.classname != ''">
   and Uts_FreeReportsItem.classname like concat('%', #{SearchPojo.classname}, '%')
</if>
<if test="SearchPojo.aligntype != null and SearchPojo.aligntype != ''">
   and Uts_FreeReportsItem.aligntype like concat('%', #{SearchPojo.aligntype}, '%')
</if>
<if test="SearchPojo.eventname != null and SearchPojo.eventname != ''">
   and Uts_FreeReportsItem.eventname like concat('%', #{SearchPojo.eventname}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Uts_FreeReportsItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Uts_FreeReportsItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Uts_FreeReportsItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Uts_FreeReportsItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Uts_FreeReportsItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
   and Uts_FreeReportsItem.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Uts_FreeReportsItem.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.fieldtype != null and SearchPojo.fieldtype != ''">
   or Uts_FreeReportsItem.FieldType like concat('%', #{SearchPojo.fieldtype}, '%')
</if>
<if test="SearchPojo.fieldname != null and SearchPojo.fieldname != ''">
   or Uts_FreeReportsItem.FieldName like concat('%', #{SearchPojo.fieldname}, '%')
</if>
<if test="SearchPojo.headertext != null and SearchPojo.headertext != ''">
   or Uts_FreeReportsItem.HeaderText like concat('%', #{SearchPojo.headertext}, '%')
</if>
<if test="SearchPojo.datapropertyname != null and SearchPojo.datapropertyname != ''">
   or Uts_FreeReportsItem.DataPropertyName like concat('%', #{SearchPojo.datapropertyname}, '%')
</if>
<if test="SearchPojo.orderstr != null and SearchPojo.orderstr != ''">
   or Uts_FreeReportsItem.OrderStr like concat('%', #{SearchPojo.orderstr}, '%')
</if>
<if test="SearchPojo.colalign != null and SearchPojo.colalign != ''">
   or Uts_FreeReportsItem.ColAlign like concat('%', #{SearchPojo.colalign}, '%')
</if>
<if test="SearchPojo.formatstring != null and SearchPojo.formatstring != ''">
   or Uts_FreeReportsItem.FormatString like concat('%', #{SearchPojo.formatstring}, '%')
</if>
<if test="SearchPojo.defwidth != null and SearchPojo.defwidth != ''">
   or Uts_FreeReportsItem.DefWidth like concat('%', #{SearchPojo.defwidth}, '%')
</if>
<if test="SearchPojo.minwidth != null and SearchPojo.minwidth != ''">
   or Uts_FreeReportsItem.MinWidth like concat('%', #{SearchPojo.minwidth}, '%')
</if>
<if test="SearchPojo.orderfield != null and SearchPojo.orderfield != ''">
   or Uts_FreeReportsItem.OrderField like concat('%', #{SearchPojo.orderfield}, '%')
</if>
<if test="SearchPojo.formatter != null and SearchPojo.formatter != ''">
   or Uts_FreeReportsItem.Formatter like concat('%', #{SearchPojo.formatter}, '%')
</if>
<if test="SearchPojo.classname != null and SearchPojo.classname != ''">
   or Uts_FreeReportsItem.ClassName like concat('%', #{SearchPojo.classname}, '%')
</if>
<if test="SearchPojo.aligntype != null and SearchPojo.aligntype != ''">
   or Uts_FreeReportsItem.AlignType like concat('%', #{SearchPojo.aligntype}, '%')
</if>
<if test="SearchPojo.eventname != null and SearchPojo.eventname != ''">
   or Uts_FreeReportsItem.EventName like concat('%', #{SearchPojo.eventname}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Uts_FreeReportsItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Uts_FreeReportsItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Uts_FreeReportsItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Uts_FreeReportsItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Uts_FreeReportsItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
   or Uts_FreeReportsItem.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
     
    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Uts_FreeReportsItem(id, Pid, FieldType, FieldName, HeaderText, DataPropertyName, OrderStr, ColWidth, ColAlign, DisplayNo, DisplayState, FormatString, DefWidth, MinWidth, Fixed, Sortable, OrderField, Overflow, Formatter, ClassName, AlignType, EventName, EditMark, OperationMark, DisplayIndex, RowNum, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision)
        values (#{id}, #{pid}, #{fieldtype}, #{fieldname}, #{headertext}, #{datapropertyname}, #{orderstr}, #{colwidth}, #{colalign}, #{displayno}, #{displaystate}, #{formatstring}, #{defwidth}, #{minwidth}, #{fixed}, #{sortable}, #{orderfield}, #{overflow}, #{formatter}, #{classname}, #{aligntype}, #{eventname}, #{editmark}, #{operationmark}, #{displayindex}, #{rownum}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Uts_FreeReportsItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="fieldtype != null ">
                FieldType = #{fieldtype},
            </if>
            <if test="fieldname != null ">
                FieldName = #{fieldname},
            </if>
            <if test="headertext != null ">
                HeaderText = #{headertext},
            </if>
            <if test="datapropertyname != null ">
                DataPropertyName = #{datapropertyname},
            </if>
            <if test="orderstr != null ">
                OrderStr = #{orderstr},
            </if>
            <if test="colwidth != null">
                ColWidth = #{colwidth},
            </if>
            <if test="colalign != null ">
                ColAlign = #{colalign},
            </if>
            <if test="displayno != null">
                DisplayNo = #{displayno},
            </if>
            <if test="displaystate != null">
                DisplayState = #{displaystate},
            </if>
            <if test="formatstring != null ">
                FormatString = #{formatstring},
            </if>
            <if test="defwidth != null ">
                DefWidth = #{defwidth},
            </if>
            <if test="minwidth != null ">
                MinWidth = #{minwidth},
            </if>
            <if test="fixed != null">
                Fixed = #{fixed},
            </if>
            <if test="sortable != null">
                Sortable = #{sortable},
            </if>
            <if test="orderfield != null ">
                OrderField = #{orderfield},
            </if>
            <if test="overflow != null">
                Overflow = #{overflow},
            </if>
            <if test="formatter != null ">
                Formatter = #{formatter},
            </if>
            <if test="classname != null ">
                ClassName = #{classname},
            </if>
            <if test="aligntype != null ">
                AlignType = #{aligntype},
            </if>
            <if test="eventname != null ">
                EventName = #{eventname},
            </if>
            <if test="editmark != null">
                EditMark = #{editmark},
            </if>
            <if test="operationmark != null">
                OperationMark = #{operationmark},
            </if>
            <if test="displayindex != null">
                DisplayIndex = #{displayindex},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="tenantname != null ">
                TenantName = #{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Uts_FreeReportsItem where id = #{key}
    </delete>

    <select id="getListByReportCode" resultType="inks.service.sa.uts.domain.pojo.UtsFreereportsitemPojo">
        <include refid="selectUtsFreereportsitemVo"/>
        where Pid =(select id from Uts_FreeReports where ReportCode = #{reportcode})
    </select>
</mapper>

