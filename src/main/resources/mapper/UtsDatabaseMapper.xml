<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.uts.mapper.UtsDatabaseMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.uts.domain.pojo.UtsDatabasePojo">
        <include refid="selectUtsDatabaseVo"/>
        where Uts_Database.id = #{key}
    </select>
    <sql id="selectUtsDatabaseVo">
         select
id, Title, Url, UserName, Password, DriverClassName, EnabledMark, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Tenantid, TenantName, Revision        from Uts_Database
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.uts.domain.pojo.UtsDatabasePojo">
        <include refid="selectUtsDatabaseVo"/>
         where 1 = 1
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Uts_Database.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.title != null ">
   and Uts_Database.Title like concat('%', #{SearchPojo.title}, '%')
</if>
<if test="SearchPojo.url != null ">
   and Uts_Database.Url like concat('%', #{SearchPojo.url}, '%')
</if>
<if test="SearchPojo.username != null ">
   and Uts_Database.UserName like concat('%', #{SearchPojo.username}, '%')
</if>
<if test="SearchPojo.password != null ">
   and Uts_Database.Password like concat('%', #{SearchPojo.password}, '%')
</if>
<if test="SearchPojo.driverclassname != null ">
   and Uts_Database.DriverClassName like concat('%', #{SearchPojo.driverclassname}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Uts_Database.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Uts_Database.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Uts_Database.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Uts_Database.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Uts_Database.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Uts_Database.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.title != null ">
   or Uts_Database.Title like concat('%', #{SearchPojo.title}, '%')
</if>
<if test="SearchPojo.url != null ">
   or Uts_Database.Url like concat('%', #{SearchPojo.url}, '%')
</if>
<if test="SearchPojo.username != null ">
   or Uts_Database.UserName like concat('%', #{SearchPojo.username}, '%')
</if>
<if test="SearchPojo.password != null ">
   or Uts_Database.Password like concat('%', #{SearchPojo.password}, '%')
</if>
<if test="SearchPojo.driverclassname != null ">
   or Uts_Database.DriverClassName like concat('%', #{SearchPojo.driverclassname}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Uts_Database.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Uts_Database.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Uts_Database.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Uts_Database.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Uts_Database.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Uts_Database.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Uts_Database(id, Title, Url, UserName, Password, DriverClassName, EnabledMark, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Tenantid, TenantName, Revision)
        values (#{id}, #{title}, #{url}, #{username}, #{password}, #{driverclassname}, #{enabledmark}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Uts_Database
        <set>
            <if test="title != null ">
                Title =#{title},
            </if>
            <if test="url != null ">
                Url =#{url},
            </if>
            <if test="username != null ">
                UserName =#{username},
            </if>
            <if test="password != null ">
                Password =#{password},
            </if>
            <if test="driverclassname != null ">
                DriverClassName =#{driverclassname},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Uts_Database where id = #{key}
    </delete>


    <select id="getUrlDatabaseType" resultType="java.lang.String">
        select case
                   when url like '%mysql%' then 'mysql'
                   when url like '%sqlserver%' then 'sqlserver'
                   end as databaseType
        from Uts_Database
        where id = #{databaseid}
    </select>
</mapper>

