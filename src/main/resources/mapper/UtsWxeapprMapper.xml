<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.uts.mapper.UtsWxeapprMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.uts.domain.pojo.UtsWxeapprPojo">
        <include refid="selectUtsWxeapprVo"/>
        where Uts_WxeAppr.id = #{key} 
    </select>
    <sql id="selectUtsWxeapprVo">
         select
id, ModuleCode, Templateid, ApprName, DataTemp, CallbackUrl, CallbackBean, ResultCode, ApprType, RowNum, TestData, EnabledMark, Remark, CreateBy, CreateByid, <PERSON>reate<PERSON>ate, <PERSON>er, <PERSON>erid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, <PERSON>anti<PERSON>, TenantName, Revision        from Uts_WxeAppr
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.uts.domain.pojo.UtsWxeapprPojo">
        <include refid="selectUtsWxeapprVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Uts_WxeAppr.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.modulecode != null ">
   and Uts_WxeAppr.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
</if>
<if test="SearchPojo.templateid != null ">
   and Uts_WxeAppr.Templateid like concat('%', #{SearchPojo.templateid}, '%')
</if>
<if test="SearchPojo.apprname != null ">
   and Uts_WxeAppr.ApprName like concat('%', #{SearchPojo.apprname}, '%')
</if>
<if test="SearchPojo.datatemp != null ">
   and Uts_WxeAppr.DataTemp like concat('%', #{SearchPojo.datatemp}, '%')
</if>
<if test="SearchPojo.callbackurl != null ">
   and Uts_WxeAppr.CallbackUrl like concat('%', #{SearchPojo.callbackurl}, '%')
</if>
<if test="SearchPojo.callbackbean != null ">
   and Uts_WxeAppr.CallbackBean like concat('%', #{SearchPojo.callbackbean}, '%')
</if>
<if test="SearchPojo.resultcode != null ">
   and Uts_WxeAppr.ResultCode like concat('%', #{SearchPojo.resultcode}, '%')
</if>
<if test="SearchPojo.apprtype != null ">
   and Uts_WxeAppr.ApprType like concat('%', #{SearchPojo.apprtype}, '%')
</if>
<if test="SearchPojo.testdata != null ">
   and Uts_WxeAppr.TestData like concat('%', #{SearchPojo.testdata}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Uts_WxeAppr.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Uts_WxeAppr.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Uts_WxeAppr.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Uts_WxeAppr.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Uts_WxeAppr.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Uts_WxeAppr.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Uts_WxeAppr.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Uts_WxeAppr.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Uts_WxeAppr.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Uts_WxeAppr.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Uts_WxeAppr.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.modulecode != null ">
   or Uts_WxeAppr.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
</if>
<if test="SearchPojo.templateid != null ">
   or Uts_WxeAppr.Templateid like concat('%', #{SearchPojo.templateid}, '%')
</if>
<if test="SearchPojo.apprname != null ">
   or Uts_WxeAppr.ApprName like concat('%', #{SearchPojo.apprname}, '%')
</if>
<if test="SearchPojo.datatemp != null ">
   or Uts_WxeAppr.DataTemp like concat('%', #{SearchPojo.datatemp}, '%')
</if>
<if test="SearchPojo.callbackurl != null ">
   or Uts_WxeAppr.CallbackUrl like concat('%', #{SearchPojo.callbackurl}, '%')
</if>
<if test="SearchPojo.callbackbean != null ">
   or Uts_WxeAppr.CallbackBean like concat('%', #{SearchPojo.callbackbean}, '%')
</if>
<if test="SearchPojo.resultcode != null ">
   or Uts_WxeAppr.ResultCode like concat('%', #{SearchPojo.resultcode}, '%')
</if>
<if test="SearchPojo.apprtype != null ">
   or Uts_WxeAppr.ApprType like concat('%', #{SearchPojo.apprtype}, '%')
</if>
<if test="SearchPojo.testdata != null ">
   or Uts_WxeAppr.TestData like concat('%', #{SearchPojo.testdata}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Uts_WxeAppr.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Uts_WxeAppr.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Uts_WxeAppr.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Uts_WxeAppr.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Uts_WxeAppr.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Uts_WxeAppr.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Uts_WxeAppr.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Uts_WxeAppr.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Uts_WxeAppr.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Uts_WxeAppr.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Uts_WxeAppr.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Uts_WxeAppr(id, ModuleCode, Templateid, ApprName, DataTemp, CallbackUrl, CallbackBean, ResultCode, ApprType, RowNum, TestData, EnabledMark, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision)
        values (#{id}, #{modulecode}, #{templateid}, #{apprname}, #{datatemp}, #{callbackurl}, #{callbackbean}, #{resultcode}, #{apprtype}, #{rownum}, #{testdata}, #{enabledmark}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Uts_WxeAppr
        <set>
            <if test="modulecode != null ">
                ModuleCode =#{modulecode},
            </if>
            <if test="templateid != null ">
                Templateid =#{templateid},
            </if>
            <if test="apprname != null ">
                ApprName =#{apprname},
            </if>
            <if test="datatemp != null ">
                DataTemp =#{datatemp},
            </if>
            <if test="callbackurl != null ">
                CallbackUrl =#{callbackurl},
            </if>
            <if test="callbackbean != null ">
                CallbackBean =#{callbackbean},
            </if>
            <if test="resultcode != null ">
                ResultCode =#{resultcode},
            </if>
            <if test="apprtype != null ">
                ApprType =#{apprtype},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="testdata != null ">
                TestData =#{testdata},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Uts_WxeAppr where id = #{key} 
    </delete>

    <select id="getListByModuleCode" resultType="inks.service.sa.uts.domain.pojo.UtsWxeapprPojo">
        select id,
        ModuleCode,
        Templateid,
        ApprName,
        DataTemp,
        CallbackUrl,
        CallbackBean,
        ResultCode,
        TestData,
        ApprType,
        RowNum,
        EnabledMark,
        Remark,
        CreateBy,
        CreateByid,
        CreateDate,
        Lister,
        Listerid,
        ModifyDate,
        Custom1,
        Custom2,
        Custom3,
        Custom4,
        Custom5,
        Tenantid,
        Revision
        from Uts_WxeAppr
        where ModuleCode = #{moduleCode}
    </select>
</mapper>

