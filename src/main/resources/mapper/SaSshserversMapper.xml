<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.uts.mapper.SaSshserversMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.uts.domain.pojo.SaSshserversPojo">
        <include refid="selectSaSshserversVo"/>
        where Sa_SshServers.id = #{key} 
    </select>
    <sql id="selectSaSshserversVo">
         select
id, ServerName, Host, Port, Username, Password, GroupName, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision        from Sa_SshServers
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.uts.domain.pojo.SaSshserversPojo">
        <include refid="selectSaSshserversVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_SshServers.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.servername != null ">
   and Sa_SshServers.ServerName like concat('%', #{SearchPojo.servername}, '%')
</if>
<if test="SearchPojo.host != null ">
   and Sa_SshServers.Host like concat('%', #{SearchPojo.host}, '%')
</if>
<if test="SearchPojo.username != null ">
   and Sa_SshServers.Username like concat('%', #{SearchPojo.username}, '%')
</if>
<if test="SearchPojo.password != null ">
   and Sa_SshServers.Password like concat('%', #{SearchPojo.password}, '%')
</if>
<if test="SearchPojo.groupname != null ">
   and Sa_SshServers.GroupName like concat('%', #{SearchPojo.groupname}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_SshServers.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_SshServers.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_SshServers.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_SshServers.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_SshServers.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_SshServers.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_SshServers.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_SshServers.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_SshServers.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_SshServers.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.servername != null ">
   or Sa_SshServers.ServerName like concat('%', #{SearchPojo.servername}, '%')
</if>
<if test="SearchPojo.host != null ">
   or Sa_SshServers.Host like concat('%', #{SearchPojo.host}, '%')
</if>
<if test="SearchPojo.username != null ">
   or Sa_SshServers.Username like concat('%', #{SearchPojo.username}, '%')
</if>
<if test="SearchPojo.password != null ">
   or Sa_SshServers.Password like concat('%', #{SearchPojo.password}, '%')
</if>
<if test="SearchPojo.groupname != null ">
   or Sa_SshServers.GroupName like concat('%', #{SearchPojo.groupname}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_SshServers.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_SshServers.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_SshServers.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_SshServers.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_SshServers.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_SshServers.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_SshServers.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_SshServers.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_SshServers.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_SshServers.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_SshServers(id, ServerName, Host, Port, Username, Password, GroupName, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision)
        values (#{id}, #{servername}, #{host}, #{port}, #{username}, #{password}, #{groupname}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_SshServers
        <set>
            <if test="servername != null ">
                ServerName =#{servername},
            </if>
            <if test="host != null ">
                Host =#{host},
            </if>
            <if test="port != null">
                Port =#{port},
            </if>
            <if test="username != null ">
                Username =#{username},
            </if>
            <if test="password != null ">
                Password =#{password},
            </if>
            <if test="groupname != null ">
                GroupName =#{groupname},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_SshServers where id = #{key} 
    </delete>
</mapper>

