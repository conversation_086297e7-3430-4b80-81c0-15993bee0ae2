<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.uts.mapper.UtsMqttmsgMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.uts.domain.pojo.UtsMqttmsgPojo">
        select id,
        MsgGroupid,
        MsgCode,
        MsgName,
        MsgType,
        MsgTemplate,
        MsgIcon,
        ModuleCode,
        Duration,
        UseridList,
        ItemJson,
        DeptidList,
        ToAllUser,
        RowNum,
        EnabledMark,
        Position,
        Remark,
        CreateBy,
        CreateByid,
        CreateDate,
        Lister,
        Listerid,
        ModifyDate,
        Custom1,
        Custom2,
        Custom3,
        Custom4,
        Custom5,
        Tenantid,
        TenantName,
        Revision
        from Uts_MqttMsg
        where Uts_MqttMsg.id = #{key}
    </select>
    <sql id="selectUtsMqttmsgVo">
        select id,
               MsgGroupid,
               MsgCode,
               MsgName,
               MsgType,
               MsgTemplate,
               MsgIcon,
               ModuleCode,
               Duration,
               UseridList,
               ItemJson,
               DeptidList,
               ToAllUser,
               RowNum,
               EnabledMark,
               Position,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Tenantid,
               TenantName,
               Revision
        from Uts_MqttMsg
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.uts.domain.pojo.UtsMqttmsgPojo">
        <include refid="selectUtsMqttmsgVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Uts_MqttMsg.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.msggroupid != null">
            and Uts_MqttMsg.MsgGroupid like concat('%', #{SearchPojo.msggroupid}, '%')
        </if>
        <if test="SearchPojo.msgcode != null">
            and Uts_MqttMsg.MsgCode like concat('%',
            #{SearchPojo.msgcode}, '%')
        </if>
        <if test="SearchPojo.msgname != null">
            and Uts_MqttMsg.MsgName like concat('%',
            #{SearchPojo.msgname}, '%')
        </if>
        <if test="SearchPojo.msgtype != null">
            and Uts_MqttMsg.MsgType like concat('%',
            #{SearchPojo.msgtype}, '%')
        </if>
        <if test="SearchPojo.msgtemplate != null">
            and Uts_MqttMsg.MsgTemplate like concat('%',
            #{SearchPojo.msgtemplate}, '%')
        </if>
        <if test="SearchPojo.modulecode != null">
            and Uts_MqttMsg.ModuleCode like concat('%',
            #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.useridlist != null">
            and Uts_MqttMsg.UseridList like concat('%',
            #{SearchPojo.useridlist}, '%')
        </if>
        <if test="SearchPojo.itemjson != null">
            and Uts_MqttMsg.ItemJson like concat('%',
            #{SearchPojo.itemjson}, '%')
        </if>
        <if test="SearchPojo.deptidlist != null">
            and Uts_MqttMsg.DeptidList like concat('%',
            #{SearchPojo.deptidlist}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Uts_MqttMsg.Remark like concat('%',
            #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Uts_MqttMsg.CreateBy like concat('%',
            #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Uts_MqttMsg.CreateByid like concat('%',
            #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Uts_MqttMsg.Lister like concat('%',
            #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Uts_MqttMsg.Listerid like concat('%',
            #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Uts_MqttMsg.Custom1 like concat('%',
            #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Uts_MqttMsg.Custom2 like concat('%',
            #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Uts_MqttMsg.Custom3 like concat('%',
            #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Uts_MqttMsg.Custom4 like concat('%',
            #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Uts_MqttMsg.Custom5 like concat('%',
            #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Uts_MqttMsg.TenantName like concat('%',
            #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.msggroupid != null">
                or Uts_MqttMsg.MsgGroupid like concat('%', #{SearchPojo.msggroupid}, '%')
            </if>
            <if test="SearchPojo.msgcode != null">
                or Uts_MqttMsg.MsgCode like concat('%', #{SearchPojo.msgcode}, '%')
            </if>
            <if test="SearchPojo.msgname != null">
                or Uts_MqttMsg.MsgName like concat('%', #{SearchPojo.msgname}, '%')
            </if>
            <if test="SearchPojo.msgtype != null">
                or Uts_MqttMsg.MsgType like concat('%', #{SearchPojo.msgtype}, '%')
            </if>
            <if test="SearchPojo.msgtemplate != null">
                or Uts_MqttMsg.MsgTemplate like concat('%', #{SearchPojo.msgtemplate}, '%')
            </if>
            <if test="SearchPojo.modulecode != null">
                or Uts_MqttMsg.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
            </if>
            <if test="SearchPojo.useridlist != null">
                or Uts_MqttMsg.UseridList like concat('%', #{SearchPojo.useridlist}, '%')
            </if>
            <if test="SearchPojo.itemjson != null">
                or Uts_MqttMsg.ItemJson like concat('%', #{SearchPojo.itemjson}, '%')
            </if>
            <if test="SearchPojo.deptidlist != null">
                or Uts_MqttMsg.DeptidList like concat('%', #{SearchPojo.deptidlist}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Uts_MqttMsg.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Uts_MqttMsg.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Uts_MqttMsg.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Uts_MqttMsg.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Uts_MqttMsg.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Uts_MqttMsg.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Uts_MqttMsg.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Uts_MqttMsg.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Uts_MqttMsg.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Uts_MqttMsg.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Uts_MqttMsg.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Uts_MqttMsg(id, MsgGroupid, MsgCode, MsgName, MsgType, MsgTemplate, MsgIcon, ModuleCode, Duration,
        UseridList, ItemJson, DeptidList, ToAllUser, RowNum, EnabledMark,Position, Remark, CreateBy,
        CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3,
        Custom4, Custom5, Tenantid, TenantName, Revision)
        values (#{id}, #{msggroupid}, #{msgcode}, #{msgname}, #{msgtype}, #{msgtemplate}, #{msgicon}, #{modulecode},
        #{duration}, #{useridlist}, #{itemjson}, #{deptidlist}, #{toalluser}, #{rownum}, #{enabledmark},#{position},
        #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1},
        #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Uts_MqttMsg
        <set>
            <if test="msggroupid != null">
                MsgGroupid =#{msggroupid},
            </if>
            <if test="msgcode != null">
                MsgCode =#{msgcode},
            </if>
            <if test="msgname != null">
                MsgName =#{msgname},
            </if>
            <if test="msgtype != null">
                MsgType =#{msgtype},
            </if>
            <if test="msgtemplate != null">
                MsgTemplate =#{msgtemplate},
            </if>
            <if test="msgicon != null">
                MsgIcon =#{msgicon},
            </if>
            <if test="modulecode != null">
                ModuleCode =#{modulecode},
            </if>
            <if test="duration != null">
                Duration =#{duration},
            </if>
            <if test="useridlist != null">
                UseridList =#{useridlist},
            </if>
            <if test="itemjson != null">
                ItemJson =#{itemjson},
            </if>
            <if test="deptidlist != null">
                DeptidList =#{deptidlist},
            </if>
            <if test="toalluser != null">
                ToAllUser =#{toalluser},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="position != null">
                Position =#{position},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Uts_MqttMsg
        where id = #{key}
    </delete>
    <select id="getListByModuleCode" resultType="inks.service.sa.uts.domain.pojo.UtsMqttmsgPojo">
        select id,
        MsgGroupid,
        MsgCode,
        MsgName,
        MsgType,
        MsgTemplate,
        ModuleCode,
        Duration,
        UseridList,
        ItemJson,
        DeptidList,
        ToAllUser,
        RowNum,
        EnabledMark,
        Position,
        Remark,
        CreateBy,
        CreateByid,
        CreateDate,
        Lister,
        Listerid,
        ModifyDate,
        Custom1,
        Custom2,
        Custom3,
        Custom4,
        Custom5,
        Tenantid,
        TenantName,
        Revision
        from Uts_MqttMsg
        where ModuleCode = #{moduleCode}
    </select>
    <select id="getEntityByMsgCode" resultType="inks.service.sa.uts.domain.pojo.UtsMqttmsgPojo">
        select *
        from Uts_MqttMsg
        where MsgCode = #{msgCode}
        limit 1
    </select>
</mapper>

