<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.uts.mapper.UtsBackupconfigMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.uts.domain.pojo.UtsBackupconfigPojo">
        <include refid="selectUtsBackupconfigVo"/>
        where Uts_BackupConfig.id = #{key} 
    </select>
    <sql id="selectUtsBackupconfigVo">
         select
id, ConfigName, LocalMark, DbDriver, DbUrl, DbUsername, DbPassword, LocalPath, IncludeTables, ExcludeTables, UploadPrefix, ZipPassword, CronExpression, CloudUrl, AuthCode, Email, EnabledMark, OssType, OssBucket, OssAccessKey, OssSecretKey, OssEndpoint, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision        from Uts_BackupConfig
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.uts.domain.pojo.UtsBackupconfigPojo">
        <include refid="selectUtsBackupconfigVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Uts_BackupConfig.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.configname != null ">
   and Uts_BackupConfig.ConfigName like concat('%', #{SearchPojo.configname}, '%')
</if>
<if test="SearchPojo.dbdriver != null ">
   and Uts_BackupConfig.DbDriver like concat('%', #{SearchPojo.dbdriver}, '%')
</if>
<if test="SearchPojo.dburl != null ">
   and Uts_BackupConfig.DbUrl like concat('%', #{SearchPojo.dburl}, '%')
</if>
<if test="SearchPojo.dbusername != null ">
   and Uts_BackupConfig.DbUsername like concat('%', #{SearchPojo.dbusername}, '%')
</if>
<if test="SearchPojo.dbpassword != null ">
   and Uts_BackupConfig.DbPassword like concat('%', #{SearchPojo.dbpassword}, '%')
</if>
<if test="SearchPojo.localpath != null ">
   and Uts_BackupConfig.LocalPath like concat('%', #{SearchPojo.localpath}, '%')
</if>
<if test="SearchPojo.includetables != null ">
   and Uts_BackupConfig.IncludeTables like concat('%', #{SearchPojo.includetables}, '%')
</if>
<if test="SearchPojo.excludetables != null ">
   and Uts_BackupConfig.ExcludeTables like concat('%', #{SearchPojo.excludetables}, '%')
</if>
<if test="SearchPojo.uploadprefix != null ">
   and Uts_BackupConfig.UploadPrefix like concat('%', #{SearchPojo.uploadprefix}, '%')
</if>
<if test="SearchPojo.zippassword != null ">
   and Uts_BackupConfig.ZipPassword like concat('%', #{SearchPojo.zippassword}, '%')
</if>
<if test="SearchPojo.cronexpression != null ">
   and Uts_BackupConfig.CronExpression like concat('%', #{SearchPojo.cronexpression}, '%')
</if>
<if test="SearchPojo.cloudurl != null ">
   and Uts_BackupConfig.CloudUrl like concat('%', #{SearchPojo.cloudurl}, '%')
</if>
<if test="SearchPojo.authcode != null ">
   and Uts_BackupConfig.AuthCode like concat('%', #{SearchPojo.authcode}, '%')
</if>
<if test="SearchPojo.email != null ">
   and Uts_BackupConfig.Email like concat('%', #{SearchPojo.email}, '%')
</if>
<if test="SearchPojo.osstype != null ">
   and Uts_BackupConfig.OssType like concat('%', #{SearchPojo.osstype}, '%')
</if>
<if test="SearchPojo.ossbucket != null ">
   and Uts_BackupConfig.OssBucket like concat('%', #{SearchPojo.ossbucket}, '%')
</if>
<if test="SearchPojo.ossaccesskey != null ">
   and Uts_BackupConfig.OssAccessKey like concat('%', #{SearchPojo.ossaccesskey}, '%')
</if>
<if test="SearchPojo.osssecretkey != null ">
   and Uts_BackupConfig.OssSecretKey like concat('%', #{SearchPojo.osssecretkey}, '%')
</if>
<if test="SearchPojo.ossendpoint != null ">
   and Uts_BackupConfig.OssEndpoint like concat('%', #{SearchPojo.ossendpoint}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Uts_BackupConfig.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Uts_BackupConfig.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Uts_BackupConfig.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Uts_BackupConfig.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Uts_BackupConfig.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Uts_BackupConfig.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Uts_BackupConfig.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Uts_BackupConfig.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Uts_BackupConfig.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Uts_BackupConfig.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Uts_BackupConfig.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Uts_BackupConfig.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Uts_BackupConfig.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Uts_BackupConfig.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Uts_BackupConfig.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Uts_BackupConfig.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.configname != null ">
   or Uts_BackupConfig.ConfigName like concat('%', #{SearchPojo.configname}, '%')
</if>
<if test="SearchPojo.dbdriver != null ">
   or Uts_BackupConfig.DbDriver like concat('%', #{SearchPojo.dbdriver}, '%')
</if>
<if test="SearchPojo.dburl != null ">
   or Uts_BackupConfig.DbUrl like concat('%', #{SearchPojo.dburl}, '%')
</if>
<if test="SearchPojo.dbusername != null ">
   or Uts_BackupConfig.DbUsername like concat('%', #{SearchPojo.dbusername}, '%')
</if>
<if test="SearchPojo.dbpassword != null ">
   or Uts_BackupConfig.DbPassword like concat('%', #{SearchPojo.dbpassword}, '%')
</if>
<if test="SearchPojo.localpath != null ">
   or Uts_BackupConfig.LocalPath like concat('%', #{SearchPojo.localpath}, '%')
</if>
<if test="SearchPojo.includetables != null ">
   or Uts_BackupConfig.IncludeTables like concat('%', #{SearchPojo.includetables}, '%')
</if>
<if test="SearchPojo.excludetables != null ">
   or Uts_BackupConfig.ExcludeTables like concat('%', #{SearchPojo.excludetables}, '%')
</if>
<if test="SearchPojo.uploadprefix != null ">
   or Uts_BackupConfig.UploadPrefix like concat('%', #{SearchPojo.uploadprefix}, '%')
</if>
<if test="SearchPojo.zippassword != null ">
   or Uts_BackupConfig.ZipPassword like concat('%', #{SearchPojo.zippassword}, '%')
</if>
<if test="SearchPojo.cronexpression != null ">
   or Uts_BackupConfig.CronExpression like concat('%', #{SearchPojo.cronexpression}, '%')
</if>
<if test="SearchPojo.cloudurl != null ">
   or Uts_BackupConfig.CloudUrl like concat('%', #{SearchPojo.cloudurl}, '%')
</if>
<if test="SearchPojo.authcode != null ">
   or Uts_BackupConfig.AuthCode like concat('%', #{SearchPojo.authcode}, '%')
</if>
<if test="SearchPojo.email != null ">
   or Uts_BackupConfig.Email like concat('%', #{SearchPojo.email}, '%')
</if>
<if test="SearchPojo.osstype != null ">
   or Uts_BackupConfig.OssType like concat('%', #{SearchPojo.osstype}, '%')
</if>
<if test="SearchPojo.ossbucket != null ">
   or Uts_BackupConfig.OssBucket like concat('%', #{SearchPojo.ossbucket}, '%')
</if>
<if test="SearchPojo.ossaccesskey != null ">
   or Uts_BackupConfig.OssAccessKey like concat('%', #{SearchPojo.ossaccesskey}, '%')
</if>
<if test="SearchPojo.osssecretkey != null ">
   or Uts_BackupConfig.OssSecretKey like concat('%', #{SearchPojo.osssecretkey}, '%')
</if>
<if test="SearchPojo.ossendpoint != null ">
   or Uts_BackupConfig.OssEndpoint like concat('%', #{SearchPojo.ossendpoint}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Uts_BackupConfig.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Uts_BackupConfig.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Uts_BackupConfig.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Uts_BackupConfig.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Uts_BackupConfig.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Uts_BackupConfig.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Uts_BackupConfig.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Uts_BackupConfig.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Uts_BackupConfig.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Uts_BackupConfig.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Uts_BackupConfig.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Uts_BackupConfig.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Uts_BackupConfig.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Uts_BackupConfig.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Uts_BackupConfig.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Uts_BackupConfig.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Uts_BackupConfig(id, ConfigName, LocalMark, DbDriver, DbUrl, DbUsername, DbPassword, LocalPath, IncludeTables, ExcludeTables, UploadPrefix, ZipPassword, CronExpression, CloudUrl, AuthCode, Email, EnabledMark, OssType, OssBucket, OssAccessKey, OssSecretKey, OssEndpoint, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{configname}, #{localmark}, #{dbdriver}, #{dburl}, #{dbusername}, #{dbpassword}, #{localpath}, #{includetables}, #{excludetables}, #{uploadprefix}, #{zippassword}, #{cronexpression}, #{cloudurl}, #{authcode}, #{email}, #{enabledmark}, #{osstype}, #{ossbucket}, #{ossaccesskey}, #{osssecretkey}, #{ossendpoint}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Uts_BackupConfig
        <set>
            <if test="configname != null ">
                ConfigName =#{configname},
            </if>
            <if test="localmark != null">
                LocalMark =#{localmark},
            </if>
            <if test="dbdriver != null ">
                DbDriver =#{dbdriver},
            </if>
            <if test="dburl != null ">
                DbUrl =#{dburl},
            </if>
            <if test="dbusername != null ">
                DbUsername =#{dbusername},
            </if>
            <if test="dbpassword != null ">
                DbPassword =#{dbpassword},
            </if>
            <if test="localpath != null ">
                LocalPath =#{localpath},
            </if>
            <if test="includetables != null ">
                IncludeTables =#{includetables},
            </if>
            <if test="excludetables != null ">
                ExcludeTables =#{excludetables},
            </if>
            <if test="uploadprefix != null ">
                UploadPrefix =#{uploadprefix},
            </if>
            <if test="zippassword != null ">
                ZipPassword =#{zippassword},
            </if>
            <if test="cronexpression != null ">
                CronExpression =#{cronexpression},
            </if>
            <if test="cloudurl != null ">
                CloudUrl =#{cloudurl},
            </if>
            <if test="authcode != null ">
                AuthCode =#{authcode},
            </if>
            <if test="email != null ">
                Email =#{email},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="osstype != null ">
                OssType =#{osstype},
            </if>
            <if test="ossbucket != null ">
                OssBucket =#{ossbucket},
            </if>
            <if test="ossaccesskey != null ">
                OssAccessKey =#{ossaccesskey},
            </if>
            <if test="osssecretkey != null ">
                OssSecretKey =#{osssecretkey},
            </if>
            <if test="ossendpoint != null ">
                OssEndpoint =#{ossendpoint},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Uts_BackupConfig where id = #{key} 
    </delete>

    <select id="getEnableList" resultType="inks.service.sa.uts.domain.pojo.UtsBackupconfigPojo">
        <include refid="selectUtsBackupconfigVo"/>
        where 1 = 1 and Uts_BackupConfig.EnabledMark = 1
    </select>

    <select id="getEnableAndCronList" resultType="inks.service.sa.uts.domain.pojo.UtsBackupconfigPojo">
        <include refid="selectUtsBackupconfigVo"/>
        where 1 = 1 and Uts_BackupConfig.EnabledMark = 1
        and Uts_BackupConfig.CronExpression is not null
        and Uts_BackupConfig.CronExpression != ''
    </select>
</mapper>

