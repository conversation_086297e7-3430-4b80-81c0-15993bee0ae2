<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.uts.mapper.SaSshhistoryMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.uts.domain.pojo.SaSshhistoryPojo">
        <include refid="selectbillVo"/>
        where Sa_SshHistory.id = #{key} 
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
         select
id, Pipelineid, PipelineName, Serverid, ServerName, Sessionid, Status, StartTime, EndTime, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision        from Sa_SshHistory
    </sql>
    <sql id="selectdetailVo">
         select
id, Pipelineid, PipelineName, Serverid, ServerName, Sessionid, Status, StartTime, EndTime, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision        from Sa_SshHistory
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.uts.domain.pojo.SaSshhistoryitemdetailPojo">
        <include refid="selectdetailVo"/>
         where 1 = 1 
       <if test="filterstr != null ">
            ${filterstr}
        </if> 
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_SshHistory.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pipelineid != null ">
   and Sa_SshHistory.pipelineid like concat('%', #{SearchPojo.pipelineid}, '%')
</if>
<if test="SearchPojo.pipelinename != null ">
   and Sa_SshHistory.pipelinename like concat('%', #{SearchPojo.pipelinename}, '%')
</if>
<if test="SearchPojo.serverid != null ">
   and Sa_SshHistory.serverid like concat('%', #{SearchPojo.serverid}, '%')
</if>
<if test="SearchPojo.servername != null ">
   and Sa_SshHistory.servername like concat('%', #{SearchPojo.servername}, '%')
</if>
<if test="SearchPojo.sessionid != null ">
   and Sa_SshHistory.sessionid like concat('%', #{SearchPojo.sessionid}, '%')
</if>
<if test="SearchPojo.status != null ">
   and Sa_SshHistory.status like concat('%', #{SearchPojo.status}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_SshHistory.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_SshHistory.createby like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_SshHistory.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_SshHistory.lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_SshHistory.listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_SshHistory.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_SshHistory.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_SshHistory.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_SshHistory.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_SshHistory.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pipelineid != null ">
   or Sa_SshHistory.Pipelineid like concat('%', #{SearchPojo.pipelineid}, '%')
</if>
<if test="SearchPojo.pipelinename != null ">
   or Sa_SshHistory.PipelineName like concat('%', #{SearchPojo.pipelinename}, '%')
</if>
<if test="SearchPojo.serverid != null ">
   or Sa_SshHistory.Serverid like concat('%', #{SearchPojo.serverid}, '%')
</if>
<if test="SearchPojo.servername != null ">
   or Sa_SshHistory.ServerName like concat('%', #{SearchPojo.servername}, '%')
</if>
<if test="SearchPojo.sessionid != null ">
   or Sa_SshHistory.Sessionid like concat('%', #{SearchPojo.sessionid}, '%')
</if>
<if test="SearchPojo.status != null ">
   or Sa_SshHistory.Status like concat('%', #{SearchPojo.status}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_SshHistory.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_SshHistory.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_SshHistory.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_SshHistory.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_SshHistory.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_SshHistory.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_SshHistory.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_SshHistory.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_SshHistory.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_SshHistory.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
</trim>
     </sql>
     
         <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.uts.domain.pojo.SaSshhistoryPojo">
        <include refid="selectbillVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_SshHistory.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="thand"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="thor"></include>        
         </if>
        </if> 
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="thand">
<if test="SearchPojo.pipelineid != null ">
   and Sa_SshHistory.Pipelineid like concat('%', #{SearchPojo.pipelineid}, '%')
</if>
<if test="SearchPojo.pipelinename != null ">
   and Sa_SshHistory.PipelineName like concat('%', #{SearchPojo.pipelinename}, '%')
</if>
<if test="SearchPojo.serverid != null ">
   and Sa_SshHistory.Serverid like concat('%', #{SearchPojo.serverid}, '%')
</if>
<if test="SearchPojo.servername != null ">
   and Sa_SshHistory.ServerName like concat('%', #{SearchPojo.servername}, '%')
</if>
<if test="SearchPojo.sessionid != null ">
   and Sa_SshHistory.Sessionid like concat('%', #{SearchPojo.sessionid}, '%')
</if>
<if test="SearchPojo.status != null ">
   and Sa_SshHistory.Status like concat('%', #{SearchPojo.status}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_SshHistory.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_SshHistory.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_SshHistory.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_SshHistory.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_SshHistory.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_SshHistory.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_SshHistory.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_SshHistory.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_SshHistory.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_SshHistory.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
     </sql>   
     <sql id="thor">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pipelineid != null ">
   or Sa_SshHistory.Pipelineid like concat('%', #{SearchPojo.pipelineid}, '%')
</if>
<if test="SearchPojo.pipelinename != null ">
   or Sa_SshHistory.PipelineName like concat('%', #{SearchPojo.pipelinename}, '%')
</if>
<if test="SearchPojo.serverid != null ">
   or Sa_SshHistory.Serverid like concat('%', #{SearchPojo.serverid}, '%')
</if>
<if test="SearchPojo.servername != null ">
   or Sa_SshHistory.ServerName like concat('%', #{SearchPojo.servername}, '%')
</if>
<if test="SearchPojo.sessionid != null ">
   or Sa_SshHistory.Sessionid like concat('%', #{SearchPojo.sessionid}, '%')
</if>
<if test="SearchPojo.status != null ">
   or Sa_SshHistory.Status like concat('%', #{SearchPojo.status}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_SshHistory.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_SshHistory.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_SshHistory.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_SshHistory.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_SshHistory.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_SshHistory.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_SshHistory.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_SshHistory.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_SshHistory.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_SshHistory.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
</trim>
     </sql>
     
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_SshHistory(id, Pipelineid, PipelineName, Serverid, ServerName, Sessionid, Status, StartTime, EndTime, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision)
        values (#{id}, #{pipelineid}, #{pipelinename}, #{serverid}, #{servername}, #{sessionid}, #{status}, #{starttime}, #{endtime}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_SshHistory
        <set>
            <if test="pipelineid != null ">
                Pipelineid =#{pipelineid},
            </if>
            <if test="pipelinename != null ">
                PipelineName =#{pipelinename},
            </if>
            <if test="serverid != null ">
                Serverid =#{serverid},
            </if>
            <if test="servername != null ">
                ServerName =#{servername},
            </if>
            <if test="sessionid != null ">
                Sessionid =#{sessionid},
            </if>
            <if test="status != null ">
                Status =#{status},
            </if>
            <if test="starttime != null">
                StartTime =#{starttime},
            </if>
            <if test="endtime != null">
                EndTime =#{endtime},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_SshHistory where id = #{key} 
    </delete>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.service.sa.uts.domain.pojo.SaSshhistoryPojo">
        select
          id
        from Sa_SshHistoryItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
         and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                 ''
            </if>
        </foreach>
         </if>
    </select>

</mapper>

