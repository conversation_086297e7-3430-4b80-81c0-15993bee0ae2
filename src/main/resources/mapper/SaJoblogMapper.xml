<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.uts.mapper.SaJoblogMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.uts.domain.pojo.SaJoblogPojo">
        <include refid="selectSaJoblogVo"/>
        where Sa_JobLog.id = #{key} 
    </select>
    <sql id="selectSaJoblogVo">
         select
id, JobName, JobGroup, InvokeTarget, JobMessage, Status, ExceptionInfo, CreateTime        from Sa_JobLog
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.uts.domain.pojo.SaJoblogPojo">
        <include refid="selectSaJoblogVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_JobLog.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.jobname != null ">
   and Sa_JobLog.JobName like concat('%', #{SearchPojo.jobname}, '%')
</if>
<if test="SearchPojo.jobgroup != null ">
   and Sa_JobLog.JobGroup like concat('%', #{SearchPojo.jobgroup}, '%')
</if>
<if test="SearchPojo.invoketarget != null ">
   and Sa_JobLog.InvokeTarget like concat('%', #{SearchPojo.invoketarget}, '%')
</if>
<if test="SearchPojo.jobmessage != null ">
   and Sa_JobLog.JobMessage like concat('%', #{SearchPojo.jobmessage}, '%')
</if>
<if test="SearchPojo.status != null ">
   and Sa_JobLog.Status like concat('%', #{SearchPojo.status}, '%')
</if>
<if test="SearchPojo.exceptioninfo != null ">
   and Sa_JobLog.ExceptionInfo like concat('%', #{SearchPojo.exceptioninfo}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.jobname != null ">
   or Sa_JobLog.JobName like concat('%', #{SearchPojo.jobname}, '%')
</if>
<if test="SearchPojo.jobgroup != null ">
   or Sa_JobLog.JobGroup like concat('%', #{SearchPojo.jobgroup}, '%')
</if>
<if test="SearchPojo.invoketarget != null ">
   or Sa_JobLog.InvokeTarget like concat('%', #{SearchPojo.invoketarget}, '%')
</if>
<if test="SearchPojo.jobmessage != null ">
   or Sa_JobLog.JobMessage like concat('%', #{SearchPojo.jobmessage}, '%')
</if>
<if test="SearchPojo.status != null ">
   or Sa_JobLog.Status like concat('%', #{SearchPojo.status}, '%')
</if>
<if test="SearchPojo.exceptioninfo != null ">
   or Sa_JobLog.ExceptionInfo like concat('%', #{SearchPojo.exceptioninfo}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_JobLog(id, JobName, JobGroup, InvokeTarget, JobMessage, Status, ExceptionInfo, CreateTime)
        values (#{id}, #{jobname}, #{jobgroup}, #{invoketarget}, #{jobmessage}, #{status}, #{exceptioninfo}, #{createtime})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_JobLog
        <set>
            <if test="jobname != null ">
                JobName =#{jobname},
            </if>
            <if test="jobgroup != null ">
                JobGroup =#{jobgroup},
            </if>
            <if test="invoketarget != null ">
                InvokeTarget =#{invoketarget},
            </if>
            <if test="jobmessage != null ">
                JobMessage =#{jobmessage},
            </if>
            <if test="status != null ">
                Status =#{status},
            </if>
            <if test="exceptioninfo != null ">
                ExceptionInfo =#{exceptioninfo},
            </if>
            <if test="createtime != null">
                CreateTime =#{createtime},
            </if>
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_JobLog where id = #{key} 
    </delete>

    <delete id="deleteJobLogByIds">
        delete from Sa_JobLog where id in
        <foreach collection="jobLogIds" item="jobLogId" open="(" separator="," close=")">
            #{jobLogId}
        </foreach>
    </delete>

    <update id="cleanJobLog">
        truncate table Sa_JobLog
    </update>
</mapper>

