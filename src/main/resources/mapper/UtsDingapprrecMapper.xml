<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.uts.mapper.UtsDingapprrecMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.uts.domain.pojo.UtsDingapprrecPojo">
        <include refid="selectUtsDingapprrecVo"/>
        where Uts_DingApprRec.id = #{key} 
    </select>
    <sql id="selectUtsDingapprrecVo">
         select
id, ModuleCode, Templateid, ApprName, DataTemp, CallbackUrl, CallbackBean, ResultCode, ApprType, ApprSn, Billid, CallbackUuid, CallbackName, CallbackDate, CallbackResult, CallbackMsg, <PERSON>rid, RealName, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>difyDate, Custom1, Custom2, Custom3, Custom4, Custom5, <PERSON><PERSON><PERSON>, TenantName, Revision        from Uts_DingApprRec
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.uts.domain.pojo.UtsDingapprrecPojo">
        <include refid="selectUtsDingapprrecVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Uts_DingApprRec.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.modulecode != null ">
   and Uts_DingApprRec.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
</if>
<if test="SearchPojo.templateid != null ">
   and Uts_DingApprRec.Templateid like concat('%', #{SearchPojo.templateid}, '%')
</if>
<if test="SearchPojo.apprname != null ">
   and Uts_DingApprRec.ApprName like concat('%', #{SearchPojo.apprname}, '%')
</if>
<if test="SearchPojo.datatemp != null ">
   and Uts_DingApprRec.DataTemp like concat('%', #{SearchPojo.datatemp}, '%')
</if>
<if test="SearchPojo.callbackurl != null ">
   and Uts_DingApprRec.CallbackUrl like concat('%', #{SearchPojo.callbackurl}, '%')
</if>
<if test="SearchPojo.callbackbean != null ">
   and Uts_DingApprRec.CallbackBean like concat('%', #{SearchPojo.callbackbean}, '%')
</if>
<if test="SearchPojo.resultcode != null ">
   and Uts_DingApprRec.ResultCode like concat('%', #{SearchPojo.resultcode}, '%')
</if>
<if test="SearchPojo.apprtype != null ">
   and Uts_DingApprRec.ApprType like concat('%', #{SearchPojo.apprtype}, '%')
</if>
<if test="SearchPojo.apprsn != null ">
   and Uts_DingApprRec.ApprSn like concat('%', #{SearchPojo.apprsn}, '%')
</if>
<if test="SearchPojo.billid != null ">
   and Uts_DingApprRec.Billid like concat('%', #{SearchPojo.billid}, '%')
</if>
<if test="SearchPojo.callbackuuid != null ">
   and Uts_DingApprRec.CallbackUuid like concat('%', #{SearchPojo.callbackuuid}, '%')
</if>
<if test="SearchPojo.callbackname != null ">
   and Uts_DingApprRec.CallbackName like concat('%', #{SearchPojo.callbackname}, '%')
</if>
<if test="SearchPojo.callbackresult != null ">
   and Uts_DingApprRec.CallbackResult like concat('%', #{SearchPojo.callbackresult}, '%')
</if>
<if test="SearchPojo.callbackmsg != null ">
   and Uts_DingApprRec.CallbackMsg like concat('%', #{SearchPojo.callbackmsg}, '%')
</if>
<if test="SearchPojo.userid != null ">
   and Uts_DingApprRec.Userid like concat('%', #{SearchPojo.userid}, '%')
</if>
<if test="SearchPojo.realname != null ">
   and Uts_DingApprRec.RealName like concat('%', #{SearchPojo.realname}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Uts_DingApprRec.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Uts_DingApprRec.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Uts_DingApprRec.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Uts_DingApprRec.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Uts_DingApprRec.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Uts_DingApprRec.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Uts_DingApprRec.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Uts_DingApprRec.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Uts_DingApprRec.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Uts_DingApprRec.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Uts_DingApprRec.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.modulecode != null ">
   or Uts_DingApprRec.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
</if>
<if test="SearchPojo.templateid != null ">
   or Uts_DingApprRec.Templateid like concat('%', #{SearchPojo.templateid}, '%')
</if>
<if test="SearchPojo.apprname != null ">
   or Uts_DingApprRec.ApprName like concat('%', #{SearchPojo.apprname}, '%')
</if>
<if test="SearchPojo.datatemp != null ">
   or Uts_DingApprRec.DataTemp like concat('%', #{SearchPojo.datatemp}, '%')
</if>
<if test="SearchPojo.callbackurl != null ">
   or Uts_DingApprRec.CallbackUrl like concat('%', #{SearchPojo.callbackurl}, '%')
</if>
<if test="SearchPojo.callbackbean != null ">
   or Uts_DingApprRec.CallbackBean like concat('%', #{SearchPojo.callbackbean}, '%')
</if>
<if test="SearchPojo.resultcode != null ">
   or Uts_DingApprRec.ResultCode like concat('%', #{SearchPojo.resultcode}, '%')
</if>
<if test="SearchPojo.apprtype != null ">
   or Uts_DingApprRec.ApprType like concat('%', #{SearchPojo.apprtype}, '%')
</if>
<if test="SearchPojo.apprsn != null ">
   or Uts_DingApprRec.ApprSn like concat('%', #{SearchPojo.apprsn}, '%')
</if>
<if test="SearchPojo.billid != null ">
   or Uts_DingApprRec.Billid like concat('%', #{SearchPojo.billid}, '%')
</if>
<if test="SearchPojo.callbackuuid != null ">
   or Uts_DingApprRec.CallbackUuid like concat('%', #{SearchPojo.callbackuuid}, '%')
</if>
<if test="SearchPojo.callbackname != null ">
   or Uts_DingApprRec.CallbackName like concat('%', #{SearchPojo.callbackname}, '%')
</if>
<if test="SearchPojo.callbackresult != null ">
   or Uts_DingApprRec.CallbackResult like concat('%', #{SearchPojo.callbackresult}, '%')
</if>
<if test="SearchPojo.callbackmsg != null ">
   or Uts_DingApprRec.CallbackMsg like concat('%', #{SearchPojo.callbackmsg}, '%')
</if>
<if test="SearchPojo.userid != null ">
   or Uts_DingApprRec.Userid like concat('%', #{SearchPojo.userid}, '%')
</if>
<if test="SearchPojo.realname != null ">
   or Uts_DingApprRec.RealName like concat('%', #{SearchPojo.realname}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Uts_DingApprRec.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Uts_DingApprRec.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Uts_DingApprRec.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Uts_DingApprRec.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Uts_DingApprRec.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Uts_DingApprRec.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Uts_DingApprRec.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Uts_DingApprRec.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Uts_DingApprRec.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Uts_DingApprRec.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Uts_DingApprRec.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Uts_DingApprRec(id, ModuleCode, Templateid, ApprName, DataTemp, CallbackUrl, CallbackBean, ResultCode, ApprType, ApprSn, Billid, CallbackUuid, CallbackName, CallbackDate, CallbackResult, CallbackMsg, Userid, RealName, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision)
        values (#{id}, #{modulecode}, #{templateid}, #{apprname}, #{datatemp}, #{callbackurl}, #{callbackbean}, #{resultcode}, #{apprtype}, #{apprsn}, #{billid}, #{callbackuuid}, #{callbackname}, #{callbackdate}, #{callbackresult}, #{callbackmsg}, #{userid}, #{realname}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Uts_DingApprRec
        <set>
            <if test="modulecode != null ">
                ModuleCode =#{modulecode},
            </if>
            <if test="templateid != null ">
                Templateid =#{templateid},
            </if>
            <if test="apprname != null ">
                ApprName =#{apprname},
            </if>
            <if test="datatemp != null ">
                DataTemp =#{datatemp},
            </if>
            <if test="callbackurl != null ">
                CallbackUrl =#{callbackurl},
            </if>
            <if test="callbackbean != null ">
                CallbackBean =#{callbackbean},
            </if>
            <if test="resultcode != null ">
                ResultCode =#{resultcode},
            </if>
            <if test="apprtype != null ">
                ApprType =#{apprtype},
            </if>
            <if test="apprsn != null ">
                ApprSn =#{apprsn},
            </if>
            <if test="billid != null ">
                Billid =#{billid},
            </if>
            <if test="callbackuuid != null ">
                CallbackUuid =#{callbackuuid},
            </if>
            <if test="callbackname != null ">
                CallbackName =#{callbackname},
            </if>
            <if test="callbackdate != null">
                CallbackDate =#{callbackdate},
            </if>
            <if test="callbackresult != null ">
                CallbackResult =#{callbackresult},
            </if>
            <if test="callbackmsg != null ">
                CallbackMsg =#{callbackmsg},
            </if>
            <if test="userid != null ">
                Userid =#{userid},
            </if>
            <if test="realname != null ">
                RealName =#{realname},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Uts_DingApprRec where id = #{key} 
    </delete>

    <select id="getEntityBySpno" resultType="inks.service.sa.uts.domain.pojo.UtsDingapprrecPojo">
        select id,
        ModuleCode,
        Templateid,
        ApprName,
        DataTemp,
        CallbackUrl,
        CallbackBean,
        ResultCode,
        ApprType,
        ApprSn,
        Billid,
        CallbackUuid,
        CallbackName,
        CallbackDate,
        CallbackResult,
        CallbackMsg,
        Userid,
        RealName,
        Remark,
        CreateBy,
        CreateByid,
        CreateDate,
        Lister,
        Listerid,
        ModifyDate,
        Custom1,
        Custom2,
        Custom3,
        Custom4,
        Custom5,
        Tenantid,
        Revision
        from Uts_DingApprRec
        where Uts_DingApprRec.ApprSn = #{key}
    </select>

    <select id="getOnlineByBillid" resultType="inks.service.sa.uts.domain.pojo.UtsDingapprrecPojo">
        select id,
        ModuleCode,
        Templateid,
        ApprName,
        DataTemp,
        CallbackUrl,
        CallbackBean,
        ResultCode,
        ApprType,
        ApprSn,
        Billid,
        CallbackUuid,
        CallbackName,
        CallbackDate,
        CallbackResult,
        CallbackMsg,
        Userid,
        RealName,
        Remark,
        CreateBy,
        CreateByid,
        CreateDate,
        Lister,
        Listerid,
        ModifyDate,
        Custom1,
        Custom2,
        Custom3,
        Custom4,
        Custom5,
        Tenantid,
        Revision
        from Uts_DingApprRec
        where Uts_DingApprRec.Billid = #{key}
        and CallbackResult = ''
    </select>
</mapper>

