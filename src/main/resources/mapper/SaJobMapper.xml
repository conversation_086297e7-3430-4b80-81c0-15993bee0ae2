<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.uts.mapper.SaJobMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.uts.domain.pojo.SaJobPojo">
        <include refid="selectSaJobVo"/>
        where Sa_Job.id = #{key} 
    </select>
    <sql id="selectSaJobVo">
        select id,
               JobName,
               JobGroup,
               InvokeTarget,
               CronExpression,
               MisfirePolicy,
               Concurrent,
               Status,
               CreateBy,
               CreateTime,
               UpdateBy,
               UpdateTime,
               Remark
        from Sa_Job
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.uts.domain.pojo.SaJobPojo">
        <include refid="selectSaJobVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_Job.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.jobname != null ">
   and Sa_Job.JobName like concat('%', #{SearchPojo.jobname}, '%')
</if>
<if test="SearchPojo.jobgroup != null ">
   and Sa_Job.JobGroup like concat('%', #{SearchPojo.jobgroup}, '%')
</if>
<if test="SearchPojo.invoketarget != null ">
   and Sa_Job.InvokeTarget like concat('%', #{SearchPojo.invoketarget}, '%')
</if>
<if test="SearchPojo.cronexpression != null ">
   and Sa_Job.CronExpression like concat('%', #{SearchPojo.cronexpression}, '%')
</if>
<if test="SearchPojo.misfirepolicy != null ">
   and Sa_Job.MisfirePolicy like concat('%', #{SearchPojo.misfirepolicy}, '%')
</if>
<if test="SearchPojo.concurrent != null ">
   and Sa_Job.Concurrent like concat('%', #{SearchPojo.concurrent}, '%')
</if>
<if test="SearchPojo.status != null ">
   and Sa_Job.Status like concat('%', #{SearchPojo.status}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_Job.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.updateby != null ">
   and Sa_Job.UpdateBy like concat('%', #{SearchPojo.updateby}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_Job.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.jobname != null ">
   or Sa_Job.JobName like concat('%', #{SearchPojo.jobname}, '%')
</if>
<if test="SearchPojo.jobgroup != null ">
   or Sa_Job.JobGroup like concat('%', #{SearchPojo.jobgroup}, '%')
</if>
<if test="SearchPojo.invoketarget != null ">
   or Sa_Job.InvokeTarget like concat('%', #{SearchPojo.invoketarget}, '%')
</if>
<if test="SearchPojo.cronexpression != null ">
   or Sa_Job.CronExpression like concat('%', #{SearchPojo.cronexpression}, '%')
</if>
<if test="SearchPojo.misfirepolicy != null ">
   or Sa_Job.MisfirePolicy like concat('%', #{SearchPojo.misfirepolicy}, '%')
</if>
<if test="SearchPojo.concurrent != null ">
   or Sa_Job.Concurrent like concat('%', #{SearchPojo.concurrent}, '%')
</if>
<if test="SearchPojo.status != null ">
   or Sa_Job.Status like concat('%', #{SearchPojo.status}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_Job.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.updateby != null ">
   or Sa_Job.UpdateBy like concat('%', #{SearchPojo.updateby}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_Job.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_Job(id, JobName, JobGroup, InvokeTarget, CronExpression, MisfirePolicy, Concurrent, Status, CreateBy, CreateTime, UpdateBy, UpdateTime, Remark)
        values (#{id}, #{jobname}, #{jobgroup}, #{invoketarget}, #{cronexpression}, #{misfirepolicy}, #{concurrent}, #{status}, #{createby}, #{createtime}, #{updateby}, #{updatetime}, #{remark})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Job
        <set>
            <if test="jobname != null ">
                JobName =#{jobname},
            </if>
            <if test="jobgroup != null ">
                JobGroup =#{jobgroup},
            </if>
            <if test="invoketarget != null ">
                InvokeTarget =#{invoketarget},
            </if>
            <if test="cronexpression != null ">
                CronExpression =#{cronexpression},
            </if>
            <if test="misfirepolicy != null ">
                MisfirePolicy =#{misfirepolicy},
            </if>
            <if test="concurrent != null ">
                Concurrent =#{concurrent},
            </if>
            <if test="status != null ">
                Status =#{status},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createtime != null">
                CreateTime =#{createtime},
            </if>
            <if test="updateby != null ">
                UpdateBy =#{updateby},
            </if>
            <if test="updatetime != null">
                UpdateTime =#{updatetime},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_Job where id = #{key} 
    </delete>

    <select id="selectJobAll" resultType="inks.service.sa.uts.domain.pojo.SaJobPojo">
        <include refid="selectSaJobVo"/>
        order by Sa_Job.id
    </select>
</mapper>

