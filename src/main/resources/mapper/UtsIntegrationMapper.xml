<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.uts.mapper.UtsIntegrationMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.uts.domain.pojo.UtsIntegrationPojo">
        <include refid="selectUtsIntegrationVo"/>
        where Uts_Integration.id = #{key} 
    </select>
    <sql id="selectUtsIntegrationVo">
         select
id, InteCode, InteName, ProxyType, ApiUrl, ReqMethod, ReqParam, ReqBody, RespFormat, AuthType, AuthCode, AuthName, AuthSecret, RowNum, Remark, CreateBy, <PERSON><PERSON><PERSON>yi<PERSON>, <PERSON>reate<PERSON>ate, <PERSON>er, <PERSON><PERSON>d, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenanti<PERSON>, TenantName, Revision        from Uts_Integration
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.uts.domain.pojo.UtsIntegrationPojo">
        <include refid="selectUtsIntegrationVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Uts_Integration.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.intecode != null ">
   and Uts_Integration.InteCode like concat('%', #{SearchPojo.intecode}, '%')
</if>
<if test="SearchPojo.intename != null ">
   and Uts_Integration.InteName like concat('%', #{SearchPojo.intename}, '%')
</if>
<if test="SearchPojo.apiurl != null ">
   and Uts_Integration.ApiUrl like concat('%', #{SearchPojo.apiurl}, '%')
</if>
<if test="SearchPojo.reqmethod != null ">
   and Uts_Integration.ReqMethod like concat('%', #{SearchPojo.reqmethod}, '%')
</if>
<if test="SearchPojo.reqparam != null ">
   and Uts_Integration.ReqParam like concat('%', #{SearchPojo.reqparam}, '%')
</if>
<if test="SearchPojo.reqbody != null ">
   and Uts_Integration.ReqBody like concat('%', #{SearchPojo.reqbody}, '%')
</if>
<if test="SearchPojo.respformat != null ">
   and Uts_Integration.RespFormat like concat('%', #{SearchPojo.respformat}, '%')
</if>
<if test="SearchPojo.authcode != null ">
   and Uts_Integration.AuthCode like concat('%', #{SearchPojo.authcode}, '%')
</if>
<if test="SearchPojo.authname != null ">
   and Uts_Integration.AuthName like concat('%', #{SearchPojo.authname}, '%')
</if>
<if test="SearchPojo.authsecret != null ">
   and Uts_Integration.AuthSecret like concat('%', #{SearchPojo.authsecret}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Uts_Integration.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Uts_Integration.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Uts_Integration.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Uts_Integration.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Uts_Integration.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Uts_Integration.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Uts_Integration.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Uts_Integration.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Uts_Integration.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Uts_Integration.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Uts_Integration.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Uts_Integration.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Uts_Integration.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Uts_Integration.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Uts_Integration.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Uts_Integration.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.intecode != null ">
   or Uts_Integration.InteCode like concat('%', #{SearchPojo.intecode}, '%')
</if>
<if test="SearchPojo.intename != null ">
   or Uts_Integration.InteName like concat('%', #{SearchPojo.intename}, '%')
</if>
<if test="SearchPojo.apiurl != null ">
   or Uts_Integration.ApiUrl like concat('%', #{SearchPojo.apiurl}, '%')
</if>
<if test="SearchPojo.reqmethod != null ">
   or Uts_Integration.ReqMethod like concat('%', #{SearchPojo.reqmethod}, '%')
</if>
<if test="SearchPojo.reqparam != null ">
   or Uts_Integration.ReqParam like concat('%', #{SearchPojo.reqparam}, '%')
</if>
<if test="SearchPojo.reqbody != null ">
   or Uts_Integration.ReqBody like concat('%', #{SearchPojo.reqbody}, '%')
</if>
<if test="SearchPojo.respformat != null ">
   or Uts_Integration.RespFormat like concat('%', #{SearchPojo.respformat}, '%')
</if>
<if test="SearchPojo.authcode != null ">
   or Uts_Integration.AuthCode like concat('%', #{SearchPojo.authcode}, '%')
</if>
<if test="SearchPojo.authname != null ">
   or Uts_Integration.AuthName like concat('%', #{SearchPojo.authname}, '%')
</if>
<if test="SearchPojo.authsecret != null ">
   or Uts_Integration.AuthSecret like concat('%', #{SearchPojo.authsecret}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Uts_Integration.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Uts_Integration.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Uts_Integration.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Uts_Integration.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Uts_Integration.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Uts_Integration.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Uts_Integration.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Uts_Integration.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Uts_Integration.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Uts_Integration.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Uts_Integration.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Uts_Integration.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Uts_Integration.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Uts_Integration.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Uts_Integration.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Uts_Integration.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Uts_Integration(id, InteCode, InteName, ProxyType, ApiUrl, ReqMethod, ReqParam, ReqBody, RespFormat, AuthType, AuthCode, AuthName, AuthSecret, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{intecode}, #{intename}, #{proxytype}, #{apiurl}, #{reqmethod}, #{reqparam}, #{reqbody}, #{respformat}, #{authtype}, #{authcode}, #{authname}, #{authsecret}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Uts_Integration
        <set>
            <if test="intecode != null ">
                InteCode =#{intecode},
            </if>
            <if test="intename != null ">
                InteName =#{intename},
            </if>
            <if test="proxytype != null">
                ProxyType =#{proxytype},
            </if>
            <if test="apiurl != null ">
                ApiUrl =#{apiurl},
            </if>
            <if test="reqmethod != null ">
                ReqMethod =#{reqmethod},
            </if>
            <if test="reqparam != null ">
                ReqParam =#{reqparam},
            </if>
            <if test="reqbody != null ">
                ReqBody =#{reqbody},
            </if>
            <if test="respformat != null ">
                RespFormat =#{respformat},
            </if>
            <if test="authtype != null">
                AuthType =#{authtype},
            </if>
            <if test="authcode != null ">
                AuthCode =#{authcode},
            </if>
            <if test="authname != null ">
                AuthName =#{authname},
            </if>
            <if test="authsecret != null ">
                AuthSecret =#{authsecret},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Uts_Integration where id = #{key} 
    </delete>

    <select id="getEntityByCodeAndType" resultType="inks.service.sa.uts.domain.pojo.UtsIntegrationPojo">
        select * from Uts_Integration where InteCode=#{code} and ProxyType=#{proxytype} limit 1
    </select>
</mapper>

