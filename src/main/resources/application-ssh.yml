# SSH执行配置
ssh:
  execution:
    # 默认命令超时时间（毫秒）
    default-timeout-ms: 30000
    
    # 默认连接超时时间（毫秒）
    default-connection-timeout-ms: 15000
    
    # 默认最大重试次数
    default-max-retries: 3
    
    # 重试延迟基数（毫秒）
    retry-delay-base-ms: 1000
    
    # 最大重试延迟（毫秒）
    max-retry-delay-ms: 10000
    
    # 是否启用指数退避重试策略
    enable-exponential-backoff: true
    
    # 默认是否在错误时继续执行
    default-continue-on-error: false
    
    # 流水线执行最大等待时间（毫秒）- 2小时
    max-pipeline-wait-ms: 7200000
    
    # 状态检查间隔（毫秒）
    status-check-interval-ms: 10000
    
    # 是否启用详细日志
    enable-verbose-logging: true
    
    # 命令输出最大长度（字符）
    max-output-length: 10000
    
    # 是否自动清理过期会话
    auto-cleanup-expired-sessions: true
    
    # 会话过期时间（毫秒）- 24小时
    session-expiration-ms: 86400000

# 日志配置
logging:
  level:
    inks.service.sa.uts.service.impl.SshExecutorServiceImpl: INFO
    inks.service.sa.uts.util.PatternMatcherUtil: DEBUG
    
# 常用正则表达式模板（用于前端参考）
ssh:
  patterns:
    success:
      # 通用成功模式
      general: "(?i).*(success|successful|completed|done|ok|finished).*"
      # 安装成功
      install: "(?i).*(install.*complete|installation.*successful|successfully installed).*"
      # 启动成功
      start: "(?i).*(started|running|active|up and running).*"
      # 连接成功
      connect: "(?i).*(connected|connection.*established|login.*successful).*"
      # 文件操作成功
      file: "(?i).*(file.*created|copied|moved|deleted|exists).*"
      
    error:
      # 通用错误模式
      general: "(?i).*(error|fail|failed|exception|denied|not found|cannot|unable).*"
      # 权限错误
      permission: "(?i).*(permission denied|access denied|unauthorized|forbidden).*"
      # 网络错误
      network: "(?i).*(network.*unreachable|connection.*refused|timeout|host.*not.*found).*"
      # 文件错误
      file: "(?i).*(no such file|file not found|directory not found|disk.*full).*"
      # 命令错误
      command: "(?i).*(command not found|invalid.*command|syntax.*error).*"
      # 服务错误
      service: "(?i).*(service.*failed|daemon.*error|process.*died).*"
      
    timeout:
      # 超时模式
      general: "(?i).*(timeout|timed out|time out|deadline exceeded).*"
      
# 预定义命令模板（用于快速创建流水线步骤）
ssh:
  command-templates:
    system:
      check-os: "cat /etc/os-release"
      check-memory: "free -h"
      check-disk: "df -h"
      check-cpu: "lscpu"
      check-uptime: "uptime"
      
    docker:
      check-version: "docker --version"
      check-status: "systemctl status docker"
      start-service: "systemctl start docker"
      stop-service: "systemctl stop docker"
      list-containers: "docker ps -a"
      
    network:
      check-connectivity: "ping -c 3 google.com"
      check-ports: "netstat -tuln"
      check-dns: "nslookup google.com"
      
    file:
      list-directory: "ls -la"
      check-file-exists: "test -f /path/to/file && echo 'exists' || echo 'not found'"
      create-directory: "mkdir -p /path/to/directory"
      remove-file: "rm -f /path/to/file"
      
    service:
      list-services: "systemctl list-units --type=service"
      check-service-status: "systemctl is-active service-name"
      start-service: "systemctl start service-name"
      stop-service: "systemctl stop service-name"
      restart-service: "systemctl restart service-name"
