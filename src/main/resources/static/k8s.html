<!DOCTYPE html>
<html lang="zh-CN" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>K8s YAML 文本替换工具</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --bg-color: #1a1a1a;
            --text-color: #e0e0e0;
            --container-bg: #242424;
            --input-bg: #333;
            --input-border: #444;
            --input-focus-border: #4a90e2;
            --primary-color: #4a90e2;
            --primary-hover: #5aa1f2;
            --secondary-color: #50e3c2;
            --secondary-hover: #61f4d3;
            --danger-color: #e25c5c;
            --danger-hover: #f36d6d;
            --table-header-bg: #2c2c2c;
            --shadow-color: rgba(0, 0, 0, 0.4);
            --switch-bg: #444;
            --switch-knob: #fff;
        }

        html.light {
            --bg-color: #f4f7f9;
            --text-color: #333;
            --container-bg: #ffffff;
            --input-bg: #fdfdfd;
            --input-border: #e0e0e0;
            --input-focus-border: #1a73e8;
            --primary-color: #1a73e8;
            --primary-hover: #185abc;
            --secondary-color: #34a853;
            --secondary-hover: #2e8c45;
            --danger-color: #e23c3c;
            --danger-hover: #c93030;
            --table-header-bg: #f7fbff;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --switch-bg: #ccc;
            --switch-knob: #fff;
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            margin: 0;
            padding: 2rem;
            background-color: var(--bg-color);
            color: var(--text-color);
            transition: background-color 0.3s, color 0.3s;
        }

        .container {
            width: 90%;
            max-width: 1400px;
            margin: 0 auto;
            background-color: var(--container-bg);
            padding: 2rem 2.5rem;
            border-radius: 12px;
            box-shadow: 0 8px 24px var(--shadow-color);
            transition: background-color 0.3s;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            border-bottom: 1px solid var(--input-border);
            padding-bottom: 1.5rem;
        }

        h1 {
            text-align: center;
            color: var(--primary-color);
            margin: 0;
            font-weight: 700;
            letter-spacing: 1px;
        }

        .theme-switcher {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 28px;
        }

        .switch input { display: none; }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0; left: 0; right: 0; bottom: 0;
            background-color: var(--switch-bg);
            transition: .4s;
            border-radius: 28px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 20px;
            width: 20px;
            left: 4px;
            bottom: 4px;
            background-color: var(--switch-knob);
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--primary-color);
        }

        input:checked + .slider:before {
            transform: translateX(22px);
        }

        #copyFrom {
            cursor: pointer;
            user-select: all;
            color: var(--secondary-color);
            margin-bottom: 1.5rem;
            transition: color 0.3s;
            font-weight: 500;
        }
        #copyFrom:hover { color: var(--secondary-hover); }

        .rules-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0 8px;
            margin-bottom: 1.5rem;
        }

        .rules-table th, .rules-table td {
            padding: 12px 15px;
            font-size: 14px;
            text-align: left;
        }

        .rules-table th {
            background: var(--table-header-bg);
            font-weight: 500;
        }
        .rules-table th:first-child { border-radius: 8px 0 0 8px; }
        .rules-table th:last-child { border-radius: 0 8px 8px 0; }

        .rules-table td {
            background: var(--input-bg);
        }
        .rules-table td:first-child { border-radius: 8px 0 0 8px; }
        .rules-table td:last-child { border-radius: 0 8px 8px 0; }

        .rules-table input {
            width: 98%;
            font-size: 14px;
            padding: 8px 10px;
            background-color: transparent;
            color: var(--text-color);
            border: 1px solid var(--input-border);
            border-radius: 6px;
            transition: border-color 0.3s, box-shadow 0.3s;
        }
        .rules-table input:focus {
            outline: none;
            border-color: var(--input-focus-border);
            box-shadow: 0 0 0 2px color-mix(in srgb, var(--input-focus-border) 20%, transparent);
        }

        .rules-table .btn-action {
            font-size: 18px;
            padding: 0.2rem 0.8rem;
            border-radius: 6px;
            border: none;
            margin: 0 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .rules-table .add-btn { background: var(--primary-color); color: white; }
        .rules-table .add-btn:hover { background: var(--primary-hover); }
        .rules-table .del-btn { background: var(--danger-color); color: white; }
        .rules-table .del-btn:hover { background: var(--danger-hover); }

        .textarea-container {
            display: flex;
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }

        textarea {
            width: 100%;
            height: 60vh;
            min-height: 500px;
            padding: 1rem;
            border: 1px solid var(--input-border);
            border-radius: 8px;
            font-size: 14px;
            line-height: 1.6;
            resize: vertical;
            background-color: var(--input-bg);
            color: var(--text-color);
            transition: border-color 0.3s, box-shadow 0.3s, background-color 0.3s;
        }
        textarea:focus {
            outline: none;
            border-color: var(--input-focus-border);
            box-shadow: 0 0 0 3px color-mix(in srgb, var(--input-focus-border) 20%, transparent);
        }

        .controls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .btn {
            padding: 0.8rem 1.8rem;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            border: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            color: white;
            letter-spacing: 0.5px;
        }

        #replaceBtn {
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            box-shadow: 0 4px 15px -5px color-mix(in srgb, var(--primary-color) 50%, var(--secondary-color));
        }
        #replaceBtn:hover { transform: translateY(-2px); box-shadow: 0 6px 20px -5px color-mix(in srgb, var(--primary-color) 50%, var(--secondary-color)); }

        #copyBtn {
            background-color: var(--secondary-color);
        }
        #copyBtn:hover { background-color: var(--secondary-hover); transform: translateY(-2px); }

        .toast-notification {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: color-mix(in srgb, var(--secondary-color) 90%, #000);
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.4s ease, top 0.4s ease;
            font-weight: 500;
        }

        .toast-notification.show {
            top: 40px;
            opacity: 1;
        }

        .copy-status {
            font-size: 14px;
            color: var(--secondary-color);
        }
    </style>
</head>
<body>
<div class="container">
    <div class="header">
        <h1>K8s YAML 文本替换工具</h1>
        <div class="theme-switcher">
            <span>☀️</span>
            <label class="switch">
                <input type="checkbox" id="themeToggle" checked>
                <span class="slider"></span>
            </label>
            <span>🌙</span>
        </div>
    </div>

    <h4 id="copyFrom">FROM hub.inksyun.com/inksdev/openjdk:8-jre</h4>

    <!-- 规则编辑区域 -->
    <table class="rules-table" id="rulesTable">
        <thead>
        <tr>
            <th style="width:42%">查找 (支持正则)</th>
            <th style="width:42%">替换为</th>
            <th style="width:16%; text-align: center;">操作</th>
        </tr>
        </thead>
        <tbody>
        <!-- js动态填充 -->
        </tbody>
    </table>

    <div class="controls">
        <button id="replaceBtn" class="btn">开始替换并复制</button>
        <button id="copyBtn" class="btn">复制结果</button>
    </div>

    <div class="textarea-container">
        <textarea id="inputText" placeholder="在此处粘贴原始文本..."></textarea>
        <textarea id="outputText" placeholder="替换后的文本将显示在这里..."></textarea>
    </div>

</div>
<script>
    const defaultRules = [
        {from: 'aliyun-docker-auth', to: 'inks-docker-auth', flags: 'g'},
        {from: 'registry.cn-hangzhou.aliyuncs.com/inksdev', to: '$REGISTRY/$DOCKERHUB_NAMESPACE', flags: 'g'},
        {from: 'registry.cn-hangzhou.aliyuncs.com', to: 'hub.inksyun.com', flags: 'g'}
    ];
    let rules = JSON.parse(localStorage.getItem('customRules')) || JSON.parse(JSON.stringify(defaultRules));

    function saveRules() {
        localStorage.setItem('customRules', JSON.stringify(rules));
    }

    function renderRulesTable() {
        const tbody = document.querySelector('#rulesTable tbody');
        tbody.innerHTML = '';
        rules.forEach((rule, idx) => {
            const tr = document.createElement('tr');
            tr.innerHTML = `
            <td><input value="${rule.from.replace(/"/g, '&quot;')}" data-field="from" data-idx="${idx}"></td>
            <td><input value="${rule.to.replace(/"/g, '&quot;')}" data-field="to" data-idx="${idx}"></td>
            <td style="text-align: center;">
              <button class="btn-action add-btn" data-idx="${idx}">+</button>
              <button class="btn-action del-btn" data-idx="${idx}">-</button>
            </td>
        `;
            tbody.appendChild(tr);
        });
    }
    renderRulesTable();

    document.querySelector('#rulesTable').addEventListener('input', function(e) {
        if (e.target.tagName === 'INPUT') {
            const idx = Number(e.target.dataset.idx);
            const field = e.target.dataset.field;
            rules[idx][field] = e.target.value;
            saveRules();
        }
    });

    document.querySelector('#rulesTable').addEventListener('click', function(e) {
        if (e.target.classList.contains('add-btn')) {
            const idx = Number(e.target.dataset.idx);
            rules.splice(idx + 1, 0, {from: '', to: '', flags: 'g'});
            renderRulesTable();
            saveRules();
        }
        if (e.target.classList.contains('del-btn')) {
            if (rules.length === 1) {
                rules[0] = {from: '', to: '', flags: 'g'};
            } else {
                rules.splice(idx, 1);
            }
            renderRulesTable();
            saveRules();
        }
    });

    function showToast(message) {
        // Remove any existing toast
        const existingToast = document.querySelector('.toast-notification');
        if (existingToast) {
            existingToast.remove();
        }

        const toast = document.createElement('div');
        toast.className = 'toast-notification';
        toast.textContent = message;
        document.body.appendChild(toast);

        // Animate in
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);

        // Animate out and remove
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 500);
        }, 2000);
    }

    // 兼容性复制函数 - 支持多种fallback机制
    async function copyToClipboard(text, successMessage = '已复制到剪贴板!', errorMessage = '复制失败!') {
        // 方法1: 尝试使用现代 Clipboard API
        if (navigator.clipboard && window.isSecureContext) {
            try {
                await navigator.clipboard.writeText(text);
                showToast(successMessage);
                return true;
            } catch (err) {
                console.warn('Clipboard API failed, trying fallback:', err);
            }
        }

        // 方法2: 使用传统的 execCommand 方法
        try {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            const successful = document.execCommand('copy');
            document.body.removeChild(textArea);

            if (successful) {
                showToast(successMessage);
                return true;
            }
        } catch (err) {
            console.warn('execCommand failed:', err);
        }

        // 方法3: 如果都失败了，创建一个可选择的文本区域
        try {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
            `;

            const content = document.createElement('div');
            content.style.cssText = `
                background: var(--container-bg);
                padding: 2rem;
                border-radius: 8px;
                max-width: 80%;
                max-height: 80%;
                overflow: auto;
            `;

            const title = document.createElement('h3');
            title.textContent = '请手动复制以下内容：';
            title.style.color = 'var(--text-color)';
            title.style.marginBottom = '1rem';

            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.cssText = `
                width: 100%;
                height: 200px;
                padding: 1rem;
                border: 1px solid var(--input-border);
                border-radius: 4px;
                background: var(--input-bg);
                color: var(--text-color);
                font-family: monospace;
                resize: vertical;
            `;
            textArea.readOnly = true;

            const closeBtn = document.createElement('button');
            closeBtn.textContent = '关闭';
            closeBtn.style.cssText = `
                margin-top: 1rem;
                padding: 0.5rem 1rem;
                background: var(--primary-color);
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
            `;

            closeBtn.onclick = () => document.body.removeChild(modal);
            modal.onclick = (e) => {
                if (e.target === modal) document.body.removeChild(modal);
            };

            content.appendChild(title);
            content.appendChild(textArea);
            content.appendChild(closeBtn);
            modal.appendChild(content);
            document.body.appendChild(modal);

            // 自动选择文本
            textArea.select();
            textArea.focus();

            showToast('自动复制失败，请手动复制文本');
            return false;
        } catch (err) {
            console.error('All copy methods failed:', err);
            showToast(errorMessage);
            return false;
        }
    }

    document.getElementById('replaceBtn').addEventListener('click', function () {
        let originalText = document.getElementById('inputText').value;
        let processedText = originalText;
        rules.forEach(rule => {
            try {
                if (rule.from) {
                    processedText = processedText.replace(new RegExp(rule.from, rule.flags || 'g'), rule.to);
                }
            } catch (e) {
                console.error('Invalid Regex:', e);
            }
        });
        const outputElem = document.getElementById('outputText');
        outputElem.value = processedText;
        if (processedText) {
            // 使用兼容性复制函数
            copyToClipboard(processedText, '已复制到剪贴板!', '自动复制失败!');
        }
    });

    document.getElementById('copyBtn').addEventListener('click', function () {
        const outputText = document.getElementById('outputText').value;
        if (outputText) {
            // 使用兼容性复制函数
            copyToClipboard(outputText, '已复制到剪贴板!', '复制失败!');
        } else {
            showToast('没有可复制的内容。');
        }
    });

    document.getElementById('copyFrom').addEventListener('click', function () {
        const text = this.innerText;
        // 使用兼容性复制函数
        copyToClipboard(text, '已复制 FROM 地址!', '复制失败!');
    });

    // Theme switcher logic
    const themeToggle = document.getElementById('themeToggle');
    const html = document.documentElement;

    function setTheme(isDark) {
        if (isDark) {
            html.classList.add('dark');
            html.classList.remove('light');
            localStorage.setItem('theme', 'dark');
        } else {
            html.classList.add('light');
            html.classList.remove('dark');
            localStorage.setItem('theme', 'light');
        }
    }

    themeToggle.addEventListener('change', () => {
        setTheme(themeToggle.checked);
    });

    // Load saved theme
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'light') {
        themeToggle.checked = false;
        setTheme(false);
    } else {
        themeToggle.checked = true;
        setTheme(true);
    }
</script>
</body>
</html>
