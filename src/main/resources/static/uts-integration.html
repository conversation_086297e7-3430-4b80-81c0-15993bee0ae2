<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <title>API 整合转发管理</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <style>
        body { padding: 20px; }
        .table-responsive { max-height: 70vh; }
        #dlgProxy .modal-dialog { max-width: 90vw; }
        .form-text { font-size: 0.875rem; }
        .card-header { background-color: #f8f9fa; }
    </style>
</head>
<body>
<div class="container-fluid">
    <div class="card">
        <div class="card-header">
            <h3 class="mb-0">API 整合转发管理</h3>
        </div>
        <div class="card-body">
            <div id="loading" class="text-center mb-3 d-none">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
            </div>

            <div class="mb-3">
                <button id="btnAdd" class="btn btn-outline-success btn-sm"><i class="bi-plus"></i> 新增</button>
                <button id="btnDelete" class="btn btn-outline-danger btn-sm" disabled><i class="bi-trash"></i> 删除</button>
            </div>

            <div class="table-responsive border rounded">
                <table id="tblData" class="table table-striped table-hover table-bordered align-middle mb-0">
                    <thead class="table-light text-center">
                    <tr>
                        <th style="width: 150px;">操作</th>
                        <th style="width: 50px;"><input type="checkbox" id="chkAll"/></th>
                        <th>系统编码</th>
                        <th>系统名称</th>
                        <th>代理类型</th>
                        <th>目标API</th>
                        <th>请求方式</th>
                        <th>请求参数</th>
                        <th>请求体</th>
                        <th>返回格式转换</th>
                        <th>鉴权方式</th>
                        <th>授权码</th>
                        <th>备注</th>
                        <th>创建者</th>
                        <th>创建时间</th>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>

            <nav class="mt-3">
                <ul class="pagination pagination-sm" id="pagination"></ul>
            </nav>
        </div>
    </div>
</div>

<div class="modal fade" id="dlgEdit" tabindex="-1">
    <div class="modal-dialog modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="dlgTitle">新增</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="frmData" class="row g-3">
                    <input type="hidden" name="id" />
                    <div class="col-md-6">
                        <label class="form-label">系统编码 <span class="text-danger">*</span></label>
                        <input class="form-control" name="intecode" required />
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">系统名称 <span class="text-danger">*</span></label>
                        <input class="form-control" name="intename" required />
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">代理类型</label>
                        <select class="form-select" name="proxytype">
                            <option value="0">PageList</option>
                            <option value="1">PageTh</option>
                            <option value="2">Entity</option>
                            <option value="3">BillEntity</option>
                            <option value="4">BillList</option>
                            <option value="5">Update</option>
                        </select>
                        <div class="form-text">选择API的代理类型</div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">API URL</label>
                        <input class="form-control" name="apiurl" />
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">请求方式</label>
                        <select class="form-select" name="reqmethod">
                            <option value="GET">GET</option>
                            <option value="POST">POST</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">请求参数</label>
                        <textarea class="form-control" rows="2" name="reqparam" placeholder='JSON格式, 例如: {"id": "123", "type": "A"}'></textarea>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">请求体</label>
                        <textarea class="form-control" rows="2" name="reqbody" placeholder="请输入请求体"></textarea>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">返回格式转换配置</label>
                        <textarea class="form-control" rows="2" name="respformat" placeholder="请输入返回格式转换配置"></textarea>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">鉴权方式</label>
                        <select class="form-select" name="authtype">
                            <option value="0">Auth Code</option>
                            <option value="1">用户名密码</option>
                        </select>
                        <div class="form-text">选择鉴权方式</div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">授权码</label>
                        <input class="form-control" name="authcode" />
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">用户名</label>
                        <input class="form-control" name="authname" />
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">密钥</label>
                        <input class="form-control" name="authsecret" />
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">行号</label>
                        <input class="form-control" type="number" name="rownum" />
                    </div>
                    <div class="col-12">
                        <label class="form-label">备注</label>
                        <textarea class="form-control" rows="2" name="remark" placeholder="请输入备注"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="btnSave">保存</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="dlgProxy" tabindex="-1">
    <div class="modal-dialog modal-xl modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">接口测试</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="frmProxy" class="row g-3">
                    <input type="hidden" name="host" value="http://*************:10684"/>
                    <div class="col-md-4">
                        <label class="form-label">目标API</label>
                        <input class="form-control" name="target" readonly />
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">集成编码 code</label>
                        <input class="form-control" name="code"/>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">操作</label>
                        <select class="form-select" name="action">
                            <option value="proxyPageList">proxyPageList (POST)</option>
                            <option value="proxyPageTh">proxyPageTh (POST)</option>
                            <option value="proxyBillList">proxyBillList (POST)</option>
                            <option value="proxyEntity">proxyEntity (GET)</option>
                            <option value="proxyBillEntity">proxyBillEntity (GET)</option>
                            <option value="proxyUpdate">proxyUpdate (POST)</option>
                            <option value="testConnection">testConnection (GET)</option>
                        </select>
                    </div>
                    <div class="col-12 query-params-field">
                        <label class="form-label">查询参数 (Query Parameters)</label>
                        <textarea class="form-control" rows="2" name="queryParams" placeholder="请输入查询参数, 格式: key1=value1&key2=value2"></textarea>
                        <div class="form-text">将作为URL查询字符串附加到请求后。</div>
                    </div>
                    <div class="col-12 body-field">
                        <label class="form-label">请求体 (JSON)</label>
                        <textarea class="form-control" rows="5" name="json" placeholder='请输入JSON格式的请求体，例如：{"key": "value"}'></textarea>
                        <div class="form-text">用于POST请求的JSON数据。</div>
                    </div>
                </form>
                <hr/>
                <h6>响应结果</h6>
                <pre id="txtProxyResult" class="border bg-light p-3" style="max-height:40vh;overflow:auto;"></pre>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="btnProxySend">发送请求</button>
            </div>
        </div>
    </div>
</div>

<script>
    const baseUrl = 'http://*************:10684/S34M16B1';
    $.ajaxSetup({
        headers: { 'Authorization': 'admin' }
    });
    let pageNum = 1, pageSize = 15, totalPages = 1;

    // ---------- 列表加载 ----------
    function loadPage(page = 1) {
        $('#loading').removeClass('d-none');
        const param = {
            pageNum: page,
            pageSize: pageSize,
            searchPojo: {},
        };
        $.ajax({
            url: baseUrl + '/getPageList',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(param),
            success: res => {
                if (res.code === 200) {
                    renderTable(res.data.list || []);
                    renderPagination(res.data.pages || 1, res.data.pageNum || 1);
                } else {
                    alert('加载失败: ' + res.msg);
                    showToast('加载失败: ' + res.msg, 'danger');
                }
            },
            error: () => {
                alert('服务器错误');
                showToast('服务器连接失败', 'danger');
            },
            complete: () => $('#loading').addClass('d-none')
        });
    }

    function renderTable(list) {
        const $tbody = $('#tblData tbody');
        $tbody.empty();
        list.forEach(row => {
            const tr = `<tr data-id="${row.id}" data-code="${row.intecode}" data-proxy="${row.proxytype}">
                <td class="text-center">
                    <button class="btn btn-sm btn-outline-info btn-test me-1" title="接口测试">
                        <i class="bi-play"></i> 测试
                    </button>
                    <button class="btn btn-sm btn-outline-primary btn-edit" title="编辑">
                        <i class="bi-pencil"></i>
                    </button>
                </td>
                <td class="text-center"><input type="checkbox" class="chkRow"></td>
                <td>${row.intecode || ''}</td>
                <td>${row.intename || ''}</td>
                <td>${['PageList','PageTh','Entity','BillEntity','BillList','Update'][row.proxytype] || row.proxytype || ''}</td>
                <td style="max-width:200px; word-break:break-all;">${row.apiurl || ''}</td>
                <td>${row.reqmethod || ''}</td>
                <td style="max-width:150px; word-break:break-all;">${row.reqparam || ''}</td>
                <td style="max-width:150px; word-break:break-all;">${row.reqbody || ''}</td>
                <td>${row.respformat || ''}</td>
                <td>${row.authtype === 0 ? 'authcode' : (row.authtype === 1 ? '用户名密码' : (row.authtype || ''))}</td>
                <td>${row.authcode || ''}</td>
                <td>${row.remark || ''}</td>
                <td>${row.createby || ''}</td>
                <td>${row.createdate ? (String(row.createdate).substring(0,19)) : ''}</td>
            </tr>`;
            $tbody.append(tr);
        });
    }

    function renderPagination(pages, current) {
        totalPages = pages; pageNum = current;
        const $ul = $('#pagination');
        $ul.empty();
        // Simplified pagination for brevity
        const start = Math.max(1, current - 2);
        const end = Math.min(pages, current + 2);
        if (current > 1) $ul.append('<li class="page-item"><a class="page-link" href="#" data-page="'+(current-1)+'">«</a></li>');
        for (let i = start; i <= end; i++) {
            $ul.append(`<li class="page-item ${i===current ? 'active' : ''}"><a class="page-link" href="#" data-page="${i}">${i}</a></li>`);
        }
        if (current < pages) $ul.append('<li class="page-item"><a class="page-link" href="#" data-page="'+(current+1)+'">»</a></li>');
    }

    $('#pagination').on('click', 'a', function (e) {
        e.preventDefault();
        const page = parseInt($(this).data('page'));
        if (page) loadPage(page);
    });

    // ---------- 复选框处理 ----------
    $('#chkAll').on('change', function () {
        $('.chkRow').prop('checked', this.checked).trigger('change');
    });

    $('#tblData').on('change', '.chkRow', function () {
        const selected = $('.chkRow:checked').length;
        $('#btnDelete').prop('disabled', selected === 0);
        $('#chkAll').prop('checked', selected > 0 && selected === $('.chkRow').length);
    });

    // ---------- 新增/编辑 ----------
    $('#btnAdd').click(() => {
        $('#dlgTitle').text('新增');
        $('#frmData')[0].reset();
        $('#frmData input[name=id]').val('');
        $('#dlgEdit').modal('show');
    });

    $(document).on('click', '.btn-edit', function() {
        const id = $(this).closest('tr').data('id');
        if (!id) return;
        $.get(`${baseUrl}/getEntity?key=${id}`, res => {
            if (res.code === 200) {
                $('#dlgTitle').text('编辑');
                fillForm($('#frmData'), res.data);
                $('#dlgEdit').modal('show');
            } else {
                showToast(res.msg, 'danger');
            }
        });
    });

    function fillForm($form, data) {
        $form[0].reset();
        for (const key in data) {
            if (data.hasOwnProperty(key)) {
                $form.find(`[name="${key}"]`).val(data[key]);
            }
        }
    }

    // ---------- 删除 ----------
    $('#btnDelete').click(() => {
        const ids = $('.chkRow:checked').map((_, el) => $(el).closest('tr').data('id')).get();
        if (ids.length === 0 || !confirm(`确定删除选中的 ${ids.length} 条记录吗？`)) return;

        const requests = ids.map(id => $.get(`${baseUrl}/delete?key=${id}`));
        Promise.all(requests).then(results => {
            loadPage($('.chkRow').length === ids.length ? Math.max(1, pageNum-1) : pageNum);
            showToast('删除成功', 'success');
        }).catch(err => showToast('删除失败', 'danger'));
    });

    // ---------- 保存 ----------
    $('#btnSave').click(() => {
        const form = $('#frmData')[0];
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }
        const data = formToJson($('#frmData'));
        const url = data.id ? '/update' : '/create';
        $.ajax({
            url: baseUrl + url,
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(data),
            success: res => {
                if (res.code === 200) {
                    bootstrap.Modal.getInstance(document.getElementById('dlgEdit')).hide();
                    const targetPage = data.id ? pageNum : 1; // if edit, stay, if new, go to first
                    loadPage(targetPage);
                    showToast('保存成功', 'success');
                } else {
                    showToast(res.msg, 'danger');
                }
            },
            error: () => showToast('请求失败', 'danger')
        });
    });

    function formToJson($form) {
        const o = {};
        $form.serializeArray().forEach(i => o[i.name] = (i.value || '').trim());
        return o;
    }

    // ---------- 接口测试 ----------
    const proxyMap = ['proxyPageList','proxyPageTh','proxyEntity','proxyBillEntity','proxyBillList','proxyUpdate'];

    // 接口测试按钮点击
    $(document).on('click', '.btn-test', function() {
        const $row = $(this).closest('tr');
        const code = $row.data('code');
        const api  = $row.find('td:eq(5)').text().trim();
        const ptype = parseInt($row.data('proxy'));
        const action = proxyMap[ptype] || 'proxyPageList';

        const reqParam = $row.find('td:eq(7)').text().trim();
        const reqBody = $row.find('td:eq(8)').text().trim();

        $('#frmProxy')[0].reset();

        $('#frmProxy [name=code]').val(code);
        $('#frmProxy [name=target]').val(api);
        $('#frmProxy [name=action]').val(action);

        // MODIFIED: Populate the generic queryParams field
        if (reqParam) {
            try {
                const params = JSON.parse(reqParam);
                const paramStr = Object.entries(params)
                    .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
                    .join('&');
                $('#frmProxy [name=queryParams]').val(paramStr);
            } catch (e) {
                // If not valid JSON, assume it's already a query string
                $('#frmProxy [name=queryParams]').val(reqParam);
            }
        }

        if (reqBody) {
            try {
                const jsonObj = JSON.parse(reqBody);
                $('#frmProxy [name=json]').val(JSON.stringify(jsonObj, null, 2));
            } catch (e) {
                $('#frmProxy [name=json]').val(reqBody);
            }
        }

        updateProxyField();
        $('#txtProxyResult').text('');
        $('#dlgProxy').modal('show');
    });

    function updateProxyField() {
        const action = $('#frmProxy [name=action]').val();
        const isPostBody = ['proxyPageList', 'proxyPageTh', 'proxyBillList', 'proxyUpdate'].includes(action);

        // MODIFIED: The query params field is always visible. We only toggle the body field.
        $('.body-field').toggleClass('d-none', !isPostBody);
    }
    $('#frmProxy [name=action]').on('change', updateProxyField);

    $('#btnProxySend').click(() => {
        const host   = $('#frmProxy [name=host]').val().replace(/\/$/, '') + '/S34M16B1';
        const action = $('#frmProxy [name=action]').val();
        const code   = encodeURIComponent($('#frmProxy [name=code]').val());
        const json   = $('#frmProxy [name=json]').val();

        // MODIFIED: Read from the generic queryParams textarea
        const queryParams = $('#frmProxy [name=queryParams]').val().trim();

        // MODIFIED: Build URL with generic query parameters
        let url = `${host}/${action}?code=${code}`;
        if (queryParams) {
            url += `&${queryParams}`;
        }

        let type = 'GET';
        let data = null;

        if (['proxyPageList', 'proxyPageTh', 'proxyBillList', 'proxyUpdate'].includes(action)) {
            type = 'POST';
            data = json || '{}';
        }

        $('#txtProxyResult').text('加载中...');
        $.ajax({
            url,
            type,
            data,
            contentType: type === 'POST' ? 'application/json' : 'application/x-www-form-urlencoded',
            success: res => {
                $('#txtProxyResult').text(JSON.stringify(res, null, 2));
                showToast('请求成功', 'success');
            },
            error: xhr => {
                let errorMsg = '请求失败';
                try {
                    const errRes = JSON.parse(xhr.responseText);
                    errorMsg += `: ${errRes.msg || xhr.statusText}`;
                } catch(e) {
                    errorMsg += `: ${xhr.statusText}`;
                }
                $('#txtProxyResult').text(errorMsg + '\n\n' + xhr.responseText);
                showToast(errorMsg, 'danger');
            }
        });
    });

    // Toast 通知
    function showToast(message, type) {
        const toastId = 'toast-' + Date.now();
        const toastHtml = `
            <div id="${toastId}" class="toast align-items-center text-white bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>`;
        $('.toast-container').append(toastHtml);
        const toastEl = document.getElementById(toastId);
        const bsToast = new bootstrap.Toast(toastEl, { delay: 3000 });
        bsToast.show();
        toastEl.addEventListener('hidden.bs.toast', () => toastEl.remove());
    }

    // 初始加载
    $(document).ready(function() {
        $('body').append('<div class="toast-container position-fixed bottom-0 end-0 p-3" style="z-index: 1100"></div>');
        loadPage(1);
    });
</script>

</body>
</html>