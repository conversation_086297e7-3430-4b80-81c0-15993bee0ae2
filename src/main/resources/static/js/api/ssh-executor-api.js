import request from '@/utils/request'

// SSH流水线执行API
export default {
  // 测试SSH连接
  testConnection(serverId) {
    return request({
      url: '/S34M11B2/testConnection',
      method: 'get',
      params: { serverId }
    })
  },
  
  // 执行SSH流水线
  executePipeline(pipelineId, serverId) {
    return request({
      url: '/S34M11B2/executePipeline',
      method: 'post',
      params: { pipelineId, serverId }
    })
  },
  
  // 批量执行SSH流水线
  executePipelineMultiple(pipelineId, serverIds) {
    return request({
      url: '/S34M11B2/executePipelineMultiple',
      method: 'post',
      params: { pipelineId },
      data: serverIds
    })
  },
  
  // 快速关机
  quickShutdown(serverId) {
    return request({
      url: '/S34M11B2/quickShutdown',
      method: 'post',
      params: { serverId }
    })
  },
  
  // 快速重启
  quickRestart(serverId) {
    return request({
      url: '/S34M11B2/quickRestart',
      method: 'post',
      params: { serverId }
    })
  },
  
  // 异步执行SSH流水线
  executeAsyncPipeline(pipelineId, serverId) {
    return request({
      url: '/S34M11B2/executeAsyncPipeline',
      method: 'post',
      params: { pipelineId, serverId }
    })
  },
  
  // 取消SSH流水线执行
  cancelExecution(sessionId) {
    return request({
      url: '/S34M11B2/cancelExecution',
      method: 'post',
      params: { sessionId }
    })
  },
  
  // 获取SSH流水线执行状态
  getExecutionStatus(sessionId) {
    return request({
      url: '/S34M11B2/getExecutionStatus',
      method: 'get',
      params: { sessionId }
    })
  },
  
  // 执行单个SSH命令
  executeCommand(params) {
    return request({
      url: '/S34M11B2/executeCommand',
      method: 'post',
      data: params
    })
  }
}
