// SSH Pipeline Management
import sshExecutorApi from '@/api/ssh-executor-api'
import { Message, MessageBox, Loading } from 'element-ui'

export default {
  name: 'SshPipelineManagement',
  
  data() {
    return {
      // 加载状态
      loading: false,
      serverLoading: false,
      pipelineLoading: false,
      executionLoading: false,
      
      // 服务器列表
      serverList: [],
      selectedServer: null,
      serverConnectionStatus: {},
      
      // 流水线列表
      pipelineList: [],
      selectedPipeline: null,
      
      // 执行相关
      executionResults: [],
      currentExecution: null,
      activeSessionId: null,
      pollingTimer: null,
      
      // 命令执行
      commandForm: {
        serverId: '',
        command: '',
        successPattern: '',
        errorPattern: '',
        timeoutMs: 60000
      },
      commandResult: null,
      
      // 历史记录
      historyList: [],
      historyDetail: null,
      
      // 表单和弹窗控制
      showCommandModal: false,
      showHistoryModal: false,
      showExecutionDetailModal: false,
      
      // 分页参数
      pageInfo: {
        total: 0,
        current: 1,
        size: 10
      }
    }
  },
  
  computed: {
    // 判断是否选择了服务器和流水线
    canExecute() {
      return this.selectedServer && this.selectedPipeline
    },
    
    // 判断是否有正在执行的任务
    isExecuting() {
      return this.activeSessionId !== null
    }
  },
  
  created() {
    this.fetchServerList()
    this.fetchPipelineList()
  },
  
  beforeDestroy() {
    // 清除定时器
    if (this.pollingTimer) {
      clearInterval(this.pollingTimer)
    }
  },
  
  methods: {
    // 获取服务器列表
    async fetchServerList() {
      this.serverLoading = true
      try {
        // 这里应该从后端API获取服务器列表数据
        // 模拟数据
        setTimeout(() => {
          this.serverList = [
            { id: 'server-test-001', servername: 'Docker测试服务器', host: '*************', port: 22, username: 'ubuntu', groupname: 'test', remark: 'Docker功能测试专用服务器' },
            { id: 'server-prod-001', servername: '生产环境服务器1', host: '*************', port: 22, username: 'admin', groupname: 'prod', remark: '生产环境应用服务器' },
            { id: 'server-prod-002', servername: '生产环境服务器2', host: '*************', port: 22, username: 'admin', groupname: 'prod', remark: '生产环境数据库服务器' }
          ]
          this.serverLoading = false
        }, 500)
      } catch (error) {
        console.error('获取服务器列表失败', error)
        Message.error('获取服务器列表失败: ' + error.message)
        this.serverLoading = false
      }
    },
    
    // 获取流水线列表
    async fetchPipelineList() {
      this.pipelineLoading = true
      try {
        // 这里应该从后端API获取流水线列表数据
        // 模拟数据
        setTimeout(() => {
          this.pipelineList = [
            { id: 'pipeline-docker-check-001', pipelinename: 'Docker检查与HelloWorld测试', description: '检查Docker是否安装并运行HelloWorld容器', category: '系统检查', issystem: 0 },
            { id: 'pipeline-sys-001', pipelinename: '系统状态检查', description: '检查系统运行状态、内存使用、磁盘空间等', category: '系统检查', issystem: 1 },
            { id: 'pipeline-app-001', pipelinename: '应用更新部署', description: '部署新版本应用', category: '应用管理', issystem: 0 }
          ]
          this.pipelineLoading = false
        }, 500)
      } catch (error) {
        console.error('获取流水线列表失败', error)
        Message.error('获取流水线列表失败: ' + error.message)
        this.pipelineLoading = false
      }
    },
    
    // 选择服务器
    selectServer(server) {
      this.selectedServer = server
      this.testServerConnection(server.id)
    },
    
    // 选择流水线
    selectPipeline(pipeline) {
      this.selectedPipeline = pipeline
    },
    
    // 测试服务器连接
    async testServerConnection(serverId) {
      try {
        this.$set(this.serverConnectionStatus, serverId, 'testing')
        const response = await sshExecutorApi.testConnection(serverId)
        if (response.code === 200 && response.data) {
          this.$set(this.serverConnectionStatus, serverId, 'success')
          Message.success('服务器连接测试成功')
        } else {
          this.$set(this.serverConnectionStatus, serverId, 'fail')
          Message.warning('服务器连接测试失败: ' + response.msg)
        }
      } catch (error) {
        console.error('服务器连接测试失败', error)
        this.$set(this.serverConnectionStatus, serverId, 'fail')
        Message.error('服务器连接测试失败: ' + error.message)
      }
    },
    
    // 执行流水线
    async executePipeline() {
      if (!this.canExecute) {
        Message.warning('请选择服务器和流水线')
        return
      }
      
      try {
        this.executionLoading = true
        const response = await sshExecutorApi.executePipeline(
          this.selectedPipeline.id, 
          this.selectedServer.id
        )
        
        if (response.code === 200) {
          this.currentExecution = response.data
          this.executionResults.unshift(this.currentExecution)
          Message.success('流水线执行成功')
        } else {
          Message.error('流水线执行失败: ' + response.msg)
        }
      } catch (error) {
        console.error('流水线执行失败', error)
        Message.error('流水线执行失败: ' + error.message)
      } finally {
        this.executionLoading = false
      }
    },
    
    // 异步执行流水线
    async executeAsyncPipeline() {
      if (!this.canExecute) {
        Message.warning('请选择服务器和流水线')
        return
      }
      
      try {
        this.executionLoading = true
        const response = await sshExecutorApi.executeAsyncPipeline(
          this.selectedPipeline.id, 
          this.selectedServer.id
        )
        
        if (response.code === 200) {
          this.activeSessionId = response.data
          Message.success('异步执行任务已提交，会话ID: ' + this.activeSessionId)
          
          // 开始轮询获取执行状态
          this.startPollingExecutionStatus()
        } else {
          Message.error('提交异步执行任务失败: ' + response.msg)
        }
      } catch (error) {
        console.error('提交异步执行任务失败', error)
        Message.error('提交异步执行任务失败: ' + error.message)
      } finally {
        this.executionLoading = false
      }
    },
    
    // 开始轮询执行状态
    startPollingExecutionStatus() {
      // 清除之前的定时器
      if (this.pollingTimer) {
        clearInterval(this.pollingTimer)
      }
      
      // 创建新的定时器，每5秒查询一次
      this.pollingTimer = setInterval(async () => {
        if (!this.activeSessionId) {
          clearInterval(this.pollingTimer)
          return
        }
        
        try {
          const response = await sshExecutorApi.getExecutionStatus(this.activeSessionId)
          
          if (response.code === 200) {
            this.currentExecution = response.data
            
            // 如果执行完成，停止轮询
            if (this.currentExecution.status !== 'Running') {
              this.executionResults.unshift(this.currentExecution)
              this.activeSessionId = null
              clearInterval(this.pollingTimer)
              
              if (this.currentExecution.status === 'Success') {
                Message.success('流水线执行成功')
              } else if (this.currentExecution.status === 'Failed') {
                Message.error('流水线执行失败')
              } else if (this.currentExecution.status === 'Cancelled') {
                Message.warning('流水线执行已取消')
              }
            }
          } else {
            console.error('获取执行状态失败', response.msg)
          }
        } catch (error) {
          console.error('获取执行状态失败', error)
        }
      }, 5000)
    },
    
    // 取消执行
    async cancelExecution() {
      if (!this.activeSessionId) {
        return
      }
      
      try {
        const response = await sshExecutorApi.cancelExecution(this.activeSessionId)
        
        if (response.code === 200 && response.data) {
          Message.success('取消执行成功')
        } else {
          Message.warning('取消执行失败: ' + response.msg)
        }
      } catch (error) {
        console.error('取消执行失败', error)
        Message.error('取消执行失败: ' + error.message)
      }
    },
    
    // 批量执行流水线
    async executePipelineMultiple() {
      if (!this.selectedPipeline) {
        Message.warning('请选择流水线')
        return
      }
      
      // 选择多个服务器
      this.$prompt('请输入服务器ID列表，多个ID用逗号分隔', '批量执行', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^[a-zA-Z0-9,-]+$/,
        inputErrorMessage: '服务器ID格式不正确'
      }).then(async ({ value }) => {
        const serverIds = value.split(',')
        
        try {
          this.executionLoading = true
          const response = await sshExecutorApi.executePipelineMultiple(
            this.selectedPipeline.id,
            serverIds
          )
          
          if (response.code === 200) {
            Message.success('批量执行成功')
            // 添加到执行结果列表
            response.data.forEach(result => {
              this.executionResults.unshift(result)
            })
          } else {
            Message.error('批量执行失败: ' + response.msg)
          }
        } catch (error) {
          console.error('批量执行失败', error)
          Message.error('批量执行失败: ' + error.message)
        } finally {
          this.executionLoading = false
        }
      }).catch(() => {})
    },
    
    // 快速关机
    async quickShutdown() {
      if (!this.selectedServer) {
        Message.warning('请选择服务器')
        return
      }
      
      this.$confirm('确定要关闭服务器 ' + this.selectedServer.servername + ' 吗?', '确认关机', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          this.executionLoading = true
          const response = await sshExecutorApi.quickShutdown(this.selectedServer.id)
          
          if (response.code === 200) {
            this.currentExecution = response.data
            this.executionResults.unshift(this.currentExecution)
            Message.success('关机命令已发送')
          } else {
            Message.error('发送关机命令失败: ' + response.msg)
          }
        } catch (error) {
          console.error('发送关机命令失败', error)
          Message.error('发送关机命令失败: ' + error.message)
        } finally {
          this.executionLoading = false
        }
      }).catch(() => {})
    },
    
    // 快速重启
    async quickRestart() {
      if (!this.selectedServer) {
        Message.warning('请选择服务器')
        return
      }
      
      this.$confirm('确定要重启服务器 ' + this.selectedServer.servername + ' 吗?', '确认重启', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          this.executionLoading = true
          const response = await sshExecutorApi.quickRestart(this.selectedServer.id)
          
          if (response.code === 200) {
            this.currentExecution = response.data
            this.executionResults.unshift(this.currentExecution)
            Message.success('重启命令已发送')
          } else {
            Message.error('发送重启命令失败: ' + response.msg)
          }
        } catch (error) {
          console.error('发送重启命令失败', error)
          Message.error('发送重启命令失败: ' + error.message)
        } finally {
          this.executionLoading = false
        }
      }).catch(() => {})
    },
    
    // 打开命令执行弹窗
    openCommandModal() {
      if (!this.selectedServer) {
        Message.warning('请先选择服务器')
        return
      }
      
      this.commandForm.serverId = this.selectedServer.id
      this.showCommandModal = true
    },
    
    // 执行单条命令
    async executeCommand() {
      if (!this.commandForm.command) {
        Message.warning('请输入要执行的命令')
        return
      }
      
      try {
        const loadingInstance = Loading.service({ fullscreen: true, text: '命令执行中...' })
        
        const response = await sshExecutorApi.executeCommand(this.commandForm)
        
        if (response.code === 200) {
          this.commandResult = response.data
          Message.success('命令执行成功')
        } else {
          Message.error('命令执行失败: ' + response.msg)
        }
        
        loadingInstance.close()
      } catch (error) {
        console.error('命令执行失败', error)
        Message.error('命令执行失败: ' + error.message)
        loadingInstance.close()
      }
    },
    
    // 查看执行详情
    viewExecutionDetail(execution) {
      this.historyDetail = execution
      this.showExecutionDetailModal = true
    },
    
    // 获取状态样式
    getStatusClass(status) {
      switch (status) {
        case 'Success':
          return 'status-success'
        case 'Running':
          return 'status-running'
        case 'Failed':
          return 'status-failed'
        case 'Cancelled':
          return 'status-warning'
        default:
          return ''
      }
    },
    
    // 格式化日期
    formatDate(date) {
      if (!date) return ''
      const d = new Date(date)
      return `${d.getFullYear()}-${(d.getMonth() + 1).toString().padStart(2, '0')}-${d.getDate().toString().padStart(2, '0')} ${d.getHours().toString().padStart(2, '0')}:${d.getMinutes().toString().padStart(2, '0')}:${d.getSeconds().toString().padStart(2, '0')}`
    },
    
    // 清空执行结果
    clearExecutionResults() {
      this.executionResults = []
      this.currentExecution = null
    }
  }
}
