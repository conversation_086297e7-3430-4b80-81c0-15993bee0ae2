<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业微信部门与成员</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.3/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap-icons/1.11.3/font/bootstrap-icons.min.css" rel="stylesheet">
    <style>
        /* 引入简洁字体 */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        :root {
            --bg-color: #f8f9fa;
            --card-bg: #ffffff;
            --text-primary: #212529;
            --text-secondary: #6c757d;
            --border-color: #dee2e6;
            --accent-color: #0d6efd;
            --hover-bg: #f1f3f5;
            --radius: 8px;
            --transition: all 0.2s ease;
        }

        body {
            background-color: var(--bg-color);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            -webkit-font-smoothing: antialiased;
            color: var(--text-primary);
            margin: 0;
            padding: 0;
        }

        /* --- 顶部导航 --- */
        .top-nav {
            background-color: var(--card-bg);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            padding: 1rem 0;
            margin-bottom: 1.5rem;
        }

        .top-nav h1 {
            color: var(--text-primary);
            font-weight: 600;
            font-size: 1.25rem;
            margin: 0;
        }

        /* --- 左侧边栏 --- */
        .sidebar {
            height: calc(100vh - 100px);
            background: var(--card-bg);
            border-radius: var(--radius);
            padding: 1.25rem;
            overflow-y: auto;
            border: 1px solid var(--border-color);
        }
        
        .sidebar-header {
            color: var(--text-primary);
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid var(--border-color);
            font-size: 1rem;
        }

        .dept-search {
            margin-bottom: 1.25rem;
        }

        .dept-search .form-control {
            background-color: var(--bg-color);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-primary);
            padding: 0.5rem 0.75rem;
            font-size: 0.9rem;
        }

        .dept-search .form-control:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.15);
        }

        .list-group-item {
            border: none;
            padding: 0.75rem 1rem;
            margin-bottom: 0.25rem;
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: 400;
            color: var(--text-primary);
            transition: var(--transition);
            background-color: transparent;
        }
        
        .list-group-item:hover {
            background-color: var(--hover-bg);
        }
        
        .list-group-item.active {
            background-color: rgba(13, 110, 253, 0.1);
            color: var(--accent-color);
            font-weight: 500;
        }
        
        /* 激活状态的左侧竖条指示器 */
        .list-group-item.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 3px;
            background-color: var(--accent-color);
        }

        /* --- 右侧主内容区 --- */
        main {
            padding: 0 1.25rem 1.25rem;
        }
        
        .main-header {
            font-weight: 600;
            color: var(--text-primary);
            padding-bottom: 1rem;
            margin-bottom: 1.25rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 1.1rem;
        }
        
        .member-search {
            max-width: 250px;
        }

        .member-search .form-control {
            background-color: var(--bg-color);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-primary);
            padding: 0.5rem 0.75rem;
            font-size: 0.9rem;
        }

        .member-search .form-control:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.15);
        }

        /* --- 成员卡片 --- */
        .member-card {
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            background: var(--card-bg);
            transition: var(--transition);
            height: 100%;
        }
        
        .member-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }

        /* 卡片内布局 */
        .card-body {
            padding: 1.25rem;
        }
        
        .avatar-placeholder {
            min-width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--bg-color);
            color: var(--text-secondary);
            display: grid;
            place-items: center;
            font-size: 1rem;
            margin-right: 0.75rem;
        }
        
        .member-info {
            flex: 1;
        }

        .member-info .name {
            font-size: 0.95rem;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 0.1rem;
        }
        
        .member-info .title {
            font-size: 0.85rem;
            font-weight: 400;
            color: var(--text-secondary);
            margin-bottom: 0.25rem;
        }
        
        .member-info .userid {
            font-size: 0.75rem;
            color: var(--text-secondary);
            background-color: var(--bg-color);
            padding: 1px 6px;
            border-radius: 4px;
            display: inline-block;
        }

        /* 加载动画 */
        .spinner-container {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            min-height: 300px;
        }
        
        .spinner-border {
            width: 2rem;
            height: 2rem;
            color: var(--accent-color);
        }

        /* 卡片加载动画 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .fade-in {
            animation: fadeIn 0.3s ease-out forwards;
        }

        /* 响应式调整 */
        @media (max-width: 767.98px) {
            .sidebar {
                height: auto;
                margin-bottom: 1.25rem;
            }
            
            main {
                padding: 0 1rem 1rem;
            }
            
            .top-nav {
                margin-bottom: 1rem;
            }
            
            .main-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .member-search {
                max-width: 100%;
                margin-top: 1rem;
                width: 100%;
            }
        }
        
        /* Empty state */
        .empty-state {
            text-align: center;
            padding: 2rem 1rem;
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 2rem;
            margin-bottom: 0.75rem;
            color: var(--border-color);
        }
    </style>
</head>
<body>
<div class="container-fluid">
    <div class="top-nav">
        <div class="container-fluid">
            <h1><i class="bi bi-people me-2"></i>企业微信部门与成员</h1>
        </div>
    </div>
    
    <div class="row">
        <nav class="col-md-4 col-lg-3 sidebar">
            <h5 class="sidebar-header"><i class="bi bi-diagram-3 me-2"></i>组织架构</h5>
            
            <div class="dept-search">
                <div class="input-group">
                    <span class="input-group-text bg-transparent border-0 p-0">
                        <i class="bi bi-search text-muted"></i>
                    </span>
                    <input type="text" class="form-control" id="deptSearchInput" placeholder="搜索部门...">
                </div>
            </div>
            
            <div id="deptList" class="list-group list-group-flush"></div>
        </nav>

        <main class="col-md-8 col-lg-9">
            <div class="main-header">
                <h5><i class="bi bi-people-fill me-2"></i>部门成员</h5>
                <div class="member-search">
                    <div class="input-group">
                        <span class="input-group-text bg-transparent border-0 p-0">
                            <i class="bi bi-search text-muted"></i>
                        </span>
                        <input type="text" class="form-control" id="memberSearchInput" placeholder="搜索成员...">
                    </div>
                </div>
            </div>

            <div id="loading" class="spinner-container" style="display:none">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
            </div>

            <div id="memberGrid" class="row g-3"></div>
        </main>
    </div>
</div>

<script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.3/js/bootstrap.bundle.min.js"></script>
<script>
    const API_BASE = 'http://*************:10684/S34M06R1';
    // const API_BASE = 'http://dev.inksyun.com:31080/utils/S34M06R1';
    const AUTH_HEADER = 'admin';
    
    // 缓存部门和成员数据
    let departments = [];
    let members = [];

    // 获取并渲染部门列表
    async function loadDepartments() {
        try {
            const res = await fetch(`${API_BASE}/getDepartmentList`, {
                headers: { 'Authorization': AUTH_HEADER }
            });
            const json = await res.json();
            if (json.code === 200) {
                departments = json.data;
                renderDepartments(departments);
                
                // 默认选中并加载第一个部门
                if (departments.length > 0) {
                    const firstDeptItem = document.querySelector('#deptList .list-group-item');
                    if (firstDeptItem) {
                        firstDeptItem.classList.add('active');
                        loadMembers(departments[0].id);
                    }
                }
            }
        } catch (e) {
            console.error('加载部门列表失败', e);
            document.getElementById('deptList').innerHTML = `<div class="list-group-item text-danger">部门加载失败</div>`;
        }
    }
    
    // 渲染部门列表
    function renderDepartments(depts) {
        const list = document.getElementById('deptList');
        list.innerHTML = '';
        
        depts.forEach((dept, idx) => {
            const li = document.createElement('a');
            li.className = 'list-group-item list-group-item-action';
            li.href = '#';
            li.innerHTML = `<i class="bi bi-building me-2"></i> ${dept.name}`;
            li.dataset.id = dept.id;

            li.addEventListener('click', (e) => {
                e.preventDefault();
                // 移除所有兄弟元素的 active 类
                list.querySelectorAll('.list-group-item').forEach(item => item.classList.remove('active'));
                // 为当前点击项添加 active 类
                li.classList.add('active');
                loadMembers(dept.id);
            });

            list.appendChild(li);
        });
    }

    // 获取并渲染成员卡片
    async function loadMembers(deptId) {
        const grid = document.getElementById('memberGrid');
        const loading = document.getElementById('loading');
        grid.innerHTML = '';
        loading.style.display = 'flex';

        try {
            const res = await fetch(`${API_BASE}/getDepartmentMembers?departmentId=${deptId}`, {
                headers: { 'Authorization': AUTH_HEADER }
            });
            const json = await res.json();

            if (json.code === 200 && json.data && json.data.length > 0) {
                members = json.data;
                renderMembers(members);
            } else {
                grid.innerHTML = `
                    <div class="col-12">
                        <div class="empty-state">
                            <i class="bi bi-person-x"></i>
                            <h5>暂无成员数据</h5>
                            <p class="mb-0">该部门暂无成员信息</p>
                        </div>
                    </div>
                `;
            }
        } catch (e) {
            grid.innerHTML = `
                <div class="col-12">
                    <div class="empty-state text-danger">
                        <i class="bi bi-wifi-off"></i>
                        <h5>加载成员失败</h5>
                        <p class="mb-0">请检查网络连接或服务状态</p>
                    </div>
                </div>
            `;
            console.error('加载成员失败', e);
        } finally {
            loading.style.display = 'none';
        }
    }
    
    // 渲染成员列表
    function renderMembers(users) {
        const grid = document.getElementById('memberGrid');
        grid.innerHTML = '';
        
        users.forEach((user, index) => {
            const col = document.createElement('div');
            col.className = 'col-xl-3 col-lg-4 col-md-6 col-12 fade-in member-col';
            col.style.animationDelay = `${index * 0.05}s`;
            
            col.innerHTML = `
                <div class="card member-card h-100">
                    <div class="card-body d-flex align-items-center">
                        <div class="avatar-placeholder">
                            <i class="bi bi-person-fill"></i>
                        </div>
                        <div class="member-info">
                            <h6 class="name mb-0 text-truncate">${user.name}</h6>
                            <p class="title mb-0">${user.title || '普通成员'}</p>
                            <span class="userid">ID: ${user.userid}</span>
                        </div>
                    </div>
                </div>
            `;
            grid.appendChild(col);
        });
    }
    
    // 部门搜索功能
    function setupDepartmentSearch() {
        const searchInput = document.getElementById('deptSearchInput');
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const filteredDepts = departments.filter(dept => 
                dept.name.toLowerCase().includes(searchTerm)
            );
            renderDepartments(filteredDepts);
        });
    }
    
    // 成员搜索功能
    function setupMemberSearch() {
        const searchInput = document.getElementById('memberSearchInput');
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const filteredMembers = members.filter(member => 
                member.name.toLowerCase().includes(searchTerm) ||
                (member.title && member.title.toLowerCase().includes(searchTerm)) ||
                member.userid.toLowerCase().includes(searchTerm)
            );
            renderMembers(filteredMembers);
        });
    }

    // 页面初始化
    document.addEventListener('DOMContentLoaded', () => {
        loadDepartments();
        setupDepartmentSearch();
        setupMemberSearch();
    });
</script>
</body>
</html>