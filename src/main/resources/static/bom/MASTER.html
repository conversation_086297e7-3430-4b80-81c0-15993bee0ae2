<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Cost Breakdown Summary</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: auto;
            background: #fff;
            padding: 20px 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 8px;
        }
        h1 {
            text-align: center;
            color: #005a9e;
            border-bottom: 2px solid #005a9e;
            padding-bottom: 10px;
        }
        h2 {
            background-color: #eaf2f8;
            color: #34495e;
            padding: 12px;
            border-left: 5px solid #3498db;
            margin-top: 30px;
            border-radius: 4px;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .data-table th, .data-table td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: left;
        }
        .data-table th {
            background-color: #f2f2f2;
            font-weight: bold;
            width: 40%;
        }
        .data-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .value {
            font-weight: 500;
            color: #2c3e50;
        }
    </style>
</head>
<body>

<div class="container">
    <h1>产品成本分解总览 (Product Cost Breakdown Summary)</h1>

    <div id="report-content"></div>

</div>

<script>
    const headers_str = "GENERAL_PRODUCT_NAME	GENERAL_PRODUCT_BRAND	GENERAL_PRODUCT_DPP	GENERAL_PRODUCT_SUPPLIER	GENERAL_PRODUCT_COUNTRY	GENERAL_PRODUCT_QTY_Y1	GENERAL_CBD_VERSION_TEMPLATE	GENERAL_CBD_VERSION_TRANSFER	GENERAL_CBD_CURRENT_DATE	GENERAL_CBD_CURRENT_VERSION	GENERAL_CBD_CURRENT_EXW	GENERAL_CBD_CURRENT_COMMENT	GENERAL_CBD_V-1_DATE	GENERAL_CBD_V-1_VERSION	GENERAL_CBD_V-1_EXW	GENERAL_CBD_V-1_COMMENT	GENERAL_PRODUCT_QTY_Y2	GENERAL_PRODUCT_QTY_Y3	GENERAL_PRODUCT_MOQ	GENERAL_CURRENCY_MAIN_NAME	GENERAL_CURRENCY_1_NAME	GENERAL_CURRENCY_1_VALUE	GENERAL_CURRENCY_2_NAME	GENERAL_CURRENCY_2_VALUE	GENERAL_PRICE_EXPORT/LOCAL	GENERAL_PRICE_LOCAL-TRANSPORT	PLASTIC_SUBTOTAL	PLASTIC_PARTS_NB	PLASTIC_LABORCOST_TOTAL	PLASTIC_MOLD_NB	PLASTIC_MOLD_TOTALCOST	PLASTIC_MOLD_MAXCOST_PARTNAME	PLASTIC_MOLD_MAXCOST_VALUE	PLASTIC_MOLD_MAXCOST_CAVITIES	PLASTIC_MAXPART1_NAME	PLASTIC_MAXPART1_UNITPRICE	PLASTIC_MAXPART1_QTY	PLASTIC_MAXPART1_MATERIAL	PLASTIC_MAXPART1_MATERIAL_PRICE-KG	PLASTIC_MAXPART1_CYCLETIME	PLASTIC_MAXPART1_MOLD-CAVITIES	PLASTIC_MAXPART1_PRESS_SIZE	PLASTIC_MAXPART1_PRESS_COSTH	PLASTIC_MAXPART1_PPH	PLASTIC_MAXPART1_LABORCOSTH	PLASTIC_MAXPART1_SCRAPRATE	PLASTIC_MAXPART1_CALCUL-METHOD	PLASTIC_MAXPART1_MOLD-USAGE	PLASTIC_MAXPART2_NAME	PLASTIC_MAXPART2_UNITPRICE	PLASTIC_MAXPART2_QTY	PLASTIC_MAXPART2_MATERIAL	PLASTIC_MAXPART2_MATERIAL_PRICE-KG	PLASTIC_MAXPART2_CYCLETIME	PLASTIC_MAXPART2_MOLD-CAVITIES	PLASTIC_MAXPART2_PRESS_SIZE	PLASTIC_MAXPART2_PRESS_COST	PLASTIC_MAXPART2_PPH	PLASTIC_MAXPART2_LABORCOSTH	PLASTIC_MAXPART2_SCRAPRATE	PLASTIC_MAXPART2_CALCUL-METHOD	PLASTIC_MAXPART2_MOLD-USAGE	PLASTIC_MAXPART3_NAME	PLASTIC_MAXPART3_UNITPRICE	PLASTIC_MAXPART3_QTY	PLASTIC_MAXPART3_MATERIAL	PLASTIC_MAXPART3_MATERIAL_PRICE-KG	PLASTIC_MAXPART3_CYCLETIME	PLASTIC_MAXPART3_MOLD-CAVITIES	PLASTIC_MAXPART3_PRESS_SIZE	PLASTIC_MAXPART3_PRESS_COST	PLASTIC_MAXPART3_PPH	PLASTIC_MAXPART3_LABORCOSTH	PLASTIC_MAXPART3_SCRAPRATE	PLASTIC_MAXPART3_CALCUL-METHOD	PLASTIC_MAXPART3_MOLD-USAGE	METAL_SUBTOTAL	METAL_PARTS_NB	ELECTRO_SUBTOTAL	ELECTRO_PARTS_NB	ELECTRO_TOTAL_LED_VALUE	ELECTRO_TOTAL_LED_QTY	ELECTRO_TOTAL_BATTERY_VALUE	ELECTRO_TOTAL_BATTERY_QTY	ELECTRO_TOTAL_CABLE_VALUE	ELECTRO_TOTAL_CABLE_QTY	ELECTRO_TOTAL_IC_VALUE	ELECTRO_TOTAL_IC_QTY	ELECTRO_MAXPART1_NAME	ELECTRO_MAXPART1_BRAND	ELECTRO_MAXPART1_REFERENCE	ELECTRO_MAXPART1_TYPOLOGY	ELECTRO_MAXPART1_ORIGINE-COUNTRY	ELECTRO_MAXPART1_COST_UNIT	ELECTRO_MAXPART1_COST_CURRENCY	ELECTRO_MAXPART1_COST_TOTAL	ELECTRO_MAXPART1_QTY	ELECTRO_MAXPART2_NAME	ELECTRO_MAXPART2_BRAND	ELECTRO_MAXPART2_REFERENCE	ELECTRO_MAXPART2_TYPOLOGY	ELECTRO_MAXPART2_ORIGINE-COUNTRY	ELECTRO_MAXPART2_COST_UNIT	ELECTRO_MAXPART2_COST_CURRENCY	ELECTRO_MAXPART2_COST_TOTAL	ELECTRO_MAXPART2_QTY	ELECTRO_MAXPART3_NAME	ELECTRO_MAXPART3_BRAND	ELECTRO_MAXPART3_REFERENCE	ELECTRO_MAXPART3_TYPOLOGY	ELECTRO_MAXPART3_ORIGINE-COUNTRY	ELECTRO_MAXPART3_COST_UNIT	ELECTRO_MAXPART3_COST_CURRENCY	ELECTRO_MAXPART3_COST_TOTAL	ELECTRO_MAXPART3_QTY	ELECTRO_MAX_BATTERY_NAME	ELECTRO_MAX_BATTERY_BRAND	ELECTRO_MAX_BATTERY_REFERENCE	ELECTRO_MAX_BATTERY_TYPOLOGY	ELECTRO_MAX_BATTERY_ORIGINE-COUNTRY	ELECTRO_MAX_BATTERY_COST_UNIT	ELECTRO_MAX_BATTERY_COST_CURRENCY	ELECTRO_MAX_BATTERY_COST_TOTAL	ELECTRO_MAX_BATTERY_QTY	ELECTRO_MAX_USBCABLE_NAME	ELECTRO_MAX_USBCABLE_BRAND	ELECTRO_MAX_USBCABLE_REFERENCE	ELECTRO_MAX_USBCABLE_TYPOLOGY	ELECTRO_MAX_USBCABLE_ORIGINE-COUNTRY	ELECTRO_MAX_USBCABLE_COST_UNIT	ELECTRO_MAX_USBCABLE_COST_CURRENCY	ELECTRO_MAX_USBCABLE_COST_TOTAL	ELECTRO_MAX_USBCABLE_QTY	ELECTRO_MAX_LED_NAME	ELECTRO_MAX_LED_BRAND	ELECTRO_MAX_LED_REFERENCE	ELECTRO_MAX_LED_TYPOLOGY	ELECTRO_MAX_LED_ORIGINE-COUNTRY	ELECTRO_MAX_LED_COST_UNIT	ELECTRO_MAX_LED_COST_CURRENCY	ELECTRO_MAX_LED_COST_TOTAL	ELECTRO_MAX_LED_QTY	ELECTRO_MAX_IC_NAME	ELECTRO_MAX_IC_BRAND	ELECTRO_MAX_IC_REFERENCE	ELECTRO_MAX_IC_TYPOLOGY	ELECTRO_MAX_IC_ORIGINE-COUNTRY	ELECTRO_MAX_IC_COST_UNIT	ELECTRO_MAX_IC_COST_CURRENCY	ELECTRO_MAX_IC_COST_TOTAL	ELECTRO_MAX_IC_QTY	ACCESSORIES_SUBTOTAL	ACCESSORIES_PARTS_NB	PACKAGING_SUBTOTAL	PACKAGING_PRICE_RFID	PACKAGING_PRICE_PCB-EXPORT	PROCESSING_SUBTOTAL	PROCESSING_SMT_NB-COMPO	PROCESSING_SMT_MACHINE-COST/PART	PROCESSING_SMT_TOTAL	PROCESSING_MANUAL-ASSEMBLY_PRODUCT_NB-OPERATOR	PROCESSING_MANUAL-ASSEMBLY_PRODUCT_CYCLE-TIME	PROCESSING_MANUAL-ASSEMBLY_PRODUCT_LABORCOSTH	PROCESSING_MANUAL-ASSEMBLY_PRODUCT_PPH	PROCESSING_MANUAL-ASSEMBLY_PRODUCT_TOTAL	PROCESSING_MANUAL-ASSEMBLY_PACK_NB-OPERATOR	PROCESSING_MANUAL-ASSEMBLY_PACK_CYCLE-TIME	PROCESSING_MANUAL-ASSEMBLY_PACK_LABORCOSTH	PROCESSING_MANUAL-ASSEMBLY_PACK_PPH	PROCESSING_MANUAL-ASSEMBLY_PACK_TOTAL	FOM_SUBTOTAL	FOM_FINANCE_RATE	FOM_FINANCE_COST	FOM_OVERHEAD_RATE	FOM_OVERHEAD_COST	FOM_MARGIN_RATE	FOM_MARGIN_COST	INVEST_AMORTIZE_PROD-TOOL	INVEST_AMORTIZE_TEST-EQUIP	INVEST_AMORTIZE_PLASTIC-MOLD	INVEST_AMORTIZE_OTHER-MOLD	INVEST_BRAND_DEV-COST	INVEST_BRAND_PROTO&SAMPLES	INVEST_BRAND_CERTIF	PRICING_BOM-COST_TOTAL	PRICING_BOM-COST_WITHOUT-PLASTICLABOR	PRICING_PROCESSING	PRICING_TOTAL-EXW		PLASTIC_PARTS_NB_TOTAL	PLASTIC_MOLD_MAXLEADTIME	PLASTIC_MOLD_LIFETIMEBOTTLENECK_IN_FG	PLASTIC_MAX_ABS_COSTPERKG	PLASTIC_ABS_COST	PLASTIC_MAX_PC_COSTPERKG	PLASTIC_PC_COST	PLASTIC_MAX_PP_COSTPERKG	PLASTIC_PP_COST	PLASTIC_MAX_TPE_COSTPERKG	PLASTIC_TPE_COST	PLASTIC_MAX_SILICONE_COSTPERKG	PLASTIC_SILICONE_COST	PLASTIC_MAX_PMMA_COSTPERKG	PLASTIC_PMMA_COST	PLASTIC_MAX_ABS-PC_COSTPERKG	PLASTIC_ABSPC_COST	PLASTIC_MAX_POM_COSTPERKG	PLASTIC_POM_COST	METAL_PARTS_NB_TOTAL	PROCESSING_MANUAL-ASSEMBLY_PRODUCT_SECONDS	PROCESSING_MANUAL-ASSEMBLY_PRODUCT_LABOR	PROCESSING_MANUAL-ASSEMBLY_PACKING_SECONDS	PROCESSING_MANUAL-PACKING_PRODUCT_LABOR	PROCESSING_LOGO-PRINTING_SECONDS	PROCESSING_LOGO-PRINTING_TOTAL	PROCESSING_LASER-MARKING_SECONDS	PROCESSING_LASER-MARKING_TOTAL	FOM_DEVCOSTAMOTOTAL	METAL_PART_TOTAL_HEATSINK_COST";
    const values_str = "0	0		0	0	0	v2.0.2	V2.0.2	1900/1/0	0	to add	to add	to add	to add	to add	to add	0	to add	0	CNY	0	0.000	0	0.000	0	to add	0.000	0	0.000	0	0		0.000														MANUAL														MANUAL														MANUAL		0.359	3	169.887	0	0.000	0	82.712	0	0.000	0	0.000	0	保护板	0	IPA100-V7主控板 SH367309U+HYG025N06LS1C2+ 蝌蚪NTC10K 刷三防漆	PCB	China	44.707	CNY	44.707	1	保护板	0	IPA100-V7灯板	PCB	China	30.470	CNY	30.470	1	圆柱电芯	0	天鹏INR18650-25PG 3.6V 	Battery - Li-Ion	China	8.271	CNY	82.712	10	圆柱电芯	0	天鹏INR18650-25PG 3.6V 	Battery - Li-Ion	China	8.271	CNY	82.712	10																												1.217	2	0.000	0.000	0.000	0.000	0	0.000		0		0.000		0.000	0		0.000		0.000	0.000	0.00%		0.00%		0.00%		0.000	0.000	0.000	0.000	0.000	0.000	0.000	171.463	171.463	0.000	171.463		0	0		0.00	0.00	0.00	0.00	0.00	0.00	0.00	0.00	0.00	0.00	0.00	0.00	0.00	0.00	0.00	0.00	9	0	0	0	0	0	0	0	0	0	0 ";

    const headers = headers_str.split('	');
    const values = values_str.split('	');

    const data = {};
    headers.forEach((header, index) => {
        const category = header.split('_')[0];
        if (!data[category]) {
            data[category] = [];
        }
        if (values[index] && values[index].trim() !== '0' && values[index].trim() !== '0.000' && values[index].trim() !== '' && values[index].trim() !== 'MANUAL' && values[index].trim() !== 'to add' && values[index].trim() !== '0.00%') {
            data[category].push({ header: header.replace(category + '_', '').replace(/_/g, ' '), value: values[index] });
        }
    });

    const categoryTitles = {
        GENERAL: "通用信息 (General Information)",
        PRICING: "定价信息 (Pricing Information)",
        METAL: "金属成本 (Metal Costs)",
        ELECTRO: "电子成本 (Electronics Costs)",
        ACCESSORIES: "配件成本 (Accessories Costs)",
        PLASTIC: "塑料成本 (Plastic Costs)",
        PACKAGING: "包装成本 (Packaging Costs)",
        PROCESSING: "加工成本 (Processing Costs)",
        FOM: "财务/管理/利润 (Finance/Overhead/Margin)",
        INVEST: "投资成本 (Investment Costs)"
    };

    let htmlContent = '';
    for (const category in categoryTitles) {
        if (data[category] && data[category].length > 0) {
            htmlContent += `<h2>${categoryTitles[category]}</h2>`;
            htmlContent += '<table class="data-table">';
            data[category].forEach(item => {
                htmlContent += `<tr><th>${item.header}</th><td class="value">${item.value}</td></tr>`;
            });
            htmlContent += '</table>';
        }
    }

    document.getElementById('report-content').innerHTML = htmlContent;
</script>

</body>
</html>