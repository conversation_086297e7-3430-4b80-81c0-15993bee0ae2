<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pricing Summary UI</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f0f2f5;
            color: #333;
            padding: 20px;
        }
        .container {
            background-color: #fff;
            border: 1px solid #ccc;
            padding: 15px;
            max-width: 1200px;
            margin: auto;
        }
        .decathlon-logo {
            font-size: 50px;
            font-weight: 900;
            color: #003087;
            text-align: left;
            margin-bottom: 20px;
        }
        .main-content {
            display: flex;
            gap: 20px;
            align-items: flex-start;
        }
        .price-table-container {
            flex: 1;
            min-width: 400px;
        }
        .charts-container {
            flex: 2;
            display: flex;
            justify-content: space-around;
            gap: 20px;
        }
        .chart-wrapper {
            text-align: center;
        }
        .chart-wrapper h3 {
            font-weight: bold;
            font-size: 16px;
        }
        .pie-chart {
            width: 250px;
            height: 250px;
            border-radius: 50%;
            position: relative;
        }
        .pie-chart-bom {
            background: conic-gradient(
                    #538dd5 0% 99%,   /* ELECTRONIC 99% */
                    #fcd5b4 99% 99.8%, /* ACCESSORIES 0.8% */
                    #a8d08d 99.8% 100%  /* METAL 0.2% */
            );
        }
        .pie-chart-exw {
            background: conic-gradient(
                    #f4b183 0% 99%,   /* ELECTRONIC 99% */
                    #ffe599 99% 99.8%, /* ACCESSORIES 0.8% */
                    #c5e0b3 99.8% 100%  /* OTHERS 0.2% */
            );
        }
        /* Labels for charts - simplified for demonstration */
        .pie-chart .label {
            position: absolute;
            color: black;
            font-size: 12px;
            font-weight: bold;
        }
        .label.electronic-bom { top: 50%; left: 65%; transform: translate(-50%, -50%); }
        .label.accessories-bom { top: 15%; left: 30%; }
        .label.electronic-exw { top: 50%; left: 65%; transform: translate(-50%, -50%);}
        .label.accessories-exw { top: 15%; left: 30%; }

        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }
        th, td {
            border: 1px solid #b2b2b2;
            padding: 8px;
            text-align: center;
        }
        th {
            background-color: #fde9d9;
            font-weight: bold;
        }
        td.align-left {
            text-align: left;
        }
        .total-row {
            font-weight: bold;
        }
        .sub-total-row {
            background-color: #d9d9d9;
            font-weight: bold;
        }
        .gray-row {
            background-color: #f2f2f2;
        }
        .final-total {
            background-color: #fde9d9;
            font-weight: bold;
            font-size: 16px;
        }
        .tabs {
            margin-top: 15px;
            padding-left: 0;
            list-style: none;
            display: flex;
            border-top: 2px solid #999;
        }
        .tabs li {
            padding: 8px 15px;
            background: #f0f0f0;
            border: 1px solid #ccc;
            border-top: none;
            margin-right: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .tabs li.active-red {
            background-color: #ff0000;
            color: white;
            font-weight: bold;
        }
        .tabs li.active-yellow {
            background-color: #ffff00;
        }
    </style>
</head>
<body>

<div class="container">
    <div class="decathlon-logo">DECATHLON</div>

    <div class="main-content">
        <div class="price-table-container">
            <table>
                <thead>
                <tr>
                    <th colspan="4">EXW PRICE</th>
                </tr>
                <tr>
                    <th>DESIGNATION</th>
                    <th>COST(CNY)</th>
                    <th>% BOM</th>
                    <th>% EXW</th>
                </tr>
                </thead>
                <tbody>
                <tr><td class="align-left">PLASTIC</td><td>0.000</td><td>0%</td><td>0%</td></tr>
                <tr><td class="align-left">METAL</td><td>0.359</td><td>0%</td><td>0%</td></tr>
                <tr><td class="align-left">ELECTRONIC</td><td>169.887</td><td>99%</td><td>99%</td></tr>
                <tr><td class="align-left">ACCESSORIES</td><td>1.217</td><td>1%</td><td>1%</td></tr>
                <tr><td class="align-left">PACKAGING</td><td>0.000</td><td>0%</td><td>0%</td></tr>
                <tr class="sub-total-row"><td class="align-left">BOM</td><td>171.463</td><td>100%</td><td>100%</td></tr>
                <tr><td class="align-left">PROCESSING</td><td>0.000</td><td></td><td>0%</td></tr>
                <tr class="gray-row"><td class="align-left">FINANCIAL</td><td></td><td>0.0%</td><td>0%</td></tr>
                <tr class="gray-row"><td class="align-left">OVERHEAD</td><td></td><td>0.0%</td><td>0%</td></tr>
                <tr class="gray-row"><td class="align-left">MARGIN</td><td></td><td>0.0%</td><td>0%</td></tr>
                <tr><td class="align-left">FOM</td><td>0.00</td><td>0%</td><td>0%</td></tr>
                <tr class="final-total"><td class="align-left">Total EXW</td><td>171.463 CNY</td><td></td><td>100%</td></tr>
                </tbody>
            </table>
        </div>

        <div class="charts-container">
            <div class="chart-wrapper">
                <h3>BOM</h3>
                <div class="pie-chart pie-chart-bom">
                    <div class="label electronic-bom">ELECTRONIC<br>99%</div>
                    <div class="label accessories-bom">ACCESSORIES<br>1%</div>
                </div>
            </div>
            <div class="chart-wrapper">
                <h3>EXW</h3>
                <div class="pie-chart pie-chart-exw">
                    <div class="label electronic-exw">ELECTRONIC<br>99%</div>
                    <div class="label accessories-exw">ACCESSORIES<br>1%</div>
                </div>
            </div>
        </div>
    </div>

    <ul class="tabs">
        <li>GENERAL</li>
        <li>PLASTIC</li>
        <li>METAL</li>
        <li>ELECTRO</li>
        <li>ACCESSORIES</li>
        <li>PACKAGING</li>
        <li class="active-yellow">PROCESSING</li>
        <li>FOH</li>
        <li class="active-yellow">INVEST.</li>
        <li class="active-red">PRICING</li>
        <li>TUTORIAL PACK</li>
        <li>MASTER</li>
    </ul>

</div>

</body>
</html>