<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BOM 查看器</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            overflow: hidden; /* 防止出现双滚动条 */
        }
        .container {
            display: flex;
            height: 100%;
        }
        #sidebar {
            width: 220px;
            background-color: #f8f9fa;
            border-right: 1px solid #dee2e6;
            padding: 20px;
            box-sizing: border-box;
            overflow-y: auto;
        }
        #sidebar h2 {
            margin-top: 0;
            font-size: 1.5rem;
            color: #343a40;
        }
        #sidebar ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        #sidebar ul li a {
            display: block;
            padding: 10px 15px;
            color: #495057;
            text-decoration: none;
            border-radius: 5px;
            transition: background-color 0.2s;
        }
        #sidebar ul li a:hover, #sidebar ul li a.active {
            background-color: #e9ecef;
            color: #007bff;
        }
        #content-frame {
            flex-grow: 1;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <nav id="sidebar">
            <h2>BOM 导航</h2>
            <ul>
                <li><a href="GENERAL.html" target="contentFrame" onclick="setActive(this)">GENERAL</a></li>
                <li><a href="PLASTIC.html" target="contentFrame" onclick="setActive(this)">PLASTIC</a></li>
                <li><a href="METAL.html" target="contentFrame" onclick="setActive(this)">METAL</a></li>
                <li><a href="ELECTRO.html" target="contentFrame" onclick="setActive(this)">ELECTRO</a></li>
                <li><a href="ACCESSORIES.html" target="contentFrame" onclick="setActive(this)">ACCESSORIES</a></li>
                <li><a href="PACKAGING.html" target="contentFrame" onclick="setActive(this)">PACKAGING</a></li>
                <li><a href="PROCESSING.html" target="contentFrame" onclick="setActive(this)">PROCESSING</a></li>
                <li><a href="FOM.html" target="contentFrame" onclick="setActive(this)">FOM</a></li>
                <li><a href="INVEST..html" target="contentFrame" onclick="setActive(this)">INVEST.</a></li>
                <li><a href="PRICING.html" target="contentFrame" onclick="setActive(this)">PRICING</a></li>
                <li><a href="TUTORIAL PACK.html" target="contentFrame" onclick="setActive(this)">TUTORIAL PACK</a></li>
                <li><a href="MASTER.html" target="contentFrame" onclick="setActive(this)">MASTER</a></li>
            </ul>
        </nav>
        <iframe id="content-frame" name="contentFrame" src="GENERAL.html"></iframe>
    </div>

    <script>
        function setActive(element) {
            // 移除所有链接的 active class
            var links = document.querySelectorAll('#sidebar a');
            links.forEach(function(link) {
                link.classList.remove('active');
            });
            // 为当前点击的链接添加 active class
            element.classList.add('active');
        }

        // 页面加载时，为默认页面设置 active 状态
        window.onload = function() {
            var defaultLink = document.querySelector('#sidebar a[href="GENERAL.html"]');
            if (defaultLink) {
                setActive(defaultLink);
            }
        };
    </script>
</body>
</html>
