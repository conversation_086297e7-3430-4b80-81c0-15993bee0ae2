/* SSH Pipeline Management UI Styles */
:root {
  --primary-color: #1890ff;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
  --heading-color: #303133;
  --text-color: #606266;
  --border-color: #e4e7ed;
  --background-color: #f5f7fa;
  --shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* General Layout */
.ssh-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: var(--shadow);
}

.ssh-card {
  margin-bottom: 20px;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  transition: all 0.3s;
}

.ssh-card:hover {
  box-shadow: var(--shadow);
}

.ssh-card-header {
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color);
  background-color: #fafafa;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ssh-card-body {
  padding: 20px;
}

/* Status Indicators */
.status-badge {
  display: inline-block;
  padding: 4px 10px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: bold;
}

.status-success {
  background-color: rgba(82, 196, 26, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(82, 196, 26, 0.2);
}

.status-running {
  background-color: rgba(24, 144, 255, 0.1);
  color: var(--primary-color);
  border: 1px solid rgba(24, 144, 255, 0.2);
  animation: pulse 2s infinite;
}

.status-failed {
  background-color: rgba(245, 34, 45, 0.1);
  color: var(--error-color);
  border: 1px solid rgba(245, 34, 45, 0.2);
}

.status-warning {
  background-color: rgba(250, 173, 20, 0.1);
  color: var(--warning-color);
  border: 1px solid rgba(250, 173, 20, 0.2);
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

/* Server Card */
.server-card {
  display: flex;
  padding: 16px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  margin-bottom: 12px;
  transition: all 0.3s;
  cursor: pointer;
}

.server-card:hover {
  box-shadow: var(--shadow);
  border-color: var(--primary-color);
}

.server-card-selected {
  border-color: var(--primary-color);
  background-color: rgba(24, 144, 255, 0.05);
}

.server-icon {
  margin-right: 16px;
  color: var(--primary-color);
  font-size: 36px;
}

.server-content {
  flex: 1;
}

.server-name {
  font-size: 16px;
  font-weight: bold;
  color: var(--heading-color);
  margin-bottom: 4px;
}

.server-info {
  color: var(--text-color);
  font-size: 13px;
  margin-bottom: 4px;
}

.server-actions {
  display: flex;
  margin-top: 8px;
}

.server-action-btn {
  margin-right: 8px;
}

/* Pipeline Card */
.pipeline-card {
  border: 1px solid var(--border-color);
  border-radius: 4px;
  margin-bottom: 16px;
  transition: all 0.3s;
}

.pipeline-card:hover {
  box-shadow: var(--shadow);
}

.pipeline-header {
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fafafa;
}

.pipeline-title {
  font-weight: bold;
  font-size: 16px;
  color: var(--heading-color);
}

.pipeline-body {
  padding: 16px;
}

.pipeline-description {
  color: var(--text-color);
  margin-bottom: 16px;
}

.pipeline-steps {
  margin-bottom: 16px;
}

.step-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid var(--border-color);
}

.step-number {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-weight: bold;
}

.step-content {
  flex: 1;
}

.step-name {
  font-weight: bold;
  color: var(--heading-color);
  margin-bottom: 4px;
}

.step-command {
  background-color: #f6f6f6;
  padding: 6px 10px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 13px;
  margin-top: 4px;
}

/* Execution Results */
.execution-result {
  background-color: #000;
  color: #fff;
  padding: 16px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  max-height: 400px;
  overflow-y: auto;
}

.result-success {
  color: #52c41a;
}

.result-error {
  color: #f5222d;
}

.result-command {
  color: #faad14;
  margin-top: 10px;
  margin-bottom: 5px;
  font-weight: bold;
}

/* Terminal Output */
.terminal {
  background-color: #1e1e1e;
  color: #f0f0f0;
  font-family: 'Courier New', monospace;
  padding: 16px;
  border-radius: 4px;
  max-height: 400px;
  overflow-y: auto;
}

.terminal-header {
  border-bottom: 1px solid #444;
  padding-bottom: 8px;
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
}

.terminal-title {
  font-weight: bold;
}

.terminal-line {
  margin: 4px 0;
  line-height: 1.5;
}

.terminal-prompt {
  color: #52c41a;
  margin-right: 8px;
}

.terminal-command {
  color: #1890ff;
}

.terminal-output {
  white-space: pre-wrap;
}

/* Button Styles */
.ssh-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid transparent;
}

.ssh-btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.ssh-btn-primary:hover {
  background-color: #40a9ff;
}

.ssh-btn-success {
  background-color: var(--success-color);
  color: white;
}

.ssh-btn-success:hover {
  background-color: #71d43a;
}

.ssh-btn-warning {
  background-color: var(--warning-color);
  color: white;
}

.ssh-btn-warning:hover {
  background-color: #ffc53d;
}

.ssh-btn-danger {
  background-color: var(--error-color);
  color: white;
}

.ssh-btn-danger:hover {
  background-color: #ff4d4f;
}

.ssh-btn-default {
  background-color: white;
  border-color: var(--border-color);
  color: var(--text-color);
}

.ssh-btn-default:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.ssh-btn-icon {
  margin-right: 6px;
}

/* Form Elements */
.ssh-form-item {
  margin-bottom: 20px;
}

.ssh-form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: var(--heading-color);
}

.ssh-form-input {
  width: 100%;
  padding: 10px 12px;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  transition: all 0.3s;
}

.ssh-form-input:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.ssh-form-select {
  width: 100%;
  padding: 10px 12px;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  transition: all 0.3s;
  background-color: white;
}

.ssh-form-select:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* Table Styles */
.ssh-table {
  width: 100%;
  border-collapse: collapse;
}

.ssh-table th,
.ssh-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.ssh-table th {
  background-color: #fafafa;
  font-weight: 500;
  color: var(--heading-color);
}

.ssh-table tr:hover {
  background-color: #f5f7fa;
}

/* Loading and Animations */
.ssh-loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(24, 144, 255, 0.2);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s infinite linear;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Responsiveness */
@media (max-width: 768px) {
  .ssh-container {
    padding: 12px;
  }
  
  .ssh-card-header {
    padding: 12px;
    flex-direction: column;
    align-items: flex-start;
  }
  
  .server-card {
    flex-direction: column;
  }
  
  .server-icon {
    margin-right: 0;
    margin-bottom: 12px;
  }
  
  .server-actions {
    flex-direction: column;
  }
  
  .server-action-btn {
    margin-right: 0;
    margin-bottom: 8px;
    width: 100%;
  }
}
