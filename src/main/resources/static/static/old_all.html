<!DOCTYPE html>
<html lang="en" class="dark-theme">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SA-UTS Portal | Dashboard</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🚀</text></svg>">
    <style>
        :root {
            --primary: #6366f1;
            --primary-hover: #4f46e5;
            --bg: #0f172a;
            --surface: #1e293b;
            --surface-hover: #334155;
            --text: #f8fafc;
            --text-secondary: #94a3b8;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --border: #334155;
            --shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background-color: var(--bg);
            color: var(--text);
            line-height: 1.6;
            padding: 0;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        /* Animated Gradient Background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(-45deg, #0f172a, #1e1b4b, #1e3a8a, #1e40af);
            background-size: 400% 400%;
            animation: gradientBG 15s ease infinite;
            z-index: -2;
            opacity: 0.9;
        }

        @keyframes gradientBG {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* Floating Particles */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float 15s infinite linear;
            opacity: 0;
        }

        @keyframes float {
            0% {
                transform: translateY(0) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 0.5;
            }
            90% {
                opacity: 0.5;
            }
            100% {
                transform: translateY(-1000px) rotate(720deg);
                opacity: 0;
            }
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem 1.5rem;
            position: relative;
            z-index: 1;
        }

        /* Header Styles */
        header {
            text-align: center;
            margin: 1rem 0 1.5rem;
            position: relative;
            padding: 1rem 0;
        }

        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 1.5rem;
            background: linear-gradient(135deg, var(--primary), #8b5cf6);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 10px 30px -10px rgba(99, 102, 241, 0.5);
            transform: rotate(45deg);
            transition: var(--transition);
        }

        .logo i {
            font-size: 2.5rem;
            color: white;
            transform: rotate(-45deg);
        }

        h1 {
            font-size: 1.8rem;
            font-weight: 700;
            background: linear-gradient(90deg, #fff, #a5b4fc);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 0.5rem;
            letter-spacing: -0.5px;
            line-height: 1.2;
        }

        header p {
            font-size: 0.9rem;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
            opacity: 0.8;
            display: none; /* Hide subtitle */
        }

        .header-decoration {
            position: absolute;
            width: 200px;
            height: 200px;
            border-radius: 50%;
            filter: blur(60px);
            opacity: 0.15;
            z-index: -1;
        }

        .decoration-1 {
            background: var(--primary);
            top: -50px;
            left: -50px;
            width: 300px;
            height: 300px;
        }

        .decoration-2 {
            background: #8b5cf6;
            bottom: -80px;
            right: -50px;
            width: 250px;
            height: 250px;
        }

        /* Menu Grid */
        .menu {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
            margin: 0 auto;
            max-width: 1200px;
        }

        /* Card Styles */
        .menu-item {
            background: rgba(30, 41, 59, 0.4);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.05);
            border-radius: 16px;
            transition: var(--transition);
            overflow: hidden;
            position: relative;
            box-shadow: var(--shadow);
            transform: translateY(0);
            height: 100%;
        }

        .menu-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary), #8b5cf6);
            opacity: 0;
            transition: var(--transition);
        }

        .menu-item:hover {
            transform: translateY(-5px);
            border-color: rgba(255, 255, 255, 0.1);
            box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.3);
        }

        .menu-item:hover::before {
            opacity: 1;
        }

        .menu-item a {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 1.25rem 1rem;
            color: var(--text);
            text-decoration: none;
            height: 100%;
            position: relative;
            z-index: 1;
            transition: var(--transition);
            text-align: center;
        }

        .menu-item a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.05));
            opacity: 0;
            transition: var(--transition);
            z-index: -1;
        }

        .menu-item:hover a::before {
            opacity: 1;
        }

        .menu-item .icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 0.75rem;
            font-size: 1.25rem;
            color: white;
            background: linear-gradient(135deg, var(--primary), #8b5cf6);
            box-shadow: 0 4px 12px -3px var(--primary);
            transition: var(--transition);
        }

        .menu-item:hover .icon {
            transform: scale(1.1) rotate(5deg);
        }

        .menu-item h3 {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
            color: var(--text);
            transition: var(--transition);
        }

        .menu-item p {
            color: var(--text-secondary);
            font-size: 0.8rem;
            margin: 0.25rem 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 100%;
            padding: 0 0.5rem;
        }

        .menu-item .meta {
            display: none; /* Hide meta info */
        }

        .menu-item .arrow {
            color: var(--primary);
            transition: var(--transition);
            font-size: 1.25rem;
        }

        .menu-item:hover .arrow {
            transform: translateX(5px);
        }

        /* Footer */
        .last-updated {
            text-align: center;
            margin: 4rem auto 0;
            color: var(--text-secondary);
            font-size: 0.9rem;
            padding: 1rem;
            max-width: 600px;
            border-top: 1px solid rgba(255, 255, 255, 0.05);
            position: relative;
        }

        .last-updated::before {
            content: '';
            position: absolute;
            top: -1px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 2px;
            background: linear-gradient(90deg, transparent, var(--primary), transparent);
        }

        /* Loading and error states */
        .loading, .no-files {
            grid-column: 1 / -1;
            text-align: center;
            padding: 2rem;
            color: var(--text-secondary);
            font-size: 1.1rem;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .menu {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 1.5rem 1rem;
            }

            header {
                margin: 1rem 0 3rem;
                padding: 1rem 0;
            }

            h1 {
                font-size: 2rem;
            }

            header p {
                font-size: 1rem;
            }

            .menu {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .menu-item {
                border-radius: 12px;
            }

            .menu-item a {
                padding: 1.5rem;
            }

            .logo {
                width: 64px;
                height: 64px;
            }
        }

        @media (max-width: 480px) {
            h1 {
                font-size: 1.75rem;
            }

            .menu-item .icon {
                width: 40px;
                height: 40px;
                font-size: 1.25rem;
            }
        }
    </style>
    <script>
        // Icon mapping for different page types
        const iconMap = {
            'ssh': 'terminal',
            'k8s': 'dharmachakra',
            'kubernetes': 'dharmachakra',
            'pipeline': 'code-branch',
            'management': 'cog',
            'wxe': 'desktop',
            'user': 'users',
            'index': 'home',
            'default': 'file-alt'
        };

        // Get page title from filename
        function getPageTitle(filename) {
            // Remove .html extension and split by hyphens/underscores
            const name = filename.replace(/\.html$/i, '')
                .replace(/[-_]/g, ' ')
                .replace(/\b\w/g, l => l.toUpperCase());
            
            // Special case for WXE

            return name;
        }

        // Get icon based on filename
        function getIcon(filename) {
            const name = filename.toLowerCase();
            
            for (const [key, icon] of Object.entries(iconMap)) {
                if (name.includes(key)) {
                    return icon;
                }
            }
            return iconMap.default;
        }

        // Get description based on filename
        // Description is now defined in the knownHtmlFiles array above

        // Get version based on filename (this is just for demo - in a real app, you might get this from an API)
        function getVersion() {
            const versions = ['1.0.0', '1.2.3', '2.0.0', '2.1.4', '3.0.0-beta', '3.2.1'];
            return versions[Math.floor(Math.random() * versions.length)];
        }

        // Generate menu item HTML
        function createMenuItem(filename) {
            const title = getPageTitle(filename);
            const icon = getIcon(filename);
            const description = getDescription(filename);
            
            return `
                <div class="menu-item">
                    <a href="${filename}" target="_blank" rel="noopener noreferrer">
                        <div class="icon">
                            <i class="fas fa-${icon}"></i>
                        </div>
                        <h3>${title}</h3>
                        <p>${description}</p>
                        <div class="meta">
                            <span>v${getVersion()}</span>
                            <span class="arrow">→</span>
                        </div>
                    </a>
                </div>
            `;
        }

        // 增加菜单，只需要在这里加{file: 'xxx.html', description: '描述文本'}就好
        // 只要和all.html同级就会自动加入该html的跳转
        const knownHtmlFiles = [
            {file: 'SSH3.html', description: 'SSH'},
            {file: 'k8s.html', description: 'k8s流水线'},
            {file: 'uts-integration.html', description: 'API整合转发'},
            {file: 'wxe.html', description: '企微审批模版'},
            {file: 'wxeuser.html', description: '企微部门成员'}
        ];
        
        // Convert to simple array of filenames for compatibility
        const knownHtmlFileNames = knownHtmlFiles.map(item => item.file);

        // Get description for a file
        function getDescription(filename) {
            const item = knownHtmlFiles.find(item => item.file.toLowerCase() === filename.toLowerCase());
            return item ? item.description : `Access the ${getPageTitle(filename)} interface.`;
        }
        
        // Check if a file exists
        async function fileExists(url) {
            try {
                const response = await fetch(url, { method: 'HEAD' });
                return response.ok;
            } catch (error) {
                return false;
            }
        }

        // Generate menu with available files
        async function generateMenu() {
            const menuContainer = document.querySelector('.menu');
            const availableFiles = [];
            
            // Check each known file
            for (const item of knownHtmlFiles) {
                if (await fileExists(item.file)) {
                    availableFiles.push(item.file);
                }
            }
            
            // If no known files found, try to find any HTML files (this is a fallback)
            if (availableFiles.length === 0) {
                try {
                    // This might not work due to CORS, but we'll try
                    const response = await fetch('.');
                    const html = await response.text();
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const links = Array.from(doc.querySelectorAll('a'))
                        .map(a => a.getAttribute('href'))
                        .filter(href => href && 
                                     href.endsWith('.html') && 
                                     href !== 'all.html' && 
                                     href !== 'index.html');
                    
                    availableFiles.push(...links);
                } catch (error) {
                    console.error('Error fetching directory listing:', error);
                }
            }
            
            // If still no files, use the known list as fallback
            const filesToShow = availableFiles.length > 0 ? availableFiles : knownHtmlFiles;
            
            // Generate and display menu items
            menuContainer.innerHTML = filesToShow.map(createMenuItem).join('') || 
                '<div class="no-files">No HTML files found in this directory.</div>';
            
            // Initialize card effects
            initCardEffects();
        }

        // Initialize card hover effects
        function initCardEffects() {
            const cards = document.querySelectorAll('.menu-item');
            cards.forEach(card => {
                card.addEventListener('mousemove', (e) => {
                    const rect = card.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;
                    
                    const centerX = rect.width / 2;
                    const centerY = rect.height / 2;
                    
                    const angleX = (y - centerY) / 20;
                    const angleY = (centerX - x) / 20;
                    
                    card.style.transform = `perspective(1000px) rotateX(${angleX}deg) rotateY(${angleY}deg) translateY(-5px)`;
                    card.style.boxShadow = `${-angleY}px ${angleX}px 30px rgba(0, 0, 0, 0.2)`;
                });

                card.addEventListener('mouseleave', () => {
                    card.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) translateY(0)';
                    card.style.boxShadow = '0 10px 30px -10px rgba(0, 0, 0, 0.1)';
                });
            });
        }

        // Initialize particles
        function initParticles() {
            const particlesContainer = document.createElement('div');
            particlesContainer.className = 'particles';
            document.body.appendChild(particlesContainer);

            for (let i = 0; i < 20; i++) {
                createParticle();
            }
        }

        // Create a single particle
        function createParticle() {
            const particle = document.createElement('div');
            particle.className = 'particle';
            
            // Random size between 2px and 6px
            const size = Math.random() * 4 + 2;
            
            // Random position
            const posX = Math.random() * 100;
            const posY = Math.random() * 100 + 100; // Start below the viewport
            
            // Random animation duration between 15s and 30s
            const duration = Math.random() * 15 + 15;
            
            // Random delay up to 15s
            const delay = Math.random() * 15;
            
            // Apply styles
            particle.style.width = `${size}px`;
            particle.style.height = `${size}px`;
            particle.style.left = `${posX}%`;
            particle.style.top = `${posY}%`;
            particle.style.animationDuration = `${duration}s`;
            particle.style.animationDelay = `-${delay}s`;
            
            // Random opacity
            particle.style.opacity = Math.random() * 0.5 + 0.1;
            
            // Add to DOM
            document.querySelector('.particles').appendChild(particle);
            
            // Remove particle after animation completes
            setTimeout(() => {
                particle.remove();
                createParticle(); // Create a new particle
            }, duration * 1000);
        }
    </script>
</head>
<body>
    <div class="particles"></div>
    <div class="container">
        <header>
            <div class="logo">
                <i class="fas fa-rocket"></i>
            </div>
            <h1>SA-UTS Portal</h1>
            <p>Access all available tools and services from one centralized dashboard</p>
            <div class="header-decoration decoration-1"></div>
            <div class="header-decoration decoration-2"></div>
        </header>

        <div class="menu">
            <!-- Menu items will be dynamically generated here -->
            <div class="loading">Loading applications...</div>
        </div>

        <div class="last-updated">
            Last updated: July 1, 2025 | System Status: <span style="color: #10b981;">All Systems Operational</span>
        </div>
    </div>
</body>
</html>

<script>
    // Initialize everything when the DOM is loaded
    document.addEventListener('DOMContentLoaded', async () => {
        initParticles();
        
        // Show loading state
        const menuContainer = document.querySelector('.menu');
        menuContainer.innerHTML = '<div class="loading">Loading applications...</div>';
        
        try {
            await generateMenu();
        } catch (error) {
            console.error('Error initializing menu:', error);
            menuContainer.innerHTML = '<div class="no-files">Error loading applications. Please try refreshing the page.</div>';
        }
        
        // Update the last updated time
        const now = new Date();
        document.querySelector('.last-updated').innerHTML = 
            `Last updated: ${now.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })} | ` +
            `System Status: <span style="color: #10b981;">All Systems Operational</span>`;
    });
</script>