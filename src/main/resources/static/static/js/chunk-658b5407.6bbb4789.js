(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-658b5407"],{"0ad1":function(t,e,i){"use strict";i("7743")},"29cc":function(t,e,i){"use strict";i("b761")},"29d1":function(t,e,i){},"2a7a":function(t,e,i){},"2c13":function(t,e,i){},"2f85":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{},[i("div",{staticClass:"dictionaries_ul myscrollbar"},[0!=t.lst.length?i("ul",{},t._l(t.lst,(function(e,a){return i("li",{key:a,class:t.radio==a?"active":"",on:{click:function(i){t.getCurrentRow(e),t.radio=a}}},[i("span",[t._v(t._s(e.dictvalue))])])})),0):i("div",{staticClass:"noData"},[t._v("暂无数据")])]),i("div",{staticClass:"foots"},[i("span",{staticStyle:{"border-right":"1px solid #dcdfe6"},on:{click:function(e){t.SYSM07B1Visible=!0,t.lst=t.formdata.item}}},[t._v("编辑")]),i("span",{on:{click:function(e){return t.$emit("closedic")}}},[t._v("关闭")])])]),t.SYSM07B1Visible?i("el-dialog",{attrs:{width:"400px",title:"编辑","append-to-body":!0,visible:t.SYSM07B1Visible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.SYSM07B1Visible=e}}},[i("div",{staticClass:"dialog-body customDialog"},[i("div",{staticClass:"left"},t._l(t.lst,(function(e,a){return i("p",{key:a,class:t.ActiveIndex==a?"isActive":"",on:{click:function(e){t.ActiveIndex=a}}},[t._v(" "+t._s(e.dictvalue)+" ")])})),0),i("div",{staticClass:"right"},[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.addInput()}}},[t._v("添 加")]),i("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.editInput()}}},[t._v("编 辑")]),i("el-button",{attrs:{type:"danger",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.delItem()}}},[t._v("删 除")]),i("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveUp()}}},[t._v("上 移")]),i("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveDown()}}},[t._v("下 移")])],1)]),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitUpdate}},[t._v("确 定")]),i("el-button",{attrs:{size:"small"},on:{click:function(e){t.SYSM07B1Visible=!1,t.bindData()}}},[t._v("取 消")])],1)]):t._e()],1)},o=[],l=(i("a434"),i("e9c4"),i("b775")),s=i("333d"),n=i("b0b8"),r={components:{Pagination:s["a"]},props:["multi","billcode"],data:function(){return{title:"数据字典",formdata:{item:[]},lst:[],total:0,radio:-1,selrows:"",SYSM07B1Visible:!1,ActiveIndex:-1,queryParams:{PageNum:1,PageSize:100,OrderType:1,SearchType:1}}},watch:{lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i}},created:function(){},methods:{getCurrentRow:function(t){this.selrows=t,this.$forceUpdate(),this.$emit("singleSel",t)},bindData:function(){var t=this;this.lst=[],l["a"].get("/system/SYSM07B1/getBillEntityByDictCode?key="+this.billcode).then((function(e){if(200==e.data.code){if(null==e.data.data)return t.formdata={item:[]},void(t.listLoading=!1);if(t.formdata=e.data.data,0==t.formdata.item.length)return void(t.lst=[]);for(var i=0;i<t.formdata.item.length;i++)t.lst.push(t.formdata.item[i])}})).catch((function(e){t.$message.error("请求出错")}))},rowIndex:function(t){var e=t.row,i=t.rowIndex;e.row_index=i},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)},addInput:function(){var t=this;this.$prompt("数值：","添加",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:"",inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var i=e.value;t.addItem(i)})).catch((function(t){console.log(t)}))},addItem:function(t){n.setOptions({checkPolyphone:!1,charCase:1});var e={cssclass:"",defaultmark:0,dictcode:n.getFullChars(t),dictvalue:t,enabledmark:1,essential:0,pid:this.formdata.id,remark:"",rownum:0};this.lst.push(e)},editInput:function(){if(-1!=this.ActiveIndex){var t=this;this.$prompt("数值：","编辑",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:t.lst[t.ActiveIndex].dictvalue,inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var i=e.value;t.lst[t.ActiveIndex].dictvalue=i,n.setOptions({checkPolyphone:!1,charCase:1}),t.lst[t.ActiveIndex].dictcode=n.getFullChars(i)})).catch((function(t){console.log(t)}))}else this.$message.warning("请选择内容")},delItem:function(){if(-1!=this.ActiveIndex){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.lst.splice(t.ActiveIndex,1),t.ActiveIndex=-1})).catch((function(){}))}else this.$message.warning("请选择内容")},getMoveUp:function(){if(0!=this.ActiveIndex){var t=this.lst[this.ActiveIndex],e=this.lst[this.ActiveIndex].rownum;this.lst.splice(e,1),this.lst.splice(e-1,0,t),this.ActiveIndex-=1;for(var i=0;i<this.lst.length;i++)this.lst[i].rownum=i}else this.$message.warning("已经是第一行了！")},getMoveDown:function(){if(this.ActiveIndex!=this.lst.length-1){var t=this.lst[this.ActiveIndex],e=this.lst[this.ActiveIndex].rownum;this.lst.splice(e,1),this.lst.splice(e+1,0,t),this.ActiveIndex+=1;for(var i=0;i<this.lst.length;i++)this.lst[i].rownum=i}else this.$message.warning("已经是最后一行了！")},submitUpdate:function(){var t=this;this.formdata.item=this.lst,l["a"].post("/system/SYSM07B1/update",JSON.stringify(this.formdata)).then((function(e){200==e.data.code?(t.$message.success(e.data.msg||"保存成功"),t.SYSM07B1Visible=!1):t.$message.warning(e.data.msg||"保存失败")})).catch((function(e){t.$message.error("请求错误")}))}}},c=r,d=(i("6156"),i("2877")),m=Object(d["a"])(c,a,o,!1,null,"2d6fcc02",null);e["a"]=m.exports},"3a18":function(t,e,i){"use strict";i("e250")},"3e72":function(t,e,i){},4032:function(t,e,i){},"42ad":function(t,e,i){},"4f8a":function(t,e,i){"use strict";i("3e72")},6156:function(t,e,i){"use strict";i("a1b8")},"70a8":function(t,e,i){"use strict";i("29d1")},7743:function(t,e,i){},7973:function(t,e,i){"use strict";i("2a7a")},"8c53":function(t,e,i){},"98e3":function(t,e,i){"use strict";i("4032")},a1b8:function(t,e,i){},aee8:function(t,e,i){"use strict";i("2c13")},b761:function(t,e,i){},c984:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.formvisible?i("div",{ref:"formedit",staticClass:"formedit"},[i("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}],ref:"index",staticClass:"page-container"},[i("div",{staticClass:"page-container-left"},[i("div",{staticStyle:{"font-size":"14px",color:"#606266"}},[i("div",{staticClass:"groupTitle"},[i("span",[t._v(t._s(t.treeTitle))]),i("i",{staticClass:"el-icon-s-tools",style:{color:t.treeEditable?"#1e80ff":""},on:{click:function(e){t.treeEditable=!t.treeEditable}}})]),i("el-tree",{staticStyle:{"font-size":"14px",height:"80vh",overflow:"auto",width:"100%"},attrs:{data:t.groupData,"node-key":"id","default-expand-all":"","expand-on-click-node":!1},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.node,o=e.data;return i("span",{staticClass:"custom-tree-node"},[i("span",{on:{click:function(){return t.handleNodeClick(o)}}},[t._v(t._s(a.label)+" ")]),i("span",{directives:[{name:"show",rawName:"v-show",value:t.treeEditable,expression:"treeEditable"}]},[i("el-button",{directives:[{name:"show",rawName:"v-show",value:"0"!==o.id,expression:"data.id !== '0'"}],staticStyle:{"padding-left":"5px"},attrs:{type:"text",size:"mini",icon:"el-icon-edit"},on:{click:function(){return t.editTreeNode(o)}}})],1),i("span",{directives:[{name:"show",rawName:"v-show",value:t.treeEditable,expression:"treeEditable"}]},[i("el-button",{staticStyle:{"padding-left":"5px"},attrs:{type:"text",size:"mini",icon:"el-icon-circle-plus-outline"},on:{click:function(){return t.addTreeChild(o)}}})],1),i("span",{directives:[{name:"show",rawName:"v-show",value:t.treeEditable,expression:"treeEditable"}]},[i("el-button",{directives:[{name:"show",rawName:"v-show",value:"0"!==o.id,expression:"data.id !== '0'"}],staticStyle:{"padding-left":"5px"},attrs:{type:"text",size:"mini",icon:"el-icon-delete-solid"},on:{click:function(){return t.delTreeNode(o)}}})],1)])}}])})],1)]),i("div",{staticClass:"page-container-right"},[i("ListHeader",{attrs:{tableForm:t.tableForm},on:{btnAdd:function(e){return t.showForm(0)},btnSearch:function(e){return t.$refs.tableList.search(e)},advancedSearch:function(e){return t.$refs.tableList.advancedSearch(e)},btnHelp:t.btnHelp}}),i("el-row",[i("el-col",{attrs:{span:t.showhelp?20:24}},[i("TableList",{ref:"tableList",attrs:{formtemplate:t.formtemplate},on:{changeIdx:t.changeIdx,showForm:t.showForm,sendTableForm:t.sendTableForm}})],1),i("el-col",{attrs:{span:t.showhelp?4:0}},[i("HelpModel",{ref:"helpmodel",attrs:{code:"SYSM07B13"}})],1)],1)],1)]),t.gropuFormVisible?i("el-dialog",{attrs:{title:"界面分组","append-to-body":!0,visible:t.gropuFormVisible,width:"800px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.gropuFormVisible=e}}},[i("Group",{ref:"group",attrs:{idx:t.idx,pid:t.pid},on:{bindData:t.bindTreeData,closeDialog:function(e){t.gropuFormVisible=!1}}})],1):t._e()],1)},o=[],l=i("2909"),s=i("c7eb"),n=i("1da1"),r=(i("b64b"),i("d81d"),i("99af"),i("ac1f"),i("841c"),function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{class:t.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[i("div",[i("el-input",{staticClass:"filter-item",staticStyle:{width:"260px"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnSearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[i("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnSearch},slot:"append"},[t._v("搜索 ")])],1),i("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-plus",plain:"",size:"mini"},on:{click:t.btnAdd}},[t._v(" 添加 ")])],1),i("div",{staticClass:"iShowBtn"},[i("el-button",{attrs:{size:"mini",icon:"el-icon-search",title:"高级筛选",type:t.$store.state.advancedSearch.modulecode==t.tableForm.formcode?"primary":"default"},on:{click:function(e){return t.openSearchForm()}}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){return t.openDialog()}}})],1)]),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/S16M87B1"},on:{bindData:function(e){return t.$emit("bindColumn")},closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e(),i("el-dialog",{attrs:{title:"高级筛选",width:"720px",visible:t.searchVisible,"append-to-body":!0,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.searchVisible=e}}},[i("SearchForm",{ref:"searchForm",attrs:{code:t.tableForm.formcode,baseparam:"/S16M88B1"},on:{advancedSearch:t.advancedSearch,closedDialog:function(e){t.searchVisible=!1},bindData:t.bindData}})],1)],1)}),c=[],d={name:"Listheader",components:{},props:["tableForm"],data:function(){return{strfilter:"",iShow:!1,formdata:{},code:"SYSM07B13List",setColumsVisible:!1,searchVisible:!1}},methods:{openDialog:function(){this.setColumsVisible=!0},advancedSearch:function(){this.$emit("advancedSearch",this.formdata),this.searchVisible=!1},openSearchForm:function(){var t=this;this.searchVisible=!0,setTimeout((function(){t.$refs.searchForm.getInit()}),100)},btnAdd:function(){this.$emit("btnAdd")},bindData:function(){this.$emit("bindData")},btnSearch:function(){this.$emit("btnSearch",this.strfilter)}}},m=d,u=(i("3a18"),i("2877")),f=Object(u["a"])(m,r,c,!1,null,"22946300",null),p=f.exports,h=i("b775"),b=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"99"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("EditGroupBtns",{ref:"EditGroupBtns",attrs:{showcontent:["save","operate"],formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,approval:t.approval,closeForm:t.closeForm,printButton:function(e){return t.$refs.PrintServer.printButton()},clickMethods:t.clickMethods}})],1)]),i("div",{staticStyle:{padding:"20px","box-sizing":"content-box",height:"100%",overflow:"auto"}},[i("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate},on:{clickMethods:t.clickMethods},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[i("div",{staticClass:"form form-head p-r"},[i("EditHeader",{ref:"formHeader",attrs:{title:t.title,formdata:t.formdata},on:{handleChange:t.handleChange}})],1)]},proxy:!0},{key:"Item",fn:function(){return[i("div",{ref:"tabsHeight",staticClass:"form-body form f-1",staticStyle:{"margin-top":"10px"}},[i("div",{staticClass:"outer-div"},[i("header",{staticClass:"header"},[i("ul",{staticClass:"tab-tilte"},[i("li",{class:{active:0==t.activeTabs},on:{click:function(e){t.activeTabs=0}}},[t._v(" 表单内容 ")]),i("li",{class:{active:1==t.activeTabs},on:{click:function(e){t.activeTabs=1}}},[t._v(" 表尾内容 ")]),i("li",{class:{active:2==t.activeTabs},on:{click:function(e){t.activeTabs=2}}},[t._v(" 单据列 ")]),i("li",{class:{active:3==t.activeTabs},on:{click:function(e){t.activeTabs=3}}},[t._v(" 明细列 ")]),i("li",{class:{active:4==t.activeTabs},on:{click:function(e){t.activeTabs=4}}},[t._v(" 子表列 ")])])]),i("div",{staticClass:"tab-content"},[i("div",{directives:[{name:"show",rawName:"v-show",value:0==t.activeTabs,expression:"activeTabs == 0"}]},[i("HeaderItem",{ref:"headerItem",staticStyle:{width:"99%"},attrs:{lstitem:t.formdata.frmcontent,formdata:t.formdata,formstate:t.formstate,formtemplate:t.formtemplate,idx:t.idx,tabsHeight:t.tabsHeight},on:{bindData:t.bindData}})],1),i("div",{directives:[{name:"show",rawName:"v-show",value:1==t.activeTabs,expression:"activeTabs == 1"}]},[i("FootItem",{ref:"footItem",staticStyle:{width:"99%"},attrs:{lstitem:t.formdata.frmcontent,formdata:t.formdata,formstate:t.formstate,formtemplate:t.formtemplate,idx:t.idx,tabsHeight:t.tabsHeight},on:{bindData:t.bindData}})],1),i("div",{directives:[{name:"show",rawName:"v-show",value:2==t.activeTabs,expression:"activeTabs == 2"}]},[i("ThItem",{ref:"thItem",staticStyle:{width:"99%"},attrs:{lstitem:t.formdata.frmcontent,formdata:t.formdata,formstate:t.formstate,formtemplate:t.formtemplate,idx:t.idx,tabsHeight:t.tabsHeight},on:{bindData:t.bindData}})],1),i("div",{directives:[{name:"show",rawName:"v-show",value:3==t.activeTabs,expression:"activeTabs == 3"}]},[i("ListItem",{ref:"listItem",staticStyle:{width:"99%"},attrs:{lstitem:t.formdata.frmcontent,formdata:t.formdata,formstate:t.formstate,formtemplate:t.formtemplate,idx:t.idx,tabsHeight:t.tabsHeight},on:{bindData:t.bindData}})],1),i("div",{directives:[{name:"show",rawName:"v-show",value:4==t.activeTabs,expression:"activeTabs == 4"}]},[i("EditItem",{ref:"editItem",staticStyle:{width:"99%"},attrs:{lstitem:t.formdata.frmcontent,formdata:t.formdata,formstate:t.formstate,formtemplate:t.formtemplate,idx:t.idx,tabsHeight:t.tabsHeight},on:{bindData:t.bindData}})],1)])])])]},proxy:!0}],null,!0)})],1)]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"SYSM07B13Edit",commonurl:"/system/SYSM07B13/printBill",weburl:"/system/SYSM07B13/printWebBill"}}),i("Flowable",{ref:"flowable",attrs:{formdata:t.formdata,examinecode:"SYSM07B13Edit",examineurl:"/system/SYSM07B13/sendapprovel"}}),t.operationVisible?i("el-dialog",{staticClass:"operationDialog",attrs:{width:"90vw",visible:t.operationVisible,"close-on-click-modal":!1,"destroy-on-close":!0,"append-to-body":!0,top:"1vh","show-close":!1},on:{"update:visible":function(e){t.operationVisible=e}}}):t._e(),t.processVisible?i("el-dialog",{attrs:{title:t.processTitle,"append-to-body":!0,width:"90vw",visible:t.processVisible,"destroy-on-close":!0,"show-close":!0,top:"1vh"},on:{"update:visible":function(e){t.processVisible=e}}}):t._e()],1)},v=[];const g={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);h["a"].post("/system/SYSM07B13/create",a).then(t=>{200==t.data.code?e(t.data):i(t.data.message)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);h["a"].post("/system/SYSM07B13/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.message)}).catch(t=>{i(t)})})},delete(t){h["a"].get("/system/SYSM07B13/delete?key="+t.formdata.id).then(e=>{200==e.data.code?0==e.data.data?t.$message.warning("删除失败,"+e.data.msg+"中已使用"):(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})},approval(t){t.submitting=1,h["a"].get("/system/SYSM07B13/approval?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||(t.formdata.assessor?"反审核成功":"审核成功")),t.formdata=e.data.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1,t.stockwarningvisible=!1):(t.submitting=0,t.$message.warning(e.data.msg||(t.formdata.assessor?"反审核失败":"审核失败")))}).catch(e=>{t.submitting=0,t.$message.error(e||"服务请求错误")})}};var y=g,w=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("gengroupid")}}},[i("el-form-item",{attrs:{label:"界面分组",prop:"gengroupid"}},[i("el-cascader",{ref:"cascader",staticStyle:{width:"100%"},attrs:{options:t.groupData,props:t.defaultProps,"show-all-levels":!1,placeholder:"请选择分组名称",size:"small",clearable:""},on:{change:function(e){return t.$emit("handleChange",e)}},model:{value:t.formdata.gengroupid,callback:function(e){t.$set(t.formdata,"gengroupid",e)},expression:"formdata.gengroupid"}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("modulecode")}}},[i("el-form-item",{attrs:{label:"模块编码",prop:"modulecode"}},[i("el-input",{attrs:{placeholder:"请输入模块编码",clearable:"",size:"small"},model:{value:t.formdata.modulecode,callback:function(e){t.$set(t.formdata,"modulecode",e)},expression:"formdata.modulecode"}})],1)],1)])],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("frmname")}}},[i("el-form-item",{attrs:{label:"界面名称"}},[i("el-input",{attrs:{placeholder:"请输入名称",clearable:"",size:"small"},model:{value:t.formdata.frmname,callback:function(e){t.$set(t.formdata,"frmname",e)},expression:"formdata.frmname"}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("frmcode")}}},[i("el-form-item",{attrs:{label:"界面编码",prop:"frmcode"}},[i("el-input",{attrs:{placeholder:"请输入界面编码",clearable:"",size:"small"},model:{value:t.formdata.frmcode,callback:function(e){t.$set(t.formdata,"frmcode",e)},expression:"formdata.frmcode"}})],1)],1)]),i("el-col",{attrs:{span:3}},[i("el-form-item",{attrs:{label:"序号"}},[i("el-input-number",{staticClass:"inputNumberContent",staticStyle:{width:"100%"},attrs:{controls:!0,type:"number",min:0,size:"small","controls-position":"right"},model:{value:t.formdata.rownum,callback:function(e){t.$set(t.formdata,"rownum",e)},expression:"formdata.rownum"}})],1)],1),i("el-col",{attrs:{span:3}},[i("el-form-item",{attrs:{label:"","label-width":"50px"}},[i("el-checkbox",{attrs:{label:"状态","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.enabledmark,callback:function(e){t.$set(t.formdata,"enabledmark",e)},expression:"formdata.enabledmark"}})],1)],1)],1)],1)},x=[],k=i("ade3"),S={props:{formdata:{type:Object},title:{type:String}},components:{},data:function(){return{formRules:Object(k["a"])({frmname:[{required:!0,trigger:"blur",message:"界面名称为必填项"}]},"frmname",[{required:!0,trigger:"blur",message:"界面名称为必填项"}]),groupData:[],defaultProps:{children:"children",label:"label",value:"id"}}},mounted:function(){this.bindTreeData()},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)},bindTreeData:function(){var t=this;this.$request.get("/system/SYSM07B8/getListByModuleCode?Code=SYSM07B13").then((function(e){if(200==e.data.code){var i=e.data.data.map((function(t){return{id:t.id,pid:t.parentid,label:t.groupname}})),a=[{id:"0",pid:"root",label:"界面分组"}],o=[].concat(Object(l["a"])(i),a);t.groupData=t.transData(o,"id","pid","children")}}))},transData:function(t,e,i,a){for(var o=[],l={},s=e,n=i,r=a,c=0,d=0,m=t.length;c<m;c++)l[t[c][s]]=t[c];for(;d<m;d++){var u=t[d],f=l[u[n]];f?(!f[r]&&(f[r]=[]),f[r].push(u)):o.push(u)}return o}}},C=S,$=(i("0ad1"),Object(u["a"])(C,w,x,!1,null,"f66db2c2",null)),R=$.exports,O=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form",style:{height:t.tableHeight+"px"}},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{showcontent:["add","moveup","movedown","delete","copyrow","refresh","column"],formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,getColumn:t.getColumn},scopedSlots:t._u([{key:"right",fn:function(){return[i("el-checkbox",{staticStyle:{"line-height":"20px"},attrs:{label:"使用",size:"mini","true-label":1,"false-label":0,border:""},model:{value:t.usetype,callback:function(e){t.usetype=e},expression:"usetype"}})]},proxy:!0}])}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight-40,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,editOption:t.editOption,"fixed-footer":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1)],1)},F=[],_=i("53ca"),T=(i("c740"),i("e9c4"),i("caad"),i("2532"),i("a9e3"),i("d3b7"),i("159b"),i("c7cd"),i("5319"),{aligntype:"center",datasheet:"",defwidth:"",displaymark:1,fixed:0,itemcode:"",itemname:"",minwidth:"100px",overflow:1,sortable:0}),I={formcode:"SYSM07B13List",item:[{itemcode:"modulecode",itemname:"模块编码",minwidth:"70",displaymark:1,overflow:1,datasheet:"CiFormCustom.modulecode"},{itemcode:"frmname",itemname:"界面名称",minwidth:"70",displaymark:1,overflow:1,datasheet:"CiFormCustom.frmname"},{itemcode:"enabledmark",itemname:"有效",minwidth:"70",displaymark:1,overflow:1,datasheet:"CiFormCustom.enabledmark"},{itemcode:"summary",itemname:"摘要",minwidth:"70",displaymark:1,overflow:1,datasheet:"CiFormCustom.summary"},{itemcode:"createdate",itemname:"创建日期",minwidth:"70",displaymark:1,sortable:1,overflow:1,datasheet:"CiFormCustom.createdate"},{itemcode:"lister",itemname:"制表",minwidth:"70",displaymark:1,overflow:1,datasheet:"CiFormCustom.lister"}]},D={formcode:"SYSM07B13Item",item:[{itemcode:"itemcode",itemname:"编码",minwidth:"70",displaymark:1,overflow:1,editmark:1},{itemcode:"itemname",itemname:"名称",minwidth:"70",displaymark:1,overflow:1,editmark:1},{itemcode:"datasheet",itemname:"数据库字段",minwidth:"70",displaymark:1,overflow:1,editmark:1},{itemcode:"minwidth",itemname:"最小宽度",minwidth:"70",displaymark:1,overflow:1,editmark:1},{itemcode:"defwidth",itemname:"默认宽度",minwidth:"70",displaymark:1,overflow:1,editmark:1},{itemcode:"displaymark",itemname:"是否显示",minwidth:"70",displaymark:1,overflow:1},{itemcode:"fixed",itemname:"是否固定",minwidth:"70",displaymark:1,overflow:1},{itemcode:"sortable",itemname:"是否排序",minwidth:"70",displaymark:1,overflow:1},{itemcode:"aligntype",itemname:"位置",minwidth:"70",displaymark:1,overflow:1,editmark:1}]},H={name:"Elitem",components:{},props:["tabsHeight","formdata","lstitem","idx","formstate","formtemplateItem"],data:function(){var t=this;return{lst:[],keynum:0,tableHeight:0,nowitemtaxrate:0,multipleSelection:[],isEditOk:!0,tableForm:D,customList:[],editmarkfiles:[],usetype:0,columsData:[],columnHidden:[],copyText:"",checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},editOption:{cellValueChange:function(e){e.row;var i=e.column;t.editmarkfiles.includes(i.field)}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},cellAutofillOption:{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData,a=Object.keys(i[0])[1];if(!t.editmarkfiles.includes(a))return!1},afterAutofill:function(e){e.sourceSelectionData;for(var i=e.targetSelectionData,a=0;a<i.length;a++){var o=i[a];t.lst.findIndex((function(t){return t.rowKeys==o.rowKeys}))}}},clipboardOption:{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;e.selectionRangeIndexes,e.selectionRangeKeys;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}}}},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){this.formdata.id?(console.log(Object(_["a"])(this.lstitem)),this.lst="string"==typeof this.lstitem?JSON.parse(this.lstitem).th.content:this.lstitem.th.content,this.usetype="string"==typeof this.lstitem?JSON.parse(this.lstitem).th.type:1):this.lst=[]},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i)}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this,e=D;this.$getColumn(D.formcode,e).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}))},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0,renderBodyCell:function(i,a){var o=i.row,l=(i.column,i.rowIndex,null);return"displaymark"==t.itemcode||"fixed"==t.itemcode||"sortable"==t.itemcode?(l=a("el-checkbox",{attrs:{"true-label":1,"false-label":0,size:"mini"},model:{value:o[t.itemcode],callback:function(i){e.$set(o,t.itemcode,i)}}}),l):o[t.itemcode]}};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.columsData=i,this.keynum+=1},pasteFun:function(t,e){var i=[];for(var a in t[0])i.push(a);for(var o=0;o<t.length;o++)for(var l=t[o],s=0;s<i.length;s++){var n=i[s];this.lst[e+o][n]=l[n].replace(/^\s*|\s*$/g,"")}},catchHight:function(){var t=this;this.$nextTick((function(){t.tabsHeight&&(t.tableHeight=t.tabsHeight.height-85)}))},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"rowKeys"),this.lst.push(e)}},getAdd:function(t){var e=Object.assign({},T);this.lst.push(e)}}},B=H,z=(i("aee8"),Object(u["a"])(B,O,F,!1,null,"67a49364",null)),N=z.exports,V=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form",style:{height:t.tableHeight+"px"}},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{showcontent:["add","moveup","movedown","delete","copyrow","refresh","column"],formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,getColumn:t.getColumn},scopedSlots:t._u([{key:"right",fn:function(){return[i("el-checkbox",{staticStyle:{"line-height":"20px"},attrs:{label:"使用",size:"mini","true-label":1,"false-label":0,border:""},model:{value:t.usetype,callback:function(e){t.usetype=e},expression:"usetype"}})]},proxy:!0}])}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight-40,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,editOption:t.editOption,"fixed-footer":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1)],1)},M=[],E={name:"Elitem",components:{},props:["tabsHeight","formdata","lstitem","idx","formstate","formtemplateItem"],data:function(){var t=this;return{lst:[],keynum:0,tableHeight:0,nowitemtaxrate:0,multipleSelection:[],isEditOk:!0,tableForm:D,customList:[],editmarkfiles:[],usetype:0,columsData:[],columnHidden:[],copyText:"",checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},editOption:{cellValueChange:function(e){e.row;var i=e.column;t.editmarkfiles.includes(i.field)}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},cellAutofillOption:{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData,a=Object.keys(i[0])[1];if(!t.editmarkfiles.includes(a))return!1},afterAutofill:function(e){e.sourceSelectionData;for(var i=e.targetSelectionData,a=0;a<i.length;a++){var o=i[a];t.lst.findIndex((function(t){return t.rowKeys==o.rowKeys}))}}},clipboardOption:{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;e.selectionRangeIndexes,e.selectionRangeKeys;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}}}},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){this.formdata.id?(this.lst="string"==typeof this.lstitem?JSON.parse(this.lstitem).list.content:this.lstitem.list.content,this.usetype="string"==typeof this.lstitem?JSON.parse(this.lstitem).list.type:1):this.lst=[]},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i)}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this,e=D;this.$getColumn(D.formcode,e).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}))},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0,renderBodyCell:function(i,a){var o=i.row,l=(i.column,i.rowIndex,null);return"displaymark"==t.itemcode||"fixed"==t.itemcode||"sortable"==t.itemcode?(l=a("el-checkbox",{attrs:{"true-label":1,"false-label":0,size:"mini"},model:{value:o[t.itemcode],callback:function(i){e.$set(o,t.itemcode,i)}}}),l):o[t.itemcode]}};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.columsData=i,this.keynum+=1},pasteFun:function(t,e){var i=[];for(var a in t[0])i.push(a);for(var o=0;o<t.length;o++)for(var l=t[o],s=0;s<i.length;s++){var n=i[s];this.lst[e+o][n]=l[n].replace(/^\s*|\s*$/g,"")}},catchHight:function(){var t=this;this.$nextTick((function(){t.tabsHeight&&(t.tableHeight=t.tabsHeight.height-85)}))},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"rowKeys"),this.lst.push(e)}},getAdd:function(t){var e=Object.assign({},T);this.lst.push(e)}}},K=E,A=(i("98e3"),Object(u["a"])(K,V,M,!1,null,"6ba3bf2f",null)),P=A.exports,L=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form",style:{height:t.tableHeight+"px"}},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{showcontent:["add","moveup","movedown","delete","copyrow","refresh","column"],formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection},on:{bindData:function(e){return t.$emit("bindData")},clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,getColumn:t.getColumn},scopedSlots:t._u([{key:"right",fn:function(){return[i("el-checkbox",{staticStyle:{"line-height":"20px"},attrs:{label:"使用",size:"mini","true-label":1,"false-label":0,border:""},model:{value:t.usetype,callback:function(e){t.usetype=e},expression:"usetype"}})]},proxy:!0}])}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight-40,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,editOption:t.editOption,"fixed-footer":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1)],1)},j=[],q={name:"Elitem",components:{},props:["tabsHeight","formdata","lstitem","idx","formstate","formtemplateItem"],data:function(){var t=this;return{lst:[],keynum:0,tableHeight:0,nowitemtaxrate:0,multipleSelection:[],isEditOk:!0,tableForm:D,customList:[],editmarkfiles:[],usetype:0,columsData:[],columnHidden:[],copyText:"",checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},editOption:{cellValueChange:function(e){e.row;var i=e.column;t.editmarkfiles.includes(i.field)}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},cellAutofillOption:{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData,a=Object.keys(i[0])[1];if(!t.editmarkfiles.includes(a))return!1},afterAutofill:function(e){e.sourceSelectionData;for(var i=e.targetSelectionData,a=0;a<i.length;a++){var o=i[a];t.lst.findIndex((function(t){return t.rowKeys==o.rowKeys}))}}},clipboardOption:{copy:!0,paste:!0,cut:!1,delete:!1,afterCopy:function(e){var i=e.data;e.selectionRangeIndexes,e.selectionRangeKeys;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}}}},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){this.formdata.id?(this.lst="string"==typeof this.lstitem?JSON.parse(this.lstitem).item.content:this.lstitem.item.content,this.usetype="string"==typeof this.lstitem?JSON.parse(this.lstitem).item.type:1):this.lst=[]},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i)}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){var t=this,e=D;this.$getColumn(D.formcode,e).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.editmarkfiles=[];for(var i=0;i<t.tableForm.item.length;i++){var a=t.tableForm.item[i];a.editmark&&t.editmarkfiles.push(a.itemcode)}t.$forceUpdate()}))},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0,renderBodyCell:function(i,a){var o=i.row,l=(i.column,i.rowIndex,null);return"displaymark"==t.itemcode||"fixed"==t.itemcode||"sortable"==t.itemcode?(l=a("el-checkbox",{attrs:{"true-label":1,"false-label":0,size:"mini"},model:{value:o[t.itemcode],callback:function(i){e.$set(o,t.itemcode,i)}}}),l):o[t.itemcode]}};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.columsData=i,this.keynum+=1},pasteFun:function(t,e){var i=[];for(var a in t[0])i.push(a);for(var o=0;o<t.length;o++)for(var l=t[o],s=0;s<i.length;s++){var n=i[s];this.lst[e+o][n]=l[n].replace(/^\s*|\s*$/g,"")}},catchHight:function(){var t=this;this.$nextTick((function(){t.tabsHeight&&(t.tableHeight=t.tabsHeight.height-85)}))},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"rowKeys"),this.lst.push(e)}},getAdd:function(t){var e=Object.assign({},T);this.lst.push(e)}}},Y=q,J=(i("fa84"),Object(u["a"])(Y,L,j,!1,null,"9ef8ab3c",null)),W=J.exports,G=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"flex",style:{height:t.tableHeight+"px"}},[i("div",{staticClass:"left",staticStyle:{width:"200px"}},[i("div",{staticClass:"title"},[t._v("行数")]),i("div",{staticClass:"Rbody"},[i("draggable",{model:{value:t.lst,callback:function(e){t.lst=e},expression:"lst"}},[i("transition-group",[t._l(t.lst,(function(e,a){return[i("div",{key:a,staticClass:"Ritem",class:t.Ractive==a?"active":"",on:{click:function(e){t.Ractive=a}}},[i("div",{staticClass:"Tips",style:{background:"divider"==e.type?"#66b1ff":"#d966ff"}},[t._v(" "+t._s("divider"==e.type?"分割线":"表单")+" ")]),t._v(" 第"+t._s(a+1)+"行 "),t.lst.length>1?i("i",{staticClass:"el-icon-close closeBtn",on:{click:function(e){t.lst.splice(a,1),t.Ractive=0}}}):t._e()])]}))],2)],1),i("div",{staticClass:"addRow",on:{click:t.addRow}},[i("i",{staticClass:"el-icon-plus"}),t._v(" 添加行 ")])],1)]),i("div",{staticClass:"center"},[i("div",{staticClass:"title"},[i("span",[t._v("表单内容")]),i("el-checkbox",{staticStyle:{"line-height":"20px",float:"right",margin:"-5px 10px 0 0"},attrs:{label:"使用",size:"mini","true-label":1,"false-label":0,border:""},model:{value:t.usetype,callback:function(e){t.usetype=e},expression:"usetype"}})],1),i("div",{staticClass:"Cbody"},["divider"==t.lst[t.Ractive].type?i("div",[i("div",{staticClass:"Citem",on:{click:function(e){return t.setdivider(t.lst[t.Ractive])}}},[t._v("分割线")])]):i("div",{staticStyle:{display:"flex","flex-wrap":"wrap","align-items":"flex-start"}},[i("draggable",{model:{value:t.lst[t.Ractive].rowitem,callback:function(e){t.$set(t.lst[t.Ractive],"rowitem",e)},expression:"lst[Ractive].rowitem"}},[i("transition-group",[t._l(t.lst[t.Ractive].rowitem,(function(e,a){return[i("div",{key:a,staticClass:"Citem",class:t.Cactive==a?"active":"",staticStyle:{display:"inline-block"},on:{click:function(i){t.Cactive=a,t.setform=e,t.setType(t.setform)}}},[t._v(" "+t._s(e.label?e.label:"名称")+" "),i("i",{staticClass:"el-icon-close closeBtn",on:{click:function(e){return t.lst[t.Ractive].rowitem.splice(a,1)}}})])]}))],2)],1),i("div",{staticClass:"addContent",on:{click:function(e){return t.addContent()}}},[i("i",{staticClass:"el-icon-plus"}),t._v(" 添加内容 ")])],1)])]),i("div",{staticClass:"right"},[i("div",{staticClass:"title"},[t._v("设置")]),i("div",{staticClass:"Sbody"},["divider"==t.lst[t.Ractive].type?i("div",[i("el-form",{staticClass:"setform",attrs:{model:t.lst[t.Ractive],"label-width":"80px"}},[i("el-form-item",{attrs:{label:"内容"}},[i("el-input",{staticStyle:{width:"90%"},attrs:{placeholder:"请输入内容",clearable:"",size:"small"},model:{value:t.lst[t.Ractive].label,callback:function(e){t.$set(t.lst[t.Ractive],"label",e)},expression:"lst[Ractive].label"}})],1),i("el-form-item",{attrs:{label:"位置"}},[i("el-select",{staticStyle:{width:"90%"},attrs:{placeholder:"请选择位置",size:"small"},model:{value:t.lst[t.Ractive].center,callback:function(e){t.$set(t.lst[t.Ractive],"center",e)},expression:"lst[Ractive].center"}},[i("el-option",{attrs:{label:"左边",value:"left"}}),i("el-option",{attrs:{label:"右边",value:"right"}}),i("el-option",{attrs:{label:"居中",value:"center"}})],1)],1)],1)],1):i("div",[i("el-form",{ref:"setform",staticClass:"setform",attrs:{model:t.setform,"label-width":"80px",rules:t.formRules}},[i("el-form-item",{attrs:{label:"类型"}},[i("el-select",{staticStyle:{width:"90%"},attrs:{placeholder:"请选择类型",size:"small"},model:{value:t.setform.type,callback:function(e){t.$set(t.setform,"type",e)},expression:"setform.type"}},[i("el-option",{attrs:{label:"输入框",value:"input"}}),i("el-option",{attrs:{label:"下拉选择",value:"select"}}),i("el-option",{attrs:{label:"日期",value:"date"}}),i("el-option",{attrs:{label:"勾选框",value:"checkbox"}}),i("el-option",{attrs:{label:"计数器",value:"number"}}),i("el-option",{attrs:{label:"文本域",value:"textarea"}}),i("el-option",{attrs:{label:"文本",value:"text"}}),i("el-option",{attrs:{label:"查询",value:"autocomplete"}}),i("el-option",{attrs:{label:"字典",value:"dictionary"}})],1)],1),i("div",{on:{click:function(e){return t.cleValidate("label")}}},[i("el-form-item",{attrs:{label:"标签名称",prop:"label"}},[i("el-input",{staticStyle:{width:"90%"},attrs:{placeholder:"请输入标签名称",clearable:"",size:"small"},model:{value:t.setform.label,callback:function(e){t.$set(t.setform,"label",e)},expression:"setform.label"}})],1)],1),i("div",{on:{click:function(e){return t.cleValidate("code")}}},[i("el-form-item",{attrs:{label:"标签字段",prop:"code"}},[i("el-input",{staticStyle:{width:"90%"},attrs:{placeholder:"请输入标签字段",clearable:"",size:"small"},model:{value:t.setform.code,callback:function(e){t.$set(t.setform,"code",e)},expression:"setform.code"}})],1)],1),i("el-form-item",{attrs:{label:"标签宽度"}},[i("el-input",{staticStyle:{width:"90%"},attrs:{placeholder:"默认100px",clearable:"",size:"small"},model:{value:t.setform.labelwidth,callback:function(e){t.$set(t.setform,"labelwidth",e)},expression:"setform.labelwidth"}})],1),i("el-form-item",{attrs:{label:"栅格"}},[i("el-input-number",{attrs:{controls:!0,type:"number",min:0,"controls-position":"right",size:"small"},model:{value:t.setform.col,callback:function(e){t.$set(t.setform,"col",e)},expression:"setform.col"}}),i("el-checkbox",{staticStyle:{"margin-left":"15px"},attrs:{label:"必填","true-label":1,"false-label":0,size:"mini"},model:{value:t.setform.required,callback:function(e){t.$set(t.setform,"required",e)},expression:"setform.required"}})],1),"dictionary"==t.setform.type?i("div",[i("el-form-item",{attrs:{label:"字典字段"}},[i("el-input",{staticStyle:{width:"90%"},attrs:{placeholder:"请输入字典字段",clearable:"",size:"small"},model:{value:t.setform.billcode,callback:function(e){t.$set(t.setform,"billcode",e)},expression:"setform.billcode"}})],1)],1):t._e(),"select"==t.setform.type?i("div",[i("el-form-item",{attrs:{label:"函数"}},[i("el-input",{staticStyle:{width:"90%"},attrs:{placeholder:"请输入函数",clearable:"",size:"small"},model:{value:t.setform.methods,callback:function(e){t.$set(t.setform,"methods",e)},expression:"setform.methods"}})],1),i("el-form-item",{attrs:{label:"下拉内容"}},[i("el-input",{staticStyle:{width:"90%"},attrs:{placeholder:"请输入下拉内容",clearable:"",type:"textarea",size:"small",autosize:{minRows:2,maxRows:4}},on:{input:function(e){return t.changeOption(t.setform)}},model:{value:t.isoptions,callback:function(e){t.isoptions=e},expression:"isoptions"}})],1)],1):t._e(),"autocomplete"==t.setform.type?i("div",[i("el-form-item",{attrs:{label:"查询内容"}},[i("el-select",{staticStyle:{width:"90%"},attrs:{placeholder:"请选择类型",size:"small"},model:{value:t.setform.searchtype,callback:function(e){t.$set(t.setform,"searchtype",e)},expression:"setform.searchtype"}},[i("el-option",{attrs:{label:"货品信息",value:"goods"}}),i("el-option",{attrs:{label:"仓库信息",value:"store"}}),i("el-option",{attrs:{label:"工序",value:"procedure"}}),i("el-option",{attrs:{label:"角色工序",value:"roleproc"}}),i("el-option",{attrs:{label:"客户",value:"customer"}}),i("el-option",{attrs:{label:"供应商",value:"supplier"}}),i("el-option",{attrs:{label:"生产车间",value:"workshop"}}),i("el-option",{attrs:{label:"外协厂商",value:"factory"}}),i("el-option",{attrs:{label:"其他部门",value:"branch"}}),i("el-option",{attrs:{label:"潜在客户",value:"prospects"}})],1)],1)],1):t._e()],1)],1)])]),i("el-dialog",{attrs:{title:"类型设置",visible:t.RowVisible,"append-to-body":!0,width:"400px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.RowVisible=e}}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择类型"},model:{value:t.Rtype,callback:function(e){t.Rtype=e},expression:"Rtype"}},[i("el-option",{attrs:{label:"表单",value:"form"}}),i("el-option",{attrs:{label:"分割线",value:"divider"}})],1),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitType}},[t._v("确 定")]),i("el-button",{attrs:{size:"small"},on:{click:function(e){t.RowVisible=!1}}},[t._v("取 消")])],1)],1)],1)},U=[],X=i("b76a"),Q=i.n(X),Z={components:{draggable:Q.a},props:["tabsHeight","formdata","lstitem"],data:function(){return{tableHeight:0,lst:[{type:"form",rowitem:[]}],clst:[],Rtype:"",Ractive:0,Cactive:-1,setform:{col:5},RowVisible:!1,formRules:{code:[{required:!0,trigger:"blur",message:"字段为必填项"}]},isoptions:"",usetype:0}},watch:{lstitem:function(t,e){this.formdata.id?(this.lst="string"==typeof this.lstitem?JSON.parse(this.lstitem).header.content:this.lstitem.header.content,this.usetype="string"==typeof this.lstitem?JSON.parse(this.lstitem).header.type:1):this.lst=[{type:"form",rowitem:[]}]}},mounted:function(){this.catchHight()},methods:{addRow:function(){this.Rtype="form",this.RowVisible=!0},submitType:function(){if("divider"==this.Rtype)var t={type:"divider",label:"",center:""};else t={type:this.Rtype,rowitem:[]};this.lst.push(t),this.Ractive=this.lst.length-1,this.RowVisible=!1},addContent:function(){var t={col:5,type:"input",code:"",label:""};this.lst[this.Ractive].rowitem.push(t),this.Cactive=this.lst[this.Ractive].rowitem.length-1,this.setform=this.lst[this.Ractive].rowitem[this.Cactive]},setdivider:function(t){},setType:function(t){if("select"==t.type){this.isoptions="";for(var e=0;e<t.options.length;e++)this.isoptions+=t.options[e].value+",";var i=/,$/gi;this.isoptions=this.isoptions.replace(i,"")}},changeOption:function(t){var e=this.isoptions.split(",");t.options=[];for(var i=0;i<e.length;i++){var a={label:e[i],value:e[i]};t.options.push(a)}},catchHight:function(){var t=this;this.$nextTick((function(){t.tabsHeight&&(t.tableHeight=t.tabsHeight.height-115)}))},cleValidate:function(t){this.$refs.setform.clearValidate(t)}}},tt=Z,et=(i("7973"),Object(u["a"])(tt,G,U,!1,null,"2967394a",null)),it=et.exports,at=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"flex",style:{height:t.tableHeight+"px"}},[i("div",{staticClass:"left",staticStyle:{width:"200px"}},[i("div",{staticClass:"title"},[t._v("行数")]),i("div",{staticClass:"Rbody"},[i("draggable",{model:{value:t.lst,callback:function(e){t.lst=e},expression:"lst"}},[i("transition-group",[t._l(t.lst,(function(e,a){return[i("div",{key:a,staticClass:"Ritem",class:t.Ractive==a?"active":"",on:{click:function(e){t.Ractive=a}}},[i("div",{staticClass:"Tips",style:{background:"divider"==e.type?"#66b1ff":"form"==e.type?"#d966ff":"#795548"}},[t._v(" "+t._s("divider"==e.type?"分割线":"form"==e.type?"表单":"通用")+" ")]),t._v(" 第"+t._s(a+1)+"行 "),t.lst.length>1?i("i",{staticClass:"el-icon-close closeBtn",on:{click:function(e){t.lst.splice(a,1),t.Ractive=0}}}):t._e()])]}))],2)],1),i("div",{staticClass:"addRow",on:{click:t.addRow}},[i("i",{staticClass:"el-icon-plus"}),t._v(" 添加行 ")])],1)]),i("div",{staticClass:"center"},[i("div",{staticClass:"title"},[i("span",[t._v("表尾内容")]),i("el-checkbox",{staticStyle:{"line-height":"20px",float:"right",margin:"-5px 10px 0 0"},attrs:{label:"使用",size:"mini","true-label":1,"false-label":0,border:""},model:{value:t.usetype,callback:function(e){t.usetype=e},expression:"usetype"}})],1),i("div",{staticClass:"Cbody"},["divider"==t.lst[t.Ractive].type?i("div",[i("div",{staticClass:"Citem",on:{click:function(e){return t.setdivider(t.lst[t.Ractive])}}},[t._v("分割线")])]):i("div",{staticStyle:{display:"flex","flex-wrap":"wrap","align-items":"flex-start"}},[i("draggable",{model:{value:t.lst[t.Ractive].rowitem,callback:function(e){t.$set(t.lst[t.Ractive],"rowitem",e)},expression:"lst[Ractive].rowitem"}},[i("transition-group",[t._l(t.lst[t.Ractive].rowitem,(function(e,a){return[i("div",{key:a,staticClass:"Citem",class:t.Cactive==a?"active":"",staticStyle:{display:"inline-block"},on:{click:function(i){t.Cactive=a,t.setform=e,t.setType(t.setform)}}},[t._v(" "+t._s(e.label?e.label:"名称")+" "),i("i",{staticClass:"el-icon-close closeBtn",on:{click:function(e){return t.lst[t.Ractive].rowitem.splice(a,1)}}})])]}))],2)],1),i("div",{staticClass:"addContent",on:{click:function(e){return t.addContent()}}},[i("i",{staticClass:"el-icon-plus"}),t._v(" 添加内容 ")])],1)])]),i("div",{staticClass:"right"},[i("div",{staticClass:"title"},[t._v("设置")]),i("div",{staticClass:"Sbody"},["divider"==t.lst[t.Ractive].type?i("div",[i("el-form",{staticClass:"setform",attrs:{model:t.lst[t.Ractive],"label-width":"80px"}},[i("el-form-item",{attrs:{label:"内容"}},[i("el-input",{staticStyle:{width:"90%"},attrs:{placeholder:"请输入内容",clearable:"",size:"small"},model:{value:t.lst[t.Ractive].label,callback:function(e){t.$set(t.lst[t.Ractive],"label",e)},expression:"lst[Ractive].label"}})],1),i("el-form-item",{attrs:{label:"位置"}},[i("el-select",{staticStyle:{width:"90%"},attrs:{placeholder:"请选择位置",size:"small"},model:{value:t.lst[t.Ractive].center,callback:function(e){t.$set(t.lst[t.Ractive],"center",e)},expression:"lst[Ractive].center"}},[i("el-option",{attrs:{label:"左边",value:"left"}}),i("el-option",{attrs:{label:"右边",value:"right"}}),i("el-option",{attrs:{label:"居中",value:"center"}})],1)],1)],1)],1):i("div",[i("el-form",{ref:"setform",staticClass:"setform",attrs:{model:t.setform,"label-width":"80px",rules:t.formRules}},[i("el-form-item",{attrs:{label:"类型"}},["foot"==t.lst[t.Ractive].type?i("el-input",{staticStyle:{width:"90%"},attrs:{value:"文本",placeholder:"请输入字段",readonly:"",size:"small"}}):i("el-select",{staticStyle:{width:"90%"},attrs:{placeholder:"请选择类型",size:"small"},model:{value:t.setform.type,callback:function(e){t.$set(t.setform,"type",e)},expression:"setform.type"}},[i("el-option",{attrs:{label:"输入框",value:"input"}}),i("el-option",{attrs:{label:"下拉选择",value:"select"}}),i("el-option",{attrs:{label:"日期",value:"date"}}),i("el-option",{attrs:{label:"勾选框",value:"checkbox"}}),i("el-option",{attrs:{label:"计数器",value:"number"}}),i("el-option",{attrs:{label:"文本域",value:"textarea"}}),i("el-option",{attrs:{label:"文本",value:"text"}}),i("el-option",{attrs:{label:"查询",value:"autocomplete"}}),i("el-option",{attrs:{label:"字典",value:"dictionary"}})],1)],1),i("div",{on:{click:function(e){return t.cleValidate("label")}}},[i("el-form-item",{attrs:{label:"标签名称",prop:"label"}},[i("el-input",{staticStyle:{width:"90%"},attrs:{placeholder:"请输入标签名称",clearable:"",size:"small"},model:{value:t.setform.label,callback:function(e){t.$set(t.setform,"label",e)},expression:"setform.label"}})],1)],1),i("div",{on:{click:function(e){return t.cleValidate("label")}}},[i("el-form-item",{attrs:{label:"标签字段",prop:"code"}},[i("el-input",{staticStyle:{width:"90%"},attrs:{placeholder:"请输入标签字段",clearable:"",size:"small"},model:{value:t.setform.code,callback:function(e){t.$set(t.setform,"code",e)},expression:"setform.code"}})],1)],1),i("el-form-item",{attrs:{label:"标签宽度"}},[i("el-input",{staticStyle:{width:"90%"},attrs:{placeholder:"默认100px",clearable:"",size:"small"},model:{value:t.setform.labelwidth,callback:function(e){t.$set(t.setform,"labelwidth",e)},expression:"setform.labelwidth"}})],1),i("el-form-item",{attrs:{label:"栅格"}},[i("el-input-number",{attrs:{controls:!0,type:"number",min:0,"controls-position":"right",size:"small"},model:{value:t.setform.col,callback:function(e){t.$set(t.setform,"col",e)},expression:"setform.col"}}),i("el-checkbox",{staticStyle:{"margin-left":"15px"},attrs:{label:"必填","true-label":1,"false-label":0,size:"mini"},model:{value:t.setform.required,callback:function(e){t.$set(t.setform,"required",e)},expression:"setform.required"}})],1),"dictionary"==t.setform.type?i("div",[i("el-form-item",{attrs:{label:"字典字段"}},[i("el-input",{staticStyle:{width:"90%"},attrs:{placeholder:"请输入字典字段",clearable:"",size:"small"},model:{value:t.setform.billcode,callback:function(e){t.$set(t.setform,"billcode",e)},expression:"setform.billcode"}})],1)],1):t._e(),"select"==t.setform.type?i("div",[i("el-form-item",{attrs:{label:"函数"}},[i("el-input",{staticStyle:{width:"90%"},attrs:{placeholder:"请输入函数",clearable:"",size:"small"},model:{value:t.setform.methods,callback:function(e){t.$set(t.setform,"methods",e)},expression:"setform.methods"}})],1),i("el-form-item",{attrs:{label:"下拉内容"}},[i("el-input",{staticStyle:{width:"90%"},attrs:{placeholder:"请输入下拉内容",clearable:"",type:"textarea",size:"small",autosize:{minRows:2,maxRows:4}},on:{input:function(e){return t.changeOption(t.setform)}},model:{value:t.isoptions,callback:function(e){t.isoptions=e},expression:"isoptions"}})],1)],1):t._e(),"autocomplete"==t.setform.type?i("div",[i("el-form-item",{attrs:{label:"查询内容"}},[i("el-select",{staticStyle:{width:"90%"},attrs:{placeholder:"请选择类型",size:"small"},model:{value:t.setform.searchtype,callback:function(e){t.$set(t.setform,"searchtype",e)},expression:"setform.searchtype"}},[i("el-option",{attrs:{label:"货品信息",value:"goods"}}),i("el-option",{attrs:{label:"仓库信息",value:"store"}}),i("el-option",{attrs:{label:"工序",value:"procedure"}}),i("el-option",{attrs:{label:"角色工序",value:"roleproc"}}),i("el-option",{attrs:{label:"客户",value:"customer"}}),i("el-option",{attrs:{label:"供应商",value:"supplier"}}),i("el-option",{attrs:{label:"生产车间",value:"workshop"}}),i("el-option",{attrs:{label:"外协厂商",value:"factory"}}),i("el-option",{attrs:{label:"其他部门",value:"branch"}}),i("el-option",{attrs:{label:"潜在客户",value:"prospects"}})],1)],1)],1):t._e()],1)],1)])]),i("el-dialog",{attrs:{title:"类型设置",visible:t.RowVisible,"append-to-body":!0,width:"400px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.RowVisible=e}}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择类型"},model:{value:t.Rtype,callback:function(e){t.Rtype=e},expression:"Rtype"}},[i("el-option",{attrs:{label:"表单",value:"form"}}),i("el-option",{attrs:{label:"底部通用",value:"foot"}}),i("el-option",{attrs:{label:"分割线",value:"divider"}})],1),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitType}},[t._v("确 定")]),i("el-button",{attrs:{size:"small"},on:{click:function(e){t.RowVisible=!1}}},[t._v("取 消")])],1)],1)],1)},ot=[],lt={components:{draggable:Q.a},props:["tabsHeight","formdata","lstitem"],data:function(){return{tableHeight:0,lst:[{type:"form",rowitem:[]}],clst:[],Rtype:"",Ractive:0,Cactive:-1,setform:{col:5},RowVisible:!1,formRules:{label:[{required:!0,trigger:"blur",message:"名称为必填项"}],code:[{required:!0,trigger:"blur",message:"字段为必填项"}]},isoptions:"",usetype:0}},watch:{lstitem:function(t,e){this.formdata.id?(this.lst="string"==typeof this.lstitem?JSON.parse(this.lstitem).footer.content:this.lstitem.footer.content,this.usetype="string"==typeof this.lstitem?JSON.parse(this.lstitem).footer.type:1):this.lst=[{type:"form",rowitem:[]}]}},mounted:function(){this.catchHight()},methods:{addRow:function(){this.Rtype="form",this.RowVisible=!0},submitType:function(){if(console.log(this.Rtype),"divider"==this.Rtype)var t={type:"divider",label:"",center:""};else if("foot"==this.Rtype)t={type:"foot",rowitem:[{col:4,type:"text",code:"createby",label:"创建人"},{col:4,type:"text",code:"createdate",label:"创建日期"},{col:4,type:"text",code:"lister",label:"制表"},{col:4,type:"text",code:"modifydate",label:"修改日期"},{col:4,type:"text",code:"assessor",label:"审核"},{col:4,type:"text",code:"assessdate",label:"审核日期"}]};else t={type:this.Rtype,rowitem:[]};this.lst.push(t),this.Ractive=this.lst.length-1,this.RowVisible=!1},addContent:function(){console.log(this.Ractive);var t={col:5,type:"input",code:"",label:""};this.lst[this.Ractive].rowitem.push(t),this.Cactive=this.lst[this.Ractive].rowitem.length-1,this.setform=this.lst[this.Ractive].rowitem[this.Cactive]},setdivider:function(t){},setType:function(t){if("select"==t.type){this.isoptions="";for(var e=0;e<t.options.length;e++)this.isoptions+=t.options[e].value+",";var i=/,$/gi;this.isoptions=this.isoptions.replace(i,"")}},changeOption:function(t){var e=this.isoptions.split(",");t.options=[];for(var i=0;i<e.length;i++){var a={label:e[i],value:e[i]};t.options.push(a)}},catchHight:function(){var t=this;this.$nextTick((function(){t.tabsHeight&&(t.tableHeight=t.tabsHeight.height-115)}))},cleValidate:function(t){this.$refs.setform.clearValidate(t)}}},st=lt,nt=(i("4f8a"),Object(u["a"])(st,at,ot,!1,null,"0981b9dd",null)),rt=nt.exports,ct={header:{type:0,title:"表单管理",content:[]},footer:{type:1,content:[{rowitem:[{col:22,code:"summary",label:"摘要",type:"input",methods:"",param:""}]},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"}]}]},item:{type:0,content:[]},th:{type:0,content:[]},list:{type:0,content:[]}},dt=i("dcb4"),mt=["id","gengroupid","modulecode","frmcode","frmname","frmcontent","rownum","enabledmark","remark","createby","createbyid","createdate","lister","listerid","modifydate","custom1","custom2","custom3","custom4","custom5"],ut={params:mt},ft=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]}],pt=[],ht={name:"Formedit",components:{FormTemp:dt["a"],EditHeader:R,ThItem:N,ListItem:P,EditItem:W,HeaderItem:it,FootItem:rt},props:["idx","isprocessDialog"],data:function(){return{title:"表单管理",operateBar:ft,processBar:pt,formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).RealName,createby:JSON.parse(window.localStorage.getItem("getInfo")).RealName,enabledmark:1,frmcode:"",frmname:"",frmcontent:{header:{type:0,content:[]},foot:{type:0,content:[]},th:{type:0,content:[]},list:{type:0,content:[]},item:{type:0,content:[]}},rownum:0,summary:"",modulecode:"",gengroupid:""},activeTabs:0,tabsHeight:300,operationVisible:!1,dialogIdx:0,processVisible:!1,processTitle:"",processModel:"",formtemplate:ct,formstate:0,submitting:0}},computed:{formcontainHeight:function(){return window.innerHeight-50-23-50+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp()},mounted:function(){this.bindData(),this.tabsHeight=this.$refs.tabsHeight.getBoundingClientRect()},methods:{bindTemp:function(){var t=this;this.$request.get("/S16M87B1/getBillEntityByCode?key=SYSM07B13").then((function(e){200==e.data.code&&null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):ct,t.formtemplate.footer.type||(t.formtemplate.footer=ct.footer))}))},bindData:function(){var t=this;this.formstate=0,0!=this.idx&&h["a"].get("/system/SYSM07B13/getEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.formstate=t.formdata.assessor?2:1):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")}))},changeIdx:function(t){this.dialogIdx=t},submitForm:function(){var t,e=this;t=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,t.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;this.submitting=1;var e={header:{type:this.$refs.headerItem.usetype,content:this.$refs.headerItem.lst},footer:{type:this.$refs.footItem.usetype,content:this.$refs.footItem.lst},th:{type:this.$refs.thItem.usetype,content:this.$refs.thItem.lst},list:{type:this.$refs.listItem.usetype,content:this.$refs.listItem.lst},item:{type:this.$refs.editItem.usetype,content:this.$refs.editItem.lst}};this.formdata.frmcontent=e;var i={};i=this.$getParam(ut,i,this.formdata),0==this.idx?y.add(i).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败")})):y.update(i).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败")})),setTimeout((function(){t.submitting=0}),500)},closeForm:function(){this.isDialog||this.isprocessDialog?this.$emit("closeDialog"):this.$emit("closeForm")},deleteForm:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){y.delete(t)})).catch((function(){}))},approval:function(){y.approval(this)},clickMethods:function(t){this[t.meth](t.param)},flowable:function(){this.$refs.flowable.action()},operateBill:function(t){this.operationVisible=!0,this.dialogIdx=0,this.processModel=t,console.log(this.processModel,"ss")},processBill:function(t){this.processVisible=!0,this.processTitle=t.label,this.processModel=t.code},changeBillType:function(){formdata.item=[]},handleChange:function(t){t.length>0?this.formdata.gengroupid=t[t.length-1]:this.formdata.gengroupid="0"}}},bt=ht,vt=(i("29cc"),Object(u["a"])(bt,b,v,!1,null,"c64ed248",null)),gt=vt.exports,yt=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("ve-table",{key:t.keynum,ref:"tableList",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize,pageSizes:[10,20,100,500]},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1),i("div",{staticStyle:{"margin-right":"40px"}},[i("Scene",{ref:"scene",attrs:{code:t.tableForm.formcode},on:{bindData:t.bindData}})],1)])],1)},wt=[],xt={components:{},props:["online","progressData","formtemplate"],data:function(){var t=this;return{lst:[],total:0,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:I,customList:[],customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},methods:{bindData:function(){var t=this;this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1));var e="/S16M87B1/getPageTh";h["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code?(t.lst=e.data.data.list,t.total=e.data.data.total):t.$message.warning(e.data.msg||"请求失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(n["a"])(Object(s["a"])().mark((function e(){var i;return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:i=I,t.formtemplate.list.type&&(i.item=t.formtemplate.list.content),t.$getColumn(t.tableForm.formcode,i).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}));case 3:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row,l=(i.column,i.rowIndex,"");return"createdate"==t.itemcode?e.$options.filters.dateFormat(o[t.itemcode]):"enabledmark"==t.itemcode?(l=1==o[t.itemcode]?a("el-tag",{attrs:{size:"medium"}},["正常"]):a("el-tag",{attrs:{type:"warning",size:"medium"}},["停用"]),l):"modulecode"==t.itemcode?(l=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.id)}}},[o[t.itemcode]?o[t.itemcode]:"单据编码"]),l):o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,["refno","taxamount","quantity"])},countCellData:function(){var t=this.$refs.tableList.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableList.getRangeCellSelection().selectionRangeIndexes,i=["quantity","taxprice","taxamount"];this.$countCellData(this,i,t,e)},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){this.$search(this,t)},advancedSearch:function(t){this.$advancedSearch(this,t)},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}},showForm:function(t){this.$emit("showForm",t)},btnExport:function(){this.$btnExport(this.lst,this.tableForm,"自定义界面")}}},kt=xt,St=(i("70a8"),Object(u["a"])(kt,yt,wt,!1,null,"54dca3a4",null)),Ct=St.exports,$t=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[i("div",{staticStyle:{width:"100%"}},[i("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth,rules:t.formRules}},[i("el-row",[i("el-col",{attrs:{span:12}},[i("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[i("el-form-item",{attrs:{label:"分组名称",prop:"groupname"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入分组名称",size:"small"},on:{input:t.writeCode},model:{value:t.formdata.groupname,callback:function(e){t.$set(t.formdata,"groupname",e)},expression:"formdata.groupname"}})],1)],1)]),i("el-col",{attrs:{span:12}},[i("div",{on:{click:function(e){return t.cleValidate("groupcode")}}},[i("el-form-item",{attrs:{label:"分组编码",prop:"groupcode"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入分组编码",size:"small"},model:{value:t.formdata.groupcode,callback:function(e){t.$set(t.formdata,"groupcode",e)},expression:"formdata.groupcode"}})],1)],1)])],1),i("el-row",[i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"排序"}},[i("el-input-number",{staticStyle:{width:"100%"},attrs:{"controls-position":"right",min:0,size:"small"},model:{value:t.formdata.rownum,callback:function(e){t.$set(t.formdata,"rownum",e)},expression:"formdata.rownum"}})],1)],1)],1)],1)],1),i("el-form",{staticClass:"footFormContent",attrs:{"label-width":t.formLabelWidth}},[i("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[i("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"制表"}},[i("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"修改日期"}},[i("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.modifydate)))])])],1)],1)],1)],1)]),i("div",{staticClass:"button-container flex j-end"},[i("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",size:"small"},on:{click:function(e){return t.submitForm("formdata")}}},[t._v(" 保 存")]),i("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.$emit("closeDialog")}}},[t._v(" 关 闭")])],1)])},Rt=[],Ot=(i("4d90"),i("25f0"),i("b0b8")),Ft={name:"Formedit",filters:{dateFormat:function(t){var e=new Date(t),i=e.getFullYear(),a=(e.getMonth()+1).toString().padStart(2,"0"),o=e.getDate().toString().padStart(2,"0"),l=e.getHours().toString().padStart(2,"0"),s=e.getMinutes().toString().padStart(2,"0"),n=e.getSeconds().toString().padStart(2,"0");return"".concat(i,"-").concat(a,"-").concat(o," ").concat(l,":").concat(s,":").concat(n)}},props:["idx","pid"],data:function(){return{formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,parentid:"",modulecode:"SYSM07B13",groupcode:"",groupname:"",rownum:0,remark:"",enabledmark:1},formRules:{modulecode:[{required:!0,trigger:"blur",message:"功能编码为必填项"}],groupcode:[{required:!0,trigger:"blur",message:"分组编码为必填项"}],groupname:[{required:!0,trigger:"blur",message:"分组名称为必填项"}]},formLabelWidth:"100px",dialogVisible:!1,delDialogVisible:!1,option:""}},computed:{formcontainHeight:function(){return"300px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindData()},methods:{bindData:function(){var t=this;0!=this.idx?h["a"].get("/S16M89B1/getEntity?key=".concat(this.idx)).then((function(e){200==e.data.code&&(t.formdata=e.data.data)})):this.formdata.parentid=this.idx},submitForm:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;this.pid&&(this.formdata.parentid=this.pid),0==this.idx?h["a"].post("/S16M89B1/create",JSON.stringify(this.formdata)).then((function(e){t.$emit("bindData"),t.$emit("closeDialog")})):h["a"].post("/S16M89B1/update",JSON.stringify(this.formdata)).then((function(e){t.$emit("bindData"),t.$emit("closeDialog")}))},closeForm:function(){this.$emit("closeForm")},check:function(){console.log("check")},cleValidate:function(t){this.$refs.formdata.clearValidate(t)},writeCode:function(t){Ot.setOptions({checkPolyphone:!1,charCase:1}),this.formdata.groupcode=Ot.getFullChars(t)}}},_t=Ft,Tt=(i("f9a2"),Object(u["a"])(_t,$t,Rt,!1,null,"5eb0d5d7",null)),It=Tt.exports,Dt={name:"SYSM07B13",components:{TableList:Ct,ListHeader:p,FormEdit:gt,Group:It},data:function(){return{title:"SYSM07B13",lst:[],formvisible:!1,idx:0,total:0,tableForm:{},showhelp:!1,pid:"root",treeTitle:"界面分组",gropuFormVisible:!1,treeVisble:!0,groupData:[],defaultProps:{children:"children",label:"label",value:"id"},treeEditable:!1,formtemplate:ct}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},created:function(){},mounted:function(){this.bindData(),this.bindTemp(),this.bindTreeData()},methods:{bindTemp:function(){var t=this;return Object(n["a"])(Object(s["a"])().mark((function e(){return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,h["a"].get("/S16M87B1/getBillEntityByCode?key=SYSM07B13").then((function(e){200==e.data.code?(null!=e.data.data&&(t.formtemplate=e.data.data.frmcontent?JSON.parse(e.data.data.frmcontent):ct),t.$refs.tableList.getColumn()):t.$alert(e.data.msg||"获取页面信息失败",{type:"error"})})).catch((function(e){t.$message.error("请求页面错误")}));case 2:case"end":return e.stop()}}),e)})))()},bindData:function(){var t=this;this.$nextTick((function(){t.$refs.tableList.bindData()}))},bindTreeData:function(){var t=this;h["a"].get("/S16M89B1/getListByModuleCode?Code=SYSM07B13").then((function(e){if(200==e.data.code){var i=e.data.data.map((function(t){return{id:t.id,pid:t.parentid,label:t.groupname}})),a=[{id:"0",pid:"root",label:t.treeTitle}],o=[].concat(Object(l["a"])(i),a);t.groupData=t.transData(o,"id","pid","children")}}))},btnHelp:function(){this.showhelp=!this.showhelp,this.showhelp&&this.$refs.helpmodel.bindData()},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t},sendTableForm:function(t){this.tableForm=t},searchByTree:function(t){""!=t?this.queryParams.SearchPojo={gengroupid:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},handleNodeClick:function(t){if(0==t.id){this.$refs.tableList.search("")}else{var e=t.id;this.$refs.tableList.search(e)}},editTreeNode:function(t){this.pid=t.pid,this.showGroupform(t.id)},addTreeChild:function(t){this.pid=t.id,this.showGroupform(0)},delTreeNode:function(t){var e=this;t.children?this.$message({message:"警告,该节点下存在子节点不能删除",type:"warning"}):this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){console.log("执行删除"),h["a"].get("/S16M89B1/delete?key=".concat(t.id)).then((function(){console.log("执行关闭保存"),e.$message.success({message:"删除成功！"}),e.bindTreeData()})).catch((function(){e.$message({showClose:!0,message:"删除失败",type:"warning"})}))})).catch((function(){}))},showGroupform:function(t){this.idx=t,this.gropuFormVisible=!0},handleChange:function(t){t.length>0?this.cloneForm.gengroupid=t[t.length-1]:this.cloneForm.gengroupid="0"},transData:function(t,e,i,a){for(var o=[],l={},s=e,n=i,r=a,c=0,d=0,m=t.length;c<m;c++)l[t[c][s]]=t[c];for(;d<m;d++){var u=t[d],f=l[u[n]];f?(!f[r]&&(f[r]=[]),f[r].push(u)):o.push(u)}return o}}},Ht=Dt,Bt=(i("cf1e1"),Object(u["a"])(Ht,a,o,!1,null,"2840453a",null));e["default"]=Bt.exports},cf1e1:function(t,e,i){"use strict";i("f457")},e250:function(t,e,i){},f457:function(t,e,i){},f9a2:function(t,e,i){"use strict";i("42ad")},fa84:function(t,e,i){"use strict";i("8c53")}}]);