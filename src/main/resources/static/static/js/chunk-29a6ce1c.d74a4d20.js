(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-29a6ce1c"],{"21f1":function(t,a,e){},"484f":function(t,a,e){"use strict";e("9ae1")},"582d":function(t,a,e){"use strict";e.r(a);var n=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"warp"},[e("div",{staticClass:"warpBox"},[e("div",{staticClass:"workbench",staticStyle:{width:"480px"}},t._l(t.linkList,(function(a,n){return e("el-card",{key:n,staticClass:"box-card cardItem"},[e("div",[e("router-link",{staticClass:"linkItem",attrs:{to:a.link}},[e("i",{class:a.icon,style:{color:a.color?a.color:"#409EFF","font-size":"24px"}}),e("span",{staticClass:"linkName"},[t._v(t._s(a.name))])])],1)])})),1),e("div",{staticClass:"panel-group flex a-c f-1"},[e("div",{staticStyle:{width:"50%"}},[e("customerTotal",{ref:"tag-2",staticClass:"card-pandiv f-1"}),e("saleNumMonth",{ref:"tag-3",staticClass:"card-pandiv f-1",attrs:{total:t.tagData.salenum}})],1),e("div",{staticStyle:{width:"50%"}},[e("salePriceMonth",{ref:"tag-4",staticClass:"card-pandiv f-1",attrs:{total:t.tagData.volume}}),e("recordsNum",{ref:"tag-5",staticClass:"card-pandiv f-1",attrs:{total:t.tagData.record}})],1)])]),e("div",{staticClass:"Statistics"},[e("el-card",{staticClass:"box-card",attrs:{"body-style":{padding:"0px"}}},[e("div",{staticClass:"card-header"},[e("i",{staticClass:"el-icon-s-operation iconStyle"}),t._v(" 统计 ")]),e("div",{staticClass:"card-content"},[e("div",{staticClass:"card-content-item",staticStyle:{"margin-left":"10px"}},[e("h3",{staticStyle:{"margin-left":"10px"}},[t._v("客户销售排行榜")]),e("div",{staticStyle:{padding:"5px 5px 5px 10px",overflow:"auto"}},[0!=t.lst.length?e("div",{staticStyle:{height:"370px"}},t._l(t.lst,(function(a,n){return e("div",{key:n,staticClass:"lst-li"},[e("span",{staticClass:"NavItemNum"},[t._v(t._s(n+1)+"、")]),e("div",{staticClass:"NavCountent"},[e("div",{staticClass:"NavItemName"},[t._v(t._s(a.name))]),e("div",{staticClass:"NavItemQty"},[e("span",[t._v("￥"+t._s(t._f("NumberFormat")(a.value)))]),t._v(" 元 ")])])])})),0):e("div",{staticStyle:{height:"270px",position:"relative"}},[e("div",{staticClass:"noData"},[t._v("暂无数据")])])])]),e("div",{staticClass:"card-content-item",staticStyle:{flex:"1"}},[e("div",{staticClass:"flex just-between"},[e("h3",[t._v(t._s(t.time)+"销售"+t._s(t.echartsType?"趋势图":"柱状图"))]),e("div",[e("el-switch",{staticStyle:{display:"inline-block","margin-right":"10px"},attrs:{"active-color":"#42aaf0","inactive-color":"#63C2FF","active-text":"趋势图","inactive-text":"柱状图","active-value":1,"inactive-value":0},on:{change:function(a){return t.bindData()}},model:{value:t.echartsType,callback:function(a){t.echartsType=a},expression:"echartsType"}}),e("el-dropdown",[e("el-button",{attrs:{size:"small"}},[t._v(t._s(t.time)),e("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),e("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[e("el-dropdown-item",{nativeOn:{click:function(a){t.echartsDate="Day",t.time="七日",t.bindData()}}},[t._v("七日")]),e("el-dropdown-item",{nativeOn:{click:function(a){t.echartsDate="Month",t.time="月度",t.bindData()}}},[t._v("月度")]),e("el-dropdown-item",{nativeOn:{click:function(a){t.echartsDate="Year",t.time="年度",t.bindData()}}},[t._v("年度")])],1)],1)],1)]),e("div",{staticStyle:{height:"370px",width:"100%","margin-top":"4px"},attrs:{id:"saleTrends"}})])])])],1)])},i=[],s=e("c7eb"),r=e("1da1"),o=(e("e9c4"),e("99af"),e("b0c0"),e("b99c")),c=e("9f21"),l=e("8d01"),u=e("6852"),d=e("ec1b"),p=e.n(d),h=e("b775"),f=e("b893"),m={name:"S16MWB1",components:{CountTo:p.a,customerTotal:o["a"],salePriceMonth:l["a"],saleNumMonth:c["a"],recordsNum:u["a"]},data:function(){return{linkList:[{icon:"el-icon-s-custom",color:"#409EFF",name:"客户",link:"/S16/M01B1"},{icon:"el-icon-s-claim",color:"#795548",name:"送货单",link:"/S16/M03B1"},{icon:"el-icon-takeaway-box",color:"#E6A23C",name:"无档送货单",link:"/S16/M03B2"},{icon:"el-icon-s-order",color:"#67C23A",name:"标签打印",link:"/S16/M04B1"},{icon:"el-icon-box",color:"#9c27b0",name:"销售明细",link:"/S16/M03R1"},{icon:"el-icon-document",color:"#9c27b0",name:"货品档案",link:"/S16/M02B1"}],echartsType:1,echartsDate:"Day",time:"七日",lst:[],tagData:{salenum:0,volume:0,record:0}}},created:function(){},mounted:function(){this.getTagData(),this.getsaleList(),this.bindData()},methods:{getTagData:function(){var t=this;return Object(r["a"])(Object(s["a"])().mark((function a(){var e;return Object(s["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return e={PageNum:1,PageSize:20,OrderType:1,SearchType:1,DateRange:{StartDate:Object(f["f"])()[0],EndDate:Object(f["f"])()[1],DateColumn:"billdate"}},a.next=3,h["a"].post("/S16MBIR1/getTagSumAmtQtyByDate",JSON.stringify(e)).then((function(a){console.log("bindData",a),200==a.data.code&&(t.tagData.salenum=a.data.data.valueb,t.tagData.volume=a.data.data.value,t.tagData.record=a.data.data.valuec)}));case 3:case"end":return a.stop()}}),a)})))()},getsaleList:function(){var t=this,a={DateRange:{StartDate:Object(f["f"])()[0],EndDate:Object(f["f"])()[1]}};h["a"].post("/S16MBIR1/getSumAmtByGroupMax",JSON.stringify(a)).then((function(a){200==a.data.code&&(t.lst=a.data.data)})).catch((function(t){}))},bindData:function(){var t=this;return Object(r["a"])(Object(s["a"])().mark((function a(){var e;return Object(s["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:e={DateRange:{StartDate:"",EndDate:""}},"Year"==t.echartsDate?(e.DateRange.StartDate=Object(f["h"])()[0],e.DateRange.EndDate=Object(f["h"])()[1]):"Month"==t.echartsDate?(e.DateRange.StartDate=Object(f["f"])()[0],e.DateRange.EndDate=Object(f["f"])()[1]):"Day"==t.echartsDate&&(e.DateRange.StartDate=Object(f["g"])()[0],e.DateRange.EndDate=Object(f["g"])()[1]),h["a"].post("/S16MBIR1/getSumAmtBy".concat(t.echartsDate,"?trend=").concat(t.echartsType),JSON.stringify(e)).then((function(a){t.saleTrends(a.data.data)})).catch((function(t){console.log(t)}));case 3:case"end":return a.stop()}}),a)})))()},getSaleTrend:function(t){console.log(t);var a=document.getElementById("saleTrend"),e=this.$echarts.init(a),n={grid:{x:60,y:30,x2:50,y2:20},color:["#e81111","#42aaf0","#febf23"],xAxis:{type:"category",data:["Mon","Tue","Wed","Thu","Fri","Sat","Sun"]},yAxis:{type:"value",splitLine:{show:!0},axisLine:{lineStyle:{color:"#000",width:2},symbol:["none","none"]}},legend:{data:["A产品销量","B产品销量","C产品销量"],left:"right"},tooltip:{trigger:"axis",axisPointer:{type:"cross",label:{backgroundColor:"#6a7985"}}},series:[{name:"A产品销量",data:[550,232,311,764,290,230,500],type:this.isLine?"line":"bar",smooth:!0,label:{normal:{show:!0,position:"top"}},areaStyle:{}},{name:"B产品销量",data:[420,162,121,474,720,640,230],type:this.isLine?"line":"bar",smooth:!0,label:{normal:{show:!0,position:"top"}},areaStyle:{}},{name:"C产品销量",data:[250,432,301,434,190,330,600],type:this.isLine?"line":"bar",smooth:!0,label:{normal:{show:!0,position:"top"}},areaStyle:{}}]};e.setOption(n),window.addEventListener("resize",(function(){e.resize()}))},saleTrends:function(t){for(var a=[],e=[],n=0;n<t.length;n++)a.push(t[n].name),e.push(t[n].value);var i=document.getElementById("saleTrends"),s=this.$echarts.init(i),r={grid:{x:50,y:30,x2:50,y2:20},color:["#42aaf0","#febf23"],xAxis:{type:"category",data:a},yAxis:{type:"value",splitLine:{show:!0},axisLine:{lineStyle:{color:"#000",width:2},symbol:["none","none"]}},legend:{data:["金额"],left:"right"},tooltip:{trigger:"axis",axisPointer:{type:"cross",label:{backgroundColor:"#6a7985"}}},series:[{name:"金额",data:e,type:this.echartsType?"line":"bar",smooth:!0,barWidth:40,label:{normal:{show:!0,position:"top"}},areaStyle:{normal:{color:new this.$echarts.graphic.LinearGradient(0,1,0,0,[{offset:0,color:"#42aaf000"},{offset:1,color:"#42aaf0"}])}}}]};s.setOption(r),window.addEventListener("resize",(function(){s.resize()}))}}},v=m,b=(e("aa9c"),e("2877")),g=Object(b["a"])(v,n,i,!1,null,"36d57944",null);a["default"]=g.exports},6852:function(t,a,e){"use strict";var n=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"card-pandiv f-1"},[e("div",{staticClass:"card-panel",on:{click:function(a){return t.handleSetLineChartData("messages")}}},[e("div",{staticClass:"card-panel-icon-wrapper icon-people"},[e("svg-icon",{attrs:{"svg-icon":"","icon-class":"record","class-name":"card-panel-icon"}})],1),e("div",{staticClass:"card-panel-description"},[e("div",{staticClass:"card-panel-text"},[t._v("记录数")]),e("count-to",{staticClass:"card-panel-num",attrs:{"start-val":0,"end-val":t.total,duration:2600}})],1)])])},i=[],s=(e("b775"),e("ec1b")),r=e.n(s),o={components:{CountTo:r.a},props:["total"],data:function(){return{searchstr:" "}},created:function(){},methods:{}},c=o,l=(e("e603"),e("2877")),u=Object(l["a"])(c,n,i,!1,null,"69b7bbb8",null);a["a"]=u.exports},7493:function(t,a,e){"use strict";e("21f1")},8031:function(t,a,e){},"8d01":function(t,a,e){"use strict";var n=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"card-pandiv f-1"},[e("div",{staticClass:"card-panel",on:{click:function(a){return t.handleSetLineChartData("messages")}}},[e("div",{staticClass:"card-panel-icon-wrapper icon-money"},[e("svg-icon",{attrs:{"icon-class":"money","class-name":"card-panel-icon"}})],1),e("div",{staticClass:"card-panel-description"},[e("div",{staticClass:"card-panel-text"},[t._v("本月"+t._s(t.pramatype?"销售":"发货")+"额")]),e("count-to",{staticClass:"card-panel-num",attrs:{"start-val":0,"end-val":t.total,decimals:0,prefix:"￥",duration:2600}})],1)])])},i=[],s=(e("b775"),e("ec1b")),r=e.n(s),o={components:{CountTo:r.a},props:["total","pramatype"],data:function(){return{}}},c=o,l=(e("7493"),e("2877")),u=Object(l["a"])(c,n,i,!1,null,"167a857e",null);a["a"]=u.exports},"9ae1":function(t,a,e){},"9f21":function(t,a,e){"use strict";var n=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"card-pandiv f-1"},[e("div",{staticClass:"card-panel",on:{click:function(a){return t.handleSetLineChartData("messages")}}},[e("div",{staticClass:"card-panel-icon-wrapper icon-shopping"},[e("svg-icon",{attrs:{"icon-class":"shopping","class-name":"card-panel-icon"}})],1),e("div",{staticClass:"card-panel-description"},[e("div",{staticClass:"card-panel-text"},[t._v("本月"+t._s(t.pramatype?"销售":"发货")+"数")]),e("count-to",{staticClass:"card-panel-num",attrs:{"start-val":0,"end-val":t.total,prefix:"",duration:2600}})],1)])])},i=[],s=(e("b775"),e("ec1b")),r=e.n(s),o={components:{CountTo:r.a},props:["total","pramatype"],data:function(){return{}}},c=o,l=(e("cdb7"),e("2877")),u=Object(l["a"])(c,n,i,!1,null,"10e2fc04",null);a["a"]=u.exports},aa9c:function(t,a,e){"use strict";e("df1b")},b99c:function(t,a,e){"use strict";var n=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"card-pandiv f-1"},[e("div",{staticClass:"card-panel"},[e("div",{staticClass:"card-panel-icon-wrapper icon-people"},[e("svg-icon",{attrs:{"svg-icon":"","icon-class":"peoples","class-name":"card-panel-icon"}})],1),e("div",{staticClass:"card-panel-description"},[e("div",{staticClass:"card-panel-text"},[t._v("客户总量")]),e("count-to",{staticClass:"card-panel-num",attrs:{"start-val":0,"end-val":t.total,duration:2600}})],1)])])},i=[],s=(e("4d90"),e("d3b7"),e("25f0"),e("99af"),e("b775")),r=e("ec1b"),o=e.n(r),c={components:{CountTo:o.a},data:function(){return{total:0,idx:0,lst:[],today:new Date,monthFirst:new Date,timer:null}},created:function(){var t=this;this.getDate(),this.bindData(),this.timer=setInterval((function(){t.freshData()}),36e5)},beforeDestroy:function(){clearInterval(this.timer)},methods:{freshData:function(){this.total=0,this.bindData()},getDate:function(){var t=new Date,a=t.getFullYear(),e=(t.getMonth()+1).toString().padStart(2,"0"),n=t.getDate().toString().padStart(2,"0");this.today="".concat(a,"-").concat(e,"-").concat(n,"T00:00:00"),this.monthFirst="".concat(a,"-").concat(e,"-01T00:00:00")},bindData:function(){var t=this;s["a"].get("/S16MBIR1/getTagGroupCount").then((function(a){200==a.data.code&&(t.total=a.data.data.value),t.listLoading=!1})).catch((function(a){t.listLoading=!1}))}}},l=c,u=(e("484f"),e("2877")),d=Object(u["a"])(l,n,i,!1,null,"a77bd8d6",null);a["a"]=d.exports},be9a:function(t,a,e){},cdb7:function(t,a,e){"use strict";e("be9a")},df1b:function(t,a,e){},e603:function(t,a,e){"use strict";e("8031")},ec1b:function(t,a,e){!function(a,e){t.exports=e()}(0,(function(){return function(t){function a(n){if(e[n])return e[n].exports;var i=e[n]={i:n,l:!1,exports:{}};return t[n].call(i.exports,i,i.exports,a),i.l=!0,i.exports}var e={};return a.m=t,a.c=e,a.i=function(t){return t},a.d=function(t,e,n){a.o(t,e)||Object.defineProperty(t,e,{configurable:!1,enumerable:!0,get:n})},a.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return a.d(e,"a",e),e},a.o=function(t,a){return Object.prototype.hasOwnProperty.call(t,a)},a.p="/dist/",a(a.s=2)}([function(t,a,e){var n=e(4)(e(1),e(5),null,null);t.exports=n.exports},function(t,a,e){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var n=e(3);a.default={props:{startVal:{type:Number,required:!1,default:0},endVal:{type:Number,required:!1,default:2017},duration:{type:Number,required:!1,default:3e3},autoplay:{type:Boolean,required:!1,default:!0},decimals:{type:Number,required:!1,default:0,validator:function(t){return t>=0}},decimal:{type:String,required:!1,default:"."},separator:{type:String,required:!1,default:","},prefix:{type:String,required:!1,default:""},suffix:{type:String,required:!1,default:""},useEasing:{type:Boolean,required:!1,default:!0},easingFn:{type:Function,default:function(t,a,e,n){return e*(1-Math.pow(2,-10*t/n))*1024/1023+a}}},data:function(){return{localStartVal:this.startVal,displayValue:this.formatNumber(this.startVal),printVal:null,paused:!1,localDuration:this.duration,startTime:null,timestamp:null,remaining:null,rAF:null}},computed:{countDown:function(){return this.startVal>this.endVal}},watch:{startVal:function(){this.autoplay&&this.start()},endVal:function(){this.autoplay&&this.start()}},mounted:function(){this.autoplay&&this.start(),this.$emit("mountedCallback")},methods:{start:function(){this.localStartVal=this.startVal,this.startTime=null,this.localDuration=this.duration,this.paused=!1,this.rAF=(0,n.requestAnimationFrame)(this.count)},pauseResume:function(){this.paused?(this.resume(),this.paused=!1):(this.pause(),this.paused=!0)},pause:function(){(0,n.cancelAnimationFrame)(this.rAF)},resume:function(){this.startTime=null,this.localDuration=+this.remaining,this.localStartVal=+this.printVal,(0,n.requestAnimationFrame)(this.count)},reset:function(){this.startTime=null,(0,n.cancelAnimationFrame)(this.rAF),this.displayValue=this.formatNumber(this.startVal)},count:function(t){this.startTime||(this.startTime=t),this.timestamp=t;var a=t-this.startTime;this.remaining=this.localDuration-a,this.useEasing?this.countDown?this.printVal=this.localStartVal-this.easingFn(a,0,this.localStartVal-this.endVal,this.localDuration):this.printVal=this.easingFn(a,this.localStartVal,this.endVal-this.localStartVal,this.localDuration):this.countDown?this.printVal=this.localStartVal-(this.localStartVal-this.endVal)*(a/this.localDuration):this.printVal=this.localStartVal+(this.localStartVal-this.startVal)*(a/this.localDuration),this.countDown?this.printVal=this.printVal<this.endVal?this.endVal:this.printVal:this.printVal=this.printVal>this.endVal?this.endVal:this.printVal,this.displayValue=this.formatNumber(this.printVal),a<this.localDuration?this.rAF=(0,n.requestAnimationFrame)(this.count):this.$emit("callback")},isNumber:function(t){return!isNaN(parseFloat(t))},formatNumber:function(t){t=t.toFixed(this.decimals),t+="";var a=t.split("."),e=a[0],n=a.length>1?this.decimal+a[1]:"",i=/(\d+)(\d{3})/;if(this.separator&&!this.isNumber(this.separator))for(;i.test(e);)e=e.replace(i,"$1"+this.separator+"$2");return this.prefix+e+n+this.suffix}},destroyed:function(){(0,n.cancelAnimationFrame)(this.rAF)}}},function(t,a,e){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var n=e(0),i=function(t){return t&&t.__esModule?t:{default:t}}(n);a.default=i.default,"undefined"!=typeof window&&window.Vue&&window.Vue.component("count-to",i.default)},function(t,a,e){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var n=0,i="webkit moz ms o".split(" "),s=void 0,r=void 0;if("undefined"==typeof window)a.requestAnimationFrame=s=function(){},a.cancelAnimationFrame=r=function(){};else{a.requestAnimationFrame=s=window.requestAnimationFrame,a.cancelAnimationFrame=r=window.cancelAnimationFrame;for(var o=void 0,c=0;c<i.length&&(!s||!r);c++)o=i[c],a.requestAnimationFrame=s=s||window[o+"RequestAnimationFrame"],a.cancelAnimationFrame=r=r||window[o+"CancelAnimationFrame"]||window[o+"CancelRequestAnimationFrame"];s&&r||(a.requestAnimationFrame=s=function(t){var a=(new Date).getTime(),e=Math.max(0,16-(a-n)),i=window.setTimeout((function(){t(a+e)}),e);return n=a+e,i},a.cancelAnimationFrame=r=function(t){window.clearTimeout(t)})}a.requestAnimationFrame=s,a.cancelAnimationFrame=r},function(t,a){t.exports=function(t,a,e,n){var i,s=t=t||{},r=typeof t.default;"object"!==r&&"function"!==r||(i=t,s=t.default);var o="function"==typeof s?s.options:s;if(a&&(o.render=a.render,o.staticRenderFns=a.staticRenderFns),e&&(o._scopeId=e),n){var c=Object.create(o.computed||null);Object.keys(n).forEach((function(t){var a=n[t];c[t]=function(){return a}})),o.computed=c}return{esModule:i,exports:s,options:o}}},function(t,a){t.exports={render:function(){var t=this,a=t.$createElement;return(t._self._c||a)("span",[t._v("\n  "+t._s(t.displayValue)+"\n")])},staticRenderFns:[]}}])}))}}]);