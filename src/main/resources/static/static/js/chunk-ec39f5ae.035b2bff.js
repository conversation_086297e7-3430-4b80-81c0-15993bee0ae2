(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-ec39f5ae"],{"128b":function(e,t,a){"use strict";a("be94")},1545:function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.formvisible?a("div",{ref:"formedit",staticClass:"formedit"},[a("formedit",e._g({ref:"formedit",attrs:{idx:e.idx}},{compForm:e.compForm,closeForm:e.closeForm,changeIdx:e.changeIdx,bindData:e.bindData}))],1):a("div",{ref:"index",staticClass:"index"},[a("listheader",{on:{btnAdd:function(t){return e.showForm(0)},btnSearch:e.search,advancedSearch:e.advancedSearch}}),a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticClass:"table-container",staticStyle:{overflow:"auto"},attrs:{data:e.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"1px 0px 1px 0px"},height:e.tableMaxHeight}},[a("el-table-column",{attrs:{align:"center",label:"ID","min-width":"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.$index+1)+" ")]}}])}),a("el-table-column",{attrs:{label:"窗体编码",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return e.showForm(t.row.id)}}},[e._v(" "+e._s(t.row.formcode||"窗体编码")+" ")])]}}])}),a("el-table-column",{attrs:{label:"窗体名称",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.formname))])]}}])}),a("el-table-column",{attrs:{label:"序号",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.rownum))])]}}])}),a("el-table-column",{attrs:{label:"有效性",align:"center","min-width":"60px"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.enabledmark?a("el-tag",{attrs:{size:"small"}},[e._v("正常")]):a("el-tag",{attrs:{type:"warning",size:"small"}},[e._v("停用")])]}}])}),a("el-table-column",{attrs:{label:"简述",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.summary)+" ")]}}])}),a("el-table-column",{attrs:{label:"制表",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.lister))])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.PageNum,limit:e.queryParams.PageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"PageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"PageSize",t)},pagination:e.GetList}})],1)],1)],1)],1)])},i=[],o=(a("e9c4"),function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{class:e.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[a("div",[a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.btnSearch(t)}},model:{value:e.strfilter,callback:function(t){e.strfilter=t},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:e.btnSearch},slot:"append"},[e._v("搜索 ")])],1),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-plus",plain:"",size:"mini"},on:{click:e.btnAdd}},[e._v(" 添加 ")])],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"}},[e._v("列设置")]),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{icon:e.iShow?"el-icon-arrow-up":"el-icon-arrow-down",size:"mini"},on:{click:function(t){e.iShow=!e.iShow}}})],1)]),a("el-collapse-transition",[a("div",{directives:[{name:"show",rawName:"v-show",value:e.iShow,expression:"iShow"}],staticClass:"searchDetail"},[a("el-form",{ref:"formdata",attrs:{model:e.formdata,"label-width":"100px","auto-complete":"on"}},[a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"窗体编码"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入窗体编码",size:"small"},model:{value:e.formdata.formcode,callback:function(t){e.$set(e.formdata,"formcode",t)},expression:"formdata.formcode"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"窗体名称"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入窗体名称",size:"small"},model:{value:e.formdata.formname,callback:function(t){e.$set(e.formdata,"formname",t)},expression:"formdata.formname"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"制表"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入制表",size:"small"},model:{value:e.formdata.lister,callback:function(t){e.$set(e.formdata,"lister",t)},expression:"formdata.lister"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"简述"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入简述",size:"small"},model:{value:e.formdata.summary,callback:function(t){e.$set(e.formdata,"summary",t)},expression:"formdata.summary"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px","margin-top":"5px"},attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.advancedSearch()}}},[e._v(" 搜索 ")])],1)],1)],1)],1)])],1)}),r=[],n={name:"Listheader",data:function(){return{strfilter:"",iShow:!1,formdata:{}}},methods:{advancedSearch:function(){this.iShow=!1,this.$emit("advancedSearch",this.formdata)},btnAdd:function(){this.$emit("btnAdd")},btnSearch:function(){this.$emit("btnSearch",this.strfilter)}}},s=n,c=(a("4838"),a("2877")),m=Object(c["a"])(s,o,r,!1,null,"0d415c6c",null),d=m.exports,u=a("333d"),f=a("b775"),p=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",size:"small",disabled:!!e.formdata.assessor},nativeOn:{click:function(t){return e.submitForm("formdata")}}},[e._v(" 保 存")]),a("el-button",{attrs:{disabled:!e.formdata.id,size:"small"},nativeOn:{click:function(t){return e.deleteForm(e.idx)}}},[e._v(" 删 除")]),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(t){return e.closeForm(t)}}},[e._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:e.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:e.formdata,"label-width":e.formLabelWidth,rules:e.formRules}},[a("p",{staticClass:"formTitle"},[e._v(e._s(e.title))]),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(t){return e.cleValidate("formcode")}}},[a("el-form-item",{attrs:{label:"窗体编码",prop:"formcode"}},[a("el-input",{attrs:{placeholder:"请输入窗体编码",clearable:"",size:"small"},model:{value:e.formdata.formcode,callback:function(t){e.$set(e.formdata,"formcode",t)},expression:"formdata.formcode"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(t){return e.cleValidate("formname")}}},[a("el-form-item",{attrs:{label:"窗体名称"}},[a("el-input",{attrs:{placeholder:"请输入窗体名称",clearable:"",size:"small"},model:{value:e.formdata.formname,callback:function(t){e.$set(e.formdata,"formname",t)},expression:"formdata.formname"}})],1)],1)]),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"序号"}},[a("el-input-number",{staticClass:"inputNumberContent",staticStyle:{width:"100%"},attrs:{controls:!0,type:"number",min:0,size:"small","controls-position":"right"},model:{value:e.formdata.rownum,callback:function(t){e.$set(e.formdata,"rownum",t)},expression:"formdata.rownum"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"状态"}},[a("el-checkbox",{attrs:{label:"有效","true-label":1,"false-label":0,size:"mini"},model:{value:e.formdata.enabledmark,callback:function(t){e.$set(e.formdata,"enabledmark",t)},expression:"formdata.enabledmark"}})],1)],1)],1)],1),a("el-divider")],1),a("div",{staticClass:"form-body form f-1"},[a("elitem",{ref:"elitem",staticStyle:{width:"99%"},attrs:{lstitem:e.formdata.item,formdata:e.formdata,idx:e.idx}})],1),a("el-form",{attrs:{"label-width":e.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:e.formdata.summary,callback:function(t){e.$set(e.formdata,"summary",t)},expression:"formdata.summary"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[e._v(e._s(e.formdata.createby))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[e._v(e._s(e._f("dateFormat")(e.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[e._v(e._s(e.formdata.lister))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[e._v(e._s(e._f("dateFormat")(e.formdata.modifydate)))])])],1)],1)],1)],1)])])},h=[];a("b64b"),a("4d90"),a("d3b7"),a("25f0"),a("99af");const b={add(e){return new Promise((t,a)=>{var l=JSON.stringify(e);f["a"].post("/S16M87B1/create",l).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})},update(e){return new Promise((t,a)=>{var l=JSON.stringify(e);f["a"].post("/S16M87B1/update",l).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})},delete(e){return new Promise((t,a)=>{f["a"].get("/S16M87B1/delete?key="+e).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})}};var w=b,v=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{ref:"elitem",staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[a("div",{staticStyle:{display:"flex","justify-content":"space-between"}},[a("el-row",[a("el-button-group",[a("el-button",{attrs:{type:"primary",size:"mini"},nativeOn:{click:function(t){return e.getselPwProcess("add")}}},[a("i",{staticClass:"el-icon-circle-plus-outline"}),e._v(" 添 加")]),a("el-button",{attrs:{disabled:1!=e.multipleSelection.length,type:"primary",size:"mini"},nativeOn:{click:function(t){return e.getMoveUp()}}},[a("i",{staticClass:"el-icon-top"}),e._v(" 上 移")]),a("el-button",{attrs:{disabled:1!=e.multipleSelection.length,type:"primary",size:"mini"},nativeOn:{click:function(t){return e.getMoveDown()}}},[a("i",{staticClass:"el-icon-bottom"}),e._v(" 下 移")]),a("el-button",{attrs:{disabled:!e.selected,type:"danger",size:"mini"},nativeOn:{click:function(t){return e.delItem()}}},[a("i",{staticClass:"el-icon-delete"}),e._v(" 删 除")])],1)],1)],1),a("div",{staticClass:"table-container f-1 table-position",staticStyle:{width:"100%"}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"multipleTable",staticClass:"tb-edit tableBox",attrs:{data:e.lst,"element-loading-text":"Loading",border:"",height:e.tableHeight,fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"4px 0px 4px 0px"},"cell-style":{padding:"4px 0px"},"row-style":{height:"20px"}},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"60"}}),a("el-table-column",{attrs:{align:"center",label:"ID",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.$index+1)+" ")]}}])}),a("el-table-column",{attrs:{label:"编码",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.itemcode))])]}}])}),a("el-table-column",{attrs:{label:"名称",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.itemname))])]}}])}),a("el-table-column",{attrs:{label:"最小宽度",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.minwidth))])]}}])}),a("el-table-column",{attrs:{label:"默认宽度",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.defwidth))])]}}])}),a("el-table-column",{attrs:{label:"是否显示",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-checkbox",{attrs:{label:"是","true-label":1,"false-label":0,size:"mini"},model:{value:t.row.displaymark,callback:function(a){e.$set(t.row,"displaymark",a)},expression:"scope.row.displaymark "}})]}}])}),a("el-table-column",{attrs:{label:"溢出隐藏",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-checkbox",{attrs:{label:"是","true-label":1,"false-label":0,size:"mini"},model:{value:t.row.overflow,callback:function(a){e.$set(t.row,"overflow",a)},expression:"scope.row.overflow "}})]}}])}),a("el-table-column",{attrs:{label:"是否固定",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-checkbox",{attrs:{label:"是","true-label":1,"false-label":0,size:"mini"},model:{value:t.row.fixed,callback:function(a){e.$set(t.row,"fixed",a)},expression:"scope.row.fixed "}})]}}])}),a("el-table-column",{attrs:{label:"是否排序",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-checkbox",{attrs:{label:"是","true-label":1,"false-label":0,size:"mini"},model:{value:t.row.sortable,callback:function(a){e.$set(t.row,"sortable",a)},expression:"scope.row.sortable "}})]}}])}),a("el-table-column",{attrs:{label:"允许编辑",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-checkbox",{attrs:{label:"是","true-label":1,"false-label":0,size:"mini"},model:{value:t.row.editmark,callback:function(a){e.$set(t.row,"editmark",a)},expression:"scope.row.editmark "}})]}}])}),a("el-table-column",{attrs:{label:"位置",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.aligntype))])]}}])}),a("el-table-column",{attrs:{label:"格式化",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.formatter))])]}}])}),a("el-table-column",{attrs:{label:"自定义calss",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.classname))])]}}])}),a("el-table-column",{attrs:{label:"事件名称",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.eventname))])]}}])}),a("el-table-column",{attrs:{label:"备注",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.remark))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center",width:"180px","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",size:"small",icon:"el-icon-edit"},nativeOn:{click:function(a){return e.getselPwProcess("edit",t.$index)}}},[e._v("编辑")]),a("el-button",{attrs:{type:"text",size:"small",icon:"el-icon-delete"},nativeOn:{click:function(a){return e.deleteItem(t.$index)}}},[e._v("删除")])]}}])})],1)],1),e.PwProcessFormVisible?a("el-dialog",{attrs:{title:"列表信息","append-to-body":!0,visible:e.PwProcessFormVisible,width:"800px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.PwProcessFormVisible=t}}},[a("el-form",{ref:"itemFormdata",staticClass:"custInfo",attrs:{model:e.itemFormdata,rules:e.itemFormRules,"label-width":"100px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(t){return e.cleValidate("itemcode")}}},[a("el-form-item",{attrs:{label:"编码",prop:"itemcode"}},[a("el-input",{attrs:{placeholder:"请输入编码",clearable:""},model:{value:e.itemFormdata.itemcode,callback:function(t){e.$set(e.itemFormdata,"itemcode",t)},expression:"itemFormdata.itemcode"}})],1)],1)]),a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(t){return e.cleValidate("itemname")}}},[a("el-form-item",{attrs:{label:"名称",prop:"itemname"}},[a("el-input",{attrs:{placeholder:"请输入名称",clearable:""},model:{value:e.itemFormdata.itemname,callback:function(t){e.$set(e.itemFormdata,"itemname",t)},expression:"itemFormdata.itemname"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(t){return e.cleValidate("minwidth")}}},[a("el-form-item",{attrs:{label:"最小宽度",prop:"minwidth"}},[a("el-input",{attrs:{placeholder:"请输入最小宽度",clearable:""},model:{value:e.itemFormdata.minwidth,callback:function(t){e.$set(e.itemFormdata,"minwidth",t)},expression:"itemFormdata.minwidth"}})],1)],1)]),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"默认宽度"}},[a("el-input",{attrs:{placeholder:"请输入默认宽度",clearable:""},model:{value:e.itemFormdata.defwidth,callback:function(t){e.$set(e.itemFormdata,"defwidth",t)},expression:"itemFormdata.defwidth"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"格式化"}},[a("el-input",{attrs:{placeholder:"请输入格式化",clearable:""},model:{value:e.itemFormdata.formatter,callback:function(t){e.$set(e.itemFormdata,"formatter",t)},expression:"itemFormdata.formatter"}})],1)],1),a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(t){return e.cleValidate("classname")}}},[a("el-form-item",{attrs:{label:"calss",prop:"classname"}},[a("el-input",{attrs:{placeholder:"请输入自定义calss",clearable:""},model:{value:e.itemFormdata.classname,callback:function(t){e.$set(e.itemFormdata,"classname",t)},expression:"itemFormdata.classname"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"位置"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},model:{value:e.itemFormdata.aligntype,callback:function(t){e.$set(e.itemFormdata,"aligntype",t)},expression:"itemFormdata.aligntype"}},[a("el-option",{attrs:{label:"left",value:"left"}}),a("el-option",{attrs:{label:"center",value:"center"}}),a("el-option",{attrs:{label:"right",value:"right"}})],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"事件名称",prop:"eventname"}},[a("el-input",{attrs:{placeholder:"请输入事件名称",clearable:""},model:{value:e.itemFormdata.eventname,callback:function(t){e.$set(e.itemFormdata,"eventname",t)},expression:"itemFormdata.eventname"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入备注"},model:{value:e.itemFormdata.remark,callback:function(t){e.$set(e.itemFormdata,"remark",t)},expression:"itemFormdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"是否显示"}},[a("el-checkbox",{attrs:{label:"是","true-label":1,"false-label":0,size:"mini"},model:{value:e.itemFormdata.displaymark,callback:function(t){e.$set(e.itemFormdata,"displaymark",t)},expression:"itemFormdata.displaymark"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"溢出隐藏"}},[a("el-checkbox",{attrs:{label:"是","true-label":1,"false-label":0,size:"mini"},model:{value:e.itemFormdata.overflow,callback:function(t){e.$set(e.itemFormdata,"overflow",t)},expression:"itemFormdata.overflow"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"固定"}},[a("el-checkbox",{attrs:{label:"是","true-label":1,"false-label":0,size:"mini"},model:{value:e.itemFormdata.fixed,callback:function(t){e.$set(e.itemFormdata,"fixed",t)},expression:"itemFormdata.fixed"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"排序"}},[a("el-checkbox",{attrs:{label:"是","true-label":1,"false-label":0,size:"mini"},model:{value:e.itemFormdata.sortable,callback:function(t){e.$set(e.itemFormdata,"sortable",t)},expression:"itemFormdata.sortable"}})],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},nativeOn:{click:function(t){return e.selPwProcess()}}},[e._v("确 定")]),a("el-button",{on:{click:function(t){e.PwProcessFormVisible=!1}}},[e._v("取 消")])],1)],1):e._e()],1)},g=[],x=(a("a434"),a("159b"),{name:"Elitem",components:{},props:["formdata","lstitem","idx"],data:function(){return{formLabelWidth:"100px",listLoading:!1,PwProcessFormVisible:!1,lst:[],billamount:0,selected:!1,tableHeight:0,isBtnType:"add",selectIndex:0,itemFormRules:{itemcode:[{required:!0,trigger:"blur",message:"编码为必填项"}],itemname:[{required:!0,trigger:"blur",message:"名称为必填项"}]},itemFormdata:{aligntype:"center",classname:"",defwidth:"",displaymark:1,eventname:"",fixed:0,formatter:"",itemcode:"",itemname:"",minwidth:"",overflow:1,pid:this.idx,remark:"",rownum:0,sortable:0},multipleSelection:[],isEditOk:!0}},watch:{lstitem:function(e,t){this.lst=this.lstitem},lst:function(e,t){void 0==e&&(this.lst=[]);for(var a=0;a<e.length;a++)e[a].rownum=a}},created:function(){this.lst=[]},mounted:function(){this.catchHight()},methods:{getselPwProcess:function(e,t){this.isBtnType=e,this.PwProcessFormVisible=!0,"add"==e?this.itemFormdata={aligntype:"centre",classname:"",defwidth:"",displaymark:1,eventname:"",fixed:0,formatter:"",itemcode:"",itemname:"",minwidth:"",overflow:1,pid:this.idx,remark:"",rownum:0,sortable:0}:(this.itemFormdata=Object.assign({},this.lst[t]),this.selectIndex=t)},selPwProcess:function(){var e=this;this.$refs["itemFormdata"].validate((function(t){if(!t)return!1;"add"==e.isBtnType?e.lst.push(e.itemFormdata):e.lst[e.selectIndex]=e.itemFormdata,e.PwProcessFormVisible=!1}))},catchHight:function(){var e=this;this.$nextTick((function(){e.$refs.elitem&&(e.tableHeight=e.$refs.elitem.getBoundingClientRect().height-72,console.log(e.tableHeight))}))},handleSelectionChange:function(e){e.length>0?this.selected=!0:this.selected=!1;for(var t=0;t<this.lst.length;t++)this.lst[t].rownum=t;this.multipleSelection=e},delItem:function(){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.deleteRows()})).catch((function(){}))},deleteItem:function(e){this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){that.lst.splice(e,1)})).catch((function(){}))},deleteRows:function(e,t){var a=this,l=this.multipleSelection;l&&l.forEach((function(e,t){a.lst.forEach((function(t,l){e.itemname===t.itemname&&e.itemcode===t.itemcode&&a.lst.splice(l,1)}))})),this.$refs.multipleTable.clearSelection(),this.selected=!1},cleValidate:function(e){this.$refs.itemFormdata.clearValidate(e)},getMoveUp:function(){if(1==this.multipleSelection.length){var e=this.multipleSelection[0],t=this.multipleSelection[0].rownum;if(0!=t){this.lst.splice(t,1),this.lst.splice(t-1,0,e);for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是第一行了！")}else this.$message.warning("只能选择一行内容！")},getMoveDown:function(){if(1==this.multipleSelection.length){var e=this.multipleSelection[0],t=this.multipleSelection[0].rownum;if(t!=this.lst.length-1){this.lst.splice(t,1),this.lst.splice(t+1,0,e);for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是最后一行了！")}else this.$message.warning("只能选择一行内容！")}}}),y=x,k=(a("ff46"),Object(c["a"])(y,v,g,!1,null,"1afe9d4e",null)),S=k.exports,_={name:"Formedit",components:{elitem:S},props:["idx"],data:function(){return{title:"列表格式",formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).RealName,createby:JSON.parse(window.localStorage.getItem("getInfo")).RealName,enabledmark:1,formcode:"",formname:"",item:[],rownum:0,summary:""},formRules:{formcode:[{required:!0,trigger:"blur",message:"窗体编码为必填项"}],formname:[{required:!0,trigger:"blur",message:"窗体名称为必填项"}]},formLabelWidth:"100px",formheight:"500px"}},computed:{formcontainHeight:function(){return window.innerHeight-50-33-50+"px"}},watch:{idx:function(e,t){this.bindData()}},created:function(){this.bindData()},methods:{bindData:function(){var e=this;0!=this.idx&&(this.listLoading=!0,f["a"].get("/S16M87B1/getBillEntity?key=".concat(this.idx)).then((function(t){200==t.data.code&&(e.formdata=t.data.data),e.listLoading=!1})).catch((function(t){e.listLoading=!1})))},submitForm:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return console.log("error submit!!"),!1;t.saveForm()}))},saveForm:function(){var e=this;this.formdata.item=this.$refs.elitem.lst,0==this.idx?w.add(this.formdata).then((function(t){e.$message.success("保存成功"),e.$emit("changeIdx",t.data.id),e.$emit("bindData"),e.bindData()})).catch((function(t){e.$message.warning("保存失败")})):w.update(this.formdata).then((function(t){e.$message.success("保存成功"),e.$emit("bindData"),e.bindData()})).catch((function(t){e.$message.warning("保存失败")}))},closeForm:function(){this.$emit("closeForm")},deleteForm:function(e){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){w.delete(e).then((function(){t.$message.success({message:"删除成功"}),t.$emit("compForm")})).catch((function(){t.$message.error({message:"删除失败"})})),console.log(e)})).catch((function(){}))},cleValidate:function(e){this.$refs.formdata.clearValidate(e)}},filters:{dateFormat:function(e){var t=new Date(e),a=t.getFullYear(),l=(t.getMonth()+1).toString().padStart(2,"0"),i=t.getDate().toString().padStart(2,"0"),o=t.getHours().toString().padStart(2,"0"),r=t.getMinutes().toString().padStart(2,"0"),n=t.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(l,"-").concat(i," ").concat(o,":").concat(r,":").concat(n)}}},F=_,$=(a("128b"),Object(c["a"])(F,p,h,!1,null,"219956ae",null)),P=$.exports,z={name:"S16M87B1",components:{Pagination:u["a"],listheader:d,formedit:P},data:function(){return{title:"S16M87B1",lst:[],formvisible:!1,listLoading:!1,idx:0,total:0,searchstr:"",queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1}}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},created:function(){this.bindData()},methods:{bindData:function(){var e=this;this.listLoading=!0,f["a"].post("/S16M87B1/getPageTh",JSON.stringify(this.queryParams)).then((function(t){200==t.data.code&&(e.lst=t.data.data.list,e.total=t.data.data.total),e.listLoading=!1})).catch((function(t){e.listLoading=!1}))},GetList:function(e){this.queryParams.PageNum=e.page,this.queryParams.PageSize=e.limit,this.bindData()},search:function(e){""!=e?this.queryParams.SearchPojo={formname:e,formcode:e}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},advancedSearch:function(e){this.queryParams.SearchPojo=e,this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},showForm:function(e){this.idx=e,console.log(this.idx),this.formvisible=!0},closeForm:function(){this.formvisible=!1,console.log("关闭编码窗口")},compForm:function(){this.bindData(),this.formvisible=!1,console.log("完成并刷新index")},changeIdx:function(e){this.idx=e}}},C=z,O=(a("59dd"),Object(c["a"])(C,l,i,!1,null,"558132a8",null));t["default"]=O.exports},"2eb8":function(e,t,a){},4838:function(e,t,a){"use strict";a("d602")},"59dd":function(e,t,a){"use strict";a("2eb8")},be94:function(e,t,a){},d602:function(e,t,a){},fad2:function(e,t,a){},ff46:function(e,t,a){"use strict";a("fad2")}}]);