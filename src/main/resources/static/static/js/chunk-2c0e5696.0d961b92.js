(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2c0e5696"],{1:function(t,e){},"10f6":function(t,e,a){"use strict";a("3f44")},"16c8":function(t,e,a){"use strict";a("a418")},2:function(t,e){},"22e1":function(t,e,a){t.exports=a.p+"static/img/icon_qywx.ae4a491d.png"},2504:function(t,e,a){},3:function(t,e){},"3f44":function(t,e,a){},4:function(t,e){},"478b":function(t,e,a){},"4eb6":function(t,e,a){t.exports=a.p+"static/img/icon_dingding.70dd0587.png"},5:function(t,e){},"5afb":function(t,e,a){t.exports=a.p+"static/img/icon_qq.a2e8a939.png"},6:function(t,e){},"697d":function(t,e,a){"use strict";a("2504")},7:function(t,e){},8:function(t,e){},"8e7b":function(t,e,a){"use strict";a("478b")},"9ed6":function(t,a,n){"use strict";n.r(a);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"login-container flex a-c j-end",staticStyle:{width:"100%"}},[a("div",{staticClass:"p-r",staticStyle:{width:"100%",height:"100%",display:"flex","justify-content":"center","align-items":"center"}},[a("div",{staticClass:"logoBox"}),t.loginTenant?a("div",{staticClass:"login"},[t.formVisable?a("el-form",{ref:"loginForm",staticClass:"login-form",attrs:{model:t.loginForm,rules:t.loginRules,"auto-complete":"on","label-position":"left"}},[a("div",{staticClass:"title-container"},[a("h3",{staticClass:"title"},[t._v(t._s(t.apptitle))])]),a("div",{directives:[{name:"show",rawName:"v-show",value:t.isErcode,expression:"isErcode"}]},[a("el-form-item",{staticStyle:{"margin-bottom":"30px"},attrs:{prop:"UserName"}},[a("span",{staticClass:"svg-container"},[a("svg-icon",{attrs:{"icon-class":"user"}})],1),a("el-input",{ref:"UserName",attrs:{placeholder:"手机和邮箱",name:"UserName",type:"text",tabindex:"1","auto-complete":"off"},model:{value:t.loginForm.UserName,callback:function(e){t.$set(t.loginForm,"UserName",e)},expression:"loginForm.UserName"}})],1),a("el-form-item",{staticStyle:{"margin-bottom":"30px"},attrs:{prop:"Password"}},[a("span",{staticClass:"svg-container"},[a("svg-icon",{attrs:{"icon-class":"password"}})],1),a("el-input",{key:t.passwordType,ref:"Password",attrs:{type:t.passwordType,placeholder:"密码",name:"Password",tabindex:"2","auto-complete":"off"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.validateCaptcha(e)}},model:{value:t.loginForm.Password,callback:function(e){t.$set(t.loginForm,"Password",e)},expression:"loginForm.Password"}}),a("span",{staticClass:"show-pwd",on:{click:t.showPwd}},[a("svg-icon",{attrs:{"icon-class":"password"===t.passwordType?"eye":"eye-open"}})],1)],1),t.codeNum>=2?a("div",[a("el-form-item",{staticStyle:{border:"0","margin-bottom":"30px"},attrs:{prop:"Code"}},[a("div",{staticStyle:{display:"flex","justify-content":"space-between","align-items":"center"}},[a("div",{staticStyle:{width:"60%",border:"1px solid rgba(0, 0, 0, 0.2)","border-radius":"5px",display:"flex"}},[a("span",{staticClass:"svg-container"},[a("svg-icon",{attrs:{"icon-class":"code"}})],1),a("el-input",{attrs:{type:"text",placeholder:"验证码","auto-complete":"off"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.validateCaptcha(e)}},model:{value:t.loginForm.Code,callback:function(e){t.$set(t.loginForm,"Code",e)},expression:"loginForm.Code"}})],1),a("div",{staticStyle:{width:"36%",height:"45px",border:"1px solid rgba(0, 0, 0, 0.2)",cursor:"pointer"}},[t.codeUrl?a("img",{staticStyle:{width:"100%",height:"100%",cursor:"pointer"},attrs:{src:"data:image/jpg;base64,"+t.codeUrl,alt:""},on:{click:t.getCode}}):a("div",{staticClass:"codeTip",on:{click:t.getCode}},[a("i",{staticClass:"el-icon-refresh-right"}),t._v(" 刷新验证码 ")])])])])],1):t._e(),a("div",{staticClass:"passwordOpera"},[a("el-checkbox",{attrs:{"true-label":1,"false-label":0},model:{value:t.loginForm.lockPwd,callback:function(e){t.$set(t.loginForm,"lockPwd",e)},expression:"loginForm.lockPwd"}},[t._v("记住密码")])],1),a("br"),a("el-button",{staticStyle:{width:"100%","margin-bottom":"10px"},attrs:{loading:t.loading,type:"primary"},nativeOn:{click:function(e){return e.preventDefault(),t.validateCaptcha(e)}}},[t._v("登录")]),"已"==t.intervalTime(t.passkey.ex)?a("div",{staticStyle:{display:"flex","justify-content":"center","align-items":"center",width:"100%","margin-top":"10px",color:"red","font-size":"16px","font-weight":"700",cursor:"pointer"},on:{click:function(e){t.isContactShow=!0}}},[t._v(" 软件授权逾期，"),a("span",{staticStyle:{"text-decoration":"underline"}},[t._v("请先注册激活")])]):t._e(),t.intervalTime(t.passkey.ex)<=30?a("div",{staticStyle:{"font-size":"16px",display:"flex","justify-content":"center","align-items":"center"},on:{click:function(e){t.isContactShow=!0}}},[t._v(" 授权期余 "),a("strong",{staticStyle:{color:"blue","text-decoration":"underline",cursor:"pointer",padding:"0 5px"}},[t._v(t._s(t.intervalTime(t.passkey.ex)))]),t._v(" ，请提前续约 ")]):t._e()],1),a("div",{directives:[{name:"show",rawName:"v-show",value:!t.isErcode,expression:"!isErcode"}]},[a("div",{staticClass:"ercode_tab switch-input",on:{click:t.changeErcode}},[a("svg",{attrs:{width:"52",height:"52","xmlns:xlink":"http://www.w3.org/1999/xlink",fill:"currentColor"}},[a("defs",[a("path",{attrs:{id:"id-14580708-a",d:"M0 0h48a4 4 0 0 1 4 4v48L0 0z"}})]),a("g",{attrs:{fill:"none","fill-rule":"evenodd"}},[a("mask",{attrs:{id:"id-14580708-b",fill:"#fff"}},[a("use",{attrs:{"xlink:href":"#id-14580708-a"}})]),a("use",{attrs:{fill:"#0084FF","xlink:href":"#id-14580708-a"}}),a("path",{attrs:{fill:"#FFF",d:"M22.125 4h13.75A4.125 4.125 0 0 1 40 8.125v27.75A4.125 4.125 0 0 1 35.875 40h-13.75A4.125 4.125 0 0 1 18 35.875V8.125A4.125 4.125 0 0 1 22.125 4zm6.938 34.222c1.139 0 2.062-.945 2.062-2.11 0-1.167-.923-2.112-2.063-2.112-1.139 0-2.062.945-2.062 2.111 0 1.166.923 2.111 2.063 2.111zM21 8.333v24h16v-24H21z",mask:"url(#id-14580708-b)"}}),a("g",{attrs:{mask:"url(#id-14580708-b)"}},[a("path",{attrs:{fill:"#FFF",d:"M46.996 15.482L39 19.064l-7.996-3.582A1.6 1.6 0 0 1 32.6 14h12.8a1.6 1.6 0 0 1 1.596 1.482zM47 16.646V24.4a1.6 1.6 0 0 1-1.6 1.6H32.6a1.6 1.6 0 0 1-1.6-1.6v-7.754l8 3.584 8-3.584z"}}),a("path",{attrs:{fill:"#0084FF",d:"M31 15.483v1.17l8 3.577 8-3.577v-1.17l-8 3.581z","fill-rule":"nonzero"}})])])])]),a("div",{staticClass:"ercode",staticStyle:{width:"200px",margin:"0 auto",position:"relative"}},[a("div",{ref:"qrCode",attrs:{id:"qrcode"}})]),t.iscodeError?a("div",{staticClass:"codeError",on:{click:function(e){return t.openErcode()}}},[a("i",{staticClass:"el-icon-refresh-left"}),t._v(" 二维码失效，请重新生成 ")]):a("div",{staticClass:"ercode-foot"},[a("div",[t._v("打开 "),a("span",[t._v("小程序")])]),a("div",{staticStyle:{"font-size":"12px","margin-top":"10px"}},[t._v(" 在「我的」打开扫一扫 ")])])])]):t._e(),a("div",{staticStyle:{color:"#666","font-size":"14px","text-align":"center",position:"absolute",bottom:"10px"}},[t._v(" Copyright@嘉兴应凯科技有限公司 版权所有 ")])],1):t._e(),t.loginTenant?t._e():a("div",{staticClass:"selectTenant border-radius right p-a",staticStyle:{right:"0","z-index":"99"}},[a("div",{staticClass:"title-container"},[a("h3",{staticClass:"title"},[t._v("选择"+t._s(t.$store.state.app.config.model))]),a("div",{staticClass:"titleFun"},[a("span",{on:{click:function(e){return t.dialogController()}}},[t._v("注册")]),a("span",{on:{click:function(e){return t.getTenant()}}},[t._v("刷新")])])]),0!=t.tenantList.length?a("div",{staticClass:"tenantList"},[t._l(t.tenantList,(function(e,n){return a("div",{key:n,class:e.tenantItemActive?"tenantList-item tenantList-item-active":"tenantList-item",on:{click:function(a){return t.changeTenantItemActive(n,e.tenantid)},dblclick:function(e){return t.submitTenant()}}},[t._m(0,!0),a("div",{staticClass:"select-none"},[a("div",[t._v(" "+t._s(t.$store.state.app.config.model)+"："+t._s(e.tenantName)+" ")])])])}))],2):a("div",{staticClass:"noData"},[t._v(" 暂无 "+t._s(t.$store.state.app.config.model)+" ")]),a("div",{staticClass:"btnSubmit"},[a("el-button",{staticStyle:{width:"65%"},attrs:{loading:t.loading,type:"primary"},on:{click:function(e){return t.submitTenant()}}},[t._v("确 认")]),a("el-button",{staticStyle:{width:"25%"},on:{click:t.logout}},[t._v("返回")])],1)])]),a("el-dialog",{attrs:{title:t.headerTitle,visible:t.dialogVisible,width:t.dialogWidth,"show-close":!0,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.dialogVisible=e}}},[a("organization",{ref:"organization",staticStyle:{height:"60vh"},on:{dialogClose:t.dialogClose,getTenant:t.getTenant}})],1),a("div",{staticClass:"contactUs"},[a("a",{attrs:{href:"http://www.inkstech.com",target:"_blank"}},[t._v(" 联系我们")]),a("a",{attrs:{href:"#"},on:{click:function(e){return t.Collection(e)}}},[t._v(" 收藏")]),a("a",{attrs:{href:"#"},on:{click:t.routerTo}},[t._v(" 首页")])]),a("el-dialog",{attrs:{title:"忘记密码",visible:t.forgetPwdVisible,width:"600px","show-close":!0,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.forgetPwdVisible=e}}},[t.forgetPwdVisible?a("forgetPassword",{ref:"forgetPassword",staticStyle:{height:"310px"},on:{forgetPwdClose:t.forgetPwdClose}}):t._e()],1),a("el-dialog",{attrs:{title:"钉钉二维码","append-to-body":!0,width:"500px",visible:t.isDDErcode,"close-on-click-modal":!1},on:{"update:visible":function(e){t.isDDErcode=e}}},[a("iframe",{ref:"iframe",staticStyle:{width:"100%",height:"500px"},attrs:{id:"codeiframe",src:t.dingdingcodeUrl,frameborder:"0"}})]),t.isContactShow?a("el-dialog",{attrs:{title:"联系我们",width:"700px",visible:t.isContactShow,"close-on-click-modal":!1},on:{"update:visible":function(e){t.isContactShow=e}}},[a("div",{staticClass:"contactBox"},[a("div",{staticClass:"leftBox"},[a("p",[t._v("产品咨询：186-0685-8808")]),a("p",[t._v("邮 箱：<EMAIL>")]),a("p",[t._v("Q Q：841850740")]),a("p",[t._v("地址：浙江省嘉兴市嘉善县惠民街道台升大道3号3号楼306室")])]),a("div",{staticClass:"qrCode"},[a("img",{staticStyle:{width:"120px",height:"120px"},attrs:{src:n("4680")}}),a("p",{staticStyle:{"font-size":"16px","font-weight":"700",color:"#000"}},[t._v("激活咨询")])])])]):t._e()],1)},o=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"tenantList-item-icon"},[a("i",{staticClass:"el-icon-office-building"})])}],s=n("ade3"),r=n("c7eb"),l=n("1da1"),c=(n("498a"),n("e9c4"),n("c740"),n("a434"),n("4d90"),n("d3b7"),n("25f0"),n("99af"),n("b64b"),n("a9e3"),n("7db0"),n("b775")),d=n("a78e"),u=n.n(d),p=n("3452"),m=n.n(p),f=(n("4360"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{directives:[{name:"show",rawName:"v-show",value:0==t.step,expression:"step == 0"}],staticClass:"set-item"},[a("div",{staticClass:"dialog-body"},[a("el-form",{ref:"organizationData",attrs:{model:t.organizationData,rules:t.organizationRules,"label-width":"100px"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"公司",prop:"company"}},[a("el-input",{attrs:{placeholder:"请输入公司","auto-complete":"off",clearable:"",size:"small"},model:{value:t.organizationData.company,callback:function(e){t.$set(t.organizationData,"company",e)},expression:"organizationData.company"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"简称",prop:"tenantname"}},[a("el-input",{attrs:{placeholder:"请输入简称","auto-complete":"off",clearable:"",size:"small"},on:{input:t.writeCode},model:{value:t.organizationData.tenantname,callback:function(e){t.$set(t.organizationData,"tenantname",e)},expression:"organizationData.tenantname"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"联系人"}},[a("el-input",{attrs:{placeholder:"请输入联系人","auto-complete":"off",clearable:"",size:"small"},model:{value:t.organizationData.contactor,callback:function(e){t.$set(t.organizationData,"contactor",e)},expression:"organizationData.contactor"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"电话"}},[a("el-input",{attrs:{placeholder:"请输入电话","auto-complete":"off",clearable:"",size:"small"},model:{value:t.organizationData.companytel,callback:function(e){t.$set(t.organizationData,"companytel",e)},expression:"organizationData.companytel"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"公司地址"}},[a("el-input",{attrs:{placeholder:"请输入公司地址","auto-complete":"off",clearable:"",size:"small",type:"textarea",rows:3},model:{value:t.organizationData.companyadd,callback:function(e){t.$set(t.organizationData,"companyadd",e)},expression:"organizationData.companyadd"}})],1)],1)],1)],1)],1),a("div",{staticClass:"dialog-footer"},[a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.createBtn()}}},[t._v("确认")]),a("el-button",{on:{click:t.dialogClose}},[t._v(" 取 消")])],1)])])}),h=[],g=n("d044"),v=n.n(g),w=n("b0b8"),y={name:"Organization",data:function(){return{step:0,weChatVisible:!1,listLoading:!1,organizationData:{tenantcode:"",tenantname:"",company:"",companytel:"",companyadd:"",contactor:"",tenantstate:1,sellerid:"111",sellercode:"sellercode"},organizationRules:{tenantname:[{required:!0,trigger:"blur",message:"组织名称为必填项"}],company:[{required:!0,trigger:"blur",message:"公司全称为必填项"}]}}},watch:{},mounted:function(){},destroyed:function(){},methods:{createBtn:function(){var t=this;this.$refs["organizationData"].validate((function(e){if(!e)return console.log("error submit!!"),!1;var a=JSON.stringify(t.organizationData);console.log("params",a),c["a"].post("/system/SYSM01B3/createByUser",a).then((function(e){200==e.data.code?(t.$message.success("创建组织成功"),t.$emit("getTenant"),t.dialogClose()):t.$message.warning("创建组织失败")})).catch((function(e){t.$message.warning("系统错误，创建组织失败")}))}))},writeCode:function(t){w.setOptions({checkPolyphone:!1,charCase:1}),this.formdata.tenantcode=w.getFullChars(t)},dialogClose:function(){this.organizationData={tenantid:"",tenantcode:"",tenantname:"",company:"",companyadd:"",companytel:"",contactor:"",tenantstate:1},this.$emit("dialogClose")}}},b=y,S=(n("cfb5"),n("16c8"),n("2877")),k=Object(S["a"])(b,f,h,!1,null,"60af80da",null),x=k.exports,C=n("5f87"),_=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{attrs:{id:"forgetStep"}},[a("el-steps",{attrs:{active:t.active,"finish-status":"success"}},[a("el-step",{attrs:{title:"身份验证"}}),a("el-step",{attrs:{title:"密码重置"}}),a("el-step",{attrs:{title:"重置完成"}})],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:0==t.active,expression:"active == 0"}],staticClass:"stepBody"},[a("el-form",{ref:"formdata",attrs:{model:t.formdata,rules:t.formdataRule,"label-width":"20px"}},[a("el-form-item",{attrs:{prop:"username"}},[a("span",{staticClass:"svg-container"},[a("svg-icon",{staticStyle:{color:"#889aa4"},attrs:{"icon-class":"user"}})],1),a("el-input",{attrs:{placeholder:"请输入账号",size:"small","auto-complete":"off"},model:{value:t.formdata.username,callback:function(e){t.$set(t.formdata,"username",e)},expression:"formdata.username"}})],1),a("el-form-item",{attrs:{prop:"code"}},[a("div",{staticStyle:{display:"flex","align-items":"center"}},[a("span",{staticClass:"svg-container",staticStyle:{"align-self":"center"}},[a("svg-icon",{staticStyle:{color:"#889aa4","font-size":"18px","margin-left":"-4px"},attrs:{"icon-class":"code"}})],1),a("el-input",{attrs:{placeholder:"请输入邮箱验证码",size:"small",name:"code","auto-complete":"off","validate-event":!1},model:{value:t.formdata.code,callback:function(e){t.$set(t.formdata,"code",e)},expression:"formdata.code"}}),a("el-button",{staticStyle:{height:"47px"},attrs:{disabled:t.flag},on:{click:t.getEmailCode}},[t._v(t._s(t.btnTitle)+" ")])],1)]),a("div",{staticStyle:{"text-align":"right"}},[a("el-button",{staticStyle:{"margin-top":"12px"},attrs:{type:"primary"},on:{click:function(e){return t.checkCode()}}},[t._v("下一步")]),a("el-button",{staticStyle:{"margin-top":"12px"},on:{click:function(e){return t.resetBtn("formdata")}}},[t._v("重置")])],1)],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:1==t.active,expression:"active == 1"}],staticClass:"stepBody"},[a("el-form",{ref:"pwdformdata",attrs:{model:t.pwdformdata,rules:t.pwdformdataRule,"label-width":"20px"}},[a("el-form-item",{attrs:{prop:"password"}},[a("span",{staticClass:"svg-container"},[a("svg-icon",{staticStyle:{color:"#889aa4"},attrs:{"icon-class":"password"}})],1),a("el-input",{attrs:{placeholder:"请输入新密码",type:t.passwordType,size:"small"},model:{value:t.pwdformdata.password,callback:function(e){t.$set(t.pwdformdata,"password",e)},expression:"pwdformdata.password"}}),a("span",{staticClass:"show-pwd",on:{click:function(e){return t.showPwd("passwordType")}}},[a("svg-icon",{attrs:{"icon-class":"password"===t.passwordType?"eye":"eye-open"}})],1)],1),a("el-form-item",{attrs:{prop:"oldpassword"}},[a("span",{staticClass:"svg-container"},[a("svg-icon",{staticStyle:{color:"#889aa4"},attrs:{"icon-class":"password"}})],1),a("el-input",{attrs:{placeholder:"请重新输入密码",type:t.passwordType2,size:"small"},model:{value:t.pwdformdata.oldpassword,callback:function(e){t.$set(t.pwdformdata,"oldpassword",e)},expression:"pwdformdata.oldpassword"}}),a("span",{staticClass:"show-pwd",on:{click:function(e){return t.showPwd("passwordType2")}}},[a("svg-icon",{attrs:{"icon-class":"password"===t.passwordType2?"eye":"eye-open"}})],1)],1),a("div",{staticStyle:{"text-align":"right"}},[a("el-button",{staticStyle:{"margin-top":"12px"},attrs:{type:"primary"},on:{click:function(e){return t.changPwd()}}},[t._v("下一步")]),a("el-button",{staticStyle:{"margin-top":"12px"},on:{click:function(e){return t.resetBtn("pwdformdata")}}},[t._v("重置")])],1)],1)],1),a("div",{directives:[{name:"show",rawName:"v-show",value:3==t.active,expression:"active == 3"}],staticClass:"stepBody"},[a("div",{staticClass:"stepLast"},[t._m(0),a("el-button",{staticStyle:{"margin-top":"40px"},attrs:{type:"primary"},on:{click:t.forgetclose}},[t._v("重新登陆")])],1)])])},$=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"lastTip"},[a("i",{staticClass:"el-icon-success"}),t._v("新密码重置成功，请重新登陆 ")])}],T=(n("ac1f"),n("00b4"),{data:function(){var t=this,e=function(t,e,a){if(e){var n=/^([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})$/,i=/^(13[0-9]|14[5|7]|15[0|1|2|3|4|5|6|7|8|9]|18[0|1|2|3|5|6|7|8|9])\d{8}$/;if(!n.test(e)&&!i.test(e))return a(new Error("请输入正确的手机或邮箱格式"));Object(c["a"])({url:"/auth/inspect",method:"get",params:{userName:e}}).then((function(t){200==t.data.code?a(new Error("该账号不存在，请确认你的账号")):a()}))}},a=function(e,a,n){""===a?n(new Error("请再次输入密码")):a!==t.pwdformdata.password?n(new Error("两次输入密码不一致!")):n()},n=function(e,a,n){a&&(console.log(a,t.formdata.username),Object(c["a"])({url:"/auth/checkCaptcha",method:"get",params:{code:a.toUpperCase(),username:t.formdata.username}}).then((function(t){200!=t.data.code?n(new Error(t.data.massage)):n()})))};return{formdata:{username:"",code:""},pwdformdata:{password:"",oldpassword:""},active:0,formdataRule:{username:[{required:!0,trigger:"blur",message:"账号为必填项"},{trigger:"blur",validator:e}],code:[{required:!0,trigger:"blur",message:"验证码为必填项"},{required:!0,validator:n}]},pwdformdataRule:{password:[{required:!0,trigger:"blur",message:"密码为必填项"},{min:6,trigger:"blur",message:"密码不能少于 6 位"}],oldpassword:[{min:6,trigger:"blur",message:"密码不能少于 6 位"},{required:!0,validator:a,trigger:"blur"}]},passwordType:"password",passwordType2:"password",count:5,btnTitle:"获取验证码",flag:!1}},methods:{getEmailCode:function(){var t=this;if(""!=this.formdata.username){var e=/^([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})$/,a=/^(13[0-9]|14[5|7]|15[0|1|2|3|4|5|6|7|8|9]|18[0|1|2|3|5|6|7|8|9])\d{8}$/;e.test(this.formdata.username)?c["a"].get("/auth/emailCaptcha?email="+this.formdata.username).then((function(e){if(200==e.data.code){t.$message.success("验证码已发送到邮箱上，请查看");var a=setInterval((function(){t.count<1?(t.flag=!1,t.btnTitle="获取验证码",t.count=5,clearInterval(a)):(t.flag=!0,t.btnTitle=t.count--+"s后重新获取")}),1e3)}})):a.test(this.formdata.username)&&c["a"].get("/auth/phoneCaptcha?phone="+this.formdata.username).then((function(e){if(200==e.data.code){t.$message.success("验证码已发送到手机短信上，请查看");var a=setInterval((function(){t.count<1?(t.flag=!1,t.btnTitle="获取验证码",t.count=5,clearInterval(a)):(t.flag=!0,t.btnTitle=t.count--+"s后重新获取")}),1e3)}}))}else this.$message.warning("账号不能为空")},checkCode:function(){var t=this;this.$refs["formdata"].validate((function(e){if(!e)return console.log("error submit!!"),!1;t.active=1,t.$message.success("身份验证成功")}))},resetBtn:function(t){console.log(this.$refs[t]),this.$refs[t].resetFields()},changPwd:function(){var t=this;this.$refs["pwdformdata"].validate((function(e){if(!e)return console.log("error submit!!"),!1;t.formdata.username,t.pwdformdata.password;c["a"].post("/auth/forgetPwd?key="+t.formdata.username+"&password="+t.pwdformdata.password).then((function(e){200==e.data.code?t.active=3:t.$message.warning("密码重置失败")}))}))},showPwd:function(t){var e=this;"passwordType"==t?this.$nextTick((function(){"password"===e.passwordType?e.passwordType="":e.passwordType="password"})):this.$nextTick((function(){"password"===e.passwordType2?e.passwordType2="":e.passwordType2="password"}))},forgetclose:function(){this.$emit("forgetPwdClose")}}}),M=T,z=(n("10f6"),n("697d"),Object(S["a"])(M,_,$,!1,null,"0a9235b0",null)),P=z.exports,F=[{name:"销售",path:"/S16M03",pid:null,meta:{icon:"D01M03B1",title:"销售"},children:[{name:"送货通",path:"",meta:{icon:"",title:"送货通"},pid:null,children:[{children:null,name:"送货单",path:"/S16/M03B1",meta:{icon:"",title:"送货单"},pid:null}]},{name:"基础信息",path:"",meta:{icon:"",title:"基础信息"},pid:null,children:[{children:null,name:"客户信息",path:"/S16/M01B1",meta:{icon:"",title:"客户信息"},pid:null},{children:null,name:"货品信息",path:"/S16/M02B1",meta:{icon:"",title:"货品信息"},pid:null}]}]},{name:"工具",path:"4",pid:null,meta:{icon:"D05M01B1",title:"工具"},children:[{name:"工具",path:"",meta:{icon:"",title:"工具"},pid:null,children:[{children:null,name:"无档送货单",path:"/S16/M03B2",meta:{icon:"",title:"无档送货单"},pid:null},{children:null,name:"标签打印",path:"/S16/M04B1",meta:{icon:"",title:"标签打印"},pid:null}]}]},{name:"报表",path:"/S16M04",pid:null,meta:{icon:"D03M01B1",title:"报表"},children:[{name:"销售报表",path:"3.1",meta:{icon:"",title:"销售报表"},pid:null,children:[{children:null,name:"销售明细",path:"/S16/M03R1",meta:{icon:"",title:"销售明细"},pid:null},{children:null,name:"销售大屏",path:"/S16/MBIR1",meta:{icon:"",title:"销售大屏"},pid:null}]}]},{name:"系统",path:"9",pid:null,meta:{icon:"user",title:"系统"},children:[{name:"用户参数",path:"",meta:{icon:"",title:"用户参数"},pid:null,children:[{children:null,name:"列表格式",path:"/S16/M87B1",meta:{icon:"",title:"列表格式"},pid:null},{children:null,name:"SPU属性",path:"/S16/M02S2",meta:{icon:"",title:"SPU属性"},pid:null}]},{name:"系统配置",path:"",meta:{icon:"",title:"系统配置"},pid:null,children:[{children:null,name:"数据字典",path:"/S16/M94B1",meta:{icon:"",title:"数据字典"},pid:null},{children:null,name:"报表中心",path:"/S16/M96S1",meta:{icon:"",title:"报表中心"},pid:null},{children:null,name:"场景字段",path:"/S16/M88B1",meta:{icon:"",title:"场景字段"},pid:null}]}]}],D=[{name:"销售",path:"/S16M03",pid:null,meta:{icon:"D01M03B1",title:"销售"},children:[{name:"送货通",path:"",meta:{icon:"",title:"送货通"},pid:null,children:[{children:null,name:"送货单",path:"/S16/M03B1",meta:{icon:"",title:"送货单"},pid:null}]},{name:"基础信息",path:"",meta:{icon:"",title:"基础信息"},pid:null,children:[{children:null,name:"客户信息",path:"/S16/M01B1",meta:{icon:"",title:"客户信息"},pid:null},{children:null,name:"货品信息",path:"/S16/M02B1",meta:{icon:"",title:"货品信息"},pid:null}]}]},{name:"工具",path:"4",pid:null,meta:{icon:"D05M01B1",title:"工具"},children:[{name:"工具",path:"",meta:{icon:"",title:"工具"},pid:null,children:[{children:null,name:"无档送货单",path:"/S16/M03B2",meta:{icon:"",title:"无档送货单"},pid:null},{children:null,name:"标签打印",path:"/S16/M04B1",meta:{icon:"",title:"标签打印"},pid:null}]}]},{name:"报表",path:"/S16M04",pid:null,meta:{icon:"D03M01B1",title:"报表"},children:[{name:"销售报表",path:"3.1",meta:{icon:"",title:"销售报表"},pid:null,children:[{children:null,name:"销售明细",path:"/S16/M03R1",meta:{icon:"",title:"销售明细"},pid:null},{children:null,name:"销售大屏",path:"/S16/MBIR1",meta:{icon:"",title:"销售大屏"},pid:null}]}]},{name:"系统",path:"9",pid:null,meta:{icon:"user",title:"系统"},children:[{name:"用户参数",path:"",meta:{icon:"",title:"用户参数"},pid:null,children:[]},{name:"系统配置",path:"",meta:{icon:"",title:"系统配置"},pid:null,children:[{children:null,name:"数据字典",path:"/S16/M94B1",meta:{icon:"",title:"数据字典"},pid:null},{children:null,name:"报表中心",path:"/S16/M96S1",meta:{icon:"",title:"报表中心"},pid:null}]}]}],I=n("e7fc"),B=n.n(I),E=Object(s["a"])(Object(s["a"])({name:"Login",components:{organization:x,forgetPassword:P},data:function(){var t=function(t,e,a){0==e.trim().length?a(new Error("请输入正确的用户名")):a()},e=function(t,e,a){e.length<6?a(new Error("密码不能少于 6 位")):a()};return{formVisable:!0,dialogVisible:!1,headerTitle:"创建账套",dialogWidth:"800px",loginForm:{UserName:"",Password:"",Code:"",lockPwd:0},loginRules:{UserName:[{required:!0,trigger:"blur",validator:t}],Password:[{required:!0,trigger:"blur",validator:e}],Code:[{required:!0,trigger:"blur",message:"验证码为必填项"}]},isErcode:!0,loading:!1,passwordType:"password",redirect:void 0,API:this.$store.state.app.config.baseURL,apptitle:this.$store.state.app.config.title,sessionLogin:[{icon:n("5afb")},{icon:n("4eb6")},{icon:n("22e1")},{icon:n("bfc7")}],loginTenant:!0,tenantList:[],tenantidActive:"",iscodeError:!1,time_set:null,scankey:"",codeUrl:"",uuid:"",passTesting:!1,forgetPwdVisible:!1,isDDErcode:!1,dingdingcodeUrl:"",codeNum:0,passkey:{},isContactShow:!1,topic:"inks/sadeli/",client:null,sn:""}},watch:{$route:{handler:function(t){this.redirect=t.query&&t.query.redirect},immediate:!0}},destroyed:function(){this.client.end()},created:function(){"notInDingTalk"!=this.$store.state.app.platform?this.showding=!0:this.showding=!1,u.a.set("platform",this.$store.state.appplatform),this.decrypt(),this.getSn()},mounted:function(){c["a"].defaults.baseURL=u.a.get("baseApi"),this.getCookie()}},"destroyed",(function(){window.clearInterval(this.time_set)})),"methods",{routerTo:function(){this.$router.push("/home"),sessionStorage.setItem("navIndex",0)},dbFormat:function(){this.$router.push({path:"/init"})},getCode:function(){var t=this;c["a"].get("/auth/captchaImage").then((function(e){200==e.data.code?(t.codeUrl=e.data.img,t.uuid=e.data.uuid):t.$message.warning("验证么获取失败，请重新获取")})).catch((function(e){t.$message.error(e||"请求错误")}))},showPwd:function(){"password"===this.passwordType?this.passwordType="":this.passwordType="password"},validateCaptcha:function(){var t=this;this.$refs.loginForm.validate((function(e){if(!e)return!1;var a={username:t.loginForm.UserName,password:t.loginForm.Password};c["a"].post("/S16M91S1/login?type=1",JSON.stringify(a)).then((function(e){if(200==e.data.code){1==t.loginForm.lockPwd?t.setCookie(t.loginForm.UserName,t.loginForm.Password,30):t.clearCookie(),t.$notify({title:"登陆成功",type:"success",message:"欢迎登录"});var a=e.data.data.loginuser;a.sn=e.data.data.sn,a.registrkey=e.data.data.registrkey,localStorage.setItem("getInfo",JSON.stringify(a)),t.$store.state.user.userinfo=e.data.data.loginuser,Object(C["c"])(e.data.data.access_token),t.$store.state.user.token=Object(C["a"])(),t.remove(),t.sendMqttMsg("登录成功")}else t.$notify.error({title:"登陆失败",message:e.data.msg}),t.sendMqttMsg("登录失败")}))}))},remove:function(){var t=this,e=[];c["a"].get("/S16M91S1/getUserInfo").then((function(a){if(console.log("getUserInfo",a,t.$store.state.user),200==a.data.code){if(e=a.data.data.adminmark?F:D,2==a.data.data.adminmark){var n=e[e.length-1].children[0].children.findIndex((function(t){return"/S16/M91S1"==t.path}));-1==n&&e[e.length-1].children[0].children.push({children:null,name:"用户设置",path:"/S16/M91S1",meta:{icon:"",title:"用户设置"},pid:null})}else{n=e[e.length-1].children[0].children.findIndex((function(t){return"/S16/M91S1"==t.path}));console.log(n,"index"),-1!=n&&e[e.length-1].children[0].children.splice(n,1)}localStorage.setItem("navjson",JSON.stringify(e)),t.$store.dispatch("app/setnavdata",e),t.$router.push({path:"/dashboard"})}}))},getTenant:function(){var t=this;this.loginTenant=!1;var e=this;c["a"].post("/auth/getTenant").then((function(a){if(200==a.data.code&&(e.tenantList=a.data.data,e.tenantList.length>0)){for(var n=0;n<e.tenantList.length;n++)e.tenantList[n].tenantItemActive=!1;e.tenantList[0].tenantItemActive=!0,t.tenantidActive=e.tenantList[0].tenantid}}))},logout:function(){var t=this;return Object(l["a"])(Object(r["a"])().mark((function e(){return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return window.localStorage.clear(),Object(C["b"])(),t.loginTenant=!t.loginTenant,t.getCode(),e.next=6,t.$store.dispatch("user/logout").then((function(e){t.$notify({title:"账号已安全退出",message:t.dateFormat(),type:"success"}),t.loading=!1})).catch((function(e){t.$message.warning(e)}));case 6:case"end":return e.stop()}}),e)})))()},dateFormat:function(){var t=new Date,e=t.getFullYear(),a=(t.getMonth()+1).toString().padStart(2,"0"),n=t.getDate().toString().padStart(2,"0"),i=t.getHours().toString().padStart(2,"0"),o=t.getMinutes().toString().padStart(2,"0"),s=t.getSeconds().toString().padStart(2,"0");return"".concat(e,"-").concat(a,"-").concat(n," ").concat(i,":").concat(o,":").concat(s)},readnav:function(){var t=this;c["a"].get("/system/SYSM02B2/getMenuWebListBySelf").then((function(e){if(200==e.data.code){var a=e.data.data;localStorage.setItem("navjson",JSON.stringify(a)),t.$store.dispatch("app/setnavdata",a),t.showWorkbench(),t.loading=!1}})).catch((function(t){console.log(t)}))},showWorkbench:function(){var t=this.$store.state.user.userinfo.configs["system.style.dashboard"];if(null==t||""==t)this.$router.push({path:"/dashboard"});else{var e=JSON.parse(t);this.$router.push(e.value)}},setCookie:function(t,e,a){var n=m.a.AES.encrypt(e,"inks").toString(),i=new Date;i.setTime(i.getTime()+864e5*a),window.document.cookie="userName="+t+";path=/;expires="+i.toGMTString(),window.document.cookie="userPwd="+n+";path=/;expires="+i.toGMTString(),window.document.cookie="lockPwd=1;path=/;expires="+i.toGMTString()},getCookie:function(){if(document.cookie.length>0)for(var t=document.cookie.split("; "),e=0;e<t.length;e++){var a=t[e].split("=");if("userName"==a[0])this.loginForm.UserName=a[1];else if("userPwd"==a[0]){var n=a[1],i=m.a.AES.decrypt(n,"inks");this.loginForm.Password=i.toString(m.a.enc.Utf8)}else"lockPwd"==a[0]&&(this.loginForm.lockPwd=Number(a[1]))}},clearCookie:function(){this.setCookie("","",-1)},dialogController:function(){console.log("控制创建组织弹窗",this.dialogVisible),this.dialogVisible=!this.dialogVisible},dialogClose:function(){this.dialogVisible=!1},changeTenantItemActive:function(t,e){for(var a=0;a<this.tenantList.length;a++)this.tenantList[a].tenantItemActive=!1;this.tenantList[t].tenantItemActive=!this.tenantList[t].tenantItemActive,this.tenantidActive=e,this.$forceUpdate()},submitTenant:function(){var t=this;this.loading=!0;var e=this;e.tenantidActive?Object(c["a"])({url:"/auth/token",method:"post",data:e.tenantidActive}).then((function(a){if(console.log("提交租户选择",a),200==a.data.code){var n=a.data.data.loginuser;localStorage.setItem("getInfo",JSON.stringify(n)),t.$store.state.user.userinfo=a.data.data.loginuser,Object(C["c"])(a.data.data.access_token),e.$store.state.user.token=Object(C["a"])();var i=t.tenantList.find((function(t){return t.tenantid==e.tenantidActive}));console.log("currentTenant",i),e.$store.dispatch("user/gettenantInfo",i),t.readnav()}e.loading=!1})):e.$message.warning("暂无租户选择")},openErcode:function(){var t=this,e=this;e.$nextTick((function(){e.isErcode=!1})),document.getElementById("qrcode").innerHTML="",this.scankey="",c["a"].get("/S16M91S1/getScanLoginCode").then((function(e){if(200==e.data.code){console.log("res",e),t.scankey=e.data.data.data.key;var a=JSON.stringify(e.data.data);new v.a("qrcode",{render:"canvas",width:200,height:200,text:t.API+"S16M91S1/getWxScanLogin?data="+a});t.time_set=window.setInterval((function(){setTimeout((function(){t.isLoginSuccess(t.scankey)}),0)}),3e3)}})),setTimeout((function(){document.getElementById("qrcode").innerHTML="";new v.a("qrcode",{render:"canvas",width:200,height:200,text:"二维码过期，请重新生成",colorDark:"#999",colorLight:"#FFF"});window.clearInterval(e.time_set),e.$nextTick((function(){e.iscodeError=!0}))}),3e5)},isLoginSuccess:function(t){var e=this;t&&c["a"].get("/S16M91S1/getScanLoginState?key="+t).then((function(t){if(200==t.data.code&&200==t.data.data.code){console.log("轮询成功",t.data.data.msg),e.$notify({title:"登陆成功",type:"success",message:"欢迎登录"});var a=t.data.data.token.loginuser;localStorage.setItem("getInfo",JSON.stringify(a)),e.$store.state.user.userinfo=t.data.data.token.loginuser,Object(C["c"])(t.data.data.token.access_token),e.$store.state.user.token=Object(C["a"])(),e.remove(),window.clearInterval(e.time_set)}}))},changeErcode:function(){var t=this;t.$nextTick((function(){t.isErcode=!0})),window.clearInterval(t.time_set)},scanDingding:function(){console.log(u.a.get("baseApi")),this.dingdingcodeUrl=u.a.get("baseApi")+"auth/justauth/render/dingtalk",this.isErcode=!0},Collection:function(){try{window.external.addFavorite(location.href,document.title)}catch(e){try{window.sidebar.addPanel(document.title,location.href,"")}catch(e){alert("加入收藏失败，请按Ctrl+D手动添加。")}}},forgetPwd:function(){this.forgetPwdVisible=!0},forgetPwdClose:function(){this.forgetPwdVisible=!1,this.loginForm.Password="",this.getCode()},decrypt:function(){var t=this;this.$request.get("/S16M91S1/decrypt").then((function(e){200==e.data.code&&(null==e.data.data||""!=e.data.data&&(t.passkey=JSON.parse(e.data.data)))})).catch((function(t){console.log(t||"请求错误")}))},intervalTime:function(t){var e=Date.parse(new Date)/1e3,a=e,n=t/1e3,i=1e3*(n-a),o=Math.floor(i/864e5),s=i%864e5,r=(Math.floor(s/36e5),s%36e5),l=(Math.floor(r/6e4),r%6e4);Math.round(l/1e3);return o<0?"已":o+"天 "},getSn:function(){var t=this;this.$request.get("/S16M91S1/getSN").then((function(e){200==e.data.code&&(t.sn=e.data.data,t.initTopic())}))},initTopic:function(){this.topic="inks/sadeli/"+this.sn,this.initMqtt()},initMqtt:function(){var t={clientId:"",port:this.$store.state.app.config.mqttport,connectTimeout:4e3,reconnectPeriod:4e3,username:"inksinfo ",password:"inksinfo@123",keepalive:60};null==this.client&&(this.client=B.a.connect(this.$store.state.app.config.mqtturl,t),this.client.on("connect",(function(t){console.log("mqtt连接成功",e)})),this.client.on("error",(function(t){console.log("mqtt连接失败",t)})))},sendMqttMsg:function(t){this.client.publish(this.topic,{msg:"设备编码：".concat(this.sn,",登录名:").concat(JSON.parse(localStorage.getItem("getInfo")).realname,",").concat(t,"!")},{qos:2})}}),O=E,L=(n("dea1"),n("8e7b"),Object(S["a"])(O,i,o,!1,null,"8fef1c82",null));a["default"]=L.exports},a418:function(t,e,a){},bfc7:function(t,e,a){t.exports=a.p+"static/img/icon_wx.dfac8ef6.png"},c2d6:function(t,e,a){},cfb5:function(t,e,a){"use strict";a("c2d6")},cfcb:function(t,e,a){},dea1:function(t,e,a){"use strict";a("cfcb")}}]);