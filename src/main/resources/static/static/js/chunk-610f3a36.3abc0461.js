(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-610f3a36"],{1534:function(a,t,e){"use strict";e("5e4e")},"5e4e":function(a,t,e){},ecac:function(a,t,e){"use strict";e.r(t);var s=function(){var a=this,t=a.$createElement,e=a._self._c||t;return e("div",{staticClass:"profileBody"},[e("el-row",{attrs:{gutter:10,type:"flex"}},[e("el-col",{attrs:{span:6}},[e("div",{staticClass:"proInfo"},[e("el-card",{staticClass:"box-card"},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[a._v("个人信息")])]),e("div",{staticClass:"text item"},[e("div",{staticClass:"img-content"},[e("div",{staticClass:"img-content-box",on:{click:a.changePhoto}},[e("img",{attrs:{src:"https://oss.aliyuncs.com/aliyun_id_photo_bucket/default_handsome.jpg",alt:""}}),a._v(" : ")])]),e("div",{staticClass:"detail-content"},[e("div",{staticClass:"proInfo-item"},[e("span",[a._v("用户账号")]),e("span",[a._v(a._s(a.listForm.username))])]),e("div",{staticClass:"proInfo-item"},[e("span",[a._v("姓名")]),e("span",[a._v(a._s(a.listForm.realname))])]),e("div",{staticClass:"proInfo-item"},[e("span",[a._v("手机号码")]),e("span",[a._v(a._s(a.listForm.phone||"-"))])]),e("div",{staticClass:"proInfo-item"},[e("span",[a._v("用户邮箱")]),e("span",[a._v(a._s(a.listForm.email||"-"))])])])])])],1)]),e("el-col",{attrs:{span:18}},[e("div",{staticClass:"baseInfo",staticStyle:{height:"100%"}},[e("el-card",{staticClass:"box-card",staticStyle:{height:"100%"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[a._v("基本资料")])]),e("div",{staticClass:"text item"},[e("el-tabs",{on:{"tab-click":a.handleClick},model:{value:a.activeName,callback:function(t){a.activeName=t},expression:"activeName"}},[e("el-tab-pane",{attrs:{label:"基本资料",name:"first"}},[e("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:a.formdata,"label-width":"100px","auto-complete":"on"}},[e("el-row",[e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"昵称",prop:"realname"}},[e("el-input",{staticStyle:{"min-width":"140px !important"},attrs:{placeholder:"请输入用户昵称",clearable:""},model:{value:a.formdata.realname,callback:function(t){a.$set(a.formdata,"realname",t)},expression:"formdata.realname"}})],1)],1),e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"手机号码",prop:"mobile"}},[e("el-input",{staticStyle:{"min-width":"140px !important"},attrs:{placeholder:"请输入手机号码",clearable:""},model:{value:a.formdata.phone,callback:function(t){a.$set(a.formdata,"phone",t)},expression:"formdata.phone"}})],1)],1),e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"邮箱",prop:"email"}},[e("el-input",{staticStyle:{"min-width":"140px !important"},attrs:{placeholder:"请输入邮箱",clearable:""},model:{value:a.formdata.email,callback:function(t){a.$set(a.formdata,"email",t)},expression:"formdata.email"}})],1)],1),e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"性别"}},[e("el-radio",{attrs:{label:0},model:{value:a.formdata.sex,callback:function(t){a.$set(a.formdata,"sex",t)},expression:"formdata.sex"}},[a._v("男")]),e("el-radio",{attrs:{label:1},model:{value:a.formdata.sex,callback:function(t){a.$set(a.formdata,"sex",t)},expression:"formdata.sex"}},[a._v("女")])],1)],1),e("el-col",{attrs:{span:22,offset:2}},[e("el-button",{staticStyle:{"background-color":"#1890ff"},attrs:{type:"primary",size:"small"},on:{click:function(t){return a.saveBaseInfo()}}},[a._v(" 保 存 信 息")]),e("el-button",{staticStyle:{"background-color":"#ff4949"},attrs:{type:"danger",size:"small"},on:{click:function(t){return a.closeTag()}}},[a._v("关 闭")])],1)],1)],1)],1),e("el-tab-pane",{attrs:{label:"修改密码",name:"second"}},[e("el-form",{ref:"pwdformdata",staticClass:"custInfo",staticStyle:{"min-height":"200px"},attrs:{model:a.pwdformdata,rules:a.pwdformdataRule,"label-width":"100px","auto-complete":"on"}},[e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"原密码",prop:"originalpassword"}},[e("div",{staticStyle:{position:"relative"}},[e("el-input",{attrs:{placeholder:"请输入原密码",type:a.passwordType1},model:{value:a.pwdformdata.originalpassword,callback:function(t){a.$set(a.pwdformdata,"originalpassword",t)},expression:"pwdformdata.originalpassword"}}),e("span",{staticClass:"show-pwd",on:{click:function(t){return a.showPwd("passwordType1")}}},[e("svg-icon",{attrs:{"icon-class":"password"===a.passwordType1?"eye":"eye-open"}})],1)],1)])],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"新密码",prop:"password"}},[e("div",{staticStyle:{position:"relative"}},[e("el-input",{attrs:{placeholder:"请输入新密码",type:a.passwordType},model:{value:a.pwdformdata.password,callback:function(t){a.$set(a.pwdformdata,"password",t)},expression:"pwdformdata.password"}}),e("span",{staticClass:"show-pwd",on:{click:function(t){return a.showPwd("passwordType")}}},[e("svg-icon",{attrs:{"icon-class":"password"===a.passwordType?"eye":"eye-open"}})],1)],1)])],1)],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("el-form-item",{attrs:{label:"确认密码",prop:"oldpassword"}},[e("div",{staticStyle:{position:"relative"}},[e("el-input",{attrs:{placeholder:"请重新输入密码",type:a.passwordType2},model:{value:a.pwdformdata.oldpassword,callback:function(t){a.$set(a.pwdformdata,"oldpassword",t)},expression:"pwdformdata.oldpassword"}}),e("span",{staticClass:"show-pwd",on:{click:function(t){return a.showPwd("passwordType2")}}},[e("svg-icon",{attrs:{"icon-class":"password"===a.passwordType2?"eye":"eye-open"}})],1)],1)])],1)],1),e("el-row",[e("el-col",{attrs:{span:22,offset:2}},[e("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],staticStyle:{"background-color":"#1890ff"},attrs:{type:"primary",size:"small"},on:{click:function(t){return a.changPwd()}}},[a._v(" 修 改 密 码")]),e("el-button",{attrs:{size:"small"},on:{click:function(t){return a.resetBtn("pwdformdata")}}},[a._v("重置")])],1)],1)],1)],1)],1)],1)])],1)])],1),e("div",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}]},[e("input",{ref:"upload",attrs:{type:"file"},on:{change:a.getFile}})])],1)},o=[],r=e("c7eb"),i=e("1da1"),l=e("5530"),n=(e("b64b"),e("e9c4"),e("b680"),e("b0c0"),e("caad"),e("b775")),d=e("5f87"),c=e("2f62"),p=e("6ca8"),m=e.n(p),f=(e("a78e"),{data:function(){var a=this,t=function(t,e,s){""===e?s(new Error("请再次输入密码")):e!==a.pwdformdata.password?s(new Error("两次输入密码不一致!")):s()};return{activeName:"first",formdata:{},pwdformdata:{password:"",oldpassword:"",originalpassword:""},listForm:{},pwdformdataRule:{originalpassword:[{required:!0,trigger:"blur",message:"原密码为必填项"},{min:6,trigger:"blur",message:"密码不能少于 6 位"}],password:[{required:!0,trigger:"blur",message:"密码为必填项"},{min:6,trigger:"blur",message:"密码不能少于 6 位"}],oldpassword:[{min:6,trigger:"blur",message:"密码不能少于 6 位"},{required:!0,validator:t,trigger:"blur"}]},passwordType:"password",passwordType1:"password",passwordType2:"password",isErcode:!1}},computed:Object(l["a"])({},Object(c["b"])(["userinfo","avatar","name"])),created:function(){this.bindData()},methods:{bindData:function(){var a=this;return Object(i["a"])(Object(r["a"])().mark((function t(){var e;return Object(r["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e=JSON.parse(window.localStorage.getItem("getInfo")).userid,JSON.parse(window.localStorage.getItem("getInfo")).tenantid,t.next=4,n["a"].get("/S16M91S1/getEntity?key=".concat(e)).then((function(t){if(200==t.data.code)for(var e in a.formdata=t.data.data,t.data.data)a.listForm[e]=t.data.data[e]}));case 4:case"end":return t.stop()}}),t)})))()},saveBaseInfo:function(){var a=this,t={id:this.formdata.id,realname:this.formdata.realname,phone:this.formdata.phone,email:this.formdata.email,sex:this.formdata.sex};n["a"].post("/S16M91S1/update",JSON.stringify(t)).then((function(t){200==t.data.code?a.$message.success("个人信息修改成功"):a.$message.warning("个人信息修改失败"),a.bindData()}))},closeTag:function(){this.$store.dispatch("tagsView/delView",this.$route),this.$router.push({path:"/"})},resetBtn:function(a){this.$refs[a].resetFields()},changPwd:function(){var a=this;this.$refs["pwdformdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;n["a"].get("/S16M91S1/updatePass?oldpassword="+a.pwdformdata.originalpassword+"&newpassword="+a.pwdformdata.password).then((function(t){200==t.data.code?(a.active=3,a.$message.success(t.data.msg||"密码修改成功"),Object(d["b"])(),a.$router.push({path:"/login"}),a.$store.dispatch("user/logout"),a.$store.dispatch("tagsView/delAllViews")):a.$message.warning(t.data.msg||"密码重置失败")}))}))},showPwd:function(a){var t=this;this.$nextTick((function(){"password"===t[a]?t[a]="":t[a]="password"}))},getFile:function(a){var t=this.$refs.upload,e=t.files[0],s=this,o=[".png",".PNG",".jpg",".JPG"],r=(parseFloat(e.size)/1024/1024).toFixed(2),i=e.name.lastIndexOf("."),l=e.name.length,n=e.name.substring(i,l);o.includes(n)?m()(e).then((function(a){console.log(a.fileLen/1024/1024,a);var t=(a.fileLen/1024/1024).toFixed(2);if(t>.4)s.$message.warning("请上传小于2MB的图片，此图片".concat(r,"MB"));else{var e=a.base64.split("data:image/jpeg;base64,")[1];s.formdata.avatar=e,s.saveBaseInfo()}})).catch((function(a){console.log("压缩失败了",a)})).always((function(){console.log("压缩成功")})):s.$message.warning("请添加正确的图片格式")},changePhoto:function(){this.$refs.upload.click()},handleClick:function(a,t){"个性化"==a.label&&(this.getDingInfo(),this.getWeChartInfo(),this.getAuthorizeInfo())}}}),u=f,w=(e("1534"),e("2877")),h=Object(w["a"])(u,s,o,!1,null,"41a6c4f1",null);t["default"]=h.exports}}]);