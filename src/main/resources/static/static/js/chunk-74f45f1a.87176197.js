(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-74f45f1a"],{"0437":function(t,e,a){},"0af7":function(t,e,a){"use strict";a("34d4")},"1d1f":function(t,e,a){},"1d4b":function(t,e,a){"use strict";a("1d1f")},"34d4":function(t,e,a){},"56e1":function(t,e,a){},"5e5b":function(t,e,a){"use strict";a("0437")},9828:function(t,e,a){},d4da:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.formvisible?a("div",{ref:"formadd",staticClass:"formadd"},[a("formadd",t._g({ref:"formadd",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm}))],1):a("div",{ref:"index",staticClass:"index"},[a("div",{staticClass:"page-container"},[a("div",{staticStyle:{padding:"20px 20px 0px 20px"}},[a("el-row",{staticClass:"row-con",attrs:{gutter:10}},[a("el-col",{attrs:{span:19}},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",{staticStyle:{"font-weight":"bold"}},[t._v("待开票订单")])]),a("div",{staticClass:"card-content"},[a("div",{staticStyle:{display:"flex","justify-content":"space-around"}},[a("div",{staticClass:"card-content-item"},[a("p",{staticClass:"title"},[t._v("待开发票金额")]),a("p",[t._v("¥1000")])]),a("div",{staticClass:"card-content-item",staticStyle:{"border-left":"1px solid #ddd"}},[a("p",{staticClass:"title"},[t._v("待开发票订单数")]),a("p",[t._v("5")])]),a("div",{staticClass:"card-content-item",staticStyle:{"border-left":"1px solid #ddd"}},[a("p",{staticClass:"title"},[t._v("正在申请发票数")]),a("p",[t._v("5")])]),a("div",{staticClass:"card-content-item",staticStyle:{"border-left":"1px solid #ddd"}},[a("div",{staticStyle:{"text-align":"center","margin-top":"20px"}},[a("button",{staticClass:"payBtn",on:{click:function(e){return t.showForm(0)}}},[t._v(" 去开票 ")])])])])])])],1),a("el-col",{attrs:{span:5}},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",{staticStyle:{"font-weight":"bold"}},[t._v("发票信息及地址管理")]),a("el-button",{staticStyle:{float:"right",padding:"0"},attrs:{type:"text"},on:{click:function(e){return t.eidtInfo()}}},[t._v("编辑")])],1),a("div",{staticClass:"card-item"},[t._v(" 发票抬头（默认）：浙江省嘉善县应凯科技有限公司 ")]),a("div",{staticClass:"card-item"},[t._v("信用代码（默认）：9133042167162966x1")]),a("div",{staticClass:"card-item"},[t._v("开户银行（默认）：9133042167162966x1")]),a("div",{staticClass:"card-item"},[t._v("开户账号（默认）：9133042167162966x1")]),a("div",{staticClass:"card-item"},[t._v(" 地址（默认）：浙江省嘉善县惠民街道永盛路 ")])])],1)],1)],1),a("el-divider",{attrs:{"content-position":"left"}},[t._v("开票记录")]),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{overflow:"auto",width:"98%",margin:"0 1%"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},height:t.tableMaxHeight}},[a("el-table-column",{attrs:{align:"center",label:"ID","min-width":"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.$index+1)+" ")]}}])}),a("el-table-column",{attrs:{label:"开票日期","min-width":"100",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.billdate)+" ")]}}])}),a("el-table-column",{attrs:{label:"发票编码",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.refno))])]}}])}),a("el-table-column",{attrs:{label:"发票类型","min-width":"100",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.billtype)+" ")]}}])}),a("el-table-column",{attrs:{label:"发票抬头","min-width":"100",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.invotitle)+" ")]}}])}),a("el-table-column",{attrs:{label:"抬头类型","min-width":"100",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.titletype)+" ")]}}])}),a("el-table-column",{attrs:{label:"发票总额","min-width":"100",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.billtaxamount)+" ")]}}])}),a("el-table-column",{attrs:{label:"账号","min-width":"100",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.username)+" ")]}}])}),a("el-table-column",{attrs:{label:"姓名","min-width":"100",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.realname)+" ")]}}])}),a("el-table-column",{attrs:{label:"备注","min-width":"100",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.remark)+" ")]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","min-width":"120px","class-name":"small-padding fixed-width"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{size:"mini",type:"text"}},[t._v("退票")]),a("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(a){return t.handleusers(e.row)}}},[t._v("详情")]),a("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(a){return t.handlePiPerMission(e.row)}}},[t._v("发票下载")])]}}])})],1)],1)]),a("el-drawer",{attrs:{visible:t.usersVisible,"with-header":!1,size:"50%"},on:{"update:visible":function(e){t.usersVisible=e}}},[t.usersVisible?a("formedit",t._g({ref:"formedit",attrs:{idx:t.idx,functionData:t.usersdata}},{closeBtn:t.usersclose})):t._e()],1),t.infoFormVisible?a("el-dialog",{attrs:{title:"发票信息","append-to-body":!0,visible:t.infoFormVisible,width:"600px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.infoFormVisible=e}}},[a("el-form",{ref:"formdata",attrs:{model:t.formdata,rules:t.formdataRule,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"发票抬头",prop:"invotitle"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入发票抬头",clearable:""},model:{value:t.formdata.invotitle,callback:function(e){t.$set(t.formdata,"invotitle",e)},expression:"formdata.invotitle"}})],1),a("el-form-item",{attrs:{label:"信用代码",prop:"creditnum"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入信用代码",clearable:""},model:{value:t.formdata.creditnum,callback:function(e){t.$set(t.formdata,"creditnum",e)},expression:"formdata.creditnum"}})],1),a("el-form-item",{attrs:{label:"开户银行",prop:"bankname"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入开户银行"},model:{value:t.formdata.bankname,callback:function(e){t.$set(t.formdata,"bankname",e)},expression:"formdata.bankname"}})],1),a("el-form-item",{attrs:{label:"开户账号",prop:"bandaccount"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入开户账号"},model:{value:t.formdata.bandaccount,callback:function(e){t.$set(t.formdata,"bandaccount",e)},expression:"formdata.bandaccount"}})],1),a("el-form-item",{attrs:{label:"电话",prop:"bustel"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入电话",clearable:""},model:{value:t.formdata.bustel,callback:function(e){t.$set(t.formdata,"bustel",e)},expression:"formdata.bustel"}})],1),a("el-form-item",{attrs:{label:"地址",prop:"busaddress"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入地址",clearable:""},model:{value:t.formdata.busaddress,callback:function(e){t.$set(t.formdata,"busaddress",e)},expression:"formdata.busaddress"}})],1),a("el-form-item",{attrs:{label:"电子邮件",prop:"email"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入电子邮件",clearable:""},model:{value:t.formdata.email,callback:function(e){t.$set(t.formdata,"email",e)},expression:"formdata.email"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.infoSubmit()}}},[t._v("确 定")]),a("el-button",{on:{click:function(e){t.infoFormVisible=!1}}},[t._v("取 消")])],1)],1):t._e()],1)},o=[],s=a("c7eb"),l=a("1da1"),n=(a("4d90"),a("d3b7"),a("25f0"),a("99af"),a("e9c4"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{class:t.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[a("div",[a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnSearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnSearch},slot:"append"},[t._v("搜索 ")])],1),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-plus",plain:"",size:"mini"},on:{click:t.btnAdd}},[t._v(" 添加 ")])],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{icon:t.iShow?"el-icon-arrow-up":"el-icon-arrow-down",size:"mini",title:"高级搜索"},on:{click:function(e){t.iShow=!t.iShow}}})],1)]),a("el-collapse-transition",[a("div",{directives:[{name:"show",rawName:"v-show",value:t.iShow,expression:"iShow"}],staticClass:"searchDetail"},[a("el-form",{ref:"formdata",attrs:{model:t.formdata,"label-width":"100px","auto-complete":"on"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"租户号"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入货品编码",size:"small"},model:{value:t.formdata.goodsname,callback:function(e){t.$set(t.formdata,"goodsname",e)},expression:"formdata.goodsname"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"租户名称"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入货品名称",size:"small"},model:{value:t.formdata.goodsname,callback:function(e){t.$set(t.formdata,"goodsname",e)},expression:"formdata.goodsname"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"状态"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择状态",size:"small",clearable:""},model:{value:t.formdata.goodsstate,callback:function(e){t.$set(t.formdata,"goodsstate",e)},expression:"formdata.goodsstate"}},[a("el-option",{attrs:{label:"正常",value:"正常"}}),a("el-option",{attrs:{label:"停用",value:"停用"}})],1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px","margin-top":"6px"},attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.advancedSearch()}}},[t._v(" 搜索 ")])],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"公司"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入公司",size:"small"},model:{value:t.formdata.goodsname,callback:function(e){t.$set(t.formdata,"goodsname",e)},expression:"formdata.goodsname"}})],1)],1)],1)],1)],1)])],1)}),r=[],c={name:"Listheader",data:function(){return{strfilter:"",iShow:!1,formdata:{}}},methods:{btnAdd:function(){this.$emit("btnAdd")},btnSearch:function(){console.log(this.strfilter),this.$emit("btnSearch",this.strfilter)},bindData:function(){this.$emit("bindData")}}},d=c,m=(a("1d4b"),a("2877")),f=Object(m["a"])(d,n,r,!1,null,"32255516",null),u=f.exports,p=a("333d"),h=a("b775"),b=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.drawclose(e)}}},[t._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("div",{staticClass:"flex j-end p-a",staticStyle:{top:"5px",right:"0"}},[0!==t.idx?a("div",{staticClass:"refNo flex j-end"},[a("span",[t._v("NO： "+t._s(t.formdata.refno)+" ")]),a("span",[t._v(" 单据日期： "+t._s(t._f("dateFormat")(t.formdata.billdate))+" ")])]):t._e()]),a("el-form",{staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"组织名称",prop:"tenantname"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{readonly:!0,size:"small"},model:{value:t.formdata.tenantname,callback:function(e){t.$set(t.formdata,"tenantname",e)},expression:"formdata.tenantname"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"公司",prop:"company"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{readonly:!0,size:"small"},model:{value:t.formdata.company,callback:function(e){t.$set(t.formdata,"company",e)},expression:"formdata.company"}})],1)],1)],1)],1),a("el-divider")],1),a("div",{staticClass:"form-body form f-1"}),a("el-form",{staticClass:"form",attrs:{"label-width":"80px"}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"创建"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.modifydate)))])])],1)],1)],1)],1)])])},g=[];a("b64b");const v={add(t){return new Promise((e,a)=>{var i=JSON.stringify(t);h["a"].post("/system/SYSM01B3/create",i).then(t=>{console.log(t),200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},update(t){return new Promise((e,a)=>{var i=JSON.stringify(t);h["a"].post("/system/SYSM01B3/update",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},delete(t){return new Promise((e,a)=>{h["a"].get("/system/SYSM01B3/delete?key="+t).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},addUserItem(t){return new Promise((e,a)=>{var i=JSON.stringify(t);h["a"].post("/system/SYSM01B4/create",i).then(t=>{console.log(i,t),200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},updateUserItem(t){return new Promise((e,a)=>{var i=JSON.stringify(t);h["a"].post("/system/SYSM01B4/update",i).then(t=>{console.log(i,t),200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},deleteUserItem(t){return new Promise((e,a)=>{h["a"].get("/system/SYSM01B4/delete?key="+t).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})}};var w=v,x=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:" f-1 form",staticStyle:{height:"100%"}},[a("div",{staticClass:"elitem-foot"},[a("div",{staticClass:"elitem-foot-item"},[a("p",{staticClass:"title"},[t._v("可开票数量")]),a("p",[t._v(t._s(t.total))])]),a("div",{staticClass:"elitem-foot-item",staticStyle:{"border-left":"1px solid #bbbbbb"}},[a("p",{staticClass:"title"},[t._v("当前开票数量")]),a("p",[t._v(t._s(t.selectLength))])]),a("div",{staticClass:"elitem-foot-item",staticStyle:{"border-left":"1px solid #bbbbbb"}},[a("p",{staticClass:"title"},[t._v("当前发票金额")]),a("p",{staticClass:"price"},[t._v("¥ "+t._s(0==t.formdata.billtaxamount?"0.00":t.formdata.billtaxamount.toFixed(2)))])]),a("div",{staticClass:"elitem-foot-item",staticStyle:{"border-left":"1px solid #bbbbbb"}},[a("button",{staticClass:"payBtn",on:{click:function(e){return t.addInvoicing()}}},[t._v("申请开票")])])]),a("div",{staticClass:"table-container table-position"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"multipleTable",staticClass:"tb-edit tableBox",staticStyle:{overflow:"auto"},attrs:{height:420,data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"4px 0px 4px 0px"},"cell-style":{padding:"4px 0px"},"row-style":{height:"20px"}},on:{"selection-change":t.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"40"}}),a("el-table-column",{attrs:{align:"center",label:"ID","min-width":"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.$index+1)+" ")]}}])}),a("el-table-column",{attrs:{label:"单据编码",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.refno))])]}}])}),a("el-table-column",{attrs:{label:"单据日期",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("dateFormat")(e.row.billdate)))])]}}])}),a("el-table-column",{attrs:{label:"服务名称",align:"center","min-width":"60px","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.functionname))])]}}])}),a("el-table-column",{attrs:{label:"姓名",align:"center","min-width":"60px","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.realname))])]}}])}),a("el-table-column",{attrs:{label:"组织",align:"center","min-width":"60px","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.tenantname))])]}}])}),a("el-table-column",{attrs:{label:"单价",align:"center","min-width":"50px"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.taxprice))])]}}])}),a("el-table-column",{attrs:{label:"金额",align:"center","min-width":"50px"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.taxamount))])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1),t.OrderFormVisible?a("el-dialog",{attrs:{title:"订单信息","append-to-body":!0,visible:t.OrderFormVisible,width:"800px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.OrderFormVisible=e}}},[a("div",{staticClass:"dialog-body"},[a("div",{staticClass:"search"},[a("div",{staticClass:"searchinfo"},[a("span",[t._v("No:"+t._s(t.searchTable.refno))]),a("span",[t._v("日期:"+t._s(t._f("dateFormat")(t.searchTable.billdate)))])]),a("el-table",{staticStyle:{width:"100%","margin-top":"10px"},attrs:{data:t.searchTable.item,border:"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"4px 0px 4px 0px"}}},[a("el-table-column",{attrs:{align:"center",label:"ID","min-width":"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.$index+1)+" ")]}}],null,!1,3056706777)}),a("el-table-column",{attrs:{prop:"functioncode",label:"服务编码",width:"180",align:"center"}}),a("el-table-column",{attrs:{prop:"functionname",label:"服务名称",width:"180",align:"center"}}),a("el-table-column",{attrs:{label:"时长",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("dataCycleFormat")(e.row.cyclecode)))])]}}],null,!1,*********)}),a("el-table-column",{attrs:{prop:"taxprice",label:"单价",align:"center"}}),a("el-table-column",{attrs:{prop:"quantity",label:"数量",align:"center"}}),a("el-table-column",{attrs:{prop:"taxamount",label:"金额",align:"center"}})],1)],1)]),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"small",type:"primary"},on:{click:function(e){t.OrderFormVisible=!1}}},[t._v("确 定")])],1)]):t._e()],1)},y=[],S=(a("fb6a"),{name:"Elitem",props:["formdata","lstitem","idx"],components:{Pagination:p["a"]},data:function(){return{title:"发票",formLabelWidth:"100px",listLoading:!1,OrderFormVisible:!1,lst:[],price:0,selectLength:0,searchTable:[],searchVal:"",total:0,queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},watch:{},created:function(){this.lst=[],this.bindData()},methods:{bindData:function(){var t=this;return Object(l["a"])(Object(s["a"])().mark((function e(){return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,h["a"].post("/system/SYSM10B2/getPageList",JSON.stringify(t.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}));case 2:case"end":return e.stop()}}),e)})))()},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},getselPwProcess:function(t){var e=this;console.log(t),this.OrderFormVisible=!0,h["a"].get("/system/SYSM10B2/getBillEntity?key="+t.pid).then((function(t){console.log(t),e.searchTable=t.data.data}))},addInvoicing:function(){var t=this;0!=this.selectLength?(console.log("申请开票",this.formdata),h["a"].post("/system/SYSM10B3/create",JSON.stringify(this.formdata)).then((function(e){console.log(e),200==e.data.code?t.$message.success("申请开票成功"):t.$message.warning("申请开票失败")}))):this.$message.warning("请选择要开票的订单")},handleSelectionChange:function(t){console.log("select",t),this.selectLength=t.length;var e=0;this.formdata.item=[];for(var a=0;a<t.length;a++)e+=t[a].taxamount,this.formdata.item.push(t[a]);this.formdata.billtaxamount=e},cleValidate:function(t){this.$refs.itemForm.clearValidate(t)}},filters:{dateFormat:function(t){if(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),o=e.getDate().toString().padStart(2,"0"),s=e.getHours().toString().padStart(2,"0"),l=e.getMinutes().toString().padStart(2,"0"),n=e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(o," ").concat(s,":").concat(l,":").concat(n)}},dataCycleFormat:function(t){var e=t.slice(0),a="";switch(e[0]){case"W":a=e[1]+"周";break;case"M":a=e[1]+"月";break;case"Y":a=e[1]+"年";break;default:a=t;break}return a}}}),_=S,k=(a("5e5b"),Object(m["a"])(_,x,y,!1,null,"0c845a75",null)),C=k.exports,F={name:"SYSM01B3",components:{elitem:C},props:["idx","functionData"],data:function(){return{formLabelWidth:"100px",formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,item:[]}}},computed:{formcontainHeight:function(){return window.innerHeight-50+"px"}},watch:{idx:function(t,e){console.log("new: %s, old: %s",t,e),this.bindData()}},created:function(){this.bindData()},methods:{bindData:function(){0!=this.idx&&(console.log(this.functionData),this.formdata=this.functionData)},submitForm:function(){this.$refs[formdata].validate((function(t){if(!t)return console.log("error submit!!"),!1}))},drawclose:function(){this.$emit("closeBtn")}},filters:{dateFormat:function(t){if(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),o=e.getDate().toString().padStart(2,"0"),s=e.getHours().toString().padStart(2,"0"),l=e.getMinutes().toString().padStart(2,"0"),n=e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(o," ").concat(s,":").concat(l,":").concat(n)}}}},$=F,P=(a("f952"),Object(m["a"])($,b,g,!1,null,"1dda032b",null)),z=P.exports,D=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.closeForm(e)}}},[t._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%","flex-wrap":"nowrap"},style:{height:t.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth,"auto-complete":"on",rules:t.formRules}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"发票抬头",prop:"invotitle"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入发票抬头",size:"small"},model:{value:t.formdata.invotitle,callback:function(e){t.$set(t.formdata,"invotitle",e)},expression:"formdata.invotitle"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"抬头类型",prop:"titletype"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入抬头类型",size:"small"},model:{value:t.formdata.titletype,callback:function(e){t.$set(t.formdata,"titletype",e)},expression:"formdata.titletype"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"发票类型",prop:"invotype"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入发票类型",size:"small"},model:{value:t.formdata.invotype,callback:function(e){t.$set(t.formdata,"invotype",e)},expression:"formdata.invotype"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"信用代码"}},[a("el-input",{attrs:{placeholder:"请输入信用代码",size:"small"},model:{value:t.formdata.creditnum,callback:function(e){t.$set(t.formdata,"creditnum",e)},expression:"formdata.creditnum"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"电话",prop:"bustel"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入电话",size:"small"},model:{value:t.formdata.bustel,callback:function(e){t.$set(t.formdata,"bustel",e)},expression:"formdata.bustel"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"地址",prop:"busaddress"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入地址",size:"small"},model:{value:t.formdata.busaddress,callback:function(e){t.$set(t.formdata,"busaddress",e)},expression:"formdata.busaddress"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"开户银行",prop:"bankname"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入开户银行",size:"small"},model:{value:t.formdata.bankname,callback:function(e){t.$set(t.formdata,"bankname",e)},expression:"formdata.bankname"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"开户账号",prop:"bandaccount"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入开户账号",size:"small"},model:{value:t.formdata.bandaccount,callback:function(e){t.$set(t.formdata,"bandaccount",e)},expression:"formdata.bandaccount"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"备注","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入备注",clearable:"",size:"small"},model:{value:t.formdata.summary,callback:function(e){t.$set(t.formdata,"summary",e)},expression:"formdata.summary"}})],1)],1)],1)],1),a("el-divider",{attrs:{"content-position":"left"}},[a("b",[t._v("可开票订单")])])],1),a("div",{staticClass:"form-body form f-1"},[a("elitem",{ref:"elitem",staticStyle:{width:"99%"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,idx:t.idx},on:{bindData:t.bindData}})],1)])])])},O=[],N=(a("498a"),{name:"Formedit",components:{elitem:C},filters:{dateFormat:function(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),o=e.getDate().toString().padStart(2,"0"),s=e.getHours().toString().padStart(2,"0"),l=e.getMinutes().toString().padStart(2,"0"),n=e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(o," ").concat(s,":").concat(l,":").concat(n)}},props:["idx","title"],data:function(){var t=function(t,e,a){console.log(e),0==e.trim().length?a(new Error("请选择员工")):a()};return{formdata:{invotitle:"",titletype:"",invotype:"",creditnum:"",bankname:"",bandaccount:"",billtaxamount:0,busaddress:"",bustel:"",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,item:[]},formRules:{test:[{required:!0,trigger:"blur",validator:t}]},formLabelWidth:"100px",formheight:"500px"}},computed:{formcontainHeight:function(){return window.innerHeight-50-33-50+"px"}},watch:{idx:function(t,e){console.log("new: %s, old: %s",t,e),this.bindData()}},created:function(){console.log(this.idx),this.bindData()},methods:{bindData:function(){this.listLoading=!0},submitForm:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return console.log("error submit!!"),!1;console.log("可保存"),e.saveForm()}))},saveForm:function(){var t=this;0==this.idx?w.add(this.formdata).then((function(e){console.log("新建保存====",e),200==e.code&&(t.$message({showClose:!0,message:"保存成功",type:"success"}),t.$emit("compForm"))})).catch((function(e){t.$message({showClose:!0,message:"保存失败",type:"warning"})})):w.update(this.formdata).then((function(e){200==e.code&&(t.$message({showClose:!0,message:"保存成功",type:"success"}),t.$emit("compForm"))})).catch((function(e){t.$message({showClose:!0,message:"保存失败",type:"warning"})}))},closeForm:function(){this.$emit("closeForm"),console.log("关闭窗口")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){console.log("执行删除"),w.delete(t).then((function(t){200==t.code&&e.$message({showClose:!0,message:"删除成功",type:"success"}),e.$emit("compForm")})).catch((function(){e.$message({showClose:!0,message:"删除失败",type:"warning"})}))})).catch((function(){}))},check:function(){console.log("check")},selectGoods:function(){var t=this.$refs.selectGoods.selrows;console.log(t),this.formdata.GoodsUid=t.GoodsUid,this.formdata.GoodsName=t.GoodsName,this.formdata.GoodsUnit=t.GoodsUnit,this.formdata.GoodsSpec=t.GoodsSpec,this.formdata.Goodsid=t.id,this.selVisible=!1}}}),L=N,V=(a("e318"),Object(m["a"])(L,D,O,!1,null,"394befc0",null)),q=V.exports,M={name:"TodoPerson",components:{Pagination:p["a"],listheader:u,formadd:q,formedit:z},filters:{dateFormat:function(t){if(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),o=e.getDate().toString().padStart(2,"0");return"".concat(a,"/").concat(i,"/").concat(o)}}},data:function(){return{group:"todo",lst:[],orderLst:[],searchstr:"",total:0,formvisible:!1,tableVisable:!0,listLoading:!0,drawerVisible:!1,drawerSize:"50%",drawdata:"",idx:this.$store.state.user.userinfo.Tenantid,formlist:{},formdata:{},formdataRule:{invotitle:[{required:!0,trigger:"blur",message:"发票抬头为必填项"}],creditnum:[{required:!0,trigger:"blur",message:"信用代码为必填项"}],bankname:[{required:!0,trigger:"blur",message:"开户银行为必填项"}],bandaccount:[{required:!0,trigger:"blur",message:"开户账号为必填项"}],bustel:[{required:!0,trigger:"blur",message:"电话为必填项"}],busaddress:[{required:!0,trigger:"blur",message:"地址为必填项"}],email:[{required:!0,trigger:"blur",message:"电子邮件为必填项"}]},queryParams:{PageNum:1,PageSize:5,OrderType:1,SearchType:1},usersVisible:!1,infoFormVisible:!1}},computed:{tableMaxHeight:function(){return window.innerHeight-160-264+"px"}},watch:{},created:function(){this.searchstr="",this.bindData()},methods:{bindData:function(){var t=this;return Object(l["a"])(Object(s["a"])().mark((function e(){return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.listLoading=!0,e.next=3,h["a"].post("/system/SYSM10B3/getPageTh",JSON.stringify(t.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.item,t.formlist=e.data.data),t.listLoading=!1})).catch((function(e){t.listLoading=!1}));case 3:case"end":return e.stop()}}),e)})))()},eidtInfo:function(){this.infoFormVisible=!0},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){this.bindData()},advancedSearch:function(t){this.queryParams.SearchPojo=t,this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},handleusers:function(t){this.idx=t.tenantid,this.usersdata=t,this.usersVisible=!0},usersclose:function(){this.usersVisible=!1},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1,this.bindData()},compForm:function(){this.bindData(),this.formvisible=!1},handleNodeClick:function(t){console.log(t)},handlePiPerMission:function(t,e){this.idx=t,this.drawdata=e,this.drawerVisible=!0},drawclose:function(){this.drawerVisible=!1},infoSubmit:function(){var t=this;this.$refs["formdata"].validate((function(e){if(!e)return console.log("error submit!!"),!1;t.saveEditInfo()}))},saveEditInfo:function(){var t=this;h["a"].post("/system/",JSON.stringify(this.formdata)).then((function(e){t.infoFormVisible=!1}))}}},B=M,I=(a("0af7"),Object(m["a"])(B,i,o,!1,null,"2d2cab68",null));e["default"]=I.exports},e318:function(t,e,a){"use strict";a("9828")},f952:function(t,e,a){"use strict";a("56e1")}}]);