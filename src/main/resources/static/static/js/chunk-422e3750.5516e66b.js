(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-422e3750"],{"04aa":function(t,e,a){},1893:function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),a("div",[a("el-table",{ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"400px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},"row-class-name":t.rowIndex},on:{"row-click":t.rowClick}},[1==t.multi?a("el-table-column",{attrs:{type:"selection",width:"40"}}):a("el-table-column",{attrs:{label:"",width:"35"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(a){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(t._s(" "))])]}}])}),a("el-table-column",{attrs:{label:"租户名称",align:"center","min-width":"80px","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.tenantname))])]}}])}),a("el-table-column",{attrs:{label:"公司",align:"center",prop:"mobile","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.company?e.row.company:"-"))])]}}])}),a("el-table-column",{attrs:{label:"联系人",align:"center",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.contactor))])]}}])}),a("el-table-column",{attrs:{label:"公司电话",align:"center","min-width":"110"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.companytel?e.row.companytel:"-"))])]}}])}),a("el-table-column",{attrs:{label:"状态",align:"center","min-width":"70"},scopedSlots:t._u([{key:"default",fn:function(e){return["1"==e.row.tenantstate?a("el-tag",{attrs:{size:"medium"}},[t._v("正常")]):a("el-tag",{attrs:{type:"warning",size:"medium"}},[t._v("停用")])]}}])}),a("el-table-column",{attrs:{label:"创建人",align:"center",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.creator?e.row.creator:"-"))])]}}])})],1)],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},o=[],n=(a("e9c4"),a("4d90"),a("d3b7"),a("25f0"),a("99af"),a("b775")),r=a("333d"),s={components:{Pagination:r["a"]},props:["multi"],data:function(){return{title:"租户信息",listLoading:!0,lst:[],searchstr:" ",strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},created:function(){this.searchstr="",this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,console.log("‘查询",t),this.bindData()},bindData:function(){var t=this;n["a"].post("/system/SYSM01B3/getPageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={tenantname:t,company:t,contactor:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},rowIndex:function(t){var e=t.row,a=t.rowIndex;e.row_index=a},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)}},filters:{dateFormat:function(t){if(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),o=e.getDate().toString().padStart(2,"0"),n=e.getHours().toString().padStart(2,"0"),r=e.getMinutes().toString().padStart(2,"0"),s=e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(o," ").concat(n,":").concat(r,":").concat(s)}}}},l=s,c=(a("2e08"),a("2877")),d=Object(c["a"])(l,i,o,!1,null,"42ae1c12",null);e["a"]=d.exports},"2e08":function(t,e,a){"use strict";a("6bed")},"48da":function(t,e,a){"use strict";a.d(e,"a",(function(){return l}));a("2909"),a("d3b7"),a("c19f"),a("ace4"),a("5cc6"),a("9a8c"),a("a975"),a("735e"),a("c1ac"),a("d139"),a("3a7b"),a("d5d6"),a("82f8"),a("e91f"),a("60bd"),a("5f96"),a("3280"),a("3fcc"),a("ca91"),a("25a1"),a("cd26"),a("3c5d"),a("2954"),a("649e"),a("219c"),a("170b"),a("b39a"),a("72f7"),a("4d90"),a("25f0"),a("99af"),a("bf19"),a("ac1f"),a("5319"),a("159b"),a("00b4"),a("d81d");function i(t,e){e&&(t+=1462);var a=Date.parse(t);return(a-new Date(Date.UTC(1899,11,30)))/864e5}function o(t,e){for(var a={},o={s:{c:1e7,r:1e7},e:{c:0,r:0}},n=0;n!=t.length;++n)for(var r=0;r!=t[n].length;++r){o.s.r>n&&(o.s.r=n),o.s.c>r&&(o.s.c=r),o.e.r<n&&(o.e.r=n),o.e.c<r&&(o.e.c=r);var s={v:t[n][r]};if(null!=s.v){var l=XLSX.utils.encode_cell({c:r,r:n});"number"===typeof s.v?s.t="n":"boolean"===typeof s.v?s.t="b":s.v instanceof Date?(s.t="n",s.z=XLSX.SSF._table[14],s.v=i(s.v)):s.t="s",a[l]=s}}return o.s.c<1e7&&(a["!ref"]=XLSX.utils.encode_range(o)),a}function n(){if(!(this instanceof n))return new n;this.SheetNames=[],this.Sheets={}}function r(t){for(var e=new ArrayBuffer(t.length),a=new Uint8Array(e),i=0;i!=t.length;++i)a[i]=255&t.charCodeAt(i);return e}function s(t){if(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),o=e.getDate().toString().padStart(2,"0"),n=e.getHours().toString().padStart(2,"0"),r=e.getMinutes().toString().padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(o," ").concat(n,":").concat(r)}}function l(t,e,a){var i=[],l=/^\d{4}-(?:0[1-9]|1[0-2])-(?:0[1-9]|[1-2]\d|3[0-1])T(?:[0-1]\d|2[0-3]):[0-5]\d:[0-5]\d(?:\.\d+|)(?:Z|(?:\+|\-)(?:\d{2}):?(?:\d{2}))$/;e.forEach((function(t,e){for(var a=[],o=0;o<t.length;o++){var n=t[o];l.test(n)&&(n=s(n)),a.push(n)}i.push(a)}));var c=i;c.unshift(t);for(var d="SheetJS",m=new n,u=o(c),f=c.map((function(t){return t.map((function(t){return null==t?{wch:10}:t.toString().charCodeAt(0)>255?{wch:2*t.toString().length}:{wch:t.toString().length}}))})),p=f[0],h=1;h<f.length;h++)for(var b=0;b<f[h].length;b++)p[b]["wch"]<f[h][b]["wch"]&&(p[b]["wch"]=f[h][b]["wch"]);u["!cols"]=p,m.SheetNames.push(d),m.Sheets[d]=u;var g=XLSX.write(m,{bookType:"xlsx",bookSST:!1,type:"binary"}),w=a||"列表";saveAs(new Blob([r(g)],{type:"application/octet-stream"}),w+".xlsx")}a("0fd4"),a("f71d"),a("1447")},"5cc6":function(t,e,a){var i=a("74e8");i("Uint8",(function(t){return function(e,a,i){return t(this,e,a,i)}}))},"6bed":function(t,e,a){},8743:function(t,e,a){},af52:function(t,e,a){"use strict";a("ff82")},bab5:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}],ref:"index",staticClass:"index"},[a("listheader",{attrs:{tableForm:t.tableForm,isEdit:t.isEdit},on:{btnAdd:function(e){return t.showForm(0)},btnSearch:t.search,advancedSearch:t.advancedSearch,btnHelp:t.btnHelp,openEdit:function(e){return t.openEdit()},btnExport:t.btnExport}}),a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{attrs:{span:4}},[a("div",{staticStyle:{"font-size":"14px",color:"#606266"}},[a("div",{staticClass:"groupTitle"},[a("span",[t._v("自定义列设置")])]),a("el-tree",{staticStyle:{"font-size":"14px",height:"80vh",overflow:"auto",width:"100%"},attrs:{data:t.groupData,"node-key":"id","default-expand-all":"","expand-on-click-node":!1},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.node,o=e.data;return a("span",{staticClass:"custom-tree-node"},[a("span",{on:{click:function(){return t.handleNodeClick(o)}}},[t._v(t._s(i.label)+" ")])])}}])})],1)]),a("el-col",{attrs:{span:t.showHelp?16:20}},[a("el-table",{ref:"tableData",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},height:t.tableMaxHeight}},[a("el-table-column",{attrs:{align:"center",type:"",label:"ID","min-width":"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.$index+1)+" ")]}}])}),t._l(t.tableForm.item,(function(e,i){return[!e.displaymark?t._e():a("el-table-column",{key:i,attrs:{prop:e.itemcode,columnKey:e.itemcode,label:e.itemname,align:e.aligntype?e.aligntype:"center","min-width":e.minwidth,"show-overflow-tooltip":!!e.overflow,sortable:!!e.sortable},scopedSlots:t._u([{key:"default",fn:function(i){return["refno"==e.itemcode?a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.showForm(i.row.id)}}},[t._v(t._s(i.row.refno?i.row.refno:"单据编号"))]):"createdate"==e.itemcode||"modifydate"==e.itemcode||"assessdate"==e.itemcode?a("span",[t._v(t._s(t._f("dateFormat")(i.row[e.itemcode])))]):"itemlabel"==e.itemcode?a("div",[t.isEdit?a("el-input",{attrs:{placeholder:"请输入",size:"small"},model:{value:i.row[e.itemcode],callback:function(a){t.$set(i.row,e.itemcode,a)},expression:"scope.row[i.itemcode]"}}):a("span",[t._v(t._s(i.row[e.itemcode]))])],1):a("span",[t._v(t._s(i.row[e.itemcode]))])]}}],null,!0)})]}))],2),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1),a("el-col",{attrs:{span:t.showHelp?4:0}},[a("helpmodel",{ref:"helpmodel",attrs:{code:"SYSM07B6"}})],1)],1)],1)],1),t.gropuFormVisible?a("el-dialog",{attrs:{title:"自定义列分组","append-to-body":!0,visible:t.gropuFormVisible,width:"800px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.gropuFormVisible=e}}},[a("group",{ref:"group",attrs:{idx:t.idx,pid:t.pid},on:{bindData:t.BindTreeData,closeDialog:function(e){t.gropuFormVisible=!1}}})],1):t._e()],1)},o=[],n=a("2909"),r=(a("d3b7"),a("159b"),a("e9c4"),a("d81d"),a("99af"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{class:t.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[a("div",[a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnSearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnSearch},slot:"append"},[t._v("搜索 ")])],1),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-edit",plain:"",size:"mini"},on:{click:function(e){return t.$emit("openEdit")}}},[t._v(" "+t._s(t.isEdit?"保存编辑":"开启编辑")+" ")])],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){return t.openDialog()}}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:t.btnExport}}),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{icon:t.iShow?"el-icon-arrow-up":"el-icon-arrow-down",size:"mini",title:"高级搜索"},on:{click:function(e){t.iShow=!t.iShow}}})],1)]),a("el-collapse-transition",[a("div",{directives:[{name:"show",rawName:"v-show",value:t.iShow,expression:"iShow"}],staticClass:"searchDetail"},[a("el-form",{ref:"formdata",attrs:{model:t.formdata,"label-width":"100px","auto-complete":"on"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"表名称"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入表名称",size:"small"},model:{value:t.formdata.tablename,callback:function(e){t.$set(t.formdata,"tablename",e)},expression:"formdata.tablename"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"列名"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入列名",size:"small"},model:{value:t.formdata.itemname,callback:function(e){t.$set(t.formdata,"itemname",e)},expression:"formdata.itemname"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"自定义列名"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入自定义列名",size:"small"},model:{value:t.formdata.itemlabel,callback:function(e){t.$set(t.formdata,"itemlabel",e)},expression:"formdata.itemlabel"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px","margin-top":"5px"},attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.advancedSearch()}}},[t._v(" 搜索 ")])],1)],1)],1)],1)]),t.setColumsVisible?a("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.setColumsVisible=e}}},[a("Setcolums",{ref:"setcolums",attrs:{code:t.code,tableForm:t.tableForm},on:{bindData:t.bindData,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)}),s=[],l=a("8daf"),c={name:"Listheader",components:{Setcolums:l["a"]},props:["tableForm","isEdit"],data:function(){return{strfilter:"",iShow:!1,formdata:{},code:"D01M01B1List",setColumsVisible:!1}},methods:{openDialog:function(){this.setColumsVisible=!0},submitUpdate:function(){this.$refs.setcolums.submitUpdate()},advancedSearch:function(){this.iShow=!1,this.$emit("advancedSearch",this.formdata)},btnAdd:function(){this.$emit("btnAdd")},btnSearch:function(){this.$emit("btnSearch",this.strfilter)},btnExport:function(){this.$emit("btnExport")},bindData:function(){this.$emit("bindData")}}},d=c,m=(a("af52"),a("2877")),u=Object(m["a"])(d,r,s,!1,null,"1f427d1b",null),f=u.exports,p=a("b775"),h=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("el-button",{attrs:{type:"primary",size:"small"},nativeOn:{click:function(e){return t.submitForm("formdata")}}},[t._v(" 保 存")]),a("el-button",{attrs:{disabled:!t.idx,size:"small"},nativeOn:{click:function(e){return t.deleteForm(t.idx)}}},[t._v(" 删 除")]),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.closeForm(e)}}},[t._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth,"auto-complete":"on",rules:t.formRules}},[a("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"自定义列分组"}},[a("el-cascader",{ref:"cascader",staticStyle:{width:"100%"},attrs:{options:t.groupData,props:t.defaultProps,"show-all-levels":!1,placeholder:"请选择分组名称",size:"small",clearable:""},on:{change:t.handleChange},model:{value:t.formdata.gengroupid,callback:function(e){t.$set(t.formdata,"gengroupid",e)},expression:"formdata.gengroupid"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("modulecode")}}},[a("el-form-item",{attrs:{label:"模块编码",prop:"modulecode"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入模块编码",clearable:"",size:"small"},model:{value:t.formdata.modulecode,callback:function(e){t.$set(t.formdata,"modulecode",e)},expression:"formdata.modulecode"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("tablename")}}},[a("el-form-item",{attrs:{label:"表中文名",prop:"tablename"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入表中文名",clearable:"",size:"small"},model:{value:t.formdata.tablename,callback:function(e){t.$set(t.formdata,"tablename",e)},expression:"formdata.tablename"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"表名"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入表名",clearable:"",size:"small"},model:{value:t.formdata.tablecode,callback:function(e){t.$set(t.formdata,"tablecode",e)},expression:"formdata.tablecode"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"字段"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入字段",clearable:"",size:"small"},model:{value:t.formdata.columncode,callback:function(e){t.$set(t.formdata,"columncode",e)},expression:"formdata.columncode"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"表.字段"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入表.字段",clearable:"",size:"small"},model:{value:t.formdata.itemcode,callback:function(e){t.$set(t.formdata,"itemcode",e)},expression:"formdata.itemcode"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"自定义标签"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入自定义标签",clearable:"",size:"small"},model:{value:t.formdata.itemlabel,callback:function(e){t.$set(t.formdata,"itemlabel",e)},expression:"formdata.itemlabel"}})],1)],1)],1)],1),a("el-divider")],1),a("el-form",{attrs:{"label-width":t.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入备注",clearable:"",size:"small"},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.modifydate)))])])],1)],1)],1)],1)])])},b=[],g=a("c7eb"),w=a("1da1");a("4d90"),a("25f0"),a("b64b");const v={add(t){return new Promise((e,a)=>{var i=JSON.stringify(t);p["a"].post("/system/SYSM07B6/create",i).then(t=>{console.log(i,t),200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},update(t){return new Promise((e,a)=>{var i=JSON.stringify(t);p["a"].post("/system/SYSM07B6/update",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},delete(t){return new Promise((e,a)=>{p["a"].get("/system/SYSM07B6/delete?key="+t).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})}};var y=v,S=a("1893"),x=(a("b0b8"),{name:"Formedit",components:{selectTenant:S["a"]},filters:{dateFormat:function(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),o=e.getDate().toString().padStart(2,"0");return"".concat(a,"/").concat(i,"/").concat(o)}},props:["idx"],data:function(){return{title:"自定义列",formdata:{gengroupid:"",lister:JSON.parse(window.localStorage.getItem("AdminGetInfo")).realname,createby:JSON.parse(window.localStorage.getItem("AdminGetInfo")).realname,tenantid:"default",columncode:"",itemcode:"",itemlabel:"",modulecode:"",remark:"",revision:0,rownum:0,tablecode:"",tablename:""},formRules:{modulecode:[{required:!0,trigger:"blur",message:"模块编码为必填项"}],tablecode:[{required:!0,trigger:"blur",message:"表名为必填项"}]},formLabelWidth:"100px",multi:0,selVisible:!1,groupData:[],defaultProps:{children:"children",label:"label",value:"id"}}},computed:{formcontainHeight:function(){return window.innerHeight-50-23-50+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindData(),this.BindTreeData()},methods:{bindData:function(){var t=this;return Object(w["a"])(Object(g["a"])().mark((function e(){return Object(g["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.listLoading=!0,0==t.idx){e.next=4;break}return e.next=4,p["a"].get("/system/SYSM07B6/getEntity?key=".concat(t.idx)).then((function(e){200==e.data.code?t.formdata=e.data.data:t.$alert(e.data.code+" 数据错误，返回主表","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}}),t.listLoading=!1})).catch((function(e){t.listLoading=!1}));case 4:case"end":return e.stop()}}),e)})))()},BindTreeData:function(){var t=this;p["a"].get("/system/SYSM07B8/getListByModuleCode?Code=SYSM07B6").then((function(e){if(200==e.data.code){var a=e.data.data.map((function(t){return{id:t.id,pid:t.parentid,label:t.groupname}})),i=[{id:"0",pid:"root",label:"自定义列"}],o=[].concat(Object(n["a"])(a),i);t.groupData=t.transData(o,"id","pid","children")}}))},handleChange:function(t){console.log(t),t.length>0?this.formdata.gengroupid=t[t.length-1]:this.formdata.gengroupid="0"},submitForm:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;0==this.idx?y.add(this.formdata).then((function(e){t.$message.success("保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.$emit("closeForm")})).catch((function(e){t.$message.warning("保存失败")})):y.update(this.formdata).then((function(){t.$message.success("保存成功"),t.$emit("bindData"),t.$emit("closeForm")})).catch((function(e){t.$message.warning("保存失败")}))},closeForm:function(){this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){console.log("执行删除"),y.delete(t).then((function(){e.$message.success("删除成功"),e.$emit("compForm")})).catch((function(){e.$message({showClose:!0,message:"删除失败",type:"warning"})}))})).catch((function(){}))},selectTenant:function(t){var e=this.$refs.selectTenant.selrows;console.log(e),this.formdata.tenantid=e.tenantid,this.formdata.tenantcode=e.tenantcode,this.formdata.tenantname=e.tenantname,this.formdata.company=e.company,this.selVisible=!1,this.$refs.formdata.clearValidate("tenantid")},transData:function(t,e,a,i){for(var o=[],n={},r=e,s=a,l=i,c=0,d=0,m=t.length;c<m;c++)n[t[c][r]]=t[c];for(;d<m;d++){var u=t[d],f=n[u[s]];f?(!f[l]&&(f[l]=[]),f[l].push(u)):o.push(u)}return o},selectSupplier:function(){var t=this.$refs.selectSupplier.selrows;console.log(t),this.formdata.GroupName=t.GroupName,this.formdata.Custid=t.id,this.selVisible=!1},cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}}),_=x,k=(a("ea5b"),Object(m["a"])(_,h,b,!1,null,"62168bca",null)),C=k.exports,D=a("333d"),$=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticStyle:{width:"100%"}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth,rules:t.formRules}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[a("el-form-item",{attrs:{label:"分组名称",prop:"groupname"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入分组名称",size:"small"},on:{input:t.writeCode},model:{value:t.formdata.groupname,callback:function(e){t.$set(t.formdata,"groupname",e)},expression:"formdata.groupname"}})],1)],1)]),a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("groupcode")}}},[a("el-form-item",{attrs:{label:"分组编码",prop:"groupcode"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入分组编码",size:"small"},model:{value:t.formdata.groupcode,callback:function(e){t.$set(t.formdata,"groupcode",e)},expression:"formdata.groupcode"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"排序"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{"controls-position":"right",min:0,size:"small"},model:{value:t.formdata.rownum,callback:function(e){t.$set(t.formdata,"rownum",e)},expression:"formdata.rownum"}})],1)],1)],1)],1)],1),a("el-form",{staticClass:"footFormContent",attrs:{"label-width":t.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormats")(t.formdata.modifydate)))])])],1)],1)],1)],1)]),a("div",{staticClass:"button-container flex j-end"},[a("el-button",{attrs:{type:"primary",size:"small"},nativeOn:{click:function(e){return t.submitForm("formdata")}}},[t._v(" 保 存")]),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.$emit("closeDialog")}}},[t._v(" 关 闭")])],1)])},P=[],F=a("b0b8"),B={name:"Formedit",props:["idx","pid"],data:function(){return{formdata:{lister:JSON.parse(window.localStorage.getItem("AdminGetInfo")).realname,createby:JSON.parse(window.localStorage.getItem("AdminGetInfo")).realname,parentid:"",modulecode:"SYSM07B6",groupcode:"",groupname:"",rownum:0,remark:"",enabledmark:1},formRules:{modulecode:[{required:!0,trigger:"blur",message:"功能编码为必填项"}],groupcode:[{required:!0,trigger:"blur",message:"分组编码为必填项"}],groupname:[{required:!0,trigger:"blur",message:"分组名称为必填项"}]},formLabelWidth:"100px",dialogVisible:!1,delDialogVisible:!1,option:""}},computed:{formcontainHeight:function(){return"300px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindData()},methods:{bindData:function(){var t=this;0!=this.idx?p["a"].get("/system/SYSM07B8/getEntity?key=".concat(this.idx)).then((function(e){console.log("==================",e),200==e.data.code&&(t.formdata=e.data.data)})):this.formdata.parentid=this.idx},submitForm:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;this.pid&&(this.formdata.parentid=this.pid),0==this.idx?p["a"].post("/system/SYSM07B8/create",JSON.stringify(this.formdata)).then((function(e){t.$emit("bindData"),t.$emit("closeDialog")})):p["a"].post("/system/SYSM07B8/update",JSON.stringify(this.formdata)).then((function(e){t.$emit("bindData"),t.$emit("closeDialog")}))},closeForm:function(){this.$emit("closeForm")},check:function(){console.log("check")},cleValidate:function(t){this.$refs.formdata.clearValidate(t)},writeCode:function(t){F.setOptions({checkPolyphone:!1,charCase:1}),this.formdata.groupcode=F.getFullChars(t)}}},L=B,O=(a("decb"),Object(m["a"])(L,$,P,!1,null,"5c6cf68c",null)),z=O.exports,N=a("0521"),E={formcode:"SYSM07B6Th",item:[{itemcode:"tablename",itemname:" 表名称",minwidth:"120",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"CiTableCustom.tablename"},{itemcode:"itemname",itemname:"列名",minwidth:"100",sortable:0,displaymark:1,overflow:1,datasheet:"CiTableCustom.itemname"},{itemcode:"itemlabel",itemname:"自定义列名",minwidth:"100",sortable:0,displaymark:1,overflow:1,datasheet:"CiTableCustom.itemlabel"},{itemcode:"createdate",itemname:"日期",minwidth:"100",sortable:0,displaymark:1,overflow:1,datasheet:"CiTableCustom.createdate"}]},R=a("b893"),T=a("48da"),j={components:{listheader:f,Pagination:D["a"],formedit:C,group:z,helpmodel:N["a"]},data:function(){return{title:"自定义列",lst:[],copyList:[],searchstr:" ",formvisible:!1,idx:0,total:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:0},showHelp:!1,tableForm:E,pid:"root",treeTitle:"自定义列",gropuFormVisible:!1,treeVisble:!0,groupData:[],defaultProps:{children:"children",label:"label",value:"id"},isEdit:!1}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},created:function(){this.bindData(),this.BindTreeData()},methods:{coypData:function(){var t=this;this.copyList=[],this.lst.forEach((function(e){var a=Object.assign({},e);t.copyList.push(a)}))},bindData:function(){var t=this;this.listLoading=!0,p["a"].post("/system/SYSM07B6/getPageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total,t.coypData()),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},BindTreeData:function(){var t=this;p["a"].get("/system/SYSM07B8/getDefListByModuleCode?Code=SYSM07B6").then((function(e){if(200==e.data.code){var a=e.data.data.map((function(t){return{id:t.id,pid:t.parentid,label:t.groupname}})),i=[{id:"0",pid:"root",label:t.treeTitle}],o=[].concat(Object(n["a"])(a),i);t.groupData=Object(R["i"])(o,"id","pid","children")}}))},btnHelp:function(){this.showHelp=!this.showHelp,this.showHelp&&this.$refs.helpmodel.bindData()},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){""!=t?this.queryParams.SearchPojo={tablename:t,itemlabel:t,itemname:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},advancedSearch:function(t){this.queryParams.SearchPojo=t,this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},showForm:function(t,e){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},openEdit:function(){var t=this;this.$nextTick((function(){t.$refs.tableData.doLayout()})),this.isEdit?this.saveData():this.isEdit=!0},saveData:function(){var t=this,e=JSON.stringify(this.lst),a=JSON.stringify(this.copyList);a===e?this.isEdit=!1:this.$confirm("检测数据有改动, 是否保存?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){p["a"].post("/system/SYSM07B6/updateList",JSON.stringify(t.lst)).then((function(e){200==e.data.code?(t.isEdit=!1,t.$message.success("保存成功")):t.$message.success("保存失败")}))})).catch((function(){}))},changeIdx:function(t){this.idx=t},searchByTree:function(t){var e=this;p["a"].get("/system//SYSM07B6/getListByGroupid?key="+t).then((function(t){200==t.data.code?(e.lst=t.data.data,e.coypData()):e.$message.warning(t.data.msg||"查询失败")}))},handleNodeClick:function(t){0!=t.id&&(this.isEdit?this.saveData():this.searchByTree(t.id))},btnExport:function(){var t=this;Promise.resolve().then(function(){for(var e=[],a=[],i=0;i<t.tableForm.item.length;i++){var o=t.tableForm.item[i];o.displaymark&&(e.push(o.itemname),a.push(o.itemcode))}var n=t.lst,r=t.formatJson(a,n);Object(T["a"])(e,r,"自定义列设置")}.bind(null,a)).catch(a.oe)},formatJson:function(t,e){return e.map((function(e){return t.map((function(t){return e[t]}))}))}}},q=j,M=(a("c6d9"),Object(m["a"])(q,i,o,!1,null,"2d578450",null));e["default"]=M.exports},bf19:function(t,e,a){"use strict";var i=a("23e7");i({target:"URL",proto:!0,enumerable:!0},{toJSON:function(){return URL.prototype.toString.call(this)}})},c19f:function(t,e,a){"use strict";var i=a("23e7"),o=a("da84"),n=a("621a"),r=a("2626"),s="ArrayBuffer",l=n[s],c=o[s];i({global:!0,forced:c!==l},{ArrayBuffer:l}),r(s)},c6d9:function(t,e,a){"use strict";a("8743")},d183:function(t,e,a){},decb:function(t,e,a){"use strict";a("d183")},ea5b:function(t,e,a){"use strict";a("04aa")},f71d:function(t,e,a){a("ac1f"),a("466d"),a("d3b7"),a("25f0"),a("b0c0"),a("a15b"),a("fb6a"),
/*! @source http://purl.eligrey.com/github/Blob.js/blob/master/Blob.js */
function(t){"use strict";if(t.URL=t.URL||t.webkitURL,t.Blob&&t.URL)try{return void new Blob}catch(a){}var e=t.BlobBuilder||t.WebKitBlobBuilder||t.MozBlobBuilder||function(t){var e=function(t){return Object.prototype.toString.call(t).match(/^\[object\s(.*)\]$/)[1]},a=function(){this.data=[]},i=function(t,e,a){this.data=t,this.size=t.length,this.type=e,this.encoding=a},o=a.prototype,n=i.prototype,r=t.FileReaderSync,s=function(t){this.code=this[this.name=t]},l="NOT_FOUND_ERR SECURITY_ERR ABORT_ERR NOT_READABLE_ERR ENCODING_ERR NO_MODIFICATION_ALLOWED_ERR INVALID_STATE_ERR SYNTAX_ERR".split(" "),c=l.length,d=t.URL||t.webkitURL||t,m=d.createObjectURL,u=d.revokeObjectURL,f=d,p=t.btoa,h=t.atob,b=t.ArrayBuffer,g=t.Uint8Array;i.fake=n.fake=!0;while(c--)s.prototype[l[c]]=c+1;return d.createObjectURL||(f=t.URL={}),f.createObjectURL=function(t){var e,a=t.type;return null===a&&(a="application/octet-stream"),t instanceof i?(e="data:"+a,"base64"===t.encoding?e+";base64,"+t.data:"URI"===t.encoding?e+","+decodeURIComponent(t.data):p?e+";base64,"+p(t.data):e+","+encodeURIComponent(t.data)):m?m.call(d,t):void 0},f.revokeObjectURL=function(t){"data:"!==t.substring(0,5)&&u&&u.call(d,t)},o.append=function(t){var a=this.data;if(g&&(t instanceof b||t instanceof g)){for(var o="",n=new g(t),l=0,c=n.length;l<c;l++)o+=String.fromCharCode(n[l]);a.push(o)}else if("Blob"===e(t)||"File"===e(t)){if(!r)throw new s("NOT_READABLE_ERR");var d=new r;a.push(d.readAsBinaryString(t))}else t instanceof i?"base64"===t.encoding&&h?a.push(h(t.data)):"URI"===t.encoding?a.push(decodeURIComponent(t.data)):"raw"===t.encoding&&a.push(t.data):("string"!==typeof t&&(t+=""),a.push(unescape(encodeURIComponent(t))))},o.getBlob=function(t){return arguments.length||(t=null),new i(this.data.join(""),t,"raw")},o.toString=function(){return"[object BlobBuilder]"},n.slice=function(t,e,a){var o=arguments.length;return o<3&&(a=null),new i(this.data.slice(t,o>1?e:this.data.length),a,this.encoding)},n.toString=function(){return"[object Blob]"},n.close=function(){this.size=this.data.length=0},a}(t);t.Blob=function(t,a){var i=a&&a.type||"",o=new e;if(t)for(var n=0,r=t.length;n<r;n++)o.append(t[n]);return o.getBlob(i)}}("undefined"!==typeof self&&self||"undefined"!==typeof window&&window||this.content||this)},ff82:function(t,e,a){}}]);