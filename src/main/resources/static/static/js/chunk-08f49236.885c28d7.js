(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-08f49236"],{3044:function(t,a,e){"use strict";e("bb7b")},bb7b:function(t,a,e){},ef11:function(t,a,e){"use strict";e.r(a);var r=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"PageSty"},[t.showPassWord?e("div",{staticClass:"passForm"},[e("h4",{staticStyle:{"text-align":"center"}},[t._v("请输入密码")]),e("el-form",{ref:"formdata",attrs:{model:t.formdata,"auto-complete":"on",rules:t.formRule},nativeOn:{submit:function(t){t.preventDefault()}}},[e("el-form-item",{attrs:{label:"密码",prop:"password"}},[e("el-input",{ref:"password",staticStyle:{width:"100%"},attrs:{placeholder:"请输入密码",clearable:""},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&t._k(a.keyCode,"enter",13,a.key,"Enter")?null:t.submitQuickCode()}},model:{value:t.formdata.password,callback:function(a){t.$set(t.formdata,"password",a)},expression:"formdata.password"}})],1),e("el-form-item",{staticStyle:{"text-align":"center"}},[e("el-button",{attrs:{type:"primary"},on:{click:function(a){return t.submitBtn()}}},[t._v("确定")])],1)],1)],1):e("div",[t.formdata.url?[e("iframe",{staticStyle:{"min-height":"calc(100vh - 88px)",width:"100%"},attrs:{id:"iframeId",src:t.formdata.url,frameborder:"0",scrolling:"auto"}})]:e("div",[e("el-empty",{staticClass:"noData",attrs:{description:"暂无大屏信息"}})],1)],2)])},s=[],o=(e("e9c4"),{name:"BiScreen",data:function(){return{password:"",showPassWord:!1,lstrow:{},formdata:{password:"",url:""},formRule:{password:[{required:!0,trigger:"blur",message:"密码为必填项"}]}}},mounted:function(){this.bindData()},methods:{bindData:function(){var t=this;console.log(this.$route);var a={PageNum:1,PageSize:1,OrderType:1,SearchType:0,SearchPojo:{extcode:this.$route.params.key?this.$route.params.key:""}},e="/utils/D96M21B1/getPageList";this.$request.post(e,JSON.stringify(a)).then((function(a){200==a.data.code?a.data.data.list.length&&(t.lstrow=a.data.data.list[0],t.password=a.data.data.list[0].permissioncode,t.password?(t.showPassWord=!0,t.formRule.password.push({trigger:"blur",validator:t.passvalidator})):t.formdata.url=a.data.data.list[0].exturl):(t.$message.warning(a.data.msg||"获取财务大屏失败"),t.formdata.url="",t.password="")})).catch((function(a){t.$message.error(a.response.data.message||"请求错误"),t.formdata.url="",t.password=""}))},submitBtn:function(){var t=this;this.$refs["formdata"].validate((function(a){if(!a)return console.log("error submit!!"),!1;t.formdata.url=t.lstrow.exturl,t.showPassWord=!1}))},passvalidator:function(t,a,e){console.log(t,a,e),a!=this.password?e(new Error("权限密码不正确")):e()}}}),i=o,n=(e("3044"),e("2877")),d=Object(n["a"])(i,r,s,!1,null,"1e3f6e46",null);a["default"]=d.exports}}]);