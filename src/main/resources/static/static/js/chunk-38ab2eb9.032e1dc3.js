(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-38ab2eb9"],{1491:function(t,e,a){},"435a":function(t,e,a){"use strict";a("1491")},"4be7":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"warp"},[a("div",{staticClass:"workbench"},t._l(t.linkList.data,(function(e,i){return a("el-card",{key:i,staticClass:"box-card cardItem"},[a("div",{staticClass:"linkItem",attrs:{title:"点击"+e.name},on:{click:function(a){return t.ClickTo(e)}}},[a("div",{staticClass:"linkName",style:{background:e.color}},[e.icon.includes("el-icon")?a("i",{class:e.icon,style:{"font-size":"18px"}}):a("svg-icon",{style:{"font-size":"18px"},attrs:{"icon-class":e.icon}}),a("span",{staticStyle:{"margin-left":"10px"}},[t._v(t._s(e.name))])],1),a("div",{staticClass:"linkcontent"},[a("div",{staticClass:"linkcontent-body",domProps:{innerHTML:t._s(e.content)}})])])])})),1),t.openDateVisible?a("el-dialog",{staticClass:"openDialog",attrs:{title:"初始化验证",width:"500px","append-to-body":!0,"close-on-click-modal":!1,visible:t.openDateVisible},on:{"update:visible":function(e){t.openDateVisible=e}}},[a("div",{staticStyle:{padding:"0 10px"}},[a("el-form",{ref:"formdata",attrs:{model:t.formdata,rules:t.formdataRule,"label-width":"20px","label-position":"top"}},[a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"时间范围"}},[a("el-date-picker",{staticStyle:{width:"100%","margin-right":"10px"},attrs:{"unlink-panels":"","value-format":"yyyy-MM-dd HH:mm:ss","default-time":["00:00:00","23:59:59"],type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":t.pickerOptions},model:{value:t.dateRange,callback:function(e){t.dateRange=e},expression:"dateRange"}})],1),a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{prop:"username",label:"邮箱或手机"}},[a("el-input",{attrs:{placeholder:"请输入邮箱或手机号码","auto-complete":"off"},model:{value:t.formdata.username,callback:function(e){t.$set(t.formdata,"username",e)},expression:"formdata.username"}})],1),a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{prop:"code",label:"验证码"}},[a("div",{staticStyle:{display:"flex","align-items":"center"}},[a("el-input",{attrs:{placeholder:"请输入验证码",name:"code","auto-complete":"off","validate-event":!1},model:{value:t.formdata.code,callback:function(e){t.$set(t.formdata,"code",e)},expression:"formdata.code"}},[a("el-button",{attrs:{slot:"append",disabled:t.flag},on:{click:t.getEmailCode},slot:"append"},[t._v(t._s(t.btnTitle)+" ")])],1)],1)])],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.openDateVisible=!1}}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.submitOpen()}}},[t._v("确 定")])],1)]):t._e()],1)},n=[],o=(a("ac1f"),a("00b4"),a("d3b7"),a("159b"),a("e9c4"),a("b775")),s={formcode:"SYSM04B3",data:[{key:"D01",name:"初始化销售",icon:"D05M07B1",color:"",url:"/system/SYSM04B3/initSale",content:"<div>D01M03 销售订单</div> <div>D01M06 发出商品 </div> \n      <div>D01M05 销售开票</div> <div>D01M08 预收、收款 </div> <div>D01M09 销售扣款</div> \n      <div>D01M12 销售账单/结账</div> "},{key:"D03",name:"初始化采购",icon:"D03M02B1",color:"",url:"/system/SYSM04B3/initBuy",content:"<div>D03M01 采购计划</div><div>D03M02 采购合同</div> \n      <div>D03M03 采购收货</div><div>D03M04 采购扣款</div><div>D03M05 采购开票</div>\n      <div>D03M06 预付、付款</div><div>D03M08-10 采购账单/结账</div>"},{key:"D04",name:"初始化仓库",icon:"D03M03B1",color:"",url:"/system/SYSM04B3/initStore",content:"<div>D04M01 出库单</div> <div>D04M08 领退料</div>"},{key:"D05",name:"初始化生产",icon:"chart2",color:"",url:"/system/SYSM04B3/initManu",content:"<div>D05M01 生产加工单</div><div>D05M02 委外加工单</div> \n      <div>D05M03 生产验收单</div><div>D05M04 生产计划</div><div>D05M05 Wip</div>\n      <div>D05M06 生产报表</div><div>D05M07 生产过数</div>"}]},r=a("b893"),l={data:function(){var t=this,e=function(t,e,a){if(e){var i=/^([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})$/,n=/^(13[0-9]|14[5|7]|15[0|1|2|3|4|5|6|7|8|9]|18[0|1|2|3|5|6|7|8|9])\d{8}$/;if(!i.test(e)&&!n.test(e))return a(new Error("请输入正确的手机或邮箱格式"));a()}},a=function(e,a,i){a&&(console.log(a,t.formdata.username),Object(o["a"])({url:"/auth/captcha/checkCaptcha",method:"get",params:{code:a.toUpperCase(),username:t.formdata.username}}).then((function(t){200!=t.data.code?i(new Error(t.data.msg)):i()})))};return{linkList:s,selRows:{},openDateVisible:!1,formdata:{username:"",code:""},dateRange:["",""],pickerOptions:Object(r["e"])(),count:5,btnTitle:"获取验证码",flag:!1,formdataRule:{username:[{required:!0,trigger:"blur",message:"邮箱或手机号码为必填项"},{trigger:"blur",validator:e}],code:[{required:!0,trigger:"blur",message:"验证码为必填项"},{required:!0,validator:a}]}}},created:function(){},mounted:function(){this.bindData()},methods:{bindData:function(){var t=["#2a7aff","#7ed321","#f31876","#f65a5a","#ff9800"];this.linkList.data.forEach((function(e,a){console.log(e,"===="),e.color=e.color?e.color:t[a%5]})),console.log(this.linkList)},ClickTo:function(t){var e=this;console.log(t),this.selRows=t,this.$confirm("此操作不可逆, 是否确认初始化?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.openDateVisible=!0}))},getEmailCode:function(){var t=this;if(""!=this.formdata.username){var e=/^([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})$/,a=/^(13[0-9]|14[5|7]|15[0|1|2|3|4|5|6|7|8|9]|18[0|1|2|3|5|6|7|8|9])\d{8}$/;e.test(this.formdata.username)?o["a"].get("/auth/captcha/emailCaptcha?email="+this.formdata.username).then((function(e){if(200==e.data.code){t.$message.success("验证码已发送到邮箱上，请查看");var a=setInterval((function(){t.count<1?(t.flag=!1,t.btnTitle="获取验证码",t.count=5,clearInterval(a)):(t.flag=!0,t.btnTitle=t.count--+"s后重新获取")}),1e3)}})):a.test(this.formdata.username)&&o["a"].get("/auth/captcha/phoneCaptcha?phone="+this.formdata.username).then((function(e){if(200==e.data.code){t.$message.success("验证码已发送到手机短信上，请查看");var a=setInterval((function(){t.count<1?(t.flag=!1,t.btnTitle="获取验证码",t.count=5,clearInterval(a)):(t.flag=!0,t.btnTitle=t.count--+"s后重新获取")}),1e3)}}))}else this.$message.warning("账号不能为空")},submitOpen:function(){var t=this;""!=this.dateRange[0]&&""!=this.dateRange[1]?this.$refs["formdata"].validate((function(e){if(!e)return console.log("error submit!!"),!1;t.submitReq()})):this.$message.warning("请选择时间范围")},submitReq:function(){var t=this,e={DateRange:{StartDate:this.dateRange[0],EndDate:this.dateRange[1]}};o["a"].post(this.selRows.url,JSON.stringify(e)).then((function(e){200==e.data.code?(t.$message.success(e.data.msg||"初始化成功"),t.openDateVisible=!1,t.formdata={username:"",code:""},t.dateRange=["",""],t.selRows={}):t.$message.warning(e.data.msg||"初始化失败")})).catch((function(e){t.$message.error(e||"请求错误")}))}}},c=l,d=(a("435a"),a("2877")),u=Object(d["a"])(c,i,n,!1,null,"2e951408",null);e["default"]=u.exports}}]);