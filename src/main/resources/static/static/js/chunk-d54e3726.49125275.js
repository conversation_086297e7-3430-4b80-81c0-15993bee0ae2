(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-d54e3726"],{"0f23":function(t,e,a){},"27ae":function(t,e,a){(function(a){var r,i;(function(e,a){t.exports=a(e)})("undefined"!==typeof self?self:"undefined"!==typeof window?window:"undefined"!==typeof a?a:this,(function(a){"use strict";a=a||{};var o,n=a.Base64,s="2.6.4",l="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",c=function(t){for(var e={},a=0,r=t.length;a<r;a++)e[t.charAt(a)]=a;return e}(l),d=String.fromCharCode,m=function(t){if(t.length<2){var e=t.charCodeAt(0);return e<128?t:e<2048?d(192|e>>>6)+d(128|63&e):d(224|e>>>12&15)+d(128|e>>>6&63)+d(128|63&e)}e=65536+1024*(t.charCodeAt(0)-55296)+(t.charCodeAt(1)-56320);return d(240|e>>>18&7)+d(128|e>>>12&63)+d(128|e>>>6&63)+d(128|63&e)},u=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,p=function(t){return t.replace(u,m)},f=function(t){var e=[0,2,1][t.length%3],a=t.charCodeAt(0)<<16|(t.length>1?t.charCodeAt(1):0)<<8|(t.length>2?t.charCodeAt(2):0),r=[l.charAt(a>>>18),l.charAt(a>>>12&63),e>=2?"=":l.charAt(a>>>6&63),e>=1?"=":l.charAt(63&a)];return r.join("")},h=a.btoa&&"function"==typeof a.btoa?function(t){return a.btoa(t)}:function(t){if(t.match(/[^\x00-\xFF]/))throw new RangeError("The string contains invalid characters.");return t.replace(/[\s\S]{1,3}/g,f)},b=function(t){return h(p(String(t)))},g=function(t){return t.replace(/[+\/]/g,(function(t){return"+"==t?"-":"_"})).replace(/=/g,"")},w=function(t,e){return e?g(b(t)):b(t)},v=function(t){return w(t,!0)};a.Uint8Array&&(o=function(t,e){for(var a="",r=0,i=t.length;r<i;r+=3){var o=t[r],n=t[r+1],s=t[r+2],c=o<<16|n<<8|s;a+=l.charAt(c>>>18)+l.charAt(c>>>12&63)+("undefined"!=typeof n?l.charAt(c>>>6&63):"=")+("undefined"!=typeof s?l.charAt(63&c):"=")}return e?g(a):a});var y,x=/[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g,S=function(t){switch(t.length){case 4:var e=(7&t.charCodeAt(0))<<18|(63&t.charCodeAt(1))<<12|(63&t.charCodeAt(2))<<6|63&t.charCodeAt(3),a=e-65536;return d(55296+(a>>>10))+d(56320+(1023&a));case 3:return d((15&t.charCodeAt(0))<<12|(63&t.charCodeAt(1))<<6|63&t.charCodeAt(2));default:return d((31&t.charCodeAt(0))<<6|63&t.charCodeAt(1))}},k=function(t){return t.replace(x,S)},C=function(t){var e=t.length,a=e%4,r=(e>0?c[t.charAt(0)]<<18:0)|(e>1?c[t.charAt(1)]<<12:0)|(e>2?c[t.charAt(2)]<<6:0)|(e>3?c[t.charAt(3)]:0),i=[d(r>>>16),d(r>>>8&255),d(255&r)];return i.length-=[0,0,2,1][a],i.join("")},F=a.atob&&"function"==typeof a.atob?function(t){return a.atob(t)}:function(t){return t.replace(/\S{1,4}/g,C)},D=function(t){return F(String(t).replace(/[^A-Za-z0-9\+\/]/g,""))},$=function(t){return k(F(t))},_=function(t){return String(t).replace(/[-_]/g,(function(t){return"-"==t?"+":"/"})).replace(/[^A-Za-z0-9\+\/]/g,"")},B=function(t){return $(_(t))};a.Uint8Array&&(y=function(t){return Uint8Array.from(D(_(t)),(function(t){return t.charCodeAt(0)}))});var P=function(){var t=a.Base64;return a.Base64=n,t};if(a.Base64={VERSION:s,atob:D,btoa:h,fromBase64:B,toBase64:w,utob:p,encode:w,encodeURI:v,btou:k,decode:B,noConflict:P,fromUint8Array:o,toUint8Array:y},"function"===typeof Object.defineProperty){var A=function(t){return{value:t,enumerable:!1,writable:!0,configurable:!0}};a.Base64.extendString=function(){Object.defineProperty(String.prototype,"fromBase64",A((function(){return B(this)}))),Object.defineProperty(String.prototype,"toBase64",A((function(t){return w(this,t)}))),Object.defineProperty(String.prototype,"toBase64URI",A((function(){return w(this,!0)})))}}return a["Meteor"]&&(Base64=a.Base64),t.exports?t.exports.Base64=a.Base64:(r=[],i=function(){return a.Base64}.apply(e,r),void 0===i||(t.exports=i)),{Base64:a.Base64}}))}).call(this,a("c8ba"))},"2a79":function(t,e,a){"use strict";a("0f23")},"3ebe":function(t,e,a){},"541f":function(t,e,a){"use strict";a("7c36")},"5cc6":function(t,e,a){var r=a("74e8");r("Uint8",(function(t){return function(e,a,r){return t(this,e,a,r)}}))},"7c36":function(t,e,a){},"818c":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.FormVisible?a("div",{ref:"formedit",staticClass:"formedit"},[a("formedit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeidx:t.changeidx,bindData:t.bindData}))],1):t._e(),a("div",{directives:[{name:"show",rawName:"v-show",value:!t.FormVisible,expression:"!FormVisible"}],staticClass:"index"},[a("listheader",{attrs:{tableForm:t.tableForm},on:{btnsearch:t.search,bindData:t.bindData,btnadd:function(e){return t.showform(0)},AdvancedSearch:t.AdvancedSearch,btnHelp:t.btnHelp,btnPull:t.btnPull,btnDelete:t.btnDelete,btnClone:t.btnClone}}),a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{attrs:{span:t.showHelp?20:24}},[a("div",[a("el-table",{ref:"tableList",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},height:t.tableMaxHeight},on:{"selection-change":t.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"50",align:"center"}}),a("el-table-column",{attrs:{align:"center",label:"ID",width:"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.$index+1)+" ")]}}])}),t._l(t.tableForm.item,(function(e,r){return[!e.displaymark?t._e():a("el-table-column",{key:r,attrs:{prop:e.itemcode,columnKey:e.itemcode,label:e.itemname,align:e.aligntype?e.aligntype:"center","min-width":e.minwidth,"show-overflow-tooltip":!!e.overflow,sortable:!!e.sortable},scopedSlots:t._u([{key:"default",fn:function(r){return["rptname"==e.itemcode?a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.showform(r.row.id)}}},[t._v(t._s(r.row[e.itemcode]?r.row[e.itemcode]:"报表名称"))]):"createdate"==e.itemcode?a("span",[t._v(t._s(t._f("dateFormat")(r.row[e.itemcode])))]):"tenantid"==e.itemcode?a("div",["default"==r.row.tenantid?a("el-tag",{attrs:{size:"small"}},[t._v("通 用")]):a("el-tag",{attrs:{type:"warning",size:"small"}},[t._v("定 制")])],1):"enabledmark"==e.itemcode?a("div",[r.row[e.itemcode]?a("el-tag",{attrs:{size:"small"}},[t._v("正 常")]):a("el-tag",{attrs:{type:"warning",size:"small"}},[t._v("停 用")])],1):a("span",[t._v(t._s(r.row[e.itemcode]))])]}}],null,!0)})]}))],2),a("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)],1)])],1)],1)],1),t.gropuFormVisible?a("el-dialog",{attrs:{title:"报表中心","append-to-body":!0,visible:t.gropuFormVisible,width:"800px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.gropuFormVisible=e}}},[a("group",{ref:"group",attrs:{idx:t.idx,pid:t.pid},on:{bindData:t.BindTreeData,closeDialog:function(e){t.gropuFormVisible=!1}}})],1):t._e(),a("el-dialog",{staticClass:"reportDialog",attrs:{title:"报表信息","append-to-body":!0,width:"460px",visible:t.cloneVisible,"close-on-click-modal":!1},on:{"update:visible":function(e){t.cloneVisible=e}}},[a("el-form",{ref:"form",attrs:{model:t.cloneForm,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"报表名称",prop:"rptname"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入报表名称"},model:{value:t.cloneForm.rptname,callback:function(e){t.$set(t.cloneForm,"rptname",e)},expression:"cloneForm.rptname"}})],1),a("el-form-item",{attrs:{label:"报表类型",prop:"rpttype"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入报表类型"},model:{value:t.cloneForm.rpttype,callback:function(e){t.$set(t.cloneForm,"rpttype",e)},expression:"cloneForm.rpttype"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:t.submitClone}},[t._v("确定")]),a("el-button",{on:{click:function(e){t.cloneVisible=!1}}},[t._v("取 消")])],1)],1)],1)},i=[],o=a("c7eb"),n=a("b85c"),s=a("1da1"),l=(a("e9c4"),a("d3b7"),a("3ca3"),a("ddb0"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{class:t.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[a("div",[a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px","margin-left":"10px"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini",clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnsearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnsearch},slot:"append"},[t._v("搜索 ")])],1),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-edit",plain:"",size:"mini"},on:{click:t.btnadd}},[t._v(" 添加 ")]),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"danger",icon:"el-icon-delete",plain:"",size:"mini"},on:{click:function(e){return t.$emit("btnDelete")}}},[t._v(" 删除 ")]),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-document-copy",plain:"",size:"mini"},on:{click:function(e){return t.$emit("btnClone")}}},[t._v(" 克隆 ")])],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){return t.openDialog()}}})],1)]),t.setColumsVisible?a("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.setColumsVisible=e}}},[a("Setcolums",{ref:"setcolums",attrs:{code:t.code,tableForm:t.tableForm},on:{bindData:t.bindData,closeDialog:function(e){t.setColumsVisible=!1}}}),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.submitUpdate()}}},[t._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.setColumsVisible=!1}}},[t._v("取 消")])],1)],1):t._e()],1)}),c=[],d=a("8daf"),m={name:"Listheader",components:{Setcolums:d["a"]},props:["tableForm"],data:function(){return{strfilter:"",iShow:!1,formdata:{},code:"S16M96S1List",setColumsVisible:!1,searchVisible:!1}},methods:{openDialog:function(){this.setColumsVisible=!0},submitUpdate:function(){this.$refs.setcolums.submitUpdate()},AdvancedSearch:function(t){this.iShow=!1,this.$emit("AdvancedSearch",t),this.searchVisible=!1},btnadd:function(){this.$emit("btnadd")},bindData:function(){this.$emit("bindData")},btnsearch:function(){this.$emit("btnsearch",this.strfilter)}}},u=m,p=(a("aa92"),a("2877")),f=Object(p["a"])(u,l,c,!1,null,"03196a7e",null),h=f.exports,b=a("333d"),g=a("b775"),w=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("el-button",{attrs:{type:"primary",size:"small"},nativeOn:{click:function(e){return t.submitForm("formdata")}}},[t._v(" 保 存")]),a("el-dropdown",{staticStyle:{margin:"0 10px"},attrs:{trigger:"click",placement:"bottom","hide-on-click":!1}},[a("el-button",{attrs:{size:"small"}},[t._v("操作"),a("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{icon:"el-icon-delete",disabled:!t.idx},nativeOn:{click:function(e){return t.deleteForm(t.idx)}}},[t._v("删 除")]),a("el-dropdown-item",{attrs:{divided:"",icon:"el-icon-upload"},nativeOn:{click:function(e){return t.$refs.upload.click()}}},[t._v("上传报表")]),a("el-dropdown-item",{attrs:{divided:"",icon:"el-icon-download",disabled:!t.reportBase64Data},nativeOn:{click:function(e){return t.downloadJp(e)}}},[t._v("下载网页报表")]),a("el-dropdown-item",{attrs:{icon:"el-icon-download",disabled:!t.grfBase64Data},nativeOn:{click:function(e){return t.downloadGrf(e)}}},[t._v("下载云打印报表")])],1)],1),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.closeForm(e)}}},[t._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth,"auto-complete":"on",rules:t.formRules}},[a("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),a("el-row",[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("modulecode")}}},[a("el-form-item",{attrs:{label:"模块编码",prop:"modulecode"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入模块编码",size:"small"},model:{value:t.formdata.modulecode,callback:function(e){t.$set(t.formdata,"modulecode",e)},expression:"formdata.modulecode"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("rptname")}}},[a("el-form-item",{attrs:{label:"报表名称",prop:"rptname"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入报表名称",size:"small"},model:{value:t.formdata.rptname,callback:function(e){t.$set(t.formdata,"rptname",e)},expression:"formdata.rptname"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("rpttype")}}},[a("el-form-item",{attrs:{label:"报表类型",prop:"rpttype"}},[a("el-select",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请选择报表类型",size:"small"},model:{value:t.formdata.rpttype,callback:function(e){t.$set(t.formdata,"rpttype",e)},expression:"formdata.rpttype"}},[a("el-option",{attrs:{label:"单据",value:"单据"}}),a("el-option",{attrs:{label:"列表",value:"列表"}}),a("el-option",{attrs:{label:"报表",value:"报表"}})],1)],1)],1)]),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"有效性"}},[a("el-checkbox",{attrs:{label:"有效","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.enabledmark,callback:function(e){t.$set(t.formdata,"enabledmark",e)},expression:"formdata.enabledmark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"单页行数",prop:"pagerow"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!0,type:"number",min:0,"controls-position":"right",size:"small"},model:{value:t.formdata.pagerow,callback:function(e){t.$set(t.formdata,"pagerow",e)},expression:"formdata.pagerow"}})],1)],1),a("el-col",{attrs:{span:3}},[a("el-form-item",{attrs:{label:"排序"}},[a("el-input-number",{staticClass:"inputNumberContent",attrs:{controls:!0,type:"number",min:0,size:"small","controls-position":"right"},model:{value:t.formdata.rownum,callback:function(e){t.$set(t.formdata,"rownum",e)},expression:"formdata.rownum"}})],1)],1)],1),a("el-row",{staticStyle:{"margin-top":"15px"}},[a("el-col",{attrs:{span:24}},[a("el-tabs",{staticStyle:{"min-height":"380px"},attrs:{"tab-position":"left"}},[a("el-tab-pane",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"网页报表"}},[a("div",{staticClass:"box-card"},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入网页报表数据",size:"small",type:"textarea",autosize:{minRows:18,maxRows:21}},model:{value:t.reportBase64Data,callback:function(e){t.reportBase64Data=e},expression:"reportBase64Data"}})],1)]),a("el-tab-pane",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"云打印报表"}},[a("div",{staticClass:"box-card"},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入云打印报表数据",size:"small",type:"textarea",autosize:{minRows:18,maxRows:21}},model:{value:t.grfBase64Data,callback:function(e){t.grfBase64Data=e},expression:"grfBase64Data"}})],1)])],1)],1)],1),a("el-divider",{attrs:{"content-position":"left"}},[t._v("云打印参数")]),a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"打印机SN",prop:"printersn"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入打印机SN",size:"small",clearable:""},on:{focus:function(t){return t.currentTarget.select()}},model:{value:t.formdata.printersn,callback:function(e){t.$set(t.formdata,"printersn",e)},expression:"formdata.printersn"}})],1)],1),a("el-col",{attrs:{span:3}},[a("el-form-item",{attrs:{label:"长",prop:"paperlength","label-width":"60px"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!0,type:"number",min:0,"controls-position":"right",size:"small",precision:1},on:{focus:function(t){return t.currentTarget.select()}},model:{value:t.formdata.paperlength,callback:function(e){t.$set(t.formdata,"paperlength",e)},expression:"formdata.paperlength"}})],1)],1),a("el-col",{attrs:{span:3}},[a("el-form-item",{attrs:{label:"宽",prop:"paperwidth","label-width":"60px"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!0,type:"number",min:0,"controls-position":"right",size:"small",precision:1},on:{focus:function(t){return t.currentTarget.select()}},model:{value:t.formdata.paperwidth,callback:function(e){t.$set(t.formdata,"paperwidth",e)},expression:"formdata.paperwidth"}})],1)],1)],1),a("el-divider"),a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormats")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormats")(t.formdata.modifydate)))])])],1)],1)],1)],1)])]),a("div",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}]},[a("input",{ref:"upload",attrs:{type:"file"},on:{change:t.getFile}})])])},v=[],y=a("2909");a("b64b"),a("d81d"),a("99af"),a("2b3d"),a("9861"),a("ac1f"),a("466d"),a("ace4"),a("5cc6"),a("9a8c"),a("a975"),a("735e"),a("c1ac"),a("d139"),a("3a7b"),a("d5d6"),a("82f8"),a("e91f"),a("60bd"),a("5f96"),a("3280"),a("3fcc"),a("ca91"),a("25a1"),a("cd26"),a("3c5d"),a("2954"),a("649e"),a("219c"),a("170b"),a("b39a"),a("72f7"),a("b0c0");const x={add(t){return new Promise((e,a)=>{var r=JSON.stringify(t);g["a"].post("/SaReports/create",r).then(t=>{console.log(r,t),200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},update(t){return new Promise((e,a)=>{var r=JSON.stringify(t);g["a"].post("/SaReports/update",r).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},delete(t){return new Promise((e,a)=>{g["a"].get("/SaReports/delete?key="+t).then(t=>{console.log("删除："+t),200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})}};var S=x,k=a("27ae"),C={name:"Formedit",components:{},props:["idx"],data:function(){return{title:"报表中心",formdata:{modulecode:"",rpttype:"",rptname:"",rptdata:"",grfdata:"",pagerow:0,rownum:0,enabledmark:1,remark:"",tempurl:"",printersn:"local",filename:"",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,tenantid:"default",gengroupid:"",paperlength:0,paperwidth:0},reportBase64Data:"",grfBase64Data:"",formRules:{rpttype:[{required:!0,trigger:"blur",message:"报表类型为必填项"}],rptname:[{required:!0,trigger:"blur",message:"报表名称为必填项"}],modulecode:[{required:!0,trigger:"blur",message:"模块编码为必填项"}]},formLabelWidth:"100px",multi:0,selVisible:!1,groupData:[],defaultProps:{children:"children",label:"label",value:"id"},filetype:"grf"}},computed:{formcontainHeight:function(){return window.innerHeight-120+"px"}},watch:{idx:function(t,e){this.bindData()}},mounted:function(){this.bindData()},methods:{bindData:function(){var t=this;return Object(s["a"])(Object(o["a"])().mark((function e(){return Object(o["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(0==t.idx){e.next=4;break}return t.listLoading=!0,e.next=4,g["a"].get("/SaReports/getEntity?key=".concat(t.idx)).then((function(e){200==e.data.code&&(t.formdata=e.data.data,t.reportBase64Data=e.data.data.rptdata,t.grfBase64Data=e.data.data.grfdata),t.listLoading=!1})).catch((function(e){t.listLoading=!1}));case 4:case"end":return e.stop()}}),e)})))()},BindTreeData:function(){var t=this;g["a"].get("/system/SYSM07B8/getDefListByModuleCode?Code=SaReports").then((function(e){if(200==e.data.code){var a=e.data.data.map((function(t){return{id:t.id,pid:t.parentid,label:t.groupname}})),r=[{id:"0",pid:"root",label:"报表中心"}],i=[].concat(Object(y["a"])(a),r);t.groupData=t.transData(i,"id","pid","children")}}))},handleChange:function(t){console.log(t),t.length>0?this.formdata.gengroupid=t[t.length-1]:this.formdata.gengroupid="0"},submitForm:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;this.formdata.rptdata=k["Base64"].encode(this.reportBase64Data),this.formdata.grfdata=k["Base64"].encode(this.grfBase64Data),0==this.idx?S.add(this.formdata).then((function(e){200==e.code?(t.$message.success("保存成功"),t.$emit("changeidx",e.data.id),t.$emit("bindData"),t.bindData()):t.$message.warning("保存失败")})):S.update(this.formdata).then((function(e){200==e.code?(t.$message.success("保存成功"),t.bindData(),t.$emit("bindData")):t.$message.warning("保存失败")}))},closeForm:function(){this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){S.delete(t).then((function(){e.$message.success("删除成功"),e.$emit("compForm")})).catch((function(){e.$message.warning("删除失败")}))})).catch((function(){}))},downloadGrf:function(){var t=k["Base64"].encode(this.grfBase64Data),e=new Blob([this.dataURLtoBlob(t)],{type:"video/x-msvideo"}),a=document.createElement("a");a.download=this.formdata.rptname+".grf",a.style.display="none",a.href=URL.createObjectURL(e),document.body.appendChild(a),a.click(),document.body.removeChild(a)},downloadJp:function(){var t=k["Base64"].encode(this.reportBase64Data),e=new Blob([this.dataURLtoBlob(t)],{type:"text/xml"}),a=document.createElement("a");a.download=this.formdata.rptname+".jrxml",a.style.display="none",a.href=URL.createObjectURL(e),document.body.appendChild(a),a.click(),document.body.removeChild(a)},dataURLtoBlob:function(t){try{var e=t.split(","),a=e[0].match(/:(.*?);/)[1],r=atob(e[1]),i=r.length,o=new Uint8Array(i);while(i--)o[i]=r.charCodeAt(i);return new Blob([o],{type:a})}catch(d){var n=t.split(","),s=atob(n[0]),l=s.length,c=new Uint8Array(l);while(l--)c[l]=s.charCodeAt(l);return new Blob([c])}},fileToBase64:function(t){var e=new FileReader;e.readAsDataURL(t),e.onload=function(t){var e=t.target.result.split(";base64,")[1];return e}},handleFocus:function(){this.selVisible=!0},handleBlur:function(){this.selVisible=!1},getFile:function(){var t=this,e=this.$refs.upload,a=e.files[0],r=new FileReader;r.readAsDataURL(a),r.onload=function(e){var r=e.target.result.split(";base64,")[1],i=a.name.lastIndexOf("."),o=a.name.substr(i+1);"grf"==o?t.grfBase64Data=k["Base64"].decode(r):"jrxml"==o?t.reportBase64Data=k["Base64"].decode(r):t.$message.warning("请上传正确的jrxml或grf文件")}},cleValidate:function(t){this.$refs.formdata.clearValidate(t)},transData:function(t,e,a,r){for(var i=[],o={},n=e,s=a,l=r,c=0,d=0,m=t.length;c<m;c++)o[t[c][n]]=t[c];for(;d<m;d++){var u=t[d],p=o[u[s]];p?(!p[l]&&(p[l]=[]),p[l].push(u)):i.push(u)}return i}}},F=C,D=(a("2a79"),Object(p["a"])(F,w,v,!1,null,"6c19a67d",null)),$=D.exports,_=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticStyle:{width:"100%"}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth,rules:t.formRules}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[a("el-form-item",{attrs:{label:"分组名称",prop:"groupname"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入分组名称",size:"small"},on:{input:t.writeCode},model:{value:t.formdata.groupname,callback:function(e){t.$set(t.formdata,"groupname",e)},expression:"formdata.groupname"}})],1)],1)]),a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("groupcode")}}},[a("el-form-item",{attrs:{label:"分组编码",prop:"groupcode"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入分组编码",size:"small"},model:{value:t.formdata.groupcode,callback:function(e){t.$set(t.formdata,"groupcode",e)},expression:"formdata.groupcode"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"排序"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{"controls-position":"right",min:0,size:"small"},model:{value:t.formdata.rownum,callback:function(e){t.$set(t.formdata,"rownum",e)},expression:"formdata.rownum"}})],1)],1)],1)],1)],1),a("el-form",{staticClass:"footFormContent",attrs:{"label-width":t.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.modifydate)))])])],1)],1)],1)],1)]),a("div",{staticClass:"button-container flex j-end"},[a("el-button",{attrs:{type:"primary",size:"small"},nativeOn:{click:function(e){return t.submitForm("formdata")}}},[t._v(" 保 存")]),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.$emit("closeDialog")}}},[t._v(" 关 闭")])],1)])},B=[],P=a("b0b8"),A={name:"Formedit",props:["idx","pid"],data:function(){return{formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,parentid:"",modulecode:"S16M96S1",groupcode:"",groupname:"",rownum:0,remark:"",enabledmark:1},formRules:{modulecode:[{required:!0,trigger:"blur",message:"功能编码为必填项"}],groupcode:[{required:!0,trigger:"blur",message:"分组编码为必填项"}],groupname:[{required:!0,trigger:"blur",message:"分组名称为必填项"}]},formLabelWidth:"100px",dialogVisible:!1,delDialogVisible:!1,option:""}},computed:{formcontainHeight:function(){return"300px"}},watch:{idx:function(t,e){console.log("new: %s, old: %s",t,e),this.bindData()}},created:function(){this.bindData()},methods:{bindData:function(){var t=this;0!=this.idx?g["a"].get("/system/SYSM07B8/getEntity?key=".concat(this.idx)).then((function(e){200==e.data.code&&(t.formdata=e.data.data)})):this.formdata.parentid=this.idx},submitForm:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;this.pid&&(this.formdata.parentid=this.pid),0==this.idx?g["a"].post("/system/SYSM07B8/create",JSON.stringify(this.formdata)).then((function(e){t.$emit("bindData"),t.$emit("closeDialog")})):g["a"].post("/system/SYSM07B8/update",JSON.stringify(this.formdata)).then((function(e){t.$emit("bindData"),t.$emit("closeDialog")}))},closeForm:function(){this.$emit("closeForm"),console.log("关闭窗口")},check:function(){console.log("check")},cleValidate:function(t){this.$refs.formdata.clearValidate(t)},writeCode:function(t){P.setOptions({checkPolyphone:!1,charCase:1}),this.formdata.groupcode=P.getFullChars(t)}}},z=A,O=(a("541f"),Object(p["a"])(z,_,B,!1,null,"2470e1e8",null)),V=O.exports,L=a("0521"),j={formcode:"S16M96S1List",item:[{itemcode:"modulecode",itemname:"模块编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"rptname",itemname:"报表名称",minwidth:"80",displaymark:1,overflow:1},{itemcode:"rpttype",itemname:"报表类型",minwidth:"80",displaymark:1,overflow:1},{itemcode:"enabledmark",itemname:"有效性",minwidth:"100",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.remark"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1},{itemcode:"createdate",itemname:"创建时间",minwidth:"120",displaymark:1,overflow:1}]},N={components:{Pagination:b["a"],listheader:h,formedit:$,group:V,helpmodel:L["a"]},data:function(){return{title:"",lst:[],FormVisible:!1,idx:0,searchstr:"",total:0,queryParams:{PageNum:1,PageSize:20,OrderType:0,SearchType:1,OrderBy:"rownum"},pid:"root",treeTitle:"报表中心",gropuFormVisible:!1,treeVisble:!0,groupData:[],defaultProps:{children:"children",label:"label",value:"id"},treeEditable:!1,tableForm:j,showHelp:!1,selectList:[],cloneVisible:!1,cloneForm:{rpttype:"",rptname:""}}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},mounted:function(){this.bindData()},methods:{bindData:function(){var t=this;this.listLoading=!0,g["a"].post("/SaReports/getPageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},getcolumn:function(){var t=this;g["a"].get("/S16M87B1/getBillEntityByCode?code=SaReportsList").then((function(e){if(200==e.data.code){if(null==e.data.data)return void(t.tableForm=j);t.tableForm=e.data.data}})).catch((function(e){t.$message.error("请求出错")}))},btnPull:function(){var t=this;g["a"].get("/SaReports/pullDefault").then((function(e){200==e.data.code?(t.$message.success("拉取默认报表成功"),t.bindData()):t.$message.warning(e.data.msg||"拉取默认报表失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},handleSelectionChange:function(t){this.selectList=t},btnDelete:function(){var t=this;0!=this.selectList.length?this.$confirm("此操作将永久删除记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.deleteRows()})).catch((function(){})):this.$message.warning("请先选择报表")},deleteRows:function(t,e){var a=this;return Object(s["a"])(Object(o["a"])().mark((function t(){var e,r,i,s,l,c;return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e=a,r=a.selectList,!r){t.next=22;break}i=[],s=Object(n["a"])(r),t.prev=5,c=Object(o["a"])().mark((function t(){var e,a;return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e=l.value,a=new Promise((function(t,a){S.delete(e.id).then((function(e){200==e.code?0==e.data?a("删除失败"):t("删除成功"):a("删除失败")})).catch((function(t){a("删除失败")}))})),i.push(a);case 3:case"end":return t.stop()}}),t)})),s.s();case 8:if((l=s.n()).done){t.next=12;break}return t.delegateYield(c(),"t0",10);case 10:t.next=8;break;case 12:t.next=17;break;case 14:t.prev=14,t.t1=t["catch"](5),s.e(t.t1);case 17:return t.prev=17,s.f(),t.finish(17);case 20:return t.next=22,Promise.all(i).then((function(t){e.$message.success("删除成功"),a.selectList=[]})).catch((function(t){e.$message.warning(t)})).finally((function(){e.bindData()}));case 22:a.$refs.tableList.clearSelection();case 23:case"end":return t.stop()}}),t,null,[[5,14,17,20]])})))()},btnClone:function(){1==this.selectList.length?(this.cloneVisible=!0,this.cloneForm={rpttype:"",rptname:""}):this.$message.warning("请选择一份要克隆的报表")},submitClone:function(){var t=this,e=Object.assign({},this.selectList[0]);this.$delete(e,"id"),this.$delete(e,"lister"),this.$delete(e,"listerid"),this.$delete(e,"modifydate"),this.$delete(e,"createdate"),this.$delete(e,"tenantid"),this.$delete(e,"tenantname"),this.$delete(e,"createby"),this.$delete(e,"createbyid"),this.$set(e,"rpttype",this.cloneForm.rpttype),this.$set(e,"rptname",this.cloneForm.rptname),e.rptdata=k["Base64"].encode(e.rptdata),e.grfdata=k["Base64"].encode(e.grfdata),S.add(e).then((function(e){200==e.code?(t.$message.success("保存成功"),t.bindData(),t.cloneVisible=!1):t.$message.warning(e.msg||"保存失败")})).catch((function(e){t.$message.warning(e||"保存失败")}))},btnHelp:function(){this.showHelp=!this.showHelp,this.showHelp&&this.$refs.helpmodel.bindData()},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){""!=t?this.queryParams.SearchPojo={modulecode:t,rptname:t,rpttype:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},AdvancedSearch:function(t){this.queryParams.SearchPojo=t,this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},showform:function(t){this.idx=t,this.FormVisible=!0},closeForm:function(){this.FormVisible=!1,this.bindData()},compForm:function(){this.bindData(),this.FormVisible=!1,console.log("完成并刷新index")},changeidx:function(t){this.idx=t},searchByTree:function(t){""!=t?this.queryParams.SearchPojo={gengroupid:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},handleNodeClick:function(t){if(0==t.id){var e="";this.searchByTree(e)}else{var a=t.id;this.searchByTree(a)}},editTreeNode:function(t){this.showGroupform(t.id)},addTreeChild:function(t){this.pid=t.id,this.showGroupform(0)},delTreeNode:function(t){var e=this;t.children?this.$message({message:"警告,该节点下存在子节点不能删除",type:"warning"}):this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){console.log("执行删除"),g["a"].get("/system/SYSM07B8/delete?key=".concat(t.id)).then((function(){e.$message.success({message:"删除成功！"}),e.BindTreeData()})).catch((function(){e.$message({showClose:!0,message:"删除失败",type:"warning"})}))})).catch((function(){}))},showGroupform:function(t){this.idx=t,console.log(this.idx),this.gropuFormVisible=!0},transData:function(t,e,a,r){for(var i=[],o={},n=e,s=a,l=r,c=0,d=0,m=t.length;c<m;c++)o[t[c][n]]=t[c];for(;d<m;d++){var u=t[d],p=o[u[s]];p?(!p[l]&&(p[l]=[]),p[l].push(u)):i.push(u)}return i}}},R=N,T=(a("d796"),Object(p["a"])(R,r,i,!1,null,"08fc2869",null));e["default"]=T.exports},aa92:function(t,e,a){"use strict";a("f4b0")},d796:function(t,e,a){"use strict";a("3ebe")},f4b0:function(t,e,a){}}]);