(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4ae5aba1"],{c9b6:function(t,n,e){"use strict";e.r(n);var o=function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("div",{staticClass:"warp"},[e("mylogin",{directives:[{name:"show",rawName:"v-show",value:t.showLogin,expression:"showLogin"}],ref:"mylogin"})],1)},a=[],i=(e("d3b7"),e("3ca3"),e("ddb0"),e("9861"),e("e9c4"),e("b64b"),e("b775")),c=e("9ed6"),s=e("5f87"),r={components:{mylogin:c["default"]},data:function(){return{value:"",value1:"",zhi:"",isloading:!0,info:"成功",showLogin:!1}},created:function(){},mounted:function(){this.Bindata()},methods:{Bindata:function(){var t=this;this.value=window.location.href?window.location.href:"";var n=this.value.split("?")[1],e=new URLSearchParams("?"+n),o=e.get("key"),a=e.get("tid");i["a"].get("/auth/getLoginUser?key="+o).then((function(n){if(200==n.data.code){t.showLogin=!0;var e=n.data.data.loginuser;localStorage.setItem("getInfo",JSON.stringify(e)),t.$store.state.user.userinfo=e;var o=n.data.data.access_token;Object(s["c"])(o),t.$store.state.user.token=o,console.log(e.tenantid,"Tid",a),e.tenantid?t.readnav():t.$refs.mylogin.getTenant()}else t.$confirm("获取用户信息失败，是否返回登录？","提示",{confirmButtonText:"返回登录",cancelButtonText:"退出系统",type:"warning",closeOnClickModal:!1}).then((function(n){"confirm"===n?(Object(s["b"])(),t.$router.push("/login"),t.$store.dispatch("user/logout")):(window.location.href="about:blank",window.close())})).catch((function(){window.location.href="about:blank",window.close()}))})).catch((function(n){t.$confirm("获取用户信息失败，是否返回登录？","提示",{confirmButtonText:"返回登录",cancelButtonText:"退出系统",type:"warning",closeOnClickModal:!1}).then((function(n){"confirm"===n?(Object(s["b"])(),t.$router.push("/login"),t.$store.dispatch("user/logout")):window.close()})).catch((function(){window.close()}))}))},readnav:function(){var t=this;i["a"].get("/system/SYSM02B2/getMenuWebListBySelf").then((function(n){if(200==n.data.code){var e=n.data.data;t.isloading=!1,localStorage.setItem("navjson",JSON.stringify(e)),t.$store.dispatch("app/setnavdata",e),t.showWorkbench()}else t.$confirm("获取服务菜单失败，是否重试？","提示",{confirmButtonText:"重试",cancelButtonText:"退出系统",type:"warning",closeOnClickModal:!1}).then((function(n){"confirm"===n?t.readnav():window.close()})).catch((function(){window.close()}))})).catch((function(t){}))},showWorkbench:function(){var t=this.$store.state.user.userinfo.configs["system.style.dashboard"];if(null==t||""==t)this.$router.push({path:this.redirect||"/"});else{var n=JSON.parse(t);this.$router.push(n.value)}}}},u=r,l=(e("ddf7"),e("2877")),d=Object(l["a"])(u,o,a,!1,null,"218d1ed7",null);n["default"]=d.exports},ddf7:function(t,n,e){"use strict";e("eb0f")},eb0f:function(t,n,e){}}]);