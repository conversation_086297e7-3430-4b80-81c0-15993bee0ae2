(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-348550d8"],{"082f":function(t,e,a){"use strict";a("7556")},1381:function(t,e,a){},"1b29":function(t,e,a){"use strict";a("d975")},"270e":function(t,e,a){"use strict";a("cbca")},3862:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.formvisible?a("div",{ref:"formedit",staticClass:"formedit"},[a("formedit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),a("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}],ref:"index",staticClass:"index"},[a("listheader",{attrs:{tableForm:t.tableForm},on:{btnAdd:function(e){return t.showForm(0)},btnSearch:t.search,bindData:t.bindData,advancedSearch:t.advancedSearch,allDelete:t.allDelete,bindColumn:t.getColumn,btnHelp:t.btnHelp}}),a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{attrs:{span:t.showHelp?20:24}},[a("ve-table",{ref:"tableList",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"userid","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"border-x":"","border-y":"","border-around":!0,"checkbox-option":t.checkboxOption,"column-width-resize-option":t.columnWidthResizeOption,"virtual-scroll-option":t.virtualScrollOption,"fixed-footer":!0,"sort-option":t.sortOption}}),a("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[a("div",{staticClass:"flex a-c"},[a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1),a("div",{staticStyle:{"margin-right":"40px"}},[a("scene",{ref:"scene",attrs:{code:"D03M01B1List"},on:{bindData:t.bindData}})],1)])],1),a("el-col",{attrs:{span:t.showHelp?4:0}},[a("helpmodel",{ref:"helpmodel",attrs:{code:"SYSM01B9"}})],1)],1)],1)],1),a("el-drawer",{attrs:{visible:t.rolesVisible,"with-header":!1,size:"50%"},on:{"update:visible":function(e){t.rolesVisible=e}}},[t.rolesVisible?a("roles",{ref:"roles",attrs:{idx:t.idx,drawdata:t.drawdata},on:{drawclose:function(e){t.rolesVisible=!1}}}):t._e()],1),a("el-drawer",{attrs:{visible:t.funsVisible,"with-header":!1,size:"50%"},on:{"update:visible":function(e){t.funsVisible=e}}},[t.funsVisible?a("funs",{ref:"funs",attrs:{idx:t.idx,drawdata:t.drawdata},on:{drawclose:function(e){t.funsVisible=!1}}}):t._e()],1)],1)},n=[],s=a("c7eb"),r=a("b85c"),o=a("1da1"),l=(a("a9e3"),a("b64b"),a("e9c4"),a("d3b7"),a("159b"),a("c7cd"),a("3ca3"),a("ddb0"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{class:t.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[a("div",[a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnSearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnSearch},slot:"append"},[t._v("搜索 ")])],1),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-plus",plain:"",size:"mini"},on:{click:t.btnAdd}},[t._v(" 添加 ")]),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"danger",icon:"el-icon-delete",plain:"",size:"mini"},on:{click:function(e){return t.$emit("allDelete")}}},[t._v(" 批量删除 ")])],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-search",title:"高级筛选",type:t.$store.state.advancedSearch.modulecode==t.code?"primary":"default"},on:{click:function(e){return t.openSearchForm()}}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:function(e){return t.$emit("bindData")}}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){return t.openDialog()}}})],1)]),t.setColumsVisible?a("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.setColumsVisible=e}}},[a("Setcolums",{ref:"setcolums",attrs:{code:t.code,tableForm:t.tableForm},on:{bindData:function(e){return t.$emit("bindColumn")},closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e(),a("el-dialog",{attrs:{title:"高级筛选",width:"720px",visible:t.searchVisible,"append-to-body":!0,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.searchVisible=e}}},[a("searchForm",{ref:"searchForm",attrs:{code:t.code},on:{advancedSearch:t.advancedSearch,closedDialog:function(e){t.searchVisible=!1},bindData:t.bindData}})],1)],1)}),c=[],d=a("8daf"),m=a("7689"),u={name:"Listheader",props:["tableForm"],components:{Setcolums:d["a"],searchForm:m["a"]},data:function(){return{strfilter:"",iShow:!1,formdata:{},setColumsVisible:!1,code:"SYSM01B9List",searchVisible:!1}},methods:{openDialog:function(){this.setColumsVisible=!0},advancedSearch:function(t){this.iShow=!1,this.$emit("advancedSearch",t),this.searchVisible=!1},openSearchForm:function(){var t=this;this.searchVisible=!0,setTimeout((function(){t.$refs.searchForm.getInit()}),100)},btnAdd:function(){this.$emit("btnAdd")},btnSearch:function(){this.$emit("btnSearch",this.strfilter)},bindData:function(){this.$emit("bindData")},defaultRole:function(){this.$emit("defaultRole")}}},f=u,h=(a("ce43"),a("2877")),p=Object(h["a"])(f,l,c,!1,null,"8cc46dc2",null),b=p.exports,g=a("333d"),v=a("b775"),w=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",size:"small"},nativeOn:{click:function(e){return t.submitForm("formdata")}}},[t._v(" 保 存")]),a("el-dropdown",{staticStyle:{margin:"0 10px"},attrs:{trigger:"click",placement:"bottom"}},[a("el-button",{attrs:{size:"small"}},[t._v("操作"),a("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{icon:"el-icon-delete",disabled:!t.idx},nativeOn:{click:function(e){return t.deleteForm(t.idx)}}},[t._v("删 除")]),a("el-dropdown-item",{attrs:{icon:"el-icon-edit-outline",disabled:!t.idx},nativeOn:{click:function(e){return t.initPassword()}}},[t._v("初始化密码")])],1)],1),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.closeForm(e)}}},[t._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth,"auto-complete":"on",rules:t.formRules}},[a("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),a("el-row",[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("username")}}},[a("el-form-item",{attrs:{label:"账号",prop:"username"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入账号",clearable:"",size:"small"},model:{value:t.formdata.username,callback:function(e){t.$set(t.formdata,"username",e)},expression:"formdata.username"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("realname")}}},[a("el-form-item",{attrs:{label:"姓名",prop:"realname"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入姓名",clearable:"",size:"small"},model:{value:t.formdata.realname,callback:function(e){t.$set(t.formdata,"realname",e)},expression:"formdata.realname"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"手机"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入手机",clearable:"",size:"small"},model:{value:t.formdata.mobile,callback:function(e){t.$set(t.formdata,"mobile",e)},expression:"formdata.mobile"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"邮箱"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入邮箱",clearable:"",size:"small"},model:{value:t.formdata.email,callback:function(e){t.$set(t.formdata,"email",e)},expression:"formdata.email"}})],1)],1)],1),a("el-divider",{attrs:{"content-position":"left"}},[t._v("关联内容")]),a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"类型"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择类型",size:"small"},model:{value:t.formdata.usertype,callback:function(e){t.$set(t.formdata,"usertype",e)},expression:"formdata.usertype"}},[a("el-option",{attrs:{label:"客户",value:1}}),a("el-option",{attrs:{label:"供应商",value:2}}),a("el-option",{attrs:{label:"加工厂商",value:3}})],1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:""}},[a("el-checkbox",{attrs:{label:"是否管理员","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.isadmin,callback:function(e){t.$set(t.formdata,"isadmin",e)},expression:"formdata.isadmin"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"往来单位"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{multiple:"",size:"small",clearable:""},on:{change:t.setGroupRow,clear:function(e){t.formdata.groupids="",t.formdata.groupnames="",t.groupVal=""}},model:{value:t.groupVal,callback:function(e){t.groupVal=e},expression:"groupVal"}},t._l(t.groupData,(function(e){return a("el-option",{key:e.id,attrs:{value:e.id,label:e.groupname}},[a("span",{staticStyle:{float:"left"}},[t._v(t._s(e.groupname))]),a("span",{staticStyle:{float:"right","margin-right":"12px",color:"#8492a6","font-size":"13px"}},[t._v(t._s(e.abbreviate))])])})),1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"服务"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{multiple:"",size:"small",clearable:""},on:{change:t.setScmFunctidRow,clear:function(e){t.formdata.scmfunctid="",t.formdata.scmfunctname="",t.scmFunctVal=""}},model:{value:t.scmFunctVal,callback:function(e){t.scmFunctVal=e},expression:"scmFunctVal"}},t._l(t.scmFunctData,(function(e){return a("el-option",{key:e.scmfunctid,attrs:{value:e.scmfunctid,label:e.scmfunctname}},[a("span",{staticStyle:{float:"left"}},[t._v(t._s(e.scmfunctname))]),a("span",{staticStyle:{float:"right","margin-right":"12px",color:"#8492a6","font-size":"13px"}},[t._v(t._s(e.scmfunctcode))])])})),1)],1)],1)],1)],1),a("el-divider")],1),a("el-form",{staticClass:"footFormContent",attrs:{"label-width":t.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.modifydate)))])])],1)],1)],1)],1)])])},y=[];a("ac1f"),a("00b4"),a("c740"),a("5319");const x={add(t){return new Promise((e,a)=>{var i=JSON.stringify(t);v["a"].post("/system/SYSM11B1/create",i).then(t=>{console.log(i,t),200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},update(t){return new Promise((e,a)=>{var i=JSON.stringify(t);v["a"].post("/system/SYSM11B1/update",i).then(t=>{console.log(t,i),200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},delete(t){return new Promise((e,a)=>{v["a"].get("/system/SYSM11B1/delete?key="+t).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})}};var S=x,_={name:"Formedit",components:{},props:["idx"],data:function(){var t=this,e=function(e,a,i){if(a){var n=/^([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})$/,s=/^(13[0-9]|14[5|7]|15[0|1|2|3|4|5|6|7|8|9]|18[0|1|2|3|5|6|7|8|9])\d{8}$/;if(!n.test(a)&&!s.test(a))return i(new Error("请输入正确的手机或邮箱格式"));v["a"].get("/system/SYSM11B1/getEntityByUserName?username="+t.formdata.username).then((function(e){if(200==e.data.code){if(e.data.data)for(var a in e.data.data)t.formdata[a]=e.data.data[a];i()}else t.$message.warning(e.data.msg||"查询用户信息失败")}))}};return{title:"SCM用户管理",formdata:{usercode:"",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,username:"",realname:"",mobile:"",email:"",userstatus:0,avatar:"",remark:"",isadmin:0},formRules:{username:[{required:!0,trigger:"blur",message:"登录账号为必填项"},{trigger:"blur",validator:e}],realname:[{required:!0,trigger:"blur",message:"姓名为必填项"}],password:[{required:!0,trigger:"blur",message:"密码为必填项"}]},formLabelWidth:"100px",groupData:[],groupVal:"",scmFunctVal:"",scmFunctData:[]}},computed:{formcontainHeight:function(){return window.innerHeight-50-33-50+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.getgroupData(),this.getscmFunctData()},mounted:function(){this.bindData()},methods:{bindData:function(){var t=this;this.listLoading=!0,0!=this.idx&&v["a"].get("/system/SYSM11B1/getEntity?key=".concat(this.idx)).then((function(e){200==e.data.code&&(t.formdata=e.data.data,t.formdata.groupids&&(t.groupVal=t.formdata.groupids.split(",")),t.formdata.scmfunctids&&(t.scmFunctVal=t.formdata.scmfunctids.split(","))),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},getgroupData:function(){var t=this,e={PageNum:1,PageSize:1e4,OrderType:1,SearchType:1};v["a"].post("/sale/D01M01R1/getOnlinePageList",JSON.stringify(e)).then((function(e){200==e.data.code&&(t.groupData=e.data.data.list)}))},setGroupRow:function(t){var e=this;this.formdata.groupids="",this.formdata.groupnames="",t.forEach((function(t){var a=e.groupData.findIndex((function(e){return t==e.id}));-1!=a&&(e.formdata.groupnames+=e.groupData[a].groupname+",",e.formdata.groupids+=e.groupData[a].id+",")}));var a=/,$/gi;this.formdata.groupids=this.formdata.groupids.replace(a,""),this.formdata.groupnames=this.formdata.groupnames.replace(a,"")},getscmFunctData:function(){var t=this,e={PageNum:1,PageSize:1e4,OrderType:1,SearchType:1};v["a"].post("/system/SYSM11B2/getPageList",JSON.stringify(e)).then((function(e){200==e.data.code&&(t.scmFunctData=e.data.data.list)}))},setScmFunctidRow:function(t){var e=this;this.formdata.scmfunctids="",this.formdata.scmfunctnames="",t.forEach((function(t){var a=e.scmFunctData.findIndex((function(e){return t==e.scmfunctid}));-1!=a&&(e.formdata.scmfunctnames+=e.scmFunctData[a].scmfunctname+",",e.formdata.scmfunctids+=e.scmFunctData[a].scmfunctid+",")}));var a=/,$/gi;this.formdata.scmfunctnames=this.formdata.scmfunctnames.replace(a,""),this.formdata.scmfunctids=this.formdata.scmfunctids.replace(a,"")},getUserInfo:function(){},submitForm:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return console.log("error submit!!"),!1;console.log("可保存"),e.saveForm()}))},saveForm:function(){var t=this;console.log(this.formdata),0==this.idx?(this.formdata.userpassword="123456",v["a"].post("/system/SYSM11B1/create",JSON.stringify(this.formdata)).then((function(e){200==e.data.code?(t.$message.success("保存成功"),t.$emit("bindData"),t.bindData()):t.$message.warning("保存失败")}))):v["a"].post("/system/SYSM11B1/update",JSON.stringify(this.formdata)).then((function(e){200==e.data.code?(t.$message.success("保存成功"),t.$emit("bindData"),t.bindData()):t.$message.warning("保存失败")}))},closeForm:function(){this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){S.delete(t).then((function(t){200==t.code&&e.$message("删除成功"),e.$emit("compForm")})).catch((function(){e.$message("删除失败")}))})).catch((function(){}))},cleValidate:function(t){this.$refs.formdata.clearValidate(t)},initPassword:function(){var t=this;console.log(this.formdata),v["a"].post("/system/SYSM11B1/initPassword?key="+this.formdata.userid).then((function(e){200==e.data.code?(t.$message.success("密码初始化成功"),t.bindData()):t.$message.warning(e.data.msg||"密码初始化失败")}))}}},k=_,F=(a("270e"),Object(h["a"])(k,w,y,!1,null,"7b12ea3e",null)),$=F.exports,C=a("0521"),D={formcode:"SYSM01B9List",item:[{itemcode:"username",itemname:"用户名",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:1,overflow:1,aligntype:"center",datasheet:"PiScmUser.username"},{itemcode:"realname",itemname:"姓名",minwidth:"100",displaymark:1,overflow:1,datasheet:"PiScmUser.realname"},{itemcode:"mobile",itemname:"手机",minwidth:"100",displaymark:1,overflow:1,datasheet:"PiScmUser.mobile"},{itemcode:"email",itemname:"邮箱",minwidth:"100",displaymark:1,overflow:1,datasheet:"PiScmUser.email"},{itemcode:"remark",itemname:"备注",minwidth:"80",displaymark:1,overflow:1,datasheet:"PiScmUser.remark"},{itemcode:"lister",itemname:"制表",minwidth:"60",displaymark:1,overflow:1,datasheet:"PiScmUser.lister"},{itemcode:"createdate",itemname:"创建时间",minwidth:"80",displaymark:1,overflow:1,datasheet:"PiScmUser.createdate"}]},P=a("4363"),O=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.closeForm(e)}}},[t._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"用户账号",prop:"username"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{readonly:!0,size:"small"},model:{value:t.formdata.username,callback:function(e){t.$set(t.formdata,"username",e)},expression:"formdata.username"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"中文名",prop:"realname"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{readonly:!0,size:"small"},model:{value:t.formdata.realname,callback:function(e){t.$set(t.formdata,"realname",e)},expression:"formdata.realname"}})],1)],1)],1)],1),a("el-divider")],1),a("div",{staticClass:"form-body form f-1"},[a("elitem",{key:(new Date).getTime(),ref:"elitem",staticStyle:{width:"100%"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,idx:t.idx}})],1),a("el-form",{staticClass:"form",attrs:{"label-width":"80px"}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"创建"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.modifydate)))])])],1)],1)],1)],1)])])},V=[],z=(a("4d90"),a("25f0"),a("99af"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[a("div",{staticStyle:{display:"flex","justify-content":"space-between"}},[a("el-row",[a("el-button-group",[a("el-button",{attrs:{type:"primary",size:"mini"},nativeOn:{click:function(e){t.UserFormVisible=!0}}},[a("i",{staticClass:"el-icon-circle-plus-outline"}),t._v(" 添 加")]),a("el-button",{attrs:{type:"danger",size:"mini",disabled:0==t.multipleSelection.length},nativeOn:{click:function(e){return t.delItem()}}},[a("i",{staticClass:"el-icon-delete"}),t._v("批 量 删 除")])],1)],1),a("div",{staticStyle:{"margin-right":"10px",position:"relative"}},[a("el-button",{staticStyle:{"font-weight":"bold"},attrs:{size:"mini",icon:"el-icon-refresh-right"},nativeOn:{click:function(e){return t.bindData()}}})],1)],1),a("div",{staticClass:"table-container f-1 table-position"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"multipleTable",staticClass:"tb-edit tableBox",staticStyle:{overflow:"auto"},attrs:{"show-summary":"",data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"",size:"small","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"4px 0px 4px 0px"},"cell-style":{padding:"6px 0px"},"row-style":{height:"20px"}},on:{"selection-change":t.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"40"}}),a("el-table-column",{attrs:{label:"名称",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.name))])]}}])})],1)],1),t.UserFormVisible?a("el-dialog",{attrs:{title:"用户信息","append-to-body":!0,visible:t.UserFormVisible,width:"800px","close-on-press-escape":!1,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.UserFormVisible=e}}},[a("div",{staticClass:"dialog-body"},[a("div",{staticClass:"search"},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"280px"},attrs:{placeholder:"请输入手机号或邮箱","prefix-icon":"el-icon-search",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnSearch(e)}},model:{value:t.searchVal,callback:function(e){t.searchVal=e},expression:"searchVal"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"small"},on:{click:t.btnSearch},slot:"append"},[t._v("搜索 ")])],1),a("el-table",{staticStyle:{width:"100%","margin-top":"10px"},attrs:{data:t.searchTable,height:"100px",border:"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"4px 0px 4px 0px"}}},[a("el-table-column",{attrs:{type:"selection",width:"40"}}),a("el-table-column",{attrs:{prop:"realname",label:"名称",width:"180",align:"center"}}),a("el-table-column",{attrs:{prop:"mobile",label:"手机",align:"center"}}),a("el-table-column",{attrs:{prop:"email",label:"邮箱",align:"center"}})],1)],1)]),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{size:"small",type:"primary"},nativeOn:{click:function(e){return t.submitadd()}}},[t._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.UserFormVisible=!1}}},[t._v("取 消")])],1)]):t._e(),a("el-dialog",{attrs:{title:"用户信息",visible:t.loginFormVisible,"close-on-press-escape":!1,"close-on-click-modal":!1,"destroy-on-close":!0,width:"400px","append-to-body":!0},on:{"update:visible":function(e){t.loginFormVisible=e}}},[a("div",[a("el-form",{ref:"loginForm",staticClass:"custInfo",attrs:{model:t.loginForm,"label-width":"80px","auto-complete":"on"}},[a("el-form-item",{attrs:{label:"账号",prop:"username"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入手机或邮箱",clearable:""},model:{value:t.loginForm.username,callback:function(e){t.$set(t.loginForm,"username",e)},expression:"loginForm.username"}})],1),a("el-form-item",{attrs:{label:"姓名",prop:"realname"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入姓名",clearable:""},model:{value:t.loginForm.realname,callback:function(e){t.$set(t.loginForm,"realname",e)},expression:"loginForm.realname"}})],1),a("el-form-item",{attrs:{label:"手机"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入手机",clearable:""},model:{value:t.loginForm.mobile,callback:function(e){t.$set(t.loginForm,"mobile",e)},expression:"loginForm.mobile"}})],1),a("el-form-item",{attrs:{label:"邮箱"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入邮箱",clearable:""},model:{value:t.loginForm.email,callback:function(e){t.$set(t.loginForm,"email",e)},expression:"loginForm.email"}})],1)],1)],1),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary"},on:{click:function(e){return t.submitLoginForm()}}},[t._v("确 定")]),a("el-button",{on:{click:function(e){t.loginFormVisible=!1}}},[t._v("取 消")])],1)])],1)}),N=[],T=(a("fb6a"),a("b0c0"),{name:"Elitem",components:{},props:["formdata","lstitem","idx"],data:function(){return{title:"服务-角色",formLabelWidth:"100px",listLoading:!1,UserFormVisible:!1,lst:[],multipleSelection:[],multi:0,searchVal:"",searchTable:[],selectList:[],loginFormVisible:!1,loginForm:{username:"",realname:"",mobile:"",email:"",userpassword:"123456"}}},watch:{},created:function(){this.lst=[]},mounted:function(){this.bindData()},methods:{bindData:function(){if(this.formdata.groupids){this.formdata.groupids=this.formdata.groupids.slice(0,this.formdata.groupids.length-1),this.formdata.groupnames=this.formdata.groupnames.slice(0,this.formdata.groupnames.length-1);for(var t=this.formdata.groupids.split(";"),e=this.formdata.groupnames.split(";"),a=0;a<t.length;a++){var i={name:e[a],value:t[a]};this.lst.push(i)}}},handleSelectionChange:function(t){this.multipleSelection=t},delItem:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.deleteRows()})).catch((function(){}))},deleteRows:function(t,e){var a=this;return Object(o["a"])(Object(s["a"])().mark((function t(){var e,i,n;return Object(s["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(a,e=a.multipleSelection,e){for(i=0;i<e.length;i++)n=e[i],-1!=a.formdata.groupids.indexOf(n.value)&&a.formdata.groupids.replace(n.value+";",""),-1!=a.formdata.groupnames.indexOf(n.name)&&a.formdata.groupnames.replace(n.name+";","");v["a"].post("/system/SYSM11B1/update",JSON.stringify(a.formdata)).then((function(t){200==t.data.code?(a.$message.success("用户关联成功"),a.UserFormVisible=!1):a.$message.warning("用户关联失败")}))}a.$refs.multipleTable.clearSelection();case 4:case"end":return t.stop()}}),t)})))()},btnSearch:function(){var t=this;v["a"].get("/system/SYSM11B1/getEntityByUserName?username="+this.searchVal).then((function(e){200==e.data.code&&(e.data.data?(t.searchTable=[],t.searchTable.push(e.data.data)):t.$confirm("未查询到该用户，是否创建?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.loginFormVisible=!0})).catch((function(){})))}))},submitadd:function(){var t=this;this.formdata.groupids+=this.searchTable[0].userid+";",this.formdata.groupnames+=this.searchTable[0].username+";",v["a"].post("/system/SYSM11B1/update",JSON.stringify(this.formdata)).then((function(e){200==e.data.code?(t.$message.success("用户关联成功"),t.UserFormVisible=!1):t.$message.warning("用户关联失败")}))},submitLoginForm:function(){var t=this,e={lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,username:this.loginForm.username,realname:this.loginForm.realname,mobile:this.loginForm.mobile,email:this.loginForm.email,userpassword:"123456"};v["a"].post("/system/SYSM11B1/create",JSON.stringify(e)).then((function(e){200==e.data.code?(t.loginFormVisible=!1,t.$message.success("用户创建成功"),t.searchTable=[],t.searchTable.push(e.data.data)):t.$message.warning(e.data.msg||"用户创建失败")}))}}}),L=T,B=(a("e807"),Object(h["a"])(L,z,N,!1,null,"39efbe5a",null)),j=B.exports,M={name:"Formedit",components:{elitem:j},props:["idx","drawdata"],data:function(){return{formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,item:[]},formLabelWidth:"100px"}},computed:{formcontainHeight:function(){return window.innerHeight-50+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindData()},methods:{bindData:function(){this.listLoading=!0,0!=this.idx&&(this.formdata=this.drawdata)},submitForm:function(t){this.$refs[t].validate((function(t){if(!t)return console.log("error submit!!"),!1}))},closeForm:function(){this.$emit("drawclose")},cleValidate:function(t){this.$refs.formdata.clearValidate(t)}},filters:{dateFormat:function(t){if(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),n=e.getDate().toString().padStart(2,"0"),s=e.getHours().toString().padStart(2,"0"),r=e.getMinutes().toString().padStart(2,"0"),o=e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(n," ").concat(s,":").concat(r,":").concat(o)}}}},I=M,q=(a("082f"),Object(h["a"])(I,O,V,!1,null,"e4e2834e",null)),H=q.exports,R=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.closeForm(e)}}},[t._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"用户账号",prop:"username"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{readonly:!0,size:"small"},model:{value:t.formdata.username,callback:function(e){t.$set(t.formdata,"username",e)},expression:"formdata.username"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"中文名",prop:"realname"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{readonly:!0,size:"small"},model:{value:t.formdata.realname,callback:function(e){t.$set(t.formdata,"realname",e)},expression:"formdata.realname"}})],1)],1)],1)],1),a("el-divider")],1),a("div",{staticClass:"form-body form f-1"},[a("elitem",{key:(new Date).getTime(),ref:"elitem",staticStyle:{width:"100%"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,idx:t.idx}})],1),a("el-form",{staticClass:"form",attrs:{"label-width":"80px"}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"创建"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.modifydate)))])])],1)],1)],1)],1)])])},Y=[],J=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[a("div",[a("el-row",[a("el-button-group",[a("el-button",{attrs:{type:"primary",size:"mini"},nativeOn:{click:function(e){return t.getselPwWork(1)}}},[a("i",{staticClass:"el-icon-circle-plus-outline"}),t._v(" 添 加")]),a("el-button",{attrs:{type:"danger",size:"mini",disabled:0==t.multipleSelection.length},nativeOn:{click:function(e){return t.delItem()}}},[a("i",{staticClass:"el-icon-delete"}),t._v("批 量 删 除")])],1)],1)],1),a("div",{staticClass:"table-container f-1 table-position"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"multipleTable",staticClass:"tb-edit tableBox",staticStyle:{overflow:"auto"},attrs:{"show-summary":"",data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"",size:"small","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"4px 0px 4px 0px"},"cell-style":{padding:"6px 0px"},"row-style":{height:"20px"}},on:{"selection-change":t.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"40"}}),a("el-table-column",{attrs:{label:"名称",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.scmfunctname))])]}}])}),a("el-table-column",{attrs:{label:"编码",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.scmfunctcode))])]}}])}),a("el-table-column",{attrs:{label:"描述",align:"center",prop:"functionname","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.description))])]}}])})],1)],1),t.PwProcessFormVisible?a("el-dialog",{attrs:{title:"SCM服务","append-to-body":!0,visible:t.PwProcessFormVisible,width:"800px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.PwProcessFormVisible=e}}},[a("selScmFun",{ref:"selScmFun",attrs:{multi:1}}),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.PwProcessFormVisible=!1}}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.submitadd()}}},[t._v("确 定")])],1)],1):t._e()],1)},E=[],U=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),a("div",[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"350px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},"row-class-name":t.rowIndex},on:{"row-click":t.rowClick}},[1==t.multi?a("el-table-column",{attrs:{type:"selection",width:"40"}}):a("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(a){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),a("el-table-column",{attrs:{label:"名称",align:"center","min-width":"100px","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.scmfunctname))])]}}])}),a("el-table-column",{attrs:{label:"编码",align:"center","min-width":"80px","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.scmfunctcode))])]}}])}),a("el-table-column",{attrs:{label:"主服务",align:"center",prop:"functionname","min-width":"60"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.functionname))])]}}])}),a("el-table-column",{attrs:{label:"描述",align:"center",prop:"functionname","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.description))])]}}])})],1)],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},W=[],A={components:{Pagination:g["a"]},props:["multi"],data:function(){return{title:"SCM服务",listLoading:!0,lst:[],strfilter:" ",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},created:function(){this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;this.listLoading=!0,v["a"].post("/system/SYSM11B2/getPageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={scmfunctname:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},rowIndex:function(t){var e=t.row,a=t.rowIndex;e.row_index=a},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)}},filters:{dateFormat:function(t){if(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),n=e.getDate().toString().padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(n)}}}},G=A,K=(a("9f03"),Object(h["a"])(G,U,W,!1,null,"49fc5bfe",null)),Z=K.exports,Q={name:"Elitem",components:{selScmFun:Z},props:["formdata","lstitem","idx"],data:function(){return{title:"用户-服务",formLabelWidth:"100px",listLoading:!1,PwProcessFormVisible:!1,lst:[],multi:0,multipleSelection:[]}},watch:{},created:function(){this.lst=[],this.bindData()},methods:{bindData:function(){var t=this;console.log("elitem",this.idx);var e={PageNum:1,PageSize:20,OrderType:1,SearchType:1};v["a"].post("/system/SYSM11B3/getPageList",JSON.stringify(e)).then((function(e){console.log("查看编辑sss",e),200==e.data.code&&(t.lst=e.data.data.list)}))},getselPwWork:function(t){this.PwProcessFormVisible=!0,this.multi=t},submitadd:function(t){var e=this;return Object(o["a"])(Object(s["a"])().mark((function t(){var a,i,n,r,o;return Object(s["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:for(a=e,console.log(e.$refs.selScmFun.$refs.selectVal.selection),i=e.$refs.selScmFun.$refs.selectVal.selection,n=[],r=0;r<i.length;r++)o=new Promise((function(t,e){var a={scmfunctid:i[r].scmfunctid,scmfunctcode:i[r].scmfunctcode,scmfunctname:i[r].scmfunctname,userid:i[r].userid,username:i[r].username,realname:i[r].realname};v["a"].post("/system/SYSM11B3/create",JSON.stringify(a)).then((function(a){200==a.data.code?t("保存成功"):e("保存失败")})).catch((function(t){e("保存失败")}))})),n.push(o);return t.next=7,Promise.all(n).then((function(t){a.$message.success("保存成功")})).catch((function(t){a.$message.warning("保存失败")})).finally((function(){a.bindData()}));case 7:e.PwProcessFormVisible=!1;case 8:case"end":return t.stop()}}),t)})))()},handleSelectionChange:function(t){this.multipleSelection=t},delItem:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.deleteRows()})).catch((function(){}))},deleteRows:function(t,e){var a=this;return Object(o["a"])(Object(s["a"])().mark((function t(){var e,i,n,o,l,c;return Object(s["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e=a,i=a.multipleSelection,console.log("val",i),!i){t.next=23;break}n=[],o=Object(r["a"])(i),t.prev=6,c=Object(s["a"])().mark((function t(){var e,a;return Object(s["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e=l.value,a=new Promise((function(t,a){v["a"].get("/system/SYSM11B3/delete?key=".concat(e.id)).then((function(e){200==e.code?t("删除成功"):a("删除失败")})).catch((function(t){a("删除失败")}))})),n.push(a);case 3:case"end":return t.stop()}}),t)})),o.s();case 9:if((l=o.n()).done){t.next=13;break}return t.delegateYield(c(),"t0",11);case 11:t.next=9;break;case 13:t.next=18;break;case 15:t.prev=15,t.t1=t["catch"](6),o.e(t.t1);case 18:return t.prev=18,o.f(),t.finish(18);case 21:return t.next=23,Promise.all(n).then((function(t){e.$message.success("删除成功"),e.bindData()})).catch((function(t){e.$message.warning("删除失败"),e.bindData()}));case 23:a.$refs.multipleTable.clearSelection(),a.selected=!1;case 25:case"end":return t.stop()}}),t,null,[[6,15,18,21]])})))()}},filters:{dateFormat:function(t){if(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),n=e.getDate().toString().padStart(2,"0"),s=e.getHours().toString().padStart(2,"0"),r=e.getMinutes().toString().padStart(2,"0"),o=e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(n," ").concat(s,":").concat(r,":").concat(o)}}}},X=Q,tt=(a("e6e6"),Object(h["a"])(X,J,E,!1,null,"52af7888",null)),et=tt.exports,at={name:"Formedit",components:{elitem:et},props:["idx","drawdata"],data:function(){return{formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,item:[]},formLabelWidth:"100px"}},computed:{formcontainHeight:function(){return window.innerHeight-40+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindData()},methods:{bindData:function(){this.listLoading=!0,0!=this.idx&&(this.formdata=this.drawdata)},submitForm:function(t){this.$refs[t].validate((function(t){if(!t)return console.log("error submit!!"),!1}))},closeForm:function(){this.$emit("drawclose")},cleValidate:function(t){this.$refs.formdata.clearValidate(t)}},filters:{dateFormat:function(t){if(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),n=e.getDate().toString().padStart(2,"0"),s=e.getHours().toString().padStart(2,"0"),r=e.getMinutes().toString().padStart(2,"0"),o=e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(n," ").concat(s,":").concat(r,":").concat(o)}}}},it=at,nt=(a("774a"),Object(h["a"])(it,R,Y,!1,null,"c4e46124",null)),st=nt.exports,rt={components:{listheader:b,formedit:$,Pagination:g["a"],helpmodel:C["a"],scene:P["a"],roles:H,funs:st},data:function(){var t=this;return{title:"SCM用户管理",idx:0,listLoading:!1,lst:[],formvisible:!1,total:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},multi:0,tableForm:D,showHelp:!1,selectList:[],rolesVisible:!1,funsVisible:!1,drawdata:{},columnHidden:[],columnWidthResizeOption:{enable:!0,minWidth:50,sizeChange:function(e){var a=e.column,i=e.differWidth,n=e.columnWidth;t.columnResizeInfo.column=a,t.columnResizeInfo.differWidth=i,t.columnResizeInfo.columnWidth=n}},columnResizeInfo:{column:"",differWidth:"",columnWidth:""},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var a=e.startRowIndex;t.rowScroll=a}},checkboxOption:{selectedRowChange:function(e){e.row,e.isSelected;var a=e.selectedRowKeys;if(t.selectList=[],0!=a.length)for(var i=0;i<a.length;i++)t.selectList.push({id:a[i]})},selectedAllChange:function(e){var a=e.isSelected;e.selectedRowKeys;t.selectList=a?t.lst:[]}},footerData:[],customData:[],sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var a=this.tableForm.item[e];a.displaymark&&(t+=Number(a.minwidth))}}return t}},mounted:function(){this.bindData(),this.getColumn()},methods:{GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;this.listLoading=!0,this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1)),v["a"].post("/system/SYSM11B1/getPageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code?(t.lst=e.data.data.list,t.total=e.data.data.total):t.$message.warning(e.data.msg||"请求失败"),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},getColumn:function(){var t=this;v["a"].get("/S16M87B1/getBillEntityByCode?code=SYSM11B1List").then((function(e){if(200==e.data.code){if(null==e.data.data)return t.tableForm=D,void t.initTable(t.tableForm);t.tableForm=e.data.data,t.initTable(t.tableForm)}})).catch((function(e){t.$message.error("请求出错")}))},initTable:function(t){var e=this,a=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,i){var n={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(a,i){var n=a.row;a.column,a.rowIndex;if("createdate"==t.itemcode)return e.$options.filters.dateFormat(n[t.itemcode]);if("username"==t.itemcode){var s=i("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(n.userid)}}},[n[t.itemcode]?n[t.itemcode]:"单据编码"]);return s}if("operate"==t.itemcode){s=i("div",[i("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.relevance(n)}}},["关联用户"])]);return s}return n[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),a.push(n)})),a.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,a){t.row,t.column;var i=t.rowIndex;return i+e.rowScroll+1}}),a.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),this.customData=a},btnHelp:function(){this.showHelp=!this.showHelp,this.showHelp&&this.$refs.helpmodel.bindData()},relevance:function(t){this.idx=t.userid,this.drawdata=t,this.rolesVisible=!0},relevanceFun:function(t){this.idx=t.userid,this.drawdata=t,this.funsVisible=!0},allDelete:function(){var t=this;0!=this.selectList.length?this.$confirm("此操作将永久删除记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.deleteRows()})).catch((function(){})):this.$message.warning("请先选择货品")},deleteRows:function(t,e){var a=this;return Object(o["a"])(Object(s["a"])().mark((function t(){var e,i,n,o,l,c;return Object(s["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e=a,i=a.selectList,!i){t.next=22;break}n=[],o=Object(r["a"])(i),t.prev=5,c=Object(s["a"])().mark((function t(){var e,a;return Object(s["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e=l.value,a=new Promise((function(t,a){v["a"].get("/system/SYSM11B1/delete?key"+e.id).then((function(e){200==e.code?0==e.data?a("删除失败"):t("删除成功"):a("删除失败")})).catch((function(t){a("删除失败")}))})),n.push(a);case 3:case"end":return t.stop()}}),t)})),o.s();case 8:if((l=o.n()).done){t.next=12;break}return t.delegateYield(c(),"t0",10);case 10:t.next=8;break;case 12:t.next=17;break;case 14:t.prev=14,t.t1=t["catch"](5),o.e(t.t1);case 17:return t.prev=17,o.f(),t.finish(17);case 20:return t.next=22,Promise.all(n).then((function(t){e.$message.success("删除成功"),a.selectList=[]})).catch((function(t){e.$message.warning(t)})).finally((function(){e.bindData()}));case 22:a.$refs.tableList.clearSelection();case 23:case"end":return t.stop()}}),t,null,[[5,14,17,20]])})))()},search:function(t){var e=this;""!=t?this.$getPageInfo(D.formcode,t).then((function(t){0!=t.length?e.queryParams.searchdata=JSON.stringify(t):e.$delete(e.queryParams,"searchdata"),e.$delete(e.queryParams,"OrderBy"),e.queryParams.SearchType=1,e.queryParams.PageNum=1,e.bindData()})).catch((function(t){e.$message.warning(t||"请求失败")})):(this.$delete(this.queryParams,"searchdata"),this.$delete(this.queryParams,"OrderBy"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData())},advancedSearch:function(t){this.queryParams.scenedata=t,""==t[0].field?this.queryParams.SearchType=1:this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},changeSort:function(t){for(var e in t)if(""!=t[e]){var a={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(a,this.tableForm),this.bindData();break}},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t}}},ot=rt,lt=(a("1b29"),Object(h["a"])(ot,i,n,!1,null,"b52b644a",null));e["default"]=lt.exports},"3e08":function(t,e,a){},"4dd7":function(t,e,a){},7556:function(t,e,a){},7689:function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"sceneContent"},[a("div",{staticClass:"sceneItem"},[a("div",{staticClass:"sceneTitle"},[t._v("查询条件")]),t._l(t.jsondata,(function(e,i){return a("div",{key:i,staticClass:"screen"},[a("div",{staticClass:"screen-item"},[a("el-select",{staticStyle:{width:"180px"},attrs:{placeholder:"请选择要查询的字段",clearable:""},on:{change:function(a){return t.filiterType(a,e)},clear:function(t){e.field="",e.math="",e.value="",e.fieldtype=0}},model:{value:e.field,callback:function(a){t.$set(e,"field",a)},expression:"i.field"}},t._l(t.fieldData,(function(t){return a("el-option",{key:t.fieldcode,attrs:{label:t.fieldname,value:t.fieldcode}})})),1)],1),a("div",{staticClass:"screen-item"},[a("el-select",{staticStyle:{width:"120px"},attrs:{placeholder:"判断条件"},on:{change:function(a){return t.changeMath(a,e)}},model:{value:e.math,callback:function(a){t.$set(e,"math",a)},expression:"i.math"}},[0==e.fieldtype?t._l(t.textType,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})):t._e(),1==e.fieldtype?t._l(t.numType,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})):t._e(),2==e.fieldtype?t._l(t.dateType,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})):t._e(),3==e.fieldtype?t._l(t.boolType,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})):t._e()],2)],1),a("div",{staticClass:"screen-item"},[2==e.fieldtype?a("div",{staticClass:"dateStyle",staticStyle:{width:"300px"}},[a("el-date-picker",{staticStyle:{width:"50%"},attrs:{type:"date","value-format":"yyyy-MM-dd HH:mm:ss",placeholder:"选择日期"},on:{change:function(a){return t.changeDate(a,e,"value")}},model:{value:e.value,callback:function(a){t.$set(e,"value",a)},expression:"i.value"}}),a("span",{staticStyle:{margin:"0 4px"}},[t._v(" - ")]),a("el-date-picker",{staticStyle:{width:"50%"},attrs:{"value-format":"yyyy-MM-dd HH:mm:ss",type:"date",placeholder:"选择日期"},on:{change:function(a){return t.changeDate(a,e,"valueb")}},model:{value:e.valueb,callback:function(a){t.$set(e,"valueb",a)},expression:"i.valueb"}})],1):3==e.fieldtype?a("div"):a("div",{staticStyle:{width:"300px"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"查询条件"},model:{value:e.value,callback:function(a){t.$set(e,"value",a)},expression:"i.value"}})],1)]),a("div",{directives:[{name:"show",rawName:"v-show",value:0!=i,expression:"index != 0"}],on:{click:function(e){return t.deleteBtn(i)}}},[a("i",{staticClass:"el-icon-delete deleteBtn"})])])})),a("div",{staticClass:"addBtn",on:{click:function(e){return t.addBtn()}}},[a("i",{staticClass:"el-icon-plus"}),a("span",[t._v("添加查询条件")])])],2)]),a("div",{staticClass:"footer"},[a("el-button",{staticStyle:{float:"left"},attrs:{type:"primary"},on:{click:t.transScene}},[t._v("转固定场景")]),a("el-button",{attrs:{type:"danger"},on:{click:function(e){return t.clearForm()}}},[t._v("清空")]),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.submitForm()}}},[t._v("确 定")]),a("el-button",{on:{click:function(e){return t.$emit("closedDialog")}}},[t._v("取 消")])],1)])},n=[],s=(a("b64b"),a("a434"),a("c740"),a("e9c4"),a("b775")),r=a("b893"),o={name:"searchForm",props:["code"],data:function(){return{formdata:{enabledmark:1,modulecode:"",scenename:"",scenedata:"",remark:"",rownum:0,lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname},fieldData:[],jsondata:[{field:"",fieldtype:0,math:"",value:""}],textType:[{value:"like",label:"包含"},{value:"not like",label:"不包含"},{value:"equal",label:"等于"},{value:"not equal",label:"不等于"}],numType:[{value:">",label:"大于"},{value:">=",label:"大于等于"},{value:"<",label:"小于"},{value:"=<",label:"小于等于"},{value:"=",label:"等于"},{value:"!=",label:"不等于"}],dateType:[{value:"between",label:"时间"},{value:"today",label:"当天"},{value:"month",label:"本月"}],boolType:[{value:"true",label:"是"},{value:"false",label:"否"}]}},mounted:function(){this.getInit()},methods:{getInit:function(){var t=this;if(this.fieldData=[],s["a"].get("/S16M88B1/getFieldListByCode?code="+this.code).then((function(e){200==e.data.code&&e.data.data&&(t.fieldData=e.data.data)})),this.$store.state.advancedSearch.modulecode==this.code){var e=this.$store.state.advancedSearch;this.jsondata=e.jsondata}else this.jsondata=[{field:"",fieldtype:0,math:"",value:""}]},submitForm:function(){var t={modulecode:this.jsondata[0].field?this.code:"",scenename:"",jsondata:this.jsondata};this.$store.commit("advancedSearch/setSearchData",t),this.$emit("advancedSearch",this.jsondata)},clearForm:function(){this.jsondata=[{field:"",fieldtype:0,math:"",value:""}];var t={modulecode:this.jsondata[0].field?this.code:"",scenename:"",jsondata:this.jsondata};this.$store.commit("advancedSearch/setSearchData",t),this.$emit("advancedSearch",this.jsondata)},addBtn:function(){if(10!=this.jsondata.length){var t={field:"",fieldtype:0,math:"",value:""};this.jsondata.push(t)}else this.$message.warning("筛选条件最多为10条")},deleteBtn:function(t){this.jsondata.splice(t,1)},filiterType:function(t,e){var a=this.fieldData.findIndex((function(e){return e.fieldcode==t}));if(-1!=a){var i=this.fieldData[a];e.fieldtype=i.fieldtype,0==i.fieldtype?e.math=this.textType[0].value:1==i.fieldtype?e.math=this.numType[0].value:2==i.fieldtype&&(e.math=this.dateType[0].value),this.$forceUpdate()}},changeMath:function(t,e){"today"==t?(e["value"]=Object(r["a"])(new Date),e["valueb"]=Object(r["b"])(new Date)):"month"==t&&(e["value"]=this.setMonthDate()[0],e["valueb"]=this.setMonthDate()[1])},changeDate:function(t,e,a){e[a]="valueb"==a?Object(r["a"])(t):Object(r["b"])(t)},setMonthDate:function(){var t=new Date,e=t.getFullYear(),a=t.getMonth()+1;0==a&&(a=12,e-=1),a<10&&(a="0"+a);var i=new Date(e,a,0),n=e+"-"+a+"-01 00:00:00",s=e+"-"+a+"-"+i.getDate()+" 23:59:59";return[n,s]},transScene:function(){var t=this;this.$prompt("请输入场景名称","提示",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:"",inputErrorMessage:"场景名称为必填项",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var a=e.value;t.formdata.scenename=a,t.submitItemUpdate()}))},submitItemUpdate:function(){var t=this;this.formdata.modulecode=this.code,this.formdata.scenedata=JSON.stringify(this.jsondata),s["a"].post("/S16M88B1/create",JSON.stringify(this.formdata)).then((function(e){200==e.data.code&&(t.$message.success("保存成功"),t.$emit("bindData"))}))}}},l=o,c=(a("dcb9"),a("2877")),d=Object(c["a"])(l,i,n,!1,null,"3dd83388",null);e["a"]=d.exports},"774a":function(t,e,a){"use strict";a("4dd7")},"8c44":function(t,e,a){},"9f03":function(t,e,a){"use strict";a("1381")},be23:function(t,e,a){},cbca:function(t,e,a){},ce43:function(t,e,a){"use strict";a("be23")},d975:function(t,e,a){},dcb9:function(t,e,a){"use strict";a("3e08")},e6e6:function(t,e,a){"use strict";a("f70d")},e807:function(t,e,a){"use strict";a("8c44")},f70d:function(t,e,a){}}]);