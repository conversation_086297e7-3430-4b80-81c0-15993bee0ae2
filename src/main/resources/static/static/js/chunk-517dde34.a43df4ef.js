(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-517dde34"],{"27ae":function(e,t,a){(function(a){var i,n;(function(t,a){e.exports=a(t)})("undefined"!==typeof self?self:"undefined"!==typeof window?window:"undefined"!==typeof a?a:this,(function(a){"use strict";a=a||{};var r,o=a.Base64,s="2.6.4",l="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",c=function(e){for(var t={},a=0,i=e.length;a<i;a++)t[e.charAt(a)]=a;return t}(l),d=String.fromCharCode,u=function(e){if(e.length<2){var t=e.charCodeAt(0);return t<128?e:t<2048?d(192|t>>>6)+d(128|63&t):d(224|t>>>12&15)+d(128|t>>>6&63)+d(128|63&t)}t=65536+1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320);return d(240|t>>>18&7)+d(128|t>>>12&63)+d(128|t>>>6&63)+d(128|63&t)},m=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,f=function(e){return e.replace(m,u)},p=function(e){var t=[0,2,1][e.length%3],a=e.charCodeAt(0)<<16|(e.length>1?e.charCodeAt(1):0)<<8|(e.length>2?e.charCodeAt(2):0),i=[l.charAt(a>>>18),l.charAt(a>>>12&63),t>=2?"=":l.charAt(a>>>6&63),t>=1?"=":l.charAt(63&a)];return i.join("")},h=a.btoa&&"function"==typeof a.btoa?function(e){return a.btoa(e)}:function(e){if(e.match(/[^\x00-\xFF]/))throw new RangeError("The string contains invalid characters.");return e.replace(/[\s\S]{1,3}/g,p)},b=function(e){return h(f(String(e)))},g=function(e){return e.replace(/[+\/]/g,(function(e){return"+"==e?"-":"_"})).replace(/=/g,"")},v=function(e,t){return t?g(b(e)):b(e)},y=function(e){return v(e,!0)};a.Uint8Array&&(r=function(e,t){for(var a="",i=0,n=e.length;i<n;i+=3){var r=e[i],o=e[i+1],s=e[i+2],c=r<<16|o<<8|s;a+=l.charAt(c>>>18)+l.charAt(c>>>12&63)+("undefined"!=typeof o?l.charAt(c>>>6&63):"=")+("undefined"!=typeof s?l.charAt(63&c):"=")}return t?g(a):a});var w,x=/[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g,S=function(e){switch(e.length){case 4:var t=(7&e.charCodeAt(0))<<18|(63&e.charCodeAt(1))<<12|(63&e.charCodeAt(2))<<6|63&e.charCodeAt(3),a=t-65536;return d(55296+(a>>>10))+d(56320+(1023&a));case 3:return d((15&e.charCodeAt(0))<<12|(63&e.charCodeAt(1))<<6|63&e.charCodeAt(2));default:return d((31&e.charCodeAt(0))<<6|63&e.charCodeAt(1))}},k=function(e){return e.replace(x,S)},D=function(e){var t=e.length,a=t%4,i=(t>0?c[e.charAt(0)]<<18:0)|(t>1?c[e.charAt(1)]<<12:0)|(t>2?c[e.charAt(2)]<<6:0)|(t>3?c[e.charAt(3)]:0),n=[d(i>>>16),d(i>>>8&255),d(255&i)];return n.length-=[0,0,2,1][a],n.join("")},$=a.atob&&"function"==typeof a.atob?function(e){return a.atob(e)}:function(e){return e.replace(/\S{1,4}/g,D)},C=function(e){return $(String(e).replace(/[^A-Za-z0-9\+\/]/g,""))},B=function(e){return k($(e))},_=function(e){return String(e).replace(/[-_]/g,(function(e){return"-"==e?"+":"/"})).replace(/[^A-Za-z0-9\+\/]/g,"")},F=function(e){return B(_(e))};a.Uint8Array&&(w=function(e){return Uint8Array.from(C(_(e)),(function(e){return e.charCodeAt(0)}))});var j=function(){var e=a.Base64;return a.Base64=o,e};if(a.Base64={VERSION:s,atob:C,btoa:h,fromBase64:F,toBase64:v,utob:f,encode:v,encodeURI:y,btou:k,decode:F,noConflict:j,fromUint8Array:r,toUint8Array:w},"function"===typeof Object.defineProperty){var O=function(e){return{value:e,enumerable:!1,writable:!0,configurable:!0}};a.Base64.extendString=function(){Object.defineProperty(String.prototype,"fromBase64",O((function(){return F(this)}))),Object.defineProperty(String.prototype,"toBase64",O((function(e){return v(this,e)}))),Object.defineProperty(String.prototype,"toBase64URI",O((function(){return v(this,!0)})))}}return a["Meteor"]&&(Base64=a.Base64),e.exports?e.exports.Base64=a.Base64:(i=[],n=function(){return a.Base64}.apply(t,i),void 0===n||(e.exports=n)),{Base64:a.Base64}}))}).call(this,a("c8ba"))},"2c30":function(e,t,a){"use strict";a("a158")},3405:function(e,t,a){"use strict";a("8af3")},"3e08":function(e,t,a){},"5cc6":function(e,t,a){var i=a("74e8");i("Uint8",(function(e){return function(t,a,i){return e(this,t,a,i)}}))},"657d":function(e,t,a){"use strict";a("8f81")},7689:function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticClass:"sceneContent"},[a("div",{staticClass:"sceneItem"},[a("div",{staticClass:"sceneTitle"},[e._v("查询条件")]),e._l(e.jsondata,(function(t,i){return a("div",{key:i,staticClass:"screen"},[a("div",{staticClass:"screen-item"},[a("el-select",{staticStyle:{width:"180px"},attrs:{placeholder:"请选择要查询的字段",clearable:""},on:{change:function(a){return e.filiterType(a,t)},clear:function(e){t.field="",t.math="",t.value="",t.fieldtype=0}},model:{value:t.field,callback:function(a){e.$set(t,"field",a)},expression:"i.field"}},e._l(e.fieldData,(function(e){return a("el-option",{key:e.fieldcode,attrs:{label:e.fieldname,value:e.fieldcode}})})),1)],1),a("div",{staticClass:"screen-item"},[a("el-select",{staticStyle:{width:"120px"},attrs:{placeholder:"判断条件"},on:{change:function(a){return e.changeMath(a,t)}},model:{value:t.math,callback:function(a){e.$set(t,"math",a)},expression:"i.math"}},[0==t.fieldtype?e._l(e.textType,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})):e._e(),1==t.fieldtype?e._l(e.numType,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})):e._e(),2==t.fieldtype?e._l(e.dateType,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})):e._e(),3==t.fieldtype?e._l(e.boolType,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})):e._e()],2)],1),a("div",{staticClass:"screen-item"},[2==t.fieldtype?a("div",{staticClass:"dateStyle",staticStyle:{width:"300px"}},[a("el-date-picker",{staticStyle:{width:"50%"},attrs:{type:"date","value-format":"yyyy-MM-dd HH:mm:ss",placeholder:"选择日期"},on:{change:function(a){return e.changeDate(a,t,"value")}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"i.value"}}),a("span",{staticStyle:{margin:"0 4px"}},[e._v(" - ")]),a("el-date-picker",{staticStyle:{width:"50%"},attrs:{"value-format":"yyyy-MM-dd HH:mm:ss",type:"date",placeholder:"选择日期"},on:{change:function(a){return e.changeDate(a,t,"valueb")}},model:{value:t.valueb,callback:function(a){e.$set(t,"valueb",a)},expression:"i.valueb"}})],1):3==t.fieldtype?a("div"):a("div",{staticStyle:{width:"300px"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"查询条件"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"i.value"}})],1)]),a("div",{directives:[{name:"show",rawName:"v-show",value:0!=i,expression:"index != 0"}],on:{click:function(t){return e.deleteBtn(i)}}},[a("i",{staticClass:"el-icon-delete deleteBtn"})])])})),a("div",{staticClass:"addBtn",on:{click:function(t){return e.addBtn()}}},[a("i",{staticClass:"el-icon-plus"}),a("span",[e._v("添加查询条件")])])],2)]),a("div",{staticClass:"footer"},[a("el-button",{staticStyle:{float:"left"},attrs:{type:"primary"},on:{click:e.transScene}},[e._v("转固定场景")]),a("el-button",{attrs:{type:"danger"},on:{click:function(t){return e.clearForm()}}},[e._v("清空")]),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitForm()}}},[e._v("确 定")]),a("el-button",{on:{click:function(t){return e.$emit("closedDialog")}}},[e._v("取 消")])],1)])},n=[],r=(a("b64b"),a("a434"),a("c740"),a("e9c4"),a("b775")),o=a("b893"),s={name:"searchForm",props:["code"],data:function(){return{formdata:{enabledmark:1,modulecode:"",scenename:"",scenedata:"",remark:"",rownum:0,lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname},fieldData:[],jsondata:[{field:"",fieldtype:0,math:"",value:""}],textType:[{value:"like",label:"包含"},{value:"not like",label:"不包含"},{value:"equal",label:"等于"},{value:"not equal",label:"不等于"}],numType:[{value:">",label:"大于"},{value:">=",label:"大于等于"},{value:"<",label:"小于"},{value:"=<",label:"小于等于"},{value:"=",label:"等于"},{value:"!=",label:"不等于"}],dateType:[{value:"between",label:"时间"},{value:"today",label:"当天"},{value:"month",label:"本月"}],boolType:[{value:"true",label:"是"},{value:"false",label:"否"}]}},mounted:function(){this.getInit()},methods:{getInit:function(){var e=this;if(this.fieldData=[],r["a"].get("/S16M88B1/getFieldListByCode?code="+this.code).then((function(t){200==t.data.code&&t.data.data&&(e.fieldData=t.data.data)})),this.$store.state.advancedSearch.modulecode==this.code){var t=this.$store.state.advancedSearch;this.jsondata=t.jsondata}else this.jsondata=[{field:"",fieldtype:0,math:"",value:""}]},submitForm:function(){var e={modulecode:this.jsondata[0].field?this.code:"",scenename:"",jsondata:this.jsondata};this.$store.commit("advancedSearch/setSearchData",e),this.$emit("advancedSearch",this.jsondata)},clearForm:function(){this.jsondata=[{field:"",fieldtype:0,math:"",value:""}];var e={modulecode:this.jsondata[0].field?this.code:"",scenename:"",jsondata:this.jsondata};this.$store.commit("advancedSearch/setSearchData",e),this.$emit("advancedSearch",this.jsondata)},addBtn:function(){if(10!=this.jsondata.length){var e={field:"",fieldtype:0,math:"",value:""};this.jsondata.push(e)}else this.$message.warning("筛选条件最多为10条")},deleteBtn:function(e){this.jsondata.splice(e,1)},filiterType:function(e,t){var a=this.fieldData.findIndex((function(t){return t.fieldcode==e}));if(-1!=a){var i=this.fieldData[a];t.fieldtype=i.fieldtype,0==i.fieldtype?t.math=this.textType[0].value:1==i.fieldtype?t.math=this.numType[0].value:2==i.fieldtype&&(t.math=this.dateType[0].value),this.$forceUpdate()}},changeMath:function(e,t){"today"==e?(t["value"]=Object(o["a"])(new Date),t["valueb"]=Object(o["b"])(new Date)):"month"==e&&(t["value"]=this.setMonthDate()[0],t["valueb"]=this.setMonthDate()[1])},changeDate:function(e,t,a){t[a]="valueb"==a?Object(o["a"])(e):Object(o["b"])(e)},setMonthDate:function(){var e=new Date,t=e.getFullYear(),a=e.getMonth()+1;0==a&&(a=12,t-=1),a<10&&(a="0"+a);var i=new Date(t,a,0),n=t+"-"+a+"-01 00:00:00",r=t+"-"+a+"-"+i.getDate()+" 23:59:59";return[n,r]},transScene:function(){var e=this;this.$prompt("请输入场景名称","提示",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:"",inputErrorMessage:"场景名称为必填项",closeOnClickModal:!1,inputValidator:function(e){if(!e)return"输入不能为空"}}).then((function(t){var a=t.value;e.formdata.scenename=a,e.submitItemUpdate()}))},submitItemUpdate:function(){var e=this;this.formdata.modulecode=this.code,this.formdata.scenedata=JSON.stringify(this.jsondata),r["a"].post("/S16M88B1/create",JSON.stringify(this.formdata)).then((function(t){200==t.data.code&&(e.$message.success("保存成功"),e.$emit("bindData"))}))}}},l=s,c=(a("dcb9"),a("2877")),d=Object(c["a"])(l,i,n,!1,null,"3dd83388",null);t["a"]=d.exports},"8af3":function(e,t,a){},"8f81":function(e,t,a){},a158:function(e,t,a){},afa7:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.formvisible?a("div",{ref:"formedit",staticClass:"formedit"},[a("formedit",e._g({ref:"formedit",attrs:{idx:e.idx}},{compForm:e.compForm,closeForm:e.closeForm,changeIdx:e.changeIdx,bindData:e.bindData}))],1):e._e(),a("div",{directives:[{name:"show",rawName:"v-show",value:!e.formvisible,expression:"!formvisible"}],staticClass:"index"},[a("listheader",{attrs:{tableForm:e.tableForm},on:{btnSearch:e.search,bindData:e.bindData,btnAdd:function(t){return e.showForm(0)},advancedSearch:e.advancedSearch,btnHelp:e.btnHelp,btnPull:e.btnPull,btnDelete:e.btnDelete,btnClone:e.btnClone}}),a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{directives:[{name:"show",rawName:"v-show",value:e.treeVisble,expression:"treeVisble"}],attrs:{span:4}},[a("div",{staticStyle:{"font-size":"14px",color:"#606266"}},[a("div",{staticClass:"groupTitle"},[a("span",[e._v(e._s(e.treeTitle))])]),a("el-tree",{staticStyle:{"font-size":"14px",height:"80vh",overflow:"auto",width:"100%"},attrs:{data:e.groupData,"node-key":"id","default-expand-all":"","expand-on-click-node":!1},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.node,n=t.data;return a("span",{staticClass:"custom-tree-node"},[a("span",{on:{click:function(){return e.handleNodeClick(n)}}},[e._v(e._s(i.label)+" ")]),a("span",{directives:[{name:"show",rawName:"v-show",value:e.treeEditable,expression:"treeEditable"}]},[a("el-button",{directives:[{name:"show",rawName:"v-show",value:"0"!==n.id,expression:"data.id !== '0'"}],staticStyle:{"padding-left":"5px"},attrs:{type:"text",size:"mini",icon:"el-icon-edit"},on:{click:function(){return e.editTreeNode(n)}}})],1),a("span",{directives:[{name:"show",rawName:"v-show",value:e.treeEditable,expression:"treeEditable"}]},[a("el-button",{staticStyle:{"padding-left":"5px"},attrs:{type:"text",size:"mini",icon:"el-icon-circle-plus-outline"},on:{click:function(){return e.addTreeChild(n)}}})],1),a("span",{directives:[{name:"show",rawName:"v-show",value:e.treeEditable,expression:"treeEditable"}]},[a("el-button",{directives:[{name:"show",rawName:"v-show",value:"0"!==n.id,expression:"data.id !== '0'"}],staticStyle:{"padding-left":"5px"},attrs:{type:"text",size:"mini",icon:"el-icon-delete-solid"},on:{click:function(){return e.delTreeNode(n)}}})],1)])}}])})],1)]),a("el-col",{attrs:{span:e.treeVisble?e.showHelp?16:20:e.showHelp?20:24}},[a("div",[a("el-table",{ref:"tableList",staticStyle:{overflow:"auto"},attrs:{data:e.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},height:e.tableMaxHeight},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"50",align:"center"}}),a("el-table-column",{attrs:{align:"center",label:"ID",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.$index+1)+" ")]}}])}),e._l(e.tableForm.item,(function(t,i){return[!t.displaymark?e._e():a("el-table-column",{key:i,attrs:{prop:t.itemcode,columnKey:t.itemcode,label:t.itemname,align:t.aligntype?t.aligntype:"center","min-width":t.minwidth,"show-overflow-tooltip":!!t.overflow,sortable:!!t.sortable},scopedSlots:e._u([{key:"default",fn:function(i){return["rptname"==t.itemcode?a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.showForm(i.row.id)}}},[e._v(e._s(i.row[t.itemcode]?i.row[t.itemcode]:"报表名称"))]):"createdate"==t.itemcode?a("span",[e._v(e._s(e._f("dateFormat")(i.row[t.itemcode])))]):"tenantid"==t.itemcode?a("div",["default"==i.row.tenantid?a("el-tag",{attrs:{size:"small"}},[e._v("通 用")]):a("el-tag",{attrs:{type:"warning",size:"small"}},[e._v("定 制")])],1):"enabledmark"==t.itemcode?a("div",[i.row[t.itemcode]?a("el-tag",{attrs:{size:"small"}},[e._v("正 常")]):a("el-tag",{attrs:{type:"warning",size:"small"}},[e._v("停 用")])],1):a("span",[e._v(e._s(i.row[t.itemcode]))])]}}],null,!0)})]}))],2),a("div",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.PageNum,limit:e.queryParams.PageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"PageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"PageSize",t)},pagination:e.GetList}}),a("div",{staticStyle:{"margin-right":"40px"}},[a("scene",{ref:"scene",attrs:{code:"SYSM07B3List"},on:{bindData:e.bindData}})],1)],1)],1)]),a("el-col",{attrs:{span:e.showHelp?4:0}},[a("helpmodel",{ref:"helpmodel",attrs:{code:"SYSM07B3"}})],1)],1)],1)],1),e.gropuFormVisible?a("el-dialog",{attrs:{title:"报表中心","append-to-body":!0,visible:e.gropuFormVisible,width:"800px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.gropuFormVisible=t}}},[a("group",{ref:"group",attrs:{idx:e.idx,pid:e.pid},on:{bindData:e.BindTreeData,closeDialog:function(t){e.gropuFormVisible=!1}}})],1):e._e(),a("el-dialog",{staticClass:"reportDialog",attrs:{title:"报表信息","append-to-body":!0,width:"460px",visible:e.cloneVisible,"close-on-click-modal":!1},on:{"update:visible":function(t){e.cloneVisible=t}}},[a("el-form",{ref:"form",attrs:{model:e.cloneForm,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"报表名称",prop:"rptname"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入报表名称"},model:{value:e.cloneForm.rptname,callback:function(t){e.$set(e.cloneForm,"rptname",t)},expression:"cloneForm.rptname"}})],1),a("el-form-item",{attrs:{label:"报表类型",prop:"rpttype"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入报表类型"},model:{value:e.cloneForm.rpttype,callback:function(t){e.$set(e.cloneForm,"rpttype",t)},expression:"cloneForm.rpttype"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitClone}},[e._v("确定")]),a("el-button",{on:{click:function(t){e.cloneVisible=!1}}},[e._v("取 消")])],1)],1)],1)},n=[],r=a("c7eb"),o=a("b85c"),s=a("1da1"),l=a("2909"),c=(a("b64b"),a("e9c4"),a("d81d"),a("99af"),a("d3b7"),a("3ca3"),a("ddb0"),function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{class:e.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[a("div",[a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px","margin-left":"10px"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.btnSearch(t)}},model:{value:e.strfilter,callback:function(t){e.strfilter=t},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:e.btnSearch},slot:"append"},[e._v("搜索 ")])],1),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-plus",plain:"",size:"mini"},on:{click:e.btnAdd}},[e._v(" 添加 ")]),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-download",plain:"",size:"mini"},on:{click:function(t){return e.$emit("btnPull")}}},[e._v(" 拉取 ")]),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"danger",icon:"el-icon-delete",plain:"",size:"mini"},on:{click:function(t){return e.$emit("btnDelete")}}},[e._v(" 删除 ")]),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-document-copy",plain:"",size:"mini"},on:{click:function(t){return e.$emit("btnClone")}}},[e._v(" 克隆 ")])],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-search",title:"高级筛选",type:e.$store.state.advancedSearch.modulecode==e.code?"primary":"default"},on:{click:function(t){return e.openSearchForm()}}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:e.bindData}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(t){return e.openDialog()}}})],1)]),e.setColumsVisible?a("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:e.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(t){e.setColumsVisible=t}}},[a("Setcolums",{ref:"setcolums",attrs:{code:e.code,tableForm:e.tableForm},on:{bindData:e.bindData,closeDialog:function(t){e.setColumsVisible=!1}}})],1):e._e(),a("el-dialog",{attrs:{title:"高级筛选",width:"720px",visible:e.searchVisible,"append-to-body":!0,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(t){e.searchVisible=t}}},[a("searchForm",{ref:"searchForm",attrs:{code:e.code},on:{advancedSearch:e.advancedSearch,closedDialog:function(t){e.searchVisible=!1},bindData:e.bindData}})],1)],1)}),d=[],u=a("8daf"),m=a("7689"),f={name:"Listheader",components:{Setcolums:u["a"],searchForm:m["a"]},props:["tableForm"],data:function(){return{strfilter:"",iShow:!1,formdata:{},code:"SYSM07B3List",setColumsVisible:!1,searchVisible:!1}},methods:{openDialog:function(){this.setColumsVisible=!0},submitUpdate:function(){this.$refs.setcolums.submitUpdate()},advancedSearch:function(e){this.iShow=!1,this.$emit("advancedSearch",e),this.searchVisible=!1},openSearchForm:function(){var e=this;this.searchVisible=!0,setTimeout((function(){e.$refs.searchForm.getInit()}),100)},btnAdd:function(){this.$emit("btnAdd")},bindData:function(){this.$emit("bindData")},btnSearch:function(){this.$emit("btnSearch",this.strfilter)}}},p=f,h=(a("657d"),a("2877")),b=Object(h["a"])(p,c,d,!1,null,"58fc39a2",null),g=b.exports,v=a("333d"),y=a("b775"),w=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("el-button",{attrs:{type:"primary",size:"small"},nativeOn:{click:function(t){return e.submitForm("formdata")}}},[e._v(" 保 存")]),a("el-dropdown",{staticStyle:{margin:"0 10px"},attrs:{trigger:"click",placement:"bottom","hide-on-click":!1}},[a("el-button",{attrs:{size:"small"}},[e._v("操作"),a("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{icon:"el-icon-delete",disabled:!e.idx},nativeOn:{click:function(t){return e.deleteForm(e.idx)}}},[e._v("删 除")]),a("el-dropdown-item",{attrs:{divided:"",icon:"el-icon-upload"},nativeOn:{click:function(t){return e.$refs.upload.click()}}},[e._v("上传报表")]),a("el-dropdown-item",{attrs:{divided:"",icon:"el-icon-download",disabled:!e.reportBase64Data},nativeOn:{click:function(t){return e.downloadJp(t)}}},[e._v("下载网页报表")]),a("el-dropdown-item",{attrs:{icon:"el-icon-download",disabled:!e.grfBase64Data},nativeOn:{click:function(t){return e.downloadGrf(t)}}},[e._v("下载云打印报表")])],1)],1),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(t){return e.closeForm(t)}}},[e._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:e.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:e.formdata,"label-width":e.formLabelWidth,"auto-complete":"on",rules:e.formRules}},[a("p",{staticClass:"formTitle"},[e._v(e._s(e.title))]),a("el-row",[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(t){return e.cleValidate("modulecode")}}},[a("el-form-item",{attrs:{label:"模块编码",prop:"modulecode"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入模块编码",size:"small"},model:{value:e.formdata.modulecode,callback:function(t){e.$set(e.formdata,"modulecode",t)},expression:"formdata.modulecode"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(t){return e.cleValidate("rptname")}}},[a("el-form-item",{attrs:{label:"报表名称",prop:"rptname"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入报表名称",size:"small"},model:{value:e.formdata.rptname,callback:function(t){e.$set(e.formdata,"rptname",t)},expression:"formdata.rptname"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(t){return e.cleValidate("rpttype")}}},[a("el-form-item",{attrs:{label:"报表类型",prop:"rpttype"}},[a("el-select",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请选择报表类型",size:"small"},model:{value:e.formdata.rpttype,callback:function(t){e.$set(e.formdata,"rpttype",t)},expression:"formdata.rpttype"}},[a("el-option",{attrs:{label:"单据",value:"单据"}}),a("el-option",{attrs:{label:"列表",value:"列表"}}),a("el-option",{attrs:{label:"报表",value:"报表"}})],1)],1)],1)]),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"有效性"}},[a("el-checkbox",{attrs:{label:"有效","true-label":1,"false-label":0,size:"mini"},model:{value:e.formdata.enabledmark,callback:function(t){e.$set(e.formdata,"enabledmark",t)},expression:"formdata.enabledmark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"单页行数",prop:"pagerow"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!0,type:"number",min:0,"controls-position":"right",size:"small"},model:{value:e.formdata.pagerow,callback:function(t){e.$set(e.formdata,"pagerow",t)},expression:"formdata.pagerow"}})],1)],1),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(t){return e.cleValidate("gengroupid")}}},[a("el-form-item",{attrs:{label:"报表分组"}},[a("el-cascader",{ref:"cascader",staticStyle:{width:"100%"},attrs:{options:e.groupData,props:e.defaultProps,"show-all-levels":!1,placeholder:"请选择分组名称",size:"small",clearable:""},on:{change:e.handleChange},model:{value:e.formdata.gengroupid,callback:function(t){e.$set(e.formdata,"gengroupid",t)},expression:"formdata.gengroupid"}})],1)],1)]),a("el-col",{attrs:{span:3}},[a("el-form-item",{attrs:{label:"排序"}},[a("el-input-number",{staticClass:"inputNumberContent",attrs:{controls:!0,type:"number",min:0,size:"small","controls-position":"right"},model:{value:e.formdata.rownum,callback:function(t){e.$set(e.formdata,"rownum",t)},expression:"formdata.rownum"}})],1)],1)],1),a("el-row",{staticStyle:{"margin-top":"15px"}},[a("el-col",{attrs:{span:24}},[a("el-tabs",{staticStyle:{"min-height":"380px"},attrs:{"tab-position":"left"}},[a("el-tab-pane",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"网页报表"}},[a("div",{staticClass:"box-card"},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入网页报表数据",size:"small",type:"textarea",autosize:{minRows:18,maxRows:21}},model:{value:e.reportBase64Data,callback:function(t){e.reportBase64Data=t},expression:"reportBase64Data"}})],1)]),a("el-tab-pane",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"云打印报表"}},[a("div",{staticClass:"box-card"},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入云打印报表数据",size:"small",type:"textarea",autosize:{minRows:18,maxRows:21}},model:{value:e.grfBase64Data,callback:function(t){e.grfBase64Data=t},expression:"grfBase64Data"}})],1)])],1)],1)],1),a("el-divider",{attrs:{"content-position":"left"}},[e._v("云打印参数")]),a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"打印机SN",prop:"printersn"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入打印机SN",size:"small",clearable:""},on:{focus:function(e){return e.currentTarget.select()}},model:{value:e.formdata.printersn,callback:function(t){e.$set(e.formdata,"printersn",t)},expression:"formdata.printersn"}})],1)],1),a("el-col",{attrs:{span:3}},[a("el-form-item",{attrs:{label:"长",prop:"paperlength","label-width":"60px"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!0,type:"number",min:0,"controls-position":"right",size:"small",precision:1},on:{focus:function(e){return e.currentTarget.select()}},model:{value:e.formdata.paperlength,callback:function(t){e.$set(e.formdata,"paperlength",t)},expression:"formdata.paperlength"}})],1)],1),a("el-col",{attrs:{span:3}},[a("el-form-item",{attrs:{label:"宽",prop:"paperwidth","label-width":"60px"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!0,type:"number",min:0,"controls-position":"right",size:"small",precision:1},on:{focus:function(e){return e.currentTarget.select()}},model:{value:e.formdata.paperwidth,callback:function(t){e.$set(e.formdata,"paperwidth",t)},expression:"formdata.paperwidth"}})],1)],1)],1),a("el-divider"),a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:e.formdata.remark,callback:function(t){e.$set(e.formdata,"remark",t)},expression:"formdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[e._v(e._s(e.formdata.createby))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[e._v(e._s(e._f("dateFormats")(e.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[e._v(e._s(e.formdata.lister))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[e._v(e._s(e._f("dateFormats")(e.formdata.modifydate)))])])],1)],1)],1)],1)])]),a("div",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}]},[a("input",{ref:"upload",attrs:{type:"file"},on:{change:e.getFile}})])])},x=[];a("2b3d"),a("9861"),a("ac1f"),a("466d"),a("ace4"),a("5cc6"),a("9a8c"),a("a975"),a("735e"),a("c1ac"),a("d139"),a("3a7b"),a("d5d6"),a("82f8"),a("e91f"),a("60bd"),a("5f96"),a("3280"),a("3fcc"),a("ca91"),a("25a1"),a("cd26"),a("3c5d"),a("2954"),a("649e"),a("219c"),a("170b"),a("b39a"),a("72f7"),a("b0c0");const S={add(e){return new Promise((t,a)=>{var i=JSON.stringify(e);y["a"].post("/system/SYSM07B3/create",i).then(e=>{console.log(i,e),200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})},update(e){return new Promise((t,a)=>{var i=JSON.stringify(e);y["a"].post("/system/SYSM07B3/update",i).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})},delete(e){return new Promise((t,a)=>{y["a"].get("/system/SYSM07B3/delete?key="+e).then(e=>{console.log("删除："+e),200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})}};var k=S,D=a("27ae"),$={name:"Formedit",components:{},props:["idx"],data:function(){return{title:"报表中心",formdata:{modulecode:"",rpttype:"",rptname:"",rptdata:"",grfdata:"",pagerow:0,rownum:0,enabledmark:1,remark:"",tempurl:"",printersn:"local",filename:"",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,tenantid:"default",gengroupid:"",paperlength:0,paperwidth:0},reportBase64Data:"",grfBase64Data:"",formRules:{rpttype:[{required:!0,trigger:"blur",message:"报表类型为必填项"}],rptname:[{required:!0,trigger:"blur",message:"报表名称为必填项"}]},formLabelWidth:"100px",multi:0,selVisible:!1,groupData:[],defaultProps:{children:"children",label:"label",value:"id"},filetype:"grf"}},computed:{formcontainHeight:function(){return window.innerHeight-120+"px"}},watch:{idx:function(e,t){this.bindData()}},mounted:function(){this.bindData(),this.BindTreeData()},methods:{bindData:function(){var e=this;return Object(s["a"])(Object(r["a"])().mark((function t(){return Object(r["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(0==e.idx){t.next=4;break}return e.listLoading=!0,t.next=4,y["a"].get("/system/SYSM07B3/getEntity?key=".concat(e.idx)).then((function(t){200==t.data.code&&(e.formdata=t.data.data,e.reportBase64Data=t.data.data.rptdata,e.grfBase64Data=t.data.data.grfdata),e.listLoading=!1})).catch((function(t){e.listLoading=!1}));case 4:case"end":return t.stop()}}),t)})))()},BindTreeData:function(){var e=this;y["a"].get("/system/SYSM07B8/getDefListByModuleCode?Code=SYSM07B3").then((function(t){if(200==t.data.code){var a=t.data.data.map((function(e){return{id:e.id,pid:e.parentid,label:e.groupname}})),i=[{id:"0",pid:"root",label:"报表中心"}],n=[].concat(Object(l["a"])(a),i);e.groupData=e.transData(n,"id","pid","children")}}))},handleChange:function(e){console.log(e),e.length>0?this.formdata.gengroupid=e[e.length-1]:this.formdata.gengroupid="0"},submitForm:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return console.log("error submit!!"),!1;t.saveForm()}))},saveForm:function(){var e=this;this.formdata.rptdata=D["Base64"].encode(this.reportBase64Data),this.formdata.grfdata=D["Base64"].encode(this.grfBase64Data),0==this.idx?k.add(this.formdata).then((function(t){200==t.code?(e.$message.success("保存成功"),e.$emit("changeIdx",t.data.id),e.$emit("bindData"),e.bindData()):e.$message.warning("保存失败")})):k.update(this.formdata).then((function(t){200==t.code?(e.$message.success("保存成功"),e.bindData(),e.$emit("bindData")):e.$message.warning("保存失败")}))},closeForm:function(){this.$emit("closeForm")},deleteForm:function(e){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){k.delete(e).then((function(){t.$message.success("删除成功"),t.$emit("compForm")})).catch((function(){t.$message.warning("删除失败")}))})).catch((function(){}))},downloadGrf:function(){var e=D["Base64"].encode(this.grfBase64Data),t=new Blob([this.dataURLtoBlob(e)],{type:"video/x-msvideo"}),a=document.createElement("a");a.download=this.formdata.rptname+".grf",a.style.display="none",a.href=URL.createObjectURL(t),document.body.appendChild(a),a.click(),document.body.removeChild(a)},downloadJp:function(){var e=D["Base64"].encode(this.reportBase64Data),t=new Blob([this.dataURLtoBlob(e)],{type:"text/xml"}),a=document.createElement("a");a.download=this.formdata.rptname+".jrxml",a.style.display="none",a.href=URL.createObjectURL(t),document.body.appendChild(a),a.click(),document.body.removeChild(a)},dataURLtoBlob:function(e){try{var t=e.split(","),a=t[0].match(/:(.*?);/)[1],i=atob(t[1]),n=i.length,r=new Uint8Array(n);while(n--)r[n]=i.charCodeAt(n);return new Blob([r],{type:a})}catch(d){var o=e.split(","),s=atob(o[0]),l=s.length,c=new Uint8Array(l);while(l--)c[l]=s.charCodeAt(l);return new Blob([c])}},fileToBase64:function(e){var t=new FileReader;t.readAsDataURL(e),t.onload=function(e){var t=e.target.result.split(";base64,")[1];return t}},handleFocus:function(){this.selVisible=!0},handleBlur:function(){this.selVisible=!1},getFile:function(){var e=this,t=this.$refs.upload,a=t.files[0],i=new FileReader;i.readAsDataURL(a),i.onload=function(t){var i=t.target.result.split(";base64,")[1],n=a.name.lastIndexOf("."),r=a.name.substr(n+1);"grf"==r?e.grfBase64Data=D["Base64"].decode(i):"jrxml"==r?e.reportBase64Data=D["Base64"].decode(i):e.$message.warning("请上传正确的jrxml或grf文件")}},cleValidate:function(e){this.$refs.formdata.clearValidate(e)},transData:function(e,t,a,i){for(var n=[],r={},o=t,s=a,l=i,c=0,d=0,u=e.length;c<u;c++)r[e[c][o]]=e[c];for(;d<u;d++){var m=e[d],f=r[m[s]];f?(!f[l]&&(f[l]=[]),f[l].push(m)):n.push(m)}return n}}},C=$,B=(a("f5d3"),Object(h["a"])(C,w,x,!1,null,"3ab51c3e",null)),_=B.exports,F=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticStyle:{width:"100%"}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:e.formdata,"label-width":e.formLabelWidth,rules:e.formRules}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(t){return e.cleValidate("groupname")}}},[a("el-form-item",{attrs:{label:"分组名称",prop:"groupname"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入分组名称",size:"small"},on:{input:e.writeCode},model:{value:e.formdata.groupname,callback:function(t){e.$set(e.formdata,"groupname",t)},expression:"formdata.groupname"}})],1)],1)]),a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(t){return e.cleValidate("groupcode")}}},[a("el-form-item",{attrs:{label:"分组编码",prop:"groupcode"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入分组编码",size:"small"},model:{value:e.formdata.groupcode,callback:function(t){e.$set(e.formdata,"groupcode",t)},expression:"formdata.groupcode"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"排序"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{"controls-position":"right",min:0,size:"small"},model:{value:e.formdata.rownum,callback:function(t){e.$set(e.formdata,"rownum",t)},expression:"formdata.rownum"}})],1)],1)],1)],1)],1),a("el-form",{staticClass:"footFormContent",attrs:{"label-width":e.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:e.formdata.remark,callback:function(t){e.$set(e.formdata,"remark",t)},expression:"formdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[e._v(e._s(e.formdata.lister))])])],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[e._v(e._s(e._f("dateFormat")(e.formdata.modifydate)))])])],1)],1)],1)],1)]),a("div",{staticClass:"button-container flex j-end"},[a("el-button",{attrs:{type:"primary",size:"small"},nativeOn:{click:function(t){return e.submitForm("formdata")}}},[e._v(" 保 存")]),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(t){return e.$emit("closeDialog")}}},[e._v(" 关 闭")])],1)])},j=[],O=a("b0b8"),P={name:"Formedit",props:["idx","pid"],data:function(){return{formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,parentid:"",modulecode:"SYSM07B3",groupcode:"",groupname:"",rownum:0,remark:"",enabledmark:1},formRules:{modulecode:[{required:!0,trigger:"blur",message:"功能编码为必填项"}],groupcode:[{required:!0,trigger:"blur",message:"分组编码为必填项"}],groupname:[{required:!0,trigger:"blur",message:"分组名称为必填项"}]},formLabelWidth:"100px",dialogVisible:!1,delDialogVisible:!1,option:""}},computed:{formcontainHeight:function(){return"300px"}},watch:{idx:function(e,t){console.log("new: %s, old: %s",e,t),this.bindData()}},created:function(){this.bindData()},methods:{bindData:function(){var e=this;0!=this.idx?y["a"].get("/system/SYSM07B8/getEntity?key=".concat(this.idx)).then((function(t){200==t.data.code&&(e.formdata=t.data.data)})):this.formdata.parentid=this.idx},submitForm:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return console.log("error submit!!"),!1;t.saveForm()}))},saveForm:function(){var e=this;this.pid&&(this.formdata.parentid=this.pid),0==this.idx?y["a"].post("/system/SYSM07B8/create",JSON.stringify(this.formdata)).then((function(t){e.$emit("bindData"),e.$emit("closeDialog")})):y["a"].post("/system/SYSM07B8/update",JSON.stringify(this.formdata)).then((function(t){e.$emit("bindData"),e.$emit("closeDialog")}))},closeForm:function(){this.$emit("closeForm"),console.log("关闭窗口")},check:function(){console.log("check")},cleValidate:function(e){this.$refs.formdata.clearValidate(e)},writeCode:function(e){O.setOptions({checkPolyphone:!1,charCase:1}),this.formdata.groupcode=O.getFullChars(e)}}},T=P,z=(a("3405"),Object(h["a"])(T,F,j,!1,null,"0afa8287",null)),N=z.exports,A=a("0521"),V=a("4363"),L={formcode:"SYSM07B3List",item:[{itemcode:"modulecode",itemname:"模块编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"rptname",itemname:"报表名称",minwidth:"80",displaymark:1,overflow:1},{itemcode:"rpttype",itemname:"报表类型",minwidth:"80",displaymark:1,overflow:1},{itemcode:"enabledmark",itemname:"有效性",minwidth:"100",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,datasheet:"App_Workgroup.remark"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1},{itemcode:"createdate",itemname:"创建时间",minwidth:"120",displaymark:1,overflow:1}]},M={components:{Pagination:v["a"],listheader:g,formedit:_,group:N,helpmodel:A["a"],scene:V["a"]},data:function(){return{title:"",lst:[],formvisible:!1,idx:0,searchstr:"",total:0,queryParams:{PageNum:1,PageSize:20,OrderType:0,SearchType:1,OrderBy:"rownum"},pid:"root",treeTitle:"报表中心",gropuFormVisible:!1,treeVisble:!0,groupData:[],defaultProps:{children:"children",label:"label",value:"id"},treeEditable:!1,tableForm:L,showHelp:!1,selectList:[],cloneVisible:!1,cloneForm:{rpttype:"",rptname:""}}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},mounted:function(){this.bindData(),this.getColumn(),this.BindTreeData()},methods:{bindData:function(){var e=this;this.listLoading=!0,this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1)),y["a"].post("/system/SYSM07B3/getPageList",JSON.stringify(this.queryParams)).then((function(t){200==t.data.code&&(e.lst=t.data.data.list,e.total=t.data.data.total),e.listLoading=!1})).catch((function(t){e.listLoading=!1}))},getColumn:function(){var e=this;y["a"].get("/S16M87B1/getBillEntityByCode?code=SYSM07B3List").then((function(t){if(200==t.data.code){if(null==t.data.data)return void(e.tableForm=L);e.tableForm=t.data.data}})).catch((function(t){e.$message.error("请求出错")}))},BindTreeData:function(){var e=this;y["a"].get("/system/SYSM07B8/getListByModuleCode?Code=SYSM07B3").then((function(t){if(200==t.data.code){var a=t.data.data.map((function(e){return{id:e.id,pid:e.parentid,label:e.groupname}})),i=[{id:"0",pid:"root",label:e.treeTitle}],n=[].concat(Object(l["a"])(a),i);e.groupData=e.transData(n,"id","pid","children")}}))},btnPull:function(){var e=this;y["a"].get("/system/SYSM07B3/pullDefault").then((function(t){200==t.data.code?(e.$message.success("拉取默认报表成功"),e.bindData()):e.$message.warning(t.data.msg||"拉取默认报表失败")})).catch((function(t){e.$message.error(t||"请求错误")}))},handleSelectionChange:function(e){this.selectList=e},btnDelete:function(){var e=this;0!=this.selectList.length?this.$confirm("此操作将永久删除记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.deleteRows()})).catch((function(){})):this.$message.warning("请先选择报表")},deleteRows:function(e,t){var a=this;return Object(s["a"])(Object(r["a"])().mark((function e(){var t,i,n,s,l,c;return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=a,i=a.selectList,!i){e.next=22;break}n=[],s=Object(o["a"])(i),e.prev=5,c=Object(r["a"])().mark((function e(){var t,a;return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t=l.value,a=new Promise((function(e,a){k.delete(t.id).then((function(t){200==t.code?0==t.data?a("删除失败"):e("删除成功"):a("删除失败")})).catch((function(e){a("删除失败")}))})),n.push(a);case 3:case"end":return e.stop()}}),e)})),s.s();case 8:if((l=s.n()).done){e.next=12;break}return e.delegateYield(c(),"t0",10);case 10:e.next=8;break;case 12:e.next=17;break;case 14:e.prev=14,e.t1=e["catch"](5),s.e(e.t1);case 17:return e.prev=17,s.f(),e.finish(17);case 20:return e.next=22,Promise.all(n).then((function(e){t.$message.success("删除成功"),a.selectList=[]})).catch((function(e){t.$message.warning(e)})).finally((function(){t.bindData()}));case 22:a.$refs.tableList.clearSelection();case 23:case"end":return e.stop()}}),e,null,[[5,14,17,20]])})))()},btnClone:function(){1==this.selectList.length?(this.cloneVisible=!0,this.cloneForm={rpttype:"",rptname:""}):this.$message.warning("请选择一份要克隆的报表")},submitClone:function(){var e=this,t=Object.assign({},this.selectList[0]);this.$delete(t,"id"),this.$delete(t,"lister"),this.$delete(t,"listerid"),this.$delete(t,"modifydate"),this.$delete(t,"createdate"),this.$delete(t,"tenantid"),this.$delete(t,"tenantname"),this.$delete(t,"createby"),this.$delete(t,"createbyid"),this.$set(t,"rpttype",this.cloneForm.rpttype),this.$set(t,"rptname",this.cloneForm.rptname),t.rptdata=D["Base64"].encode(t.rptdata),t.grfdata=D["Base64"].encode(t.grfdata),k.add(t).then((function(t){200==t.code?(e.$message.success("保存成功"),e.bindData(),e.cloneVisible=!1):e.$message.warning(t.msg||"保存失败")})).catch((function(t){e.$message.warning(t||"保存失败")}))},btnHelp:function(){this.showHelp=!this.showHelp,this.showHelp&&this.$refs.helpmodel.bindData()},GetList:function(e){this.queryParams.PageNum=e.page,this.queryParams.PageSize=e.limit,this.bindData()},search:function(e){""!=e?this.queryParams.SearchPojo={modulecode:e,rptname:e,rpttype:e}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},advancedSearch:function(e){this.queryParams.SearchPojo=e,this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},showForm:function(e){this.idx=e,this.formvisible=!0},closeForm:function(){this.formvisible=!1,this.bindData()},compForm:function(){this.bindData(),this.formvisible=!1,console.log("完成并刷新index")},changeIdx:function(e){this.idx=e},searchByTree:function(e){""!=e?this.queryParams.SearchPojo={gengroupid:e}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},handleNodeClick:function(e){if(0==e.id){var t="";this.searchByTree(t)}else{var a=e.id;this.searchByTree(a)}},editTreeNode:function(e){this.showGroupform(e.id)},addTreeChild:function(e){this.pid=e.id,this.showGroupform(0)},delTreeNode:function(e){var t=this;e.children?this.$message({message:"警告,该节点下存在子节点不能删除",type:"warning"}):this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){console.log("执行删除"),y["a"].get("/system/SYSM07B8/delete?key=".concat(e.id)).then((function(){t.$message.success({message:"删除成功！"}),t.BindTreeData()})).catch((function(){t.$message({showClose:!0,message:"删除失败",type:"warning"})}))})).catch((function(){}))},showGroupform:function(e){this.idx=e,console.log(this.idx),this.gropuFormVisible=!0},transData:function(e,t,a,i){for(var n=[],r={},o=t,s=a,l=i,c=0,d=0,u=e.length;c<u;c++)r[e[c][o]]=e[c];for(;d<u;d++){var m=e[d],f=r[m[s]];f?(!f[l]&&(f[l]=[]),f[l].push(m)):n.push(m)}return n}}},I=M,q=(a("2c30"),Object(h["a"])(I,i,n,!1,null,"1f9b7ae8",null));t["default"]=q.exports},dcb9:function(e,t,a){"use strict";a("3e08")},f5d3:function(e,t,a){"use strict";a("fbcb")},fbcb:function(e,t,a){}}]);