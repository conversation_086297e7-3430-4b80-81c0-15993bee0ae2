(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-commons"],{"0521":function(e,t,a){"use strict";var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"help-warp"},[a("div",{staticClass:"help-header"},[a("span",[e._v(e._s(e.formdata.introname?e.formdata.introname:"功能简介"))])]),a("div",{staticClass:"help-content"},[e.formdata.introcontent?a("div",[a("div",{domProps:{innerHTML:e._s(e.formdata.introcontent)}})]):a("div",{staticClass:"noData",staticStyle:{"font-size":"22px"}},[e._v("暂无内容")])])])},i=[],r=a("b775"),s={props:["code"],data:function(){return{formdata:{}}},created:function(){},methods:{bindData:function(){var e=this;r["a"].get("/system/SYSM06B7/getEntityByCode?key="+this.code).then((function(t){if(200==t.data.code){if(null==t.data.data)return void(e.formdata={introname:"",introcontent:""});e.formdata=t.data.data}else e.$message.warning(t.data.msg||"获取功能简介失败")})).catch((function(t){e.$message.error(t||"请求错误")}))}}},l=s,n=(a("f4e2"),a("2877")),d=Object(n["a"])(l,o,i,!1,null,"41c88702",null);t["a"]=d.exports},4e3:function(e,t,a){},"5bb8":function(e,t,a){},"7c0e":function(e,t,a){"use strict";a("da2a")},"82f0":function(module,__webpack_exports__,__webpack_require__){"use strict";var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("b64b"),core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_0___default=__webpack_require__.n(core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_0__),_views_modules_SYS_SYSM07B1_components_select_vue__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("2f85");__webpack_exports__["a"]={components:{selDictionaries:_views_modules_SYS_SYSM07B1_components_select_vue__WEBPACK_IMPORTED_MODULE_1__["a"]},props:{title:{type:String,default:"单据标题"},formdata:{type:Object,default:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname}},formtemplate:{type:[Array],default:[]},selectform:{type:[Array],default:function(){return[]}}},mounted:function(){},data:function(){return{}},methods:{returnEval:function returnEval(data,itrue){return"string"==typeof data?eval(data):data},clickMethods:function(e,t){if(e){var a={meth:e,param:t};this.$emit("clickMethods",a)}},cleValidate:function(e){this.$refs.formdata.clearValidate(e)}}}},"8daf":function(e,t,a){"use strict";var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticClass:"dialog-body customDialog"},[a("div",{staticClass:"right"},[a("el-button-group",[a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==e.ActiveIndex,icon:"el-icon-top"},on:{click:function(t){return e.getMoveUp()}}},[e._v("上 移")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==e.ActiveIndex,icon:"el-icon-bottom"},on:{click:function(t){return e.getMoveDown()}}},[e._v("下 移")])],1),a("el-button-group",{staticStyle:{"margin-right":"12px"}})],1),a("div",{staticClass:"left"},[a("div",[a("table",{staticClass:"productlTable",attrs:{cellspacing:"0",cellpadding:"0"}},[a("thead",[a("tr",[a("th",{staticClass:"tabTh",staticStyle:{width:"50px"}},[e._v("序号")]),e.showcode?a("th",{staticClass:"tabTh"},[e._v("编码")]):e._e(),a("th",{staticClass:"tabTh",on:{dblclick:function(t){e.showcode=!e.showcode}}},[e._v("名称")]),a("th",{staticClass:"tabTh",staticStyle:{width:"150px"}},[e._v("最小宽度")]),a("th",{staticClass:"tabTh",staticStyle:{width:"100px"}},[e._v("是否固定")]),a("th",{staticClass:"tabTh",staticStyle:{width:"140px"}},[e._v("显示/隐藏")])])]),a("tbody",e._l(e.lst,(function(t,o){return a("tr",{key:o,class:e.ActiveIndex==o?"isActive":"",on:{click:function(t){e.ActiveIndex=o}}},[a("td",{staticStyle:{width:"50px"}},[e._v(e._s(o+1))]),e.showcode?a("td",[e._v(e._s(t.itemcode))]):e._e(),a("td",[e._v(e._s(t.itemname))]),a("td",{staticStyle:{width:"150px"}},[e.ActiveIndex==o?a("el-input",{attrs:{placeholder:"最小宽度",size:"small"},model:{value:t.minwidth,callback:function(a){e.$set(t,"minwidth",a)},expression:"i.minwidth"}}):a("span",[e._v(e._s(t.minwidth))])],1),a("td",{staticStyle:{width:"100px"}},[a("el-checkbox",{attrs:{"true-label":1,"false-label":0},model:{value:t.fixed,callback:function(a){e.$set(t,"fixed",a)},expression:"i.fixed"}})],1),a("td",{staticStyle:{width:"140px"}},[a("el-switch",{staticStyle:{"margin-right":"10px"},attrs:{"inactive-color":"#ff4949","active-value":1,"inactive-value":0},model:{value:t.displaymark,callback:function(a){e.$set(t,"displaymark",a)},expression:"i.displaymark"}})],1)])})),0)])])]),a("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"space-between","margin-top":"20px","margin-right":"12px"}},[a("div",[!this.$store.state.user.userinfo.isadmin?e._e():a("el-button",{attrs:{size:"small"},nativeOn:{click:function(t){return e.submitUpdateTen()}}},[e._v("保存默认格式")]),a("el-button",{attrs:{size:"mini",icon:"el-icon-view"},nativeOn:{click:function(t){return e.getDataTen(!0)}}},[e._v("预览默认列")])],1),a("div",[e.formdata.id&&!e.formdata.defmark?a("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"danger",size:"mini",icon:"el-icon-refresh-left"},on:{click:function(t){return e.refreshLeft()}}},[e._v("重置")]):e._e(),a("el-button",{attrs:{type:"primary",size:"small"},nativeOn:{click:function(t){return e.submitUpdate()}}},[e._v("保 存")]),a("el-button",{attrs:{size:"small"},on:{click:function(t){return e.$emit("closeDialog")}}},[e._v("关 闭")])],1)])]),a("el-dialog",{attrs:{title:"预览默认列","append-to-body":!0,width:"60vw",visible:e.showcolumnvisible,"close-on-click-modal":!1},on:{"update:visible":function(t){e.showcolumnvisible=t}}},[a("div",[a("table",{staticClass:"productlTable",attrs:{cellspacing:"0",cellpadding:"0"}},[a("thead",[a("tr",[a("th",{staticClass:"tabTh",staticStyle:{width:"50px"}},[e._v("序号")]),e.showcode?a("th",{staticClass:"tabTh"},[e._v("编码")]):e._e(),a("th",{staticClass:"tabTh",on:{dblclick:function(t){e.showcode=!e.showcode}}},[e._v("名称")]),a("th",{staticClass:"tabTh",staticStyle:{width:"150px"}},[e._v("最小宽度")]),a("th",{staticClass:"tabTh",staticStyle:{width:"100px"}},[e._v("是否固定")]),a("th",{staticClass:"tabTh",staticStyle:{width:"150px"}},[e._v("显示/隐藏")])])]),a("tbody",e._l(e.defformdata.item,(function(t,o){return a("tr",{key:o,class:e.ActiveIndex==o?"isActive":"",on:{click:function(t){e.ActiveIndex=o}}},[a("td",{staticStyle:{width:"50px"}},[e._v(e._s(o+1))]),e.showcode?a("td",[e._v(e._s(t.itemcode))]):e._e(),a("td",[e._v(e._s(t.itemname))]),a("td",{staticStyle:{width:"150px"}},[a("span",[e._v(e._s(t.minwidth))])]),a("td",{staticStyle:{width:"100px"}},[a("el-checkbox",{attrs:{"true-label":1,"false-label":0},model:{value:t.fixed,callback:function(a){e.$set(t,"fixed",a)},expression:"i.fixed"}})],1),a("td",{staticStyle:{width:"150px"}},[a("el-switch",{staticStyle:{"margin-right":"10px"},attrs:{"inactive-color":"#ff4949","active-value":1,"inactive-value":0},model:{value:t.displaymark,callback:function(a){e.$set(t,"displaymark",a)},expression:"i.displaymark"}})],1)])})),0)])]),a("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"space-between","margin-top":"20px","margin-right":"12px"}},[a("div",[!this.$store.state.user.userinfo.isadmin?e._e():a("el-button",{attrs:{size:"small"},nativeOn:{click:function(t){return e.submitUpdateTen("defformdata")}}},[e._v("保存默认列")])],1),a("div",[a("el-button",{attrs:{type:"primary",size:"small"},nativeOn:{click:function(t){return e.imptDefData()}}},[e._v("导入默认列")]),a("el-button",{attrs:{size:"small"},on:{click:function(t){e.showcolumnvisible=!1}}},[e._v("关闭")])],1)])])],1)},i=[],r=(a("b64b"),a("e9c4"),a("99af"),a("a434"),{name:"SetColums",inject:["reload"],props:["code","tableForm","baseparam"],data:function(){return{title:"列设置",lst:[],formdata:{item:[]},showcolumnvisible:!1,defformdata:{item:[]},showcode:!1,ActiveIndex:-1,itemFormdata:{aligntype:"center",classname:"",defwidth:"",displaymark:1,eventname:"",fixed:0,formatter:"",itemcode:"",itemname:"",minwidth:100,overflow:1,pid:"",remark:"",rownum:0,sortable:0}}},watch:{lst:function(e,t){void 0==e&&(this.lst=[]);for(var a=0;a<e.length;a++)e[a].rownum=a}},created:function(){},mounted:function(){var e=this;this.$nextTick((function(){e.bindData()}))},methods:{bindData:function(){this.formdata=JSON.parse(JSON.stringify(this.tableForm)),1==this.formdata.defmark&&this.$delete(this.formdata,"id"),this.lst=[].concat(this.formdata.item)},refreshLeft:function(){var e=this,t=this;this.$confirm("此操作将初始化表格内容, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var a="/S16M87B1";e.baseparam&&(a=e.baseparam),t.$request.get(a+"/delete?key="+t.formdata.id).then((function(e){200==e.data.code?(t.$message.success(e.data.msg||"重置内容成功"),t.getColumn(t.formdata.formcode),t.$emit("bindData"),t.$emit("closeDialog")):t.$message.warning(e.data.msg||"重置内容失败")}))})).catch((function(){}))},getDataTen:function(e){var t=this;this.defformdata={item:[]};var a="/S16M87B1";this.baseparam&&(a=this.baseparam);var o=a+"/getTenBillEntityByCode?code="+this.tableForm.formcode;this.$request.get(o).then((function(a){if(200==a.data.code){if(null==a.data.data||!a.data.data)return void t.$message.warning(a.data.msg||"未查询到默认列，请联系管理员设置默认列");t.defformdata=a.data.data,e&&(t.showcolumnvisible=!0)}else t.$message.warning(a.data.msg||"获取默认列失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},imptDefData:function(){this.lst=[];for(var e=0;e<this.defformdata.item.length;e++){var t=this.defformdata.item[e];this.$delete(t,"id"),this.$delete(t,"pid"),this.lst.push(t)}this.showcolumnvisible=!1,this.$forceUpdate()},submitUpdateTen:function(e){var t=this,a=Object.assign({},this.formdata);a.item=this.lst,"defformdata"==e&&(a=Object.assign({},this.defformdata),a.item=this.defformdata.item),a.enabledmark=1,a.defmark=1;var o="/S16M87B1";this.baseparam&&(o=this.baseparam);var i=o+"/updateTen";this.$request.post(i,JSON.stringify(a)).then((function(e){200==e.data.code?t.$message.success(e.data.msg||"保存成功"):t.$message.warning(e.data.msg||"保存失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},submitUpdate:function(){var e=this;this.formdata.item=this.lst,this.formdata.enabledmark=1,this.formdata.defmark=0;var t="/S16M87B1";if(this.baseparam&&(t=this.baseparam),this.formdata.id)a=t+"/update";else var a=t+"/create";this.$request.post(a,JSON.stringify(this.formdata)).then((function(t){200==t.data.code?(e.$message.success(t.data.msg||"保存成功"),e.formdata.id=t.data.data.id,e.$forceUpdate(),e.$emit("bindData"),e.$emit("closeDialog")):e.$message.warning(t.data.msg||"保存失败")})).catch((function(t){e.$message.error(t||"请求错误")}))},getColumn:function(e){var t=this,a="/S16M87B1";this.baseparam&&(a=this.baseparam),this.$request.get(a+"/getBillEntityByCode?code="+e).then((function(e){if(200==e.data.code){if(null==e.data.data)return t.formdata.id="",void t.$forceUpdate();t.formdata=e.data.data,t.lst=e.data.data.item,t.$forceUpdate()}})).catch((function(e){t.$message.error("请求出错")}))},cleValidate:function(e){this.$refs.itemFormdata.clearValidate(e)},getMoveUp:function(){if(0!=this.ActiveIndex){var e=this.lst[this.ActiveIndex],t=this.lst[this.ActiveIndex].rownum;this.lst.splice(t,1),this.lst.splice(t-1,0,e),this.ActiveIndex-=1;for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是第一行了！")},getMoveDown:function(){if(this.ActiveIndex!=this.lst.length-1){var e=this.lst[this.ActiveIndex],t=this.lst[this.ActiveIndex].rownum;this.lst.splice(t,1),this.lst.splice(t+1,0,e),this.ActiveIndex+=1;for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是最后一行了！")}}}),s=r,l=(a("a4ad"),a("2877")),n=Object(l["a"])(s,o,i,!1,null,"22577f10",null);t["a"]=n.exports},a4ad:function(e,t,a){"use strict";a("f9bc")},da2a:function(e,t,a){},dcb4:function(e,t,a){"use strict";var o,i,r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.$slots.Header?a("div",[e._t("Header")],2):a("div",{staticStyle:{"min-height":"150px"}},[a("Header",{ref:"formHeader",attrs:{formdata:e.formdata,formtemplate:e.formtemplate.header.content,title:e.formtemplate.header.title,selectform:e.selectform},on:{clickMethods:function(t){return e.$emit("clickMethods",t)},cleValidate:function(t){return e.$emit("cleValidate",t)},getAllGroupName:function(t){return e.$emit("getAllGroupName",t)},getGroupName:function(t){return e.$emit("getGroupName",t)},getSuppGroupName:function(t){return e.$emit("getSuppGroupName",t)},getFactGroupName:function(t){return e.$emit("getFactGroupName",t)},getWorkGroupName:function(t){return e.$emit("getWorkGroupName",t)},getBranGroupName:function(t){return e.$emit("getBranGroupName",t)},getProsGroupName:function(t){return e.$emit("getProsGroupName",t)},getStoreName:e.getStoreName,getProcName:function(t){return e.$emit("getProcName",t)},getRoleProcName:function(t){return e.$emit("getRoleProcName",t)},getGoodsName:function(t){return e.$emit("getGoodsName",t)},getFlawName:function(t){return e.$emit("getFlawName",t)},autoClear:function(t){return e.$emit("autoClear")},autoStoreClear:function(t){return e.$emit("autoStoreClear",t)},autoProcClear:function(t){return e.$emit("autoProcClear")},autoRoleProcClear:function(t){return e.$emit("autoRoleProcClear")},autoFlawClear:function(t){return e.$emit("autoFlawClear")},autoGoodsClear:function(t){return e.$emit("autoGoodsClear")}}})],1),e._t("Item"),e.$slots.Footer?a("div",[e._t("Footer")],2):a("div",[a("Footer",{attrs:{formdata:e.formdata,formtemplate:e.formtemplate.footer.content}})],1)],2)},s=[],l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-form",{ref:"formdata",staticClass:"custInfo",staticStyle:{"margin-top":"20px"},attrs:{model:e.formdata,"label-width":"100px"}},[a("p",{staticClass:"formTitle"},[e._v(e._s(e.title?e.title:"单据标题"))]),e._l(e.formtemplate,(function(t,o){return[a("div",{key:o},["divider"==t.type?a("el-divider",{attrs:{"content-position":t.center?t.center:"left"}},[e._v(e._s(t.label))]):a("el-row",[e._l(t.rowitem,(function(t,o){return[(t.show?e.returnEval(t.show):!t.show)?a("el-col",{key:o,attrs:{span:t.col}},[a("div",{on:{click:function(a){return e.cleValidate(t.code)}}},[a("el-form-item",{attrs:{label:"checkbox"==t.type?"":t.label,prop:t.code,"label-width":t.labelwidth?t.labelwidth:"100px",rules:[{required:!!t.required&&t.required,trigger:"blur",message:t.label+"为必填项"}]}},["input"==t.type?a("el-input",{attrs:{placeholder:"请输入"+t.label,clearable:"",size:t.size?t.size:"small",disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}},[t.iconbtn?[a("i",{directives:[{name:"show",rawName:"v-show",value:e.returnEval(t.iconbtn.show,1),expression:"returnEval(b.iconbtn.show, 1)"}],staticClass:"getNextCode",class:t.iconbtn.icon,attrs:{slot:"suffix"},on:{click:function(a){return e.clickMethods(t.iconbtn.methods,t.iconbtn.param)}},slot:"suffix"})]:e._e()],2):"select"==t.type?a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"+t.label,size:t.size?t.size:"small",disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},on:{change:function(a){return e.clickMethods(t.methods,t.param)}},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}},e._l(-1!=e.selectform.findIndex((function(e){return e.code==t.code}))?e.selectform[e.selectform.findIndex((function(e){return e.code==t.code}))].data:t.options,(function(e,t){return a("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1):"checkbox"==t.type?a("div",[a("el-checkbox",{attrs:{label:t.label,"true-label":1,"false-label":0,disabled:!!t.disabled&&e.returnEval(t.disabled)},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}})],1):"number"==t.type?a("div",[a("el-input-number",{attrs:{controls:!0,type:"number",min:0,"controls-position":"right",size:t.size?t.size:"small",disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}})],1):"date"==t.type?a("div",[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",size:t.size?t.size:"small",clearable:"",placeholder:"选择日期",disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}})],1):"dictionary"==t.type?a("div",[a("el-popover",{ref:t.code+"PropRef",refInFor:!0,attrs:{placement:"bottom",trigger:"click"},on:{show:function(a){e.$refs[t.code+"Ref"][0].bindData()}}},[a("selDictionaries",{ref:t.code+"Ref",refInFor:!0,staticStyle:{width:"200px"},attrs:{multi:0,billcode:t.billcode},on:{singleSel:function(a){e.formdata[t.code]=a.dictvalue,e.$refs[t.code+"PropRef"][0].doClose()},closedic:function(a){e.$refs[t.code+"PropRef"][0].doClose()}}}),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("el-input",{attrs:{placeholder:"请选择"+t.label,clearable:"",size:t.size?t.size:"small",disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}})],1)],1)],1):"textarea"==t.type?a("div",[a("el-input",{attrs:{placeholder:"请输入"+t.label,clearable:"",type:"textarea",size:t.size?t.size:"small",autosize:t.autosize?t.autosize:{minRows:2,maxRows:4},disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}})],1):"text"==t.type?a("div",[a("span",{staticStyle:{"font-size":"18px",color:"#666"}},[e._v(e._s(e.formdata[t.code]))])]):"autocomplete"==t.type?a("div",["customer"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],baseurl:"/S16M03R1/getOnlinePageList",type:"客户",isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"supplier"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],baseurl:"/sale/D01M01B2/getOnlinePageList",type:"供应商",isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getSuppGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"workshop"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],baseurl:"/sale/D01M01B3/getPageList",type:"生产车间",isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getWorkGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"factory"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],baseurl:"/sale/D01M01B4/getPageList",type:"外协厂商",isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getFactGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"branch"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled),baseurl:"/sale/D01M01B5/getPageList",type:"部门"},on:{setRow:function(a){e.$emit("getBranGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"prospects"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled),baseurl:"/sale/D01M01B6/getPageList",type:"潜在客户"},on:{setRow:function(a){e.$emit("getProsGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"group"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getAllGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"store"==t.searchtype?a("div",[a("StoreAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getStoreName",a,t.code),e.cleValidate(t.code)},autoClear:function(a){return e.$emit("autoStoreClear",t.code)}}})],1):"procedure"==t.searchtype?a("div",[a("ProcAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getProcName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoProcClear")}}})],1):"roleproc"==t.searchtype?a("div",[a("RoleProcAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getRoleProcName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoRoleProcClear")}}})],1):"goods"==t.searchtype?a("div",[a("GoodsAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getGoodsName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoGoodsClear")}}})],1):e._e()]):e._e()],1)],1)]):e._e()]}))],2)],1)]}))],2)},n=[],d=a("82f0"),c=d["a"],u=(a("f8be"),a("2877")),m=Object(u["a"])(c,l,n,!1,null,"72086049",null),f=m.exports,p=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{"margin-top":"15px"}},[a("el-form",{ref:"formdata",staticClass:"footFormContent",attrs:{"label-width":"100px"}},[e._l(e.tempData,(function(t,o){return[a("div",{key:o},["divider"==t.type?a("el-divider",{attrs:{"content-position":t.center?t.center:"left"}},[e._v(e._s(t.label))]):"foot"==t.type?a("el-row",[e._l(t.rowitem,(function(t,o){return[a("el-col",{directives:[{name:"show",rawName:"v-show",value:"assessdate"!=t.code||e.formdata.assessor,expression:"b.code == 'assessdate' ? formdata.assessor : true"}],key:o,attrs:{span:t.col}},[a("el-form-item",{attrs:{label:t.label,prop:t.code}},["assessdate"==t.code||"modifydate"==t.code||"createdate"==t.code?a("div",[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata[t.code],expression:"formdata[b.code]"}],staticClass:"el-form-item__label",staticStyle:{"white-space":"nowrap"}},[e._v(e._s(e._f("dateFormats")(e.formdata[t.code])))])]):a("div",[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata[t.code],expression:"formdata[b.code]"}],staticClass:"el-form-item__label"},[e._v(e._s(e.formdata[t.code]))])])])],1)]}))],2):a("el-row",[e._l(t.rowitem,(function(t,o){return[(t.show?e.returnEval(t.show):!t.show)?a("el-col",{key:o,attrs:{span:t.col}},[a("div",{on:{click:function(a){return e.cleValidate(t.code)}}},[a("el-form-item",{attrs:{label:"checkbox"==t.type?"":t.label,prop:t.code}},["input"==t.type?a("el-input",{attrs:{placeholder:"请输入"+t.label,clearable:"",size:t.size?t.size:"small",disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}}):"select"==t.type?a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"+t.label,size:t.size?t.size:"small",disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},on:{change:function(a){return e.clickMethods(t.methods,t.param)}},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}},e._l(t.options,(function(e,t){return a("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1):"checkbox"==t.type?a("div",[a("el-checkbox",{attrs:{label:t.label,"true-label":0,"false-label":1,size:t.size?t.size:"mini",disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},on:{change:function(a){return e.clickMethods(t.methods,t.param)}},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}})],1):"number"==t.type?a("div",[a("el-input-number",{attrs:{controls:!0,type:"number",min:0,"controls-position":"right",size:t.size?t.size:"small",disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},on:{change:function(a){return e.clickMethods(t.methods,t.param)}},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}})],1):"date"==t.type?a("div",[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",size:t.size?t.size:"small",clearable:"",placeholder:"选择日期",disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}})],1):"dictionary"==t.type?a("div",[a("el-popover",{ref:t.code+"PropRef",refInFor:!0,attrs:{placement:"bottom",trigger:"click"},on:{show:function(a){e.$refs[t.code+"Ref"][0].bindData()}}},[a("selDictionaries",{ref:t.code+"Ref",refInFor:!0,staticStyle:{width:"200px"},attrs:{multi:0,billcode:t.billcode},on:{singleSel:function(a){e.formdata[t.code]=a.dictvalue,e.$refs[t.code+"PropRef"][0].doClose()},closedic:function(a){e.$refs[t.code+"PropRef"][0].doClose()}}}),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("el-input",{attrs:{placeholder:"请选择"+t.label,clearable:"",size:t.size?t.size:"small",disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}})],1)],1)],1):"textarea"==t.type?a("div",[a("el-input",{attrs:{placeholder:"请输入"+t.label,clearable:"",type:"textarea",size:t.size?t.size:"small",autosize:{minRows:2,maxRows:4},disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}})],1):"text"==t.type?a("div",[a("span",{staticStyle:{"font-size":"18px",color:"#606266"}},[e._v(e._s(e.formdata[t.code]))])]):"autocomplete"==t.type?a("div",["customer"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],baseurl:"/S16M03R1/getOnlinePageList",type:"客户",isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"supplier"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],baseurl:"/sale/D01M01B2/getOnlinePageList",type:"供应商",isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getSuppGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"workshop"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],baseurl:"/sale/D01M01B3/getPageList",type:"生产车间",isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getWorkGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"factory"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],baseurl:"/sale/D01M01B4/getPageList",type:"外协厂商",isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getFactGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"branch"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled),baseurl:"/sale/D01M01B5/getPageList",type:"部门"},on:{setRow:function(a){e.$emit("getBranGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"prospects"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled),baseurl:"/sale/D01M01B6/getPageList",type:"潜在客户"},on:{setRow:function(a){e.$emit("getProsGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"group"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getAllGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"store"==t.searchtype?a("div",[a("StoreAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(t){return e.$emit("getStoreName",t)},autoClear:function(t){return e.$emit("autoStoreClear")}}})],1):"proc"==t.searchtype?a("div",[a("ProcAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(t){return e.$emit("getProcName",t)},autoClear:function(t){return e.$emit("autoProcClear")}}})],1):"roleproc"==t.searchtype?a("div",[a("RoleProcAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(t){return e.$emit("getRoleProcName",t)},autoClear:function(t){return e.$emit("autoRoleProcClear")}}})],1):"goods"==t.searchtype?a("div",[a("GoodsAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(t){return e.$emit("getGoodsName",t)},autoClear:function(t){return e.$emit("autoGoodsClear")}}})],1):e._e()]):e._e()],1)],1)]):e._e()]}))],2)],1)]}))],2)],1)},b=[],v=a("e08f"),h=v["a"],_=(a("7c0e"),Object(u["a"])(h,p,b,!1,null,"2e208fb5",null)),y=_.exports,g={},w=Object(u["a"])(g,o,i,!1,null,null,null),$=w.exports,C={components:{Header:f,Item:$,Footer:y},props:{formdata:{type:Object},formtemplate:{type:Object},selectform:{type:[Object,Array]}},methods:{getStoreName:function(e,t){this.$emit("getStoreName",e,t)}}},x=C,k=Object(u["a"])(x,r,s,!1,null,null,null);t["a"]=k.exports},e08f:function(module,__webpack_exports__,__webpack_require__){"use strict";var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("b64b"),core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_0___default=__webpack_require__.n(core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_0__),_views_modules_SYS_SYSM07B1_components_select_vue__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("2f85");__webpack_exports__["a"]={components:{selDictionaries:_views_modules_SYS_SYSM07B1_components_select_vue__WEBPACK_IMPORTED_MODULE_1__["a"]},props:{title:{type:String,default:"单据标题"},formdata:{type:Object,default:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname}},formtemplate:{type:[Array],default:function(){return[]}}},data:function(){return{tempData:this.formtemplate}},watch:{formtemplate:function(e,t){console.log("foot",e),this.formtemplate.length?this.tempData=this.formtemplate:this.tempData=[{type:"divider",label:"",center:""},{type:"form",item:[{col:23,type:"input",code:"summary",label:"摘  要"}]},{type:"foot",item:[{col:4,type:"text",code:"createby",label:"创建人"},{col:4,type:"text",code:"createdate",label:"创建日期"},{col:4,type:"text",code:"lister",label:"制表"},{col:4,type:"text",code:"modifydate",label:"修改日期"},{col:4,type:"text",code:"assessor",label:"审核"},{col:4,type:"text",code:"assessdate",label:"审核日期"}]}]}},mounted:function(){},methods:{returnEval:function returnEval(data,itrue){return"string"==typeof data?eval(data):data},cleValidate:function(e){this.$refs.formdata.clearValidate(e)},clickMethods:function(e,t){var a={meth:e,param:t};this.$emit("clickMethods",a)}}}},f4e2:function(e,t,a){"use strict";a("4000")},f8be:function(e,t,a){"use strict";a("5bb8")},f9bc:function(e,t,a){}}]);