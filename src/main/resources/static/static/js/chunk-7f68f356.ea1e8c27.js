(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7f68f356"],{"0893":function(e,t,a){"use strict";a("4f0a")},"4f0a":function(e,t,a){},"8f75":function(e,t,a){"use strict";a("be07")},be07:function(e,t,a){},cbb3:function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",size:"small"},nativeOn:{click:function(t){return e.submitForm("formdata")}}},[e._v(" 保 存")]),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(t){return e.closeForm(t)}}},[e._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:e.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:e.formdata,"label-width":e.formLabelWidth,"auto-complete":"on",rules:e.formRules}},[a("p",{staticClass:"formTitle"},[e._v(e._s(e.title))]),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"上级参数"}},[a("el-cascader",{staticStyle:{width:"100%"},attrs:{options:e.cfgData,props:e.defaultProps,clearable:"","change-on-select":"","show-all-levels":!1,size:"small"},on:{change:e.handleChange},model:{value:e.formdata.parentid,callback:function(t){e.$set(e.formdata,"parentid",t)},expression:"formdata.parentid"}})],1)],1)],1)],1),a("el-divider")],1),a("div",{staticClass:"form-body form f-1"},[a("elitem",{ref:"elitem",staticStyle:{width:"99%"},attrs:{lstitem:e.formdata.item,formdata:e.formdata}})],1),a("el-form",{staticClass:"footFormContent",attrs:{"label-width":e.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入备注",clearable:"",size:"small"},model:{value:e.formdata.remark,callback:function(t){e.$set(e.formdata,"remark",t)},expression:"formdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[e._v(e._s(e.formdata.createby))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[e._v(e._s(e._f("dateFormat")(e.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[e._v(e._s(e.formdata.lister))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[e._v(e._s(e._f("dateFormat")(e.formdata.modifydate)))])])],1)],1)],1)],1)])])},i=[],s=a("c7eb"),o=a("1da1"),n=(a("498a"),a("b64b"),a("e9c4"),a("d3b7"),a("159b"),a("b775")),r=a("e6d0"),c=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{ref:"elitem",staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[a("div",[a("el-row",[a("el-button-group",[a("el-button",{attrs:{type:"primary",size:"mini",disabled:!!e.formdata.assessor},nativeOn:{click:function(t){return e.getselPwProcess(1)}}},[a("i",{staticClass:"el-icon-circle-plus-outline"}),e._v(" 添 加")]),a("el-button",{attrs:{disabled:1!=e.multipleSelection.length,type:"primary",size:"mini"},nativeOn:{click:function(t){return e.getMoveUp()}}},[a("i",{staticClass:"el-icon-top"}),e._v(" 上 移")]),a("el-button",{attrs:{disabled:1!=e.multipleSelection.length,type:"primary",size:"mini"},nativeOn:{click:function(t){return e.getMoveDown()}}},[a("i",{staticClass:"el-icon-bottom"}),e._v(" 下 移")]),a("el-button",{attrs:{disabled:!e.selected,type:"danger",size:"mini"},nativeOn:{click:function(t){return e.delItem()}}},[a("i",{staticClass:"el-icon-delete"}),e._v("删 除")])],1)],1)],1),a("div",{staticClass:"table-container f-1 table-position"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"multipleTable",staticClass:"tb-edit tableBox",staticStyle:{overflow:"auto"},attrs:{data:e.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"",size:"small",height:e.tableHeight,"header-cell-style":{background:"#F3F4F7",color:"#555",padding:"4px 0px 4px 0px"},"cell-style":{padding:"4px 0px"},"row-style":{height:"20px"}},on:{"selection-change":e.handleSelectionChange,"row-click":e.saveRow}},[a("el-table-column",{attrs:{type:"selection",width:"50",align:"center"}}),a("el-table-column",{attrs:{align:"center",label:"ID","min-width":"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.$index+1)+" ")]}}])}),a("el-table-column",{attrs:{label:"参数类型",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isEdit?a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择参数类型",size:"small"},model:{value:e.formdata.cfgtype,callback:function(t){e.$set(e.formdata,"cfgtype",t)},expression:"formdata.cfgtype"}},[a("el-option",{attrs:{label:"系统",value:0}}),a("el-option",{attrs:{label:"模块",value:1}})],1):a("span",[e._v(e._s(0==t.row.cfgtype?"系统":"模块"))])]}}])}),a("el-table-column",{attrs:{label:"参数名称",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isEdit?a("el-input",{attrs:{size:"small",placeholder:"参数名称"},model:{value:t.row.cfgname,callback:function(a){e.$set(t.row,"cfgname",a)},expression:"scope.row.cfgname"}}):a("span",[e._v(e._s(t.row.cfgname))])]}}])}),a("el-table-column",{attrs:{label:"参数键名",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isEdit?a("el-input",{attrs:{size:"small",placeholder:"参数键名"},model:{value:t.row.cfgkey,callback:function(a){e.$set(t.row,"cfgkey",a)},expression:"scope.row.cfgkey"}}):a("span",[e._v(e._s(t.row.cfgkey))])]}}])}),a("el-table-column",{attrs:{label:"参数键值",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isEdit?a("el-input",{attrs:{size:"small",placeholder:"参数键值"},model:{value:t.row.cfgvalue,callback:function(a){e.$set(t.row,"cfgvalue",a)},expression:"scope.row.cfgvalue"}}):a("span",[e._v(e._s(t.row.cfgvalue))])]}}])}),a("el-table-column",{attrs:{label:"状态",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[a("el-checkbox",{attrs:{label:"有效","true-label":1,"false-label":0,size:"mini"},model:{value:t.row.enabledmark,callback:function(a){e.$set(t.row,"enabledmark",a)},expression:"scope.row.enabledmark"}})],1)]}}])}),a("el-table-column",{attrs:{label:"允许删除",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[a("el-checkbox",{attrs:{label:"是","true-label":1,"false-label":0,size:"mini"},model:{value:t.row.allowdelete,callback:function(a){e.$set(t.row,"allowdelete",a)},expression:"scope.row.allowdelete"}})],1)]}}])}),a("el-table-column",{attrs:{label:"可选值",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isEdit?a("el-input",{attrs:{size:"small",placeholder:"可选值"},model:{value:t.row.cfgoption,callback:function(a){e.$set(t.row,"cfgoption",a)},expression:"scope.row.cfgoption"}}):a("span",[e._v(e._s(t.row.cfgoption))])]}}])})],1)],1)])},f=[],d=(a("a434"),a("da92"),{name:"Elitem",components:{},props:["formdata","lstitem"],data:function(){return{title:"系统",formLabelWidth:"100px",listLoading:!1,lst:[],selected:!1,tableHeight:0,selVisible:!1,multipleSelection:[],isEditOk:!0}},watch:{lstitem:function(e,t){this.lst=this.lstitem},lst:function(e,t){void 0==e&&(this.lst=[]);for(var a=0;a<e.length;a++)e[a].rownum=a}},created:function(){this.lst=[]},updated:function(){var e=this;this.$nextTick((function(){e.$refs.multipleTable.doLayout()}))},mounted:function(){this.catchHight()},methods:{getselPwProcess:function(e){this.selPwProcess()},selPwProcess:function(){var e={cfgtype:0,cfgname:"",cfgkey:"",cfgvalue:"",cfglevel:2,enabledmark:1,allowdelete:0,cfgoption:""};this.lst.push(e)},handleSelectionChange:function(e){e.length>0?this.selected=!0:this.selected=!1;for(var t=0;t<this.lst.length;t++)this.lst[t].rownum=t;this.multipleSelection=e},delItem:function(){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.deleteRows()})).catch((function(){}))},deleteRows:function(e,t){var a=this,l=this.multipleSelection;console.log("选中数据",l,a.lst),l&&l.forEach((function(e,t){e.cfgname?e.cfgkey?a.lst.forEach((function(t,l){e.cfgtype===t.cfgtype&&e.cfgkey===t.cfgkey&&e.cfgvalue===t.cfgvalue&&e.cfgname===t.cfgname&&a.lst.splice(l,1)})):a.$message.warning("参数键名不能为空"):a.$message.warning("参数名称不能为空")})),this.$refs.multipleTable.clearSelection(),this.selected=!1},handleFocus:function(){this.selVisible=!0},handleBlur:function(){this.selVisible=!1},catchHight:function(){var e=this;this.$nextTick((function(){e.$refs.elitem&&(e.tableHeight=e.$refs.elitem.getBoundingClientRect().height-32,console.log(" this.tableHeight",e.tableHeight))}))},saveRow:function(e){for(var t=0;t<this.lst.length;t++)this.lst[t].isEdit=!1;this.isEditOk&&(e.isEdit?this.$set(e,"isEdit",!1):this.$set(e,"isEdit",!0)),this.$forceUpdate()},getMoveUp:function(){if(1==this.multipleSelection.length){var e=this.multipleSelection[0],t=this.multipleSelection[0].rownum;if(0!=t){this.lst.splice(t,1),this.lst.splice(t-1,0,e);for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是第一行了！")}else this.$message.warning("只能选择一行内容！")},getMoveDown:function(){if(1==this.multipleSelection.length){var e=this.multipleSelection[0],t=this.multipleSelection[0].rownum;if(t!=this.lst.length-1){this.lst.splice(t,1),this.lst.splice(t+1,0,e);for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是最后一行了！")}else this.$message.warning("只能选择一行内容！")}}}),m=d,u=(a("0893"),a("2877")),h=Object(u["a"])(m,c,f,!1,null,"11ff0b8c",null),p=h.exports,g={name:"Formedit",components:{elitem:p},props:["idx","getType"],data:function(){return{title:"用户参数",formdata:{item:[],remark:"",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,parentid:"root"},formRules:{cfgname:[{required:!0,trigger:"blur",message:"参数名称为必填项"}],cfgvalue:[{required:!0,trigger:"blur",message:"参数键值为必填项"}],cfgkey:[{required:!0,trigger:"blur",message:"参数键名为必填项"}]},formLabelWidth:"100px",multi:0,selVisible:!1,cfgData:[],defaultProps:{children:"children",label:"cfgname",value:"id"},queryParams:{PageNum:1,PageSize:500,OrderType:1,SearchType:0}}},computed:{formcontainHeight:function(){return window.innerHeight-50-23-50+"px"}},watch:{idx:function(e,t){this.bindData()}},created:function(){this.bindData()},methods:{bindData:function(){var e=this;return Object(o["a"])(Object(s["a"])().mark((function t(){return Object(s["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.listLoading=!0,t.next=3,n["a"].post("/system/SYSM06B3/getPageList",JSON.stringify(e.queryParams)).then((function(t){200==t.data.code&&(e.cfgData=e.changeFormat(t.data.data.list))}));case 3:case"end":return t.stop()}}),t)})))()},submitForm:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return console.log("error submit!!"),!1;t.saveForm()}))},saveForm:function(){var e=this;if(0!=this.$refs.elitem.lst.length)for(var t=this.$refs.elitem.lst,a=0;a<t.length;a++){var l=Object.assign(this.formdata,t[a]);console.log("data",l),r["a"].add(l).then((function(t){e.$message.success("保存成功"),e.closeForm()})).catch((function(t){e.$message.warning("保存失败")}))}else this.$message.warning("单据内容不能为空")},closeForm:function(){this.$store.dispatch("tagsView/delView",this.$route),this.$router.go(-1)},handleChange:function(e){e.length>0?this.formdata.parentid=e[e.length-1]:this.formdata.parentid="root"},changeFormat:function(e){var t=[];if(!Array.isArray(e))return t;e.forEach((function(e){delete e.children}));var a={};return e.forEach((function(e){a[e.id]=e})),e.forEach((function(e){var l=a[e.parentid];l?(l.children||(l.children=[])).push(e):t.push(e)})),t},selectSupplier:function(){var e=this.$refs.selectSupplier.selrows;console.log(e),this.formdata.GroupName=e.GroupName,this.formdata.Custid=e.id,this.selVisible=!1},cleValidate:function(e){this.$refs.formdata.clearValidate(e)}}},b=g,w=(a("8f75"),Object(u["a"])(b,l,i,!1,null,"b1d300c8",null));t["default"]=w.exports},e6d0:function(e,t,a){"use strict";var l=a("b775");const i={add(e){return new Promise((t,a)=>{var i=JSON.stringify(e);l["a"].post("/system/SYSM06B3/create",i).then(e=>{console.log(i,e),200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})},update(e){return new Promise((t,a)=>{var i=JSON.stringify(e);l["a"].post("/system/SYSM06B3/update",i).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})},delete(e){return new Promise((t,a)=>{l["a"].get("/system/SYSM06B3/delete?key="+e).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})}};t["a"]=i}}]);