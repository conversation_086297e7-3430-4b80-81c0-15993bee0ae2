(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2f15535a"],{"03af":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),a("div",[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"350px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},"row-class-name":t.rowIndex},on:{"row-click":t.rowClick}},[1==t.multi?a("el-table-column",{attrs:{type:"selection",width:"40"}}):a("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(a){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),a("el-table-column",{attrs:{label:"用户账号",align:"center","min-width":"100px","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.username))])]}}])}),a("el-table-column",{attrs:{label:"姓名",align:"center","min-width":"80px","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.realname))])]}}])}),a("el-table-column",{attrs:{label:"手机",align:"center",prop:"mobile","min-width":"60"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.mobile?e.row.mobile:"-"))])]}}])}),a("el-table-column",{attrs:{label:"邮箱",align:"center","min-width":"60"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.email?e.row.email:"-"))])]}}])}),a("el-table-column",{attrs:{label:"身份",align:"center","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[0==e.row.isadmin?a("el-tag",{attrs:{size:"medium"}},[t._v("普通用户")]):a("el-tag",{attrs:{type:"warning",size:"medium"}},[t._v("管理员")])]}}])}),a("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createdate","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("dateFormat")(e.row.createdate)))])]}}])})],1)],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},n=[],s=(a("e9c4"),a("4d90"),a("d3b7"),a("25f0"),a("99af"),a("b775")),r=a("333d"),o={components:{Pagination:r["a"]},props:["multi"],data:function(){return{title:"用户信息",listLoading:!0,lst:[],searchstr:" ",strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},created:function(){this.searchstr="",this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;this.listLoading=!0,s["a"].post("/system/SYSM01B4/getPageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={username:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},rowIndex:function(t){var e=t.row,a=t.rowIndex;e.row_index=a},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)}},filters:{dateFormat:function(t){if(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),n=e.getDate().toString().padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(n)}}}},l=o,c=(a("5b71"),a("2877")),d=Object(c["a"])(l,i,n,!1,null,"5443452c",null);e["a"]=d.exports},"0589":function(t,e,a){},"07ac":function(t,e,a){var i=a("23e7"),n=a("6f53").values;i({target:"Object",stat:!0},{values:function(t){return n(t)}})},"0a50":function(t,e,a){},"10e6":function(t,e,a){"use strict";a("334d")},1276:function(t,e,a){"use strict";var i=a("d784"),n=a("44e7"),s=a("825a"),r=a("1d80"),o=a("4840"),l=a("8aa5"),c=a("50c4"),d=a("14c3"),m=a("9263"),u=a("d039"),f=[].push,p=Math.min,h=4294967295,g=!u((function(){return!RegExp(h,"y")}));i("split",2,(function(t,e,a){var i;return i="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(t,a){var i=String(r(this)),s=void 0===a?h:a>>>0;if(0===s)return[];if(void 0===t)return[i];if(!n(t))return e.call(i,t,s);var o,l,c,d=[],u=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),p=0,g=new RegExp(t.source,u+"g");while(o=m.call(g,i)){if(l=g.lastIndex,l>p&&(d.push(i.slice(p,o.index)),o.length>1&&o.index<i.length&&f.apply(d,o.slice(1)),c=o[0].length,p=l,d.length>=s))break;g.lastIndex===o.index&&g.lastIndex++}return p===i.length?!c&&g.test("")||d.push(""):d.push(i.slice(p)),d.length>s?d.slice(0,s):d}:"0".split(void 0,0).length?function(t,a){return void 0===t&&0===a?[]:e.call(this,t,a)}:e,[function(e,a){var n=r(this),s=void 0==e?void 0:e[t];return void 0!==s?s.call(e,n,a):i.call(String(n),e,a)},function(t,n){var r=a(i,t,this,n,i!==e);if(r.done)return r.value;var m=s(t),u=String(this),f=o(m,RegExp),b=m.unicode,w=(m.ignoreCase?"i":"")+(m.multiline?"m":"")+(m.unicode?"u":"")+(g?"y":"g"),v=new f(g?m:"^(?:"+m.source+")",w),y=void 0===n?h:n>>>0;if(0===y)return[];if(0===u.length)return null===d(v,u)?[u]:[];var x=0,S=0,_=[];while(S<u.length){v.lastIndex=g?S:0;var k,P=d(v,g?u:u.slice(S));if(null===P||(k=p(c(v.lastIndex+(g?0:S)),u.length))===x)S=l(u,S,b);else{if(_.push(u.slice(x,S)),_.length===y)return _;for(var $=1;$<=P.length-1;$++)if(_.push(P[$]),_.length===y)return _;S=x=k}}return _.push(u.slice(x)),_}]}),!g)},"19d3":function(t,e,a){"use strict";a("1d08")},"1d08":function(t,e,a){},2253:function(t,e,a){},"334d":function(t,e,a){},3781:function(t,e,a){"use strict";a("bfae")},"4ec9":function(t,e,a){"use strict";var i=a("6d61"),n=a("6566");t.exports=i("Map",(function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}}),n)},"5b71":function(t,e,a){"use strict";a("0a50")},"67e7":function(t,e,a){"use strict";a("e5ec")},"6f53":function(t,e,a){var i=a("83ab"),n=a("df75"),s=a("fc6a"),r=a("d1e7").f,o=function(t){return function(e){var a,o=s(e),l=n(o),c=l.length,d=0,m=[];while(c>d)a=l[d++],i&&!r.call(o,a)||m.push(t?[a,o[a]]:o[a]);return m}};t.exports={entries:o(!0),values:o(!1)}},7840:function(t,e,a){"use strict";a("f449")},7890:function(t,e,a){"use strict";a("daf3")},"7ca5":function(t,e,a){"use strict";var i=a("b775");const n={add(t){return new Promise((e,a)=>{var n=JSON.stringify(t);i["a"].post("/system/SYSM01B1/create",n).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},update(t){return new Promise((e,a)=>{var n=JSON.stringify(t);i["a"].post("/system/SYSM01B1/update",n).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},delete(t){return new Promise((e,a)=>{i["a"].get("/system/SYSM01B1/delete?key="+t).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})}};e["a"]=n},"7f911":function(t,e,a){"use strict";a("2253")},8974:function(t,e,a){"use strict";a("8aab")},"8aab":function(t,e,a){},"9e4d":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.formvisible?a("div",{ref:"formadd",staticClass:"formadd"},[a("formadd",t._g({ref:"formadd",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):a("div",{ref:"index",staticClass:"index"},[a("div",{staticClass:"page-container"},[a("e-desc",{attrs:{data:t.formlist,margin:"10px 1%","label-width":"100px"}},[a("e-desc-item",{attrs:{label:t.$store.state.app.config.model+"编码"}},[a("template",{slot:"content"},[t.formlist.isEdit?a("el-input",{attrs:{size:"small",placeholder:"请输入"+t.$store.state.app.config.model+"编码"},model:{value:t.formlist.tenantcode,callback:function(e){t.$set(t.formlist,"tenantcode",e)},expression:"formlist.tenantcode"}}):a("span",[t._v(t._s(t.formlist.tenantcode?t.formlist.tenantcode:"暂无数据"))])],1)],2),a("e-desc-item",{attrs:{label:t.$store.state.app.config.model+"名称"}},[a("template",{slot:"content"},[t.formlist.isEdit?a("el-input",{attrs:{size:"small",placeholder:"请输入"+t.$store.state.app.config.model+"名称"},model:{value:t.formlist.tenantname,callback:function(e){t.$set(t.formlist,"tenantname",e)},expression:"formlist.tenantname"}}):a("span",[t._v(t._s(t.formlist.tenantname?t.formlist.tenantname:"暂无数据"))])],1)],2),a("e-desc-item",{attrs:{label:"创建人"}},[t._v(t._s(t.formlist.createby))]),a("e-desc-item",{attrs:{label:"公司名称"}},[a("template",{slot:"content"},[t.formlist.isEdit?a("el-input",{attrs:{size:"small",placeholder:"请输入公司名称"},model:{value:t.formlist.company,callback:function(e){t.$set(t.formlist,"company",e)},expression:"formlist.company"}}):a("span",[t._v(" "+t._s(t.formlist.company?t.formlist.company:"暂无数据"))])],1)],2),a("e-desc-item",{attrs:{label:"公司电话"}},[a("template",{slot:"content"},[t.formlist.isEdit?a("el-input",{attrs:{size:"small",placeholder:"请输入公司电话"},model:{value:t.formlist.companytel,callback:function(e){t.$set(t.formlist,"companytel",e)},expression:"formlist.companytel"}}):a("span",[t._v(t._s(t.formlist.companytel?t.formlist.companytel:"暂无数据"))])],1)],2),a("e-desc-item",{attrs:{label:"上次登录"}},[t._v(t._s(t._f("dateFormat")(t.formlist.previouvisit)))]),a("e-desc-item",{attrs:{label:"联系人"}},[a("template",{slot:"content"},[t.formlist.isEdit?a("el-input",{attrs:{size:"small",placeholder:"请输入联系人"},model:{value:t.formlist.contactor,callback:function(e){t.$set(t.formlist,"contactor",e)},expression:"formlist.contactor"}}):a("span",[t._v(t._s(t.formlist.contactor?t.formlist.contactor:"暂无数据"))])],1)],2),a("e-desc-item",{attrs:{label:"公司地址"}},[a("template",{slot:"content"},[t.formlist.isEdit?a("el-input",{attrs:{size:"small",placeholder:"请输入公司地址"},model:{value:t.formlist.companyadd,callback:function(e){t.$set(t.formlist,"companyadd",e)},expression:"formlist.companyadd"}}):a("span",[t._v(t._s(t.formlist.companyadd?t.formlist.companyadd:"暂无数据"))])],1)],2),a("e-desc-item",{attrs:{label:"描述",span:2}},[a("template",{slot:"content"},[t.formlist.isEdit?a("el-input",{attrs:{size:"small",placeholder:"请输入描述"},model:{value:t.formlist.summary,callback:function(e){t.$set(t.formlist,"summary",e)},expression:"formlist.summary"}}):a("span",[t._v(t._s(t.formlist.summary?t.formlist.summary:"暂无数据"))])],1)],2),a("e-desc-item",{attrs:{label:"操作",span:3}},[a("template",{slot:"content"},[a("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-edit"},on:{click:function(e){return t.changeEidt()}}},[t._v(t._s(t.formlist.isEdit?"保存":"修改"))]),a("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-circle-plus-outline"},on:{click:function(e){return t.handleusers(t.formlist)}}},[t._v("关联")]),a("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-refresh"},on:{click:function(e){return t.bindData()}}},[t._v("刷新")]),a("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-plus"},on:{click:function(e){return t.addUser()}}},[t._v("新增用户")])],1)],2)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{overflow:"auto",width:"98%",margin:"0 1%"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},height:t.tableMaxHeight}},[a("el-table-column",{attrs:{align:"center",label:"ID","min-width":"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.$index+1)+" ")]}}])}),a("el-table-column",{attrs:{label:"登录名",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.username))])]}}])}),a("el-table-column",{attrs:{label:"中文名","min-width":"100",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.realname)+" ")]}}])}),a("el-table-column",{attrs:{label:"身份","min-width":"100",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[0==e.row.isadmin?a("el-tag",{attrs:{size:"medium"}},[t._v("普通用户")]):a("el-tag",{attrs:{type:"success",size:"medium"}},[t._v("管理员")])]}}])}),a("el-table-column",{attrs:{label:"手机","min-width":"100",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.mobile?e.row.mobile:"-")+" ")]}}])}),a("el-table-column",{attrs:{label:"邮箱","min-width":"100",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.email?e.row.email:"-")+" ")]}}])}),a("el-table-column",{attrs:{label:"新建时间","min-width":"100",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("dateFormat")(e.row.createdate)))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","min-width":"120","class-name":"small-padding fixed-width"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(a){return t.handlePower(e.row)}}},[t._v("我的权限")]),a("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-circle-check"},on:{click:function(a){return t.handlePiPerMission(e.row.userid,e.row)}}},[t._v("分配角色")]),t.$store.state.user.userinfo.isadmin?a("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-warning-outline"},on:{click:function(a){return t.initialPwd(e.row)}}},[t._v("初始化密码")]):t._e()]}}])})],1)],1)]),a("el-drawer",{attrs:{visible:t.usersVisible,"with-header":!1,size:"50%"},on:{"update:visible":function(e){t.usersVisible=e}}},[t.usersVisible?a("formedit",t._g({ref:"formedit",attrs:{idx:t.idx,functionData:t.usersdata}},{closeBtn:t.usersclose,bindData:t.bindData})):t._e()],1),t.drawerVisible?a("el-drawer",{attrs:{visible:t.drawerVisible,"with-header":!1,size:t.drawerSize},on:{"update:visible":function(e){t.drawerVisible=e}}},[t.drawerVisible?a("roles",t._g({ref:"roles",attrs:{idx:t.idx,drawdata:t.drawdata}},{drawclose:t.drawclose})):t._e()],1):t._e(),t.addUserVisible?a("el-dialog",{attrs:{title:"添加角色",width:"500px",visible:t.addUserVisible,"close-on-click-modal":!1,top:"10vh"},on:{"update:visible":function(e){t.addUserVisible=e}}},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"60px","auto-complete":"on",rules:t.formRules}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"账号",prop:"username"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入账号",clearable:"",size:"small"},model:{value:t.formdata.username,callback:function(e){t.$set(t.formdata,"username",e)},expression:"formdata.username"}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"姓名",prop:"realname"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入姓名",clearable:"",size:"small"},model:{value:t.formdata.realname,callback:function(e){t.$set(t.formdata,"realname",e)},expression:"formdata.realname"}})],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.submitAddUserBtn()}}},[t._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.addUserVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),a("el-drawer",{attrs:{visible:t.powerVisible,"with-header":!1,size:"60%"},on:{"update:visible":function(e){t.powerVisible=e}}},[t.powerVisible?a("Power",t._g({ref:"power",attrs:{functionData:t.powerdata}},{closeBtn:t.powerclose})):t._e()],1)],1)},n=[],s=(a("4d90"),a("d3b7"),a("25f0"),a("99af"),a("e9c4"),a("b64b"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{class:t.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[a("div",[a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnSearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnSearch},slot:"append"},[t._v("搜索 ")])],1),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-plus",plain:"",size:"mini"},on:{click:t.btnAdd}},[t._v(" 添加 ")])],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right"},on:{click:t.bindData}}),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{icon:t.iShow?"el-icon-arrow-up":"el-icon-arrow-down",size:"mini",title:"高级搜索"},on:{click:function(e){t.iShow=!t.iShow}}})],1)]),a("el-collapse-transition",[a("div",{directives:[{name:"show",rawName:"v-show",value:t.iShow,expression:"iShow"}],staticClass:"searchDetail"},[a("el-form",{ref:"formdata",attrs:{model:t.formdata,"label-width":"100px","auto-complete":"on"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"租户号"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入货品编码",size:"small"},model:{value:t.formdata.goodsname,callback:function(e){t.$set(t.formdata,"goodsname",e)},expression:"formdata.goodsname"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"租户名称"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入货品名称",size:"small"},model:{value:t.formdata.goodsname,callback:function(e){t.$set(t.formdata,"goodsname",e)},expression:"formdata.goodsname"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"状态"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择状态",size:"small",clearable:""},model:{value:t.formdata.goodsstate,callback:function(e){t.$set(t.formdata,"goodsstate",e)},expression:"formdata.goodsstate"}},[a("el-option",{attrs:{label:"正常",value:"正常"}}),a("el-option",{attrs:{label:"停用",value:"停用"}})],1)],1)],1),a("el-col",{attrs:{span:6}},[a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px","margin-top":"6px"},attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.advancedSearch()}}},[t._v(" 搜索 ")])],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"公司"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入公司",size:"small"},model:{value:t.formdata.goodsname,callback:function(e){t.$set(t.formdata,"goodsname",e)},expression:"formdata.goodsname"}})],1)],1)],1)],1)],1)])],1)}),r=[],o={name:"Listheader",data:function(){return{strfilter:"",iShow:!1,formdata:{}}},methods:{btnAdd:function(){this.$emit("btnAdd")},btnSearch:function(){console.log(this.strfilter),this.$emit("btnSearch",this.strfilter)},bindData:function(){this.$emit("bindData")}}},l=o,c=(a("7840"),a("2877")),d=Object(c["a"])(l,s,r,!1,null,"0a80e116",null),m=d.exports,u=a("333d"),f=a("b775"),p=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.drawclose(e)}}},[t._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth}},[a("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:t.$store.state.app.config.model+"名称",prop:"tenantname"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{readonly:!0,size:"small"},model:{value:t.formdata.tenantname,callback:function(e){t.$set(t.formdata,"tenantname",e)},expression:"formdata.tenantname"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"公司",prop:"company"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{readonly:!0,size:"small"},model:{value:t.formdata.company,callback:function(e){t.$set(t.formdata,"company",e)},expression:"formdata.company"}})],1)],1)],1)],1),a("el-divider")],1),a("div",{staticClass:"form-body form f-1"},[a("elitem",{ref:"elitem",staticStyle:{width:"99%"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,idx:t.idx},on:{parentBindData:t.parentBindData}})],1),a("el-form",{staticClass:"form",attrs:{"label-width":"80px"}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"创建"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.modifydate)))])])],1)],1)],1)],1)])])},h=[];const g={add(t){return new Promise((e,a)=>{var i=JSON.stringify(t);f["a"].post("/system/SYSM01B3/create",i).then(t=>{console.log(t),200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},update(t){return new Promise((e,a)=>{var i=JSON.stringify(t);f["a"].post("/system/SYSM01B3/update",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},delete(t){return new Promise((e,a)=>{f["a"].get("/system/SYSM01B3/delete?key="+t).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},addUserItem(t){return new Promise((e,a)=>{var i=JSON.stringify(t);f["a"].post("/system/SYSM01B4/create",i).then(t=>{console.log(i,t),200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},updateUserItem(t){return new Promise((e,a)=>{var i=JSON.stringify(t);f["a"].post("/system/SYSM01B4/update",i).then(t=>{console.log(i,t),200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},deleteUserItem(t){return new Promise((e,a)=>{f["a"].get("/system/SYSM01B4/delete?key="+t).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})}};var b=g,w=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[a("div",[a("el-row",[a("el-button-group",[a("el-button",{attrs:{type:"primary",size:"mini"},nativeOn:{click:function(e){return t.getselPwProcess(1)}}},[a("i",{staticClass:"el-icon-circle-plus-outline"}),t._v(" 添 加")]),a("el-button",{attrs:{disabled:!t.selected,type:"danger",size:"mini"},nativeOn:{click:function(e){return t.delItem()}}},[a("i",{staticClass:"el-icon-delete"}),t._v(" 批量删除")])],1)],1)],1),a("div",{staticClass:"table-container f-1 table-position"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"multipleTable",staticClass:"tb-edit tableBox",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"",size:"small","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"4px 0px 4px 0px"},"cell-style":{padding:"4px 0px"},"row-style":{height:"20px"}},on:{"selection-change":t.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"40"}}),a("el-table-column",{attrs:{align:"center",label:"ID","min-width":"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.$index+1)+" ")]}}])}),a("el-table-column",{attrs:{label:"用户账号",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.username))])]}}])}),a("el-table-column",{attrs:{label:"姓名",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.realname))])]}}])}),a("el-table-column",{attrs:{label:"是否为管理员",align:"center","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(1==e.row.isadmin?"是":"否"))])]}}])}),a("el-table-column",{attrs:{label:"手机",align:"center","min-width":"150","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.mobile?e.row.mobile:"-"))])]}}])}),a("el-table-column",{attrs:{label:"邮箱",align:"center","min-width":"150","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.email?e.row.email:"-"))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","min-width":"100px","class-name":"small-padding fixed-width"},scopedSlots:t._u([{key:"default",fn:function(e){return[0==e.row.isadmin?a("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(a){return t.changeisAdmin(e.row)}}},[t._v("授权为管理员")]):a("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(a){return t.changeisCommon(e.row)}}},[t._v("授权为普通用户")])]}}])})],1)],1),t.UserFormVisible?a("el-dialog",{attrs:{title:"用户信息","append-to-body":!0,visible:t.UserFormVisible,width:"800px","close-on-press-escape":!1,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.UserFormVisible=e}}},[a("div",{staticClass:"dialog-body"},[a("div",{staticClass:"search"},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"280px"},attrs:{placeholder:"请输入账号","prefix-icon":"el-icon-search",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnSearch(e)}},model:{value:t.searchVal,callback:function(e){t.searchVal=e},expression:"searchVal"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"small"},on:{click:t.btnSearch},slot:"append"},[t._v("搜索 ")])],1),a("el-table",{staticStyle:{width:"100%","margin-top":"10px"},attrs:{data:t.searchTable,border:"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"4px 0px 4px 0px"}}},[a("el-table-column",{attrs:{prop:"username",label:"登录号",width:"180",align:"center"}}),a("el-table-column",{attrs:{prop:"realname",label:"中文名",width:"180",align:"center"}}),a("el-table-column",{attrs:{prop:"mobile",label:"手机",align:"center"}}),a("el-table-column",{attrs:{prop:"email",label:"邮箱",align:"center"}})],1)],1)]),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"small"},on:{click:function(e){t.UserFormVisible=!1}}},[t._v("取 消")]),a("el-button",{attrs:{size:"small",type:"primary"},nativeOn:{click:function(e){return t.submit()}}},[t._v("确 定")])],1)]):t._e()],1)},v=[],y=a("b85c"),x=a("c7eb"),S=a("1da1"),_=(a("3ca3"),a("ddb0"),a("03af")),k={name:"Elitem",components:{selUser:_["a"]},props:["formdata","lstitem","idx"],data:function(){return{title:"组织-用户",formLabelWidth:"100px",listLoading:!1,UserFormVisible:!1,lst:[],multi:0,TotalHour:0,TotalAmount:0,dummyLst:[1,2,3,4,5,6,7],duummyLength:5,selected:!1,searchTable:[],searchVal:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},watch:{},created:function(){this.lst=[],this.bindData()},methods:{bindData:function(){var t=this;f["a"].get("/system/SYSM01B4/getBillEntityByTenant?key=".concat(this.idx)).then((function(e){200==e.data.code&&(t.lst=e.data.data.item)}))},getselPwProcess:function(t){this.UserFormVisible=!0,this.multi=t},submit:function(){var t=this;return Object(S["a"])(Object(x["a"])().mark((function e(){var a,i,n,s,r;return Object(x["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(0!=t.searchTable.length){e.next=3;break}return t.$message.warning("用户信息为空！"),e.abrupt("return");case 3:for(a=t,i=[],n=0;n<t.searchTable.length;n++)s={},r=new Promise((function(e,a){s.userid=t.searchTable[n].userid,s.username=t.searchTable[n].username,s.realname=t.searchTable[n].realname,s.tenantcode=t.formdata.tenantcode,s.tenantname=t.formdata.tenantname,s.tenantid=t.formdata.tenantid,console.log(s),b.addUserItem(s).then((function(i){200==i.code?(e(i),t.lst.push(t.searchTable[n])):a(i)})).catch((function(t){a(t)}))})),i.push(r);return e.next=8,Promise.all(i).then((function(e){t.bindData(),t.$emit("parentBindData"),a.$message.success("保存成功"),t.searchVal="",t.lst=[]})).catch((function(t){a.$message.warning("保存失败")}));case 8:t.UserFormVisible=!1;case 9:case"end":return e.stop()}}),e)})))()},btnSearch:function(){var t=this;this.queryParams.SearchPojo={username:this.searchVal},this.queryParams.SearchType=1,this.queryParams.PageNum=1,f["a"].post("/system/SYSM01B1/getPageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.searchTable=e.data.data.list,0==t.searchTable.length&&t.$message.warning("暂无该用户")),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},changeisAdmin:function(t){var e=this;this.$confirm("是否将"+t.realname+"的权限升级为管理员?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.isadmin?t.isadmin=0:t.isadmin=1,b.updateUserItem(t).then((function(t){e.bindData(),e.$message.success("授权成功"),e.$emit("parentBindData")})).catch((function(t){e.$message.warning("授权失败")}))})).catch((function(){}))},changeisCommon:function(t){var e=this;this.$confirm("是否将"+t.realname+"的权限降为普通用户?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.isadmin?t.isadmin=0:t.isadmin=1,b.updateUserItem(t).then((function(t){e.bindData(),e.$message.success("授权成功"),e.$emit("parentBindData")})).catch((function(t){e.$message.warning("授权失败")}))})).catch((function(){}))},handleSelectionChange:function(t){t.length>0?this.selected=!0:this.selected=!1,this.multipleSelection=t},delItem:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.deleteRows()})).catch((function(){}))},deleteRows:function(t,e){var a=this;return Object(S["a"])(Object(x["a"])().mark((function t(){var e,i,n,s,r,o;return Object(x["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e=a,i=a.multipleSelection,console.log("val",i),!i){t.next=23;break}n=[],s=Object(y["a"])(i),t.prev=6,o=Object(x["a"])().mark((function t(){var e,a;return Object(x["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e=r.value,a=new Promise((function(t,a){b.deleteUserItem(e.id).then((function(e){200==e.code?t("删除成功"):a("删除失败")})).catch((function(t){a("删除失败")}))})),n.push(a);case 3:case"end":return t.stop()}}),t)})),s.s();case 9:if((r=s.n()).done){t.next=13;break}return t.delegateYield(o(),"t0",11);case 11:t.next=9;break;case 13:t.next=18;break;case 15:t.prev=15,t.t1=t["catch"](6),s.e(t.t1);case 18:return t.prev=18,s.f(),t.finish(18);case 21:return t.next=23,Promise.all(n).then((function(t){a.bindData(),e.$message.success("删除成功"),a.$emit("parentBindData")})).catch((function(t){a.bindData(),e.$message.warning("删除失败")}));case 23:a.$refs.multipleTable.clearSelection(),a.selected=!1;case 25:case"end":return t.stop()}}),t,null,[[6,15,18,21]])})))()},cleValidate:function(t){this.$refs.itemForm.clearValidate(t)}}},P=k,$=(a("a7dc"),Object(c["a"])(P,w,v,!1,null,"431c8088",null)),C=$.exports,D={name:"SYSM01B3",components:{elitem:C},props:["idx","functionData"],data:function(){return{title:"关联用户",formLabelWidth:"100px",formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,item:[]}}},computed:{formcontainHeight:function(){return window.innerHeight-50+"px"}},watch:{idx:function(t,e){console.log("new: %s, old: %s",t,e),this.bindData()}},created:function(){this.bindData()},methods:{bindData:function(){0!=this.idx&&(console.log(this.functionData),this.formdata=this.functionData)},submitForm:function(){this.$refs[formdata].validate((function(t){if(!t)return console.log("error submit!!"),!1}))},drawclose:function(){this.$emit("closeBtn")},parentBindData:function(){this.$emit("bindData")}},filters:{dateFormat:function(t){if(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),n=e.getDate().toString().padStart(2,"0"),s=e.getHours().toString().padStart(2,"0"),r=e.getMinutes().toString().padStart(2,"0"),o=e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(n," ").concat(s,":").concat(r,":").concat(o)}}}},z=D,O=(a("3781"),Object(c["a"])(z,p,h,!1,null,"84159172",null)),F=O.exports,L=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",size:"small"},nativeOn:{click:function(e){return t.submitForm("formdata")}}},[t._v(" 保 存")]),t.idx?a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.deleteForm(t.idx)}}},[t._v(" 删 除")]):t._e(),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.closeForm(e)}}},[t._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth,"auto-complete":"on",rules:t.formRules}},[a("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"用户编码",prop:"usercode"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"用户编码",clearable:"",size:"small"},model:{value:t.formdata.usercode,callback:function(e){t.$set(t.formdata,"usercode",e)},expression:"formdata.usercode"}})],1)],1),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("username")}}},[a("el-form-item",{attrs:{label:"登录账号",prop:"username"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入登录账号",clearable:"",size:"small"},model:{value:t.formdata.username,callback:function(e){t.$set(t.formdata,"username",e)},expression:"formdata.username"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("realname")}}},[a("el-form-item",{attrs:{label:"姓名",prop:"realname"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入姓名",clearable:"",size:"small"},model:{value:t.formdata.realname,callback:function(e){t.$set(t.formdata,"realname",e)},expression:"formdata.realname"}})],1)],1)]),0==t.idx?a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("password")}}},[a("el-form-item",{attrs:{label:"密码",prop:"password"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入密码",type:"text",size:"small"},model:{value:t.formdata.password,callback:function(e){t.$set(t.formdata,"password",e)},expression:"formdata.password"}})],1)],1)]):t._e()],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"手机"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入手机",clearable:"",size:"small"},model:{value:t.formdata.mobile,callback:function(e){t.$set(t.formdata,"mobile",e)},expression:"formdata.mobile"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"邮箱"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入邮箱",clearable:"",size:"small"},model:{value:t.formdata.email,callback:function(e){t.$set(t.formdata,"email",e)},expression:"formdata.email"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"状态"}},[a("el-radio",{attrs:{label:0},model:{value:t.formdata.userstatus,callback:function(e){t.$set(t.formdata,"userstatus",e)},expression:"formdata.userstatus"}},[t._v("正常")]),a("el-radio",{attrs:{label:1},model:{value:t.formdata.userstatus,callback:function(e){t.$set(t.formdata,"userstatus",e)},expression:"formdata.userstatus"}},[t._v("停用")])],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"头像"}},[a("div",[a("el-upload",{ref:"upload",class:{imageupload:!0,disabled:t.isMax},staticStyle:{display:"flex"},attrs:{action:"","on-change":t.getFile,"on-remove":t.handleRemove,"list-type":"picture-card","on-preview":t.handlePictureCardPreview,"auto-upload":!1,limit:1}},[a("i",{staticClass:"el-icon-plus",staticStyle:{width:"30px",height:"30px","font-size":"30px"}})])],1),t.dialogVisible?a("el-image-viewer",{attrs:{visible:t.dialogVisible,"append-to-body":"","on-close":t.closeViwer,"url-list":[t.dialogImageUrl]},on:{"update:visible":function(e){t.dialogVisible=e}}}):t._e()],1)],1)],1)],1),a("el-divider")],1),a("el-form",{staticClass:"form",attrs:{"label-width":t.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{type:"textarea",rows:4,placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.modifydate)))])])],1)],1)],1)],1)])])},T=[],B=a("ade3"),V=(a("b0c0"),a("caad"),a("7ca5")),N=a("08a9"),I={name:"Formedit",components:{ElImageViewer:N["a"]},filters:{dateFormat:function(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),n=e.getDate().toString().padStart(2,"0");return"".concat(a,"/").concat(i,"/").concat(n)}},props:["idx"],data:function(){return Object(B["a"])(Object(B["a"])(Object(B["a"])(Object(B["a"])(Object(B["a"])({title:"用户管理",formdata:{usercode:"",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,username:"",realname:"",mobile:"",email:"",userstatus:0,password:"",avatar:"",remark:""},formRules:{username:[{required:!0,trigger:"blur",message:"登录账号为必填项"}],realname:[{required:!0,trigger:"blur",message:"姓名为必填项"}],password:[{required:!0,trigger:"blur",message:"密码为必填项"}]},formLabelWidth:"100px",ItemPicList:[],dialogImageUrl:"",dialogVisible:!1,finshDialogVisible:!1},"formLabelWidth","100px"),"enActive",!1),"visable",!1),"disabled",!1),"isMax",!1)},computed:{formcontainHeight:function(){return window.innerHeight-50-33-50+"px"},preview1:function(){return"data:image/jpg;base64,"+this.formdata.avatar}},watch:{idx:function(t,e){console.log("new: %s, old: %s",t,e),this.bindData()},ItemPicList:function(t,e){console.log("new: %s, old: %s",t,e),this.formdata.avatar="",this.ItemPicList.length>0&&(this.formdata.avatar=this.ItemPicList[0])}},created:function(){console.log(this.idx),this.bindData()},methods:{bindData:function(){var t=this;this.listLoading=!0,console.log("绑定数据"),0!=this.idx&&f["a"].get("/system/SYSM01B1/getEntity?key=".concat(this.idx)).then((function(e){console.log("SYSM01B1",e),200==e.data.code&&(t.formdata=e.data.data),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},submitForm:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return console.log("error submit!!"),!1;console.log("可保存"),e.saveForm()}))},saveForm:function(){var t=this;0==this.idx?V["a"].add(this.formdata).then((function(e){console.log("新建保存====",e),200==e.code&&(t.$message.success("保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.bindData())})).catch((function(e){t.$message.warning("保存失败")})):V["a"].update(this.formdata).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("bindData"),t.bindData())})).catch((function(e){t.$message.warning("保存失败")}))},closeForm:function(){this.$emit("closeForm"),console.log("关闭窗口")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){console.log("执行删除"),V["a"].delete(t).then((function(t){200==t.code&&e.$message({showClose:!0,message:"删除成功",type:"success"}),e.$emit("compForm")})).catch((function(){e.$message({showClose:!0,message:"删除失败",type:"warning"})}))})).catch((function(){}))},check:function(){console.log("check")},cleValidate:function(t){this.$refs.formdata.clearValidate(t)},handlePictureCardPreview:function(t){console.log(t),this.dialogImageUrl=t.url,this.dialogVisible=!0},handleRemove:function(t,e){var a=this;e.length<2&&(this.isMax=!1),this.hideUpload=e.length>=3,this.ItemPicList=[];for(var i=0;i<e.length;i++)this.getBase64(e[i].raw).then((function(t){var e=t.split(",");console.log(e),a.ItemPicList.push(e[1]),console.log(a.ItemPicList)}))},closeViwer:function(){this.dialogVisible=!1},getFile:function(t,e){var a=this;e.length>=1&&(this.isMax=!0);var i=[".png",".PNG",".jpg",".JPG"],n=t.name,s=t.size,r=n.lastIndexOf("."),o=n.length,l=n.substring(r,o),c=parseFloat(s)/1024/1024>.1;!i.includes(l)||c?(console.log(this.ItemPicList),this.$message.error({message:"注意:文件格式需要为200KB以下的jpg图片！"})):(this.hideUpload=e.length>=3,this.getBase64(t.raw).then((function(t){var e=t.split(",");a.ItemPicList.push(e[1])})))},getBase64:function(t){return new Promise((function(e,a){var i=new FileReader,n="";i.readAsDataURL(t),i.onload=function(){n=i.result},i.onerror=function(t){a(t)},i.onloadend=function(){e(n)}}))}}},j=I,M=(a("10e6"),Object(c["a"])(j,L,T,!1,null,"292a3f74",null)),E=M.exports,q=a("034e"),U=a("e2cc4"),Y=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.closeForm(e)}}},[t._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"用户账号",prop:"username"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{readonly:!0,size:"small"},model:{value:t.formdata.username,callback:function(e){t.$set(t.formdata,"username",e)},expression:"formdata.username"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"中文名",prop:"realname"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{readonly:!0,size:"small"},model:{value:t.formdata.realname,callback:function(e){t.$set(t.formdata,"realname",e)},expression:"formdata.realname"}})],1)],1)],1)],1),a("el-divider")],1),a("div",{staticClass:"form-body form f-1"},[a("elitem",{key:(new Date).getTime(),ref:"elitem",staticStyle:{width:"100%"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,idx:t.idx}})],1),a("el-form",{staticClass:"form",attrs:{"label-width":"80px"}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"创建"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.modifydate)))])])],1)],1)],1)],1)])])},R=[],J=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[a("div",[a("el-row",[a("el-button-group",[a("el-button",{attrs:{type:"primary",size:"mini"},nativeOn:{click:function(e){return t.getselPwWork(1)}}},[a("i",{staticClass:"el-icon-circle-plus-outline"}),t._v(" 添 加")]),a("el-button",{attrs:{type:"danger",size:"mini",disabled:!t.selected},nativeOn:{click:function(e){return t.delItem()}}},[a("i",{staticClass:"el-icon-delete"}),t._v("批 量 删 除")])],1)],1)],1),a("div",{staticClass:"table-container f-1 table-position"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"multipleTable",staticClass:"tb-edit tableBox",staticStyle:{overflow:"auto"},attrs:{"show-summary":"",data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"",size:"small","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"4px 0px 4px 0px"},"cell-style":{padding:"6px 0px"},"row-style":{height:"20px"}},on:{"selection-change":t.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"40"}}),a("el-table-column",{attrs:{label:"角色编码",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.rolecode))])]}}])}),a("el-table-column",{attrs:{label:"角色名称",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.rolename))])]}}])}),a("el-table-column",{attrs:{label:"制表",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.lister))])]}}])}),a("el-table-column",{attrs:{label:"创建时间",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("dateFormat")(e.row.createdate)))])]}}])})],1)],1),a("div",{staticStyle:{"margin-top":"6px",display:"flex","align-items":"center"}}),t.PwProcessFormVisible?a("el-dialog",{attrs:{title:"角色信息","append-to-body":!0,visible:t.PwProcessFormVisible,width:"1200px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.PwProcessFormVisible=e}}},[a("selRole",{ref:"selRole",attrs:{multi:t.multi}}),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.PwProcessFormVisible=!1}}},[t._v("取 消")]),a("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selPwWork()}}},[t._v("确 定")])],1)],1):t._e()],1)},H=[],W=a("c7df"),A={name:"Elitem",components:{selRole:W["a"]},props:["formdata","lstitem","idx"],data:function(){return{title:"服务-角色",formLabelWidth:"100px",listLoading:!1,PwProcessFormVisible:!1,lst:[],multi:0,dummyLst:[1,2,3,4,5,6,7],duummyLength:5,selected:!1}},watch:{},created:function(){this.lst=[],this.bindData()},methods:{bindData:function(){var t=this;f["a"].get("/system/SYSM03B2/getListByUser?key=".concat(this.idx)).then((function(e){200==e.data.code&&(t.lst=e.data.data)}))},getselPwWork:function(t){this.PwProcessFormVisible=!0,this.multi=t},selPwWork:function(t){var e=this;return Object(S["a"])(Object(x["a"])().mark((function t(){var a,i,n,s,r;return Object(x["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:for(a=e,console.log(e.$refs.selRole.$refs.selectVal.selection),i=e.$refs.selRole.$refs.selectVal.selection,n=[],s=0;s<i.length;s++)r=new Promise((function(t,a){e.formdata.roleid=i[s].roleid,e.formdata.rolename=i[s].rolename,e.formdata.realname=i[s].realname,e.formdata.rolecode=i[s].rolecode,e.$request.post("/system/SYSM03B2/create",JSON.stringify(e.formdata)).then((function(e){200==e.code?t("保存成功"):a("保存失败")})).catch((function(t){a("保存失败")}))})),n.push(r);return t.next=7,Promise.all(n).then((function(t){a.$message.success("保存成功"),a.bindData()})).catch((function(t){a.$message.warning("保存失败"),a.bindData()}));case 7:e.PwProcessFormVisible=!1;case 8:case"end":return t.stop()}}),t)})))()},handleSelectionChange:function(t){t.length>0?this.selected=!0:this.selected=!1,this.multipleSelection=t},delItem:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.deleteRows()})).catch((function(){}))},deleteRows:function(t,e){var a=this;return Object(S["a"])(Object(x["a"])().mark((function t(){var e,i,n,s,r,o;return Object(x["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e=a,i=a.multipleSelection,console.log("val",i),!i){t.next=23;break}n=[],s=Object(y["a"])(i),t.prev=6,o=Object(x["a"])().mark((function t(){var e,i;return Object(x["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e=r.value,i=new Promise((function(t,i){a.$request.get("/system/SYSM03B2/delete?key="+e.id).then((function(e){200==e.code?t("删除成功"):i("删除失败")})).catch((function(t){i("删除失败")}))})),n.push(i);case 3:case"end":return t.stop()}}),t)})),s.s();case 9:if((r=s.n()).done){t.next=13;break}return t.delegateYield(o(),"t0",11);case 11:t.next=9;break;case 13:t.next=18;break;case 15:t.prev=15,t.t1=t["catch"](6),s.e(t.t1);case 18:return t.prev=18,s.f(),t.finish(18);case 21:return t.next=23,Promise.all(n).then((function(t){e.$message.success("删除成功"),e.bindData()})).catch((function(t){e.$message.warning("删除失败"),e.bindData()}));case 23:a.$refs.multipleTable.clearSelection(),a.selected=!1;case 25:case"end":return t.stop()}}),t,null,[[6,15,18,21]])})))()}},filters:{dateFormat:function(t){if(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),n=e.getDate().toString().padStart(2,"0"),s=e.getHours().toString().padStart(2,"0"),r=e.getMinutes().toString().padStart(2,"0"),o=e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(n," ").concat(s,":").concat(r,":").concat(o)}}}},G=A,K=(a("8974"),Object(c["a"])(G,J,H,!1,null,"7c897d34",null)),Q=K.exports,X={name:"Formedit",components:{elitem:Q},props:["idx","drawdata"],data:function(){return{formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,item:[]},formLabelWidth:"100px"}},computed:{formcontainHeight:function(){return window.innerHeight-50+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindData()},methods:{bindData:function(){this.listLoading=!0,console.log("绑定数据",this.idx),0!=this.idx&&(this.formdata=this.drawdata)},submitForm:function(t){this.$refs[t].validate((function(t){if(!t)return console.log("error submit!!"),!1}))},closeForm:function(){this.$emit("drawclose")},cleValidate:function(t){this.$refs.formdata.clearValidate(t)}},filters:{dateFormat:function(t){if(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),n=e.getDate().toString().padStart(2,"0"),s=e.getHours().toString().padStart(2,"0"),r=e.getMinutes().toString().padStart(2,"0"),o=e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(n," ").concat(s,":").concat(r,":").concat(o)}}}},Z=X,tt=(a("67e7"),Object(c["a"])(Z,Y,R,!1,null,"3e209b1c",null)),et=tt.exports,at=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.$emit("closeBtn")}}},[t._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth,"auto-complete":"on"}},[a("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),a("el-row",[a("el-col",{attrs:{span:10}},[a("el-form-item",{attrs:{label:"登录名"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{readonly:!0,size:"small"},model:{value:t.formdata.username,callback:function(e){t.$set(t.formdata,"username",e)},expression:"formdata.username"}})],1)],1),a("el-col",{attrs:{span:10}},[a("el-form-item",{attrs:{label:"姓名"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{readonly:!0,size:"small"},model:{value:t.formdata.realname,callback:function(e){t.$set(t.formdata,"realname",e)},expression:"formdata.realname"}})],1)],1)],1)],1),a("el-divider")],1),a("div",{staticClass:"form-body form f-1"},[a("elitem",{key:(new Date).getTime(),ref:"elitem",staticStyle:{width:"98%"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,idx:t.idx}})],1),a("el-form",{staticClass:"form",attrs:{"label-width":"80px"}},[a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormats")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormats")(t.formdata.modifydate)))])])],1)],1)],1)],1)])])},it=[],nt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[a("div",{staticClass:"table-position"},[a("div",{staticClass:"accordion",staticStyle:{overflow:"auto",height:"calc(100vh - 265px)"}},[t._l(t.powerLst,(function(e,i){return[a("div",{key:i},[a("div",{staticClass:"atitle"},[a("el-checkbox",{on:{change:function(a){return t.changePowerGroup(a,e)}},model:{value:e.isTrue,callback:function(a){t.$set(e,"isTrue",a)},expression:"a.isTrue"}},[a("span",{staticStyle:{"font-weight":"bold"}},[t._v(" "+t._s(e.permname?e.permname:"全选"))])])],1),t._l(e.children,(function(e,i){return[a("div",{key:i},[a("div",{staticClass:"groupTitle title",on:{click:function(t){e.isShow=!e.isShow}}},[a("el-checkbox",{on:{change:function(a){return t.changePowerGroup(a,e)}},model:{value:e.isTrue,callback:function(a){t.$set(e,"isTrue",a)},expression:"b.isTrue"}},[a("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(e.permname))])]),a("i",{class:e.isShow?"el-icon-arrow-down":"el-icon-arrow-right",staticStyle:{"margin-right":"20px"}})],1),a("div",{directives:[{name:"show",rawName:"v-show",value:e.isShow,expression:"b.isShow"}],staticStyle:{padding:"4px 10px"}},[t._l(e.children,(function(e,i){return[a("div",{key:i},[a("div",{staticStyle:{margin:"6px 0"}},[a("el-checkbox",{on:{change:function(a){return t.changePowerGroup(a,e)}},model:{value:e.isTrue,callback:function(a){t.$set(e,"isTrue",a)},expression:"c.isTrue"}},[a("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(e.permname))])])],1),a("div",{staticClass:"dBody"},[t._l(e.children,(function(e,i){return[a("div",{key:i,staticStyle:{"margin-bottom":"6px"}},[a("el-checkbox",{on:{change:function(a){return t.changePower(a,e)}},model:{value:e.isTrue,callback:function(a){t.$set(e,"isTrue",a)},expression:"d.isTrue"}},[t._v(t._s(e.permname))])],1)]}))],2)])]}))],2)])]}))],2)]}))],2)]),a("div",{staticStyle:{"margin-top":"6px",display:"flex","align-items":"center"}})])},st=[],rt=(a("4ec9"),a("4de4"),a("c740"),a("159b"),a("5c96")),ot={name:"Elitem",components:{},props:["formdata","lstitem","idx"],data:function(){return{title:"用户-权限",listLoading:!1,lst:[],powerLst:[],multi:0}},watch:{},mounted:function(){this.lst=[],this.bindData()},methods:{bindData:function(t){var e=this;return Object(S["a"])(Object(x["a"])().mark((function a(){return Object(x["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,f["a"].get("/system/SYSM03B4/getUserAllPerm?key=".concat(e.formdata.userid)).then((function(a){if(200==a.data.code){e.lst=a.data.data;var i=new Map;e.lst=a.data.data.filter((function(t){return!i.has(t.permid)&&i.set(t.permid,1)})),console.log(e.lst)}t||e.bindTemp()}));case 2:case"end":return a.stop()}}),a)})))()},bindTemp:function(){var t=this;this.powerLst=[],f["a"].get("/system/SYSM02B2/getPermAllListBySelf").then((function(e){if(200==e.data.code){for(var a=0;a<e.data.data.length;a++){e.data.data[a].isTrue=!1,e.data.data[a].isShow=!0;for(var i=0;i<t.lst.length;i++)t.lst[i].permid==e.data.data[a].permid&&(e.data.data[a].isTrue=!0)}t.powerLst=t.changeFormat(e.data.data)}else t.$message.warning(e.data.msg||"获取权限失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},changePowerGroup:function(t,e){var a=this;return Object(S["a"])(Object(x["a"])().mark((function i(){var n,s,r,o,l,c,d,m,u,p,h;return Object(x["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(n=a.lst.findIndex((function(t){return t.permid==e.permid})),-1==n){i.next=6;break}if("Role"!=a.lst[n].resourcetype){i.next=6;break}return e.isTrue=!t,a.$message.warning(e.permname+"为角色权限，不可修改"),i.abrupt("return");case 6:for(s=a,r=[{permid:e.permid,parentid:e.parentid,permname:e.permname,permcode:e.permcode,resourceid:a.formdata.userid,resourcetype:"User"}],o=0;o<e.children.length;o++){if(e.children[o].isTrue=t,e.children[o].children)for(l=0;l<e.children[o].children.length;l++){if(c=e.children[o].children[l],c.children)for(d=0;d<c.children.length;d++)m=c.children[d],m.isTrue=t,r.push(m);c.isTrue=t,r.push(c)}r.push(e.children[o])}if(u=[],rt["Loading"].service({fullscreen:!0}),!t){i.next=17;break}for(o=0;o<r.length;o++)p=new Promise((function(t,e){s.formdata.permid=r[o].permid,s.formdata.parentid=r[o].parentid,s.formdata.permname=r[o].permname,s.formdata.permcode=r[o].permcode,s.formdata.resourceid=a.formdata.userid,s.formdata.resourcetype="User",f["a"].post("/system/SYSM03B4/create",JSON.stringify(a.formdata)).then((function(a){200==a.data.code?t("保存成功"):e("保存失败")})).catch((function(t){e("保存失败")}))})),u.push(p);return i.next=15,Promise.all(u).then((function(t){s.$message.success("保存成功"),s.bindData(!0)})).catch((function(t){s.$message.warning("保存失败"),s.bindData(!0)})).finally((function(){rt["Loading"].service({fullscreen:!0}).close()}));case 15:i.next=20;break;case 17:for(o=0;o<r.length;o++)h=new Promise((function(t,e){for(var a="",i=0;i<s.lst.length;i++)if(s.lst[i].permid==r[o].permid){a=s.lst[i].id;break}f["a"].get("/system/SYSM03B4/delete?key="+a).then((function(a){200==a.data.code?t("保存成功"):e("保存失败")})).catch((function(t){e("保存失败")}))})),u.push(h);return i.next=20,Promise.all(u).then((function(t){s.$message.success("保存成功"),s.bindData(!0)})).catch((function(t){s.$message.warning("保存失败"),s.bindData(!0)})).finally((function(){rt["Loading"].service({fullscreen:!0}).close()}));case 20:case"end":return i.stop()}}),i)})))()},changePower:function(t,e){var a=this,i=this.lst.findIndex((function(t){return t.permid==e.permid}));if(-1!=i&&"Role"==this.lst[i].resourcetype)return e.isTrue=!t,void this.$message.warning(e.permname+"为角色权限，不可修改");if(t)this.formdata.permid=e.permid,this.formdata.parentid=e.parentid,this.formdata.permname=e.permname,this.formdata.permcode=e.permcode,this.formdata.resourceid=this.formdata.userid,this.formdata.resourcetype="User",f["a"].post("/system/SYSM03B4/create",JSON.stringify(this.formdata)).then((function(t){200==t.data.code||a.$message.warning(t.data.msg||"请求失败"),a.bindData(!0)}));else{for(var n="",s=0;s<this.lst.length;s++)if(this.lst[s].permid==e.permid){n=this.lst[s].id;break}console.log("rowid",n),f["a"].get("/system/SYSM03B4/delete?key="+n).then((function(t){200==t.data.code?a.bindData(!0):a.$message.warning(t.data.msg||"请求失败")}))}},forEachExample:function(t,e){var a=this,i=this;this.$nextTick((function(){t.forEach((function(t,n){e?(t.isChecked=!0,i.$refs.multipleTable.toggleRowSelection(t,!0),t.children&&0!=t.children.length&&a.forEachExample(t.children,!0)):(t.isChecked=!1,i.$refs.multipleTable.toggleRowSelection(t,!1),t.children&&0!=t.children.length&&a.forEachExample(t.children,!1))}))}))},changeFormat:function(t){var e=[];if(!Array.isArray(t))return e;t.forEach((function(t){delete t.children}));var a={};return t.forEach((function(t){a[t.permid]=t})),t.forEach((function(t){var i=a[t.parentid];i?(i.children||(i.children=[])).push(t):e.push(t)})),e}}},lt=ot,ct=(a("19d3"),Object(c["a"])(lt,nt,st,!1,null,"2a7ed7f6",null)),dt=ct.exports,mt={name:"Formedit",components:{elitem:dt},props:["idx","functionData"],data:function(){return{title:"数据权限",formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,item:[]},formLabelWidth:"100px"}},computed:{formcontainHeight:function(){return window.innerHeight-30+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindData()},methods:{bindData:function(){this.formdata=this.functionData,console.log(this.formdata)}}},ut=mt,ft=(a("7f911"),Object(c["a"])(ut,at,it,!1,null,"d82a22ee",null)),pt=ft.exports,ht={name:"TodoPerson",components:{Pagination:u["a"],listheader:m,formadd:E,formedit:F,EDesc:q["a"],EDescItem:U["a"],roles:et,Power:pt},filters:{dateFormat:function(t){if(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),n=e.getDate().toString().padStart(2,"0"),s=e.getHours().toString().padStart(2,"0"),r=e.getMinutes().toString().padStart(2,"0"),o=e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(n," ").concat(s,":").concat(r,":").concat(o)}}},data:function(){return{group:"todo",lst:[],PageIndex:1,PageSize:20,searchstr:"",total:0,formvisible:!1,tableVisable:!0,listLoading:!0,drawerVisible:!1,drawerSize:"50%",drawdata:"",idx:this.$store.state.user.userinfo.tenantid,formlist:{isEdit:!1},queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1},usersVisible:!1,addUserVisible:!1,formdata:{username:"",realname:""},formRules:{username:[{required:!0,trigger:"blur",message:"账号为必填项"}],realname:[{required:!0,trigger:"blur",message:"姓名为必填项"}]},powerVisible:!1,powerdata:{}}},computed:{tableMaxHeight:function(){return window.innerHeight-160-264+"px"}},watch:{},created:function(){this.bindData()},methods:{bindData:function(){var t=this;this.listLoading=!0,f["a"].get("/system/SYSM01B4/getBillEntityByTenant?key=".concat(this.idx)).then((function(e){200==e.data.code&&(t.lst=e.data.data.item,t.formlist=e.data.data,t.formlist.isEdit=!1),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},addUser:function(){this.addUserVisible=!0},submitAddUserBtn:function(){var t=this;this.$refs["formdata"].validate((function(e){if(!e)return console.log("error submit!!"),!1;console.log(JSON.stringify(t.formdata)),f["a"].post("/system//SYSM01B1/createByTen",JSON.stringify(t.formdata)).then((function(e){200==e.data.code?(t.$message.success(e.data.msg||"新增用户成功"),t.addUserVisible=!1,t.bindData()):t.$message.warning(e.data.msg||"新增用户失败")})).catch((function(e){t.$message.error("请求错误")}))}))},changeEidt:function(){var t=this;if(this.formlist.isEdit){if(""==this.formlist.tenantname)return void this.$message.warning("组织名称为必填项");f["a"].post("/system/SYSM01B3/update",JSON.stringify(this.formlist)).then((function(e){console.log(e.data),200==e.data.code?(t.$message.success("保存成功"),t.bindData()):t.$message.warning("保存失败")}))}this.$set(this.formlist,"isEdit",!this.formlist.isEdit),this.$forceUpdate()},deletedRows:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var e=JSON.parse(window.localStorage.getItem("getInfo")).Userid;t.lst.length>1?t.$message.warning("检测当前组织还有多位成员，请先解散成员"):1!=t.lst.length||t.lst[0].id==e?f["a"].get("/system/SYSM01B3/delete?key=".concat(t.formlist.id)).then((function(e){200==e.data.code?(t.$message.success("注销组织成功"),t.$store.dispatch("tagsView/delAllViews"),router.push("/login"),t.$store.dispatch("user/logout")):t.$message.warning("注销组织错误，请稍后重试")})):t.$message.warning("只有管理员才能注销组织")})).catch((function(){}))},initialPwd:function(t){var e=this;f["a"].get("/system/SYSM01B4/getListByUser?key="+t.userid).then((function(a){200==a.data.code&&(1==a.data.data.length?e.changePwd(t):e.$message.warning(a.data.msg||"初始化密码失败，该用户已关联多个"+e.$store.state.app.config.model+"！"))})).catch((function(t){console.log(t)}))},changePwd:function(t){var e=this;this.$confirm("是否确认初始化用户密码?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){f["a"].get("/system/SYSM01B2/initPwdByTen?key="+t.userid).then((function(t){200==t.data.code?e.$message.success("初始化密码成功"):e.$message.warning(t.data.msg||"初始化密码失败")})).catch((function(t){e.$message.error("请求错误")}))})).catch((function(){}))},handlePower:function(t){this.powerdata=t,this.powerVisible=!0},powerclose:function(){this.powerVisible=!1},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){this.bindData()},advancedSearch:function(t){this.queryParams.SearchPojo=t,this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},handleusers:function(t){this.idx=t.tenantid,this.usersdata=t,this.usersVisible=!0},usersclose:function(){this.usersVisible=!1},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},handleNodeClick:function(t){console.log(t)},handlePiPerMission:function(t,e){this.idx=t,this.drawdata=e,this.drawerVisible=!0},drawclose:function(){this.drawerVisible=!1},changeIdx:function(t){this.idx=t}}},gt=ht,bt=(a("7890"),Object(c["a"])(gt,i,n,!1,null,"226e9562",null));e["default"]=bt.exports},a7dc:function(t,e,a){"use strict";a("0589")},bf17:function(t,e,a){},bfae:function(t,e,a){},c7df:function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),a("div",[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"400px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[1==t.multi?a("el-table-column",{attrs:{type:"selection",width:"40"}}):a("el-table-column",{attrs:{label:"",width:"35"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(a){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(t._s(" "))])]}}])}),a("el-table-column",{attrs:{label:"角色编码",align:"center","min-width":"100px"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.rolecode))])]}}])}),a("el-table-column",{attrs:{label:"角色名",align:"center","min-width":"80px","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.rolename))])]}}])}),a("el-table-column",{attrs:{label:"制表",align:"center",prop:"mobile","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.company?e.row.company:"-"))])]}}])}),a("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createdate","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("dateFormat")(e.row.createdate)))])]}}])})],1)],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},n=[],s=(a("e9c4"),a("4d90"),a("d3b7"),a("25f0"),a("99af"),a("b775")),r=a("333d"),o={components:{Pagination:r["a"]},props:["multi"],data:function(){return{title:"角色信息",listLoading:!0,lst:[],searchstr:" ",strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},created:function(){this.searchstr="",this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;this.listLoading=!0,s["a"].post("/system/SYSM03B1/getPageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={rolecode:t,rolename:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()}},filters:{dateFormat:function(t){if(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),n=e.getDate().toString().padStart(2,"0"),s=e.getHours().toString().padStart(2,"0"),r=e.getMinutes().toString().padStart(2,"0"),o=e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(n," ").concat(s,":").concat(r,":").concat(o)}}}},l=o,c=(a("f9cd"),a("2877")),d=Object(c["a"])(l,i,n,!1,null,"a36f0d78",null);e["a"]=d.exports},daf3:function(t,e,a){},e5ec:function(t,e,a){},f449:function(t,e,a){},f9cd:function(t,e,a){"use strict";a("bf17")},fd87:function(t,e,a){var i=a("74e8");i("Int8",(function(t){return function(e,a,i){return t(this,e,a,i)}}))}}]);