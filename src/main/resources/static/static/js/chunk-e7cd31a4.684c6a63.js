(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e7cd31a4"],{"0358":function(t,e,a){},"0b13":function(t,e,a){},"63b9":function(t,e,a){"use strict";a("98f3")},"98f3":function(t,e,a){},a244:function(t,e,a){"use strict";a("0b13")},c2d5:function(t,e,a){},e6c9:function(t,e,a){"use strict";a("c2d5")},f790:function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.formvisible?a("div",{ref:"formedit",staticClass:"formedit"},[a("formedit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):a("div",{ref:"index",staticClass:"index"},[a("listheader",{on:{btnAdd:function(e){return t.showForm(0)},btnSearch:t.search,bindData:t.bindData,btnImport:t.btnImport,btnPrint:t.btnPrint,btnExport:t.btnExport,modelExport:t.modelExport,advancedSearch:t.advancedSearch}}),a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"pageTable",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},height:t.tableMaxHeight},on:{"selection-change":t.handleSelectionChange}},[a("el-table-column",{attrs:{fixed:"left",type:"selection",width:"40",align:"center"}}),a("el-table-column",{attrs:{align:"center",label:"ID","min-width":"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.$index+1)+" ")]}}])}),a("el-table-column",{attrs:{label:"编码",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.showForm(e.row.id)}}},[t._v(" "+t._s(e.row.refno)+" ")])]}}])}),a("el-table-column",{attrs:{label:"单据日期",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("dateFormat")(e.row.billdate)))])]}}])}),a("el-table-column",{attrs:{label:"供应商",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.supplier))])]}}])}),a("el-table-column",{attrs:{label:"客户",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.customer))])]}}])}),a("el-table-column",{attrs:{label:"产品编码",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.itemcode))])]}}])}),a("el-table-column",{attrs:{label:"产品名称",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.itemname))])]}}])}),a("el-table-column",{attrs:{label:"产品规格",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.itemspec))])]}}])}),a("el-table-column",{attrs:{label:"产品单位",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.itemunit))])]}}])}),a("el-table-column",{attrs:{label:"数量",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.quantity))])]}}])}),a("el-table-column",{attrs:{label:"第二单位",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.subunit))])]}}])}),a("el-table-column",{attrs:{label:"第二数量",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.subqty))])]}}])}),a("el-table-column",{attrs:{label:"订单号",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.custpo))])]}}])}),a("el-table-column",{attrs:{label:"批号",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.lotnumber))])]}}])}),a("el-table-column",{attrs:{label:"仓库",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.storename))])]}}])}),a("el-table-column",{attrs:{label:"库位",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.cargospace))])]}}])}),a("el-table-column",{attrs:{label:"业务员",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.salesman))])]}}])}),a("el-table-column",{attrs:{label:"检查员",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.inspector))])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)],1)],1)],1),a("el-dialog",{attrs:{title:"标签导入",visible:t.ExportVisible,width:"500px"},on:{"update:visible":function(e){t.ExportVisible=e}}},[a("div",[a("Export",{ref:"exportFile",on:{closeDialog:t.closeDialog}})],1),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.submitExPort()}}},[t._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.ExportVisible=!1}}},[t._v("取 消")])],1)]),a("el-dialog",{attrs:{title:"打印模板","append-to-body":!0,width:"400px",visible:t.ReportVisible,"close-on-click-modal":!1},on:{"update:visible":function(e){t.ReportVisible=e}}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择打印模板"},model:{value:t.reportModel,callback:function(e){t.reportModel=e},expression:"reportModel"}},t._l(t.ReportData,(function(t){return a("el-option",{key:t.id,attrs:{label:t.rptname,value:t.id}})})),1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitReport}},[t._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.ReportVisible=!1}}},[t._v("取 消")])],1)],1),a("el-dialog",{attrs:{title:"标签预览",width:"80%",visible:t.isViewPdf20,top:"4vh","close-on-click-modal":!1},on:{"update:visible":function(e){t.isViewPdf20=e}}},[a("iframe",{staticStyle:{width:"100%",height:"90vh"},attrs:{src:t.pdfUrl,frameborder:"0"}})])],1)},l=[],i=(a("4d90"),a("d3b7"),a("25f0"),a("99af"),a("e9c4"),a("3ca3"),a("ddb0"),a("2b3d"),a("9861"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{class:t.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[a("div",[a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnSearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnSearch},slot:"append"},[t._v("搜索 ")])],1),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-plus",plain:"",size:"mini"},on:{click:t.btnAdd}},[t._v(" 添加 ")]),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-printer",plain:"",size:"mini"},on:{click:t.btnPrint}},[t._v(" 标签打印 ")]),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"info",icon:"el-icon-upload2",plain:"",size:"mini"},on:{click:t.btnImport}},[t._v(" 导入 ")])],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:t.btnExport}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right"},on:{click:t.bindData}}),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{icon:t.iShow?"el-icon-arrow-up":"el-icon-arrow-down",size:"mini",title:"高级搜索"},on:{click:function(e){t.iShow=!t.iShow}}})],1)]),a("el-collapse-transition",[a("div",{directives:[{name:"show",rawName:"v-show",value:t.iShow,expression:"iShow"}],staticClass:"searchDetail"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px","auto-complete":"on"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"编码"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入编码",size:"small"},model:{value:t.formdata.refno,callback:function(e){t.$set(t.formdata,"refno",e)},expression:"formdata.refno"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"单据类型"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入单据类型",size:"small"},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"供应商"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入供应商",size:"small"},model:{value:t.formdata.supplier,callback:function(e){t.$set(t.formdata,"supplier",e)},expression:"formdata.supplier"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"客户"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入客户",size:"small"},model:{value:t.formdata.customer,callback:function(e){t.$set(t.formdata,"customer",e)},expression:"formdata.customer"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px","margin-top":"5px"},attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.advancedSearch()}}},[t._v(" 搜索 ")])],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"产品编码"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入产品编码",size:"small"},model:{value:t.formdata.itemcode,callback:function(e){t.$set(t.formdata,"itemcode",e)},expression:"formdata.itemcode"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"产品名称"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入产品名称",size:"small"},model:{value:t.formdata.itemname,callback:function(e){t.$set(t.formdata,"itemname",e)},expression:"formdata.itemname"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"产品规格"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入产品规格",size:"small"},model:{value:t.formdata.itemspec,callback:function(e){t.$set(t.formdata,"itemspec",e)},expression:"formdata.itemspec"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"产品单位"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入产品单位",size:"small"},model:{value:t.formdata.itemunit,callback:function(e){t.$set(t.formdata,"itemunit",e)},expression:"formdata.itemunit"}})],1)],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"订单号"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入订单号",size:"small"},model:{value:t.formdata.custpo,callback:function(e){t.$set(t.formdata,"custpo",e)},expression:"formdata.custpo"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"批号"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入批号",size:"small"},model:{value:t.formdata.lotnumber,callback:function(e){t.$set(t.formdata,"lotnumber",e)},expression:"formdata.lotnumber"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"仓库"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入仓库",size:"small"},model:{value:t.formdata.storename,callback:function(e){t.$set(t.formdata,"storename",e)},expression:"formdata.storename"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"库位"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入库位",size:"small"},model:{value:t.formdata.cargospace,callback:function(e){t.$set(t.formdata,"cargospace",e)},expression:"formdata.cargospace"}})],1)],1)],1)],1)],1)])],1)}),n=[],r={name:"Listheader",data:function(){return{strfilter:"",iShow:!1,formdata:{}}},methods:{advancedSearch:function(){this.iShow=!1,this.$emit("advancedSearch",this.formdata)},btnPrint:function(){this.$emit("btnPrint")},btnExport:function(){this.$emit("btnExport")},modelExport:function(){this.$emit("modelExport")},btnImport:function(){this.$emit("btnImport")},btnAdd:function(){this.$emit("btnAdd")},btnSearch:function(){this.$emit("btnSearch",this.strfilter)},bindData:function(){this.$emit("bindData")}}},s=r,c=(a("63b9"),a("2877")),d=Object(c["a"])(s,i,n,!1,null,"fe2d729e",null),m=d.exports,u=a("333d"),p=a("b775"),f=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",size:"small"},nativeOn:{click:function(e){return t.submitForm("formdata")}}},[t._v(" 保 存")]),a("el-button",{attrs:{disabled:!t.formdata.id,type:"primary",size:"small"},nativeOn:{click:function(e){return t.printButton()}}},[t._v("打 印")]),a("el-dropdown",{staticStyle:{margin:"0 10px"},attrs:{trigger:"click",placement:"bottom"}},[a("el-button",{attrs:{size:"small"}},[t._v("操作"),a("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{icon:"el-icon-delete",disabled:!t.formdata.id},nativeOn:{click:function(e){return t.deleteForm(t.idx)}}},[t._v("删 除")])],1)],1),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.closeForm(e)}}},[t._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("div",{staticClass:"flex j-end p-a",staticStyle:{top:"0px",right:"0"}},[a("div",{staticClass:"refNo flex j-end"},[a("div",{staticClass:"refNo-item"},[a("span",{staticStyle:{"margin-right":"10px"}},[t._v(" 日期： "),a("el-date-picker",{staticStyle:{width:"150px"},attrs:{type:"date",placeholder:"选择日期",size:"mini"},model:{value:t.formdata.billdate,callback:function(e){t.$set(t.formdata,"billdate",e)},expression:"formdata.billdate"}})],1),a("div",[a("span",[t._v("NO：")]),a("el-input",{staticStyle:{width:"150px"},attrs:{placeholder:"请输入单号",size:"mini",readonly:!!t.formdata.refno},model:{value:t.formdata.refno,callback:function(e){t.$set(t.formdata,"refno",e)},expression:"formdata.refno"}})],1)])])]),a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth,rules:t.formRules}},[a("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),a("el-divider",{attrs:{"content-position":"left"}},[t._v("基础信息")]),a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"单据类型"}},[a("el-input",{attrs:{placeholder:"请输入单据类型",clearable:"",size:"small"},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"单据标题",prop:"billtitle"}},[a("el-input",{attrs:{placeholder:"请输入单据标题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("supplier")}}},[a("el-form-item",{attrs:{label:"供应商",prop:"supplier"}},[a("el-input",{attrs:{placeholder:"请输入供应商",size:"small"},model:{value:t.formdata.supplier,callback:function(e){t.$set(t.formdata,"supplier",e)},expression:"formdata.supplier"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("customer")}}},[a("el-form-item",{attrs:{label:"客户",prop:"customer"}},[a("el-input",{attrs:{placeholder:"请输入客户",size:"small"},model:{value:t.formdata.customer,callback:function(e){t.$set(t.formdata,"customer",e)},expression:"formdata.customer"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("itemcode")}}},[a("el-form-item",{attrs:{label:"产品编码",prop:"itemcode"}},[a("el-input",{attrs:{placeholder:"请输入产品编码",size:"small"},model:{value:t.formdata.itemcode,callback:function(e){t.$set(t.formdata,"itemcode",e)},expression:"formdata.itemcode"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("itemname")}}},[a("el-form-item",{attrs:{label:"产品名称",prop:"itemname"}},[a("el-input",{attrs:{placeholder:"请输入产品名称",size:"small"},model:{value:t.formdata.itemname,callback:function(e){t.$set(t.formdata,"itemname",e)},expression:"formdata.itemname"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"产品规格"}},[a("el-input",{attrs:{placeholder:"请输入产品规格",size:"small"},model:{value:t.formdata.itemspec,callback:function(e){t.$set(t.formdata,"itemspec",e)},expression:"formdata.itemspec"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"产品单位"}},[a("el-input",{attrs:{placeholder:"请输入产品单位",size:"small"},model:{value:t.formdata.itemunit,callback:function(e){t.$set(t.formdata,"itemunit",e)},expression:"formdata.itemunit"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"数量"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!0,type:"number",min:0,"controls-position":"right",size:"small"},model:{value:t.formdata.quantity,callback:function(e){t.$set(t.formdata,"quantity",e)},expression:"formdata.quantity"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"第二单位"}},[a("el-input",{attrs:{placeholder:"请输入第二单位",size:"small"},model:{value:t.formdata.subunit,callback:function(e){t.$set(t.formdata,"subunit",e)},expression:"formdata.subunit"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"第二数量"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!0,type:"number",min:0,"controls-position":"right",size:"small"},model:{value:t.formdata.subqty,callback:function(e){t.$set(t.formdata,"subqty",e)},expression:"formdata.subqty"}})],1)],1)],1)],1),a("el-divider",{attrs:{"content-position":"left"}},[t._v("详细信息")]),a("el-form",{staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth}},[a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"订单号"}},[a("el-input",{attrs:{placeholder:"请输入订单号",size:"small"},model:{value:t.formdata.custpo,callback:function(e){t.$set(t.formdata,"custpo",e)},expression:"formdata.custpo"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"检查员"}},[a("el-input",{attrs:{placeholder:"请输入检查员",size:"small"},model:{value:t.formdata.inspector,callback:function(e){t.$set(t.formdata,"inspector",e)},expression:"formdata.inspector"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"批号"}},[a("el-input",{attrs:{placeholder:"请输入批号",size:"small"},model:{value:t.formdata.lotnumber,callback:function(e){t.$set(t.formdata,"lotnumber",e)},expression:"formdata.lotnumber"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"仓库"}},[a("el-input",{attrs:{placeholder:"请输入仓库",size:"small"},model:{value:t.formdata.storename,callback:function(e){t.$set(t.formdata,"storename",e)},expression:"formdata.storename"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"库位"}},[a("el-input",{attrs:{placeholder:"请输入库位",size:"small"},model:{value:t.formdata.cargospace,callback:function(e){t.$set(t.formdata,"cargospace",e)},expression:"formdata.cargospace"}})],1)],1)],1)],1),a("el-divider",{attrs:{"content-position":"left"}},[t._v("其他信息")])],1),a("el-form",{staticClass:"footFormContent",attrs:{"label-width":t.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:18}},[a("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"经办人"}},[a("el-input",{attrs:{placeholder:"请输入经办人",size:"small"},model:{value:t.formdata.operator,callback:function(e){t.$set(t.formdata,"operator",e)},expression:"formdata.operator"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.modifydate)))])])],1)],1)],1)],1)]),a("el-dialog",{attrs:{title:"打印模板","append-to-body":!0,width:"400px",visible:t.ReportVisible,"destroy-on-close":!0},on:{"update:visible":function(e){t.ReportVisible=e}}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择打印模板"},model:{value:t.reportModel,callback:function(e){t.reportModel=e},expression:"reportModel"}},t._l(t.ReportData,(function(t){return a("el-option",{key:t.id,attrs:{label:t.rptname,value:t.id}})})),1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitReport}},[t._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.ReportVisible=!1}}},[t._v("取 消")])],1)],1),a("el-dialog",{staticClass:"pdfDialog",attrs:{"append-to-body":!0,width:"80%",visible:t.isViewPdf20,top:"2vh","close-on-click-modal":!1},on:{"update:visible":function(e){t.isViewPdf20=e}}},[a("iframe",{staticStyle:{width:"100%",height:"90vh"},attrs:{src:t.pdfUrl,frameborder:"0"}})])],1)},b=[],h=a("c7eb"),w=a("1da1"),v=a("ade3");a("b64b");const g={add(t){return new Promise((e,a)=>{var o=JSON.stringify(t);p["a"].post("/S16M04B1/create",o).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},update(t){return new Promise((e,a)=>{var o=JSON.stringify(t);p["a"].post("/S16M04B1/update",o).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},delete(t){return new Promise((e,a)=>{p["a"].get("/S16M04B1/delete?key="+t).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})}};var x=g,y=(a("6ca8"),{name:"Formedit",components:{},filters:{dateFormat:function(t){if(t){var e=new Date(t),a=e.getFullYear(),o=(e.getMonth()+1).toString().padStart(2,"0"),l=e.getDate().toString().padStart(2,"0"),i=e.getHours().toString().padStart(2,"0"),n=e.getMinutes().toString().padStart(2,"0"),r=e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(o,"-").concat(l," ").concat(i,":").concat(n,":").concat(r)}}},props:["idx"],data:function(){return Object(v["a"])(Object(v["a"])(Object(v["a"])(Object(v["a"])(Object(v["a"])(Object(v["a"])(Object(v["a"])(Object(v["a"])(Object(v["a"])({title:"标签信息",formdata:{billdate:new Date,billtitle:"",billtype:"",cargospace:"",customer:"",custpo:"",inspector:"",itemcode:"",itemname:"",itemspec:"",itemunit:"",lotnumber:"",operator:"",quantity:0,refno:"",remark:"",rownum:0,salesman:"",storename:"",subqty:0,subunit:"",supplier:"",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname},formRules:{supplier:[{required:!0,trigger:"blur",message:"供应商为必填项"}],customer:[{required:!0,trigger:"blur",message:"客户为必填项"}],itemcode:[{required:!0,trigger:"blur",message:"产品编码为必填项"}],itemname:[{required:!0,trigger:"blur",message:"产品名称为必填项"}]},multi:0,selVisible:!1,formLabelWidth:"100px",formheight:"500px",finshDialogVisible:!1},"formLabelWidth","100px"),"enActive",!1),"visable",!1),"disabled",!1),"ReportVisible",!1),"ReportData",[]),"reportModel",""),"pdfUrl",""),"isViewPdf20",!1)},computed:{formcontainHeight:function(){return window.innerHeight-50-23-50+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindData()},methods:{bindData:function(){var t=this;return Object(w["a"])(Object(h["a"])().mark((function e(){return Object(h["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.listLoading=!0,0==t.idx){e.next=4;break}return e.next=4,p["a"].get("/S16M04B1/getEntity?key=".concat(t.idx)).then((function(e){console.log("===========",e),200==e.data.code&&(t.formdata=e.data.data),t.listLoading=!1})).catch((function(e){t.listLoading=!1,t.$message.error("请求错误")}));case 4:case"end":return e.stop()}}),e)})))()},submitForm:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;0==this.idx?x.add(this.formdata).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.bindData())})).catch((function(e){t.$message.warning("保存失败")})):x.update(this.formdata).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("bindData"),t.bindData())})).catch((function(e){t.$message.warning("保存失败")}))},closeForm:function(){this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){console.log("执行删除"),x.delete(t).then((function(t){200==t.code&&e.$message.success("删除成功"),e.$emit("compForm")})).catch((function(){e.$message.warning("删除失败")}))})).catch((function(){}))},printButton:function(){var t=this;p["a"].get("/SaReports/getListByModuleCode?code=S16M04B1Edit").then((function(e){t.ReportData=e.data.data,0!=t.ReportData.length&&(t.reportModel=t.ReportData[0].id),t.ReportVisible=!0}))},submitReport:function(){var t=this;""!=this.reportModel?p["a"].get("/S16M04B1/printBill?key="+this.idx+"&ptid="+this.reportModel,{responseType:"blob"}).then((function(e){t.formdata.printcount+=1,t.ReportVisible=!1;var a=[];a.push(e.data);var o=window.URL.createObjectURL(new Blob(a,{type:"application/pdf"}));t.pdfUrl=o,t.isViewPdf20=!0})):this.$message.warning("打印模板不能为空!")},handleFocus:function(){this.selVisible=!0},handleBlur:function(){this.selVisible=!1},cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}}),_=y,S=(a("fac0"),Object(c["a"])(_,f,b,!1,null,"19445e05",null)),k=S.exports,$=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("el-upload",{ref:"upload",staticClass:"upload-demo",attrs:{drag:!0,action:"",multiple:!1,"on-change":t.handleChange,"on-remove":t.handleRemove,"on-preview":t.handlePreview,"on-success":t.handleSuccess,limit:t.limitUpload,"auto-upload":!1,accept:".xlsx,.xls,.csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"}},[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[t._v("上传文件")]),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[t._v("只能上传xlsx / xls文件 或 "),a("span",{staticClass:"tipSpan",on:{click:t.modelExport}},[t._v("下载模板")])])]),a("el-dialog",{attrs:{title:"标签信息",width:"80vw",visible:t.staffVisible,"modal-append-to-body":!1,"append-to-body":!0,"close-on-click-modal":!1},on:{"update:visible":function(e){t.staffVisible=e}}},[a("div",[a("el-table",{ref:"multipleTable",staticClass:"tb-edit tableBox",staticStyle:{overflow:"auto","min-height":"500px"},attrs:{data:t.staffData,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"",size:"small","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"4px 0px 4px 0px"},"cell-style":{padding:"0px 0px"},"row-style":{height:"40px"}}},[a("el-table-column",{attrs:{align:"center",label:"序号",width:"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.$index+1)+" ")]}}])}),a("el-table-column",{attrs:{label:"单据日期",align:"center","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("dateFormat")(e.row.billdate)))])]}}])}),a("el-table-column",{attrs:{label:"供应商",align:"center","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.supplier))])]}}])}),a("el-table-column",{attrs:{label:"客户",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.customer))])]}}])}),a("el-table-column",{attrs:{label:"产品编码",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.itemcode))])]}}])}),a("el-table-column",{attrs:{label:"产品名称",align:"center","min-width":"60","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.itemname))])]}}])}),a("el-table-column",{attrs:{label:"产品规格",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.itemspec))])]}}])}),a("el-table-column",{attrs:{label:"产品单位",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.itemunit))])]}}])}),a("el-table-column",{attrs:{label:"数量",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.quantity))])]}}])}),a("el-table-column",{attrs:{label:"第二单位",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.subunit))])]}}])}),a("el-table-column",{attrs:{label:"第二数量",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.subqty))])]}}])}),a("el-table-column",{attrs:{label:"订单号",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.custpo))])]}}])}),a("el-table-column",{attrs:{label:"批号",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.lotnumber))])]}}])}),a("el-table-column",{attrs:{label:"仓库",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.storename))])]}}])}),a("el-table-column",{attrs:{label:"库位",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.cargospace))])]}}])}),a("el-table-column",{attrs:{label:"业务员",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.salesman))])]}}])}),a("el-table-column",{attrs:{label:"检查员",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.inspector))])]}}])})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitStaff}},[t._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.staffVisible=!1}}},[t._v("取 消")])],1)])],1)},z=[],D=(a("b0c0"),{data:function(){return{limitUpload:1,fileTemp:{},staffVisible:!1,staffData:[]}},methods:{handlePreview:function(t){console.log(t)},handleChange:function(t,e){this.fileTemp=t.raw;var a=t.raw.name,o=a.substring(a.lastIndexOf(".")+1);this.fileTemp?"xlsx"==o||"xls"==o||this.$message({type:"warning",message:"文件格式错误，请删除后重新上传！"}):this.$message({type:"warning",message:"请上传文件！"})},submitStaff:function(){var t=this;return Object(w["a"])(Object(h["a"])().mark((function e(){var a,o,l,i;return Object(h["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:for(a=t,console.log("上传-保存数据库"),o=[],l=0;l<t.staffData.length;l++)i=new Promise((function(e,a){x.add(t.staffData[l]).then((function(t){200==t.code?e("保存成功"):a("保存失败")})).catch((function(t){a("保存失败")}))})),o.push(i);return e.next=6,Promise.all(o).then((function(e){a.$message.success("保存成功"),t.staffVisible=!1,t.$emit("closeDialog")})).catch((function(t){a.$message.warning("保存失败")}));case 6:case"end":return e.stop()}}),e)})))()},importf:function(t){var e=this,a=(this.$refs.inputer,this.fileTemp),o=new FormData;o.append("file",a),p["a"].post("/S16M04B1/importExecl",o,{headers:{"Content-Type":"multipart/form-data"}}).then((function(t){console.log("文件导入",t),200==t.data.code?(e.$message.success("文件导入成功"),e.staffVisible=!0,e.staffData=t.data.data):e.$message.warning("文件导入失败，请重试")}))},modelExport:function(){p["a"].get("/S16M04B1/exportModel",{responseType:"blob"}).then((function(t){var e=document.createElement("a"),a=new Blob([t.data],{type:"application/vnd.ms-excel"});e.style.display="none",e.href=URL.createObjectURL(a),e.download="标签信息模板",document.body.appendChild(e),e.click()})).catch((function(t){console.log(t)}))},handleSuccess:function(t,e){console.log("handleSuccess",t)},handleRemove:function(t,e){console.log("handleRemove",t)}},filters:{dateFormat:function(t){if(t){var e=new Date(t),a=e.getFullYear(),o=(e.getMonth()+1).toString().padStart(2,"0"),l=e.getDate().toString().padStart(2,"0");return"".concat(a,"-").concat(o,"-").concat(l," ")}}}}),C=D,P=(a("a244"),Object(c["a"])(C,$,z,!1,null,"4cca0ad6",null)),R=P.exports,F={name:"",components:{Pagination:u["a"],listheader:m,formedit:k,Export:R},filters:{dateFormat:function(t){if(t){var e=new Date(t),a=e.getFullYear(),o=(e.getMonth()+1).toString().padStart(2,"0"),l=e.getDate().toString().padStart(2,"0");return"".concat(a,"-").concat(o,"-").concat(l)}}},data:function(){return{lst:[],formvisible:!1,listLoading:!1,idx:0,total:0,searchstr:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1},ExportVisible:!1,ReportVisible:!1,ReportData:[],reportModel:"",pdfUrl:"",isViewPdf20:!1,selectList:[]}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},watch:{},created:function(){this.searchstr="",this.bindData()},methods:{bindData:function(){var t=this;this.listLoading=!0,p["a"].post("/S16M04B1/getPageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},handleSelectionChange:function(t){console.log(t),this.selectList=t},btnPrint:function(){var t=this;0!=this.selectList.length?p["a"].get("/SaReports/getListByModuleCode?code=S16M04B1Label").then((function(e){t.ReportData=e.data.data,0!=t.ReportData.length&&(t.reportModel=t.ReportData[0].id,0!=t.ReportData.length&&(t.reportModel=t.ReportData[0].id)),t.ReportVisible=!0})):this.$message.warning("标签内容不能为空!")},submitReport:function(){var t=this;""!=this.reportModel?(console.log(window.location.host,this.reportModel),p["a"].post("/S16M04B1/printList?ptid="+this.reportModel+"&qrcodeurl="+window.location.host,JSON.stringify(this.selectList),{responseType:"blob"}).then((function(e){t.$refs.pageTable.clearSelection(),t.selectList=[],t.ReportVisible=!1;var a=[];a.push(e.data);var o=window.URL.createObjectURL(new Blob(a,{type:"application/pdf"}));t.pdfUrl=o,t.isViewPdf20=!0}))):this.$message.warning("打印模板不能为空!")},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){""!=t?this.queryParams.SearchPojo={refno:t,itemcode:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},advancedSearch:function(t){this.queryParams.SearchPojo=t,this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},btnImport:function(){this.ExportVisible=!0},closeDialog:function(){this.ExportVisible=!1,this.bindData()},submitExPort:function(){console.log(this.$refs.exportFile),this.$refs.exportFile.importf()},btnExport:function(){p["a"].post("/S16M04B1/exportList",{params:JSON.stringify(this.queryParams)},{responseType:"blob"}).then((function(t){var e=document.createElement("a"),a=new Blob([t.data],{type:"application/vnd.ms-excel"});e.style.display="none",e.href=URL.createObjectURL(a),e.download="标签信息",document.body.appendChild(e),e.click()})).catch((function(t){console.log(t)}))},modelExport:function(){p["a"].get("/S16M04B1/exportModel",{responseType:"blob"}).then((function(t){var e=document.createElement("a"),a=new Blob([t.data],{type:"application/vnd.ms-excel"});e.style.display="none",e.href=URL.createObjectURL(a),e.download="标签信息模板",document.body.appendChild(e),e.click()})).catch((function(t){console.log(t)}))},changeIdx:function(t){this.idx=t}}},V=F,L=(a("e6c9"),Object(c["a"])(V,o,l,!1,null,"24c5ca7c",null));e["default"]=L.exports},fac0:function(t,e,a){"use strict";a("0358")}}]);