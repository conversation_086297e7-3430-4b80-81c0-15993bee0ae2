(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-01f2f9f1"],{"0050":function(t,e,r){"use strict";(function(e){function r(t){return t instanceof e?e.from(t):new t.constructor(t.buffer.slice(),t.byteOffset,t.length)}function n(t){return t=t||{},t.circles?i(t):t.proto?o:n;function e(t,e){for(var n=Object.keys(t),i=new Array(n.length),o=0;o<n.length;o++){var s=n[o],a=t[s];"object"!==typeof a||null===a?i[s]=a:a instanceof Date?i[s]=new Date(a):ArrayBuffer.isView(a)?i[s]=r(a):i[s]=e(a)}return i}function n(t){if("object"!==typeof t||null===t)return t;if(t instanceof Date)return new Date(t);if(Array.isArray(t))return e(t,n);if(t instanceof Map)return new Map(e(Array.from(t),n));if(t instanceof Set)return new Set(e(Array.from(t),n));var i={};for(var o in t)if(!1!==Object.hasOwnProperty.call(t,o)){var s=t[o];"object"!==typeof s||null===s?i[o]=s:s instanceof Date?i[o]=new Date(s):s instanceof Map?i[o]=new Map(e(Array.from(s),n)):s instanceof Set?i[o]=new Set(e(Array.from(s),n)):ArrayBuffer.isView(s)?i[o]=r(s):i[o]=n(s)}return i}function o(t){if("object"!==typeof t||null===t)return t;if(t instanceof Date)return new Date(t);if(Array.isArray(t))return e(t,o);if(t instanceof Map)return new Map(e(Array.from(t),o));if(t instanceof Set)return new Set(e(Array.from(t),o));var n={};for(var i in t){var s=t[i];"object"!==typeof s||null===s?n[i]=s:s instanceof Date?n[i]=new Date(s):s instanceof Map?n[i]=new Map(e(Array.from(s),o)):s instanceof Set?n[i]=new Set(e(Array.from(s),o)):ArrayBuffer.isView(s)?n[i]=r(s):n[i]=o(s)}return n}}function i(t){var e=[],n=[];return t.proto?s:o;function i(t,i){for(var o=Object.keys(t),s=new Array(o.length),a=0;a<o.length;a++){var c=o[a],u=t[c];if("object"!==typeof u||null===u)s[c]=u;else if(u instanceof Date)s[c]=new Date(u);else if(ArrayBuffer.isView(u))s[c]=r(u);else{var l=e.indexOf(u);s[c]=-1!==l?n[l]:i(u)}}return s}function o(t){if("object"!==typeof t||null===t)return t;if(t instanceof Date)return new Date(t);if(Array.isArray(t))return i(t,o);if(t instanceof Map)return new Map(i(Array.from(t),o));if(t instanceof Set)return new Set(i(Array.from(t),o));var s={};for(var a in e.push(t),n.push(s),t)if(!1!==Object.hasOwnProperty.call(t,a)){var c=t[a];if("object"!==typeof c||null===c)s[a]=c;else if(c instanceof Date)s[a]=new Date(c);else if(c instanceof Map)s[a]=new Map(i(Array.from(c),o));else if(c instanceof Set)s[a]=new Set(i(Array.from(c),o));else if(ArrayBuffer.isView(c))s[a]=r(c);else{var u=e.indexOf(c);s[a]=-1!==u?n[u]:o(c)}}return e.pop(),n.pop(),s}function s(t){if("object"!==typeof t||null===t)return t;if(t instanceof Date)return new Date(t);if(Array.isArray(t))return i(t,s);if(t instanceof Map)return new Map(i(Array.from(t),s));if(t instanceof Set)return new Set(i(Array.from(t),s));var o={};for(var a in e.push(t),n.push(o),t){var c=t[a];if("object"!==typeof c||null===c)o[a]=c;else if(c instanceof Date)o[a]=new Date(c);else if(c instanceof Map)o[a]=new Map(i(Array.from(c),s));else if(c instanceof Set)o[a]=new Set(i(Array.from(c),s));else if(ArrayBuffer.isView(c))o[a]=r(c);else{var u=e.indexOf(c);o[a]=-1!==u?n[u]:s(c)}}return e.pop(),n.pop(),o}}t.exports=n}).call(this,r("b639").Buffer)},"00bb":function(t,e,r){(function(e,n,i){t.exports=n(r("21bf"),r("38ba"))})(0,(function(t){return t.mode.CFB=function(){var e=t.lib.BlockCipherMode.extend();function r(t,e,r,n){var i,o=this._iv;o?(i=o.slice(0),this._iv=void 0):i=this._prevBlock,n.encryptBlock(i,0);for(var s=0;s<r;s++)t[e+s]^=i[s]}return e.Encryptor=e.extend({processBlock:function(t,e){var n=this._cipher,i=n.blockSize;r.call(this,t,e,i,n),this._prevBlock=t.slice(e,e+i)}}),e.Decryptor=e.extend({processBlock:function(t,e){var n=this._cipher,i=n.blockSize,o=t.slice(e,e+i);r.call(this,t,e,i,n),this._prevBlock=o}}),e}(),t.mode.CFB}))},"00ce":function(t,e,r){"use strict";var n,i=SyntaxError,o=Function,s=TypeError,a=function(t){try{return o('"use strict"; return ('+t+").constructor;")()}catch(e){}},c=Object.getOwnPropertyDescriptor;if(c)try{c({},"")}catch(C){c=null}var u=function(){throw new s},l=c?function(){try{return u}catch(t){try{return c(arguments,"callee").get}catch(e){return u}}}():u,h=r("5156")(),f=r("0a36")(),p=Object.getPrototypeOf||(f?function(t){return t.__proto__}:null),d={},y="undefined"!==typeof Uint8Array&&p?p(Uint8Array):n,b={"%AggregateError%":"undefined"===typeof AggregateError?n:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"===typeof ArrayBuffer?n:ArrayBuffer,"%ArrayIteratorPrototype%":h&&p?p([][Symbol.iterator]()):n,"%AsyncFromSyncIteratorPrototype%":n,"%AsyncFunction%":d,"%AsyncGenerator%":d,"%AsyncGeneratorFunction%":d,"%AsyncIteratorPrototype%":d,"%Atomics%":"undefined"===typeof Atomics?n:Atomics,"%BigInt%":"undefined"===typeof BigInt?n:BigInt,"%BigInt64Array%":"undefined"===typeof BigInt64Array?n:BigInt64Array,"%BigUint64Array%":"undefined"===typeof BigUint64Array?n:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"===typeof DataView?n:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":Error,"%eval%":eval,"%EvalError%":EvalError,"%Float32Array%":"undefined"===typeof Float32Array?n:Float32Array,"%Float64Array%":"undefined"===typeof Float64Array?n:Float64Array,"%FinalizationRegistry%":"undefined"===typeof FinalizationRegistry?n:FinalizationRegistry,"%Function%":o,"%GeneratorFunction%":d,"%Int8Array%":"undefined"===typeof Int8Array?n:Int8Array,"%Int16Array%":"undefined"===typeof Int16Array?n:Int16Array,"%Int32Array%":"undefined"===typeof Int32Array?n:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":h&&p?p(p([][Symbol.iterator]())):n,"%JSON%":"object"===typeof JSON?JSON:n,"%Map%":"undefined"===typeof Map?n:Map,"%MapIteratorPrototype%":"undefined"!==typeof Map&&h&&p?p((new Map)[Symbol.iterator]()):n,"%Math%":Math,"%Number%":Number,"%Object%":Object,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"===typeof Promise?n:Promise,"%Proxy%":"undefined"===typeof Proxy?n:Proxy,"%RangeError%":RangeError,"%ReferenceError%":ReferenceError,"%Reflect%":"undefined"===typeof Reflect?n:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"===typeof Set?n:Set,"%SetIteratorPrototype%":"undefined"!==typeof Set&&h&&p?p((new Set)[Symbol.iterator]()):n,"%SharedArrayBuffer%":"undefined"===typeof SharedArrayBuffer?n:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":h&&p?p(""[Symbol.iterator]()):n,"%Symbol%":h?Symbol:n,"%SyntaxError%":i,"%ThrowTypeError%":l,"%TypedArray%":y,"%TypeError%":s,"%Uint8Array%":"undefined"===typeof Uint8Array?n:Uint8Array,"%Uint8ClampedArray%":"undefined"===typeof Uint8ClampedArray?n:Uint8ClampedArray,"%Uint16Array%":"undefined"===typeof Uint16Array?n:Uint16Array,"%Uint32Array%":"undefined"===typeof Uint32Array?n:Uint32Array,"%URIError%":URIError,"%WeakMap%":"undefined"===typeof WeakMap?n:WeakMap,"%WeakRef%":"undefined"===typeof WeakRef?n:WeakRef,"%WeakSet%":"undefined"===typeof WeakSet?n:WeakSet};if(p)try{null.error}catch(C){var g=p(p(C));b["%Error.prototype%"]=g}var m=function t(e){var r;if("%AsyncFunction%"===e)r=a("async function () {}");else if("%GeneratorFunction%"===e)r=a("function* () {}");else if("%AsyncGeneratorFunction%"===e)r=a("async function* () {}");else if("%AsyncGenerator%"===e){var n=t("%AsyncGeneratorFunction%");n&&(r=n.prototype)}else if("%AsyncIteratorPrototype%"===e){var i=t("%AsyncGenerator%");i&&p&&(r=p(i.prototype))}return b[e]=r,r},v={"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},_=r("0f7c"),w=r("9671"),S=_.call(Function.call,Array.prototype.concat),E=_.call(Function.apply,Array.prototype.splice),k=_.call(Function.call,String.prototype.replace),A=_.call(Function.call,String.prototype.slice),x=_.call(Function.call,RegExp.prototype.exec),P=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,O=/\\(\\)?/g,R=function(t){var e=A(t,0,1),r=A(t,-1);if("%"===e&&"%"!==r)throw new i("invalid intrinsic syntax, expected closing `%`");if("%"===r&&"%"!==e)throw new i("invalid intrinsic syntax, expected opening `%`");var n=[];return k(t,P,(function(t,e,r,i){n[n.length]=r?k(i,O,"$1"):e||t})),n},M=function(t,e){var r,n=t;if(w(v,n)&&(r=v[n],n="%"+r[0]+"%"),w(b,n)){var o=b[n];if(o===d&&(o=m(n)),"undefined"===typeof o&&!e)throw new s("intrinsic "+t+" exists, but is not available. Please file an issue!");return{alias:r,name:n,value:o}}throw new i("intrinsic "+t+" does not exist!")};t.exports=function(t,e){if("string"!==typeof t||0===t.length)throw new s("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!==typeof e)throw new s('"allowMissing" argument must be a boolean');if(null===x(/^%?[^%]*%?$/,t))throw new i("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var r=R(t),n=r.length>0?r[0]:"",o=M("%"+n+"%",e),a=o.name,u=o.value,l=!1,h=o.alias;h&&(n=h[0],E(r,S([0,1],h)));for(var f=1,p=!0;f<r.length;f+=1){var d=r[f],y=A(d,0,1),g=A(d,-1);if(('"'===y||"'"===y||"`"===y||'"'===g||"'"===g||"`"===g)&&y!==g)throw new i("property names with quotes must have matching quotes");if("constructor"!==d&&p||(l=!0),n+="."+d,a="%"+n+"%",w(b,a))u=b[a];else if(null!=u){if(!(d in u)){if(!e)throw new s("base intrinsic for "+t+" exists, but the property is not available.");return}if(c&&f+1>=r.length){var m=c(u,d);p=!!m,u=p&&"get"in m&&!("originalValue"in m.get)?m.get:u[d]}else p=w(u,d),u=u[d];p&&!l&&(b[a]=u)}}return u}},"035d":function(t,e,r){e=t.exports=r("85f8"),e.Stream=e,e.Readable=e,e.Writable=r("13a8"),e.Duplex=r("be3f"),e.Transform=r("3ca2"),e.PassThrough=r("7058"),e.finished=r("d9e1"),e.pipeline=r("652a")},"05ee":function(t,e,r){"use strict";const n=r(1),i=r("34eb")("mqttjs:tcp");function o(t,e){e.port=e.port||1883,e.hostname=e.hostname||e.host||"localhost";const r=e.port,o=e.hostname;return i("port %d and host %s",r,o),n.createConnection(r,o)}t.exports=o},"0a36":function(t,e,r){"use strict";var n={foo:{}},i=Object;t.exports=function(){return{__proto__:n}.foo===n.foo&&!({__proto__:null}instanceof i)}},"0b16":function(t,e,r){"use strict";var n=r("1985");function i(){this.protocol=null,this.slashes=null,this.auth=null,this.host=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.query=null,this.pathname=null,this.path=null,this.href=null}var o=/^([a-z0-9.+-]+:)/i,s=/:[0-9]*$/,a=/^(\/\/?(?!\/)[^?\s]*)(\?[^\s]*)?$/,c=["<",">",'"',"`"," ","\r","\n","\t"],u=["{","}","|","\\","^","`"].concat(c),l=["'"].concat(u),h=["%","/","?",";","#"].concat(l),f=["/","?","#"],p=255,d=/^[+a-z0-9A-Z_-]{0,63}$/,y=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,b={javascript:!0,"javascript:":!0},g={javascript:!0,"javascript:":!0},m={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0},v=r("184d");function _(t,e,r){if(t&&"object"===typeof t&&t instanceof i)return t;var n=new i;return n.parse(t,e,r),n}function w(t){return"string"===typeof t&&(t=_(t)),t instanceof i?t.format():i.prototype.format.call(t)}function S(t,e){return _(t,!1,!0).resolve(e)}function E(t,e){return t?_(t,!1,!0).resolveObject(e):e}i.prototype.parse=function(t,e,r){if("string"!==typeof t)throw new TypeError("Parameter 'url' must be a string, not "+typeof t);var i=t.indexOf("?"),s=-1!==i&&i<t.indexOf("#")?"?":"#",c=t.split(s),u=/\\/g;c[0]=c[0].replace(u,"/"),t=c.join(s);var _=t;if(_=_.trim(),!r&&1===t.split("#").length){var w=a.exec(_);if(w)return this.path=_,this.href=_,this.pathname=w[1],w[2]?(this.search=w[2],this.query=e?v.parse(this.search.substr(1)):this.search.substr(1)):e&&(this.search="",this.query={}),this}var S=o.exec(_);if(S){S=S[0];var E=S.toLowerCase();this.protocol=E,_=_.substr(S.length)}if(r||S||_.match(/^\/\/[^@/]+@[^@/]+/)){var k="//"===_.substr(0,2);!k||S&&g[S]||(_=_.substr(2),this.slashes=!0)}if(!g[S]&&(k||S&&!m[S])){for(var A,x,P=-1,O=0;O<f.length;O++){var R=_.indexOf(f[O]);-1!==R&&(-1===P||R<P)&&(P=R)}x=-1===P?_.lastIndexOf("@"):_.lastIndexOf("@",P),-1!==x&&(A=_.slice(0,x),_=_.slice(x+1),this.auth=decodeURIComponent(A)),P=-1;for(O=0;O<h.length;O++){R=_.indexOf(h[O]);-1!==R&&(-1===P||R<P)&&(P=R)}-1===P&&(P=_.length),this.host=_.slice(0,P),_=_.slice(P),this.parseHost(),this.hostname=this.hostname||"";var M="["===this.hostname[0]&&"]"===this.hostname[this.hostname.length-1];if(!M)for(var C=this.hostname.split(/\./),T=(O=0,C.length);O<T;O++){var B=C[O];if(B&&!B.match(d)){for(var I="",j=0,N=B.length;j<N;j++)B.charCodeAt(j)>127?I+="x":I+=B[j];if(!I.match(d)){var L=C.slice(0,O),D=C.slice(O+1),F=B.match(y);F&&(L.push(F[1]),D.unshift(F[2])),D.length&&(_="/"+D.join(".")+_),this.hostname=L.join(".");break}}}this.hostname.length>p?this.hostname="":this.hostname=this.hostname.toLowerCase(),M||(this.hostname=n.toASCII(this.hostname));var U=this.port?":"+this.port:"",H=this.hostname||"";this.host=H+U,this.href+=this.host,M&&(this.hostname=this.hostname.substr(1,this.hostname.length-2),"/"!==_[0]&&(_="/"+_))}if(!b[E])for(O=0,T=l.length;O<T;O++){var q=l[O];if(-1!==_.indexOf(q)){var W=encodeURIComponent(q);W===q&&(W=escape(q)),_=_.split(q).join(W)}}var K=_.indexOf("#");-1!==K&&(this.hash=_.substr(K),_=_.slice(0,K));var z=_.indexOf("?");if(-1!==z?(this.search=_.substr(z),this.query=_.substr(z+1),e&&(this.query=v.parse(this.query)),_=_.slice(0,z)):e&&(this.search="",this.query={}),_&&(this.pathname=_),m[E]&&this.hostname&&!this.pathname&&(this.pathname="/"),this.pathname||this.search){U=this.pathname||"";var V=this.search||"";this.path=U+V}return this.href=this.format(),this},i.prototype.format=function(){var t=this.auth||"";t&&(t=encodeURIComponent(t),t=t.replace(/%3A/i,":"),t+="@");var e=this.protocol||"",r=this.pathname||"",n=this.hash||"",i=!1,o="";this.host?i=t+this.host:this.hostname&&(i=t+(-1===this.hostname.indexOf(":")?this.hostname:"["+this.hostname+"]"),this.port&&(i+=":"+this.port)),this.query&&"object"===typeof this.query&&Object.keys(this.query).length&&(o=v.stringify(this.query,{arrayFormat:"repeat",addQueryPrefix:!1}));var s=this.search||o&&"?"+o||"";return e&&":"!==e.substr(-1)&&(e+=":"),this.slashes||(!e||m[e])&&!1!==i?(i="//"+(i||""),r&&"/"!==r.charAt(0)&&(r="/"+r)):i||(i=""),n&&"#"!==n.charAt(0)&&(n="#"+n),s&&"?"!==s.charAt(0)&&(s="?"+s),r=r.replace(/[?#]/g,(function(t){return encodeURIComponent(t)})),s=s.replace("#","%23"),e+i+r+s+n},i.prototype.resolve=function(t){return this.resolveObject(_(t,!1,!0)).format()},i.prototype.resolveObject=function(t){if("string"===typeof t){var e=new i;e.parse(t,!1,!0),t=e}for(var r=new i,n=Object.keys(this),o=0;o<n.length;o++){var s=n[o];r[s]=this[s]}if(r.hash=t.hash,""===t.href)return r.href=r.format(),r;if(t.slashes&&!t.protocol){for(var a=Object.keys(t),c=0;c<a.length;c++){var u=a[c];"protocol"!==u&&(r[u]=t[u])}return m[r.protocol]&&r.hostname&&!r.pathname&&(r.pathname="/",r.path=r.pathname),r.href=r.format(),r}if(t.protocol&&t.protocol!==r.protocol){if(!m[t.protocol]){for(var l=Object.keys(t),h=0;h<l.length;h++){var f=l[h];r[f]=t[f]}return r.href=r.format(),r}if(r.protocol=t.protocol,t.host||g[t.protocol])r.pathname=t.pathname;else{var p=(t.pathname||"").split("/");while(p.length&&!(t.host=p.shift()));t.host||(t.host=""),t.hostname||(t.hostname=""),""!==p[0]&&p.unshift(""),p.length<2&&p.unshift(""),r.pathname=p.join("/")}if(r.search=t.search,r.query=t.query,r.host=t.host||"",r.auth=t.auth,r.hostname=t.hostname||t.host,r.port=t.port,r.pathname||r.search){var d=r.pathname||"",y=r.search||"";r.path=d+y}return r.slashes=r.slashes||t.slashes,r.href=r.format(),r}var b=r.pathname&&"/"===r.pathname.charAt(0),v=t.host||t.pathname&&"/"===t.pathname.charAt(0),_=v||b||r.host&&t.pathname,w=_,S=r.pathname&&r.pathname.split("/")||[],E=(p=t.pathname&&t.pathname.split("/")||[],r.protocol&&!m[r.protocol]);if(E&&(r.hostname="",r.port=null,r.host&&(""===S[0]?S[0]=r.host:S.unshift(r.host)),r.host="",t.protocol&&(t.hostname=null,t.port=null,t.host&&(""===p[0]?p[0]=t.host:p.unshift(t.host)),t.host=null),_=_&&(""===p[0]||""===S[0])),v)r.host=t.host||""===t.host?t.host:r.host,r.hostname=t.hostname||""===t.hostname?t.hostname:r.hostname,r.search=t.search,r.query=t.query,S=p;else if(p.length)S||(S=[]),S.pop(),S=S.concat(p),r.search=t.search,r.query=t.query;else if(null!=t.search){if(E){r.host=S.shift(),r.hostname=r.host;var k=!!(r.host&&r.host.indexOf("@")>0)&&r.host.split("@");k&&(r.auth=k.shift(),r.hostname=k.shift(),r.host=r.hostname)}return r.search=t.search,r.query=t.query,null===r.pathname&&null===r.search||(r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")),r.href=r.format(),r}if(!S.length)return r.pathname=null,r.search?r.path="/"+r.search:r.path=null,r.href=r.format(),r;for(var A=S.slice(-1)[0],x=(r.host||t.host||S.length>1)&&("."===A||".."===A)||""===A,P=0,O=S.length;O>=0;O--)A=S[O],"."===A?S.splice(O,1):".."===A?(S.splice(O,1),P++):P&&(S.splice(O,1),P--);if(!_&&!w)for(;P--;P)S.unshift("..");!_||""===S[0]||S[0]&&"/"===S[0].charAt(0)||S.unshift(""),x&&"/"!==S.join("/").substr(-1)&&S.push("");var R=""===S[0]||S[0]&&"/"===S[0].charAt(0);if(E){r.hostname=R?"":S.length?S.shift():"",r.host=r.hostname;k=!!(r.host&&r.host.indexOf("@")>0)&&r.host.split("@");k&&(r.auth=k.shift(),r.hostname=k.shift(),r.host=r.hostname)}return _=_||r.host&&S.length,_&&!R&&S.unshift(""),S.length>0?r.pathname=S.join("/"):(r.pathname=null,r.path=null),null===r.pathname&&null===r.search||(r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")),r.auth=t.auth||r.auth,r.slashes=r.slashes||t.slashes,r.href=r.format(),r},i.prototype.parseHost=function(){var t=this.host,e=s.exec(t);e&&(e=e[0],":"!==e&&(this.port=e.substr(1)),t=t.substr(0,t.length-e.length)),t&&(this.hostname=t)},e.parse=_,e.resolve=S,e.resolveObject=E,e.format=w,e.Url=i},"0e8b":function(t,e,r){"use strict";(function(e,n){var i;t.exports=R,R.ReadableState=O;r("faa1").EventEmitter;var o=function(t,e){return t.listeners(e).length},s=r("b98b"),a=r("b639").Buffer,c=("undefined"!==typeof e?e:"undefined"!==typeof window?window:"undefined"!==typeof self?self:{}).Uint8Array||function(){};function u(t){return a.from(t)}function l(t){return a.isBuffer(t)||t instanceof c}var h,f=r(5);h=f&&f.debuglog?f.debuglog("stream"):function(){};var p,d,y,b=r("e937"),g=r("f482"),m=r("86c6"),v=m.getHighWaterMark,_=r("9bfc").codes,w=_.ERR_INVALID_ARG_TYPE,S=_.ERR_STREAM_PUSH_AFTER_EOF,E=_.ERR_METHOD_NOT_IMPLEMENTED,k=_.ERR_STREAM_UNSHIFT_AFTER_END_EVENT;r("3fb5")(R,s);var A=g.errorOrDestroy,x=["error","close","destroy","pause","resume"];function P(t,e,r){if("function"===typeof t.prependListener)return t.prependListener(e,r);t._events&&t._events[e]?Array.isArray(t._events[e])?t._events[e].unshift(r):t._events[e]=[r,t._events[e]]:t.on(e,r)}function O(t,e,n){i=i||r("a493"),t=t||{},"boolean"!==typeof n&&(n=e instanceof i),this.objectMode=!!t.objectMode,n&&(this.objectMode=this.objectMode||!!t.readableObjectMode),this.highWaterMark=v(this,t,"readableHighWaterMark",n),this.buffer=new b,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.paused=!0,this.emitClose=!1!==t.emitClose,this.autoDestroy=!!t.autoDestroy,this.destroyed=!1,this.defaultEncoding=t.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,t.encoding&&(p||(p=r("aa22").StringDecoder),this.decoder=new p(t.encoding),this.encoding=t.encoding)}function R(t){if(i=i||r("a493"),!(this instanceof R))return new R(t);var e=this instanceof i;this._readableState=new O(t,this,e),this.readable=!0,t&&("function"===typeof t.read&&(this._read=t.read),"function"===typeof t.destroy&&(this._destroy=t.destroy)),s.call(this)}function M(t,e,r,n,i){h("readableAddChunk",e);var o,s=t._readableState;if(null===e)s.reading=!1,N(t,s);else if(i||(o=T(s,e)),o)A(t,o);else if(s.objectMode||e&&e.length>0)if("string"===typeof e||s.objectMode||Object.getPrototypeOf(e)===a.prototype||(e=u(e)),n)s.endEmitted?A(t,new k):C(t,s,e,!0);else if(s.ended)A(t,new S);else{if(s.destroyed)return!1;s.reading=!1,s.decoder&&!r?(e=s.decoder.write(e),s.objectMode||0!==e.length?C(t,s,e,!1):F(t,s)):C(t,s,e,!1)}else n||(s.reading=!1,F(t,s));return!s.ended&&(s.length<s.highWaterMark||0===s.length)}function C(t,e,r,n){e.flowing&&0===e.length&&!e.sync?(e.awaitDrain=0,t.emit("data",r)):(e.length+=e.objectMode?1:r.length,n?e.buffer.unshift(r):e.buffer.push(r),e.needReadable&&L(t)),F(t,e)}function T(t,e){var r;return l(e)||"string"===typeof e||void 0===e||t.objectMode||(r=new w("chunk",["string","Buffer","Uint8Array"],e)),r}Object.defineProperty(R.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._readableState&&this._readableState.destroyed},set:function(t){this._readableState&&(this._readableState.destroyed=t)}}),R.prototype.destroy=g.destroy,R.prototype._undestroy=g.undestroy,R.prototype._destroy=function(t,e){e(t)},R.prototype.push=function(t,e){var r,n=this._readableState;return n.objectMode?r=!0:"string"===typeof t&&(e=e||n.defaultEncoding,e!==n.encoding&&(t=a.from(t,e),e=""),r=!0),M(this,t,e,!1,r)},R.prototype.unshift=function(t){return M(this,t,null,!0,!1)},R.prototype.isPaused=function(){return!1===this._readableState.flowing},R.prototype.setEncoding=function(t){p||(p=r("aa22").StringDecoder);var e=new p(t);this._readableState.decoder=e,this._readableState.encoding=this._readableState.decoder.encoding;var n=this._readableState.buffer.head,i="";while(null!==n)i+=e.write(n.data),n=n.next;return this._readableState.buffer.clear(),""!==i&&this._readableState.buffer.push(i),this._readableState.length=i.length,this};var B=1073741824;function I(t){return t>=B?t=B:(t--,t|=t>>>1,t|=t>>>2,t|=t>>>4,t|=t>>>8,t|=t>>>16,t++),t}function j(t,e){return t<=0||0===e.length&&e.ended?0:e.objectMode?1:t!==t?e.flowing&&e.length?e.buffer.head.data.length:e.length:(t>e.highWaterMark&&(e.highWaterMark=I(t)),t<=e.length?t:e.ended?e.length:(e.needReadable=!0,0))}function N(t,e){if(h("onEofChunk"),!e.ended){if(e.decoder){var r=e.decoder.end();r&&r.length&&(e.buffer.push(r),e.length+=e.objectMode?1:r.length)}e.ended=!0,e.sync?L(t):(e.needReadable=!1,e.emittedReadable||(e.emittedReadable=!0,D(t)))}}function L(t){var e=t._readableState;h("emitReadable",e.needReadable,e.emittedReadable),e.needReadable=!1,e.emittedReadable||(h("emitReadable",e.flowing),e.emittedReadable=!0,n.nextTick(D,t))}function D(t){var e=t._readableState;h("emitReadable_",e.destroyed,e.length,e.ended),e.destroyed||!e.length&&!e.ended||(t.emit("readable"),e.emittedReadable=!1),e.needReadable=!e.flowing&&!e.ended&&e.length<=e.highWaterMark,V(t)}function F(t,e){e.readingMore||(e.readingMore=!0,n.nextTick(U,t,e))}function U(t,e){while(!e.reading&&!e.ended&&(e.length<e.highWaterMark||e.flowing&&0===e.length)){var r=e.length;if(h("maybeReadMore read 0"),t.read(0),r===e.length)break}e.readingMore=!1}function H(t){return function(){var e=t._readableState;h("pipeOnDrain",e.awaitDrain),e.awaitDrain&&e.awaitDrain--,0===e.awaitDrain&&o(t,"data")&&(e.flowing=!0,V(t))}}function q(t){var e=t._readableState;e.readableListening=t.listenerCount("readable")>0,e.resumeScheduled&&!e.paused?e.flowing=!0:t.listenerCount("data")>0&&t.resume()}function W(t){h("readable nexttick read 0"),t.read(0)}function K(t,e){e.resumeScheduled||(e.resumeScheduled=!0,n.nextTick(z,t,e))}function z(t,e){h("resume",e.reading),e.reading||t.read(0),e.resumeScheduled=!1,t.emit("resume"),V(t),e.flowing&&!e.reading&&t.read(0)}function V(t){var e=t._readableState;h("flow",e.flowing);while(e.flowing&&null!==t.read());}function G(t,e){return 0===e.length?null:(e.objectMode?r=e.buffer.shift():!t||t>=e.length?(r=e.decoder?e.buffer.join(""):1===e.buffer.length?e.buffer.first():e.buffer.concat(e.length),e.buffer.clear()):r=e.buffer.consume(t,e.decoder),r);var r}function Q(t){var e=t._readableState;h("endReadable",e.endEmitted),e.endEmitted||(e.ended=!0,n.nextTick($,e,t))}function $(t,e){if(h("endReadableNT",t.endEmitted,t.length),!t.endEmitted&&0===t.length&&(t.endEmitted=!0,e.readable=!1,e.emit("end"),t.autoDestroy)){var r=e._writableState;(!r||r.autoDestroy&&r.finished)&&e.destroy()}}function J(t,e){for(var r=0,n=t.length;r<n;r++)if(t[r]===e)return r;return-1}R.prototype.read=function(t){h("read",t),t=parseInt(t,10);var e=this._readableState,r=t;if(0!==t&&(e.emittedReadable=!1),0===t&&e.needReadable&&((0!==e.highWaterMark?e.length>=e.highWaterMark:e.length>0)||e.ended))return h("read: emitReadable",e.length,e.ended),0===e.length&&e.ended?Q(this):L(this),null;if(t=j(t,e),0===t&&e.ended)return 0===e.length&&Q(this),null;var n,i=e.needReadable;return h("need readable",i),(0===e.length||e.length-t<e.highWaterMark)&&(i=!0,h("length less than watermark",i)),e.ended||e.reading?(i=!1,h("reading or ended",i)):i&&(h("do read"),e.reading=!0,e.sync=!0,0===e.length&&(e.needReadable=!0),this._read(e.highWaterMark),e.sync=!1,e.reading||(t=j(r,e))),n=t>0?G(t,e):null,null===n?(e.needReadable=e.length<=e.highWaterMark,t=0):(e.length-=t,e.awaitDrain=0),0===e.length&&(e.ended||(e.needReadable=!0),r!==t&&e.ended&&Q(this)),null!==n&&this.emit("data",n),n},R.prototype._read=function(t){A(this,new E("_read()"))},R.prototype.pipe=function(t,e){var r=this,i=this._readableState;switch(i.pipesCount){case 0:i.pipes=t;break;case 1:i.pipes=[i.pipes,t];break;default:i.pipes.push(t);break}i.pipesCount+=1,h("pipe count=%d opts=%j",i.pipesCount,e);var s=(!e||!1!==e.end)&&t!==n.stdout&&t!==n.stderr,a=s?u:m;function c(t,e){h("onunpipe"),t===r&&e&&!1===e.hasUnpiped&&(e.hasUnpiped=!0,p())}function u(){h("onend"),t.end()}i.endEmitted?n.nextTick(a):r.once("end",a),t.on("unpipe",c);var l=H(r);t.on("drain",l);var f=!1;function p(){h("cleanup"),t.removeListener("close",b),t.removeListener("finish",g),t.removeListener("drain",l),t.removeListener("error",y),t.removeListener("unpipe",c),r.removeListener("end",u),r.removeListener("end",m),r.removeListener("data",d),f=!0,!i.awaitDrain||t._writableState&&!t._writableState.needDrain||l()}function d(e){h("ondata");var n=t.write(e);h("dest.write",n),!1===n&&((1===i.pipesCount&&i.pipes===t||i.pipesCount>1&&-1!==J(i.pipes,t))&&!f&&(h("false write response, pause",i.awaitDrain),i.awaitDrain++),r.pause())}function y(e){h("onerror",e),m(),t.removeListener("error",y),0===o(t,"error")&&A(t,e)}function b(){t.removeListener("finish",g),m()}function g(){h("onfinish"),t.removeListener("close",b),m()}function m(){h("unpipe"),r.unpipe(t)}return r.on("data",d),P(t,"error",y),t.once("close",b),t.once("finish",g),t.emit("pipe",r),i.flowing||(h("pipe resume"),r.resume()),t},R.prototype.unpipe=function(t){var e=this._readableState,r={hasUnpiped:!1};if(0===e.pipesCount)return this;if(1===e.pipesCount)return t&&t!==e.pipes||(t||(t=e.pipes),e.pipes=null,e.pipesCount=0,e.flowing=!1,t&&t.emit("unpipe",this,r)),this;if(!t){var n=e.pipes,i=e.pipesCount;e.pipes=null,e.pipesCount=0,e.flowing=!1;for(var o=0;o<i;o++)n[o].emit("unpipe",this,{hasUnpiped:!1});return this}var s=J(e.pipes,t);return-1===s||(e.pipes.splice(s,1),e.pipesCount-=1,1===e.pipesCount&&(e.pipes=e.pipes[0]),t.emit("unpipe",this,r)),this},R.prototype.on=function(t,e){var r=s.prototype.on.call(this,t,e),i=this._readableState;return"data"===t?(i.readableListening=this.listenerCount("readable")>0,!1!==i.flowing&&this.resume()):"readable"===t&&(i.endEmitted||i.readableListening||(i.readableListening=i.needReadable=!0,i.flowing=!1,i.emittedReadable=!1,h("on readable",i.length,i.reading),i.length?L(this):i.reading||n.nextTick(W,this))),r},R.prototype.addListener=R.prototype.on,R.prototype.removeListener=function(t,e){var r=s.prototype.removeListener.call(this,t,e);return"readable"===t&&n.nextTick(q,this),r},R.prototype.removeAllListeners=function(t){var e=s.prototype.removeAllListeners.apply(this,arguments);return"readable"!==t&&void 0!==t||n.nextTick(q,this),e},R.prototype.resume=function(){var t=this._readableState;return t.flowing||(h("resume"),t.flowing=!t.readableListening,K(this,t)),t.paused=!1,this},R.prototype.pause=function(){return h("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(h("pause"),this._readableState.flowing=!1,this.emit("pause")),this._readableState.paused=!0,this},R.prototype.wrap=function(t){var e=this,r=this._readableState,n=!1;for(var i in t.on("end",(function(){if(h("wrapped end"),r.decoder&&!r.ended){var t=r.decoder.end();t&&t.length&&e.push(t)}e.push(null)})),t.on("data",(function(i){if(h("wrapped data"),r.decoder&&(i=r.decoder.write(i)),(!r.objectMode||null!==i&&void 0!==i)&&(r.objectMode||i&&i.length)){var o=e.push(i);o||(n=!0,t.pause())}})),t)void 0===this[i]&&"function"===typeof t[i]&&(this[i]=function(e){return function(){return t[e].apply(t,arguments)}}(i));for(var o=0;o<x.length;o++)t.on(x[o],this.emit.bind(this,x[o]));return this._read=function(e){h("wrapped _read",e),n&&(n=!1,t.resume())},this},"function"===typeof Symbol&&(R.prototype[Symbol.asyncIterator]=function(){return void 0===d&&(d=r("782c")),d(this)}),Object.defineProperty(R.prototype,"readableHighWaterMark",{enumerable:!1,get:function(){return this._readableState.highWaterMark}}),Object.defineProperty(R.prototype,"readableBuffer",{enumerable:!1,get:function(){return this._readableState&&this._readableState.buffer}}),Object.defineProperty(R.prototype,"readableFlowing",{enumerable:!1,get:function(){return this._readableState.flowing},set:function(t){this._readableState&&(this._readableState.flowing=t)}}),R._fromList=G,Object.defineProperty(R.prototype,"readableLength",{enumerable:!1,get:function(){return this._readableState.length}}),"function"===typeof Symbol&&(R.from=function(t,e){return void 0===y&&(y=r("a50f")),y(R,t,e)})}).call(this,r("c8ba"),r("4362"))},"0f7c":function(t,e,r){"use strict";var n=r("688e");t.exports=Function.prototype.bind||n},"10b7":function(t,e,r){(function(e,n){t.exports=n(r("21bf"))})(0,(function(t){
/** @preserve
	(c) 2012 by Cédric Mesnil. All rights reserved.

	Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:

	    - Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.
	    - Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.

	THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
	*/
return function(e){var r=t,n=r.lib,i=n.WordArray,o=n.Hasher,s=r.algo,a=i.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),c=i.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),u=i.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),l=i.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),h=i.create([0,1518500249,1859775393,2400959708,2840853838]),f=i.create([1352829926,1548603684,1836072691,2053994217,0]),p=s.RIPEMD160=o.extend({_doReset:function(){this._hash=i.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(t,e){for(var r=0;r<16;r++){var n=e+r,i=t[n];t[n]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8)}var o,s,p,_,w,S,E,k,A,x,P,O=this._hash.words,R=h.words,M=f.words,C=a.words,T=c.words,B=u.words,I=l.words;S=o=O[0],E=s=O[1],k=p=O[2],A=_=O[3],x=w=O[4];for(r=0;r<80;r+=1)P=o+t[e+C[r]]|0,P+=r<16?d(s,p,_)+R[0]:r<32?y(s,p,_)+R[1]:r<48?b(s,p,_)+R[2]:r<64?g(s,p,_)+R[3]:m(s,p,_)+R[4],P|=0,P=v(P,B[r]),P=P+w|0,o=w,w=_,_=v(p,10),p=s,s=P,P=S+t[e+T[r]]|0,P+=r<16?m(E,k,A)+M[0]:r<32?g(E,k,A)+M[1]:r<48?b(E,k,A)+M[2]:r<64?y(E,k,A)+M[3]:d(E,k,A)+M[4],P|=0,P=v(P,I[r]),P=P+x|0,S=x,x=A,A=v(k,10),k=E,E=P;P=O[1]+p+A|0,O[1]=O[2]+_+x|0,O[2]=O[3]+w+S|0,O[3]=O[4]+o+E|0,O[4]=O[0]+s+k|0,O[0]=P},_doFinalize:function(){var t=this._data,e=t.words,r=8*this._nDataBytes,n=8*t.sigBytes;e[n>>>5]|=128<<24-n%32,e[14+(n+64>>>9<<4)]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),t.sigBytes=4*(e.length+1),this._process();for(var i=this._hash,o=i.words,s=0;s<5;s++){var a=o[s];o[s]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8)}return i},clone:function(){var t=o.clone.call(this);return t._hash=this._hash.clone(),t}});function d(t,e,r){return t^e^r}function y(t,e,r){return t&e|~t&r}function b(t,e,r){return(t|~e)^r}function g(t,e,r){return t&r|e&~r}function m(t,e,r){return t^(e|~r)}function v(t,e){return t<<e|t>>>32-e}r.RIPEMD160=o._createHelper(p),r.HmacRIPEMD160=o._createHmacHelper(p)}(Math),t.RIPEMD160}))},1132:function(t,e,r){(function(e,n){t.exports=n(r("21bf"))})(0,(function(t){return function(){var e=t,r=e.lib,n=r.WordArray,i=e.enc;i.Base64={stringify:function(t){var e=t.words,r=t.sigBytes,n=this._map;t.clamp();for(var i=[],o=0;o<r;o+=3)for(var s=e[o>>>2]>>>24-o%4*8&255,a=e[o+1>>>2]>>>24-(o+1)%4*8&255,c=e[o+2>>>2]>>>24-(o+2)%4*8&255,u=s<<16|a<<8|c,l=0;l<4&&o+.75*l<r;l++)i.push(n.charAt(u>>>6*(3-l)&63));var h=n.charAt(64);if(h)while(i.length%4)i.push(h);return i.join("")},parse:function(t){var e=t.length,r=this._map,n=this._reverseMap;if(!n){n=this._reverseMap=[];for(var i=0;i<r.length;i++)n[r.charCodeAt(i)]=i}var s=r.charAt(64);if(s){var a=t.indexOf(s);-1!==a&&(e=a)}return o(t,e,n)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="};function o(t,e,r){for(var i=[],o=0,s=0;s<e;s++)if(s%4){var a=r[t.charCodeAt(s-1)]<<s%4*2,c=r[t.charCodeAt(s)]>>>6-s%4*2,u=a|c;i[o>>>2]|=u<<24-o%4*8,o++}return n.create(i,o)}}(),t.enc.Base64}))},1382:function(t,e,r){(function(e,n,i){t.exports=n(r("21bf"),r("1132"),r("72fe"),r("2b79"),r("38ba"))})(0,(function(t){return function(){var e=t,r=e.lib,n=r.StreamCipher,i=e.algo,o=[],s=[],a=[],c=i.Rabbit=n.extend({_doReset:function(){for(var t=this._key.words,e=this.cfg.iv,r=0;r<4;r++)t[r]=16711935&(t[r]<<8|t[r]>>>24)|4278255360&(t[r]<<24|t[r]>>>8);var n=this._X=[t[0],t[3]<<16|t[2]>>>16,t[1],t[0]<<16|t[3]>>>16,t[2],t[1]<<16|t[0]>>>16,t[3],t[2]<<16|t[1]>>>16],i=this._C=[t[2]<<16|t[2]>>>16,4294901760&t[0]|65535&t[1],t[3]<<16|t[3]>>>16,4294901760&t[1]|65535&t[2],t[0]<<16|t[0]>>>16,4294901760&t[2]|65535&t[3],t[1]<<16|t[1]>>>16,4294901760&t[3]|65535&t[0]];this._b=0;for(r=0;r<4;r++)u.call(this);for(r=0;r<8;r++)i[r]^=n[r+4&7];if(e){var o=e.words,s=o[0],a=o[1],c=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),l=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),h=c>>>16|4294901760&l,f=l<<16|65535&c;i[0]^=c,i[1]^=h,i[2]^=l,i[3]^=f,i[4]^=c,i[5]^=h,i[6]^=l,i[7]^=f;for(r=0;r<4;r++)u.call(this)}},_doProcessBlock:function(t,e){var r=this._X;u.call(this),o[0]=r[0]^r[5]>>>16^r[3]<<16,o[1]=r[2]^r[7]>>>16^r[5]<<16,o[2]=r[4]^r[1]>>>16^r[7]<<16,o[3]=r[6]^r[3]>>>16^r[1]<<16;for(var n=0;n<4;n++)o[n]=16711935&(o[n]<<8|o[n]>>>24)|4278255360&(o[n]<<24|o[n]>>>8),t[e+n]^=o[n]},blockSize:4,ivSize:2});function u(){for(var t=this._X,e=this._C,r=0;r<8;r++)s[r]=e[r];e[0]=e[0]+1295307597+this._b|0,e[1]=e[1]+3545052371+(e[0]>>>0<s[0]>>>0?1:0)|0,e[2]=e[2]+886263092+(e[1]>>>0<s[1]>>>0?1:0)|0,e[3]=e[3]+1295307597+(e[2]>>>0<s[2]>>>0?1:0)|0,e[4]=e[4]+3545052371+(e[3]>>>0<s[3]>>>0?1:0)|0,e[5]=e[5]+886263092+(e[4]>>>0<s[4]>>>0?1:0)|0,e[6]=e[6]+1295307597+(e[5]>>>0<s[5]>>>0?1:0)|0,e[7]=e[7]+3545052371+(e[6]>>>0<s[6]>>>0?1:0)|0,this._b=e[7]>>>0<s[7]>>>0?1:0;for(r=0;r<8;r++){var n=t[r]+e[r],i=65535&n,o=n>>>16,c=((i*i>>>17)+i*o>>>15)+o*o,u=((4294901760&n)*n|0)+((65535&n)*n|0);a[r]=c^u}t[0]=a[0]+(a[7]<<16|a[7]>>>16)+(a[6]<<16|a[6]>>>16)|0,t[1]=a[1]+(a[0]<<8|a[0]>>>24)+a[7]|0,t[2]=a[2]+(a[1]<<16|a[1]>>>16)+(a[0]<<16|a[0]>>>16)|0,t[3]=a[3]+(a[2]<<8|a[2]>>>24)+a[1]|0,t[4]=a[4]+(a[3]<<16|a[3]>>>16)+(a[2]<<16|a[2]>>>16)|0,t[5]=a[5]+(a[4]<<8|a[4]>>>24)+a[3]|0,t[6]=a[6]+(a[5]<<16|a[5]>>>16)+(a[4]<<16|a[4]>>>16)|0,t[7]=a[7]+(a[6]<<8|a[6]>>>24)+a[5]|0}e.Rabbit=n._createHelper(c)}(),t.Rabbit}))},"13a8":function(t,e,r){"use strict";(function(e,n){function i(t){var e=this;this.next=null,this.entry=null,this.finish=function(){z(e,t)}}var o;t.exports=O,O.WritableState=P;var s={deprecate:r("b7d1")},a=r("9ede"),c=r("b639").Buffer,u=("undefined"!==typeof e?e:"undefined"!==typeof window?window:"undefined"!==typeof self?self:{}).Uint8Array||function(){};function l(t){return c.from(t)}function h(t){return c.isBuffer(t)||t instanceof u}var f,p=r("edb3"),d=r("31b5"),y=d.getHighWaterMark,b=r("fbd7").codes,g=b.ERR_INVALID_ARG_TYPE,m=b.ERR_METHOD_NOT_IMPLEMENTED,v=b.ERR_MULTIPLE_CALLBACK,_=b.ERR_STREAM_CANNOT_PIPE,w=b.ERR_STREAM_DESTROYED,S=b.ERR_STREAM_NULL_VALUES,E=b.ERR_STREAM_WRITE_AFTER_END,k=b.ERR_UNKNOWN_ENCODING,A=p.errorOrDestroy;function x(){}function P(t,e,n){o=o||r("be3f"),t=t||{},"boolean"!==typeof n&&(n=e instanceof o),this.objectMode=!!t.objectMode,n&&(this.objectMode=this.objectMode||!!t.writableObjectMode),this.highWaterMark=y(this,t,"writableHighWaterMark",n),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var s=!1===t.decodeStrings;this.decodeStrings=!s,this.defaultEncoding=t.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(t){N(e,t)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.emitClose=!1!==t.emitClose,this.autoDestroy=!!t.autoDestroy,this.bufferedRequestCount=0,this.corkedRequestsFree=new i(this)}function O(t){o=o||r("be3f");var e=this instanceof o;if(!e&&!f.call(O,this))return new O(t);this._writableState=new P(t,this,e),this.writable=!0,t&&("function"===typeof t.write&&(this._write=t.write),"function"===typeof t.writev&&(this._writev=t.writev),"function"===typeof t.destroy&&(this._destroy=t.destroy),"function"===typeof t.final&&(this._final=t.final)),a.call(this)}function R(t,e){var r=new E;A(t,r),n.nextTick(e,r)}function M(t,e,r,i){var o;return null===r?o=new S:"string"===typeof r||e.objectMode||(o=new g("chunk",["string","Buffer"],r)),!o||(A(t,o),n.nextTick(i,o),!1)}function C(t,e,r){return t.objectMode||!1===t.decodeStrings||"string"!==typeof e||(e=c.from(e,r)),e}function T(t,e,r,n,i,o){if(!r){var s=C(e,n,i);n!==s&&(r=!0,i="buffer",n=s)}var a=e.objectMode?1:n.length;e.length+=a;var c=e.length<e.highWaterMark;if(c||(e.needDrain=!0),e.writing||e.corked){var u=e.lastBufferedRequest;e.lastBufferedRequest={chunk:n,encoding:i,isBuf:r,callback:o,next:null},u?u.next=e.lastBufferedRequest:e.bufferedRequest=e.lastBufferedRequest,e.bufferedRequestCount+=1}else B(t,e,!1,a,n,i,o);return c}function B(t,e,r,n,i,o,s){e.writelen=n,e.writecb=s,e.writing=!0,e.sync=!0,e.destroyed?e.onwrite(new w("write")):r?t._writev(i,e.onwrite):t._write(i,o,e.onwrite),e.sync=!1}function I(t,e,r,i,o){--e.pendingcb,r?(n.nextTick(o,i),n.nextTick(W,t,e),t._writableState.errorEmitted=!0,A(t,i)):(o(i),t._writableState.errorEmitted=!0,A(t,i),W(t,e))}function j(t){t.writing=!1,t.writecb=null,t.length-=t.writelen,t.writelen=0}function N(t,e){var r=t._writableState,i=r.sync,o=r.writecb;if("function"!==typeof o)throw new v;if(j(r),e)I(t,r,i,e,o);else{var s=U(r)||t.destroyed;s||r.corked||r.bufferProcessing||!r.bufferedRequest||F(t,r),i?n.nextTick(L,t,r,s,o):L(t,r,s,o)}}function L(t,e,r,n){r||D(t,e),e.pendingcb--,n(),W(t,e)}function D(t,e){0===e.length&&e.needDrain&&(e.needDrain=!1,t.emit("drain"))}function F(t,e){e.bufferProcessing=!0;var r=e.bufferedRequest;if(t._writev&&r&&r.next){var n=e.bufferedRequestCount,o=new Array(n),s=e.corkedRequestsFree;s.entry=r;var a=0,c=!0;while(r)o[a]=r,r.isBuf||(c=!1),r=r.next,a+=1;o.allBuffers=c,B(t,e,!0,e.length,o,"",s.finish),e.pendingcb++,e.lastBufferedRequest=null,s.next?(e.corkedRequestsFree=s.next,s.next=null):e.corkedRequestsFree=new i(e),e.bufferedRequestCount=0}else{while(r){var u=r.chunk,l=r.encoding,h=r.callback,f=e.objectMode?1:u.length;if(B(t,e,!1,f,u,l,h),r=r.next,e.bufferedRequestCount--,e.writing)break}null===r&&(e.lastBufferedRequest=null)}e.bufferedRequest=r,e.bufferProcessing=!1}function U(t){return t.ending&&0===t.length&&null===t.bufferedRequest&&!t.finished&&!t.writing}function H(t,e){t._final((function(r){e.pendingcb--,r&&A(t,r),e.prefinished=!0,t.emit("prefinish"),W(t,e)}))}function q(t,e){e.prefinished||e.finalCalled||("function"!==typeof t._final||e.destroyed?(e.prefinished=!0,t.emit("prefinish")):(e.pendingcb++,e.finalCalled=!0,n.nextTick(H,t,e)))}function W(t,e){var r=U(e);if(r&&(q(t,e),0===e.pendingcb&&(e.finished=!0,t.emit("finish"),e.autoDestroy))){var n=t._readableState;(!n||n.autoDestroy&&n.endEmitted)&&t.destroy()}return r}function K(t,e,r){e.ending=!0,W(t,e),r&&(e.finished?n.nextTick(r):t.once("finish",r)),e.ended=!0,t.writable=!1}function z(t,e,r){var n=t.entry;t.entry=null;while(n){var i=n.callback;e.pendingcb--,i(r),n=n.next}e.corkedRequestsFree.next=t}r("3fb5")(O,a),P.prototype.getBuffer=function(){var t=this.bufferedRequest,e=[];while(t)e.push(t),t=t.next;return e},function(){try{Object.defineProperty(P.prototype,"buffer",{get:s.deprecate((function(){return this.getBuffer()}),"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch(t){}}(),"function"===typeof Symbol&&Symbol.hasInstance&&"function"===typeof Function.prototype[Symbol.hasInstance]?(f=Function.prototype[Symbol.hasInstance],Object.defineProperty(O,Symbol.hasInstance,{value:function(t){return!!f.call(this,t)||this===O&&(t&&t._writableState instanceof P)}})):f=function(t){return t instanceof this},O.prototype.pipe=function(){A(this,new _)},O.prototype.write=function(t,e,r){var n=this._writableState,i=!1,o=!n.objectMode&&h(t);return o&&!c.isBuffer(t)&&(t=l(t)),"function"===typeof e&&(r=e,e=null),o?e="buffer":e||(e=n.defaultEncoding),"function"!==typeof r&&(r=x),n.ending?R(this,r):(o||M(this,n,t,r))&&(n.pendingcb++,i=T(this,n,o,t,e,r)),i},O.prototype.cork=function(){this._writableState.corked++},O.prototype.uncork=function(){var t=this._writableState;t.corked&&(t.corked--,t.writing||t.corked||t.bufferProcessing||!t.bufferedRequest||F(this,t))},O.prototype.setDefaultEncoding=function(t){if("string"===typeof t&&(t=t.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((t+"").toLowerCase())>-1))throw new k(t);return this._writableState.defaultEncoding=t,this},Object.defineProperty(O.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}}),Object.defineProperty(O.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),O.prototype._write=function(t,e,r){r(new m("_write()"))},O.prototype._writev=null,O.prototype.end=function(t,e,r){var n=this._writableState;return"function"===typeof t?(r=t,t=null,e=null):"function"===typeof e&&(r=e,e=null),null!==t&&void 0!==t&&this.write(t,e),n.corked&&(n.corked=1,this.uncork()),n.ending||K(this,n,r),this},Object.defineProperty(O.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}}),Object.defineProperty(O.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._writableState&&this._writableState.destroyed},set:function(t){this._writableState&&(this._writableState.destroyed=t)}}),O.prototype.destroy=p.destroy,O.prototype._undestroy=p.undestroy,O.prototype._destroy=function(t,e){e(t)}}).call(this,r("c8ba"),r("4362"))},1468:function(t,e){var r=1e3,n=60*r,i=60*n,o=24*i,s=7*o,a=365.25*o;function c(t){if(t=String(t),!(t.length>100)){var e=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(t);if(e){var c=parseFloat(e[1]),u=(e[2]||"ms").toLowerCase();switch(u){case"years":case"year":case"yrs":case"yr":case"y":return c*a;case"weeks":case"week":case"w":return c*s;case"days":case"day":case"d":return c*o;case"hours":case"hour":case"hrs":case"hr":case"h":return c*i;case"minutes":case"minute":case"mins":case"min":case"m":return c*n;case"seconds":case"second":case"secs":case"sec":case"s":return c*r;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return c;default:return}}}}function u(t){var e=Math.abs(t);return e>=o?Math.round(t/o)+"d":e>=i?Math.round(t/i)+"h":e>=n?Math.round(t/n)+"m":e>=r?Math.round(t/r)+"s":t+"ms"}function l(t){var e=Math.abs(t);return e>=o?h(t,e,o,"day"):e>=i?h(t,e,i,"hour"):e>=n?h(t,e,n,"minute"):e>=r?h(t,e,r,"second"):t+" ms"}function h(t,e,r,n){var i=e>=1.5*r;return Math.round(t/r)+" "+n+(i?"s":"")}t.exports=function(t,e){e=e||{};var r=typeof t;if("string"===r&&t.length>0)return c(t);if("number"===r&&isFinite(t))return e.long?l(t):u(t);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(t))}},1696:function(t,e,r){"use strict";t.exports=function(){if("function"!==typeof Symbol||"function"!==typeof Object.getOwnPropertySymbols)return!1;if("symbol"===typeof Symbol.iterator)return!0;var t={},e=Symbol("test"),r=Object(e);if("string"===typeof e)return!1;if("[object Symbol]"!==Object.prototype.toString.call(e))return!1;if("[object Symbol]"!==Object.prototype.toString.call(r))return!1;var n=42;for(e in t[e]=n,t)return!1;if("function"===typeof Object.keys&&0!==Object.keys(t).length)return!1;if("function"===typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(t).length)return!1;var i=Object.getOwnPropertySymbols(t);if(1!==i.length||i[0]!==e)return!1;if(!Object.prototype.propertyIsEnumerable.call(t,e))return!1;if("function"===typeof Object.getOwnPropertyDescriptor){var o=Object.getOwnPropertyDescriptor(t,e);if(o.value!==n||!0!==o.enumerable)return!1}return!0}},"17e1":function(t,e,r){(function(e,n){t.exports=n(r("21bf"))})(0,(function(t){return function(){if("function"==typeof ArrayBuffer){var e=t,r=e.lib,n=r.WordArray,i=n.init,o=n.init=function(t){if(t instanceof ArrayBuffer&&(t=new Uint8Array(t)),(t instanceof Int8Array||"undefined"!==typeof Uint8ClampedArray&&t instanceof Uint8ClampedArray||t instanceof Int16Array||t instanceof Uint16Array||t instanceof Int32Array||t instanceof Uint32Array||t instanceof Float32Array||t instanceof Float64Array)&&(t=new Uint8Array(t.buffer,t.byteOffset,t.byteLength)),t instanceof Uint8Array){for(var e=t.byteLength,r=[],n=0;n<e;n++)r[n>>>2]|=t[n]<<24-n%4*8;i.call(this,r,e)}else i.apply(this,arguments)};o.prototype=n}}(),t.lib.WordArray}))},"184d":function(t,e,r){"use strict";var n=r("f177"),i=r("2500"),o=r("bbc7");t.exports={formats:o,parse:i,stringify:n}},"191b":function(t,e,r){(function(e,n,i){t.exports=n(r("21bf"),r("94f8"))})(0,(function(t){return function(){var e=t,r=e.lib,n=r.WordArray,i=e.algo,o=i.SHA256,s=i.SHA224=o.extend({_doReset:function(){this._hash=new n.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var t=o._doFinalize.call(this);return t.sigBytes-=4,t}});e.SHA224=o._createHelper(s),e.HmacSHA224=o._createHmacHelper(s)}(),t.SHA224}))},1985:function(t,e,r){(function(t,n){var i;/*! https://mths.be/punycode v1.4.1 by @mathias */(function(o){e&&e.nodeType,t&&t.nodeType;var s="object"==typeof n&&n;s.global!==s&&s.window!==s&&s.self;var a,c=2147483647,u=36,l=1,h=26,f=38,p=700,d=72,y=128,b="-",g=/^xn--/,m=/[^\x20-\x7E]/,v=/[\x2E\u3002\uFF0E\uFF61]/g,_={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},w=u-l,S=Math.floor,E=String.fromCharCode;function k(t){throw new RangeError(_[t])}function A(t,e){var r=t.length,n=[];while(r--)n[r]=e(t[r]);return n}function x(t,e){var r=t.split("@"),n="";r.length>1&&(n=r[0]+"@",t=r[1]),t=t.replace(v,".");var i=t.split("."),o=A(i,e).join(".");return n+o}function P(t){var e,r,n=[],i=0,o=t.length;while(i<o)e=t.charCodeAt(i++),e>=55296&&e<=56319&&i<o?(r=t.charCodeAt(i++),56320==(64512&r)?n.push(((1023&e)<<10)+(1023&r)+65536):(n.push(e),i--)):n.push(e);return n}function O(t){return A(t,(function(t){var e="";return t>65535&&(t-=65536,e+=E(t>>>10&1023|55296),t=56320|1023&t),e+=E(t),e})).join("")}function R(t){return t-48<10?t-22:t-65<26?t-65:t-97<26?t-97:u}function M(t,e){return t+22+75*(t<26)-((0!=e)<<5)}function C(t,e,r){var n=0;for(t=r?S(t/p):t>>1,t+=S(t/e);t>w*h>>1;n+=u)t=S(t/w);return S(n+(w+1)*t/(t+f))}function T(t){var e,r,n,i,o,s,a,f,p,g,m=[],v=t.length,_=0,w=y,E=d;for(r=t.lastIndexOf(b),r<0&&(r=0),n=0;n<r;++n)t.charCodeAt(n)>=128&&k("not-basic"),m.push(t.charCodeAt(n));for(i=r>0?r+1:0;i<v;){for(o=_,s=1,a=u;;a+=u){if(i>=v&&k("invalid-input"),f=R(t.charCodeAt(i++)),(f>=u||f>S((c-_)/s))&&k("overflow"),_+=f*s,p=a<=E?l:a>=E+h?h:a-E,f<p)break;g=u-p,s>S(c/g)&&k("overflow"),s*=g}e=m.length+1,E=C(_-o,e,0==o),S(_/e)>c-w&&k("overflow"),w+=S(_/e),_%=e,m.splice(_++,0,w)}return O(m)}function B(t){var e,r,n,i,o,s,a,f,p,g,m,v,_,w,A,x=[];for(t=P(t),v=t.length,e=y,r=0,o=d,s=0;s<v;++s)m=t[s],m<128&&x.push(E(m));n=i=x.length,i&&x.push(b);while(n<v){for(a=c,s=0;s<v;++s)m=t[s],m>=e&&m<a&&(a=m);for(_=n+1,a-e>S((c-r)/_)&&k("overflow"),r+=(a-e)*_,e=a,s=0;s<v;++s)if(m=t[s],m<e&&++r>c&&k("overflow"),m==e){for(f=r,p=u;;p+=u){if(g=p<=o?l:p>=o+h?h:p-o,f<g)break;A=f-g,w=u-g,x.push(E(M(g+A%w,0))),f=S(A/w)}x.push(E(M(f,0))),o=C(r,_,n==i),r=0,++n}++r,++e}return x.join("")}function I(t){return x(t,(function(t){return g.test(t)?T(t.slice(4).toLowerCase()):t}))}function j(t){return x(t,(function(t){return m.test(t)?"xn--"+B(t):t}))}a={version:"1.4.1",ucs2:{decode:P,encode:O},decode:T,encode:B,toASCII:j,toUnicode:I},i=function(){return a}.call(e,r,e,t),void 0===i||(t.exports=i)})()}).call(this,r("62e4")(t),r("c8ba"))},"1e4d":function(t,e,r){"use strict";function n(){if(!(this instanceof n))return new n;this.nextId=Math.max(1,Math.floor(65535*Math.random()))}n.prototype.allocate=function(){const t=this.nextId++;return 65536===this.nextId&&(this.nextId=1),t},n.prototype.getLastAllocated=function(){return 1===this.nextId?65535:this.nextId-1},n.prototype.register=function(t){return!0},n.prototype.deallocate=function(t){},n.prototype.clear=function(){},t.exports=n},"1fad":function(t,e,r){const n=r("51e9"),i=r("faa1"),o=r("a7c9"),s=r("b289"),a=r("34eb")("mqtt-packet:parser");class c extends i{constructor(){super(),this.parser=this.constructor.parser}static parser(t){return this instanceof c?(this.settings=t||{},this._states=["_parseHeader","_parseLength","_parsePayload","_newPacket"],this._resetState(),this):(new c).parser(t)}_resetState(){a("_resetState: resetting packet, error, _list, and _stateCounter"),this.packet=new o,this.error=null,this._list=n(),this._stateCounter=0}parse(t){this.error&&this._resetState(),this._list.append(t),a("parse: current state: %s",this._states[this._stateCounter]);while((-1!==this.packet.length||this._list.length>0)&&this[this._states[this._stateCounter]]()&&!this.error)this._stateCounter++,a("parse: state complete. _stateCounter is now: %d",this._stateCounter),a("parse: packet.length: %d, buffer list length: %d",this.packet.length,this._list.length),this._stateCounter>=this._states.length&&(this._stateCounter=0);return a("parse: exited while loop. packet: %d, buffer list length: %d",this.packet.length,this._list.length),this._list.length}_parseHeader(){const t=this._list.readUInt8(0);return this.packet.cmd=s.types[t>>s.CMD_SHIFT],this.packet.retain=0!==(t&s.RETAIN_MASK),this.packet.qos=t>>s.QOS_SHIFT&s.QOS_MASK,this.packet.dup=0!==(t&s.DUP_MASK),a("_parseHeader: packet: %o",this.packet),this._list.consume(1),!0}_parseLength(){const t=this._parseVarByteNum(!0);return t&&(this.packet.length=t.value,this._list.consume(t.bytes)),a("_parseLength %d",t.value),!!t}_parsePayload(){a("_parsePayload: payload %O",this._list);let t=!1;if(0===this.packet.length||this._list.length>=this.packet.length){switch(this._pos=0,this.packet.cmd){case"connect":this._parseConnect();break;case"connack":this._parseConnack();break;case"publish":this._parsePublish();break;case"puback":case"pubrec":case"pubrel":case"pubcomp":this._parseConfirmation();break;case"subscribe":this._parseSubscribe();break;case"suback":this._parseSuback();break;case"unsubscribe":this._parseUnsubscribe();break;case"unsuback":this._parseUnsuback();break;case"pingreq":case"pingresp":break;case"disconnect":this._parseDisconnect();break;case"auth":this._parseAuth();break;default:this._emitError(new Error("Not supported"))}t=!0}return a("_parsePayload complete result: %s",t),t}_parseConnect(){let t,e,r,n;a("_parseConnect");const i={},o=this.packet,c=this._parseString();if(null===c)return this._emitError(new Error("Cannot parse protocolId"));if("MQTT"!==c&&"MQIsdp"!==c)return this._emitError(new Error("Invalid protocolId"));if(o.protocolId=c,this._pos>=this._list.length)return this._emitError(new Error("Packet too short"));if(o.protocolVersion=this._list.readUInt8(this._pos),o.protocolVersion>=128&&(o.bridgeMode=!0,o.protocolVersion=o.protocolVersion-128),3!==o.protocolVersion&&4!==o.protocolVersion&&5!==o.protocolVersion)return this._emitError(new Error("Invalid protocol version"));if(this._pos++,this._pos>=this._list.length)return this._emitError(new Error("Packet too short"));if(i.username=this._list.readUInt8(this._pos)&s.USERNAME_MASK,i.password=this._list.readUInt8(this._pos)&s.PASSWORD_MASK,i.will=this._list.readUInt8(this._pos)&s.WILL_FLAG_MASK,i.will&&(o.will={},o.will.retain=0!==(this._list.readUInt8(this._pos)&s.WILL_RETAIN_MASK),o.will.qos=(this._list.readUInt8(this._pos)&s.WILL_QOS_MASK)>>s.WILL_QOS_SHIFT),o.clean=0!==(this._list.readUInt8(this._pos)&s.CLEAN_SESSION_MASK),this._pos++,o.keepalive=this._parseNum(),-1===o.keepalive)return this._emitError(new Error("Packet too short"));if(5===o.protocolVersion){const t=this._parseProperties();Object.getOwnPropertyNames(t).length&&(o.properties=t)}const u=this._parseString();if(null===u)return this._emitError(new Error("Packet too short"));if(o.clientId=u,a("_parseConnect: packet.clientId: %s",o.clientId),i.will){if(5===o.protocolVersion){const t=this._parseProperties();Object.getOwnPropertyNames(t).length&&(o.will.properties=t)}if(t=this._parseString(),null===t)return this._emitError(new Error("Cannot parse will topic"));if(o.will.topic=t,a("_parseConnect: packet.will.topic: %s",o.will.topic),e=this._parseBuffer(),null===e)return this._emitError(new Error("Cannot parse will payload"));o.will.payload=e,a("_parseConnect: packet.will.paylaod: %s",o.will.payload)}if(i.username){if(n=this._parseString(),null===n)return this._emitError(new Error("Cannot parse username"));o.username=n,a("_parseConnect: packet.username: %s",o.username)}if(i.password){if(r=this._parseBuffer(),null===r)return this._emitError(new Error("Cannot parse password"));o.password=r}return this.settings=o,a("_parseConnect: complete"),o}_parseConnack(){a("_parseConnack");const t=this.packet;if(this._list.length<1)return null;if(t.sessionPresent=!!(this._list.readUInt8(this._pos++)&s.SESSIONPRESENT_MASK),5===this.settings.protocolVersion)this._list.length>=2?t.reasonCode=this._list.readUInt8(this._pos++):t.reasonCode=0;else{if(this._list.length<2)return null;t.returnCode=this._list.readUInt8(this._pos++)}if(-1===t.returnCode||-1===t.reasonCode)return this._emitError(new Error("Cannot parse return code"));if(5===this.settings.protocolVersion){const e=this._parseProperties();Object.getOwnPropertyNames(e).length&&(t.properties=e)}a("_parseConnack: complete")}_parsePublish(){a("_parsePublish");const t=this.packet;if(t.topic=this._parseString(),null===t.topic)return this._emitError(new Error("Cannot parse topic"));if(!(t.qos>0)||this._parseMessageId()){if(5===this.settings.protocolVersion){const e=this._parseProperties();Object.getOwnPropertyNames(e).length&&(t.properties=e)}t.payload=this._list.slice(this._pos,t.length),a("_parsePublish: payload from buffer list: %o",t.payload)}}_parseSubscribe(){a("_parseSubscribe");const t=this.packet;let e,r,n,i,o,c,u;if(1!==t.qos)return this._emitError(new Error("Wrong subscribe header"));if(t.subscriptions=[],this._parseMessageId()){if(5===this.settings.protocolVersion){const e=this._parseProperties();Object.getOwnPropertyNames(e).length&&(t.properties=e)}while(this._pos<t.length){if(e=this._parseString(),null===e)return this._emitError(new Error("Cannot parse topic"));if(this._pos>=t.length)return this._emitError(new Error("Malformed Subscribe Payload"));r=this._parseByte(),n=r&s.SUBSCRIBE_OPTIONS_QOS_MASK,c=0!==(r>>s.SUBSCRIBE_OPTIONS_NL_SHIFT&s.SUBSCRIBE_OPTIONS_NL_MASK),o=0!==(r>>s.SUBSCRIBE_OPTIONS_RAP_SHIFT&s.SUBSCRIBE_OPTIONS_RAP_MASK),i=r>>s.SUBSCRIBE_OPTIONS_RH_SHIFT&s.SUBSCRIBE_OPTIONS_RH_MASK,u={topic:e,qos:n},5===this.settings.protocolVersion?(u.nl=c,u.rap=o,u.rh=i):this.settings.bridgeMode&&(u.rh=0,u.rap=!0,u.nl=!0),a("_parseSubscribe: push subscription `%s` to subscription",u),t.subscriptions.push(u)}}}_parseSuback(){a("_parseSuback");const t=this.packet;if(this.packet.granted=[],this._parseMessageId()){if(5===this.settings.protocolVersion){const e=this._parseProperties();Object.getOwnPropertyNames(e).length&&(t.properties=e)}while(this._pos<this.packet.length)this.packet.granted.push(this._list.readUInt8(this._pos++))}}_parseUnsubscribe(){a("_parseUnsubscribe");const t=this.packet;if(t.unsubscriptions=[],this._parseMessageId()){if(5===this.settings.protocolVersion){const e=this._parseProperties();Object.getOwnPropertyNames(e).length&&(t.properties=e)}while(this._pos<t.length){const e=this._parseString();if(null===e)return this._emitError(new Error("Cannot parse topic"));a("_parseUnsubscribe: push topic `%s` to unsubscriptions",e),t.unsubscriptions.push(e)}}}_parseUnsuback(){a("_parseUnsuback");const t=this.packet;if(!this._parseMessageId())return this._emitError(new Error("Cannot parse messageId"));if(5===this.settings.protocolVersion){const e=this._parseProperties();Object.getOwnPropertyNames(e).length&&(t.properties=e),t.granted=[];while(this._pos<this.packet.length)this.packet.granted.push(this._list.readUInt8(this._pos++))}}_parseConfirmation(){a("_parseConfirmation: packet.cmd: `%s`",this.packet.cmd);const t=this.packet;if(this._parseMessageId(),5===this.settings.protocolVersion&&(t.length>2?(t.reasonCode=this._parseByte(),a("_parseConfirmation: packet.reasonCode `%d`",t.reasonCode)):t.reasonCode=0,t.length>3)){const e=this._parseProperties();Object.getOwnPropertyNames(e).length&&(t.properties=e)}return!0}_parseDisconnect(){const t=this.packet;if(a("_parseDisconnect"),5===this.settings.protocolVersion){this._list.length>0?t.reasonCode=this._parseByte():t.reasonCode=0;const e=this._parseProperties();Object.getOwnPropertyNames(e).length&&(t.properties=e)}return a("_parseDisconnect result: true"),!0}_parseAuth(){a("_parseAuth");const t=this.packet;if(5!==this.settings.protocolVersion)return this._emitError(new Error("Not supported auth packet for this version MQTT"));t.reasonCode=this._parseByte();const e=this._parseProperties();return Object.getOwnPropertyNames(e).length&&(t.properties=e),a("_parseAuth: result: true"),!0}_parseMessageId(){const t=this.packet;return t.messageId=this._parseNum(),null===t.messageId?(this._emitError(new Error("Cannot parse messageId")),!1):(a("_parseMessageId: packet.messageId %d",t.messageId),!0)}_parseString(t){const e=this._parseNum(),r=e+this._pos;if(-1===e||r>this._list.length||r>this.packet.length)return null;const n=this._list.toString("utf8",this._pos,r);return this._pos+=e,a("_parseString: result: %s",n),n}_parseStringPair(){return a("_parseStringPair"),{name:this._parseString(),value:this._parseString()}}_parseBuffer(){const t=this._parseNum(),e=t+this._pos;if(-1===t||e>this._list.length||e>this.packet.length)return null;const r=this._list.slice(this._pos,e);return this._pos+=t,a("_parseBuffer: result: %o",r),r}_parseNum(){if(this._list.length-this._pos<2)return-1;const t=this._list.readUInt16BE(this._pos);return this._pos+=2,a("_parseNum: result: %s",t),t}_parse4ByteNum(){if(this._list.length-this._pos<4)return-1;const t=this._list.readUInt32BE(this._pos);return this._pos+=4,a("_parse4ByteNum: result: %s",t),t}_parseVarByteNum(t){a("_parseVarByteNum");const e=4;let r,n=0,i=1,o=0,c=!1;const u=this._pos?this._pos:0;while(n<e&&u+n<this._list.length){if(r=this._list.readUInt8(u+n++),o+=i*(r&s.VARBYTEINT_MASK),i*=128,0===(r&s.VARBYTEINT_FIN_MASK)){c=!0;break}if(this._list.length<=n)break}return!c&&n===e&&this._list.length>=n&&this._emitError(new Error("Invalid variable byte integer")),u&&(this._pos+=n),c=!!c&&(t?{bytes:n,value:o}:o),a("_parseVarByteNum: result: %o",c),c}_parseByte(){let t;return this._pos<this._list.length&&(t=this._list.readUInt8(this._pos),this._pos++),a("_parseByte: result: %o",t),t}_parseByType(t){switch(a("_parseByType: type: %s",t),t){case"byte":return 0!==this._parseByte();case"int8":return this._parseByte();case"int16":return this._parseNum();case"int32":return this._parse4ByteNum();case"var":return this._parseVarByteNum();case"string":return this._parseString();case"pair":return this._parseStringPair();case"binary":return this._parseBuffer()}}_parseProperties(){a("_parseProperties");const t=this._parseVarByteNum(),e=this._pos,r=e+t,n={};while(this._pos<r){const t=this._parseByte();if(!t)return this._emitError(new Error("Cannot parse property code type")),!1;const e=s.propertiesCodes[t];if(!e)return this._emitError(new Error("Unknown property")),!1;if("userProperties"!==e)n[e]?(Array.isArray(n[e])||(n[e]=[n[e]]),n[e].push(this._parseByType(s.propertiesTypes[e]))):n[e]=this._parseByType(s.propertiesTypes[e]);else{n[e]||(n[e]=Object.create(null));const t=this._parseByType(s.propertiesTypes[e]);if(n[e][t.name])if(Array.isArray(n[e][t.name]))n[e][t.name].push(t.value);else{const r=n[e][t.name];n[e][t.name]=[r],n[e][t.name].push(t.value)}else n[e][t.name]=t.value}}return n}_newPacket(){return a("_newPacket"),this.packet&&(this._list.consume(this.packet.length),a("_newPacket: parser emit packet: packet.cmd: %s, packet.payload: %s, packet.length: %d",this.packet.cmd,this.packet.payload,this.packet.length),this.emit("packet",this.packet)),a("_newPacket: new packet"),this.packet=new o,this._pos=0,!0}_emitError(t){a("_emitError"),this.error=t,this.emit("error",t)}}t.exports=c},"21bf":function(t,e,r){(function(e){(function(e,r){t.exports=r()})(0,(function(){var t=t||function(t,n){var i;if("undefined"!==typeof window&&window.crypto&&(i=window.crypto),"undefined"!==typeof self&&self.crypto&&(i=self.crypto),"undefined"!==typeof globalThis&&globalThis.crypto&&(i=globalThis.crypto),!i&&"undefined"!==typeof window&&window.msCrypto&&(i=window.msCrypto),!i&&"undefined"!==typeof e&&e.crypto&&(i=e.crypto),!i)try{i=r(2)}catch(g){}var o=function(){if(i){if("function"===typeof i.getRandomValues)try{return i.getRandomValues(new Uint32Array(1))[0]}catch(g){}if("function"===typeof i.randomBytes)try{return i.randomBytes(4).readInt32LE()}catch(g){}}throw new Error("Native crypto module could not be used to get secure random number.")},s=Object.create||function(){function t(){}return function(e){var r;return t.prototype=e,r=new t,t.prototype=null,r}}(),a={},c=a.lib={},u=c.Base=function(){return{extend:function(t){var e=s(this);return t&&e.mixIn(t),e.hasOwnProperty("init")&&this.init!==e.init||(e.init=function(){e.$super.init.apply(this,arguments)}),e.init.prototype=e,e.$super=this,e},create:function(){var t=this.extend();return t.init.apply(t,arguments),t},init:function(){},mixIn:function(t){for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e]);t.hasOwnProperty("toString")&&(this.toString=t.toString)},clone:function(){return this.init.prototype.extend(this)}}}(),l=c.WordArray=u.extend({init:function(t,e){t=this.words=t||[],this.sigBytes=e!=n?e:4*t.length},toString:function(t){return(t||f).stringify(this)},concat:function(t){var e=this.words,r=t.words,n=this.sigBytes,i=t.sigBytes;if(this.clamp(),n%4)for(var o=0;o<i;o++){var s=r[o>>>2]>>>24-o%4*8&255;e[n+o>>>2]|=s<<24-(n+o)%4*8}else for(var a=0;a<i;a+=4)e[n+a>>>2]=r[a>>>2];return this.sigBytes+=i,this},clamp:function(){var e=this.words,r=this.sigBytes;e[r>>>2]&=4294967295<<32-r%4*8,e.length=t.ceil(r/4)},clone:function(){var t=u.clone.call(this);return t.words=this.words.slice(0),t},random:function(t){for(var e=[],r=0;r<t;r+=4)e.push(o());return new l.init(e,t)}}),h=a.enc={},f=h.Hex={stringify:function(t){for(var e=t.words,r=t.sigBytes,n=[],i=0;i<r;i++){var o=e[i>>>2]>>>24-i%4*8&255;n.push((o>>>4).toString(16)),n.push((15&o).toString(16))}return n.join("")},parse:function(t){for(var e=t.length,r=[],n=0;n<e;n+=2)r[n>>>3]|=parseInt(t.substr(n,2),16)<<24-n%8*4;return new l.init(r,e/2)}},p=h.Latin1={stringify:function(t){for(var e=t.words,r=t.sigBytes,n=[],i=0;i<r;i++){var o=e[i>>>2]>>>24-i%4*8&255;n.push(String.fromCharCode(o))}return n.join("")},parse:function(t){for(var e=t.length,r=[],n=0;n<e;n++)r[n>>>2]|=(255&t.charCodeAt(n))<<24-n%4*8;return new l.init(r,e)}},d=h.Utf8={stringify:function(t){try{return decodeURIComponent(escape(p.stringify(t)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(t){return p.parse(unescape(encodeURIComponent(t)))}},y=c.BufferedBlockAlgorithm=u.extend({reset:function(){this._data=new l.init,this._nDataBytes=0},_append:function(t){"string"==typeof t&&(t=d.parse(t)),this._data.concat(t),this._nDataBytes+=t.sigBytes},_process:function(e){var r,n=this._data,i=n.words,o=n.sigBytes,s=this.blockSize,a=4*s,c=o/a;c=e?t.ceil(c):t.max((0|c)-this._minBufferSize,0);var u=c*s,h=t.min(4*u,o);if(u){for(var f=0;f<u;f+=s)this._doProcessBlock(i,f);r=i.splice(0,u),n.sigBytes-=h}return new l.init(r,h)},clone:function(){var t=u.clone.call(this);return t._data=this._data.clone(),t},_minBufferSize:0}),b=(c.Hasher=y.extend({cfg:u.extend(),init:function(t){this.cfg=this.cfg.extend(t),this.reset()},reset:function(){y.reset.call(this),this._doReset()},update:function(t){return this._append(t),this._process(),this},finalize:function(t){t&&this._append(t);var e=this._doFinalize();return e},blockSize:16,_createHelper:function(t){return function(e,r){return new t.init(r).finalize(e)}},_createHmacHelper:function(t){return function(e,r){return new b.HMAC.init(t,r).finalize(e)}}}),a.algo={});return a}(Math);return t}))}).call(this,r("c8ba"))},2500:function(t,e,r){"use strict";var n=r("a29f"),i=Object.prototype.hasOwnProperty,o=Array.isArray,s={allowDots:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decoder:n.decode,delimiter:"&",depth:5,ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictNullHandling:!1},a=function(t){return t.replace(/&#(\d+);/g,(function(t,e){return String.fromCharCode(parseInt(e,10))}))},c=function(t,e){return t&&"string"===typeof t&&e.comma&&t.indexOf(",")>-1?t.split(","):t},u="utf8=%26%2310003%3B",l="utf8=%E2%9C%93",h=function(t,e){var r,h={__proto__:null},f=e.ignoreQueryPrefix?t.replace(/^\?/,""):t,p=e.parameterLimit===1/0?void 0:e.parameterLimit,d=f.split(e.delimiter,p),y=-1,b=e.charset;if(e.charsetSentinel)for(r=0;r<d.length;++r)0===d[r].indexOf("utf8=")&&(d[r]===l?b="utf-8":d[r]===u&&(b="iso-8859-1"),y=r,r=d.length);for(r=0;r<d.length;++r)if(r!==y){var g,m,v=d[r],_=v.indexOf("]="),w=-1===_?v.indexOf("="):_+1;-1===w?(g=e.decoder(v,s.decoder,b,"key"),m=e.strictNullHandling?null:""):(g=e.decoder(v.slice(0,w),s.decoder,b,"key"),m=n.maybeMap(c(v.slice(w+1),e),(function(t){return e.decoder(t,s.decoder,b,"value")}))),m&&e.interpretNumericEntities&&"iso-8859-1"===b&&(m=a(m)),v.indexOf("[]=")>-1&&(m=o(m)?[m]:m),i.call(h,g)?h[g]=n.combine(h[g],m):h[g]=m}return h},f=function(t,e,r,n){for(var i=n?e:c(e,r),o=t.length-1;o>=0;--o){var s,a=t[o];if("[]"===a&&r.parseArrays)s=[].concat(i);else{s=r.plainObjects?Object.create(null):{};var u="["===a.charAt(0)&&"]"===a.charAt(a.length-1)?a.slice(1,-1):a,l=parseInt(u,10);r.parseArrays||""!==u?!isNaN(l)&&a!==u&&String(l)===u&&l>=0&&r.parseArrays&&l<=r.arrayLimit?(s=[],s[l]=i):"__proto__"!==u&&(s[u]=i):s={0:i}}i=s}return i},p=function(t,e,r,n){if(t){var o=r.allowDots?t.replace(/\.([^.[]+)/g,"[$1]"):t,s=/(\[[^[\]]*])/,a=/(\[[^[\]]*])/g,c=r.depth>0&&s.exec(o),u=c?o.slice(0,c.index):o,l=[];if(u){if(!r.plainObjects&&i.call(Object.prototype,u)&&!r.allowPrototypes)return;l.push(u)}var h=0;while(r.depth>0&&null!==(c=a.exec(o))&&h<r.depth){if(h+=1,!r.plainObjects&&i.call(Object.prototype,c[1].slice(1,-1))&&!r.allowPrototypes)return;l.push(c[1])}return c&&l.push("["+o.slice(c.index)+"]"),f(l,e,r,n)}},d=function(t){if(!t)return s;if(null!==t.decoder&&void 0!==t.decoder&&"function"!==typeof t.decoder)throw new TypeError("Decoder has to be a function.");if("undefined"!==typeof t.charset&&"utf-8"!==t.charset&&"iso-8859-1"!==t.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var e="undefined"===typeof t.charset?s.charset:t.charset;return{allowDots:"undefined"===typeof t.allowDots?s.allowDots:!!t.allowDots,allowPrototypes:"boolean"===typeof t.allowPrototypes?t.allowPrototypes:s.allowPrototypes,allowSparse:"boolean"===typeof t.allowSparse?t.allowSparse:s.allowSparse,arrayLimit:"number"===typeof t.arrayLimit?t.arrayLimit:s.arrayLimit,charset:e,charsetSentinel:"boolean"===typeof t.charsetSentinel?t.charsetSentinel:s.charsetSentinel,comma:"boolean"===typeof t.comma?t.comma:s.comma,decoder:"function"===typeof t.decoder?t.decoder:s.decoder,delimiter:"string"===typeof t.delimiter||n.isRegExp(t.delimiter)?t.delimiter:s.delimiter,depth:"number"===typeof t.depth||!1===t.depth?+t.depth:s.depth,ignoreQueryPrefix:!0===t.ignoreQueryPrefix,interpretNumericEntities:"boolean"===typeof t.interpretNumericEntities?t.interpretNumericEntities:s.interpretNumericEntities,parameterLimit:"number"===typeof t.parameterLimit?t.parameterLimit:s.parameterLimit,parseArrays:!1!==t.parseArrays,plainObjects:"boolean"===typeof t.plainObjects?t.plainObjects:s.plainObjects,strictNullHandling:"boolean"===typeof t.strictNullHandling?t.strictNullHandling:s.strictNullHandling}};t.exports=function(t,e){var r=d(e);if(""===t||null===t||"undefined"===typeof t)return r.plainObjects?Object.create(null):{};for(var i="string"===typeof t?h(t,r):t,o=r.plainObjects?Object.create(null):{},s=Object.keys(i),a=0;a<s.length;++a){var c=s[a],u=p(c,i[c],r,"string"===typeof t);o=n.merge(o,u,r)}return!0===r.allowSparse?o:n.compact(o)}},2527:function(t,e){t.exports=function(){throw new Error("Readable.from is not available in the browser")}},2714:function(t,e,r){(function(e){var n="function"===typeof Map&&Map.prototype,i=Object.getOwnPropertyDescriptor&&n?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,o=n&&i&&"function"===typeof i.get?i.get:null,s=n&&Map.prototype.forEach,a="function"===typeof Set&&Set.prototype,c=Object.getOwnPropertyDescriptor&&a?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,u=a&&c&&"function"===typeof c.get?c.get:null,l=a&&Set.prototype.forEach,h="function"===typeof WeakMap&&WeakMap.prototype,f=h?WeakMap.prototype.has:null,p="function"===typeof WeakSet&&WeakSet.prototype,d=p?WeakSet.prototype.has:null,y="function"===typeof WeakRef&&WeakRef.prototype,b=y?WeakRef.prototype.deref:null,g=Boolean.prototype.valueOf,m=Object.prototype.toString,v=Function.prototype.toString,_=String.prototype.match,w=String.prototype.slice,S=String.prototype.replace,E=String.prototype.toUpperCase,k=String.prototype.toLowerCase,A=RegExp.prototype.test,x=Array.prototype.concat,P=Array.prototype.join,O=Array.prototype.slice,R=Math.floor,M="function"===typeof BigInt?BigInt.prototype.valueOf:null,C=Object.getOwnPropertySymbols,T="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?Symbol.prototype.toString:null,B="function"===typeof Symbol&&"object"===typeof Symbol.iterator,I="function"===typeof Symbol&&Symbol.toStringTag&&(typeof Symbol.toStringTag===B||"symbol")?Symbol.toStringTag:null,j=Object.prototype.propertyIsEnumerable,N=("function"===typeof Reflect?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(t){return t.__proto__}:null);function L(t,e){if(t===1/0||t===-1/0||t!==t||t&&t>-1e3&&t<1e3||A.call(/e/,e))return e;var r=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"===typeof t){var n=t<0?-R(-t):R(t);if(n!==t){var i=String(n),o=w.call(e,i.length+1);return S.call(i,r,"$&_")+"."+S.call(S.call(o,/([0-9]{3})/g,"$&_"),/_$/,"")}}return S.call(e,r,"$&_")}var D=r(7),F=D.custom,U=J(F)?F:null;function H(t,e,r){var n="double"===(r.quoteStyle||e)?'"':"'";return n+t+n}function q(t){return S.call(String(t),/"/g,"&quot;")}function W(t){return"[object Array]"===tt(t)&&(!I||!("object"===typeof t&&I in t))}function K(t){return"[object Date]"===tt(t)&&(!I||!("object"===typeof t&&I in t))}function z(t){return"[object RegExp]"===tt(t)&&(!I||!("object"===typeof t&&I in t))}function V(t){return"[object Error]"===tt(t)&&(!I||!("object"===typeof t&&I in t))}function G(t){return"[object String]"===tt(t)&&(!I||!("object"===typeof t&&I in t))}function Q(t){return"[object Number]"===tt(t)&&(!I||!("object"===typeof t&&I in t))}function $(t){return"[object Boolean]"===tt(t)&&(!I||!("object"===typeof t&&I in t))}function J(t){if(B)return t&&"object"===typeof t&&t instanceof Symbol;if("symbol"===typeof t)return!0;if(!t||"object"!==typeof t||!T)return!1;try{return T.call(t),!0}catch(e){}return!1}function Y(t){if(!t||"object"!==typeof t||!M)return!1;try{return M.call(t),!0}catch(e){}return!1}t.exports=function t(r,n,i,a){var c=n||{};if(Z(c,"quoteStyle")&&"single"!==c.quoteStyle&&"double"!==c.quoteStyle)throw new TypeError('option "quoteStyle" must be "single" or "double"');if(Z(c,"maxStringLength")&&("number"===typeof c.maxStringLength?c.maxStringLength<0&&c.maxStringLength!==1/0:null!==c.maxStringLength))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var h=!Z(c,"customInspect")||c.customInspect;if("boolean"!==typeof h&&"symbol"!==h)throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(Z(c,"indent")&&null!==c.indent&&"\t"!==c.indent&&!(parseInt(c.indent,10)===c.indent&&c.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(Z(c,"numericSeparator")&&"boolean"!==typeof c.numericSeparator)throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var f=c.numericSeparator;if("undefined"===typeof r)return"undefined";if(null===r)return"null";if("boolean"===typeof r)return r?"true":"false";if("string"===typeof r)return ut(r,c);if("number"===typeof r){if(0===r)return 1/0/r>0?"0":"-0";var p=String(r);return f?L(r,p):p}if("bigint"===typeof r){var d=String(r)+"n";return f?L(r,d):d}var y="undefined"===typeof c.depth?5:c.depth;if("undefined"===typeof i&&(i=0),i>=y&&y>0&&"object"===typeof r)return W(r)?"[Array]":"[Object]";var b=yt(c,i);if("undefined"===typeof a)a=[];else if(rt(a,r)>=0)return"[Circular]";function m(e,r,n){if(r&&(a=O.call(a),a.push(r)),n){var o={depth:c.depth};return Z(c,"quoteStyle")&&(o.quoteStyle=c.quoteStyle),t(e,o,i+1,a)}return t(e,c,i+1,a)}if("function"===typeof r&&!z(r)){var v=et(r),_=gt(r,m);return"[Function"+(v?": "+v:" (anonymous)")+"]"+(_.length>0?" { "+P.call(_,", ")+" }":"")}if(J(r)){var E=B?S.call(String(r),/^(Symbol\(.*\))_[^)]*$/,"$1"):T.call(r);return"object"!==typeof r||B?E:ht(E)}if(ct(r)){for(var A="<"+k.call(String(r.nodeName)),R=r.attributes||[],C=0;C<R.length;C++)A+=" "+R[C].name+"="+H(q(R[C].value),"double",c);return A+=">",r.childNodes&&r.childNodes.length&&(A+="..."),A+="</"+k.call(String(r.nodeName))+">",A}if(W(r)){if(0===r.length)return"[]";var F=gt(r,m);return b&&!dt(F)?"["+bt(F,b)+"]":"[ "+P.call(F,", ")+" ]"}if(V(r)){var X=gt(r,m);return"cause"in Error.prototype||!("cause"in r)||j.call(r,"cause")?0===X.length?"["+String(r)+"]":"{ ["+String(r)+"] "+P.call(X,", ")+" }":"{ ["+String(r)+"] "+P.call(x.call("[cause]: "+m(r.cause),X),", ")+" }"}if("object"===typeof r&&h){if(U&&"function"===typeof r[U]&&D)return D(r,{depth:y-i});if("symbol"!==h&&"function"===typeof r.inspect)return r.inspect()}if(nt(r)){var lt=[];return s&&s.call(r,(function(t,e){lt.push(m(e,r,!0)+" => "+m(t,r))})),pt("Map",o.call(r),lt,b)}if(st(r)){var mt=[];return l&&l.call(r,(function(t){mt.push(m(t,r))})),pt("Set",u.call(r),mt,b)}if(it(r))return ft("WeakMap");if(at(r))return ft("WeakSet");if(ot(r))return ft("WeakRef");if(Q(r))return ht(m(Number(r)));if(Y(r))return ht(m(M.call(r)));if($(r))return ht(g.call(r));if(G(r))return ht(m(String(r)));if("undefined"!==typeof window&&r===window)return"{ [object Window] }";if(r===e)return"{ [object globalThis] }";if(!K(r)&&!z(r)){var vt=gt(r,m),_t=N?N(r)===Object.prototype:r instanceof Object||r.constructor===Object,wt=r instanceof Object?"":"null prototype",St=!_t&&I&&Object(r)===r&&I in r?w.call(tt(r),8,-1):wt?"Object":"",Et=_t||"function"!==typeof r.constructor?"":r.constructor.name?r.constructor.name+" ":"",kt=Et+(St||wt?"["+P.call(x.call([],St||[],wt||[]),": ")+"] ":"");return 0===vt.length?kt+"{}":b?kt+"{"+bt(vt,b)+"}":kt+"{ "+P.call(vt,", ")+" }"}return String(r)};var X=Object.prototype.hasOwnProperty||function(t){return t in this};function Z(t,e){return X.call(t,e)}function tt(t){return m.call(t)}function et(t){if(t.name)return t.name;var e=_.call(v.call(t),/^function\s*([\w$]+)/);return e?e[1]:null}function rt(t,e){if(t.indexOf)return t.indexOf(e);for(var r=0,n=t.length;r<n;r++)if(t[r]===e)return r;return-1}function nt(t){if(!o||!t||"object"!==typeof t)return!1;try{o.call(t);try{u.call(t)}catch(e){return!0}return t instanceof Map}catch(r){}return!1}function it(t){if(!f||!t||"object"!==typeof t)return!1;try{f.call(t,f);try{d.call(t,d)}catch(e){return!0}return t instanceof WeakMap}catch(r){}return!1}function ot(t){if(!b||!t||"object"!==typeof t)return!1;try{return b.call(t),!0}catch(e){}return!1}function st(t){if(!u||!t||"object"!==typeof t)return!1;try{u.call(t);try{o.call(t)}catch(e){return!0}return t instanceof Set}catch(r){}return!1}function at(t){if(!d||!t||"object"!==typeof t)return!1;try{d.call(t,d);try{f.call(t,f)}catch(e){return!0}return t instanceof WeakSet}catch(r){}return!1}function ct(t){return!(!t||"object"!==typeof t)&&("undefined"!==typeof HTMLElement&&t instanceof HTMLElement||"string"===typeof t.nodeName&&"function"===typeof t.getAttribute)}function ut(t,e){if(t.length>e.maxStringLength){var r=t.length-e.maxStringLength,n="... "+r+" more character"+(r>1?"s":"");return ut(w.call(t,0,e.maxStringLength),e)+n}var i=S.call(S.call(t,/(['\\])/g,"\\$1"),/[\x00-\x1f]/g,lt);return H(i,"single",e)}function lt(t){var e=t.charCodeAt(0),r={8:"b",9:"t",10:"n",12:"f",13:"r"}[e];return r?"\\"+r:"\\x"+(e<16?"0":"")+E.call(e.toString(16))}function ht(t){return"Object("+t+")"}function ft(t){return t+" { ? }"}function pt(t,e,r,n){var i=n?bt(r,n):P.call(r,", ");return t+" ("+e+") {"+i+"}"}function dt(t){for(var e=0;e<t.length;e++)if(rt(t[e],"\n")>=0)return!1;return!0}function yt(t,e){var r;if("\t"===t.indent)r="\t";else{if(!("number"===typeof t.indent&&t.indent>0))return null;r=P.call(Array(t.indent+1)," ")}return{base:r,prev:P.call(Array(e+1),r)}}function bt(t,e){if(0===t.length)return"";var r="\n"+e.prev+e.base;return r+P.call(t,","+r)+"\n"+e.prev}function gt(t,e){var r=W(t),n=[];if(r){n.length=t.length;for(var i=0;i<t.length;i++)n[i]=Z(t,i)?e(t[i],t):""}var o,s="function"===typeof C?C(t):[];if(B){o={};for(var a=0;a<s.length;a++)o["$"+s[a]]=s[a]}for(var c in t)Z(t,c)&&(r&&String(Number(c))===c&&c<t.length||B&&o["$"+c]instanceof Symbol||(A.call(/[^\w$]/,c)?n.push(e(c,t)+": "+e(t[c],t)):n.push(c+": "+e(t[c],t))));if("function"===typeof C)for(var u=0;u<s.length;u++)j.call(t,s[u])&&n.push("["+e(s[u])+"]: "+e(t[s[u]],t));return n}}).call(this,r("c8ba"))},"29a2":function(t,e,r){"use strict";function n(t,e,r){var n=this;this._callback=t,this._args=r,this._interval=setInterval(t,e,this._args),this.reschedule=function(t){t||(t=n._interval),n._interval&&clearInterval(n._interval),n._interval=setInterval(n._callback,t,n._args)},this.clear=function(){n._interval&&(clearInterval(n._interval),n._interval=void 0)},this.destroy=function(){n._interval&&clearInterval(n._interval),n._callback=void 0,n._interval=void 0,n._args=void 0}}function i(){if("function"!==typeof arguments[0])throw new Error("callback needed");if("number"!==typeof arguments[1])throw new Error("interval needed");var t;if(arguments.length>0){t=new Array(arguments.length-2);for(var e=0;e<t.length;e++)t[e]=arguments[e+2]}return new n(arguments[0],arguments[1],t)}t.exports=i},"2a28":function(t,e,r){"use strict";t.exports=r("0050")()},"2a66":function(t,e,r){(function(e,n,i){t.exports=n(r("21bf"),r("38ba"))})(0,(function(t){return t.pad.ZeroPadding={pad:function(t,e){var r=4*e;t.clamp(),t.sigBytes+=r-(t.sigBytes%r||r)},unpad:function(t){var e=t.words,r=t.sigBytes-1;for(r=t.sigBytes-1;r>=0;r--)if(e[r>>>2]>>>24-r%4*8&255){t.sigBytes=r+1;break}}},t.pad.ZeroPadding}))},"2aa9":function(t,e,r){"use strict";var n=r("00ce"),i=n("%Object.getOwnPropertyDescriptor%",!0);if(i)try{i([],"length")}catch(o){i=null}t.exports=i},"2b79":function(t,e,r){(function(e,n,i){t.exports=n(r("21bf"),r("df2f"),r("5980"))})(0,(function(t){return function(){var e=t,r=e.lib,n=r.Base,i=r.WordArray,o=e.algo,s=o.MD5,a=o.EvpKDF=n.extend({cfg:n.extend({keySize:4,hasher:s,iterations:1}),init:function(t){this.cfg=this.cfg.extend(t)},compute:function(t,e){var r,n=this.cfg,o=n.hasher.create(),s=i.create(),a=s.words,c=n.keySize,u=n.iterations;while(a.length<c){r&&o.update(r),r=o.update(t).finalize(e),o.reset();for(var l=1;l<u;l++)r=o.finalize(r),o.reset();s.concat(r)}return s.sigBytes=4*c,s}});e.EvpKDF=function(t,e,r){return a.create(r).compute(t,e)}}(),t.EvpKDF}))},"2fae":function(t,e,r){"use strict";function n(t){var e=this;if(e instanceof n||(e=new n),e.tail=null,e.head=null,e.length=0,t&&"function"===typeof t.forEach)t.forEach((function(t){e.push(t)}));else if(arguments.length>0)for(var r=0,i=arguments.length;r<i;r++)e.push(arguments[r]);return e}function i(t,e,r){var n=e===t.head?new a(r,null,e,t):new a(r,e,e.next,t);return null===n.next&&(t.tail=n),null===n.prev&&(t.head=n),t.length++,n}function o(t,e){t.tail=new a(e,t.tail,null,t),t.head||(t.head=t.tail),t.length++}function s(t,e){t.head=new a(e,null,t.head,t),t.tail||(t.tail=t.head),t.length++}function a(t,e,r,n){if(!(this instanceof a))return new a(t,e,r,n);this.list=n,this.value=t,e?(e.next=this,this.prev=e):this.prev=null,r?(r.prev=this,this.next=r):this.next=null}t.exports=n,n.Node=a,n.create=n,n.prototype.removeNode=function(t){if(t.list!==this)throw new Error("removing node which does not belong to this list");var e=t.next,r=t.prev;return e&&(e.prev=r),r&&(r.next=e),t===this.head&&(this.head=e),t===this.tail&&(this.tail=r),t.list.length--,t.next=null,t.prev=null,t.list=null,e},n.prototype.unshiftNode=function(t){if(t!==this.head){t.list&&t.list.removeNode(t);var e=this.head;t.list=this,t.next=e,e&&(e.prev=t),this.head=t,this.tail||(this.tail=t),this.length++}},n.prototype.pushNode=function(t){if(t!==this.tail){t.list&&t.list.removeNode(t);var e=this.tail;t.list=this,t.prev=e,e&&(e.next=t),this.tail=t,this.head||(this.head=t),this.length++}},n.prototype.push=function(){for(var t=0,e=arguments.length;t<e;t++)o(this,arguments[t]);return this.length},n.prototype.unshift=function(){for(var t=0,e=arguments.length;t<e;t++)s(this,arguments[t]);return this.length},n.prototype.pop=function(){if(this.tail){var t=this.tail.value;return this.tail=this.tail.prev,this.tail?this.tail.next=null:this.head=null,this.length--,t}},n.prototype.shift=function(){if(this.head){var t=this.head.value;return this.head=this.head.next,this.head?this.head.prev=null:this.tail=null,this.length--,t}},n.prototype.forEach=function(t,e){e=e||this;for(var r=this.head,n=0;null!==r;n++)t.call(e,r.value,n,this),r=r.next},n.prototype.forEachReverse=function(t,e){e=e||this;for(var r=this.tail,n=this.length-1;null!==r;n--)t.call(e,r.value,n,this),r=r.prev},n.prototype.get=function(t){for(var e=0,r=this.head;null!==r&&e<t;e++)r=r.next;if(e===t&&null!==r)return r.value},n.prototype.getReverse=function(t){for(var e=0,r=this.tail;null!==r&&e<t;e++)r=r.prev;if(e===t&&null!==r)return r.value},n.prototype.map=function(t,e){e=e||this;for(var r=new n,i=this.head;null!==i;)r.push(t.call(e,i.value,this)),i=i.next;return r},n.prototype.mapReverse=function(t,e){e=e||this;for(var r=new n,i=this.tail;null!==i;)r.push(t.call(e,i.value,this)),i=i.prev;return r},n.prototype.reduce=function(t,e){var r,n=this.head;if(arguments.length>1)r=e;else{if(!this.head)throw new TypeError("Reduce of empty list with no initial value");n=this.head.next,r=this.head.value}for(var i=0;null!==n;i++)r=t(r,n.value,i),n=n.next;return r},n.prototype.reduceReverse=function(t,e){var r,n=this.tail;if(arguments.length>1)r=e;else{if(!this.tail)throw new TypeError("Reduce of empty list with no initial value");n=this.tail.prev,r=this.tail.value}for(var i=this.length-1;null!==n;i--)r=t(r,n.value,i),n=n.prev;return r},n.prototype.toArray=function(){for(var t=new Array(this.length),e=0,r=this.head;null!==r;e++)t[e]=r.value,r=r.next;return t},n.prototype.toArrayReverse=function(){for(var t=new Array(this.length),e=0,r=this.tail;null!==r;e++)t[e]=r.value,r=r.prev;return t},n.prototype.slice=function(t,e){e=e||this.length,e<0&&(e+=this.length),t=t||0,t<0&&(t+=this.length);var r=new n;if(e<t||e<0)return r;t<0&&(t=0),e>this.length&&(e=this.length);for(var i=0,o=this.head;null!==o&&i<t;i++)o=o.next;for(;null!==o&&i<e;i++,o=o.next)r.push(o.value);return r},n.prototype.sliceReverse=function(t,e){e=e||this.length,e<0&&(e+=this.length),t=t||0,t<0&&(t+=this.length);var r=new n;if(e<t||e<0)return r;t<0&&(t=0),e>this.length&&(e=this.length);for(var i=this.length,o=this.tail;null!==o&&i>e;i--)o=o.prev;for(;null!==o&&i>t;i--,o=o.prev)r.push(o.value);return r},n.prototype.splice=function(t,e,...r){t>this.length&&(t=this.length-1),t<0&&(t=this.length+t);for(var n=0,o=this.head;null!==o&&n<t;n++)o=o.next;var s=[];for(n=0;o&&n<e;n++)s.push(o.value),o=this.removeNode(o);null===o&&(o=this.tail),o!==this.head&&o!==this.tail&&(o=o.prev);for(n=0;n<r.length;n++)o=i(this,o,r[n]);return s},n.prototype.reverse=function(){for(var t=this.head,e=this.tail,r=t;null!==r;r=r.prev){var n=r.prev;r.prev=r.next,r.next=n}return this.head=e,this.tail=t,this};try{r("aff9")(n)}catch(c){}},"31b5":function(t,e,r){"use strict";var n=r("fbd7").codes.ERR_INVALID_OPT_VALUE;function i(t,e,r){return null!=t.highWaterMark?t.highWaterMark:e?t[r]:null}function o(t,e,r,o){var s=i(e,o,r);if(null!=s){if(!isFinite(s)||Math.floor(s)!==s||s<0){var a=o?r:"highWaterMark";throw new n(a,s)}return Math.floor(s)}return t.objectMode?16:16384}t.exports={getHighWaterMark:o}},3252:function(t,e,r){(function(e,n){t.exports=n(r("21bf"))})(0,(function(t){return function(e){var r=t,n=r.lib,i=n.Base,o=n.WordArray,s=r.x64={};s.Word=i.extend({init:function(t,e){this.high=t,this.low=e}}),s.WordArray=i.extend({init:function(t,r){t=this.words=t||[],this.sigBytes=r!=e?r:8*t.length},toX32:function(){for(var t=this.words,e=t.length,r=[],n=0;n<e;n++){var i=t[n];r.push(i.high),r.push(i.low)}return o.create(r,this.sigBytes)},clone:function(){for(var t=i.clone.call(this),e=t.words=this.words.slice(0),r=e.length,n=0;n<r;n++)e[n]=e[n].clone();return t}})}(),t}))},33013:function(t,e,r){"use strict";const{Buffer:n}=r("b639"),i=Symbol.for("BufferList");function o(t){if(!(this instanceof o))return new o(t);o._init.call(this,t)}o._init=function(t){Object.defineProperty(this,i,{value:!0}),this._bufs=[],this.length=0,t&&this.append(t)},o.prototype._new=function(t){return new o(t)},o.prototype._offset=function(t){if(0===t)return[0,0];let e=0;for(let r=0;r<this._bufs.length;r++){const n=e+this._bufs[r].length;if(t<n||r===this._bufs.length-1)return[r,t-e];e=n}},o.prototype._reverseOffset=function(t){const e=t[0];let r=t[1];for(let n=0;n<e;n++)r+=this._bufs[n].length;return r},o.prototype.get=function(t){if(t>this.length||t<0)return;const e=this._offset(t);return this._bufs[e[0]][e[1]]},o.prototype.slice=function(t,e){return"number"===typeof t&&t<0&&(t+=this.length),"number"===typeof e&&e<0&&(e+=this.length),this.copy(null,0,t,e)},o.prototype.copy=function(t,e,r,i){if(("number"!==typeof r||r<0)&&(r=0),("number"!==typeof i||i>this.length)&&(i=this.length),r>=this.length)return t||n.alloc(0);if(i<=0)return t||n.alloc(0);const o=!!t,s=this._offset(r),a=i-r;let c=a,u=o&&e||0,l=s[1];if(0===r&&i===this.length){if(!o)return 1===this._bufs.length?this._bufs[0]:n.concat(this._bufs,this.length);for(let e=0;e<this._bufs.length;e++)this._bufs[e].copy(t,u),u+=this._bufs[e].length;return t}if(c<=this._bufs[s[0]].length-l)return o?this._bufs[s[0]].copy(t,e,l,l+c):this._bufs[s[0]].slice(l,l+c);o||(t=n.allocUnsafe(a));for(let n=s[0];n<this._bufs.length;n++){const e=this._bufs[n].length-l;if(!(c>e)){this._bufs[n].copy(t,u,l,l+c),u+=e;break}this._bufs[n].copy(t,u,l),u+=e,c-=e,l&&(l=0)}return t.length>u?t.slice(0,u):t},o.prototype.shallowSlice=function(t,e){if(t=t||0,e="number"!==typeof e?this.length:e,t<0&&(t+=this.length),e<0&&(e+=this.length),t===e)return this._new();const r=this._offset(t),n=this._offset(e),i=this._bufs.slice(r[0],n[0]+1);return 0===n[1]?i.pop():i[i.length-1]=i[i.length-1].slice(0,n[1]),0!==r[1]&&(i[0]=i[0].slice(r[1])),this._new(i)},o.prototype.toString=function(t,e,r){return this.slice(e,r).toString(t)},o.prototype.consume=function(t){if(t=Math.trunc(t),Number.isNaN(t)||t<=0)return this;while(this._bufs.length){if(!(t>=this._bufs[0].length)){this._bufs[0]=this._bufs[0].slice(t),this.length-=t;break}t-=this._bufs[0].length,this.length-=this._bufs[0].length,this._bufs.shift()}return this},o.prototype.duplicate=function(){const t=this._new();for(let e=0;e<this._bufs.length;e++)t.append(this._bufs[e]);return t},o.prototype.append=function(t){if(null==t)return this;if(t.buffer)this._appendBuffer(n.from(t.buffer,t.byteOffset,t.byteLength));else if(Array.isArray(t))for(let e=0;e<t.length;e++)this.append(t[e]);else if(this._isBufferList(t))for(let e=0;e<t._bufs.length;e++)this.append(t._bufs[e]);else"number"===typeof t&&(t=t.toString()),this._appendBuffer(n.from(t));return this},o.prototype._appendBuffer=function(t){this._bufs.push(t),this.length+=t.length},o.prototype.indexOf=function(t,e,r){if(void 0===r&&"string"===typeof e&&(r=e,e=void 0),"function"===typeof t||Array.isArray(t))throw new TypeError('The "value" argument must be one of type string, Buffer, BufferList, or Uint8Array.');if("number"===typeof t?t=n.from([t]):"string"===typeof t?t=n.from(t,r):this._isBufferList(t)?t=t.slice():Array.isArray(t.buffer)?t=n.from(t.buffer,t.byteOffset,t.byteLength):n.isBuffer(t)||(t=n.from(t)),e=Number(e||0),isNaN(e)&&(e=0),e<0&&(e=this.length+e),e<0&&(e=0),0===t.length)return e>this.length?this.length:e;const i=this._offset(e);let o=i[0],s=i[1];for(;o<this._bufs.length;o++){const e=this._bufs[o];while(s<e.length){const r=e.length-s;if(r>=t.length){const r=e.indexOf(t,s);if(-1!==r)return this._reverseOffset([o,r]);s=e.length-t.length+1}else{const e=this._reverseOffset([o,s]);if(this._match(e,t))return e;s++}}s=0}return-1},o.prototype._match=function(t,e){if(this.length-t<e.length)return!1;for(let r=0;r<e.length;r++)if(this.get(t+r)!==e[r])return!1;return!0},function(){const t={readDoubleBE:8,readDoubleLE:8,readFloatBE:4,readFloatLE:4,readInt32BE:4,readInt32LE:4,readUInt32BE:4,readUInt32LE:4,readInt16BE:2,readInt16LE:2,readUInt16BE:2,readUInt16LE:2,readInt8:1,readUInt8:1,readIntBE:null,readIntLE:null,readUIntBE:null,readUIntLE:null};for(const e in t)(function(e){null===t[e]?o.prototype[e]=function(t,r){return this.slice(t,t+r)[e](0,r)}:o.prototype[e]=function(r=0){return this.slice(r,r+t[e])[e](0)}})(e)}(),o.prototype._isBufferList=function(t){return t instanceof o||o.isBufferList(t)},o.isBufferList=function(t){return null!=t&&t[i]},t.exports=o},"3409f":function(t,e,r){e.parser=r("1fad").parser,e.generate=r("7f0f"),e.writeToStream=r("7135")},3452:function(t,e,r){(function(e,n,i){t.exports=n(r("21bf"),r("3252"),r("17e1"),r("a8ce"),r("1132"),r("c1bc"),r("72fe"),r("df2f"),r("94f8"),r("191b"),r("d6e6"),r("b86b"),r("e61b"),r("10b7"),r("5980"),r("7bbc"),r("2b79"),r("38ba"),r("00bb"),r("f4ea"),r("aaef"),r("4ba9"),r("81bf"),r("a817"),r("a11b"),r("8cef"),r("2a66"),r("b86c"),r("6d08"),r("c198"),r("a40e"),r("c3b6"),r("1382"),r("3d5a"),r("af5b"))})(0,(function(t){return t}))},"34e3":function(t,e,r){"use strict";(function(e){var n;function i(t,e,r){return e=o(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function o(t){var e=s(t,"string");return"symbol"===typeof e?e:String(e)}function s(t,e){if("object"!==typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var a=r("d9e1"),c=Symbol("lastResolve"),u=Symbol("lastReject"),l=Symbol("error"),h=Symbol("ended"),f=Symbol("lastPromise"),p=Symbol("handlePromise"),d=Symbol("stream");function y(t,e){return{value:t,done:e}}function b(t){var e=t[c];if(null!==e){var r=t[d].read();null!==r&&(t[f]=null,t[c]=null,t[u]=null,e(y(r,!1)))}}function g(t){e.nextTick(b,t)}function m(t,e){return function(r,n){t.then((function(){e[h]?r(y(void 0,!0)):e[p](r,n)}),n)}}var v=Object.getPrototypeOf((function(){})),_=Object.setPrototypeOf((n={get stream(){return this[d]},next:function(){var t=this,r=this[l];if(null!==r)return Promise.reject(r);if(this[h])return Promise.resolve(y(void 0,!0));if(this[d].destroyed)return new Promise((function(r,n){e.nextTick((function(){t[l]?n(t[l]):r(y(void 0,!0))}))}));var n,i=this[f];if(i)n=new Promise(m(i,this));else{var o=this[d].read();if(null!==o)return Promise.resolve(y(o,!1));n=new Promise(this[p])}return this[f]=n,n}},i(n,Symbol.asyncIterator,(function(){return this})),i(n,"return",(function(){var t=this;return new Promise((function(e,r){t[d].destroy(null,(function(t){t?r(t):e(y(void 0,!0))}))}))})),n),v),w=function(t){var e,r=Object.create(_,(e={},i(e,d,{value:t,writable:!0}),i(e,c,{value:null,writable:!0}),i(e,u,{value:null,writable:!0}),i(e,l,{value:null,writable:!0}),i(e,h,{value:t._readableState.endEmitted,writable:!0}),i(e,p,{value:function(t,e){var n=r[d].read();n?(r[f]=null,r[c]=null,r[u]=null,t(y(n,!1))):(r[c]=t,r[u]=e)},writable:!0}),e));return r[f]=null,a(t,(function(t){if(t&&"ERR_STREAM_PREMATURE_CLOSE"!==t.code){var e=r[u];return null!==e&&(r[f]=null,r[c]=null,r[u]=null,e(t)),void(r[l]=t)}var n=r[c];null!==n&&(r[f]=null,r[c]=null,r[u]=null,n(y(void 0,!0))),r[h]=!0})),t.on("readable",g.bind(null,r)),r};t.exports=w}).call(this,r("4362"))},"34eb":function(t,e,r){(function(n){function i(){return!("undefined"===typeof window||!window.process||"renderer"!==window.process.type&&!window.process.__nwjs)||("undefined"===typeof navigator||!navigator.userAgent||!navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!==typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!==typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!==typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!==typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))}function o(e){if(e[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+e[0]+(this.useColors?"%c ":" ")+"+"+t.exports.humanize(this.diff),!this.useColors)return;const r="color: "+this.color;e.splice(1,0,r,"color: inherit");let n=0,i=0;e[0].replace(/%[a-zA-Z%]/g,t=>{"%%"!==t&&(n++,"%c"===t&&(i=n))}),e.splice(i,0,r)}function s(t){try{t?e.storage.setItem("debug",t):e.storage.removeItem("debug")}catch(r){}}function a(){let t;try{t=e.storage.getItem("debug")}catch(r){}return!t&&"undefined"!==typeof n&&"env"in n&&(t=Object({NODE_ENV:"production",VUE_APP_BASE_API:"/prod-api",BASE_URL:"/"}).DEBUG),t}function c(){try{return localStorage}catch(t){}}e.formatArgs=o,e.save=s,e.load=a,e.useColors=i,e.storage=c(),e.destroy=(()=>{let t=!1;return()=>{t||(t=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),e.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],e.log=console.debug||console.log||(()=>{}),t.exports=r("dc90")(e);const{formatters:u}=t.exports;u.j=function(t){try{return JSON.stringify(t)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}}).call(this,r("4362"))},"386b":function(t,e,r){"use strict";var n;function i(t){var e=!1;return function(){e||(e=!0,t.apply(void 0,arguments))}}var o=r("9bfc").codes,s=o.ERR_MISSING_ARGS,a=o.ERR_STREAM_DESTROYED;function c(t){if(t)throw t}function u(t){return t.setHeader&&"function"===typeof t.abort}function l(t,e,o,s){s=i(s);var c=!1;t.on("close",(function(){c=!0})),void 0===n&&(n=r("bf09")),n(t,{readable:e,writable:o},(function(t){if(t)return s(t);c=!0,s()}));var l=!1;return function(e){if(!c&&!l)return l=!0,u(t)?t.abort():"function"===typeof t.destroy?t.destroy():void s(e||new a("pipe"))}}function h(t){t()}function f(t,e){return t.pipe(e)}function p(t){return t.length?"function"!==typeof t[t.length-1]?c:t.pop():c}function d(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];var n,i=p(e);if(Array.isArray(e[0])&&(e=e[0]),e.length<2)throw new s("streams");var o=e.map((function(t,r){var s=r<e.length-1,a=r>0;return l(t,s,a,(function(t){n||(n=t),t&&o.forEach(h),s||(o.forEach(h),i(n))}))}));return e.reduce(f)}t.exports=d},"38ba":function(t,e,r){(function(e,n,i){t.exports=n(r("21bf"),r("2b79"))})(0,(function(t){t.lib.Cipher||function(e){var r=t,n=r.lib,i=n.Base,o=n.WordArray,s=n.BufferedBlockAlgorithm,a=r.enc,c=(a.Utf8,a.Base64),u=r.algo,l=u.EvpKDF,h=n.Cipher=s.extend({cfg:i.extend(),createEncryptor:function(t,e){return this.create(this._ENC_XFORM_MODE,t,e)},createDecryptor:function(t,e){return this.create(this._DEC_XFORM_MODE,t,e)},init:function(t,e,r){this.cfg=this.cfg.extend(r),this._xformMode=t,this._key=e,this.reset()},reset:function(){s.reset.call(this),this._doReset()},process:function(t){return this._append(t),this._process()},finalize:function(t){t&&this._append(t);var e=this._doFinalize();return e},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function t(t){return"string"==typeof t?E:_}return function(e){return{encrypt:function(r,n,i){return t(n).encrypt(e,r,n,i)},decrypt:function(r,n,i){return t(n).decrypt(e,r,n,i)}}}}()}),f=(n.StreamCipher=h.extend({_doFinalize:function(){var t=this._process(!0);return t},blockSize:1}),r.mode={}),p=n.BlockCipherMode=i.extend({createEncryptor:function(t,e){return this.Encryptor.create(t,e)},createDecryptor:function(t,e){return this.Decryptor.create(t,e)},init:function(t,e){this._cipher=t,this._iv=e}}),d=f.CBC=function(){var t=p.extend();function r(t,r,n){var i,o=this._iv;o?(i=o,this._iv=e):i=this._prevBlock;for(var s=0;s<n;s++)t[r+s]^=i[s]}return t.Encryptor=t.extend({processBlock:function(t,e){var n=this._cipher,i=n.blockSize;r.call(this,t,e,i),n.encryptBlock(t,e),this._prevBlock=t.slice(e,e+i)}}),t.Decryptor=t.extend({processBlock:function(t,e){var n=this._cipher,i=n.blockSize,o=t.slice(e,e+i);n.decryptBlock(t,e),r.call(this,t,e,i),this._prevBlock=o}}),t}(),y=r.pad={},b=y.Pkcs7={pad:function(t,e){for(var r=4*e,n=r-t.sigBytes%r,i=n<<24|n<<16|n<<8|n,s=[],a=0;a<n;a+=4)s.push(i);var c=o.create(s,n);t.concat(c)},unpad:function(t){var e=255&t.words[t.sigBytes-1>>>2];t.sigBytes-=e}},g=(n.BlockCipher=h.extend({cfg:h.cfg.extend({mode:d,padding:b}),reset:function(){var t;h.reset.call(this);var e=this.cfg,r=e.iv,n=e.mode;this._xformMode==this._ENC_XFORM_MODE?t=n.createEncryptor:(t=n.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==t?this._mode.init(this,r&&r.words):(this._mode=t.call(n,this,r&&r.words),this._mode.__creator=t)},_doProcessBlock:function(t,e){this._mode.processBlock(t,e)},_doFinalize:function(){var t,e=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(e.pad(this._data,this.blockSize),t=this._process(!0)):(t=this._process(!0),e.unpad(t)),t},blockSize:4}),n.CipherParams=i.extend({init:function(t){this.mixIn(t)},toString:function(t){return(t||this.formatter).stringify(this)}})),m=r.format={},v=m.OpenSSL={stringify:function(t){var e,r=t.ciphertext,n=t.salt;return e=n?o.create([1398893684,1701076831]).concat(n).concat(r):r,e.toString(c)},parse:function(t){var e,r=c.parse(t),n=r.words;return 1398893684==n[0]&&1701076831==n[1]&&(e=o.create(n.slice(2,4)),n.splice(0,4),r.sigBytes-=16),g.create({ciphertext:r,salt:e})}},_=n.SerializableCipher=i.extend({cfg:i.extend({format:v}),encrypt:function(t,e,r,n){n=this.cfg.extend(n);var i=t.createEncryptor(r,n),o=i.finalize(e),s=i.cfg;return g.create({ciphertext:o,key:r,iv:s.iv,algorithm:t,mode:s.mode,padding:s.padding,blockSize:t.blockSize,formatter:n.format})},decrypt:function(t,e,r,n){n=this.cfg.extend(n),e=this._parse(e,n.format);var i=t.createDecryptor(r,n).finalize(e.ciphertext);return i},_parse:function(t,e){return"string"==typeof t?e.parse(t,this):t}}),w=r.kdf={},S=w.OpenSSL={execute:function(t,e,r,n,i){if(n||(n=o.random(8)),i)s=l.create({keySize:e+r,hasher:i}).compute(t,n);else var s=l.create({keySize:e+r}).compute(t,n);var a=o.create(s.words.slice(e),4*r);return s.sigBytes=4*e,g.create({key:s,iv:a,salt:n})}},E=n.PasswordBasedCipher=_.extend({cfg:_.cfg.extend({kdf:S}),encrypt:function(t,e,r,n){n=this.cfg.extend(n);var i=n.kdf.execute(r,t.keySize,t.ivSize,n.salt,n.hasher);n.iv=i.iv;var o=_.encrypt.call(this,t,e,i.key,n);return o.mixIn(i),o},decrypt:function(t,e,r,n){n=this.cfg.extend(n),e=this._parse(e,n.format);var i=n.kdf.execute(r,t.keySize,t.ivSize,e.salt,n.hasher);n.iv=i.iv;var o=_.decrypt.call(this,t,e,i.key,n);return o}})}()}))},"3ca2":function(t,e,r){"use strict";t.exports=l;var n=r("fbd7").codes,i=n.ERR_METHOD_NOT_IMPLEMENTED,o=n.ERR_MULTIPLE_CALLBACK,s=n.ERR_TRANSFORM_ALREADY_TRANSFORMING,a=n.ERR_TRANSFORM_WITH_LENGTH_0,c=r("be3f");function u(t,e){var r=this._transformState;r.transforming=!1;var n=r.writecb;if(null===n)return this.emit("error",new o);r.writechunk=null,r.writecb=null,null!=e&&this.push(e),n(t);var i=this._readableState;i.reading=!1,(i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}function l(t){if(!(this instanceof l))return new l(t);c.call(this,t),this._transformState={afterTransform:u.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,t&&("function"===typeof t.transform&&(this._transform=t.transform),"function"===typeof t.flush&&(this._flush=t.flush)),this.on("prefinish",h)}function h(){var t=this;"function"!==typeof this._flush||this._readableState.destroyed?f(this,null,null):this._flush((function(e,r){f(t,e,r)}))}function f(t,e,r){if(e)return t.emit("error",e);if(null!=r&&t.push(r),t._writableState.length)throw new a;if(t._transformState.transforming)throw new s;return t.push(null)}r("3fb5")(l,c),l.prototype.push=function(t,e){return this._transformState.needTransform=!1,c.prototype.push.call(this,t,e)},l.prototype._transform=function(t,e,r){r(new i("_transform()"))},l.prototype._write=function(t,e,r){var n=this._transformState;if(n.writecb=r,n.writechunk=t,n.writeencoding=e,!n.transforming){var i=this._readableState;(n.needTransform||i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}},l.prototype._read=function(t){var e=this._transformState;null===e.writechunk||e.transforming?e.needTransform=!0:(e.transforming=!0,this._transform(e.writechunk,e.writeencoding,e.afterTransform))},l.prototype._destroy=function(t,e){c.prototype._destroy.call(this,t,(function(t){e(t)}))}},"3d5a":function(t,e,r){(function(e,n,i){t.exports=n(r("21bf"),r("1132"),r("72fe"),r("2b79"),r("38ba"))})(0,(function(t){return function(){var e=t,r=e.lib,n=r.StreamCipher,i=e.algo,o=[],s=[],a=[],c=i.RabbitLegacy=n.extend({_doReset:function(){var t=this._key.words,e=this.cfg.iv,r=this._X=[t[0],t[3]<<16|t[2]>>>16,t[1],t[0]<<16|t[3]>>>16,t[2],t[1]<<16|t[0]>>>16,t[3],t[2]<<16|t[1]>>>16],n=this._C=[t[2]<<16|t[2]>>>16,4294901760&t[0]|65535&t[1],t[3]<<16|t[3]>>>16,4294901760&t[1]|65535&t[2],t[0]<<16|t[0]>>>16,4294901760&t[2]|65535&t[3],t[1]<<16|t[1]>>>16,4294901760&t[3]|65535&t[0]];this._b=0;for(var i=0;i<4;i++)u.call(this);for(i=0;i<8;i++)n[i]^=r[i+4&7];if(e){var o=e.words,s=o[0],a=o[1],c=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),l=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),h=c>>>16|4294901760&l,f=l<<16|65535&c;n[0]^=c,n[1]^=h,n[2]^=l,n[3]^=f,n[4]^=c,n[5]^=h,n[6]^=l,n[7]^=f;for(i=0;i<4;i++)u.call(this)}},_doProcessBlock:function(t,e){var r=this._X;u.call(this),o[0]=r[0]^r[5]>>>16^r[3]<<16,o[1]=r[2]^r[7]>>>16^r[5]<<16,o[2]=r[4]^r[1]>>>16^r[7]<<16,o[3]=r[6]^r[3]>>>16^r[1]<<16;for(var n=0;n<4;n++)o[n]=16711935&(o[n]<<8|o[n]>>>24)|4278255360&(o[n]<<24|o[n]>>>8),t[e+n]^=o[n]},blockSize:4,ivSize:2});function u(){for(var t=this._X,e=this._C,r=0;r<8;r++)s[r]=e[r];e[0]=e[0]+1295307597+this._b|0,e[1]=e[1]+3545052371+(e[0]>>>0<s[0]>>>0?1:0)|0,e[2]=e[2]+886263092+(e[1]>>>0<s[1]>>>0?1:0)|0,e[3]=e[3]+1295307597+(e[2]>>>0<s[2]>>>0?1:0)|0,e[4]=e[4]+3545052371+(e[3]>>>0<s[3]>>>0?1:0)|0,e[5]=e[5]+886263092+(e[4]>>>0<s[4]>>>0?1:0)|0,e[6]=e[6]+1295307597+(e[5]>>>0<s[5]>>>0?1:0)|0,e[7]=e[7]+3545052371+(e[6]>>>0<s[6]>>>0?1:0)|0,this._b=e[7]>>>0<s[7]>>>0?1:0;for(r=0;r<8;r++){var n=t[r]+e[r],i=65535&n,o=n>>>16,c=((i*i>>>17)+i*o>>>15)+o*o,u=((4294901760&n)*n|0)+((65535&n)*n|0);a[r]=c^u}t[0]=a[0]+(a[7]<<16|a[7]>>>16)+(a[6]<<16|a[6]>>>16)|0,t[1]=a[1]+(a[0]<<8|a[0]>>>24)+a[7]|0,t[2]=a[2]+(a[1]<<16|a[1]>>>16)+(a[0]<<16|a[0]>>>16)|0,t[3]=a[3]+(a[2]<<8|a[2]>>>24)+a[1]|0,t[4]=a[4]+(a[3]<<16|a[3]>>>16)+(a[2]<<16|a[2]>>>16)|0,t[5]=a[5]+(a[4]<<8|a[4]>>>24)+a[3]|0,t[6]=a[6]+(a[5]<<16|a[5]>>>16)+(a[4]<<16|a[4]>>>16)|0,t[7]=a[7]+(a[6]<<8|a[6]>>>24)+a[5]|0}e.RabbitLegacy=n._createHelper(c)}(),t.RabbitLegacy}))},"3d67":function(t,e,r){"use strict";r.r(e),r.d(e,"Stack",(function(){return u})),r.d(e,"Queue",(function(){return f})),r.d(e,"PriorityQueue",(function(){return g})),r.d(e,"Vector",(function(){return C})),r.d(e,"LinkList",(function(){return N})),r.d(e,"Deque",(function(){return W})),r.d(e,"OrderedSet",(function(){return st})),r.d(e,"OrderedMap",(function(){return ft})),r.d(e,"HashSet",(function(){return wt})),r.d(e,"HashMap",(function(){return xt}));var n=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),i=function(){function t(t){void 0===t&&(t=0),this.iteratorType=t}return t.prototype.equals=function(t){return this.o===t.o},t}(),o=function(){function t(){this.M=0}return Object.defineProperty(t.prototype,"length",{get:function(){return this.M},enumerable:!1,configurable:!0}),t.prototype.size=function(){return this.M},t.prototype.empty=function(){return 0===this.M},t}(),s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e}(o),a=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),c=function(t){function e(e){void 0===e&&(e=[]);var r=t.call(this)||this;r.nt=[];var n=r;return e.forEach((function(t){n.push(t)})),r}return a(e,t),e.prototype.clear=function(){this.M=0,this.nt=[]},e.prototype.push=function(t){return this.nt.push(t),this.M+=1,this.M},e.prototype.pop=function(){if(0!==this.M)return this.M-=1,this.nt.pop()},e.prototype.top=function(){return this.nt[this.M-1]},e}(o),u=c,l=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),h=function(t){function e(e){void 0===e&&(e=[]);var r=t.call(this)||this;r.A=0,r.tt=[];var n=r;return e.forEach((function(t){n.push(t)})),r}return l(e,t),e.prototype.clear=function(){this.tt=[],this.M=this.A=0},e.prototype.push=function(t){var e=this.tt.length;if(this.A/e>.5&&this.A+this.M>=e&&e>4096){for(var r=this.M,n=0;n<r;++n)this.tt[n]=this.tt[this.A+n];this.A=0,this.tt[this.M]=t}else this.tt[this.A+this.M]=t;return++this.M},e.prototype.pop=function(){if(0!==this.M){var t=this.tt[this.A++];return this.M-=1,t}},e.prototype.front=function(){if(0!==this.M)return this.tt[this.A]},e}(o),f=h,p=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),d=function(t,e){var r="function"===typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,o=r.call(t),s=[];try{while((void 0===e||e-- >0)&&!(n=o.next()).done)s.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=o["return"])&&r.call(o)}finally{if(i)throw i.error}}return s},y=function(t,e,r){if(r||2===arguments.length)for(var n,i=0,o=e.length;i<o;i++)!n&&i in e||(n||(n=Array.prototype.slice.call(e,0,i)),n[i]=e[i]);return t.concat(n||Array.prototype.slice.call(e))},b=function(t){function e(e,r,n){void 0===e&&(e=[]),void 0===r&&(r=function(t,e){return t>e?-1:t<e?1:0}),void 0===n&&(n=!0);var i=t.call(this)||this;if(i.$=r,Array.isArray(e))i.ii=n?y([],d(e),!1):e;else{i.ii=[];var o=i;e.forEach((function(t){o.ii.push(t)}))}i.M=i.ii.length;for(var s=i.M>>1,a=i.M-1>>1;a>=0;--a)i.ri(a,s);return i}return p(e,t),e.prototype.ti=function(t){var e=this.ii[t];while(t>0){var r=t-1>>1,n=this.ii[r];if(this.$(n,e)<=0)break;this.ii[t]=n,t=r}this.ii[t]=e},e.prototype.ri=function(t,e){var r=this.ii[t];while(t<e){var n=t<<1|1,i=n+1,o=this.ii[n];if(i<this.M&&this.$(o,this.ii[i])>0&&(n=i,o=this.ii[i]),this.$(o,r)>=0)break;this.ii[t]=o,t=n}this.ii[t]=r},e.prototype.clear=function(){this.M=0,this.ii.length=0},e.prototype.push=function(t){this.ii.push(t),this.ti(this.M),this.M+=1},e.prototype.pop=function(){if(0!==this.M){var t=this.ii[0],e=this.ii.pop();return this.M-=1,this.M&&(this.ii[0]=e,this.ri(0,this.M>>1)),t}},e.prototype.top=function(){return this.ii[0]},e.prototype.find=function(t){return this.ii.indexOf(t)>=0},e.prototype.remove=function(t){var e=this.ii.indexOf(t);return!(e<0)&&(0===e?this.pop():e===this.M-1?(this.ii.pop(),this.M-=1):(this.ii.splice(e,1,this.ii.pop()),this.M-=1,this.ti(e),this.ri(e,this.M>>1)),!0)},e.prototype.updateItem=function(t){var e=this.ii.indexOf(t);return!(e<0)&&(this.ti(e),this.ri(e,this.M>>1),!0)},e.prototype.toArray=function(){return y([],d(this.ii),!1)},e}(o),g=b,m=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),v=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return m(e,t),e}(s),_=v;function w(){throw new RangeError("Iterator access denied!")}var S=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),E=function(t){function e(e,r){var n=t.call(this,r)||this;return n.o=e,0===n.iteratorType?(n.pre=function(){return 0===this.o&&w(),this.o-=1,this},n.next=function(){return this.o===this.container.size()&&w(),this.o+=1,this}):(n.pre=function(){return this.o===this.container.size()-1&&w(),this.o+=1,this},n.next=function(){return-1===this.o&&w(),this.o-=1,this}),n}return S(e,t),Object.defineProperty(e.prototype,"pointer",{get:function(){return this.container.getElementByPos(this.o)},set:function(t){this.container.setElementByPos(this.o,t)},enumerable:!1,configurable:!0}),e}(i),k=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),A=function(t,e){var r,n,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(t){return function(e){return c([t,e])}}function c(o){if(r)throw new TypeError("Generator is already executing.");while(s)try{if(r=1,n&&(i=2&o[0]?n["return"]:o[0]?n["throw"]||((i=n["return"])&&i.call(n),0):n.next)&&!(i=i.call(n,o[1])).done)return i;switch(n=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(i=s.trys,!(i=i.length>0&&i[i.length-1])&&(6===o[0]||2===o[0])){s=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){s.label=o[1];break}if(6===o[0]&&s.label<i[1]){s.label=i[1],i=o;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(o);break}i[2]&&s.ops.pop(),s.trys.pop();continue}o=e.call(t,s)}catch(t){o=[6,t],n=0}finally{r=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}},x=function(t,e){var r="function"===typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,o=r.call(t),s=[];try{while((void 0===e||e-- >0)&&!(n=o.next()).done)s.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=o["return"])&&r.call(o)}finally{if(i)throw i.error}}return s},P=function(t,e,r){if(r||2===arguments.length)for(var n,i=0,o=e.length;i<o;i++)!n&&i in e||(n||(n=Array.prototype.slice.call(e,0,i)),n[i]=e[i]);return t.concat(n||Array.prototype.slice.call(e))},O=function(t){var e="function"===typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"===typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},R=function(t){function e(e,r,n){var i=t.call(this,e,n)||this;return i.container=r,i}return k(e,t),e.prototype.copy=function(){return new e(this.o,this.container,this.iteratorType)},e}(E),M=function(t){function e(e,r){void 0===e&&(e=[]),void 0===r&&(r=!0);var n=t.call(this)||this;if(Array.isArray(e))n.J=r?P([],x(e),!1):e,n.M=e.length;else{n.J=[];var i=n;e.forEach((function(t){i.pushBack(t)}))}return n}return k(e,t),e.prototype.clear=function(){this.M=0,this.J.length=0},e.prototype.begin=function(){return new R(0,this)},e.prototype.end=function(){return new R(this.M,this)},e.prototype.rBegin=function(){return new R(this.M-1,this,1)},e.prototype.rEnd=function(){return new R(-1,this,1)},e.prototype.front=function(){return this.J[0]},e.prototype.back=function(){return this.J[this.M-1]},e.prototype.getElementByPos=function(t){if(t<0||t>this.M-1)throw new RangeError;return this.J[t]},e.prototype.eraseElementByPos=function(t){if(t<0||t>this.M-1)throw new RangeError;return this.J.splice(t,1),this.M-=1,this.M},e.prototype.eraseElementByValue=function(t){for(var e=0,r=0;r<this.M;++r)this.J[r]!==t&&(this.J[e++]=this.J[r]);return this.M=this.J.length=e,this.M},e.prototype.eraseElementByIterator=function(t){var e=t.o;return t=t.next(),this.eraseElementByPos(e),t},e.prototype.pushBack=function(t){return this.J.push(t),this.M+=1,this.M},e.prototype.popBack=function(){if(0!==this.M)return this.M-=1,this.J.pop()},e.prototype.setElementByPos=function(t,e){if(t<0||t>this.M-1)throw new RangeError;this.J[t]=e},e.prototype.insert=function(t,e,r){var n;if(void 0===r&&(r=1),t<0||t>this.M)throw new RangeError;return(n=this.J).splice.apply(n,P([t,0],x(new Array(r).fill(e)),!1)),this.M+=r,this.M},e.prototype.find=function(t){for(var e=0;e<this.M;++e)if(this.J[e]===t)return new R(e,this);return this.end()},e.prototype.reverse=function(){this.J.reverse()},e.prototype.unique=function(){for(var t=1,e=1;e<this.M;++e)this.J[e]!==this.J[e-1]&&(this.J[t++]=this.J[e]);return this.M=this.J.length=t,this.M},e.prototype.sort=function(t){this.J.sort(t)},e.prototype.forEach=function(t){for(var e=0;e<this.M;++e)t(this.J[e],e,this)},e.prototype[Symbol.iterator]=function(){return function(){return A(this,(function(t){switch(t.label){case 0:return[5,O(this.J)];case 1:return t.sent(),[2]}}))}.bind(this)()},e}(_),C=M,T=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),B=function(t,e){var r,n,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(t){return function(e){return c([t,e])}}function c(o){if(r)throw new TypeError("Generator is already executing.");while(s)try{if(r=1,n&&(i=2&o[0]?n["return"]:o[0]?n["throw"]||((i=n["return"])&&i.call(n),0):n.next)&&!(i=i.call(n,o[1])).done)return i;switch(n=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(i=s.trys,!(i=i.length>0&&i[i.length-1])&&(6===o[0]||2===o[0])){s=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){s.label=o[1];break}if(6===o[0]&&s.label<i[1]){s.label=i[1],i=o;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(o);break}i[2]&&s.ops.pop(),s.trys.pop();continue}o=e.call(t,s)}catch(t){o=[6,t],n=0}finally{r=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}},I=function(t){function e(e,r,n,i){var o=t.call(this,i)||this;return o.o=e,o.h=r,o.container=n,0===o.iteratorType?(o.pre=function(){return this.o.L===this.h&&w(),this.o=this.o.L,this},o.next=function(){return this.o===this.h&&w(),this.o=this.o.m,this}):(o.pre=function(){return this.o.m===this.h&&w(),this.o=this.o.m,this},o.next=function(){return this.o===this.h&&w(),this.o=this.o.L,this}),o}return T(e,t),Object.defineProperty(e.prototype,"pointer",{get:function(){return this.o===this.h&&w(),this.o.p},set:function(t){this.o===this.h&&w(),this.o.p=t},enumerable:!1,configurable:!0}),e.prototype.copy=function(){return new e(this.o,this.h,this.container,this.iteratorType)},e}(i),j=function(t){function e(e){void 0===e&&(e=[]);var r=t.call(this)||this;r.h={},r.H=r.l=r.h.L=r.h.m=r.h;var n=r;return e.forEach((function(t){n.pushBack(t)})),r}return T(e,t),e.prototype.G=function(t){var e=t.L,r=t.m;e.m=r,r.L=e,t===this.H&&(this.H=r),t===this.l&&(this.l=e),this.M-=1},e.prototype.F=function(t,e){var r=e.m,n={p:t,L:e,m:r};e.m=n,r.L=n,e===this.h&&(this.H=n),r===this.h&&(this.l=n),this.M+=1},e.prototype.clear=function(){this.M=0,this.H=this.l=this.h.L=this.h.m=this.h},e.prototype.begin=function(){return new I(this.H,this.h,this)},e.prototype.end=function(){return new I(this.h,this.h,this)},e.prototype.rBegin=function(){return new I(this.l,this.h,this,1)},e.prototype.rEnd=function(){return new I(this.h,this.h,this,1)},e.prototype.front=function(){return this.H.p},e.prototype.back=function(){return this.l.p},e.prototype.getElementByPos=function(t){if(t<0||t>this.M-1)throw new RangeError;var e=this.H;while(t--)e=e.m;return e.p},e.prototype.eraseElementByPos=function(t){if(t<0||t>this.M-1)throw new RangeError;var e=this.H;while(t--)e=e.m;return this.G(e),this.M},e.prototype.eraseElementByValue=function(t){var e=this.H;while(e!==this.h)e.p===t&&this.G(e),e=e.m;return this.M},e.prototype.eraseElementByIterator=function(t){var e=t.o;return e===this.h&&w(),t=t.next(),this.G(e),t},e.prototype.pushBack=function(t){return this.F(t,this.l),this.M},e.prototype.popBack=function(){if(0!==this.M){var t=this.l.p;return this.G(this.l),t}},e.prototype.pushFront=function(t){return this.F(t,this.h),this.M},e.prototype.popFront=function(){if(0!==this.M){var t=this.H.p;return this.G(this.H),t}},e.prototype.setElementByPos=function(t,e){if(t<0||t>this.M-1)throw new RangeError;var r=this.H;while(t--)r=r.m;r.p=e},e.prototype.insert=function(t,e,r){if(void 0===r&&(r=1),t<0||t>this.M)throw new RangeError;if(r<=0)return this.M;if(0===t)while(r--)this.pushFront(e);else if(t===this.M)while(r--)this.pushBack(e);else{for(var n=this.H,i=1;i<t;++i)n=n.m;var o=n.m;this.M+=r;while(r--)n.m={p:e,L:n},n.m.L=n,n=n.m;n.m=o,o.L=n}return this.M},e.prototype.find=function(t){var e=this.H;while(e!==this.h){if(e.p===t)return new I(e,this.h,this);e=e.m}return this.end()},e.prototype.reverse=function(){if(!(this.M<=1)){var t=this.H,e=this.l,r=0;while(r<<1<this.M){var n=t.p;t.p=e.p,e.p=n,t=t.m,e=e.L,r+=1}}},e.prototype.unique=function(){if(this.M<=1)return this.M;var t=this.H;while(t!==this.h){var e=t;while(e.m!==this.h&&e.p===e.m.p)e=e.m,this.M-=1;t.m=e.m,t.m.L=t,t=t.m}return this.M},e.prototype.sort=function(t){if(!(this.M<=1)){var e=[];this.forEach((function(t){e.push(t)})),e.sort(t);var r=this.H;e.forEach((function(t){r.p=t,r=r.m}))}},e.prototype.merge=function(t){var e=this;if(0===this.M)t.forEach((function(t){e.pushBack(t)}));else{var r=this.H;t.forEach((function(t){while(r!==e.h&&r.p<=t)r=r.m;e.F(t,r.L)}))}return this.M},e.prototype.forEach=function(t){var e=this.H,r=0;while(e!==this.h)t(e.p,r++,this),e=e.m},e.prototype[Symbol.iterator]=function(){return function(){var t;return B(this,(function(e){switch(e.label){case 0:if(0===this.M)return[2];t=this.H,e.label=1;case 1:return t===this.h?[3,3]:[4,t.p];case 2:return e.sent(),t=t.m,[3,1];case 3:return[2]}}))}.bind(this)()},e}(_),N=j,L=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),D=function(t,e){var r,n,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(t){return function(e){return c([t,e])}}function c(o){if(r)throw new TypeError("Generator is already executing.");while(s)try{if(r=1,n&&(i=2&o[0]?n["return"]:o[0]?n["throw"]||((i=n["return"])&&i.call(n),0):n.next)&&!(i=i.call(n,o[1])).done)return i;switch(n=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(i=s.trys,!(i=i.length>0&&i[i.length-1])&&(6===o[0]||2===o[0])){s=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){s.label=o[1];break}if(6===o[0]&&s.label<i[1]){s.label=i[1],i=o;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(o);break}i[2]&&s.ops.pop(),s.trys.pop();continue}o=e.call(t,s)}catch(t){o=[6,t],n=0}finally{r=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}},F=function(t,e){var r="function"===typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,o=r.call(t),s=[];try{while((void 0===e||e-- >0)&&!(n=o.next()).done)s.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=o["return"])&&r.call(o)}finally{if(i)throw i.error}}return s},U=function(t,e,r){if(r||2===arguments.length)for(var n,i=0,o=e.length;i<o;i++)!n&&i in e||(n||(n=Array.prototype.slice.call(e,0,i)),n[i]=e[i]);return t.concat(n||Array.prototype.slice.call(e))},H=function(t){function e(e,r,n){var i=t.call(this,e,n)||this;return i.container=r,i}return L(e,t),e.prototype.copy=function(){return new e(this.o,this.container,this.iteratorType)},e}(E),q=function(t){function e(e,r){void 0===e&&(e=[]),void 0===r&&(r=4096);var n=t.call(this)||this;n.A=0,n.S=0,n.R=0,n.k=0,n.C=0,n.j=[];var i=function(){if("number"===typeof e.length)return e.length;if("number"===typeof e.size)return e.size;if("function"===typeof e.size)return e.size();throw new TypeError("Cannot get the length or size of the container")}();n.B=r,n.C=Math.max(Math.ceil(i/n.B),1);for(var o=0;o<n.C;++o)n.j.push(new Array(n.B));var s=Math.ceil(i/n.B);n.A=n.R=(n.C>>1)-(s>>1),n.S=n.k=n.B-i%n.B>>1;var a=n;return e.forEach((function(t){a.pushBack(t)})),n}return L(e,t),e.prototype.O=function(){for(var t=[],e=Math.max(this.C>>1,1),r=0;r<e;++r)t[r]=new Array(this.B);for(r=this.A;r<this.C;++r)t[t.length]=this.j[r];for(r=0;r<this.R;++r)t[t.length]=this.j[r];t[t.length]=U([],F(this.j[this.R]),!1),this.A=e,this.R=t.length-1;for(r=0;r<e;++r)t[t.length]=new Array(this.B);this.j=t,this.C=t.length},e.prototype.T=function(t){var e=this.S+t+1,r=e%this.B,n=r-1,i=this.A+(e-r)/this.B;return 0===r&&(i-=1),i%=this.C,n<0&&(n+=this.B),{curNodeBucketIndex:i,curNodePointerIndex:n}},e.prototype.clear=function(){this.j=[new Array(this.B)],this.C=1,this.A=this.R=this.M=0,this.S=this.k=this.B>>1},e.prototype.begin=function(){return new H(0,this)},e.prototype.end=function(){return new H(this.M,this)},e.prototype.rBegin=function(){return new H(this.M-1,this,1)},e.prototype.rEnd=function(){return new H(-1,this,1)},e.prototype.front=function(){if(0!==this.M)return this.j[this.A][this.S]},e.prototype.back=function(){if(0!==this.M)return this.j[this.R][this.k]},e.prototype.pushBack=function(t){return this.M&&(this.k<this.B-1?this.k+=1:this.R<this.C-1?(this.R+=1,this.k=0):(this.R=0,this.k=0),this.R===this.A&&this.k===this.S&&this.O()),this.M+=1,this.j[this.R][this.k]=t,this.M},e.prototype.popBack=function(){if(0!==this.M){var t=this.j[this.R][this.k];return 1!==this.M&&(this.k>0?this.k-=1:this.R>0?(this.R-=1,this.k=this.B-1):(this.R=this.C-1,this.k=this.B-1)),this.M-=1,t}},e.prototype.pushFront=function(t){return this.M&&(this.S>0?this.S-=1:this.A>0?(this.A-=1,this.S=this.B-1):(this.A=this.C-1,this.S=this.B-1),this.A===this.R&&this.S===this.k&&this.O()),this.M+=1,this.j[this.A][this.S]=t,this.M},e.prototype.popFront=function(){if(0!==this.M){var t=this.j[this.A][this.S];return 1!==this.M&&(this.S<this.B-1?this.S+=1:this.A<this.C-1?(this.A+=1,this.S=0):(this.A=0,this.S=0)),this.M-=1,t}},e.prototype.getElementByPos=function(t){if(t<0||t>this.M-1)throw new RangeError;var e=this.T(t),r=e.curNodeBucketIndex,n=e.curNodePointerIndex;return this.j[r][n]},e.prototype.setElementByPos=function(t,e){if(t<0||t>this.M-1)throw new RangeError;var r=this.T(t),n=r.curNodeBucketIndex,i=r.curNodePointerIndex;this.j[n][i]=e},e.prototype.insert=function(t,e,r){if(void 0===r&&(r=1),t<0||t>this.M)throw new RangeError;if(0===t)while(r--)this.pushFront(e);else if(t===this.M)while(r--)this.pushBack(e);else{for(var n=[],i=t;i<this.M;++i)n.push(this.getElementByPos(i));this.cut(t-1);for(i=0;i<r;++i)this.pushBack(e);for(i=0;i<n.length;++i)this.pushBack(n[i])}return this.M},e.prototype.cut=function(t){if(t<0)return this.clear(),0;var e=this.T(t),r=e.curNodeBucketIndex,n=e.curNodePointerIndex;return this.R=r,this.k=n,this.M=t+1,this.M},e.prototype.eraseElementByPos=function(t){if(t<0||t>this.M-1)throw new RangeError;if(0===t)this.popFront();else if(t===this.M-1)this.popBack();else{for(var e=[],r=t+1;r<this.M;++r)e.push(this.getElementByPos(r));this.cut(t),this.popBack();var n=this;e.forEach((function(t){n.pushBack(t)}))}return this.M},e.prototype.eraseElementByValue=function(t){if(0===this.M)return 0;for(var e=[],r=0;r<this.M;++r){var n=this.getElementByPos(r);n!==t&&e.push(n)}var i=e.length;for(r=0;r<i;++r)this.setElementByPos(r,e[r]);return this.cut(i-1)},e.prototype.eraseElementByIterator=function(t){var e=t.o;return this.eraseElementByPos(e),t=t.next(),t},e.prototype.find=function(t){for(var e=0;e<this.M;++e)if(this.getElementByPos(e)===t)return new H(e,this);return this.end()},e.prototype.reverse=function(){var t=0,e=this.M-1;while(t<e){var r=this.getElementByPos(t);this.setElementByPos(t,this.getElementByPos(e)),this.setElementByPos(e,r),t+=1,e-=1}},e.prototype.unique=function(){if(this.M<=1)return this.M;for(var t=1,e=this.getElementByPos(0),r=1;r<this.M;++r){var n=this.getElementByPos(r);n!==e&&(e=n,this.setElementByPos(t++,n))}while(this.M>t)this.popBack();return this.M},e.prototype.sort=function(t){for(var e=[],r=0;r<this.M;++r)e.push(this.getElementByPos(r));e.sort(t);for(r=0;r<this.M;++r)this.setElementByPos(r,e[r])},e.prototype.shrinkToFit=function(){if(0!==this.M){var t=[];this.forEach((function(e){t.push(e)})),this.C=Math.max(Math.ceil(this.M/this.B),1),this.M=this.A=this.R=this.S=this.k=0,this.j=[];for(var e=0;e<this.C;++e)this.j.push(new Array(this.B));for(e=0;e<t.length;++e)this.pushBack(t[e])}},e.prototype.forEach=function(t){for(var e=0;e<this.M;++e)t(this.getElementByPos(e),e,this)},e.prototype[Symbol.iterator]=function(){return function(){var t;return D(this,(function(e){switch(e.label){case 0:t=0,e.label=1;case 1:return t<this.M?[4,this.getElementByPos(t)]:[3,4];case 2:e.sent(),e.label=3;case 3:return++t,[3,1];case 4:return[2]}}))}.bind(this)()},e}(_),W=q,K=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),z=function(){function t(t,e){this.ee=1,this.u=void 0,this.p=void 0,this.K=void 0,this.N=void 0,this.rr=void 0,this.u=t,this.p=e}return t.prototype.L=function(){var t=this;if(1===t.ee&&t.rr.rr===t)t=t.N;else if(t.K){t=t.K;while(t.N)t=t.N}else{var e=t.rr;while(e.K===t)t=e,e=t.rr;t=e}return t},t.prototype.m=function(){var t=this;if(t.N){t=t.N;while(t.K)t=t.K;return t}var e=t.rr;while(e.N===t)t=e,e=t.rr;return t.N!==e?e:t},t.prototype.ne=function(){var t=this.rr,e=this.N,r=e.K;return t.rr===this?t.rr=e:t.K===this?t.K=e:t.N=e,e.rr=t,e.K=this,this.rr=e,this.N=r,r&&(r.rr=this),e},t.prototype.te=function(){var t=this.rr,e=this.K,r=e.N;return t.rr===this?t.rr=e:t.K===this?t.K=e:t.N=e,e.rr=t,e.N=this,this.rr=e,this.K=r,r&&(r.rr=this),e},t}(),V=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.tr=1,e}return K(e,t),e.prototype.ne=function(){var e=t.prototype.ne.call(this);return this.ie(),e.ie(),e},e.prototype.te=function(){var e=t.prototype.te.call(this);return this.ie(),e.ie(),e},e.prototype.ie=function(){this.tr=1,this.K&&(this.tr+=this.K.tr),this.N&&(this.tr+=this.N.tr)},e}(z),G=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Q=function(t,e){var r="function"===typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,o=r.call(t),s=[];try{while((void 0===e||e-- >0)&&!(n=o.next()).done)s.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=o["return"])&&r.call(o)}finally{if(i)throw i.error}}return s},$=function(t){var e="function"===typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"===typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},J=function(t){function e(e,r){void 0===e&&(e=function(t,e){return t<e?-1:t>e?1:0}),void 0===r&&(r=!1);var n=t.call(this)||this;return n.W=void 0,n.$=e,r?(n.re=V,n.v=function(t,e,r){var n=this.se(t,e,r);if(n){var i=n.rr;while(i!==this.h)i.tr+=1,i=i.rr;var o=this.fe(n);if(o){var s=o,a=s.parentNode,c=s.grandParent,u=s.curNode;a.ie(),c.ie(),u.ie()}}return this.M},n.G=function(t){var e=this.he(t);while(e!==this.h)e.tr-=1,e=e.rr}):(n.re=z,n.v=function(t,e,r){var n=this.se(t,e,r);return n&&this.fe(n),this.M},n.G=n.he),n.h=new n.re,n}return G(e,t),e.prototype.U=function(t,e){var r=this.h;while(t){var n=this.$(t.u,e);if(n<0)t=t.N;else{if(!(n>0))return t;r=t,t=t.K}}return r},e.prototype.X=function(t,e){var r=this.h;while(t){var n=this.$(t.u,e);n<=0?t=t.N:(r=t,t=t.K)}return r},e.prototype.Y=function(t,e){var r=this.h;while(t){var n=this.$(t.u,e);if(n<0)r=t,t=t.N;else{if(!(n>0))return t;t=t.K}}return r},e.prototype.Z=function(t,e){var r=this.h;while(t){var n=this.$(t.u,e);n<0?(r=t,t=t.N):t=t.K}return r},e.prototype.ue=function(t){while(1){var e=t.rr;if(e===this.h)return;if(1===t.ee)return void(t.ee=0);if(t===e.K){var r=e.N;if(1===r.ee)r.ee=0,e.ee=1,e===this.W?this.W=e.ne():e.ne();else{if(r.N&&1===r.N.ee)return r.ee=e.ee,e.ee=0,r.N.ee=0,void(e===this.W?this.W=e.ne():e.ne());r.K&&1===r.K.ee?(r.ee=1,r.K.ee=0,r.te()):(r.ee=1,t=e)}}else{r=e.K;if(1===r.ee)r.ee=0,e.ee=1,e===this.W?this.W=e.te():e.te();else{if(r.K&&1===r.K.ee)return r.ee=e.ee,e.ee=0,r.K.ee=0,void(e===this.W?this.W=e.te():e.te());r.N&&1===r.N.ee?(r.ee=1,r.N.ee=0,r.ne()):(r.ee=1,t=e)}}}},e.prototype.he=function(t){var e,r;if(1===this.M)return this.clear(),this.h;var n=t;while(n.K||n.N){if(n.N){n=n.N;while(n.K)n=n.K}else n=n.K;e=Q([n.u,t.u],2),t.u=e[0],n.u=e[1],r=Q([n.p,t.p],2),t.p=r[0],n.p=r[1],t=n}this.h.K===n?this.h.K=n.rr:this.h.N===n&&(this.h.N=n.rr),this.ue(n);var i=n.rr;return n===i.K?i.K=void 0:i.N=void 0,this.M-=1,this.W.ee=0,i},e.prototype.ae=function(t,e){if(void 0===t)return!1;var r=this.ae(t.K,e);return!!r||(!!e(t)||this.ae(t.N,e))},e.prototype.fe=function(t){while(1){var e=t.rr;if(0===e.ee)return;var r=e.rr;if(e===r.K){var n=r.N;if(n&&1===n.ee){if(n.ee=e.ee=0,r===this.W)return;r.ee=1,t=r;continue}if(t===e.N){if(t.ee=0,t.K&&(t.K.rr=e),t.N&&(t.N.rr=r),e.N=t.K,r.K=t.N,t.K=e,t.N=r,r===this.W)this.W=t,this.h.rr=t;else{var i=r.rr;i.K===r?i.K=t:i.N=t}return t.rr=r.rr,e.rr=t,r.rr=t,r.ee=1,{parentNode:e,grandParent:r,curNode:t}}e.ee=0,r===this.W?this.W=r.te():r.te(),r.ee=1}else{n=r.K;if(n&&1===n.ee){if(n.ee=e.ee=0,r===this.W)return;r.ee=1,t=r;continue}if(t===e.K){if(t.ee=0,t.K&&(t.K.rr=r),t.N&&(t.N.rr=e),r.N=t.K,e.K=t.N,t.K=r,t.N=e,r===this.W)this.W=t,this.h.rr=t;else{i=r.rr;i.K===r?i.K=t:i.N=t}return t.rr=r.rr,e.rr=t,r.rr=t,r.ee=1,{parentNode:e,grandParent:r,curNode:t}}e.ee=0,r===this.W?this.W=r.ne():r.ne(),r.ee=1}return}},e.prototype.se=function(t,e,r){if(void 0===this.W)return this.M+=1,this.W=new this.re(t,e),this.W.ee=0,this.W.rr=this.h,this.h.rr=this.W,this.h.K=this.W,void(this.h.N=this.W);var n,i=this.h.K,o=this.$(i.u,t);if(0!==o){if(o>0)i.K=new this.re(t,e),i.K.rr=i,n=i.K,this.h.K=n;else{var s=this.h.N,a=this.$(s.u,t);if(0===a)return void(s.p=e);if(a<0)s.N=new this.re(t,e),s.N.rr=s,n=s.N,this.h.N=n;else{if(void 0!==r){var c=r.o;if(c!==this.h){var u=this.$(c.u,t);if(0===u)return void(c.p=e);if(u>0){var l=c.L(),h=this.$(l.u,t);if(0===h)return void(l.p=e);h<0&&(n=new this.re(t,e),void 0===l.N?(l.N=n,n.rr=l):(c.K=n,n.rr=c))}}}if(void 0===n){n=this.W;while(1){var f=this.$(n.u,t);if(f>0){if(void 0===n.K){n.K=new this.re(t,e),n.K.rr=n,n=n.K;break}n=n.K}else{if(!(f<0))return void(n.p=e);if(void 0===n.N){n.N=new this.re(t,e),n.N.rr=n,n=n.N;break}n=n.N}}}}}return this.M+=1,n}i.p=e},e.prototype.g=function(t,e){while(t){var r=this.$(t.u,e);if(r<0)t=t.N;else{if(!(r>0))return t;t=t.K}}return t||this.h},e.prototype.clear=function(){this.M=0,this.W=void 0,this.h.rr=void 0,this.h.K=this.h.N=void 0},e.prototype.updateKeyByIterator=function(t,e){var r=t.o;if(r===this.h&&w(),1===this.M)return r.u=e,!0;if(r===this.h.K)return this.$(r.m().u,e)>0&&(r.u=e,!0);if(r===this.h.N)return this.$(r.L().u,e)<0&&(r.u=e,!0);var n=r.L().u;if(this.$(n,e)>=0)return!1;var i=r.m().u;return!(this.$(i,e)<=0)&&(r.u=e,!0)},e.prototype.eraseElementByPos=function(t){if(t<0||t>this.M-1)throw new RangeError;var e=0,r=this;return this.ae(this.W,(function(n){return t===e?(r.G(n),!0):(e+=1,!1)})),this.M},e.prototype.eraseElementByKey=function(t){if(0===this.M)return!1;var e=this.g(this.W,t);return e!==this.h&&(this.G(e),!0)},e.prototype.eraseElementByIterator=function(t){var e=t.o;e===this.h&&w();var r=void 0===e.N,n=0===t.iteratorType;return n?r&&t.next():r&&void 0!==e.K||t.next(),this.G(e),t},e.prototype.forEach=function(t){var e,r,n=0;try{for(var i=$(this),o=i.next();!o.done;o=i.next()){var s=o.value;t(s,n++,this)}}catch(t){e={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}},e.prototype.getElementByPos=function(t){var e,r,n;if(t<0||t>this.M-1)throw new RangeError;var i=0;try{for(var o=$(this),s=o.next();!s.done;s=o.next()){var a=s.value;if(i===t){n=a;break}i+=1}}catch(t){e={error:t}}finally{try{s&&!s.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}return n},e.prototype.getHeight=function(){if(0===this.M)return 0;var t=function(e){return e?Math.max(t(e.K),t(e.N))+1:0};return t(this.W)},e}(s),Y=J,X=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Z=function(t){function e(e,r,n){var i=t.call(this,n)||this;return i.o=e,i.h=r,0===i.iteratorType?(i.pre=function(){return this.o===this.h.K&&w(),this.o=this.o.L(),this},i.next=function(){return this.o===this.h&&w(),this.o=this.o.m(),this}):(i.pre=function(){return this.o===this.h.N&&w(),this.o=this.o.m(),this},i.next=function(){return this.o===this.h&&w(),this.o=this.o.L(),this}),i}return X(e,t),Object.defineProperty(e.prototype,"index",{get:function(){var t=this.o,e=this.h.rr;if(t===this.h)return e?e.tr-1:0;var r=0;t.K&&(r+=t.K.tr);while(t!==e){var n=t.rr;t===n.N&&(r+=1,n.K&&(r+=n.K.tr)),t=n}return r},enumerable:!1,configurable:!0}),e}(i),tt=Z,et=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),rt=function(t,e){var r,n,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(t){return function(e){return c([t,e])}}function c(o){if(r)throw new TypeError("Generator is already executing.");while(s)try{if(r=1,n&&(i=2&o[0]?n["return"]:o[0]?n["throw"]||((i=n["return"])&&i.call(n),0):n.next)&&!(i=i.call(n,o[1])).done)return i;switch(n=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(i=s.trys,!(i=i.length>0&&i[i.length-1])&&(6===o[0]||2===o[0])){s=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){s.label=o[1];break}if(6===o[0]&&s.label<i[1]){s.label=i[1],i=o;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(o);break}i[2]&&s.ops.pop(),s.trys.pop();continue}o=e.call(t,s)}catch(t){o=[6,t],n=0}finally{r=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}},nt=function(t){var e="function"===typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"===typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},it=function(t){function e(e,r,n,i){var o=t.call(this,e,r,i)||this;return o.container=n,o}return et(e,t),Object.defineProperty(e.prototype,"pointer",{get:function(){return this.o===this.h&&w(),this.o.u},enumerable:!1,configurable:!0}),e.prototype.copy=function(){return new e(this.o,this.h,this.container,this.iteratorType)},e}(tt),ot=function(t){function e(e,r,n){void 0===e&&(e=[]);var i=t.call(this,r,n)||this,o=i;return e.forEach((function(t){o.insert(t)})),i}return et(e,t),e.prototype.P=function(t){return rt(this,(function(e){switch(e.label){case 0:return void 0===t?[2]:[5,nt(this.P(t.K))];case 1:return e.sent(),[4,t.u];case 2:return e.sent(),[5,nt(this.P(t.N))];case 3:return e.sent(),[2]}}))},e.prototype.begin=function(){return new it(this.h.K||this.h,this.h,this)},e.prototype.end=function(){return new it(this.h,this.h,this)},e.prototype.rBegin=function(){return new it(this.h.N||this.h,this.h,this,1)},e.prototype.rEnd=function(){return new it(this.h,this.h,this,1)},e.prototype.front=function(){return this.h.K?this.h.K.u:void 0},e.prototype.back=function(){return this.h.N?this.h.N.u:void 0},e.prototype.insert=function(t,e){return this.v(t,void 0,e)},e.prototype.find=function(t){var e=this.g(this.W,t);return new it(e,this.h,this)},e.prototype.lowerBound=function(t){var e=this.U(this.W,t);return new it(e,this.h,this)},e.prototype.upperBound=function(t){var e=this.X(this.W,t);return new it(e,this.h,this)},e.prototype.reverseLowerBound=function(t){var e=this.Y(this.W,t);return new it(e,this.h,this)},e.prototype.reverseUpperBound=function(t){var e=this.Z(this.W,t);return new it(e,this.h,this)},e.prototype.union=function(t){var e=this;return t.forEach((function(t){e.insert(t)})),this.M},e.prototype[Symbol.iterator]=function(){return this.P(this.W)},e}(Y),st=ot,at=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),ct=function(t,e){var r,n,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(t){return function(e){return c([t,e])}}function c(o){if(r)throw new TypeError("Generator is already executing.");while(s)try{if(r=1,n&&(i=2&o[0]?n["return"]:o[0]?n["throw"]||((i=n["return"])&&i.call(n),0):n.next)&&!(i=i.call(n,o[1])).done)return i;switch(n=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(i=s.trys,!(i=i.length>0&&i[i.length-1])&&(6===o[0]||2===o[0])){s=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){s.label=o[1];break}if(6===o[0]&&s.label<i[1]){s.label=i[1],i=o;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(o);break}i[2]&&s.ops.pop(),s.trys.pop();continue}o=e.call(t,s)}catch(t){o=[6,t],n=0}finally{r=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}},ut=function(t){var e="function"===typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"===typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},lt=function(t){function e(e,r,n,i){var o=t.call(this,e,r,i)||this;return o.container=n,o}return at(e,t),Object.defineProperty(e.prototype,"pointer",{get:function(){this.o===this.h&&w();var t=this;return new Proxy([],{get:function(e,r){return"0"===r?t.o.u:"1"===r?t.o.p:void 0},set:function(e,r,n){if("1"!==r)throw new TypeError("props must be 1");return t.o.p=n,!0}})},enumerable:!1,configurable:!0}),e.prototype.copy=function(){return new e(this.o,this.h,this.container,this.iteratorType)},e}(tt),ht=function(t){function e(e,r,n){void 0===e&&(e=[]);var i=t.call(this,r,n)||this,o=i;return e.forEach((function(t){o.setElement(t[0],t[1])})),i}return at(e,t),e.prototype.P=function(t){return ct(this,(function(e){switch(e.label){case 0:return void 0===t?[2]:[5,ut(this.P(t.K))];case 1:return e.sent(),[4,[t.u,t.p]];case 2:return e.sent(),[5,ut(this.P(t.N))];case 3:return e.sent(),[2]}}))},e.prototype.begin=function(){return new lt(this.h.K||this.h,this.h,this)},e.prototype.end=function(){return new lt(this.h,this.h,this)},e.prototype.rBegin=function(){return new lt(this.h.N||this.h,this.h,this,1)},e.prototype.rEnd=function(){return new lt(this.h,this.h,this,1)},e.prototype.front=function(){if(0!==this.M){var t=this.h.K;return[t.u,t.p]}},e.prototype.back=function(){if(0!==this.M){var t=this.h.N;return[t.u,t.p]}},e.prototype.lowerBound=function(t){var e=this.U(this.W,t);return new lt(e,this.h,this)},e.prototype.upperBound=function(t){var e=this.X(this.W,t);return new lt(e,this.h,this)},e.prototype.reverseLowerBound=function(t){var e=this.Y(this.W,t);return new lt(e,this.h,this)},e.prototype.reverseUpperBound=function(t){var e=this.Z(this.W,t);return new lt(e,this.h,this)},e.prototype.setElement=function(t,e,r){return this.v(t,e,r)},e.prototype.find=function(t){var e=this.g(this.W,t);return new lt(e,this.h,this)},e.prototype.getElementByKey=function(t){var e=this.g(this.W,t);return e.p},e.prototype.union=function(t){var e=this;return t.forEach((function(t){e.setElement(t[0],t[1])})),this.M},e.prototype[Symbol.iterator]=function(){return this.P(this.W)},e}(Y),ft=ht;function pt(t){var e=typeof t;return"object"===e&&null!==t||"function"===e}var dt=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),yt=function(t){function e(e,r,n){var i=t.call(this,n)||this;return i.o=e,i.h=r,0===i.iteratorType?(i.pre=function(){return this.o.L===this.h&&w(),this.o=this.o.L,this},i.next=function(){return this.o===this.h&&w(),this.o=this.o.m,this}):(i.pre=function(){return this.o.m===this.h&&w(),this.o=this.o.m,this},i.next=function(){return this.o===this.h&&w(),this.o=this.o.L,this}),i}return dt(e,t),e}(i),bt=function(t){function e(){var e=t.call(this)||this;return e._=[],e.I={},e.HASH_TAG=Symbol("@@HASH_TAG"),Object.setPrototypeOf(e.I,null),e.h={},e.h.L=e.h.m=e.H=e.l=e.h,e}return dt(e,t),e.prototype.G=function(t){var e=t.L,r=t.m;e.m=r,r.L=e,t===this.H&&(this.H=r),t===this.l&&(this.l=e),this.M-=1},e.prototype.v=function(t,e,r){var n;if(void 0===r&&(r=pt(t)),r){var i=t[this.HASH_TAG];if(void 0!==i)return this._[i].p=e,this.M;Object.defineProperty(t,this.HASH_TAG,{value:this._.length,configurable:!0}),n={u:t,p:e,L:this.l,m:this.h},this._.push(n)}else{var o=this.I[t];if(o)return o.p=e,this.M;n={u:t,p:e,L:this.l,m:this.h},this.I[t]=n}return 0===this.M?(this.H=n,this.h.m=n):this.l.m=n,this.l=n,this.h.L=n,++this.M},e.prototype.g=function(t,e){if(void 0===e&&(e=pt(t)),e){var r=t[this.HASH_TAG];return void 0===r?this.h:this._[r]}return this.I[t]||this.h},e.prototype.clear=function(){var t=this.HASH_TAG;this._.forEach((function(e){delete e.u[t]})),this._=[],this.I={},Object.setPrototypeOf(this.I,null),this.M=0,this.H=this.l=this.h.L=this.h.m=this.h},e.prototype.eraseElementByKey=function(t,e){var r;if(void 0===e&&(e=pt(t)),e){var n=t[this.HASH_TAG];if(void 0===n)return!1;delete t[this.HASH_TAG],r=this._[n],delete this._[n]}else{if(r=this.I[t],void 0===r)return!1;delete this.I[t]}return this.G(r),!0},e.prototype.eraseElementByIterator=function(t){var e=t.o;return e===this.h&&w(),this.G(e),t.next()},e.prototype.eraseElementByPos=function(t){if(t<0||t>this.M-1)throw new RangeError;var e=this.H;while(t--)e=e.m;return this.G(e),this.M},e}(s),gt=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),mt=function(t,e){var r,n,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(t){return function(e){return c([t,e])}}function c(o){if(r)throw new TypeError("Generator is already executing.");while(s)try{if(r=1,n&&(i=2&o[0]?n["return"]:o[0]?n["throw"]||((i=n["return"])&&i.call(n),0):n.next)&&!(i=i.call(n,o[1])).done)return i;switch(n=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(i=s.trys,!(i=i.length>0&&i[i.length-1])&&(6===o[0]||2===o[0])){s=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){s.label=o[1];break}if(6===o[0]&&s.label<i[1]){s.label=i[1],i=o;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(o);break}i[2]&&s.ops.pop(),s.trys.pop();continue}o=e.call(t,s)}catch(t){o=[6,t],n=0}finally{r=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}},vt=function(t){function e(e,r,n,i){var o=t.call(this,e,r,i)||this;return o.container=n,o}return gt(e,t),Object.defineProperty(e.prototype,"pointer",{get:function(){return this.o===this.h&&w(),this.o.u},enumerable:!1,configurable:!0}),e.prototype.copy=function(){return new e(this.o,this.h,this.container,this.iteratorType)},e}(yt),_t=function(t){function e(e){void 0===e&&(e=[]);var r=t.call(this)||this,n=r;return e.forEach((function(t){n.insert(t)})),r}return gt(e,t),e.prototype.begin=function(){return new vt(this.H,this.h,this)},e.prototype.end=function(){return new vt(this.h,this.h,this)},e.prototype.rBegin=function(){return new vt(this.l,this.h,this,1)},e.prototype.rEnd=function(){return new vt(this.h,this.h,this,1)},e.prototype.front=function(){return this.H.u},e.prototype.back=function(){return this.l.u},e.prototype.insert=function(t,e){return this.v(t,void 0,e)},e.prototype.getElementByPos=function(t){if(t<0||t>this.M-1)throw new RangeError;var e=this.H;while(t--)e=e.m;return e.u},e.prototype.find=function(t,e){var r=this.g(t,e);return new vt(r,this.h,this)},e.prototype.forEach=function(t){var e=0,r=this.H;while(r!==this.h)t(r.u,e++,this),r=r.m},e.prototype[Symbol.iterator]=function(){return function(){var t;return mt(this,(function(e){switch(e.label){case 0:t=this.H,e.label=1;case 1:return t===this.h?[3,3]:[4,t.u];case 2:return e.sent(),t=t.m,[3,1];case 3:return[2]}}))}.bind(this)()},e}(bt),wt=_t,St=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Et=function(t,e){var r,n,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(t){return function(e){return c([t,e])}}function c(o){if(r)throw new TypeError("Generator is already executing.");while(s)try{if(r=1,n&&(i=2&o[0]?n["return"]:o[0]?n["throw"]||((i=n["return"])&&i.call(n),0):n.next)&&!(i=i.call(n,o[1])).done)return i;switch(n=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(i=s.trys,!(i=i.length>0&&i[i.length-1])&&(6===o[0]||2===o[0])){s=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){s.label=o[1];break}if(6===o[0]&&s.label<i[1]){s.label=i[1],i=o;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(o);break}i[2]&&s.ops.pop(),s.trys.pop();continue}o=e.call(t,s)}catch(t){o=[6,t],n=0}finally{r=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}},kt=function(t){function e(e,r,n,i){var o=t.call(this,e,r,i)||this;return o.container=n,o}return St(e,t),Object.defineProperty(e.prototype,"pointer",{get:function(){this.o===this.h&&w();var t=this;return new Proxy([],{get:function(e,r){return"0"===r?t.o.u:"1"===r?t.o.p:void 0},set:function(e,r,n){if("1"!==r)throw new TypeError("props must be 1");return t.o.p=n,!0}})},enumerable:!1,configurable:!0}),e.prototype.copy=function(){return new e(this.o,this.h,this.container,this.iteratorType)},e}(yt),At=function(t){function e(e){void 0===e&&(e=[]);var r=t.call(this)||this,n=r;return e.forEach((function(t){n.setElement(t[0],t[1])})),r}return St(e,t),e.prototype.begin=function(){return new kt(this.H,this.h,this)},e.prototype.end=function(){return new kt(this.h,this.h,this)},e.prototype.rBegin=function(){return new kt(this.l,this.h,this,1)},e.prototype.rEnd=function(){return new kt(this.h,this.h,this,1)},e.prototype.front=function(){if(0!==this.M)return[this.H.u,this.H.p]},e.prototype.back=function(){if(0!==this.M)return[this.l.u,this.l.p]},e.prototype.setElement=function(t,e,r){return this.v(t,e,r)},e.prototype.getElementByKey=function(t,e){if(void 0===e&&(e=pt(t)),e){var r=t[this.HASH_TAG];return void 0!==r?this._[r].p:void 0}var n=this.I[t];return n?n.p:void 0},e.prototype.getElementByPos=function(t){if(t<0||t>this.M-1)throw new RangeError;var e=this.H;while(t--)e=e.m;return[e.u,e.p]},e.prototype.find=function(t,e){var r=this.g(t,e);return new kt(r,this.h,this)},e.prototype.forEach=function(t){var e=0,r=this.H;while(r!==this.h)t([r.u,r.p],e++,this),r=r.m},e.prototype[Symbol.iterator]=function(){return function(){var t;return Et(this,(function(e){switch(e.label){case 0:t=this.H,e.label=1;case 1:return t===this.h?[3,3]:[4,[t.u,t.p]];case 2:return e.sent(),t=t.m,[3,1];case 3:return[2]}}))}.bind(this)()},e}(bt),xt=At},"3eb1":function(t,e,r){"use strict";var n=r("0f7c"),i=r("00ce"),o=r("d009"),s=i("%TypeError%"),a=i("%Function.prototype.apply%"),c=i("%Function.prototype.call%"),u=i("%Reflect.apply%",!0)||n.call(c,a),l=i("%Object.defineProperty%",!0),h=i("%Math.max%");if(l)try{l({},"a",{value:1})}catch(p){l=null}t.exports=function(t){if("function"!==typeof t)throw new s("a function is required");var e=u(n,c,arguments);return o(e,1+h(0,t.length-(arguments.length-1)),!0)};var f=function(){return u(n,a,arguments)};l?l(t.exports,"apply",{value:f}):t.exports.apply=f},"3fb5":function(t,e){"function"===typeof Object.create?t.exports=function(t,e){e&&(t.super_=e,t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}))}:t.exports=function(t,e){if(e){t.super_=e;var r=function(){};r.prototype=e.prototype,t.prototype=new r,t.prototype.constructor=t}}},"4ba9":function(t,e,r){(function(e,n,i){t.exports=n(r("21bf"),r("38ba"))})(0,(function(t){return t.mode.OFB=function(){var e=t.lib.BlockCipherMode.extend(),r=e.Encryptor=e.extend({processBlock:function(t,e){var r=this._cipher,n=r.blockSize,i=this._iv,o=this._keystream;i&&(o=this._keystream=i.slice(0),this._iv=void 0),r.encryptBlock(o,0);for(var s=0;s<n;s++)t[e+s]^=o[s]}});return e.Decryptor=r,e}(),t.mode.OFB}))},"4d84":function(t,e,r){"use strict";const n=r("3d67").OrderedSet,i=r("34eb")("number-allocator:trace"),o=r("34eb")("number-allocator:error");function s(t,e){this.low=t,this.high=e}function a(t,e){if(!(this instanceof a))return new a(t,e);this.min=t,this.max=e,this.ss=new n([],(t,e)=>t.compare(e)),i("Create"),this.clear()}s.prototype.equals=function(t){return this.low===t.low&&this.high===t.high},s.prototype.compare=function(t){return this.low<t.low&&this.high<t.low?-1:t.low<this.low&&t.high<this.low?1:0},a.prototype.firstVacant=function(){return 0===this.ss.size()?null:this.ss.front().low},a.prototype.alloc=function(){if(0===this.ss.size())return i("alloc():empty"),null;const t=this.ss.begin(),e=t.pointer.low,r=t.pointer.high,n=e;return n+1<=r?this.ss.updateKeyByIterator(t,new s(e+1,r)):this.ss.eraseElementByPos(0),i("alloc():"+n),n},a.prototype.use=function(t){const e=new s(t,t),r=this.ss.lowerBound(e);if(!r.equals(this.ss.end())){const n=r.pointer.low,o=r.pointer.high;return r.pointer.equals(e)?(this.ss.eraseElementByIterator(r),i("use():"+t),!0):!(n>t)&&(n===t?(this.ss.updateKeyByIterator(r,new s(n+1,o)),i("use():"+t),!0):o===t?(this.ss.updateKeyByIterator(r,new s(n,o-1)),i("use():"+t),!0):(this.ss.updateKeyByIterator(r,new s(t+1,o)),this.ss.insert(new s(n,t-1)),i("use():"+t),!0))}return i("use():failed"),!1},a.prototype.free=function(t){if(t<this.min||t>this.max)return void o("free():"+t+" is out of range");const e=new s(t,t),r=this.ss.upperBound(e);if(r.equals(this.ss.end())){if(r.equals(this.ss.begin()))return void this.ss.insert(e);r.pre();const n=r.pointer.high,i=r.pointer.high;i+1===t?this.ss.updateKeyByIterator(r,new s(n,t)):this.ss.insert(e)}else if(r.equals(this.ss.begin()))if(t+1===r.pointer.low){const e=r.pointer.high;this.ss.updateKeyByIterator(r,new s(t,e))}else this.ss.insert(e);else{const n=r.pointer.low,i=r.pointer.high;r.pre();const o=r.pointer.low,a=r.pointer.high;a+1===t?t+1===n?(this.ss.eraseElementByIterator(r),this.ss.updateKeyByIterator(r,new s(o,i))):this.ss.updateKeyByIterator(r,new s(o,t)):t+1===n?(this.ss.eraseElementByIterator(r.next()),this.ss.insert(new s(t,i))):this.ss.insert(e)}i("free():"+t)},a.prototype.clear=function(){i("clear()"),this.ss.clear(),this.ss.insert(new s(this.min,this.max))},a.prototype.intervalCount=function(){return this.ss.size()},a.prototype.dump=function(){console.log("length:"+this.ss.size());for(const t of this.ss)console.log(t)},t.exports=a},5156:function(t,e,r){"use strict";var n="undefined"!==typeof Symbol&&Symbol,i=r("1696");t.exports=function(){return"function"===typeof n&&("function"===typeof Symbol&&("symbol"===typeof n("foo")&&("symbol"===typeof Symbol("bar")&&i())))}},"51e9":function(t,e,r){"use strict";const n=r("f214").Duplex,i=r("3fb5"),o=r("33013");function s(t){if(!(this instanceof s))return new s(t);if("function"===typeof t){this._callback=t;const e=function(t){this._callback&&(this._callback(t),this._callback=null)}.bind(this);this.on("pipe",(function(t){t.on("error",e)})),this.on("unpipe",(function(t){t.removeListener("error",e)})),t=null}o._init.call(this,t),n.call(this)}i(s,n),Object.assign(s.prototype,o.prototype),s.prototype._new=function(t){return new s(t)},s.prototype._write=function(t,e,r){this._appendBuffer(t),"function"===typeof r&&r()},s.prototype._read=function(t){if(!this.length)return this.push(null);t=Math.min(t,this.length),this.push(this.slice(0,t)),this.consume(t)},s.prototype.end=function(t){n.prototype.end.call(this,t),this._callback&&(this._callback(null,this.slice()),this._callback=null)},s.prototype._destroy=function(t,e){this._bufs.length=0,this.length=0,e(t)},s.prototype._isBufferList=function(t){return t instanceof s||t instanceof o||s.isBufferList(t)},s.isBufferList=o.isBufferList,t.exports=s,t.exports.BufferListStream=s,t.exports.BufferList=o},"53a8":function(t,e){t.exports=n;var r=Object.prototype.hasOwnProperty;function n(){for(var t={},e=0;e<arguments.length;e++){var n=arguments[e];for(var i in n)r.call(n,i)&&(t[i]=n[i])}return t}},5402:function(t,e,r){"use strict";var n=r("00ce"),i=r("545e"),o=r("2714"),s=n("%TypeError%"),a=n("%WeakMap%",!0),c=n("%Map%",!0),u=i("WeakMap.prototype.get",!0),l=i("WeakMap.prototype.set",!0),h=i("WeakMap.prototype.has",!0),f=i("Map.prototype.get",!0),p=i("Map.prototype.set",!0),d=i("Map.prototype.has",!0),y=function(t,e){for(var r,n=t;null!==(r=n.next);n=r)if(r.key===e)return n.next=r.next,r.next=t.next,t.next=r,r},b=function(t,e){var r=y(t,e);return r&&r.value},g=function(t,e,r){var n=y(t,e);n?n.value=r:t.next={key:e,next:t.next,value:r}},m=function(t,e){return!!y(t,e)};t.exports=function(){var t,e,r,n={assert:function(t){if(!n.has(t))throw new s("Side channel does not contain "+o(t))},get:function(n){if(a&&n&&("object"===typeof n||"function"===typeof n)){if(t)return u(t,n)}else if(c){if(e)return f(e,n)}else if(r)return b(r,n)},has:function(n){if(a&&n&&("object"===typeof n||"function"===typeof n)){if(t)return h(t,n)}else if(c){if(e)return d(e,n)}else if(r)return m(r,n);return!1},set:function(n,i){a&&n&&("object"===typeof n||"function"===typeof n)?(t||(t=new a),l(t,n,i)):c?(e||(e=new c),p(e,n,i)):(r||(r={key:{},next:null}),g(r,n,i))}};return n}},"545e":function(t,e,r){"use strict";var n=r("00ce"),i=r("3eb1"),o=i(n("String.prototype.indexOf"));t.exports=function(t,e){var r=n(t,!!e);return"function"===typeof r&&o(t,".prototype.")>-1?i(r):r}},"566b":function(t,e,r){var n=r("d633");function i(t){var e=function(){return e.called?e.value:(e.called=!0,e.value=t.apply(this,arguments))};return e.called=!1,e}function o(t){var e=function(){if(e.called)throw new Error(e.onceError);return e.called=!0,e.value=t.apply(this,arguments)},r=t.name||"Function wrapped with `once`";return e.onceError=r+" shouldn't be called more than once",e.called=!1,e}t.exports=n(i),t.exports.strict=n(o),i.proto=i((function(){Object.defineProperty(Function.prototype,"once",{value:function(){return i(this)},configurable:!0}),Object.defineProperty(Function.prototype,"onceStrict",{value:function(){return o(this)},configurable:!0})}))},"56ac":function(t,e,r){(function(e,n){var i=r("035d"),o=r("ab52"),s=r("3fb5"),a=r("f9c1"),c=e.from&&e.from!==Uint8Array.from?e.from([0]):new e([0]),u=function(t,e){t._corked?t.once("uncork",e):e()},l=function(t,e){t._autoDestroy&&t.destroy(e)},h=function(t,e){return function(r){r?l(t,"premature close"===r.message?null:r):e&&!t._ended&&t.end()}},f=function(t,e){return t?t._writableState&&t._writableState.finished?e():t._writableState?t.end(e):(t.end(),void e()):e()},p=function(){},d=function(t){return new i.Readable({objectMode:!0,highWaterMark:16}).wrap(t)},y=function(t,e,r){if(!(this instanceof y))return new y(t,e,r);i.Duplex.call(this,r),this._writable=null,this._readable=null,this._readable2=null,this._autoDestroy=!r||!1!==r.autoDestroy,this._forwardDestroy=!r||!1!==r.destroy,this._forwardEnd=!r||!1!==r.end,this._corked=1,this._ondrain=null,this._drained=!1,this._forwarding=!1,this._unwrite=null,this._unread=null,this._ended=!1,this.destroyed=!1,t&&this.setWritable(t),e&&this.setReadable(e)};s(y,i.Duplex),y.obj=function(t,e,r){return r||(r={}),r.objectMode=!0,r.highWaterMark=16,new y(t,e,r)},y.prototype.cork=function(){1===++this._corked&&this.emit("cork")},y.prototype.uncork=function(){this._corked&&0===--this._corked&&this.emit("uncork")},y.prototype.setWritable=function(t){if(this._unwrite&&this._unwrite(),this.destroyed)t&&t.destroy&&t.destroy();else if(null!==t&&!1!==t){var e=this,r=o(t,{writable:!0,readable:!1},h(this,this._forwardEnd)),i=function(){var t=e._ondrain;e._ondrain=null,t&&t()},s=function(){e._writable.removeListener("drain",i),r()};this._unwrite&&n.nextTick(i),this._writable=t,this._writable.on("drain",i),this._unwrite=s,this.uncork()}else this.end()},y.prototype.setReadable=function(t){if(this._unread&&this._unread(),this.destroyed)t&&t.destroy&&t.destroy();else{if(null===t||!1===t)return this.push(null),void this.resume();var e=this,r=o(t,{writable:!1,readable:!0},h(this)),n=function(){e._forward()},i=function(){e.push(null)},s=function(){e._readable2.removeListener("readable",n),e._readable2.removeListener("end",i),r()};this._drained=!0,this._readable=t,this._readable2=t._readableState?t:d(t),this._readable2.on("readable",n),this._readable2.on("end",i),this._unread=s,this._forward()}},y.prototype._read=function(){this._drained=!0,this._forward()},y.prototype._forward=function(){if(!this._forwarding&&this._readable2&&this._drained){var t;this._forwarding=!0;while(this._drained&&null!==(t=a(this._readable2)))this.destroyed||(this._drained=this.push(t));this._forwarding=!1}},y.prototype.destroy=function(t,e){if(e||(e=p),this.destroyed)return e(null);this.destroyed=!0;var r=this;n.nextTick((function(){r._destroy(t),e(null)}))},y.prototype._destroy=function(t){if(t){var e=this._ondrain;this._ondrain=null,e?e(t):this.emit("error",t)}this._forwardDestroy&&(this._readable&&this._readable.destroy&&this._readable.destroy(),this._writable&&this._writable.destroy&&this._writable.destroy()),this.emit("close")},y.prototype._write=function(t,e,r){if(!this.destroyed)return this._corked?u(this,this._write.bind(this,t,e,r)):t===c?this._finish(r):this._writable?void(!1===this._writable.write(t)?this._ondrain=r:this.destroyed||r()):r()},y.prototype._finish=function(t){var e=this;this.emit("preend"),u(this,(function(){f(e._forwardEnd&&e._writable,(function(){!1===e._writableState.prefinished&&(e._writableState.prefinished=!0),e.emit("prefinish"),u(e,t)}))}))},y.prototype.end=function(t,e,r){return"function"===typeof t?this.end(null,null,t):"function"===typeof e?this.end(t,null,e):(this._ended=!0,t&&this.write(t),this._writableState.ending||this._writableState.destroyed||this.write(c),i.Writable.prototype.end.call(this,r))},t.exports=y}).call(this,r("b639").Buffer,r("4362"))},5980:function(t,e,r){(function(e,n){t.exports=n(r("21bf"))})(0,(function(t){(function(){var e=t,r=e.lib,n=r.Base,i=e.enc,o=i.Utf8,s=e.algo;s.HMAC=n.extend({init:function(t,e){t=this._hasher=new t.init,"string"==typeof e&&(e=o.parse(e));var r=t.blockSize,n=4*r;e.sigBytes>n&&(e=t.finalize(e)),e.clamp();for(var i=this._oKey=e.clone(),s=this._iKey=e.clone(),a=i.words,c=s.words,u=0;u<r;u++)a[u]^=1549556828,c[u]^=909522486;i.sigBytes=s.sigBytes=n,this.reset()},reset:function(){var t=this._hasher;t.reset(),t.update(this._iKey)},update:function(t){return this._hasher.update(t),this},finalize:function(t){var e=this._hasher,r=e.finalize(t);e.reset();var n=e.finalize(this._oKey.clone().concat(r));return n}})})()}))},6155:function(t,e,r){const n=r("4d84");t.exports.NumberAllocator=n},"63f0":function(t,e,r){"use strict";const n=r("2fae"),i=Symbol("max"),o=Symbol("length"),s=Symbol("lengthCalculator"),a=Symbol("allowStale"),c=Symbol("maxAge"),u=Symbol("dispose"),l=Symbol("noDisposeOnSet"),h=Symbol("lruList"),f=Symbol("cache"),p=Symbol("updateAgeOnGet"),d=()=>1;class y{constructor(t){if("number"===typeof t&&(t={max:t}),t||(t={}),t.max&&("number"!==typeof t.max||t.max<0))throw new TypeError("max must be a non-negative number");this[i]=t.max||1/0;const e=t.length||d;if(this[s]="function"!==typeof e?d:e,this[a]=t.stale||!1,t.maxAge&&"number"!==typeof t.maxAge)throw new TypeError("maxAge must be a number");this[c]=t.maxAge||0,this[u]=t.dispose,this[l]=t.noDisposeOnSet||!1,this[p]=t.updateAgeOnGet||!1,this.reset()}set max(t){if("number"!==typeof t||t<0)throw new TypeError("max must be a non-negative number");this[i]=t||1/0,m(this)}get max(){return this[i]}set allowStale(t){this[a]=!!t}get allowStale(){return this[a]}set maxAge(t){if("number"!==typeof t)throw new TypeError("maxAge must be a non-negative number");this[c]=t,m(this)}get maxAge(){return this[c]}set lengthCalculator(t){"function"!==typeof t&&(t=d),t!==this[s]&&(this[s]=t,this[o]=0,this[h].forEach(t=>{t.length=this[s](t.value,t.key),this[o]+=t.length})),m(this)}get lengthCalculator(){return this[s]}get length(){return this[o]}get itemCount(){return this[h].length}rforEach(t,e){e=e||this;for(let r=this[h].tail;null!==r;){const n=r.prev;w(this,t,r,e),r=n}}forEach(t,e){e=e||this;for(let r=this[h].head;null!==r;){const n=r.next;w(this,t,r,e),r=n}}keys(){return this[h].toArray().map(t=>t.key)}values(){return this[h].toArray().map(t=>t.value)}reset(){this[u]&&this[h]&&this[h].length&&this[h].forEach(t=>this[u](t.key,t.value)),this[f]=new Map,this[h]=new n,this[o]=0}dump(){return this[h].map(t=>!g(this,t)&&{k:t.key,v:t.value,e:t.now+(t.maxAge||0)}).toArray().filter(t=>t)}dumpLru(){return this[h]}set(t,e,r){if(r=r||this[c],r&&"number"!==typeof r)throw new TypeError("maxAge must be a number");const n=r?Date.now():0,a=this[s](e,t);if(this[f].has(t)){if(a>this[i])return v(this,this[f].get(t)),!1;const s=this[f].get(t),c=s.value;return this[u]&&(this[l]||this[u](t,c.value)),c.now=n,c.maxAge=r,c.value=e,this[o]+=a-c.length,c.length=a,this.get(t),m(this),!0}const p=new _(t,e,a,n,r);return p.length>this[i]?(this[u]&&this[u](t,e),!1):(this[o]+=p.length,this[h].unshift(p),this[f].set(t,this[h].head),m(this),!0)}has(t){if(!this[f].has(t))return!1;const e=this[f].get(t).value;return!g(this,e)}get(t){return b(this,t,!0)}peek(t){return b(this,t,!1)}pop(){const t=this[h].tail;return t?(v(this,t),t.value):null}del(t){v(this,this[f].get(t))}load(t){this.reset();const e=Date.now();for(let r=t.length-1;r>=0;r--){const n=t[r],i=n.e||0;if(0===i)this.set(n.k,n.v);else{const t=i-e;t>0&&this.set(n.k,n.v,t)}}}prune(){this[f].forEach((t,e)=>b(this,e,!1))}}const b=(t,e,r)=>{const n=t[f].get(e);if(n){const e=n.value;if(g(t,e)){if(v(t,n),!t[a])return}else r&&(t[p]&&(n.value.now=Date.now()),t[h].unshiftNode(n));return e.value}},g=(t,e)=>{if(!e||!e.maxAge&&!t[c])return!1;const r=Date.now()-e.now;return e.maxAge?r>e.maxAge:t[c]&&r>t[c]},m=t=>{if(t[o]>t[i])for(let e=t[h].tail;t[o]>t[i]&&null!==e;){const r=e.prev;v(t,e),e=r}},v=(t,e)=>{if(e){const r=e.value;t[u]&&t[u](r.key,r.value),t[o]-=r.length,t[f].delete(r.key),t[h].removeNode(e)}};class _{constructor(t,e,r,n,i){this.key=t,this.value=e,this.length=r,this.now=n,this.maxAge=i||0}}const w=(t,e,r,n)=>{let i=r.value;g(t,i)&&(v(t,r),t[a]||(i=void 0)),i&&e.call(n,i.value,i.key,t)};t.exports=y},"64b0":function(t,e,r){"use strict";var n=r("00ce"),i=n("%Object.defineProperty%",!0),o=function(){if(i)try{return i({},"a",{value:1}),!0}catch(t){return!1}return!1};o.hasArrayLengthDefineBug=function(){if(!o())return null;try{return 1!==i([],"length",{value:1}).length}catch(t){return!0}},t.exports=o},"652a":function(t,e,r){"use strict";var n;function i(t){var e=!1;return function(){e||(e=!0,t.apply(void 0,arguments))}}var o=r("fbd7").codes,s=o.ERR_MISSING_ARGS,a=o.ERR_STREAM_DESTROYED;function c(t){if(t)throw t}function u(t){return t.setHeader&&"function"===typeof t.abort}function l(t,e,o,s){s=i(s);var c=!1;t.on("close",(function(){c=!0})),void 0===n&&(n=r("d9e1")),n(t,{readable:e,writable:o},(function(t){if(t)return s(t);c=!0,s()}));var l=!1;return function(e){if(!c&&!l)return l=!0,u(t)?t.abort():"function"===typeof t.destroy?t.destroy():void s(e||new a("pipe"))}}function h(t){t()}function f(t,e){return t.pipe(e)}function p(t){return t.length?"function"!==typeof t[t.length-1]?c:t.pop():c}function d(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];var n,i=p(e);if(Array.isArray(e[0])&&(e=e[0]),e.length<2)throw new s("streams");var o=e.map((function(t,r){var s=r<e.length-1,a=r>0;return l(t,s,a,(function(t){n||(n=t),t&&o.forEach(h),s||(o.forEach(h),i(n))}))}));return e.reduce(f)}t.exports=d},"688e":function(t,e,r){"use strict";var n="Function.prototype.bind called on incompatible ",i=Object.prototype.toString,o=Math.max,s="[object Function]",a=function(t,e){for(var r=[],n=0;n<t.length;n+=1)r[n]=t[n];for(var i=0;i<e.length;i+=1)r[i+t.length]=e[i];return r},c=function(t,e){for(var r=[],n=e||0,i=0;n<t.length;n+=1,i+=1)r[i]=t[n];return r},u=function(t,e){for(var r="",n=0;n<t.length;n+=1)r+=t[n],n+1<t.length&&(r+=e);return r};t.exports=function(t){var e=this;if("function"!==typeof e||i.apply(e)!==s)throw new TypeError(n+e);for(var r,l=c(arguments,1),h=function(){if(this instanceof r){var n=e.apply(this,a(l,arguments));return Object(n)===n?n:this}return e.apply(t,a(l,arguments))},f=o(0,e.length-l.length),p=[],d=0;d<f;d++)p[d]="$"+d;if(r=Function("binder","return function ("+u(p,",")+"){ return binder.apply(this,arguments); }")(h),e.prototype){var y=function(){};y.prototype=e.prototype,r.prototype=new y,y.prototype=null}return r}},"6d08":function(t,e,r){(function(e,n,i){t.exports=n(r("21bf"),r("38ba"))})(0,(function(t){return function(e){var r=t,n=r.lib,i=n.CipherParams,o=r.enc,s=o.Hex,a=r.format;a.Hex={stringify:function(t){return t.ciphertext.toString(s)},parse:function(t){var e=s.parse(t);return i.create({ciphertext:e})}}}(),t.format.Hex}))},7058:function(t,e,r){"use strict";t.exports=i;var n=r("3ca2");function i(t){if(!(this instanceof i))return new i(t);n.call(this,t)}r("3fb5")(i,n),i.prototype._transform=function(t,e,r){r(null,t)}},7135:function(t,e,r){(function(e){const n=r("b289"),i=e.allocUnsafe(0),o=e.from([0]),s=r("f0cb"),a=r("966d").nextTick,c=r("34eb")("mqtt-packet:writeToStream"),u=s.cache,l=s.generateNumber,h=s.generateCache,f=s.genBufVariableByteInt,p=s.generate4ByteBuffer;let d=B,y=!0;function b(t,e,r){switch(c("generate called"),e.cork&&(e.cork(),a(g,e)),y&&(y=!1,h()),c("generate: packet.cmd: %s",t.cmd),t.cmd){case"connect":return m(t,e,r);case"connack":return v(t,e,r);case"publish":return _(t,e,r);case"puback":case"pubrec":case"pubrel":case"pubcomp":return w(t,e,r);case"subscribe":return S(t,e,r);case"suback":return E(t,e,r);case"unsubscribe":return k(t,e,r);case"unsuback":return A(t,e,r);case"pingreq":case"pingresp":return x(t,e,r);case"disconnect":return P(t,e,r);case"auth":return O(t,e,r);default:return e.emit("error",new Error("Unknown command")),!1}}function g(t){t.uncork()}function m(t,r,i){const o=t||{},s=o.protocolId||"MQTT";let a=o.protocolVersion||4;const c=o.will;let u=o.clean;const l=o.keepalive||0,h=o.clientId||"",f=o.username,p=o.password,y=o.properties;void 0===u&&(u=!0);let b=0;if(!s||"string"!==typeof s&&!e.isBuffer(s))return r.emit("error",new Error("Invalid protocolId")),!1;if(b+=s.length+2,3!==a&&4!==a&&5!==a)return r.emit("error",new Error("Invalid protocol version")),!1;if(b+=1,("string"===typeof h||e.isBuffer(h))&&(h||a>=4)&&(h||u))b+=e.byteLength(h)+2;else{if(a<4)return r.emit("error",new Error("clientId must be supplied before 3.1.1")),!1;if(1*u===0)return r.emit("error",new Error("clientId must be given if cleanSession set to 0")),!1}if("number"!==typeof l||l<0||l>65535||l%1!==0)return r.emit("error",new Error("Invalid keepalive")),!1;if(b+=2,b+=1,5===a){var g=L(r,y);if(!g)return!1;b+=g.length}if(c){if("object"!==typeof c)return r.emit("error",new Error("Invalid will")),!1;if(!c.topic||"string"!==typeof c.topic)return r.emit("error",new Error("Invalid will topic")),!1;if(b+=e.byteLength(c.topic)+2,b+=2,c.payload){if(!(c.payload.length>=0))return r.emit("error",new Error("Invalid will payload")),!1;"string"===typeof c.payload?b+=e.byteLength(c.payload):b+=c.payload.length}var m={};if(5===a){if(m=L(r,c.properties),!m)return!1;b+=m.length}}let v=!1;if(null!=f){if(!q(f))return r.emit("error",new Error("Invalid username")),!1;v=!0,b+=e.byteLength(f)+2}if(null!=p){if(!v)return r.emit("error",new Error("Username is required to use password")),!1;if(!q(p))return r.emit("error",new Error("Invalid password")),!1;b+=H(p)+2}r.write(n.CONNECT_HEADER),M(r,b),N(r,s),o.bridgeMode&&(a+=128),r.write(131===a?n.VERSION131:132===a?n.VERSION132:4===a?n.VERSION4:5===a?n.VERSION5:n.VERSION3);let _=0;return _|=null!=f?n.USERNAME_MASK:0,_|=null!=p?n.PASSWORD_MASK:0,_|=c&&c.retain?n.WILL_RETAIN_MASK:0,_|=c&&c.qos?c.qos<<n.WILL_QOS_SHIFT:0,_|=c?n.WILL_FLAG_MASK:0,_|=u?n.CLEAN_SESSION_MASK:0,r.write(e.from([_])),d(r,l),5===a&&g.write(),N(r,h),c&&(5===a&&m.write(),C(r,c.topic),N(r,c.payload)),null!=f&&N(r,f),null!=p&&N(r,p),!0}function v(t,r,i){const s=i?i.protocolVersion:4,a=t||{},c=5===s?a.reasonCode:a.returnCode,u=a.properties;let l=2;if("number"!==typeof c)return r.emit("error",new Error("Invalid return code")),!1;let h=null;if(5===s){if(h=L(r,u),!h)return!1;l+=h.length}return r.write(n.CONNACK_HEADER),M(r,l),r.write(a.sessionPresent?n.SESSIONPRESENT_HEADER:o),r.write(e.from([c])),null!=h&&h.write(),!0}function _(t,r,o){c("publish: packet: %o",t);const s=o?o.protocolVersion:4,a=t||{},u=a.qos||0,l=a.retain?n.RETAIN_MASK:0,h=a.topic,f=a.payload||i,p=a.messageId,y=a.properties;let b=0;if("string"===typeof h)b+=e.byteLength(h)+2;else{if(!e.isBuffer(h))return r.emit("error",new Error("Invalid topic")),!1;b+=h.length+2}if(e.isBuffer(f)?b+=f.length:b+=e.byteLength(f),u&&"number"!==typeof p)return r.emit("error",new Error("Invalid messageId")),!1;u&&(b+=2);let g=null;if(5===s){if(g=L(r,y),!g)return!1;b+=g.length}return r.write(n.PUBLISH_HEADER[u][a.dup?1:0][l?1:0]),M(r,b),d(r,H(h)),r.write(h),u>0&&d(r,p),null!=g&&g.write(),c("publish: payload: %o",f),r.write(f)}function w(t,r,i){const o=i?i.protocolVersion:4,s=t||{},a=s.cmd||"puback",c=s.messageId,u=s.dup&&"pubrel"===a?n.DUP_MASK:0;let l=0;const h=s.reasonCode,f=s.properties;let p=5===o?3:2;if("pubrel"===a&&(l=1),"number"!==typeof c)return r.emit("error",new Error("Invalid messageId")),!1;let y=null;if(5===o&&"object"===typeof f){if(y=D(r,f,i,p),!y)return!1;p+=y.length}return r.write(n.ACKS[a][l][u][0]),M(r,p),d(r,c),5===o&&r.write(e.from([h])),null!==y&&y.write(),!0}function S(t,r,i){c("subscribe: packet: ");const o=i?i.protocolVersion:4,s=t||{},a=s.dup?n.DUP_MASK:0,u=s.messageId,l=s.subscriptions,h=s.properties;let f=0;if("number"!==typeof u)return r.emit("error",new Error("Invalid messageId")),!1;f+=2;let p=null;if(5===o){if(p=L(r,h),!p)return!1;f+=p.length}if("object"!==typeof l||!l.length)return r.emit("error",new Error("Invalid subscriptions")),!1;for(let n=0;n<l.length;n+=1){const t=l[n].topic,i=l[n].qos;if("string"!==typeof t)return r.emit("error",new Error("Invalid subscriptions - invalid topic")),!1;if("number"!==typeof i)return r.emit("error",new Error("Invalid subscriptions - invalid qos")),!1;if(5===o){const t=l[n].nl||!1;if("boolean"!==typeof t)return r.emit("error",new Error("Invalid subscriptions - invalid No Local")),!1;const e=l[n].rap||!1;if("boolean"!==typeof e)return r.emit("error",new Error("Invalid subscriptions - invalid Retain as Published")),!1;const i=l[n].rh||0;if("number"!==typeof i||i>2)return r.emit("error",new Error("Invalid subscriptions - invalid Retain Handling")),!1}f+=e.byteLength(t)+2+1}c("subscribe: writing to stream: %o",n.SUBSCRIBE_HEADER),r.write(n.SUBSCRIBE_HEADER[1][a?1:0][0]),M(r,f),d(r,u),null!==p&&p.write();let y=!0;for(const c of l){const t=c.topic,i=c.qos,s=+c.nl,a=+c.rap,u=c.rh;let l;C(r,t),l=n.SUBSCRIBE_OPTIONS_QOS[i],5===o&&(l|=s?n.SUBSCRIBE_OPTIONS_NL:0,l|=a?n.SUBSCRIBE_OPTIONS_RAP:0,l|=u?n.SUBSCRIBE_OPTIONS_RH[u]:0),y=r.write(e.from([l]))}return y}function E(t,r,i){const o=i?i.protocolVersion:4,s=t||{},a=s.messageId,c=s.granted,u=s.properties;let l=0;if("number"!==typeof a)return r.emit("error",new Error("Invalid messageId")),!1;if(l+=2,"object"!==typeof c||!c.length)return r.emit("error",new Error("Invalid qos vector")),!1;for(let e=0;e<c.length;e+=1){if("number"!==typeof c[e])return r.emit("error",new Error("Invalid qos vector")),!1;l+=1}let h=null;if(5===o){if(h=D(r,u,i,l),!h)return!1;l+=h.length}return r.write(n.SUBACK_HEADER),M(r,l),d(r,a),null!==h&&h.write(),r.write(e.from(c))}function k(t,r,i){const o=i?i.protocolVersion:4,s=t||{},a=s.messageId,c=s.dup?n.DUP_MASK:0,u=s.unsubscriptions,l=s.properties;let h=0;if("number"!==typeof a)return r.emit("error",new Error("Invalid messageId")),!1;if(h+=2,"object"!==typeof u||!u.length)return r.emit("error",new Error("Invalid unsubscriptions")),!1;for(let n=0;n<u.length;n+=1){if("string"!==typeof u[n])return r.emit("error",new Error("Invalid unsubscriptions")),!1;h+=e.byteLength(u[n])+2}let f=null;if(5===o){if(f=L(r,l),!f)return!1;h+=f.length}r.write(n.UNSUBSCRIBE_HEADER[1][c?1:0][0]),M(r,h),d(r,a),null!==f&&f.write();let p=!0;for(let e=0;e<u.length;e++)p=C(r,u[e]);return p}function A(t,r,i){const o=i?i.protocolVersion:4,s=t||{},a=s.messageId,c=s.dup?n.DUP_MASK:0,u=s.granted,l=s.properties,h=s.cmd,f=0;let p=2;if("number"!==typeof a)return r.emit("error",new Error("Invalid messageId")),!1;if(5===o){if("object"!==typeof u||!u.length)return r.emit("error",new Error("Invalid qos vector")),!1;for(let t=0;t<u.length;t+=1){if("number"!==typeof u[t])return r.emit("error",new Error("Invalid qos vector")),!1;p+=1}}let y=null;if(5===o){if(y=D(r,l,i,p),!y)return!1;p+=y.length}return r.write(n.ACKS[h][f][c][0]),M(r,p),d(r,a),null!==y&&y.write(),5===o&&r.write(e.from(u)),!0}function x(t,e,r){return e.write(n.EMPTY[t.cmd])}function P(t,r,i){const o=i?i.protocolVersion:4,s=t||{},a=s.reasonCode,c=s.properties;let u=5===o?1:0,l=null;if(5===o){if(l=D(r,c,i,u),!l)return!1;u+=l.length}return r.write(e.from([n.codes.disconnect<<4])),M(r,u),5===o&&r.write(e.from([a])),null!==l&&l.write(),!0}function O(t,r,i){const o=i?i.protocolVersion:4,s=t||{},a=s.reasonCode,c=s.properties;let u=5===o?1:0;5!==o&&r.emit("error",new Error("Invalid mqtt version for auth packet"));const l=D(r,c,i,u);return!!l&&(u+=l.length,r.write(e.from([n.codes.auth<<4])),M(r,u),r.write(e.from([a])),null!==l&&l.write(),!0)}Object.defineProperty(b,"cacheNumbers",{get(){return d===B},set(t){t?(u&&0!==Object.keys(u).length||(y=!0),d=B):(y=!1,d=I)}});const R={};function M(t,e){if(e>n.VARBYTEINT_MAX)return t.emit("error",new Error("Invalid variable byte integer: "+e)),!1;let r=R[e];return r||(r=f(e),e<16384&&(R[e]=r)),c("writeVarByteInt: writing to stream: %o",r),t.write(r)}function C(t,r){const n=e.byteLength(r);return d(t,n),c("writeString: %s",r),t.write(r,"utf8")}function T(t,e,r){C(t,e),C(t,r)}function B(t,e){return c("writeNumberCached: number: %d",e),c("writeNumberCached: %o",u[e]),t.write(u[e])}function I(t,e){const r=l(e);return c("writeNumberGenerated: %o",r),t.write(r)}function j(t,e){const r=p(e);return c("write4ByteNumber: %o",r),t.write(r)}function N(t,e){"string"===typeof e?C(t,e):e?(d(t,e.length),t.write(e)):d(t,0)}function L(t,r){if("object"!==typeof r||null!=r.length)return{length:1,write(){U(t,{},0)}};let i=0;function o(r,i){const o=n.propertiesTypes[r];let s=0;switch(o){case"byte":if("boolean"!==typeof i)return t.emit("error",new Error(`Invalid ${r}: ${i}`)),!1;s+=2;break;case"int8":if("number"!==typeof i||i<0||i>255)return t.emit("error",new Error(`Invalid ${r}: ${i}`)),!1;s+=2;break;case"binary":if(i&&null===i)return t.emit("error",new Error(`Invalid ${r}: ${i}`)),!1;s+=1+e.byteLength(i)+2;break;case"int16":if("number"!==typeof i||i<0||i>65535)return t.emit("error",new Error(`Invalid ${r}: ${i}`)),!1;s+=3;break;case"int32":if("number"!==typeof i||i<0||i>4294967295)return t.emit("error",new Error(`Invalid ${r}: ${i}`)),!1;s+=5;break;case"var":if("number"!==typeof i||i<0||i>268435455)return t.emit("error",new Error(`Invalid ${r}: ${i}`)),!1;s+=1+e.byteLength(f(i));break;case"string":if("string"!==typeof i)return t.emit("error",new Error(`Invalid ${r}: ${i}`)),!1;s+=3+e.byteLength(i.toString());break;case"pair":if("object"!==typeof i)return t.emit("error",new Error(`Invalid ${r}: ${i}`)),!1;s+=Object.getOwnPropertyNames(i).reduce((t,r)=>{const n=i[r];return Array.isArray(n)?t+=n.reduce((t,n)=>(t+=3+e.byteLength(r.toString())+2+e.byteLength(n.toString()),t),0):t+=3+e.byteLength(r.toString())+2+e.byteLength(i[r].toString()),t},0);break;default:return t.emit("error",new Error(`Invalid property ${r}: ${i}`)),!1}return s}if(r)for(const e in r){let t=0,n=0;const s=r[e];if(Array.isArray(s))for(let r=0;r<s.length;r++){if(n=o(e,s[r]),!n)return!1;t+=n}else{if(n=o(e,s),!n)return!1;t=n}if(!t)return!1;i+=t}const s=e.byteLength(f(i));return{length:s+i,write(){U(t,r,i)}}}function D(t,e,r,n){const i=["reasonString","userProperties"],o=r&&r.properties&&r.properties.maximumPacketSize?r.properties.maximumPacketSize:0;let s=L(t,e);if(o)while(n+s.length>o){const r=i.shift();if(!r||!e[r])return!1;delete e[r],s=L(t,e)}return s}function F(t,r,i){const o=n.propertiesTypes[r];switch(o){case"byte":t.write(e.from([n.properties[r]])),t.write(e.from([+i]));break;case"int8":t.write(e.from([n.properties[r]])),t.write(e.from([i]));break;case"binary":t.write(e.from([n.properties[r]])),N(t,i);break;case"int16":t.write(e.from([n.properties[r]])),d(t,i);break;case"int32":t.write(e.from([n.properties[r]])),j(t,i);break;case"var":t.write(e.from([n.properties[r]])),M(t,i);break;case"string":t.write(e.from([n.properties[r]])),C(t,i);break;case"pair":Object.getOwnPropertyNames(i).forEach(o=>{const s=i[o];Array.isArray(s)?s.forEach(i=>{t.write(e.from([n.properties[r]])),T(t,o.toString(),i.toString())}):(t.write(e.from([n.properties[r]])),T(t,o.toString(),s.toString()))});break;default:return t.emit("error",new Error(`Invalid property ${r} value: ${i}`)),!1}}function U(t,e,r){M(t,r);for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&null!==e[n]){const r=e[n];if(Array.isArray(r))for(let e=0;e<r.length;e++)F(t,n,r[e]);else F(t,n,r)}}function H(t){return t?t instanceof e?t.length:e.byteLength(t):0}function q(t){return"string"===typeof t||t instanceof e}t.exports=b}).call(this,r("b639").Buffer)},"72fe":function(t,e,r){(function(e,n){t.exports=n(r("21bf"))})(0,(function(t){return function(e){var r=t,n=r.lib,i=n.WordArray,o=n.Hasher,s=r.algo,a=[];(function(){for(var t=0;t<64;t++)a[t]=4294967296*e.abs(e.sin(t+1))|0})();var c=s.MD5=o.extend({_doReset:function(){this._hash=new i.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(t,e){for(var r=0;r<16;r++){var n=e+r,i=t[n];t[n]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8)}var o=this._hash.words,s=t[e+0],c=t[e+1],p=t[e+2],d=t[e+3],y=t[e+4],b=t[e+5],g=t[e+6],m=t[e+7],v=t[e+8],_=t[e+9],w=t[e+10],S=t[e+11],E=t[e+12],k=t[e+13],A=t[e+14],x=t[e+15],P=o[0],O=o[1],R=o[2],M=o[3];P=u(P,O,R,M,s,7,a[0]),M=u(M,P,O,R,c,12,a[1]),R=u(R,M,P,O,p,17,a[2]),O=u(O,R,M,P,d,22,a[3]),P=u(P,O,R,M,y,7,a[4]),M=u(M,P,O,R,b,12,a[5]),R=u(R,M,P,O,g,17,a[6]),O=u(O,R,M,P,m,22,a[7]),P=u(P,O,R,M,v,7,a[8]),M=u(M,P,O,R,_,12,a[9]),R=u(R,M,P,O,w,17,a[10]),O=u(O,R,M,P,S,22,a[11]),P=u(P,O,R,M,E,7,a[12]),M=u(M,P,O,R,k,12,a[13]),R=u(R,M,P,O,A,17,a[14]),O=u(O,R,M,P,x,22,a[15]),P=l(P,O,R,M,c,5,a[16]),M=l(M,P,O,R,g,9,a[17]),R=l(R,M,P,O,S,14,a[18]),O=l(O,R,M,P,s,20,a[19]),P=l(P,O,R,M,b,5,a[20]),M=l(M,P,O,R,w,9,a[21]),R=l(R,M,P,O,x,14,a[22]),O=l(O,R,M,P,y,20,a[23]),P=l(P,O,R,M,_,5,a[24]),M=l(M,P,O,R,A,9,a[25]),R=l(R,M,P,O,d,14,a[26]),O=l(O,R,M,P,v,20,a[27]),P=l(P,O,R,M,k,5,a[28]),M=l(M,P,O,R,p,9,a[29]),R=l(R,M,P,O,m,14,a[30]),O=l(O,R,M,P,E,20,a[31]),P=h(P,O,R,M,b,4,a[32]),M=h(M,P,O,R,v,11,a[33]),R=h(R,M,P,O,S,16,a[34]),O=h(O,R,M,P,A,23,a[35]),P=h(P,O,R,M,c,4,a[36]),M=h(M,P,O,R,y,11,a[37]),R=h(R,M,P,O,m,16,a[38]),O=h(O,R,M,P,w,23,a[39]),P=h(P,O,R,M,k,4,a[40]),M=h(M,P,O,R,s,11,a[41]),R=h(R,M,P,O,d,16,a[42]),O=h(O,R,M,P,g,23,a[43]),P=h(P,O,R,M,_,4,a[44]),M=h(M,P,O,R,E,11,a[45]),R=h(R,M,P,O,x,16,a[46]),O=h(O,R,M,P,p,23,a[47]),P=f(P,O,R,M,s,6,a[48]),M=f(M,P,O,R,m,10,a[49]),R=f(R,M,P,O,A,15,a[50]),O=f(O,R,M,P,b,21,a[51]),P=f(P,O,R,M,E,6,a[52]),M=f(M,P,O,R,d,10,a[53]),R=f(R,M,P,O,w,15,a[54]),O=f(O,R,M,P,c,21,a[55]),P=f(P,O,R,M,v,6,a[56]),M=f(M,P,O,R,x,10,a[57]),R=f(R,M,P,O,g,15,a[58]),O=f(O,R,M,P,k,21,a[59]),P=f(P,O,R,M,y,6,a[60]),M=f(M,P,O,R,S,10,a[61]),R=f(R,M,P,O,p,15,a[62]),O=f(O,R,M,P,_,21,a[63]),o[0]=o[0]+P|0,o[1]=o[1]+O|0,o[2]=o[2]+R|0,o[3]=o[3]+M|0},_doFinalize:function(){var t=this._data,r=t.words,n=8*this._nDataBytes,i=8*t.sigBytes;r[i>>>5]|=128<<24-i%32;var o=e.floor(n/4294967296),s=n;r[15+(i+64>>>9<<4)]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),r[14+(i+64>>>9<<4)]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),t.sigBytes=4*(r.length+1),this._process();for(var a=this._hash,c=a.words,u=0;u<4;u++){var l=c[u];c[u]=16711935&(l<<8|l>>>24)|4278255360&(l<<24|l>>>8)}return a},clone:function(){var t=o.clone.call(this);return t._hash=this._hash.clone(),t}});function u(t,e,r,n,i,o,s){var a=t+(e&r|~e&n)+i+s;return(a<<o|a>>>32-o)+e}function l(t,e,r,n,i,o,s){var a=t+(e&n|r&~n)+i+s;return(a<<o|a>>>32-o)+e}function h(t,e,r,n,i,o,s){var a=t+(e^r^n)+i+s;return(a<<o|a>>>32-o)+e}function f(t,e,r,n,i,o,s){var a=t+(r^(e|~n))+i+s;return(a<<o|a>>>32-o)+e}r.MD5=o._createHelper(c),r.HmacMD5=o._createHmacHelper(c)}(Math),t.MD5}))},"782c":function(t,e,r){"use strict";(function(e){var n;function i(t,e,r){return e=o(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function o(t){var e=s(t,"string");return"symbol"===typeof e?e:String(e)}function s(t,e){if("object"!==typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var a=r("bf09"),c=Symbol("lastResolve"),u=Symbol("lastReject"),l=Symbol("error"),h=Symbol("ended"),f=Symbol("lastPromise"),p=Symbol("handlePromise"),d=Symbol("stream");function y(t,e){return{value:t,done:e}}function b(t){var e=t[c];if(null!==e){var r=t[d].read();null!==r&&(t[f]=null,t[c]=null,t[u]=null,e(y(r,!1)))}}function g(t){e.nextTick(b,t)}function m(t,e){return function(r,n){t.then((function(){e[h]?r(y(void 0,!0)):e[p](r,n)}),n)}}var v=Object.getPrototypeOf((function(){})),_=Object.setPrototypeOf((n={get stream(){return this[d]},next:function(){var t=this,r=this[l];if(null!==r)return Promise.reject(r);if(this[h])return Promise.resolve(y(void 0,!0));if(this[d].destroyed)return new Promise((function(r,n){e.nextTick((function(){t[l]?n(t[l]):r(y(void 0,!0))}))}));var n,i=this[f];if(i)n=new Promise(m(i,this));else{var o=this[d].read();if(null!==o)return Promise.resolve(y(o,!1));n=new Promise(this[p])}return this[f]=n,n}},i(n,Symbol.asyncIterator,(function(){return this})),i(n,"return",(function(){var t=this;return new Promise((function(e,r){t[d].destroy(null,(function(t){t?r(t):e(y(void 0,!0))}))}))})),n),v),w=function(t){var e,r=Object.create(_,(e={},i(e,d,{value:t,writable:!0}),i(e,c,{value:null,writable:!0}),i(e,u,{value:null,writable:!0}),i(e,l,{value:null,writable:!0}),i(e,h,{value:t._readableState.endEmitted,writable:!0}),i(e,p,{value:function(t,e){var n=r[d].read();n?(r[f]=null,r[c]=null,r[u]=null,t(y(n,!1))):(r[c]=t,r[u]=e)},writable:!0}),e));return r[f]=null,a(t,(function(t){if(t&&"ERR_STREAM_PREMATURE_CLOSE"!==t.code){var e=r[u];return null!==e&&(r[f]=null,r[c]=null,r[u]=null,e(t)),void(r[l]=t)}var n=r[c];null!==n&&(r[f]=null,r[c]=null,r[u]=null,n(y(void 0,!0))),r[h]=!0})),t.on("readable",g.bind(null,r)),r};t.exports=w}).call(this,r("4362"))},7992:function(t,e,r){"use strict";var n=r("64b0")(),i=r("00ce"),o=n&&i("%Object.defineProperty%",!0);if(o)try{o({},"a",{value:1})}catch(u){o=!1}var s=i("%SyntaxError%"),a=i("%TypeError%"),c=r("2aa9");t.exports=function(t,e,r){if(!t||"object"!==typeof t&&"function"!==typeof t)throw new a("`obj` must be an object or a function`");if("string"!==typeof e&&"symbol"!==typeof e)throw new a("`property` must be a string or a symbol`");if(arguments.length>3&&"boolean"!==typeof arguments[3]&&null!==arguments[3])throw new a("`nonEnumerable`, if provided, must be a boolean or null");if(arguments.length>4&&"boolean"!==typeof arguments[4]&&null!==arguments[4])throw new a("`nonWritable`, if provided, must be a boolean or null");if(arguments.length>5&&"boolean"!==typeof arguments[5]&&null!==arguments[5])throw new a("`nonConfigurable`, if provided, must be a boolean or null");if(arguments.length>6&&"boolean"!==typeof arguments[6])throw new a("`loose`, if provided, must be a boolean");var n=arguments.length>3?arguments[3]:null,i=arguments.length>4?arguments[4]:null,u=arguments.length>5?arguments[5]:null,l=arguments.length>6&&arguments[6],h=!!c&&c(t,e);if(o)o(t,e,{configurable:null===u&&h?h.configurable:!u,enumerable:null===n&&h?h.enumerable:!n,value:r,writable:null===i&&h?h.writable:!i});else{if(!l&&(n||i||u))throw new s("This environment does not support defining a property as non-configurable, non-writable, or non-enumerable.");t[e]=r}}},"7bbc":function(t,e,r){(function(e,n,i){t.exports=n(r("21bf"),r("94f8"),r("5980"))})(0,(function(t){return function(){var e=t,r=e.lib,n=r.Base,i=r.WordArray,o=e.algo,s=o.SHA256,a=o.HMAC,c=o.PBKDF2=n.extend({cfg:n.extend({keySize:4,hasher:s,iterations:25e4}),init:function(t){this.cfg=this.cfg.extend(t)},compute:function(t,e){var r=this.cfg,n=a.create(r.hasher,t),o=i.create(),s=i.create([1]),c=o.words,u=s.words,l=r.keySize,h=r.iterations;while(c.length<l){var f=n.update(e).finalize(s);n.reset();for(var p=f.words,d=p.length,y=f,b=1;b<h;b++){y=n.finalize(y),n.reset();for(var g=y.words,m=0;m<d;m++)p[m]^=g[m]}o.concat(f),u[0]++}return o.sigBytes=4*l,o}});e.PBKDF2=function(t,e,r){return c.create(r).compute(t,e)}}(),t.PBKDF2}))},"7f0f":function(t,e,r){(function(e){const n=r("7135"),i=r("faa1");function o(t,e){const r=new s;return n(t,r,e),r.concat()}class s extends i{constructor(){super(),this._array=new Array(20),this._i=0}write(t){return this._array[this._i++]=t,!0}concat(){let t=0;const r=new Array(this._array.length),n=this._array;let i,o=0;for(i=0;i<n.length&&void 0!==n[i];i++)"string"!==typeof n[i]?r[i]=n[i].length:r[i]=e.byteLength(n[i]),t+=r[i];const s=e.allocUnsafe(t);for(i=0;i<n.length&&void 0!==n[i];i++)"string"!==typeof n[i]?(n[i].copy(s,o),o+=r[i]):(s.write(n[i],o),o+=r[i]);return s}}t.exports=o}).call(this,r("b639").Buffer)},"81bf":function(t,e,r){(function(e,n,i){t.exports=n(r("21bf"),r("38ba"))})(0,(function(t){return t.mode.ECB=function(){var e=t.lib.BlockCipherMode.extend();return e.Encryptor=e.extend({processBlock:function(t,e){this._cipher.encryptBlock(t,e)}}),e.Decryptor=e.extend({processBlock:function(t,e){this._cipher.decryptBlock(t,e)}}),e}(),t.mode.ECB}))},8311:function(t,e,r){"use strict";const{Buffer:n}=r("b639"),i=r("035d").Transform,o=r("56ac");let s,a,c;function u(){const t=new i;return t._write=function(t,e,r){s.send({data:t.buffer,success:function(){r()},fail:function(t){r(new Error(t))}})},t._flush=function(t){s.close({success:function(){t()}})},t}function l(t){t.hostname||(t.hostname="localhost"),t.path||(t.path="/"),t.wsOptions||(t.wsOptions={})}function h(t,e){const r="wxs"===t.protocol?"wss":"ws";let n=r+"://"+t.hostname+t.path;return t.port&&80!==t.port&&443!==t.port&&(n=r+"://"+t.hostname+":"+t.port+t.path),"function"===typeof t.transformWsUrl&&(n=t.transformWsUrl(n,t,e)),n}function f(){s.onOpen((function(){c.setReadable(a),c.setWritable(a),c.emit("connect")})),s.onMessage((function(t){let e=t.data;e=e instanceof ArrayBuffer?n.from(e):n.from(e,"utf8"),a.push(e)})),s.onClose((function(){c.end(),c.destroy()})),s.onError((function(t){c.destroy(new Error(t.errMsg))}))}function p(t,e){if(e.hostname=e.hostname||e.host,!e.hostname)throw new Error("Could not determine host. Specify host manually.");const r="MQIsdp"===e.protocolId&&3===e.protocolVersion?"mqttv3.1":"mqtt";l(e);const n=h(e,t);s=wx.connectSocket({url:n,protocols:[r]}),a=u(),c=o.obj(),c._destroy=function(t,e){s.close({success:function(){e&&e(t)}})};const i=c.destroy;return c.destroy=function(){c.destroy=i;const t=this;setTimeout((function(){s.close({fail:function(){t._destroy(new Error)}})}),0)}.bind(c),f(),c}t.exports=p},"85f8":function(t,e,r){"use strict";(function(e,n){var i;t.exports=R,R.ReadableState=O;r("faa1").EventEmitter;var o=function(t,e){return t.listeners(e).length},s=r("9ede"),a=r("b639").Buffer,c=("undefined"!==typeof e?e:"undefined"!==typeof window?window:"undefined"!==typeof self?self:{}).Uint8Array||function(){};function u(t){return a.from(t)}function l(t){return a.isBuffer(t)||t instanceof c}var h,f=r(3);h=f&&f.debuglog?f.debuglog("stream"):function(){};var p,d,y,b=r("f688"),g=r("edb3"),m=r("31b5"),v=m.getHighWaterMark,_=r("fbd7").codes,w=_.ERR_INVALID_ARG_TYPE,S=_.ERR_STREAM_PUSH_AFTER_EOF,E=_.ERR_METHOD_NOT_IMPLEMENTED,k=_.ERR_STREAM_UNSHIFT_AFTER_END_EVENT;r("3fb5")(R,s);var A=g.errorOrDestroy,x=["error","close","destroy","pause","resume"];function P(t,e,r){if("function"===typeof t.prependListener)return t.prependListener(e,r);t._events&&t._events[e]?Array.isArray(t._events[e])?t._events[e].unshift(r):t._events[e]=[r,t._events[e]]:t.on(e,r)}function O(t,e,n){i=i||r("be3f"),t=t||{},"boolean"!==typeof n&&(n=e instanceof i),this.objectMode=!!t.objectMode,n&&(this.objectMode=this.objectMode||!!t.readableObjectMode),this.highWaterMark=v(this,t,"readableHighWaterMark",n),this.buffer=new b,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.paused=!0,this.emitClose=!1!==t.emitClose,this.autoDestroy=!!t.autoDestroy,this.destroyed=!1,this.defaultEncoding=t.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,t.encoding&&(p||(p=r("aa22").StringDecoder),this.decoder=new p(t.encoding),this.encoding=t.encoding)}function R(t){if(i=i||r("be3f"),!(this instanceof R))return new R(t);var e=this instanceof i;this._readableState=new O(t,this,e),this.readable=!0,t&&("function"===typeof t.read&&(this._read=t.read),"function"===typeof t.destroy&&(this._destroy=t.destroy)),s.call(this)}function M(t,e,r,n,i){h("readableAddChunk",e);var o,s=t._readableState;if(null===e)s.reading=!1,N(t,s);else if(i||(o=T(s,e)),o)A(t,o);else if(s.objectMode||e&&e.length>0)if("string"===typeof e||s.objectMode||Object.getPrototypeOf(e)===a.prototype||(e=u(e)),n)s.endEmitted?A(t,new k):C(t,s,e,!0);else if(s.ended)A(t,new S);else{if(s.destroyed)return!1;s.reading=!1,s.decoder&&!r?(e=s.decoder.write(e),s.objectMode||0!==e.length?C(t,s,e,!1):F(t,s)):C(t,s,e,!1)}else n||(s.reading=!1,F(t,s));return!s.ended&&(s.length<s.highWaterMark||0===s.length)}function C(t,e,r,n){e.flowing&&0===e.length&&!e.sync?(e.awaitDrain=0,t.emit("data",r)):(e.length+=e.objectMode?1:r.length,n?e.buffer.unshift(r):e.buffer.push(r),e.needReadable&&L(t)),F(t,e)}function T(t,e){var r;return l(e)||"string"===typeof e||void 0===e||t.objectMode||(r=new w("chunk",["string","Buffer","Uint8Array"],e)),r}Object.defineProperty(R.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._readableState&&this._readableState.destroyed},set:function(t){this._readableState&&(this._readableState.destroyed=t)}}),R.prototype.destroy=g.destroy,R.prototype._undestroy=g.undestroy,R.prototype._destroy=function(t,e){e(t)},R.prototype.push=function(t,e){var r,n=this._readableState;return n.objectMode?r=!0:"string"===typeof t&&(e=e||n.defaultEncoding,e!==n.encoding&&(t=a.from(t,e),e=""),r=!0),M(this,t,e,!1,r)},R.prototype.unshift=function(t){return M(this,t,null,!0,!1)},R.prototype.isPaused=function(){return!1===this._readableState.flowing},R.prototype.setEncoding=function(t){p||(p=r("aa22").StringDecoder);var e=new p(t);this._readableState.decoder=e,this._readableState.encoding=this._readableState.decoder.encoding;var n=this._readableState.buffer.head,i="";while(null!==n)i+=e.write(n.data),n=n.next;return this._readableState.buffer.clear(),""!==i&&this._readableState.buffer.push(i),this._readableState.length=i.length,this};var B=1073741824;function I(t){return t>=B?t=B:(t--,t|=t>>>1,t|=t>>>2,t|=t>>>4,t|=t>>>8,t|=t>>>16,t++),t}function j(t,e){return t<=0||0===e.length&&e.ended?0:e.objectMode?1:t!==t?e.flowing&&e.length?e.buffer.head.data.length:e.length:(t>e.highWaterMark&&(e.highWaterMark=I(t)),t<=e.length?t:e.ended?e.length:(e.needReadable=!0,0))}function N(t,e){if(h("onEofChunk"),!e.ended){if(e.decoder){var r=e.decoder.end();r&&r.length&&(e.buffer.push(r),e.length+=e.objectMode?1:r.length)}e.ended=!0,e.sync?L(t):(e.needReadable=!1,e.emittedReadable||(e.emittedReadable=!0,D(t)))}}function L(t){var e=t._readableState;h("emitReadable",e.needReadable,e.emittedReadable),e.needReadable=!1,e.emittedReadable||(h("emitReadable",e.flowing),e.emittedReadable=!0,n.nextTick(D,t))}function D(t){var e=t._readableState;h("emitReadable_",e.destroyed,e.length,e.ended),e.destroyed||!e.length&&!e.ended||(t.emit("readable"),e.emittedReadable=!1),e.needReadable=!e.flowing&&!e.ended&&e.length<=e.highWaterMark,V(t)}function F(t,e){e.readingMore||(e.readingMore=!0,n.nextTick(U,t,e))}function U(t,e){while(!e.reading&&!e.ended&&(e.length<e.highWaterMark||e.flowing&&0===e.length)){var r=e.length;if(h("maybeReadMore read 0"),t.read(0),r===e.length)break}e.readingMore=!1}function H(t){return function(){var e=t._readableState;h("pipeOnDrain",e.awaitDrain),e.awaitDrain&&e.awaitDrain--,0===e.awaitDrain&&o(t,"data")&&(e.flowing=!0,V(t))}}function q(t){var e=t._readableState;e.readableListening=t.listenerCount("readable")>0,e.resumeScheduled&&!e.paused?e.flowing=!0:t.listenerCount("data")>0&&t.resume()}function W(t){h("readable nexttick read 0"),t.read(0)}function K(t,e){e.resumeScheduled||(e.resumeScheduled=!0,n.nextTick(z,t,e))}function z(t,e){h("resume",e.reading),e.reading||t.read(0),e.resumeScheduled=!1,t.emit("resume"),V(t),e.flowing&&!e.reading&&t.read(0)}function V(t){var e=t._readableState;h("flow",e.flowing);while(e.flowing&&null!==t.read());}function G(t,e){return 0===e.length?null:(e.objectMode?r=e.buffer.shift():!t||t>=e.length?(r=e.decoder?e.buffer.join(""):1===e.buffer.length?e.buffer.first():e.buffer.concat(e.length),e.buffer.clear()):r=e.buffer.consume(t,e.decoder),r);var r}function Q(t){var e=t._readableState;h("endReadable",e.endEmitted),e.endEmitted||(e.ended=!0,n.nextTick($,e,t))}function $(t,e){if(h("endReadableNT",t.endEmitted,t.length),!t.endEmitted&&0===t.length&&(t.endEmitted=!0,e.readable=!1,e.emit("end"),t.autoDestroy)){var r=e._writableState;(!r||r.autoDestroy&&r.finished)&&e.destroy()}}function J(t,e){for(var r=0,n=t.length;r<n;r++)if(t[r]===e)return r;return-1}R.prototype.read=function(t){h("read",t),t=parseInt(t,10);var e=this._readableState,r=t;if(0!==t&&(e.emittedReadable=!1),0===t&&e.needReadable&&((0!==e.highWaterMark?e.length>=e.highWaterMark:e.length>0)||e.ended))return h("read: emitReadable",e.length,e.ended),0===e.length&&e.ended?Q(this):L(this),null;if(t=j(t,e),0===t&&e.ended)return 0===e.length&&Q(this),null;var n,i=e.needReadable;return h("need readable",i),(0===e.length||e.length-t<e.highWaterMark)&&(i=!0,h("length less than watermark",i)),e.ended||e.reading?(i=!1,h("reading or ended",i)):i&&(h("do read"),e.reading=!0,e.sync=!0,0===e.length&&(e.needReadable=!0),this._read(e.highWaterMark),e.sync=!1,e.reading||(t=j(r,e))),n=t>0?G(t,e):null,null===n?(e.needReadable=e.length<=e.highWaterMark,t=0):(e.length-=t,e.awaitDrain=0),0===e.length&&(e.ended||(e.needReadable=!0),r!==t&&e.ended&&Q(this)),null!==n&&this.emit("data",n),n},R.prototype._read=function(t){A(this,new E("_read()"))},R.prototype.pipe=function(t,e){var r=this,i=this._readableState;switch(i.pipesCount){case 0:i.pipes=t;break;case 1:i.pipes=[i.pipes,t];break;default:i.pipes.push(t);break}i.pipesCount+=1,h("pipe count=%d opts=%j",i.pipesCount,e);var s=(!e||!1!==e.end)&&t!==n.stdout&&t!==n.stderr,a=s?u:m;function c(t,e){h("onunpipe"),t===r&&e&&!1===e.hasUnpiped&&(e.hasUnpiped=!0,p())}function u(){h("onend"),t.end()}i.endEmitted?n.nextTick(a):r.once("end",a),t.on("unpipe",c);var l=H(r);t.on("drain",l);var f=!1;function p(){h("cleanup"),t.removeListener("close",b),t.removeListener("finish",g),t.removeListener("drain",l),t.removeListener("error",y),t.removeListener("unpipe",c),r.removeListener("end",u),r.removeListener("end",m),r.removeListener("data",d),f=!0,!i.awaitDrain||t._writableState&&!t._writableState.needDrain||l()}function d(e){h("ondata");var n=t.write(e);h("dest.write",n),!1===n&&((1===i.pipesCount&&i.pipes===t||i.pipesCount>1&&-1!==J(i.pipes,t))&&!f&&(h("false write response, pause",i.awaitDrain),i.awaitDrain++),r.pause())}function y(e){h("onerror",e),m(),t.removeListener("error",y),0===o(t,"error")&&A(t,e)}function b(){t.removeListener("finish",g),m()}function g(){h("onfinish"),t.removeListener("close",b),m()}function m(){h("unpipe"),r.unpipe(t)}return r.on("data",d),P(t,"error",y),t.once("close",b),t.once("finish",g),t.emit("pipe",r),i.flowing||(h("pipe resume"),r.resume()),t},R.prototype.unpipe=function(t){var e=this._readableState,r={hasUnpiped:!1};if(0===e.pipesCount)return this;if(1===e.pipesCount)return t&&t!==e.pipes||(t||(t=e.pipes),e.pipes=null,e.pipesCount=0,e.flowing=!1,t&&t.emit("unpipe",this,r)),this;if(!t){var n=e.pipes,i=e.pipesCount;e.pipes=null,e.pipesCount=0,e.flowing=!1;for(var o=0;o<i;o++)n[o].emit("unpipe",this,{hasUnpiped:!1});return this}var s=J(e.pipes,t);return-1===s||(e.pipes.splice(s,1),e.pipesCount-=1,1===e.pipesCount&&(e.pipes=e.pipes[0]),t.emit("unpipe",this,r)),this},R.prototype.on=function(t,e){var r=s.prototype.on.call(this,t,e),i=this._readableState;return"data"===t?(i.readableListening=this.listenerCount("readable")>0,!1!==i.flowing&&this.resume()):"readable"===t&&(i.endEmitted||i.readableListening||(i.readableListening=i.needReadable=!0,i.flowing=!1,i.emittedReadable=!1,h("on readable",i.length,i.reading),i.length?L(this):i.reading||n.nextTick(W,this))),r},R.prototype.addListener=R.prototype.on,R.prototype.removeListener=function(t,e){var r=s.prototype.removeListener.call(this,t,e);return"readable"===t&&n.nextTick(q,this),r},R.prototype.removeAllListeners=function(t){var e=s.prototype.removeAllListeners.apply(this,arguments);return"readable"!==t&&void 0!==t||n.nextTick(q,this),e},R.prototype.resume=function(){var t=this._readableState;return t.flowing||(h("resume"),t.flowing=!t.readableListening,K(this,t)),t.paused=!1,this},R.prototype.pause=function(){return h("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(h("pause"),this._readableState.flowing=!1,this.emit("pause")),this._readableState.paused=!0,this},R.prototype.wrap=function(t){var e=this,r=this._readableState,n=!1;for(var i in t.on("end",(function(){if(h("wrapped end"),r.decoder&&!r.ended){var t=r.decoder.end();t&&t.length&&e.push(t)}e.push(null)})),t.on("data",(function(i){if(h("wrapped data"),r.decoder&&(i=r.decoder.write(i)),(!r.objectMode||null!==i&&void 0!==i)&&(r.objectMode||i&&i.length)){var o=e.push(i);o||(n=!0,t.pause())}})),t)void 0===this[i]&&"function"===typeof t[i]&&(this[i]=function(e){return function(){return t[e].apply(t,arguments)}}(i));for(var o=0;o<x.length;o++)t.on(x[o],this.emit.bind(this,x[o]));return this._read=function(e){h("wrapped _read",e),n&&(n=!1,t.resume())},this},"function"===typeof Symbol&&(R.prototype[Symbol.asyncIterator]=function(){return void 0===d&&(d=r("34e3")),d(this)}),Object.defineProperty(R.prototype,"readableHighWaterMark",{enumerable:!1,get:function(){return this._readableState.highWaterMark}}),Object.defineProperty(R.prototype,"readableBuffer",{enumerable:!1,get:function(){return this._readableState&&this._readableState.buffer}}),Object.defineProperty(R.prototype,"readableFlowing",{enumerable:!1,get:function(){return this._readableState.flowing},set:function(t){this._readableState&&(this._readableState.flowing=t)}}),R._fromList=G,Object.defineProperty(R.prototype,"readableLength",{enumerable:!1,get:function(){return this._readableState.length}}),"function"===typeof Symbol&&(R.from=function(t,e){return void 0===y&&(y=r("2527")),y(R,t,e)})}).call(this,r("c8ba"),r("4362"))},"86c6":function(t,e,r){"use strict";var n=r("9bfc").codes.ERR_INVALID_OPT_VALUE;function i(t,e,r){return null!=t.highWaterMark?t.highWaterMark:e?t[r]:null}function o(t,e,r,o){var s=i(e,o,r);if(null!=s){if(!isFinite(s)||Math.floor(s)!==s||s<0){var a=o?r:"highWaterMark";throw new n(a,s)}return Math.floor(s)}return t.objectMode?16:16384}t.exports={getHighWaterMark:o}},8707:function(t,e,r){
/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */
var n=r("b639"),i=n.Buffer;function o(t,e){for(var r in t)e[r]=t[r]}function s(t,e,r){return i(t,e,r)}i.from&&i.alloc&&i.allocUnsafe&&i.allocUnsafeSlow?t.exports=n:(o(n,e),e.Buffer=s),s.prototype=Object.create(i.prototype),o(i,s),s.from=function(t,e,r){if("number"===typeof t)throw new TypeError("Argument must not be a number");return i(t,e,r)},s.alloc=function(t,e,r){if("number"!==typeof t)throw new TypeError("Argument must be a number");var n=i(t);return void 0!==e?"string"===typeof r?n.fill(e,r):n.fill(e):n.fill(0),n},s.allocUnsafe=function(t){if("number"!==typeof t)throw new TypeError("Argument must be a number");return i(t)},s.allocUnsafeSlow=function(t){if("number"!==typeof t)throw new TypeError("Argument must be a number");return n.SlowBuffer(t)}},"8cef":function(t,e,r){(function(e,n,i){t.exports=n(r("21bf"),r("38ba"))})(0,(function(t){return t.pad.Iso97971={pad:function(e,r){e.concat(t.lib.WordArray.create([2147483648],1)),t.pad.ZeroPadding.pad(e,r)},unpad:function(e){t.pad.ZeroPadding.unpad(e),e.sigBytes--}},t.pad.Iso97971}))},"94f8":function(t,e,r){(function(e,n){t.exports=n(r("21bf"))})(0,(function(t){return function(e){var r=t,n=r.lib,i=n.WordArray,o=n.Hasher,s=r.algo,a=[],c=[];(function(){function t(t){for(var r=e.sqrt(t),n=2;n<=r;n++)if(!(t%n))return!1;return!0}function r(t){return 4294967296*(t-(0|t))|0}var n=2,i=0;while(i<64)t(n)&&(i<8&&(a[i]=r(e.pow(n,.5))),c[i]=r(e.pow(n,1/3)),i++),n++})();var u=[],l=s.SHA256=o.extend({_doReset:function(){this._hash=new i.init(a.slice(0))},_doProcessBlock:function(t,e){for(var r=this._hash.words,n=r[0],i=r[1],o=r[2],s=r[3],a=r[4],l=r[5],h=r[6],f=r[7],p=0;p<64;p++){if(p<16)u[p]=0|t[e+p];else{var d=u[p-15],y=(d<<25|d>>>7)^(d<<14|d>>>18)^d>>>3,b=u[p-2],g=(b<<15|b>>>17)^(b<<13|b>>>19)^b>>>10;u[p]=y+u[p-7]+g+u[p-16]}var m=a&l^~a&h,v=n&i^n&o^i&o,_=(n<<30|n>>>2)^(n<<19|n>>>13)^(n<<10|n>>>22),w=(a<<26|a>>>6)^(a<<21|a>>>11)^(a<<7|a>>>25),S=f+w+m+c[p]+u[p],E=_+v;f=h,h=l,l=a,a=s+S|0,s=o,o=i,i=n,n=S+E|0}r[0]=r[0]+n|0,r[1]=r[1]+i|0,r[2]=r[2]+o|0,r[3]=r[3]+s|0,r[4]=r[4]+a|0,r[5]=r[5]+l|0,r[6]=r[6]+h|0,r[7]=r[7]+f|0},_doFinalize:function(){var t=this._data,r=t.words,n=8*this._nDataBytes,i=8*t.sigBytes;return r[i>>>5]|=128<<24-i%32,r[14+(i+64>>>9<<4)]=e.floor(n/4294967296),r[15+(i+64>>>9<<4)]=n,t.sigBytes=4*r.length,this._process(),this._hash},clone:function(){var t=o.clone.call(this);return t._hash=this._hash.clone(),t}});r.SHA256=o._createHelper(l),r.HmacSHA256=o._createHmacHelper(l)}(Math),t.SHA256}))},"966d":function(t,e,r){"use strict";(function(e){function r(t,r,n,i){if("function"!==typeof t)throw new TypeError('"callback" argument must be a function');var o,s,a=arguments.length;switch(a){case 0:case 1:return e.nextTick(t);case 2:return e.nextTick((function(){t.call(null,r)}));case 3:return e.nextTick((function(){t.call(null,r,n)}));case 4:return e.nextTick((function(){t.call(null,r,n,i)}));default:o=new Array(a-1),s=0;while(s<o.length)o[s++]=arguments[s];return e.nextTick((function(){t.apply(null,o)}))}}"undefined"===typeof e||!e.version||0===e.version.indexOf("v0.")||0===e.version.indexOf("v1.")&&0!==e.version.indexOf("v1.8.")?t.exports={nextTick:r}:t.exports=e}).call(this,r("4362"))},9671:function(t,e,r){"use strict";var n=Function.prototype.call,i=Object.prototype.hasOwnProperty,o=r("0f7c");t.exports=o.call(n,i)},"9bfc":function(t,e,r){"use strict";function n(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.__proto__=e}var i={};function o(t,e,r){function o(t,r,n){return"string"===typeof e?e:e(t,r,n)}r||(r=Error);var s=function(t){function e(e,r,n){return t.call(this,o(e,r,n))||this}return n(e,t),e}(r);s.prototype.name=r.name,s.prototype.code=t,i[t]=s}function s(t,e){if(Array.isArray(t)){var r=t.length;return t=t.map((function(t){return String(t)})),r>2?"one of ".concat(e," ").concat(t.slice(0,r-1).join(", "),", or ")+t[r-1]:2===r?"one of ".concat(e," ").concat(t[0]," or ").concat(t[1]):"of ".concat(e," ").concat(t[0])}return"of ".concat(e," ").concat(String(t))}function a(t,e,r){return t.substr(!r||r<0?0:+r,e.length)===e}function c(t,e,r){return(void 0===r||r>t.length)&&(r=t.length),t.substring(r-e.length,r)===e}function u(t,e,r){return"number"!==typeof r&&(r=0),!(r+e.length>t.length)&&-1!==t.indexOf(e,r)}o("ERR_INVALID_OPT_VALUE",(function(t,e){return'The value "'+e+'" is invalid for option "'+t+'"'}),TypeError),o("ERR_INVALID_ARG_TYPE",(function(t,e,r){var n,i;if("string"===typeof e&&a(e,"not ")?(n="must not be",e=e.replace(/^not /,"")):n="must be",c(t," argument"))i="The ".concat(t," ").concat(n," ").concat(s(e,"type"));else{var o=u(t,".")?"property":"argument";i='The "'.concat(t,'" ').concat(o," ").concat(n," ").concat(s(e,"type"))}return i+=". Received type ".concat(typeof r),i}),TypeError),o("ERR_STREAM_PUSH_AFTER_EOF","stream.push() after EOF"),o("ERR_METHOD_NOT_IMPLEMENTED",(function(t){return"The "+t+" method is not implemented"})),o("ERR_STREAM_PREMATURE_CLOSE","Premature close"),o("ERR_STREAM_DESTROYED",(function(t){return"Cannot call "+t+" after a stream was destroyed"})),o("ERR_MULTIPLE_CALLBACK","Callback called multiple times"),o("ERR_STREAM_CANNOT_PIPE","Cannot pipe, not readable"),o("ERR_STREAM_WRITE_AFTER_END","write after end"),o("ERR_STREAM_NULL_VALUES","May not write null values to stream",TypeError),o("ERR_UNKNOWN_ENCODING",(function(t){return"Unknown encoding: "+t}),TypeError),o("ERR_STREAM_UNSHIFT_AFTER_END_EVENT","stream.unshift() after end event"),t.exports.codes=i},"9d37":function(t,e,r){"use strict";t.exports=i;var n=r("fe34");function i(t){if(!(this instanceof i))return new i(t);n.call(this,t)}r("3fb5")(i,n),i.prototype._transform=function(t,e,r){r(null,t)}},"9ede":function(t,e,r){t.exports=r("faa1").EventEmitter},a11b:function(t,e,r){(function(e,n,i){t.exports=n(r("21bf"),r("38ba"))})(0,(function(t){return t.pad.Iso10126={pad:function(e,r){var n=4*r,i=n-e.sigBytes%n;e.concat(t.lib.WordArray.random(i-1)).concat(t.lib.WordArray.create([i<<24],1))},unpad:function(t){var e=255&t.words[t.sigBytes-1>>>2];t.sigBytes-=e}},t.pad.Iso10126}))},a29f:function(t,e,r){"use strict";var n=r("bbc7"),i=Object.prototype.hasOwnProperty,o=Array.isArray,s=function(){for(var t=[],e=0;e<256;++e)t.push("%"+((e<16?"0":"")+e.toString(16)).toUpperCase());return t}(),a=function(t){while(t.length>1){var e=t.pop(),r=e.obj[e.prop];if(o(r)){for(var n=[],i=0;i<r.length;++i)"undefined"!==typeof r[i]&&n.push(r[i]);e.obj[e.prop]=n}}},c=function(t,e){for(var r=e&&e.plainObjects?Object.create(null):{},n=0;n<t.length;++n)"undefined"!==typeof t[n]&&(r[n]=t[n]);return r},u=function t(e,r,n){if(!r)return e;if("object"!==typeof r){if(o(e))e.push(r);else{if(!e||"object"!==typeof e)return[e,r];(n&&(n.plainObjects||n.allowPrototypes)||!i.call(Object.prototype,r))&&(e[r]=!0)}return e}if(!e||"object"!==typeof e)return[e].concat(r);var s=e;return o(e)&&!o(r)&&(s=c(e,n)),o(e)&&o(r)?(r.forEach((function(r,o){if(i.call(e,o)){var s=e[o];s&&"object"===typeof s&&r&&"object"===typeof r?e[o]=t(s,r,n):e.push(r)}else e[o]=r})),e):Object.keys(r).reduce((function(e,o){var s=r[o];return i.call(e,o)?e[o]=t(e[o],s,n):e[o]=s,e}),s)},l=function(t,e){return Object.keys(e).reduce((function(t,r){return t[r]=e[r],t}),t)},h=function(t,e,r){var n=t.replace(/\+/g," ");if("iso-8859-1"===r)return n.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(n)}catch(i){return n}},f=function(t,e,r,i,o){if(0===t.length)return t;var a=t;if("symbol"===typeof t?a=Symbol.prototype.toString.call(t):"string"!==typeof t&&(a=String(t)),"iso-8859-1"===r)return escape(a).replace(/%u[0-9a-f]{4}/gi,(function(t){return"%26%23"+parseInt(t.slice(2),16)+"%3B"}));for(var c="",u=0;u<a.length;++u){var l=a.charCodeAt(u);45===l||46===l||95===l||126===l||l>=48&&l<=57||l>=65&&l<=90||l>=97&&l<=122||o===n.RFC1738&&(40===l||41===l)?c+=a.charAt(u):l<128?c+=s[l]:l<2048?c+=s[192|l>>6]+s[128|63&l]:l<55296||l>=57344?c+=s[224|l>>12]+s[128|l>>6&63]+s[128|63&l]:(u+=1,l=65536+((1023&l)<<10|1023&a.charCodeAt(u)),c+=s[240|l>>18]+s[128|l>>12&63]+s[128|l>>6&63]+s[128|63&l])}return c},p=function(t){for(var e=[{obj:{o:t},prop:"o"}],r=[],n=0;n<e.length;++n)for(var i=e[n],o=i.obj[i.prop],s=Object.keys(o),c=0;c<s.length;++c){var u=s[c],l=o[u];"object"===typeof l&&null!==l&&-1===r.indexOf(l)&&(e.push({obj:o,prop:u}),r.push(l))}return a(e),t},d=function(t){return"[object RegExp]"===Object.prototype.toString.call(t)},y=function(t){return!(!t||"object"!==typeof t)&&!!(t.constructor&&t.constructor.isBuffer&&t.constructor.isBuffer(t))},b=function(t,e){return[].concat(t,e)},g=function(t,e){if(o(t)){for(var r=[],n=0;n<t.length;n+=1)r.push(e(t[n]));return r}return e(t)};t.exports={arrayToObject:c,assign:l,combine:b,compact:p,decode:h,encode:f,isBuffer:y,isRegExp:d,maybeMap:g,merge:u}},a40e:function(t,e,r){(function(e,n,i){t.exports=n(r("21bf"),r("1132"),r("72fe"),r("2b79"),r("38ba"))})(0,(function(t){return function(){var e=t,r=e.lib,n=r.WordArray,i=r.BlockCipher,o=e.algo,s=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],a=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],c=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],u=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],l=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],h=o.DES=i.extend({_doReset:function(){for(var t=this._key,e=t.words,r=[],n=0;n<56;n++){var i=s[n]-1;r[n]=e[i>>>5]>>>31-i%32&1}for(var o=this._subKeys=[],u=0;u<16;u++){var l=o[u]=[],h=c[u];for(n=0;n<24;n++)l[n/6|0]|=r[(a[n]-1+h)%28]<<31-n%6,l[4+(n/6|0)]|=r[28+(a[n+24]-1+h)%28]<<31-n%6;l[0]=l[0]<<1|l[0]>>>31;for(n=1;n<7;n++)l[n]=l[n]>>>4*(n-1)+3;l[7]=l[7]<<5|l[7]>>>27}var f=this._invSubKeys=[];for(n=0;n<16;n++)f[n]=o[15-n]},encryptBlock:function(t,e){this._doCryptBlock(t,e,this._subKeys)},decryptBlock:function(t,e){this._doCryptBlock(t,e,this._invSubKeys)},_doCryptBlock:function(t,e,r){this._lBlock=t[e],this._rBlock=t[e+1],f.call(this,4,252645135),f.call(this,16,65535),p.call(this,2,858993459),p.call(this,8,16711935),f.call(this,1,1431655765);for(var n=0;n<16;n++){for(var i=r[n],o=this._lBlock,s=this._rBlock,a=0,c=0;c<8;c++)a|=u[c][((s^i[c])&l[c])>>>0];this._lBlock=s,this._rBlock=o^a}var h=this._lBlock;this._lBlock=this._rBlock,this._rBlock=h,f.call(this,1,1431655765),p.call(this,8,16711935),p.call(this,2,858993459),f.call(this,16,65535),f.call(this,4,252645135),t[e]=this._lBlock,t[e+1]=this._rBlock},keySize:2,ivSize:2,blockSize:2});function f(t,e){var r=(this._lBlock>>>t^this._rBlock)&e;this._rBlock^=r,this._lBlock^=r<<t}function p(t,e){var r=(this._rBlock>>>t^this._lBlock)&e;this._lBlock^=r,this._rBlock^=r<<t}e.DES=i._createHelper(h);var d=o.TripleDES=i.extend({_doReset:function(){var t=this._key,e=t.words;if(2!==e.length&&4!==e.length&&e.length<6)throw new Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");var r=e.slice(0,2),i=e.length<4?e.slice(0,2):e.slice(2,4),o=e.length<6?e.slice(0,2):e.slice(4,6);this._des1=h.createEncryptor(n.create(r)),this._des2=h.createEncryptor(n.create(i)),this._des3=h.createEncryptor(n.create(o))},encryptBlock:function(t,e){this._des1.encryptBlock(t,e),this._des2.decryptBlock(t,e),this._des3.encryptBlock(t,e)},decryptBlock:function(t,e){this._des3.decryptBlock(t,e),this._des2.encryptBlock(t,e),this._des1.decryptBlock(t,e)},keySize:6,ivSize:2,blockSize:2});e.TripleDES=i._createHelper(d)}(),t.TripleDES}))},a43f:function(t,e,r){"use strict";const n=r("63f0"),i=r("6155").NumberAllocator;function o(t){if(!(this instanceof o))return new o(t);t>0&&(this.aliasToTopic=new n({max:t}),this.topicToAlias={},this.numberAllocator=new i(1,t),this.max=t,this.length=0)}o.prototype.put=function(t,e){if(0===e||e>this.max)return!1;const r=this.aliasToTopic.get(e);return r&&delete this.topicToAlias[r],this.aliasToTopic.set(e,t),this.topicToAlias[t]=e,this.numberAllocator.use(e),this.length=this.aliasToTopic.length,!0},o.prototype.getTopicByAlias=function(t){return this.aliasToTopic.get(t)},o.prototype.getAliasByTopic=function(t){const e=this.topicToAlias[t];return"undefined"!==typeof e&&this.aliasToTopic.get(e),e},o.prototype.clear=function(){this.aliasToTopic.reset(),this.topicToAlias={},this.numberAllocator.clear(),this.length=0},o.prototype.getLruAlias=function(){const t=this.numberAllocator.firstVacant();return t||this.aliasToTopic.keys()[this.aliasToTopic.length-1]},t.exports=o},a493:function(t,e,r){"use strict";(function(e){var n=Object.keys||function(t){var e=[];for(var r in t)e.push(r);return e};t.exports=u;var i=r("0e8b"),o=r("f6ba");r("3fb5")(u,i);for(var s=n(o.prototype),a=0;a<s.length;a++){var c=s[a];u.prototype[c]||(u.prototype[c]=o.prototype[c])}function u(t){if(!(this instanceof u))return new u(t);i.call(this,t),o.call(this,t),this.allowHalfOpen=!0,t&&(!1===t.readable&&(this.readable=!1),!1===t.writable&&(this.writable=!1),!1===t.allowHalfOpen&&(this.allowHalfOpen=!1,this.once("end",l)))}function l(){this._writableState.ended||e.nextTick(h,this)}function h(t){t.end()}Object.defineProperty(u.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),Object.defineProperty(u.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}}),Object.defineProperty(u.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}}),Object.defineProperty(u.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed&&this._writableState.destroyed)},set:function(t){void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed=t,this._writableState.destroyed=t)}})}).call(this,r("4362"))},a50f:function(t,e){t.exports=function(){throw new Error("Readable.from is not available in the browser")}},a7c9:function(t,e){class r{constructor(){this.cmd=null,this.retain=!1,this.qos=0,this.dup=!1,this.length=-1,this.topic=null,this.payload=null}}t.exports=r},a817:function(t,e,r){(function(e,n,i){t.exports=n(r("21bf"),r("38ba"))})(0,(function(t){return t.pad.AnsiX923={pad:function(t,e){var r=t.sigBytes,n=4*e,i=n-r%n,o=r+i-1;t.clamp(),t.words[o>>>2]|=i<<24-o%4*8,t.sigBytes+=i},unpad:function(t){var e=255&t.words[t.sigBytes-1>>>2];t.sigBytes-=e}},t.pad.Ansix923}))},a8ce:function(t,e,r){(function(e,n){t.exports=n(r("21bf"))})(0,(function(t){return function(){var e=t,r=e.lib,n=r.WordArray,i=e.enc;i.Utf16=i.Utf16BE={stringify:function(t){for(var e=t.words,r=t.sigBytes,n=[],i=0;i<r;i+=2){var o=e[i>>>2]>>>16-i%4*8&65535;n.push(String.fromCharCode(o))}return n.join("")},parse:function(t){for(var e=t.length,r=[],i=0;i<e;i++)r[i>>>1]|=t.charCodeAt(i)<<16-i%2*16;return n.create(r,2*e)}};function o(t){return t<<8&4278255360|t>>>8&16711935}i.Utf16LE={stringify:function(t){for(var e=t.words,r=t.sigBytes,n=[],i=0;i<r;i+=2){var s=o(e[i>>>2]>>>16-i%4*8&65535);n.push(String.fromCharCode(s))}return n.join("")},parse:function(t){for(var e=t.length,r=[],i=0;i<e;i++)r[i>>>1]|=o(t.charCodeAt(i)<<16-i%2*16);return n.create(r,2*e)}}}(),t.enc.Utf16}))},a9b9:function(t,e,r){"use strict";t.exports=function(){throw new Error("ws does not work in the browser. Browser clients must use the native WebSocket object")}},aa22:function(t,e,r){"use strict";var n=r("8707").Buffer,i=n.isEncoding||function(t){switch(t=""+t,t&&t.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function o(t){if(!t)return"utf8";var e;while(1)switch(t){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return t;default:if(e)return;t=(""+t).toLowerCase(),e=!0}}function s(t){var e=o(t);if("string"!==typeof e&&(n.isEncoding===i||!i(t)))throw new Error("Unknown encoding: "+t);return e||t}function a(t){var e;switch(this.encoding=s(t),this.encoding){case"utf16le":this.text=d,this.end=y,e=4;break;case"utf8":this.fillLast=h,e=4;break;case"base64":this.text=b,this.end=g,e=3;break;default:return this.write=m,void(this.end=v)}this.lastNeed=0,this.lastTotal=0,this.lastChar=n.allocUnsafe(e)}function c(t){return t<=127?0:t>>5===6?2:t>>4===14?3:t>>3===30?4:t>>6===2?-1:-2}function u(t,e,r){var n=e.length-1;if(n<r)return 0;var i=c(e[n]);return i>=0?(i>0&&(t.lastNeed=i-1),i):--n<r||-2===i?0:(i=c(e[n]),i>=0?(i>0&&(t.lastNeed=i-2),i):--n<r||-2===i?0:(i=c(e[n]),i>=0?(i>0&&(2===i?i=0:t.lastNeed=i-3),i):0))}function l(t,e,r){if(128!==(192&e[0]))return t.lastNeed=0,"�";if(t.lastNeed>1&&e.length>1){if(128!==(192&e[1]))return t.lastNeed=1,"�";if(t.lastNeed>2&&e.length>2&&128!==(192&e[2]))return t.lastNeed=2,"�"}}function h(t){var e=this.lastTotal-this.lastNeed,r=l(this,t,e);return void 0!==r?r:this.lastNeed<=t.length?(t.copy(this.lastChar,e,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):(t.copy(this.lastChar,e,0,t.length),void(this.lastNeed-=t.length))}function f(t,e){var r=u(this,t,e);if(!this.lastNeed)return t.toString("utf8",e);this.lastTotal=r;var n=t.length-(r-this.lastNeed);return t.copy(this.lastChar,0,n),t.toString("utf8",e,n)}function p(t){var e=t&&t.length?this.write(t):"";return this.lastNeed?e+"�":e}function d(t,e){if((t.length-e)%2===0){var r=t.toString("utf16le",e);if(r){var n=r.charCodeAt(r.length-1);if(n>=55296&&n<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=t[t.length-2],this.lastChar[1]=t[t.length-1],r.slice(0,-1)}return r}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=t[t.length-1],t.toString("utf16le",e,t.length-1)}function y(t){var e=t&&t.length?this.write(t):"";if(this.lastNeed){var r=this.lastTotal-this.lastNeed;return e+this.lastChar.toString("utf16le",0,r)}return e}function b(t,e){var r=(t.length-e)%3;return 0===r?t.toString("base64",e):(this.lastNeed=3-r,this.lastTotal=3,1===r?this.lastChar[0]=t[t.length-1]:(this.lastChar[0]=t[t.length-2],this.lastChar[1]=t[t.length-1]),t.toString("base64",e,t.length-r))}function g(t){var e=t&&t.length?this.write(t):"";return this.lastNeed?e+this.lastChar.toString("base64",0,3-this.lastNeed):e}function m(t){return t.toString(this.encoding)}function v(t){return t&&t.length?this.write(t):""}e.StringDecoder=a,a.prototype.write=function(t){if(0===t.length)return"";var e,r;if(this.lastNeed){if(e=this.fillLast(t),void 0===e)return"";r=this.lastNeed,this.lastNeed=0}else r=0;return r<t.length?e?e+this.text(t,r):this.text(t,r):e||""},a.prototype.end=p,a.prototype.text=f,a.prototype.fillLast=function(t){if(this.lastNeed<=t.length)return t.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);t.copy(this.lastChar,this.lastTotal-this.lastNeed,0,t.length),this.lastNeed-=t.length}},aaef:function(t,e,r){(function(e,n,i){t.exports=n(r("21bf"),r("38ba"))})(0,(function(t){
/** @preserve
	 * Counter block mode compatible with  Dr Brian Gladman fileenc.c
	 * derived from CryptoJS.mode.CTR
	 * <NAME_EMAIL>
	 */
return t.mode.CTRGladman=function(){var e=t.lib.BlockCipherMode.extend();function r(t){if(255===(t>>24&255)){var e=t>>16&255,r=t>>8&255,n=255&t;255===e?(e=0,255===r?(r=0,255===n?n=0:++n):++r):++e,t=0,t+=e<<16,t+=r<<8,t+=n}else t+=1<<24;return t}function n(t){return 0===(t[0]=r(t[0]))&&(t[1]=r(t[1])),t}var i=e.Encryptor=e.extend({processBlock:function(t,e){var r=this._cipher,i=r.blockSize,o=this._iv,s=this._counter;o&&(s=this._counter=o.slice(0),this._iv=void 0),n(s);var a=s.slice(0);r.encryptBlock(a,0);for(var c=0;c<i;c++)t[e+c]^=a[c]}});return e.Decryptor=i,e}(),t.mode.CTRGladman}))},ab52:function(t,e,r){(function(e){var n=r("566b"),i=function(){},o=function(t){return t.setHeader&&"function"===typeof t.abort},s=function(t){return t.stdio&&Array.isArray(t.stdio)&&3===t.stdio.length},a=function(t,r,c){if("function"===typeof r)return a(t,null,r);r||(r={}),c=n(c||i);var u=t._writableState,l=t._readableState,h=r.readable||!1!==r.readable&&t.readable,f=r.writable||!1!==r.writable&&t.writable,p=!1,d=function(){t.writable||y()},y=function(){f=!1,h||c.call(t)},b=function(){h=!1,f||c.call(t)},g=function(e){c.call(t,e?new Error("exited with error code: "+e):null)},m=function(e){c.call(t,e)},v=function(){e.nextTick(_)},_=function(){if(!p)return(!h||l&&l.ended&&!l.destroyed)&&(!f||u&&u.ended&&!u.destroyed)?void 0:c.call(t,new Error("premature close"))},w=function(){t.req.on("finish",y)};return o(t)?(t.on("complete",y),t.on("abort",v),t.req?w():t.on("request",w)):f&&!u&&(t.on("end",d),t.on("close",d)),s(t)&&t.on("exit",g),t.on("end",b),t.on("finish",y),!1!==r.error&&t.on("error",m),t.on("close",v),function(){p=!0,t.removeListener("complete",y),t.removeListener("abort",v),t.removeListener("request",w),t.req&&t.req.removeListener("finish",y),t.removeListener("end",d),t.removeListener("close",d),t.removeListener("finish",y),t.removeListener("exit",g),t.removeListener("end",b),t.removeListener("error",m),t.removeListener("close",v)}};t.exports=a}).call(this,r("4362"))},ae84:function(t,e,r){"use strict";function n(t){const e=t.split("/");for(let r=0;r<e.length;r++)if("+"!==e[r]){if("#"===e[r])return r===e.length-1;if(-1!==e[r].indexOf("+")||-1!==e[r].indexOf("#"))return!1}return!0}function i(t){if(0===t.length)return"empty_topic_list";for(let e=0;e<t.length;e++)if(!n(t[e]))return t[e];return null}t.exports={validateTopics:i}},af5b:function(t,e,r){(function(e,n,i){t.exports=n(r("21bf"),r("1132"),r("72fe"),r("2b79"),r("38ba"))})(0,(function(t){return function(){var e=t,r=e.lib,n=r.BlockCipher,i=e.algo;const o=16,s=[608135816,2242054355,320440878,57701188,2752067618,698298832,137296536,3964562569,1160258022,953160567,3193202383,887688300,3232508343,3380367581,1065670069,3041331479,2450970073,2306472731],a=[[3509652390,2564797868,805139163,3491422135,3101798381,1780907670,3128725573,4046225305,614570311,3012652279,134345442,2240740374,1667834072,1901547113,2757295779,4103290238,227898511,1921955416,1904987480,2182433518,2069144605,3260701109,2620446009,720527379,3318853667,677414384,3393288472,3101374703,2390351024,1614419982,1822297739,2954791486,3608508353,3174124327,2024746970,1432378464,3864339955,2857741204,1464375394,1676153920,1439316330,715854006,3033291828,289532110,2706671279,2087905683,3018724369,1668267050,732546397,1947742710,3462151702,2609353502,2950085171,1814351708,2050118529,680887927,999245976,1800124847,3300911131,1713906067,1641548236,4213287313,1216130144,1575780402,4018429277,3917837745,3693486850,3949271944,596196993,3549867205,258830323,2213823033,772490370,2760122372,1774776394,2652871518,566650946,4142492826,1728879713,2882767088,1783734482,3629395816,2517608232,2874225571,1861159788,326777828,3124490320,2130389656,2716951837,967770486,1724537150,2185432712,2364442137,1164943284,2105845187,998989502,3765401048,2244026483,1075463327,1455516326,1322494562,910128902,469688178,1117454909,936433444,3490320968,3675253459,1240580251,122909385,2157517691,634681816,4142456567,3825094682,3061402683,2540495037,79693498,3249098678,1084186820,1583128258,426386531,1761308591,1047286709,322548459,995290223,1845252383,2603652396,3431023940,2942221577,3202600964,3727903485,1712269319,422464435,3234572375,1170764815,3523960633,3117677531,1434042557,442511882,3600875718,1076654713,1738483198,4213154764,2393238008,3677496056,1014306527,4251020053,793779912,2902807211,842905082,4246964064,1395751752,1040244610,2656851899,3396308128,445077038,3742853595,3577915638,679411651,2892444358,2354009459,1767581616,3150600392,3791627101,3102740896,284835224,4246832056,1258075500,768725851,2589189241,3069724005,3532540348,1274779536,3789419226,2764799539,1660621633,3471099624,4011903706,913787905,3497959166,737222580,2514213453,2928710040,3937242737,1804850592,3499020752,2949064160,2386320175,2390070455,2415321851,4061277028,2290661394,2416832540,1336762016,1754252060,3520065937,3014181293,791618072,3188594551,3933548030,2332172193,3852520463,3043980520,413987798,3465142937,3030929376,4245938359,2093235073,3534596313,375366246,2157278981,2479649556,555357303,3870105701,2008414854,3344188149,4221384143,3956125452,2067696032,3594591187,2921233993,2428461,544322398,577241275,1471733935,610547355,4027169054,1432588573,1507829418,2025931657,3646575487,545086370,48609733,2200306550,1653985193,298326376,1316178497,3007786442,2064951626,458293330,2589141269,3591329599,3164325604,727753846,2179363840,146436021,1461446943,4069977195,705550613,3059967265,3887724982,4281599278,3313849956,1404054877,2845806497,146425753,1854211946],[1266315497,3048417604,3681880366,3289982499,290971e4,1235738493,2632868024,2414719590,3970600049,1771706367,1449415276,3266420449,422970021,1963543593,2690192192,3826793022,1062508698,1531092325,1804592342,2583117782,2714934279,4024971509,1294809318,4028980673,1289560198,2221992742,1669523910,35572830,157838143,1052438473,1016535060,1802137761,1753167236,1386275462,3080475397,2857371447,1040679964,2145300060,2390574316,1461121720,2956646967,4031777805,4028374788,33600511,2920084762,1018524850,629373528,3691585981,3515945977,2091462646,2486323059,586499841,988145025,935516892,3367335476,2599673255,2839830854,265290510,3972581182,2759138881,3795373465,1005194799,847297441,406762289,1314163512,1332590856,1866599683,4127851711,750260880,613907577,1450815602,3165620655,3734664991,3650291728,3012275730,3704569646,1427272223,778793252,1343938022,2676280711,2052605720,1946737175,3164576444,3914038668,3967478842,3682934266,1661551462,3294938066,4011595847,840292616,3712170807,616741398,312560963,711312465,1351876610,322626781,1910503582,271666773,2175563734,1594956187,70604529,3617834859,1007753275,1495573769,4069517037,2549218298,2663038764,504708206,2263041392,3941167025,2249088522,1514023603,1998579484,1312622330,694541497,2582060303,2151582166,1382467621,776784248,2618340202,3323268794,2497899128,2784771155,503983604,4076293799,907881277,423175695,432175456,1378068232,4145222326,3954048622,3938656102,3820766613,2793130115,2977904593,26017576,3274890735,3194772133,1700274565,1756076034,4006520079,3677328699,720338349,1533947780,354530856,688349552,3973924725,1637815568,332179504,3949051286,53804574,2852348879,3044236432,1282449977,3583942155,3416972820,4006381244,1617046695,2628476075,3002303598,1686838959,431878346,2686675385,1700445008,1080580658,1009431731,832498133,3223435511,2605976345,2271191193,2516031870,1648197032,4164389018,2548247927,300782431,375919233,238389289,3353747414,2531188641,2019080857,1475708069,455242339,2609103871,448939670,3451063019,1395535956,2413381860,1841049896,1491858159,885456874,4264095073,4001119347,1565136089,3898914787,1108368660,540939232,1173283510,2745871338,3681308437,4207628240,3343053890,4016749493,1699691293,1103962373,3625875870,2256883143,3830138730,1031889488,3479347698,1535977030,4236805024,3251091107,2132092099,1774941330,1199868427,1452454533,157007616,2904115357,342012276,595725824,1480756522,206960106,497939518,591360097,863170706,2375253569,3596610801,1814182875,2094937945,3421402208,1082520231,3463918190,2785509508,435703966,3908032597,1641649973,2842273706,3305899714,1510255612,2148256476,2655287854,3276092548,4258621189,236887753,3681803219,274041037,1734335097,3815195456,3317970021,1899903192,1026095262,4050517792,356393447,2410691914,3873677099,3682840055],[3913112168,2491498743,4132185628,2489919796,1091903735,1979897079,3170134830,3567386728,3557303409,857797738,1136121015,1342202287,507115054,2535736646,337727348,3213592640,1301675037,2528481711,1895095763,1721773893,3216771564,62756741,2142006736,835421444,2531993523,1442658625,3659876326,2882144922,676362277,1392781812,170690266,3921047035,1759253602,3611846912,1745797284,664899054,1329594018,3901205900,3045908486,2062866102,2865634940,3543621612,3464012697,1080764994,553557557,3656615353,3996768171,991055499,499776247,1265440854,648242737,3940784050,980351604,3713745714,1749149687,3396870395,4211799374,3640570775,1161844396,3125318951,1431517754,545492359,4268468663,3499529547,1437099964,2702547544,3433638243,2581715763,2787789398,1060185593,1593081372,2418618748,4260947970,69676912,2159744348,86519011,2512459080,3838209314,1220612927,3339683548,133810670,1090789135,1078426020,1569222167,845107691,3583754449,4072456591,1091646820,628848692,1613405280,3757631651,526609435,236106946,48312990,2942717905,3402727701,1797494240,859738849,992217954,4005476642,2243076622,3870952857,3732016268,765654824,3490871365,2511836413,1685915746,3888969200,1414112111,2273134842,3281911079,4080962846,172450625,2569994100,980381355,4109958455,2819808352,2716589560,2568741196,3681446669,3329971472,1835478071,660984891,3704678404,4045999559,3422617507,3040415634,1762651403,1719377915,3470491036,2693910283,3642056355,3138596744,1364962596,2073328063,1983633131,926494387,3423689081,2150032023,4096667949,1749200295,3328846651,309677260,2016342300,1779581495,3079819751,111262694,1274766160,443224088,298511866,1025883608,3806446537,1145181785,168956806,3641502830,3584813610,1689216846,3666258015,3200248200,1692713982,2646376535,4042768518,1618508792,1610833997,3523052358,4130873264,2001055236,3610705100,2202168115,4028541809,2961195399,1006657119,2006996926,3186142756,1430667929,3210227297,1314452623,4074634658,4101304120,2273951170,1399257539,3367210612,3027628629,1190975929,2062231137,2333990788,2221543033,2438960610,1181637006,548689776,2362791313,3372408396,3104550113,3145860560,296247880,1970579870,3078560182,3769228297,1714227617,3291629107,3898220290,166772364,1251581989,493813264,448347421,195405023,2709975567,677966185,3703036547,1463355134,2715995803,1338867538,1343315457,2802222074,2684532164,233230375,2599980071,2000651841,3277868038,1638401717,4028070440,3237316320,6314154,819756386,300326615,590932579,1405279636,3267499572,3150704214,2428286686,3959192993,3461946742,1862657033,1266418056,963775037,2089974820,2263052895,1917689273,448879540,3550394620,3981727096,150775221,3627908307,1303187396,508620638,2975983352,2726630617,1817252668,1876281319,1457606340,908771278,3720792119,3617206836,2455994898,1729034894,1080033504],[976866871,3556439503,2881648439,1522871579,1555064734,1336096578,3548522304,2579274686,3574697629,3205460757,3593280638,3338716283,3079412587,564236357,2993598910,1781952180,1464380207,3163844217,3332601554,1699332808,1393555694,1183702653,3581086237,1288719814,691649499,2847557200,2895455976,3193889540,2717570544,1781354906,1676643554,2592534050,3230253752,1126444790,2770207658,2633158820,2210423226,2615765581,2414155088,3127139286,673620729,2805611233,1269405062,4015350505,3341807571,4149409754,1057255273,2012875353,2162469141,2276492801,2601117357,993977747,3918593370,2654263191,753973209,36408145,2530585658,25011837,3520020182,2088578344,530523599,2918365339,1524020338,1518925132,3760827505,3759777254,1202760957,3985898139,3906192525,674977740,4174734889,2031300136,2019492241,3983892565,4153806404,3822280332,352677332,2297720250,60907813,90501309,3286998549,1016092578,2535922412,2839152426,457141659,509813237,4120667899,652014361,1966332200,2975202805,55981186,2327461051,676427537,3255491064,2882294119,3433927263,1307055953,942726286,933058658,2468411793,3933900994,4215176142,1361170020,2001714738,2830558078,3274259782,1222529897,1679025792,2729314320,3714953764,1770335741,151462246,3013232138,1682292957,1483529935,471910574,1539241949,458788160,3436315007,1807016891,3718408830,978976581,1043663428,3165965781,1927990952,4200891579,2372276910,3208408903,3533431907,1412390302,2931980059,4132332400,1947078029,3881505623,4168226417,2941484381,1077988104,1320477388,886195818,18198404,3786409e3,2509781533,112762804,3463356488,1866414978,891333506,18488651,661792760,1628790961,3885187036,3141171499,876946877,2693282273,1372485963,791857591,2686433993,3759982718,3167212022,3472953795,2716379847,445679433,3561995674,3504004811,3574258232,54117162,3331405415,2381918588,3769707343,4154350007,1140177722,4074052095,668550556,3214352940,367459370,261225585,2610173221,4209349473,3468074219,3265815641,314222801,3066103646,3808782860,282218597,3406013506,3773591054,379116347,1285071038,846784868,2669647154,3771962079,3550491691,2305946142,453669953,1268987020,3317592352,3279303384,3744833421,2610507566,3859509063,266596637,3847019092,517658769,3462560207,3443424879,370717030,4247526661,2224018117,4143653529,4112773975,2788324899,2477274417,1456262402,2901442914,1517677493,1846949527,2295493580,3734397586,2176403920,1280348187,1908823572,3871786941,846861322,1172426758,3287448474,3383383037,1655181056,3139813346,901632758,1897031941,2986607138,3066810236,3447102507,1393639104,373351379,950779232,625454576,3124240540,4148612726,2007998917,544563296,2244738638,2330496472,2058025392,1291430526,424198748,50039436,29584100,3605783033,2429876329,2791104160,1057563949,3255363231,3075367218,3463963227,1469046755,985887462]];var c={pbox:[],sbox:[]};function u(t,e){let r=e>>24&255,n=e>>16&255,i=e>>8&255,o=255&e,s=t.sbox[0][r]+t.sbox[1][n];return s^=t.sbox[2][i],s+=t.sbox[3][o],s}function l(t,e,r){let n,i=e,s=r;for(let a=0;a<o;++a)i^=t.pbox[a],s=u(t,i)^s,n=i,i=s,s=n;return n=i,i=s,s=n,s^=t.pbox[o],i^=t.pbox[o+1],{left:i,right:s}}function h(t,e,r){let n,i=e,s=r;for(let a=o+1;a>1;--a)i^=t.pbox[a],s=u(t,i)^s,n=i,i=s,s=n;return n=i,i=s,s=n,s^=t.pbox[1],i^=t.pbox[0],{left:i,right:s}}function f(t,e,r){for(let o=0;o<4;o++){t.sbox[o]=[];for(let e=0;e<256;e++)t.sbox[o][e]=a[o][e]}let n=0;for(let a=0;a<o+2;a++)t.pbox[a]=s[a]^e[n],n++,n>=r&&(n=0);let i=0,c=0,u=0;for(let s=0;s<o+2;s+=2)u=l(t,i,c),i=u.left,c=u.right,t.pbox[s]=i,t.pbox[s+1]=c;for(let o=0;o<4;o++)for(let e=0;e<256;e+=2)u=l(t,i,c),i=u.left,c=u.right,t.sbox[o][e]=i,t.sbox[o][e+1]=c;return!0}var p=i.Blowfish=n.extend({_doReset:function(){if(this._keyPriorReset!==this._key){var t=this._keyPriorReset=this._key,e=t.words,r=t.sigBytes/4;f(c,e,r)}},encryptBlock:function(t,e){var r=l(c,t[e],t[e+1]);t[e]=r.left,t[e+1]=r.right},decryptBlock:function(t,e){var r=h(c,t[e],t[e+1]);t[e]=r.left,t[e+1]=r.right},blockSize:2,keySize:4,ivSize:2});e.Blowfish=n._createHelper(p)}(),t.Blowfish}))},aff9:function(t,e,r){"use strict";t.exports=function(t){t.prototype[Symbol.iterator]=function*(){for(let t=this.head;t;t=t.next)yield t.value}}},b289:function(t,e,r){(function(e){const r=t.exports;r.types={0:"reserved",1:"connect",2:"connack",3:"publish",4:"puback",5:"pubrec",6:"pubrel",7:"pubcomp",8:"subscribe",9:"suback",10:"unsubscribe",11:"unsuback",12:"pingreq",13:"pingresp",14:"disconnect",15:"auth"},r.codes={};for(const t in r.types){const e=r.types[t];r.codes[e]=t}r.CMD_SHIFT=4,r.CMD_MASK=240,r.DUP_MASK=8,r.QOS_MASK=3,r.QOS_SHIFT=1,r.RETAIN_MASK=1,r.VARBYTEINT_MASK=127,r.VARBYTEINT_FIN_MASK=128,r.VARBYTEINT_MAX=268435455,r.SESSIONPRESENT_MASK=1,r.SESSIONPRESENT_HEADER=e.from([r.SESSIONPRESENT_MASK]),r.CONNACK_HEADER=e.from([r.codes.connack<<r.CMD_SHIFT]),r.USERNAME_MASK=128,r.PASSWORD_MASK=64,r.WILL_RETAIN_MASK=32,r.WILL_QOS_MASK=24,r.WILL_QOS_SHIFT=3,r.WILL_FLAG_MASK=4,r.CLEAN_SESSION_MASK=2,r.CONNECT_HEADER=e.from([r.codes.connect<<r.CMD_SHIFT]),r.properties={sessionExpiryInterval:17,willDelayInterval:24,receiveMaximum:33,maximumPacketSize:39,topicAliasMaximum:34,requestResponseInformation:25,requestProblemInformation:23,userProperties:38,authenticationMethod:21,authenticationData:22,payloadFormatIndicator:1,messageExpiryInterval:2,contentType:3,responseTopic:8,correlationData:9,maximumQoS:36,retainAvailable:37,assignedClientIdentifier:18,reasonString:31,wildcardSubscriptionAvailable:40,subscriptionIdentifiersAvailable:41,sharedSubscriptionAvailable:42,serverKeepAlive:19,responseInformation:26,serverReference:28,topicAlias:35,subscriptionIdentifier:11},r.propertiesCodes={};for(const t in r.properties){const e=r.properties[t];r.propertiesCodes[e]=t}function n(t){return[0,1,2].map(n=>[0,1].map(i=>[0,1].map(o=>{const s=e.alloc(1);return s.writeUInt8(r.codes[t]<<r.CMD_SHIFT|(i?r.DUP_MASK:0)|n<<r.QOS_SHIFT|o,0,!0),s})))}r.propertiesTypes={sessionExpiryInterval:"int32",willDelayInterval:"int32",receiveMaximum:"int16",maximumPacketSize:"int32",topicAliasMaximum:"int16",requestResponseInformation:"byte",requestProblemInformation:"byte",userProperties:"pair",authenticationMethod:"string",authenticationData:"binary",payloadFormatIndicator:"byte",messageExpiryInterval:"int32",contentType:"string",responseTopic:"string",correlationData:"binary",maximumQoS:"int8",retainAvailable:"byte",assignedClientIdentifier:"string",reasonString:"string",wildcardSubscriptionAvailable:"byte",subscriptionIdentifiersAvailable:"byte",sharedSubscriptionAvailable:"byte",serverKeepAlive:"int16",responseInformation:"string",serverReference:"string",topicAlias:"int16",subscriptionIdentifier:"var"},r.PUBLISH_HEADER=n("publish"),r.SUBSCRIBE_HEADER=n("subscribe"),r.SUBSCRIBE_OPTIONS_QOS_MASK=3,r.SUBSCRIBE_OPTIONS_NL_MASK=1,r.SUBSCRIBE_OPTIONS_NL_SHIFT=2,r.SUBSCRIBE_OPTIONS_RAP_MASK=1,r.SUBSCRIBE_OPTIONS_RAP_SHIFT=3,r.SUBSCRIBE_OPTIONS_RH_MASK=3,r.SUBSCRIBE_OPTIONS_RH_SHIFT=4,r.SUBSCRIBE_OPTIONS_RH=[0,16,32],r.SUBSCRIBE_OPTIONS_NL=4,r.SUBSCRIBE_OPTIONS_RAP=8,r.SUBSCRIBE_OPTIONS_QOS=[0,1,2],r.UNSUBSCRIBE_HEADER=n("unsubscribe"),r.ACKS={unsuback:n("unsuback"),puback:n("puback"),pubcomp:n("pubcomp"),pubrel:n("pubrel"),pubrec:n("pubrec")},r.SUBACK_HEADER=e.from([r.codes.suback<<r.CMD_SHIFT]),r.VERSION3=e.from([3]),r.VERSION4=e.from([4]),r.VERSION5=e.from([5]),r.VERSION131=e.from([131]),r.VERSION132=e.from([132]),r.QOS=[0,1,2].map(t=>e.from([t])),r.EMPTY={pingreq:e.from([r.codes.pingreq<<4,0]),pingresp:e.from([r.codes.pingresp<<4,0]),disconnect:e.from([r.codes.disconnect<<4,0])}}).call(this,r("b639").Buffer)},b7d1:function(t,e,r){(function(e){function r(t,e){if(n("noDeprecation"))return t;var r=!1;function i(){if(!r){if(n("throwDeprecation"))throw new Error(e);n("traceDeprecation")?console.trace(e):console.warn(e),r=!0}return t.apply(this,arguments)}return i}function n(t){try{if(!e.localStorage)return!1}catch(n){return!1}var r=e.localStorage[t];return null!=r&&"true"===String(r).toLowerCase()}t.exports=r}).call(this,r("c8ba"))},b86b:function(t,e,r){(function(e,n,i){t.exports=n(r("21bf"),r("3252"),r("d6e6"))})(0,(function(t){return function(){var e=t,r=e.x64,n=r.Word,i=r.WordArray,o=e.algo,s=o.SHA512,a=o.SHA384=s.extend({_doReset:function(){this._hash=new i.init([new n.init(3418070365,3238371032),new n.init(1654270250,914150663),new n.init(2438529370,812702999),new n.init(355462360,4144912697),new n.init(1731405415,4290775857),new n.init(2394180231,1750603025),new n.init(3675008525,1694076839),new n.init(1203062813,3204075428)])},_doFinalize:function(){var t=s._doFinalize.call(this);return t.sigBytes-=16,t}});e.SHA384=s._createHelper(a),e.HmacSHA384=s._createHmacHelper(a)}(),t.SHA384}))},b86c:function(t,e,r){(function(e,n,i){t.exports=n(r("21bf"),r("38ba"))})(0,(function(t){return t.pad.NoPadding={pad:function(){},unpad:function(){}},t.pad.NoPadding}))},b98b:function(t,e,r){t.exports=r("faa1").EventEmitter},bbc7:function(t,e,r){"use strict";var n=String.prototype.replace,i=/%20/g,o={RFC1738:"RFC1738",RFC3986:"RFC3986"};t.exports={default:o.RFC3986,formatters:{RFC1738:function(t){return n.call(t,i,"+")},RFC3986:function(t){return String(t)}},RFC1738:o.RFC1738,RFC3986:o.RFC3986}},be3f:function(t,e,r){"use strict";(function(e){var n=Object.keys||function(t){var e=[];for(var r in t)e.push(r);return e};t.exports=u;var i=r("85f8"),o=r("13a8");r("3fb5")(u,i);for(var s=n(o.prototype),a=0;a<s.length;a++){var c=s[a];u.prototype[c]||(u.prototype[c]=o.prototype[c])}function u(t){if(!(this instanceof u))return new u(t);i.call(this,t),o.call(this,t),this.allowHalfOpen=!0,t&&(!1===t.readable&&(this.readable=!1),!1===t.writable&&(this.writable=!1),!1===t.allowHalfOpen&&(this.allowHalfOpen=!1,this.once("end",l)))}function l(){this._writableState.ended||e.nextTick(h,this)}function h(t){t.end()}Object.defineProperty(u.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),Object.defineProperty(u.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}}),Object.defineProperty(u.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}}),Object.defineProperty(u.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed&&this._writableState.destroyed)},set:function(t){void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed=t,this._writableState.destroyed=t)}})}).call(this,r("4362"))},bf09:function(t,e,r){"use strict";var n=r("9bfc").codes.ERR_STREAM_PREMATURE_CLOSE;function i(t){var e=!1;return function(){if(!e){e=!0;for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];t.apply(this,n)}}}function o(){}function s(t){return t.setHeader&&"function"===typeof t.abort}function a(t,e,r){if("function"===typeof e)return a(t,null,e);e||(e={}),r=i(r||o);var c=e.readable||!1!==e.readable&&t.readable,u=e.writable||!1!==e.writable&&t.writable,l=function(){t.writable||f()},h=t._writableState&&t._writableState.finished,f=function(){u=!1,h=!0,c||r.call(t)},p=t._readableState&&t._readableState.endEmitted,d=function(){c=!1,p=!0,u||r.call(t)},y=function(e){r.call(t,e)},b=function(){var e;return c&&!p?(t._readableState&&t._readableState.ended||(e=new n),r.call(t,e)):u&&!h?(t._writableState&&t._writableState.ended||(e=new n),r.call(t,e)):void 0},g=function(){t.req.on("finish",f)};return s(t)?(t.on("complete",f),t.on("abort",b),t.req?g():t.on("request",g)):u&&!t._writableState&&(t.on("end",l),t.on("close",l)),t.on("end",d),t.on("finish",f),!1!==e.error&&t.on("error",y),t.on("close",b),function(){t.removeListener("complete",f),t.removeListener("abort",b),t.removeListener("request",g),t.req&&t.req.removeListener("finish",f),t.removeListener("end",l),t.removeListener("close",l),t.removeListener("finish",f),t.removeListener("end",d),t.removeListener("error",y),t.removeListener("close",b)}}t.exports=a},c198:function(t,e,r){(function(e,n,i){t.exports=n(r("21bf"),r("1132"),r("72fe"),r("2b79"),r("38ba"))})(0,(function(t){return function(){var e=t,r=e.lib,n=r.BlockCipher,i=e.algo,o=[],s=[],a=[],c=[],u=[],l=[],h=[],f=[],p=[],d=[];(function(){for(var t=[],e=0;e<256;e++)t[e]=e<128?e<<1:e<<1^283;var r=0,n=0;for(e=0;e<256;e++){var i=n^n<<1^n<<2^n<<3^n<<4;i=i>>>8^255&i^99,o[r]=i,s[i]=r;var y=t[r],b=t[y],g=t[b],m=257*t[i]^16843008*i;a[r]=m<<24|m>>>8,c[r]=m<<16|m>>>16,u[r]=m<<8|m>>>24,l[r]=m;m=16843009*g^65537*b^257*y^16843008*r;h[i]=m<<24|m>>>8,f[i]=m<<16|m>>>16,p[i]=m<<8|m>>>24,d[i]=m,r?(r=y^t[t[t[g^y]]],n^=t[t[n]]):r=n=1}})();var y=[0,1,2,4,8,16,32,64,128,27,54],b=i.AES=n.extend({_doReset:function(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var t=this._keyPriorReset=this._key,e=t.words,r=t.sigBytes/4,n=this._nRounds=r+6,i=4*(n+1),s=this._keySchedule=[],a=0;a<i;a++)a<r?s[a]=e[a]:(l=s[a-1],a%r?r>6&&a%r==4&&(l=o[l>>>24]<<24|o[l>>>16&255]<<16|o[l>>>8&255]<<8|o[255&l]):(l=l<<8|l>>>24,l=o[l>>>24]<<24|o[l>>>16&255]<<16|o[l>>>8&255]<<8|o[255&l],l^=y[a/r|0]<<24),s[a]=s[a-r]^l);for(var c=this._invKeySchedule=[],u=0;u<i;u++){a=i-u;if(u%4)var l=s[a];else l=s[a-4];c[u]=u<4||a<=4?l:h[o[l>>>24]]^f[o[l>>>16&255]]^p[o[l>>>8&255]]^d[o[255&l]]}}},encryptBlock:function(t,e){this._doCryptBlock(t,e,this._keySchedule,a,c,u,l,o)},decryptBlock:function(t,e){var r=t[e+1];t[e+1]=t[e+3],t[e+3]=r,this._doCryptBlock(t,e,this._invKeySchedule,h,f,p,d,s);r=t[e+1];t[e+1]=t[e+3],t[e+3]=r},_doCryptBlock:function(t,e,r,n,i,o,s,a){for(var c=this._nRounds,u=t[e]^r[0],l=t[e+1]^r[1],h=t[e+2]^r[2],f=t[e+3]^r[3],p=4,d=1;d<c;d++){var y=n[u>>>24]^i[l>>>16&255]^o[h>>>8&255]^s[255&f]^r[p++],b=n[l>>>24]^i[h>>>16&255]^o[f>>>8&255]^s[255&u]^r[p++],g=n[h>>>24]^i[f>>>16&255]^o[u>>>8&255]^s[255&l]^r[p++],m=n[f>>>24]^i[u>>>16&255]^o[l>>>8&255]^s[255&h]^r[p++];u=y,l=b,h=g,f=m}y=(a[u>>>24]<<24|a[l>>>16&255]<<16|a[h>>>8&255]<<8|a[255&f])^r[p++],b=(a[l>>>24]<<24|a[h>>>16&255]<<16|a[f>>>8&255]<<8|a[255&u])^r[p++],g=(a[h>>>24]<<24|a[f>>>16&255]<<16|a[u>>>8&255]<<8|a[255&l])^r[p++],m=(a[f>>>24]<<24|a[u>>>16&255]<<16|a[l>>>8&255]<<8|a[255&h])^r[p++];t[e]=y,t[e+1]=b,t[e+2]=g,t[e+3]=m},keySize:8});e.AES=n._createHelper(b)}(),t.AES}))},c1bc:function(t,e,r){(function(e,n){t.exports=n(r("21bf"))})(0,(function(t){return function(){var e=t,r=e.lib,n=r.WordArray,i=e.enc;i.Base64url={stringify:function(t,e){void 0===e&&(e=!0);var r=t.words,n=t.sigBytes,i=e?this._safe_map:this._map;t.clamp();for(var o=[],s=0;s<n;s+=3)for(var a=r[s>>>2]>>>24-s%4*8&255,c=r[s+1>>>2]>>>24-(s+1)%4*8&255,u=r[s+2>>>2]>>>24-(s+2)%4*8&255,l=a<<16|c<<8|u,h=0;h<4&&s+.75*h<n;h++)o.push(i.charAt(l>>>6*(3-h)&63));var f=i.charAt(64);if(f)while(o.length%4)o.push(f);return o.join("")},parse:function(t,e){void 0===e&&(e=!0);var r=t.length,n=e?this._safe_map:this._map,i=this._reverseMap;if(!i){i=this._reverseMap=[];for(var s=0;s<n.length;s++)i[n.charCodeAt(s)]=s}var a=n.charAt(64);if(a){var c=t.indexOf(a);-1!==c&&(r=c)}return o(t,r,i)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",_safe_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"};function o(t,e,r){for(var i=[],o=0,s=0;s<e;s++)if(s%4){var a=r[t.charCodeAt(s-1)]<<s%4*2,c=r[t.charCodeAt(s)]>>>6-s%4*2,u=a|c;i[o>>>2]|=u<<24-o%4*8,o++}return n.create(i,o)}}(),t.enc.Base64url}))},c3b6:function(t,e,r){(function(e,n,i){t.exports=n(r("21bf"),r("1132"),r("72fe"),r("2b79"),r("38ba"))})(0,(function(t){return function(){var e=t,r=e.lib,n=r.StreamCipher,i=e.algo,o=i.RC4=n.extend({_doReset:function(){for(var t=this._key,e=t.words,r=t.sigBytes,n=this._S=[],i=0;i<256;i++)n[i]=i;i=0;for(var o=0;i<256;i++){var s=i%r,a=e[s>>>2]>>>24-s%4*8&255;o=(o+n[i]+a)%256;var c=n[i];n[i]=n[o],n[o]=c}this._i=this._j=0},_doProcessBlock:function(t,e){t[e]^=s.call(this)},keySize:8,ivSize:0});function s(){for(var t=this._S,e=this._i,r=this._j,n=0,i=0;i<4;i++){e=(e+1)%256,r=(r+t[e])%256;var o=t[e];t[e]=t[r],t[r]=o,n|=t[(t[e]+t[r])%256]<<24-8*i}return this._i=e,this._j=r,n}e.RC4=n._createHelper(o);var a=i.RC4Drop=o.extend({cfg:o.cfg.extend({drop:192}),_doReset:function(){o._doReset.call(this);for(var t=this.cfg.drop;t>0;t--)s.call(this)}});e.RC4Drop=n._createHelper(a)}(),t.RC4}))},c4c7:function(t,e,r){"use strict";const{Buffer:n}=r("b639"),i=r("035d").Transform,o=r("56ac");let s,a,c,u=!1;function l(){const t=new i;return t._write=function(t,e,r){s.sendSocketMessage({data:t.buffer,success:function(){r()},fail:function(){r(new Error)}})},t._flush=function(t){s.closeSocket({success:function(){t()}})},t}function h(t){t.hostname||(t.hostname="localhost"),t.path||(t.path="/"),t.wsOptions||(t.wsOptions={})}function f(t,e){const r="alis"===t.protocol?"wss":"ws";let n=r+"://"+t.hostname+t.path;return t.port&&80!==t.port&&443!==t.port&&(n=r+"://"+t.hostname+":"+t.port+t.path),"function"===typeof t.transformWsUrl&&(n=t.transformWsUrl(n,t,e)),n}function p(){u||(u=!0,s.onSocketOpen((function(){c.setReadable(a),c.setWritable(a),c.emit("connect")})),s.onSocketMessage((function(t){if("string"===typeof t.data){const e=n.from(t.data,"base64");a.push(e)}else{const e=new FileReader;e.addEventListener("load",(function(){let t=e.result;t=t instanceof ArrayBuffer?n.from(t):n.from(t,"utf8"),a.push(t)})),e.readAsArrayBuffer(t.data)}})),s.onSocketClose((function(){c.end(),c.destroy()})),s.onSocketError((function(t){c.destroy(t)})))}function d(t,e){if(e.hostname=e.hostname||e.host,!e.hostname)throw new Error("Could not determine host. Specify host manually.");const r="MQIsdp"===e.protocolId&&3===e.protocolVersion?"mqttv3.1":"mqtt";h(e);const n=f(e,t);return s=e.my,s.connectSocket({url:n,protocols:r}),a=l(),c=o.obj(),p(),c}t.exports=d},d009:function(t,e,r){"use strict";var n=r("00ce"),i=r("7992"),o=r("64b0")(),s=r("2aa9"),a=n("%TypeError%"),c=n("%Math.floor%");t.exports=function(t,e){if("function"!==typeof t)throw new a("`fn` is not a function");if("number"!==typeof e||e<0||e>4294967295||c(e)!==e)throw new a("`length` must be a positive 32-bit integer");var r=arguments.length>2&&!!arguments[2],n=!0,u=!0;if("length"in t&&s){var l=s(t,"length");l&&!l.configurable&&(n=!1),l&&!l.writable&&(u=!1)}return(n||u||!r)&&(o?i(t,"length",e,!0,!0):i(t,"length",e)),t}},d633:function(t,e){function r(t,e){if(t&&e)return r(t)(e);if("function"!==typeof t)throw new TypeError("need wrapper function");return Object.keys(t).forEach((function(e){n[e]=t[e]})),n;function n(){for(var e=new Array(arguments.length),r=0;r<e.length;r++)e[r]=arguments[r];var n=t.apply(this,e),i=e[e.length-1];return"function"===typeof n&&n!==i&&Object.keys(i).forEach((function(t){n[t]=i[t]})),n}}t.exports=r},d6e6:function(t,e,r){(function(e,n,i){t.exports=n(r("21bf"),r("3252"))})(0,(function(t){return function(){var e=t,r=e.lib,n=r.Hasher,i=e.x64,o=i.Word,s=i.WordArray,a=e.algo;function c(){return o.create.apply(o,arguments)}var u=[c(1116352408,3609767458),c(1899447441,602891725),c(3049323471,3964484399),c(3921009573,2173295548),c(961987163,4081628472),c(1508970993,3053834265),c(2453635748,2937671579),c(2870763221,3664609560),c(3624381080,2734883394),c(310598401,1164996542),c(607225278,1323610764),c(1426881987,3590304994),c(1925078388,4068182383),c(2162078206,991336113),c(2614888103,633803317),c(3248222580,3479774868),c(3835390401,2666613458),c(4022224774,944711139),c(264347078,2341262773),c(604807628,2007800933),c(770255983,1495990901),c(1249150122,1856431235),c(1555081692,3175218132),c(1996064986,2198950837),c(2554220882,3999719339),c(2821834349,766784016),c(2952996808,2566594879),c(3210313671,3203337956),c(3336571891,1034457026),c(3584528711,2466948901),c(113926993,3758326383),c(338241895,168717936),c(666307205,1188179964),c(773529912,1546045734),c(1294757372,1522805485),c(1396182291,2643833823),c(1695183700,2343527390),c(1986661051,1014477480),c(2177026350,1206759142),c(2456956037,344077627),c(2730485921,1290863460),c(2820302411,3158454273),c(3259730800,3505952657),c(3345764771,106217008),c(3516065817,3606008344),c(3600352804,1432725776),c(4094571909,1467031594),c(275423344,851169720),c(430227734,3100823752),c(506948616,1363258195),c(659060556,3750685593),c(883997877,3785050280),c(958139571,3318307427),c(1322822218,3812723403),c(1537002063,2003034995),c(1747873779,3602036899),c(1955562222,1575990012),c(2024104815,1125592928),c(2227730452,2716904306),c(2361852424,442776044),c(2428436474,593698344),c(2756734187,3733110249),c(3204031479,2999351573),c(3329325298,3815920427),c(3391569614,3928383900),c(3515267271,566280711),c(3940187606,3454069534),c(4118630271,4000239992),c(116418474,1914138554),c(174292421,2731055270),c(289380356,3203993006),c(460393269,320620315),c(685471733,587496836),c(852142971,1086792851),c(1017036298,365543100),c(1126000580,2618297676),c(1288033470,3409855158),c(1501505948,4234509866),c(1607167915,987167468),c(1816402316,1246189591)],l=[];(function(){for(var t=0;t<80;t++)l[t]=c()})();var h=a.SHA512=n.extend({_doReset:function(){this._hash=new s.init([new o.init(1779033703,4089235720),new o.init(3144134277,2227873595),new o.init(1013904242,4271175723),new o.init(2773480762,1595750129),new o.init(1359893119,2917565137),new o.init(2600822924,725511199),new o.init(528734635,4215389547),new o.init(1541459225,327033209)])},_doProcessBlock:function(t,e){for(var r=this._hash.words,n=r[0],i=r[1],o=r[2],s=r[3],a=r[4],c=r[5],h=r[6],f=r[7],p=n.high,d=n.low,y=i.high,b=i.low,g=o.high,m=o.low,v=s.high,_=s.low,w=a.high,S=a.low,E=c.high,k=c.low,A=h.high,x=h.low,P=f.high,O=f.low,R=p,M=d,C=y,T=b,B=g,I=m,j=v,N=_,L=w,D=S,F=E,U=k,H=A,q=x,W=P,K=O,z=0;z<80;z++){var V,G,Q=l[z];if(z<16)G=Q.high=0|t[e+2*z],V=Q.low=0|t[e+2*z+1];else{var $=l[z-15],J=$.high,Y=$.low,X=(J>>>1|Y<<31)^(J>>>8|Y<<24)^J>>>7,Z=(Y>>>1|J<<31)^(Y>>>8|J<<24)^(Y>>>7|J<<25),tt=l[z-2],et=tt.high,rt=tt.low,nt=(et>>>19|rt<<13)^(et<<3|rt>>>29)^et>>>6,it=(rt>>>19|et<<13)^(rt<<3|et>>>29)^(rt>>>6|et<<26),ot=l[z-7],st=ot.high,at=ot.low,ct=l[z-16],ut=ct.high,lt=ct.low;V=Z+at,G=X+st+(V>>>0<Z>>>0?1:0),V+=it,G=G+nt+(V>>>0<it>>>0?1:0),V+=lt,G=G+ut+(V>>>0<lt>>>0?1:0),Q.high=G,Q.low=V}var ht=L&F^~L&H,ft=D&U^~D&q,pt=R&C^R&B^C&B,dt=M&T^M&I^T&I,yt=(R>>>28|M<<4)^(R<<30|M>>>2)^(R<<25|M>>>7),bt=(M>>>28|R<<4)^(M<<30|R>>>2)^(M<<25|R>>>7),gt=(L>>>14|D<<18)^(L>>>18|D<<14)^(L<<23|D>>>9),mt=(D>>>14|L<<18)^(D>>>18|L<<14)^(D<<23|L>>>9),vt=u[z],_t=vt.high,wt=vt.low,St=K+mt,Et=W+gt+(St>>>0<K>>>0?1:0),kt=(St=St+ft,Et=Et+ht+(St>>>0<ft>>>0?1:0),St=St+wt,Et=Et+_t+(St>>>0<wt>>>0?1:0),St=St+V,Et=Et+G+(St>>>0<V>>>0?1:0),bt+dt),At=yt+pt+(kt>>>0<bt>>>0?1:0);W=H,K=q,H=F,q=U,F=L,U=D,D=N+St|0,L=j+Et+(D>>>0<N>>>0?1:0)|0,j=B,N=I,B=C,I=T,C=R,T=M,M=St+kt|0,R=Et+At+(M>>>0<St>>>0?1:0)|0}d=n.low=d+M,n.high=p+R+(d>>>0<M>>>0?1:0),b=i.low=b+T,i.high=y+C+(b>>>0<T>>>0?1:0),m=o.low=m+I,o.high=g+B+(m>>>0<I>>>0?1:0),_=s.low=_+N,s.high=v+j+(_>>>0<N>>>0?1:0),S=a.low=S+D,a.high=w+L+(S>>>0<D>>>0?1:0),k=c.low=k+U,c.high=E+F+(k>>>0<U>>>0?1:0),x=h.low=x+q,h.high=A+H+(x>>>0<q>>>0?1:0),O=f.low=O+K,f.high=P+W+(O>>>0<K>>>0?1:0)},_doFinalize:function(){var t=this._data,e=t.words,r=8*this._nDataBytes,n=8*t.sigBytes;e[n>>>5]|=128<<24-n%32,e[30+(n+128>>>10<<5)]=Math.floor(r/4294967296),e[31+(n+128>>>10<<5)]=r,t.sigBytes=4*e.length,this._process();var i=this._hash.toX32();return i},clone:function(){var t=n.clone.call(this);return t._hash=this._hash.clone(),t},blockSize:32});e.SHA512=n._createHelper(h),e.HmacSHA512=n._createHmacHelper(h)}(),t.SHA512}))},d9e1:function(t,e,r){"use strict";var n=r("fbd7").codes.ERR_STREAM_PREMATURE_CLOSE;function i(t){var e=!1;return function(){if(!e){e=!0;for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];t.apply(this,n)}}}function o(){}function s(t){return t.setHeader&&"function"===typeof t.abort}function a(t,e,r){if("function"===typeof e)return a(t,null,e);e||(e={}),r=i(r||o);var c=e.readable||!1!==e.readable&&t.readable,u=e.writable||!1!==e.writable&&t.writable,l=function(){t.writable||f()},h=t._writableState&&t._writableState.finished,f=function(){u=!1,h=!0,c||r.call(t)},p=t._readableState&&t._readableState.endEmitted,d=function(){c=!1,p=!0,u||r.call(t)},y=function(e){r.call(t,e)},b=function(){var e;return c&&!p?(t._readableState&&t._readableState.ended||(e=new n),r.call(t,e)):u&&!h?(t._writableState&&t._writableState.ended||(e=new n),r.call(t,e)):void 0},g=function(){t.req.on("finish",f)};return s(t)?(t.on("complete",f),t.on("abort",b),t.req?g():t.on("request",g)):u&&!t._writableState&&(t.on("end",l),t.on("close",l)),t.on("end",d),t.on("finish",f),!1!==e.error&&t.on("error",y),t.on("close",b),function(){t.removeListener("complete",f),t.removeListener("abort",b),t.removeListener("request",g),t.req&&t.req.removeListener("finish",f),t.removeListener("end",l),t.removeListener("close",l),t.removeListener("finish",f),t.removeListener("end",d),t.removeListener("error",y),t.removeListener("close",b)}}t.exports=a},dc90:function(t,e,r){function n(t){function e(t){let e=0;for(let r=0;r<t.length;r++)e=(e<<5)-e+t.charCodeAt(r),e|=0;return n.colors[Math.abs(e)%n.colors.length]}function n(t){let e,r,o,s=null;function a(...t){if(!a.enabled)return;const r=a,i=Number(new Date),o=i-(e||i);r.diff=o,r.prev=e,r.curr=i,e=i,t[0]=n.coerce(t[0]),"string"!==typeof t[0]&&t.unshift("%O");let s=0;t[0]=t[0].replace(/%([a-zA-Z%])/g,(e,i)=>{if("%%"===e)return"%";s++;const o=n.formatters[i];if("function"===typeof o){const n=t[s];e=o.call(r,n),t.splice(s,1),s--}return e}),n.formatArgs.call(r,t);const c=r.log||n.log;c.apply(r,t)}return a.namespace=t,a.useColors=n.useColors(),a.color=n.selectColor(t),a.extend=i,a.destroy=n.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==s?s:(r!==n.namespaces&&(r=n.namespaces,o=n.enabled(t)),o),set:t=>{s=t}}),"function"===typeof n.init&&n.init(a),a}function i(t,e){const r=n(this.namespace+("undefined"===typeof e?":":e)+t);return r.log=this.log,r}function o(t){let e;n.save(t),n.namespaces=t,n.names=[],n.skips=[];const r=("string"===typeof t?t:"").split(/[\s,]+/),i=r.length;for(e=0;e<i;e++)r[e]&&(t=r[e].replace(/\*/g,".*?"),"-"===t[0]?n.skips.push(new RegExp("^"+t.slice(1)+"$")):n.names.push(new RegExp("^"+t+"$")))}function s(){const t=[...n.names.map(c),...n.skips.map(c).map(t=>"-"+t)].join(",");return n.enable(""),t}function a(t){if("*"===t[t.length-1])return!0;let e,r;for(e=0,r=n.skips.length;e<r;e++)if(n.skips[e].test(t))return!1;for(e=0,r=n.names.length;e<r;e++)if(n.names[e].test(t))return!0;return!1}function c(t){return t.toString().substring(2,t.toString().length-2).replace(/\.\*\?$/,"*")}function u(t){return t instanceof Error?t.stack||t.message:t}function l(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return n.debug=n,n.default=n,n.coerce=u,n.disable=s,n.enable=o,n.enabled=a,n.humanize=r("1468"),n.destroy=l,Object.keys(t).forEach(e=>{n[e]=t[e]}),n.names=[],n.skips=[],n.formatters={},n.selectColor=e,n.enable(n.load()),n}t.exports=n},df2f:function(t,e,r){(function(e,n){t.exports=n(r("21bf"))})(0,(function(t){return function(){var e=t,r=e.lib,n=r.WordArray,i=r.Hasher,o=e.algo,s=[],a=o.SHA1=i.extend({_doReset:function(){this._hash=new n.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(t,e){for(var r=this._hash.words,n=r[0],i=r[1],o=r[2],a=r[3],c=r[4],u=0;u<80;u++){if(u<16)s[u]=0|t[e+u];else{var l=s[u-3]^s[u-8]^s[u-14]^s[u-16];s[u]=l<<1|l>>>31}var h=(n<<5|n>>>27)+c+s[u];h+=u<20?1518500249+(i&o|~i&a):u<40?1859775393+(i^o^a):u<60?(i&o|i&a|o&a)-1894007588:(i^o^a)-899497514,c=a,a=o,o=i<<30|i>>>2,i=n,n=h}r[0]=r[0]+n|0,r[1]=r[1]+i|0,r[2]=r[2]+o|0,r[3]=r[3]+a|0,r[4]=r[4]+c|0},_doFinalize:function(){var t=this._data,e=t.words,r=8*this._nDataBytes,n=8*t.sigBytes;return e[n>>>5]|=128<<24-n%32,e[14+(n+64>>>9<<4)]=Math.floor(r/4294967296),e[15+(n+64>>>9<<4)]=r,t.sigBytes=4*e.length,this._process(),this._hash},clone:function(){var t=i.clone.call(this);return t._hash=this._hash.clone(),t}});e.SHA1=i._createHelper(a),e.HmacSHA1=i._createHmacHelper(a)}(),t.SHA1}))},df86:function(t,e,r){"use strict";(function(e,n){const i=r("faa1").EventEmitter,o=r("ea08"),s=r("e7d0"),a=r("a43f"),c=r("3409f"),u=r("1e4d"),l=r("035d").Writable,h=r("3fb5"),f=r("29a2"),p=r("2a28"),d=r("ae84"),y=r("53a8"),b=r("34eb")("mqttjs:client"),g=e?e.nextTick:function(t){setTimeout(t,0)},m=n.setImmediate||function(t){g(t)},v={keepalive:60,reschedulePings:!0,protocolId:"MQTT",protocolVersion:4,reconnectPeriod:1e3,connectTimeout:3e4,clean:!0,resubscribe:!0},_=["ECONNREFUSED","EADDRINUSE","ECONNRESET","ENOTFOUND"],w={0:"",1:"Unacceptable protocol version",2:"Identifier rejected",3:"Server unavailable",4:"Bad username or password",5:"Not authorized",16:"No matching subscribers",17:"No subscription existed",128:"Unspecified error",129:"Malformed Packet",130:"Protocol Error",131:"Implementation specific error",132:"Unsupported Protocol Version",133:"Client Identifier not valid",134:"Bad User Name or Password",135:"Not authorized",136:"Server unavailable",137:"Server busy",138:"Banned",139:"Server shutting down",140:"Bad authentication method",141:"Keep Alive timeout",142:"Session taken over",143:"Topic Filter invalid",144:"Topic Name invalid",145:"Packet identifier in use",146:"Packet Identifier not found",147:"Receive Maximum exceeded",148:"Topic Alias invalid",149:"Packet too large",150:"Message rate too high",151:"Quota exceeded",152:"Administrative action",153:"Payload format invalid",154:"Retain not supported",155:"QoS not supported",156:"Use another server",157:"Server moved",158:"Shared Subscriptions not supported",159:"Connection rate exceeded",160:"Maximum connect time",161:"Subscription Identifiers not supported",162:"Wildcard Subscriptions not supported"};function S(){return"mqttjs_"+Math.random().toString(16).substr(2,8)}function E(t,e){if(5===t.options.protocolVersion&&"publish"===e.cmd){let r;e.properties&&(r=e.properties.topicAlias);const n=e.topic.toString();if(t.topicAliasSend)if(r){if(0!==n.length&&(b("applyTopicAlias :: register topic: %s - alias: %d",n,r),!t.topicAliasSend.put(n,r)))return b("applyTopicAlias :: error out of range. topic: %s - alias: %d",n,r),new Error("Sending Topic Alias out of range")}else 0!==n.length&&(t.options.autoAssignTopicAlias?(r=t.topicAliasSend.getAliasByTopic(n),r?(e.topic="",e.properties={...e.properties,topicAlias:r},b("applyTopicAlias :: auto assign(use) topic: %s - alias: %d",n,r)):(r=t.topicAliasSend.getLruAlias(),t.topicAliasSend.put(n,r),e.properties={...e.properties,topicAlias:r},b("applyTopicAlias :: auto assign topic: %s - alias: %d",n,r))):t.options.autoUseTopicAlias&&(r=t.topicAliasSend.getAliasByTopic(n),r&&(e.topic="",e.properties={...e.properties,topicAlias:r},b("applyTopicAlias :: auto use topic: %s - alias: %d",n,r))));else if(r)return b("applyTopicAlias :: error out of range. topic: %s - alias: %d",n,r),new Error("Sending Topic Alias out of range")}}function k(t,e){let r;e.properties&&(r=e.properties.topicAlias);let n=e.topic.toString();if(0===n.length){if("undefined"===typeof r)return new Error("Unregistered Topic Alias");if(n=t.topicAliasSend.getTopicByAlias(r),"undefined"===typeof n)return new Error("Unregistered Topic Alias");e.topic=n}r&&delete e.properties.topicAlias}function A(t,e,r){b("sendPacket :: packet: %O",e),b("sendPacket :: emitting `packetsend`"),t.emit("packetsend",e),b("sendPacket :: writing to stream");const n=c.writeToStream(e,t.stream,t.options);b("sendPacket :: writeToStream result %s",n),!n&&r&&r!==R?(b("sendPacket :: handle events on `drain` once through callback."),t.stream.once("drain",r)):r&&(b("sendPacket :: invoking cb"),r())}function x(t){t&&(b("flush: queue exists? %b",!!t),Object.keys(t).forEach((function(e){"function"===typeof t[e].cb&&(t[e].cb(new Error("Connection closed")),delete t[e])})))}function P(t){t&&(b("flushVolatile :: deleting volatile messages from the queue and setting their callbacks as error function"),Object.keys(t).forEach((function(e){t[e].volatile&&"function"===typeof t[e].cb&&(t[e].cb(new Error("Connection closed")),delete t[e])})))}function O(t,e,r,n){b("storeAndSend :: store packet with cmd %s to outgoingStore",e.cmd);let i,o=e;if("publish"===o.cmd&&(o=p(e),i=k(t,o),i))return r&&r(i);t.outgoingStore.put(o,(function(i){if(i)return r&&r(i);n(),A(t,e,r)}))}function R(t){b("nop ::",t)}function M(t,e){let r;const n=this;if(!(this instanceof M))return new M(t,e);for(r in this.options=e||{},v)"undefined"===typeof this.options[r]?this.options[r]=v[r]:this.options[r]=e[r];b("MqttClient :: options.protocol",e.protocol),b("MqttClient :: options.protocolVersion",e.protocolVersion),b("MqttClient :: options.username",e.username),b("MqttClient :: options.keepalive",e.keepalive),b("MqttClient :: options.reconnectPeriod",e.reconnectPeriod),b("MqttClient :: options.rejectUnauthorized",e.rejectUnauthorized),b("MqttClient :: options.topicAliasMaximum",e.topicAliasMaximum),this.options.clientId="string"===typeof e.clientId?e.clientId:S(),b("MqttClient :: clientId",this.options.clientId),this.options.customHandleAcks=5===e.protocolVersion&&e.customHandleAcks?e.customHandleAcks:function(){arguments[3](0)},this.streamBuilder=t,this.messageIdProvider="undefined"===typeof this.options.messageIdProvider?new u:this.options.messageIdProvider,this.outgoingStore=e.outgoingStore||new o,this.incomingStore=e.incomingStore||new o,this.queueQoSZero=void 0===e.queueQoSZero||e.queueQoSZero,this._resubscribeTopics={},this.messageIdToTopic={},this.pingTimer=null,this.connected=!1,this.disconnecting=!1,this.queue=[],this.connackTimer=null,this.reconnectTimer=null,this._storeProcessing=!1,this._packetIdsDuringStoreProcessing={},this._storeProcessingQueue=[],this.outgoing={},this._firstConnection=!0,e.topicAliasMaximum>0&&(e.topicAliasMaximum>65535?b("MqttClient :: options.topicAliasMaximum is out of range"):this.topicAliasRecv=new s(e.topicAliasMaximum)),this.on("connect",(function(){const t=this.queue;function e(){const r=t.shift();b("deliver :: entry %o",r);let i=null;if(!r)return void n._resubscribe();i=r.packet,b("deliver :: call _sendPacket for %o",i);let o=!0;i.messageId&&0!==i.messageId&&(n.messageIdProvider.register(i.messageId)||(o=!1)),o?n._sendPacket(i,(function(t){r.cb&&r.cb(t),e()})):(b("messageId: %d has already used. The message is skipped and removed.",i.messageId),e())}b("connect :: sending queued packets"),e()})),this.on("close",(function(){b("close :: connected set to `false`"),this.connected=!1,b("close :: clearing connackTimer"),clearTimeout(this.connackTimer),b("close :: clearing ping timer"),null!==n.pingTimer&&(n.pingTimer.clear(),n.pingTimer=null),this.topicAliasRecv&&this.topicAliasRecv.clear(),b("close :: calling _setupReconnect"),this._setupReconnect()})),i.call(this),b("MqttClient :: setting up stream"),this._setupStream()}h(M,i),M.prototype._setupStream=function(){const t=this,e=new l,r=c.parser(this.options);let n=null;const i=[];function o(){if(i.length)g(s);else{const t=n;n=null,t()}}function s(){b("work :: getting next packet in queue");const e=i.shift();if(e)b("work :: packet pulled from queue"),t._handlePacket(e,o);else{b("work :: no packets in queue");const t=n;n=null,b("work :: done flag is %s",!!t),t&&t()}}function a(e){b("streamErrorHandler :: error",e.message),_.includes(e.code)?(b("streamErrorHandler :: emitting error"),t.emit("error",e)):R(e)}b("_setupStream :: calling method to clear reconnect"),this._clearReconnect(),b("_setupStream :: using streamBuilder provided to client to create stream"),this.stream=this.streamBuilder(this),r.on("packet",(function(t){b("parser :: on packet push to packets array."),i.push(t)})),e._write=function(t,e,i){n=i,b("writable stream :: parsing buffer"),r.parse(t),s()},b("_setupStream :: pipe stream to writable stream"),this.stream.pipe(e),this.stream.on("error",a),this.stream.on("close",(function(){b("(%s)stream :: on close",t.options.clientId),P(t.outgoing),b("stream: emit close to MqttClient"),t.emit("close")})),b("_setupStream: sending packet `connect`");const u=Object.create(this.options);if(u.cmd="connect",this.topicAliasRecv&&(u.properties||(u.properties={}),this.topicAliasRecv&&(u.properties.topicAliasMaximum=this.topicAliasRecv.max)),A(this,u),r.on("error",this.emit.bind(this,"error")),this.options.properties){if(!this.options.properties.authenticationMethod&&this.options.properties.authenticationData)return t.end(()=>this.emit("error",new Error("Packet has no Authentication Method"))),this;if(this.options.properties.authenticationMethod&&this.options.authPacket&&"object"===typeof this.options.authPacket){const t=y({cmd:"auth",reasonCode:0},this.options.authPacket);A(this,t)}}this.stream.setMaxListeners(1e3),clearTimeout(this.connackTimer),this.connackTimer=setTimeout((function(){b("!!connectTimeout hit!! Calling _cleanUp with force `true`"),t._cleanUp(!0)}),this.options.connectTimeout)},M.prototype._handlePacket=function(t,e){const r=this.options;if(5===r.protocolVersion&&r.properties&&r.properties.maximumPacketSize&&r.properties.maximumPacketSize<t.length)return this.emit("error",new Error("exceeding packets size "+t.cmd)),this.end({reasonCode:149,properties:{reasonString:"Maximum packet size was exceeded"}}),this;switch(b("_handlePacket :: emitting packetreceive"),this.emit("packetreceive",t),t.cmd){case"publish":this._handlePublish(t,e);break;case"puback":case"pubrec":case"pubcomp":case"suback":case"unsuback":this._handleAck(t),e();break;case"pubrel":this._handlePubrel(t,e);break;case"connack":this._handleConnack(t),e();break;case"auth":this._handleAuth(t),e();break;case"pingresp":this._handlePingresp(t),e();break;case"disconnect":this._handleDisconnect(t),e();break;default:break}},M.prototype._checkDisconnecting=function(t){return this.disconnecting&&(t&&t!==R?t(new Error("client disconnecting")):this.emit("error",new Error("client disconnecting"))),this.disconnecting},M.prototype.publish=function(t,e,r,n){b("publish :: message `%s` to topic `%s`",e,t);const i=this.options;"function"===typeof r&&(n=r,r=null);const o={qos:0,retain:!1,dup:!1};if(r=y(o,r),this._checkDisconnecting(n))return this;const s=this,a=function(){let o=0;if((1===r.qos||2===r.qos)&&(o=s._nextId(),null===o))return b("No messageId left"),!1;const a={cmd:"publish",topic:t,payload:e,qos:r.qos,retain:r.retain,messageId:o,dup:r.dup};switch(5===i.protocolVersion&&(a.properties=r.properties),b("publish :: qos",r.qos),r.qos){case 1:case 2:s.outgoing[a.messageId]={volatile:!1,cb:n||R},b("MqttClient:publish: packet cmd: %s",a.cmd),s._sendPacket(a,void 0,r.cbStorePut);break;default:b("MqttClient:publish: packet cmd: %s",a.cmd),s._sendPacket(a,n,r.cbStorePut);break}return!0};return(this._storeProcessing||this._storeProcessingQueue.length>0||!a())&&this._storeProcessingQueue.push({invoke:a,cbStorePut:r.cbStorePut,callback:n}),this},M.prototype.subscribe=function(){const t=this,e=new Array(arguments.length);for(let h=0;h<arguments.length;h++)e[h]=arguments[h];const r=[];let n=e.shift();const i=n.resubscribe;let o=e.pop()||R,s=e.pop();const a=this.options.protocolVersion;delete n.resubscribe,"string"===typeof n&&(n=[n]),"function"!==typeof o&&(s=o,o=R);const c=d.validateTopics(n);if(null!==c)return m(o,new Error("Invalid topic "+c)),this;if(this._checkDisconnecting(o))return b("subscribe: discconecting true"),this;const u={qos:0};if(5===a&&(u.nl=!1,u.rap=!1,u.rh=0),s=y(u,s),Array.isArray(n)?n.forEach((function(e){if(b("subscribe: array topic %s",e),!Object.prototype.hasOwnProperty.call(t._resubscribeTopics,e)||t._resubscribeTopics[e].qos<s.qos||i){const t={topic:e,qos:s.qos};5===a&&(t.nl=s.nl,t.rap=s.rap,t.rh=s.rh,t.properties=s.properties),b("subscribe: pushing topic `%s` and qos `%s` to subs list",t.topic,t.qos),r.push(t)}})):Object.keys(n).forEach((function(e){if(b("subscribe: object topic %s",e),!Object.prototype.hasOwnProperty.call(t._resubscribeTopics,e)||t._resubscribeTopics[e].qos<n[e].qos||i){const t={topic:e,qos:n[e].qos};5===a&&(t.nl=n[e].nl,t.rap=n[e].rap,t.rh=n[e].rh,t.properties=s.properties),b("subscribe: pushing `%s` to subs list",t),r.push(t)}})),!r.length)return o(null,[]),this;const l=function(){const e=t._nextId();if(null===e)return b("No messageId left"),!1;const n={cmd:"subscribe",subscriptions:r,qos:1,retain:!1,dup:!1,messageId:e};if(s.properties&&(n.properties=s.properties),t.options.resubscribe){b("subscribe :: resubscribe true");const e=[];r.forEach((function(r){if(t.options.reconnectPeriod>0){const n={qos:r.qos};5===a&&(n.nl=r.nl||!1,n.rap=r.rap||!1,n.rh=r.rh||0,n.properties=r.properties),t._resubscribeTopics[r.topic]=n,e.push(r.topic)}})),t.messageIdToTopic[n.messageId]=e}return t.outgoing[n.messageId]={volatile:!0,cb:function(t,e){if(!t){const t=e.granted;for(let e=0;e<t.length;e+=1)r[e].qos=t[e]}o(t,r)}},b("subscribe :: call _sendPacket"),t._sendPacket(n),!0};return(this._storeProcessing||this._storeProcessingQueue.length>0||!l())&&this._storeProcessingQueue.push({invoke:l,callback:o}),this},M.prototype.unsubscribe=function(){const t=this,e=new Array(arguments.length);for(let a=0;a<arguments.length;a++)e[a]=arguments[a];let r=e.shift(),n=e.pop()||R,i=e.pop();"string"===typeof r&&(r=[r]),"function"!==typeof n&&(i=n,n=R);const o=d.validateTopics(r);if(null!==o)return m(n,new Error("Invalid topic "+o)),this;if(t._checkDisconnecting(n))return this;const s=function(){const e=t._nextId();if(null===e)return b("No messageId left"),!1;const o={cmd:"unsubscribe",qos:1,messageId:e};return"string"===typeof r?o.unsubscriptions=[r]:Array.isArray(r)&&(o.unsubscriptions=r),t.options.resubscribe&&o.unsubscriptions.forEach((function(e){delete t._resubscribeTopics[e]})),"object"===typeof i&&i.properties&&(o.properties=i.properties),t.outgoing[o.messageId]={volatile:!0,cb:n},b("unsubscribe: call _sendPacket"),t._sendPacket(o),!0};return(this._storeProcessing||this._storeProcessingQueue.length>0||!s())&&this._storeProcessingQueue.push({invoke:s,callback:n}),this},M.prototype.end=function(t,e,r){const n=this;function i(){b("end :: closeStores: closing incoming and outgoing stores"),n.disconnected=!0,n.incomingStore.close((function(t){n.outgoingStore.close((function(e){if(b("end :: closeStores: emitting end"),n.emit("end"),r){const n=t||e;b("end :: closeStores: invoking callback with args"),r(n)}}))})),n._deferredReconnect&&n._deferredReconnect()}function o(){b("end :: (%s) :: finish :: calling _cleanUp with force %s",n.options.clientId,t),n._cleanUp(t,()=>{b("end :: finish :: calling process.nextTick on closeStores"),g(i.bind(n))},e)}return b("end :: (%s)",this.options.clientId),null!=t&&"boolean"===typeof t||(r=e||R,e=t,t=!1,"object"!==typeof e&&(r=e,e=null,"function"!==typeof r&&(r=R))),"object"!==typeof e&&(r=e,e=null),b("end :: cb? %s",!!r),r=r||R,this.disconnecting?(r(),this):(this._clearReconnect(),this.disconnecting=!0,!t&&Object.keys(this.outgoing).length>0?(b("end :: (%s) :: calling finish in 10ms once outgoing is empty",n.options.clientId),this.once("outgoingEmpty",setTimeout.bind(null,o,10))):(b("end :: (%s) :: immediately calling finish",n.options.clientId),o()),this)},M.prototype.removeOutgoingMessage=function(t){const e=this.outgoing[t]?this.outgoing[t].cb:null;return delete this.outgoing[t],this.outgoingStore.del({messageId:t},(function(){e(new Error("Message removed"))})),this},M.prototype.reconnect=function(t){b("client reconnect");const e=this,r=function(){t?(e.options.incomingStore=t.incomingStore,e.options.outgoingStore=t.outgoingStore):(e.options.incomingStore=null,e.options.outgoingStore=null),e.incomingStore=e.options.incomingStore||new o,e.outgoingStore=e.options.outgoingStore||new o,e.disconnecting=!1,e.disconnected=!1,e._deferredReconnect=null,e._reconnect()};return this.disconnecting&&!this.disconnected?this._deferredReconnect=r:r(),this},M.prototype._reconnect=function(){b("_reconnect: emitting reconnect to client"),this.emit("reconnect"),this.connected?(this.end(()=>{this._setupStream()}),b("client already connected. disconnecting first.")):(b("_reconnect: calling _setupStream"),this._setupStream())},M.prototype._setupReconnect=function(){const t=this;!t.disconnecting&&!t.reconnectTimer&&t.options.reconnectPeriod>0?(this.reconnecting||(b("_setupReconnect :: emit `offline` state"),this.emit("offline"),b("_setupReconnect :: set `reconnecting` to `true`"),this.reconnecting=!0),b("_setupReconnect :: setting reconnectTimer for %d ms",t.options.reconnectPeriod),t.reconnectTimer=setInterval((function(){b("reconnectTimer :: reconnect triggered!"),t._reconnect()}),t.options.reconnectPeriod)):b("_setupReconnect :: doing nothing...")},M.prototype._clearReconnect=function(){b("_clearReconnect : clearing reconnect timer"),this.reconnectTimer&&(clearInterval(this.reconnectTimer),this.reconnectTimer=null)},M.prototype._cleanUp=function(t,e){const r=arguments[2];if(e&&(b("_cleanUp :: done callback provided for on stream close"),this.stream.on("close",e)),b("_cleanUp :: forced? %s",t),t)0===this.options.reconnectPeriod&&this.options.clean&&x(this.outgoing),b("_cleanUp :: (%s) :: destroying stream",this.options.clientId),this.stream.destroy();else{const t=y({cmd:"disconnect"},r);b("_cleanUp :: (%s) :: call _sendPacket with disconnect packet",this.options.clientId),this._sendPacket(t,m.bind(null,this.stream.end.bind(this.stream)))}this.disconnecting||(b("_cleanUp :: client not disconnecting. Clearing and resetting reconnect."),this._clearReconnect(),this._setupReconnect()),null!==this.pingTimer&&(b("_cleanUp :: clearing pingTimer"),this.pingTimer.clear(),this.pingTimer=null),e&&!this.connected&&(b("_cleanUp :: (%s) :: removing stream `done` callback `close` listener",this.options.clientId),this.stream.removeListener("close",e),e())},M.prototype._sendPacket=function(t,e,r){b("_sendPacket :: (%s) ::  start",this.options.clientId),r=r||R,e=e||R;const n=E(this,t);if(n)e(n);else{if(!this.connected)return"auth"===t.cmd?(this._shiftPingInterval(),void A(this,t,e)):(b("_sendPacket :: client not connected. Storing packet offline."),void this._storePacket(t,e,r));switch(this._shiftPingInterval(),t.cmd){case"publish":break;case"pubrel":return void O(this,t,e,r);default:return void A(this,t,e)}switch(t.qos){case 2:case 1:O(this,t,e,r);break;case 0:default:A(this,t,e);break}b("_sendPacket :: (%s) ::  end",this.options.clientId)}},M.prototype._storePacket=function(t,e,r){b("_storePacket :: packet: %o",t),b("_storePacket :: cb? %s",!!e),r=r||R;let n=t;if("publish"===n.cmd){n=p(t);const r=k(this,n);if(r)return e&&e(r)}0===(n.qos||0)&&this.queueQoSZero||"publish"!==n.cmd?this.queue.push({packet:n,cb:e}):n.qos>0?(e=this.outgoing[n.messageId]?this.outgoing[n.messageId].cb:null,this.outgoingStore.put(n,(function(t){if(t)return e&&e(t);r()}))):e&&e(new Error("No connection to broker"))},M.prototype._setupPingTimer=function(){b("_setupPingTimer :: keepalive %d (seconds)",this.options.keepalive);const t=this;!this.pingTimer&&this.options.keepalive&&(this.pingResp=!0,this.pingTimer=f((function(){t._checkPing()}),1e3*this.options.keepalive))},M.prototype._shiftPingInterval=function(){this.pingTimer&&this.options.keepalive&&this.options.reschedulePings&&this.pingTimer.reschedule(1e3*this.options.keepalive)},M.prototype._checkPing=function(){b("_checkPing :: checking ping..."),this.pingResp?(b("_checkPing :: ping response received. Clearing flag and sending `pingreq`"),this.pingResp=!1,this._sendPacket({cmd:"pingreq"})):(b("_checkPing :: calling _cleanUp with force true"),this._cleanUp(!0))},M.prototype._handlePingresp=function(){this.pingResp=!0},M.prototype._handleConnack=function(t){b("_handleConnack");const e=this.options,r=e.protocolVersion,n=5===r?t.reasonCode:t.returnCode;if(clearTimeout(this.connackTimer),delete this.topicAliasSend,t.properties){if(t.properties.topicAliasMaximum){if(t.properties.topicAliasMaximum>65535)return void this.emit("error",new Error("topicAliasMaximum from broker is out of range"));t.properties.topicAliasMaximum>0&&(this.topicAliasSend=new a(t.properties.topicAliasMaximum))}t.properties.serverKeepAlive&&e.keepalive&&(e.keepalive=t.properties.serverKeepAlive,this._shiftPingInterval()),t.properties.maximumPacketSize&&(e.properties||(e.properties={}),e.properties.maximumPacketSize=t.properties.maximumPacketSize)}if(0===n)this.reconnecting=!1,this._onConnect(t);else if(n>0){const t=new Error("Connection refused: "+w[n]);t.code=n,this.emit("error",t)}},M.prototype._handleAuth=function(t){const e=this.options,r=e.protocolVersion,n=5===r?t.reasonCode:t.returnCode;if(5!==r){const t=new Error("Protocol error: Auth packets are only supported in MQTT 5. Your version:"+r);return t.code=n,void this.emit("error",t)}const i=this;this.handleAuth(t,(function(t,e){if(t)i.emit("error",t);else if(24===n)i.reconnecting=!1,i._sendPacket(e);else{const e=new Error("Connection refused: "+w[n]);t.code=n,i.emit("error",e)}}))},M.prototype.handleAuth=function(t,e){e()},M.prototype._handlePublish=function(t,e){b("_handlePublish: packet %o",t),e="undefined"!==typeof e?e:R;let r=t.topic.toString();const n=t.payload,i=t.qos,o=t.messageId,s=this,a=this.options,c=[0,16,128,131,135,144,145,151,153];if(5===this.options.protocolVersion){let e;if(t.properties&&(e=t.properties.topicAlias),"undefined"!==typeof e)if(0===r.length){if(!(e>0&&e<=65535))return b("_handlePublish :: topic alias out of range. alias: %d",e),void this.emit("error",new Error("Received Topic Alias is out of range"));{const t=this.topicAliasRecv.getTopicByAlias(e);if(!t)return b("_handlePublish :: unregistered topic alias. alias: %d",e),void this.emit("error",new Error("Received unregistered Topic Alias"));r=t,b("_handlePublish :: topic complemented by alias. topic: %s - alias: %d",r,e)}}else{if(!this.topicAliasRecv.put(r,e))return b("_handlePublish :: topic alias out of range. alias: %d",e),void this.emit("error",new Error("Received Topic Alias is out of range"));b("_handlePublish :: registered topic: %s - alias: %d",r,e)}}switch(b("_handlePublish: qos %d",i),i){case 2:a.customHandleAcks(r,n,t,(function(r,n){return r instanceof Error||(n=r,r=null),r?s.emit("error",r):-1===c.indexOf(n)?s.emit("error",new Error("Wrong reason code for pubrec")):void(n?s._sendPacket({cmd:"pubrec",messageId:o,reasonCode:n},e):s.incomingStore.put(t,(function(){s._sendPacket({cmd:"pubrec",messageId:o},e)})))}));break;case 1:a.customHandleAcks(r,n,t,(function(i,a){return i instanceof Error||(a=i,i=null),i?s.emit("error",i):-1===c.indexOf(a)?s.emit("error",new Error("Wrong reason code for puback")):(a||s.emit("message",r,n,t),void s.handleMessage(t,(function(t){if(t)return e&&e(t);s._sendPacket({cmd:"puback",messageId:o,reasonCode:a},e)})))}));break;case 0:this.emit("message",r,n,t),this.handleMessage(t,e);break;default:b("_handlePublish: unknown QoS. Doing nothing.");break}},M.prototype.handleMessage=function(t,e){e()},M.prototype._handleAck=function(t){const e=t.messageId,r=t.cmd;let n=null;const i=this.outgoing[e]?this.outgoing[e].cb:null,o=this;let s;if(i){switch(b("_handleAck :: packet type",r),r){case"pubcomp":case"puback":{const r=t.reasonCode;r&&r>0&&16!==r&&(s=new Error("Publish error: "+w[r]),s.code=r,i(s,t)),delete this.outgoing[e],this.outgoingStore.del(t,i),this.messageIdProvider.deallocate(e),this._invokeStoreProcessingQueue();break}case"pubrec":{n={cmd:"pubrel",qos:2,messageId:e};const r=t.reasonCode;r&&r>0&&16!==r?(s=new Error("Publish error: "+w[r]),s.code=r,i(s,t)):this._sendPacket(n);break}case"suback":delete this.outgoing[e],this.messageIdProvider.deallocate(e);for(let r=0;r<t.granted.length;r++)if(0!==(128&t.granted[r])){const t=this.messageIdToTopic[e];t&&t.forEach((function(t){delete o._resubscribeTopics[t]}))}this._invokeStoreProcessingQueue(),i(null,t);break;case"unsuback":delete this.outgoing[e],this.messageIdProvider.deallocate(e),this._invokeStoreProcessingQueue(),i(null);break;default:o.emit("error",new Error("unrecognized packet type"))}this.disconnecting&&0===Object.keys(this.outgoing).length&&this.emit("outgoingEmpty")}else b("_handleAck :: Server sent an ack in error. Ignoring.")},M.prototype._handlePubrel=function(t,e){b("handling pubrel packet"),e="undefined"!==typeof e?e:R;const r=t.messageId,n=this,i={cmd:"pubcomp",messageId:r};n.incomingStore.get(t,(function(t,r){t?n._sendPacket(i,e):(n.emit("message",r.topic,r.payload,r),n.handleMessage(r,(function(t){if(t)return e(t);n.incomingStore.del(r,R),n._sendPacket(i,e)})))}))},M.prototype._handleDisconnect=function(t){this.emit("disconnect",t)},M.prototype._nextId=function(){return this.messageIdProvider.allocate()},M.prototype.getLastMessageId=function(){return this.messageIdProvider.getLastAllocated()},M.prototype._resubscribe=function(){b("_resubscribe");const t=Object.keys(this._resubscribeTopics);if(!this._firstConnection&&(this.options.clean||5===this.options.protocolVersion&&!this.connackPacket.sessionPresent)&&t.length>0)if(this.options.resubscribe)if(5===this.options.protocolVersion){b("_resubscribe: protocolVersion 5");for(let e=0;e<t.length;e++){const r={};r[t[e]]=this._resubscribeTopics[t[e]],r.resubscribe=!0,this.subscribe(r,{properties:r[t[e]].properties})}}else this._resubscribeTopics.resubscribe=!0,this.subscribe(this._resubscribeTopics);else this._resubscribeTopics={};this._firstConnection=!1},M.prototype._onConnect=function(t){if(this.disconnected)return void this.emit("connect",t);const e=this;function r(){let n=e.outgoingStore.createStream();function i(){e._storeProcessing=!1,e._packetIdsDuringStoreProcessing={}}function o(){n.destroy(),n=null,e._flushStoreProcessingQueue(),i()}function s(){if(!n)return;e._storeProcessing=!0;const t=n.read(1);let r;t?e._packetIdsDuringStoreProcessing[t.messageId]?s():e.disconnecting||e.reconnectTimer?n.destroy&&n.destroy():(r=e.outgoing[t.messageId]?e.outgoing[t.messageId].cb:null,e.outgoing[t.messageId]={volatile:!1,cb:function(t,e){r&&r(t,e),s()}},e._packetIdsDuringStoreProcessing[t.messageId]=!0,e.messageIdProvider.register(t.messageId)?e._sendPacket(t):b("messageId: %d has already used.",t.messageId)):n.once("readable",s)}e.once("close",o),n.on("error",(function(t){i(),e._flushStoreProcessingQueue(),e.removeListener("close",o),e.emit("error",t)})),n.on("end",(function(){let n=!0;for(const t in e._packetIdsDuringStoreProcessing)if(!e._packetIdsDuringStoreProcessing[t]){n=!1;break}n?(i(),e.removeListener("close",o),e._invokeAllStoreProcessingQueue(),e.emit("connect",t)):r()})),s()}this.connackPacket=t,this.messageIdProvider.clear(),this._setupPingTimer(),this.connected=!0,r()},M.prototype._invokeStoreProcessingQueue=function(){if(this._storeProcessingQueue.length>0){const t=this._storeProcessingQueue[0];if(t&&t.invoke())return this._storeProcessingQueue.shift(),!0}return!1},M.prototype._invokeAllStoreProcessingQueue=function(){while(this._invokeStoreProcessingQueue());},M.prototype._flushStoreProcessingQueue=function(){for(const t of this._storeProcessingQueue)t.cbStorePut&&t.cbStorePut(new Error("Connection closed")),t.callback&&t.callback(new Error("Connection closed"));this._storeProcessingQueue.splice(0)},t.exports=M}).call(this,r("4362"),r("c8ba"))},e61b:function(t,e,r){(function(e,n,i){t.exports=n(r("21bf"),r("3252"))})(0,(function(t){return function(e){var r=t,n=r.lib,i=n.WordArray,o=n.Hasher,s=r.x64,a=s.Word,c=r.algo,u=[],l=[],h=[];(function(){for(var t=1,e=0,r=0;r<24;r++){u[t+5*e]=(r+1)*(r+2)/2%64;var n=e%5,i=(2*t+3*e)%5;t=n,e=i}for(t=0;t<5;t++)for(e=0;e<5;e++)l[t+5*e]=e+(2*t+3*e)%5*5;for(var o=1,s=0;s<24;s++){for(var c=0,f=0,p=0;p<7;p++){if(1&o){var d=(1<<p)-1;d<32?f^=1<<d:c^=1<<d-32}128&o?o=o<<1^113:o<<=1}h[s]=a.create(c,f)}})();var f=[];(function(){for(var t=0;t<25;t++)f[t]=a.create()})();var p=c.SHA3=o.extend({cfg:o.cfg.extend({outputLength:512}),_doReset:function(){for(var t=this._state=[],e=0;e<25;e++)t[e]=new a.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(t,e){for(var r=this._state,n=this.blockSize/2,i=0;i<n;i++){var o=t[e+2*i],s=t[e+2*i+1];o=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),s=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8);var a=r[i];a.high^=s,a.low^=o}for(var c=0;c<24;c++){for(var p=0;p<5;p++){for(var d=0,y=0,b=0;b<5;b++){a=r[p+5*b];d^=a.high,y^=a.low}var g=f[p];g.high=d,g.low=y}for(p=0;p<5;p++){var m=f[(p+4)%5],v=f[(p+1)%5],_=v.high,w=v.low;for(d=m.high^(_<<1|w>>>31),y=m.low^(w<<1|_>>>31),b=0;b<5;b++){a=r[p+5*b];a.high^=d,a.low^=y}}for(var S=1;S<25;S++){a=r[S];var E=a.high,k=a.low,A=u[S];A<32?(d=E<<A|k>>>32-A,y=k<<A|E>>>32-A):(d=k<<A-32|E>>>64-A,y=E<<A-32|k>>>64-A);var x=f[l[S]];x.high=d,x.low=y}var P=f[0],O=r[0];P.high=O.high,P.low=O.low;for(p=0;p<5;p++)for(b=0;b<5;b++){S=p+5*b,a=r[S];var R=f[S],M=f[(p+1)%5+5*b],C=f[(p+2)%5+5*b];a.high=R.high^~M.high&C.high,a.low=R.low^~M.low&C.low}a=r[0];var T=h[c];a.high^=T.high,a.low^=T.low}},_doFinalize:function(){var t=this._data,r=t.words,n=(this._nDataBytes,8*t.sigBytes),o=32*this.blockSize;r[n>>>5]|=1<<24-n%32,r[(e.ceil((n+1)/o)*o>>>5)-1]|=128,t.sigBytes=4*r.length,this._process();for(var s=this._state,a=this.cfg.outputLength/8,c=a/8,u=[],l=0;l<c;l++){var h=s[l],f=h.high,p=h.low;f=16711935&(f<<8|f>>>24)|4278255360&(f<<24|f>>>8),p=16711935&(p<<8|p>>>24)|4278255360&(p<<24|p>>>8),u.push(p),u.push(f)}return new i.init(u,a)},clone:function(){for(var t=o.clone.call(this),e=t._state=this._state.slice(0),r=0;r<25;r++)e[r]=e[r].clone();return t}});r.SHA3=o._createHelper(p),r.HmacSHA3=o._createHmacHelper(p)}(Math),t.SHA3}))},e7d0:function(t,e,r){"use strict";function n(t){if(!(this instanceof n))return new n(t);this.aliasToTopic={},this.max=t}n.prototype.put=function(t,e){return!(0===e||e>this.max)&&(this.aliasToTopic[e]=t,this.length=Object.keys(this.aliasToTopic).length,!0)},n.prototype.getTopicByAlias=function(t){return this.aliasToTopic[t]},n.prototype.clear=function(){this.aliasToTopic={}},t.exports=n},e7fc:function(t,e,r){"use strict";(function(e){const n=r("df86"),i=r("ea08"),o=r("0b16"),s=r("53a8"),a=r("34eb")("mqttjs"),c={};function u(t){let e;t.auth&&(e=t.auth.match(/^(.+):(.+)$/),e?(t.username=e[1],t.password=e[2]):t.username=t.auth)}function l(t,e){if(a("connecting to an MQTT broker..."),"object"!==typeof t||e||(e=t,t=null),e=e||{},t){const r=o.parse(t,!0);if(null!=r.port&&(r.port=Number(r.port)),e=s(r,e),null===e.protocol)throw new Error("Missing protocol");e.protocol=e.protocol.replace(/:$/,"")}if(u(e),e.query&&"string"===typeof e.query.clientId&&(e.clientId=e.query.clientId),e.cert&&e.key){if(!e.protocol)throw new Error("Missing secure protocol key");if(-1===["mqtts","wss","wxs","alis"].indexOf(e.protocol))switch(e.protocol){case"mqtt":e.protocol="mqtts";break;case"ws":e.protocol="wss";break;case"wx":e.protocol="wxs";break;case"ali":e.protocol="alis";break;default:throw new Error('Unknown protocol for secure connection: "'+e.protocol+'"!')}}if(!c[e.protocol]){const t=-1!==["mqtts","wss"].indexOf(e.protocol);e.protocol=["mqtt","mqtts","ws","wss","wx","wxs","ali","alis"].filter((function(e,r){return(!t||r%2!==0)&&"function"===typeof c[e]}))[0]}if(!1===e.clean&&!e.clientId)throw new Error("Missing clientId for unclean clients");function r(t){return e.servers&&(t._reconnectCount&&t._reconnectCount!==e.servers.length||(t._reconnectCount=0),e.host=e.servers[t._reconnectCount].host,e.port=e.servers[t._reconnectCount].port,e.protocol=e.servers[t._reconnectCount].protocol?e.servers[t._reconnectCount].protocol:e.defaultProtocol,e.hostname=e.host,t._reconnectCount++),a("calling streambuilder for",e.protocol),c[e.protocol](t,e)}e.protocol&&(e.defaultProtocol=e.protocol);const i=new n(r,e);return i.on("error",(function(){})),i}"undefined"!==typeof e&&"browser"!==e.title||"function"!==typeof r?(c.mqtt=r("05ee"),c.tcp=r("05ee"),c.ssl=r("fe3c"),c.tls=r("fe3c"),c.mqtts=r("fe3c")):(c.wx=r("8311"),c.wxs=r("8311"),c.ali=r("c4c7"),c.alis=r("c4c7")),c.ws=r("fcb9"),c.wss=r("fcb9"),t.exports=l,t.exports.connect=l,t.exports.MqttClient=n,t.exports.Store=i}).call(this,r("4362"))},e937:function(t,e,r){"use strict";function n(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function i(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?n(Object(r),!0).forEach((function(e){o(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function o(t,e,r){return e=u(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function s(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function a(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,u(n.key),n)}}function c(t,e,r){return e&&a(t.prototype,e),r&&a(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function u(t){var e=l(t,"string");return"symbol"===typeof e?e:String(e)}function l(t,e){if("object"!==typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var h=r("b639"),f=h.Buffer,p=r(6),d=p.inspect,y=d&&d.custom||"inspect";function b(t,e,r){f.prototype.copy.call(t,e,r)}t.exports=function(){function t(){s(this,t),this.head=null,this.tail=null,this.length=0}return c(t,[{key:"push",value:function(t){var e={data:t,next:null};this.length>0?this.tail.next=e:this.head=e,this.tail=e,++this.length}},{key:"unshift",value:function(t){var e={data:t,next:this.head};0===this.length&&(this.tail=e),this.head=e,++this.length}},{key:"shift",value:function(){if(0!==this.length){var t=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,t}}},{key:"clear",value:function(){this.head=this.tail=null,this.length=0}},{key:"join",value:function(t){if(0===this.length)return"";var e=this.head,r=""+e.data;while(e=e.next)r+=t+e.data;return r}},{key:"concat",value:function(t){if(0===this.length)return f.alloc(0);var e=f.allocUnsafe(t>>>0),r=this.head,n=0;while(r)b(r.data,e,n),n+=r.data.length,r=r.next;return e}},{key:"consume",value:function(t,e){var r;return t<this.head.data.length?(r=this.head.data.slice(0,t),this.head.data=this.head.data.slice(t)):r=t===this.head.data.length?this.shift():e?this._getString(t):this._getBuffer(t),r}},{key:"first",value:function(){return this.head.data}},{key:"_getString",value:function(t){var e=this.head,r=1,n=e.data;t-=n.length;while(e=e.next){var i=e.data,o=t>i.length?i.length:t;if(o===i.length?n+=i:n+=i.slice(0,t),t-=o,0===t){o===i.length?(++r,e.next?this.head=e.next:this.head=this.tail=null):(this.head=e,e.data=i.slice(o));break}++r}return this.length-=r,n}},{key:"_getBuffer",value:function(t){var e=f.allocUnsafe(t),r=this.head,n=1;r.data.copy(e),t-=r.data.length;while(r=r.next){var i=r.data,o=t>i.length?i.length:t;if(i.copy(e,e.length-t,0,o),t-=o,0===t){o===i.length?(++n,r.next?this.head=r.next:this.head=this.tail=null):(this.head=r,r.data=i.slice(o));break}++n}return this.length-=n,e}},{key:y,value:function(t,e){return d(this,i(i({},e),{},{depth:0,customInspect:!1}))}}]),t}()},ea08:function(t,e,r){"use strict";const n=r("53a8"),i=r("035d").Readable,o={objectMode:!0},s={clean:!0};function a(t){if(!(this instanceof a))return new a(t);this.options=t||{},this.options=n(s,t),this._inflights=new Map}a.prototype.put=function(t,e){return this._inflights.set(t.messageId,t),e&&e(),this},a.prototype.createStream=function(){const t=new i(o),e=[];let r=!1,n=0;return this._inflights.forEach((function(t,r){e.push(t)})),t._read=function(){!r&&n<e.length?this.push(e[n++]):this.push(null)},t.destroy=function(){if(r)return;const t=this;r=!0,setTimeout((function(){t.emit("close")}),0)},t},a.prototype.del=function(t,e){return t=this._inflights.get(t.messageId),t?(this._inflights.delete(t.messageId),e(null,t)):e&&e(new Error("missing packet")),this},a.prototype.get=function(t,e){return t=this._inflights.get(t.messageId),t?e(null,t):e&&e(new Error("missing packet")),this},a.prototype.close=function(t){this.options.clean&&(this._inflights=null),t&&t()},t.exports=a},edb3:function(t,e,r){"use strict";(function(e){function r(t,r){var o=this,a=this._readableState&&this._readableState.destroyed,c=this._writableState&&this._writableState.destroyed;return a||c?(r?r(t):t&&(this._writableState?this._writableState.errorEmitted||(this._writableState.errorEmitted=!0,e.nextTick(s,this,t)):e.nextTick(s,this,t)),this):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(t||null,(function(t){!r&&t?o._writableState?o._writableState.errorEmitted?e.nextTick(i,o):(o._writableState.errorEmitted=!0,e.nextTick(n,o,t)):e.nextTick(n,o,t):r?(e.nextTick(i,o),r(t)):e.nextTick(i,o)})),this)}function n(t,e){s(t,e),i(t)}function i(t){t._writableState&&!t._writableState.emitClose||t._readableState&&!t._readableState.emitClose||t.emit("close")}function o(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finalCalled=!1,this._writableState.prefinished=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)}function s(t,e){t.emit("error",e)}function a(t,e){var r=t._readableState,n=t._writableState;r&&r.autoDestroy||n&&n.autoDestroy?t.destroy(e):t.emit("error",e)}t.exports={destroy:r,undestroy:o,errorOrDestroy:a}}).call(this,r("4362"))},f0cb:function(t,e,r){(function(e){const r=65536,n={},i=e.isBuffer(e.from([1,2]).subarray(0,1));function o(t){const r=e.allocUnsafe(2);return r.writeUInt8(t>>8,0),r.writeUInt8(255&t,1),r}function s(){for(let t=0;t<r;t++)n[t]=o(t)}function a(t){const r=4;let n=0,o=0;const s=e.allocUnsafe(r);do{n=t%128|0,t=t/128|0,t>0&&(n|=128),s.writeUInt8(n,o++)}while(t>0&&o<r);return t>0&&(o=0),i?s.subarray(0,o):s.slice(0,o)}function c(t){const r=e.allocUnsafe(4);return r.writeUInt32BE(t,0),r}t.exports={cache:n,generateCache:s,generateNumber:o,genBufVariableByteInt:a,generate4ByteBuffer:c}}).call(this,r("b639").Buffer)},f177:function(t,e,r){"use strict";var n=r("5402"),i=r("a29f"),o=r("bbc7"),s=Object.prototype.hasOwnProperty,a={brackets:function(t){return t+"[]"},comma:"comma",indices:function(t,e){return t+"["+e+"]"},repeat:function(t){return t}},c=Array.isArray,u=Array.prototype.push,l=function(t,e){u.apply(t,c(e)?e:[e])},h=Date.prototype.toISOString,f=o["default"],p={addQueryPrefix:!1,allowDots:!1,charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encoder:i.encode,encodeValuesOnly:!1,format:f,formatter:o.formatters[f],indices:!1,serializeDate:function(t){return h.call(t)},skipNulls:!1,strictNullHandling:!1},d=function(t){return"string"===typeof t||"number"===typeof t||"boolean"===typeof t||"symbol"===typeof t||"bigint"===typeof t},y={},b=function t(e,r,o,s,a,u,h,f,b,g,m,v,_,w,S,E){var k=e,A=E,x=0,P=!1;while(void 0!==(A=A.get(y))&&!P){var O=A.get(e);if(x+=1,"undefined"!==typeof O){if(O===x)throw new RangeError("Cyclic object value");P=!0}"undefined"===typeof A.get(y)&&(x=0)}if("function"===typeof f?k=f(r,k):k instanceof Date?k=m(k):"comma"===o&&c(k)&&(k=i.maybeMap(k,(function(t){return t instanceof Date?m(t):t}))),null===k){if(a)return h&&!w?h(r,p.encoder,S,"key",v):r;k=""}if(d(k)||i.isBuffer(k)){if(h){var R=w?r:h(r,p.encoder,S,"key",v);return[_(R)+"="+_(h(k,p.encoder,S,"value",v))]}return[_(r)+"="+_(String(k))]}var M,C=[];if("undefined"===typeof k)return C;if("comma"===o&&c(k))w&&h&&(k=i.maybeMap(k,h)),M=[{value:k.length>0?k.join(",")||null:void 0}];else if(c(f))M=f;else{var T=Object.keys(k);M=b?T.sort(b):T}for(var B=s&&c(k)&&1===k.length?r+"[]":r,I=0;I<M.length;++I){var j=M[I],N="object"===typeof j&&"undefined"!==typeof j.value?j.value:k[j];if(!u||null!==N){var L=c(k)?"function"===typeof o?o(B,j):B:B+(g?"."+j:"["+j+"]");E.set(e,x);var D=n();D.set(y,E),l(C,t(N,L,o,s,a,u,"comma"===o&&w&&c(k)?null:h,f,b,g,m,v,_,w,S,D))}}return C},g=function(t){if(!t)return p;if(null!==t.encoder&&"undefined"!==typeof t.encoder&&"function"!==typeof t.encoder)throw new TypeError("Encoder has to be a function.");var e=t.charset||p.charset;if("undefined"!==typeof t.charset&&"utf-8"!==t.charset&&"iso-8859-1"!==t.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var r=o["default"];if("undefined"!==typeof t.format){if(!s.call(o.formatters,t.format))throw new TypeError("Unknown format option provided.");r=t.format}var n=o.formatters[r],i=p.filter;return("function"===typeof t.filter||c(t.filter))&&(i=t.filter),{addQueryPrefix:"boolean"===typeof t.addQueryPrefix?t.addQueryPrefix:p.addQueryPrefix,allowDots:"undefined"===typeof t.allowDots?p.allowDots:!!t.allowDots,charset:e,charsetSentinel:"boolean"===typeof t.charsetSentinel?t.charsetSentinel:p.charsetSentinel,delimiter:"undefined"===typeof t.delimiter?p.delimiter:t.delimiter,encode:"boolean"===typeof t.encode?t.encode:p.encode,encoder:"function"===typeof t.encoder?t.encoder:p.encoder,encodeValuesOnly:"boolean"===typeof t.encodeValuesOnly?t.encodeValuesOnly:p.encodeValuesOnly,filter:i,format:r,formatter:n,serializeDate:"function"===typeof t.serializeDate?t.serializeDate:p.serializeDate,skipNulls:"boolean"===typeof t.skipNulls?t.skipNulls:p.skipNulls,sort:"function"===typeof t.sort?t.sort:null,strictNullHandling:"boolean"===typeof t.strictNullHandling?t.strictNullHandling:p.strictNullHandling}};t.exports=function(t,e){var r,i,o=t,s=g(e);"function"===typeof s.filter?(i=s.filter,o=i("",o)):c(s.filter)&&(i=s.filter,r=i);var u,h=[];if("object"!==typeof o||null===o)return"";u=e&&e.arrayFormat in a?e.arrayFormat:e&&"indices"in e?e.indices?"indices":"repeat":"indices";var f=a[u];if(e&&"commaRoundTrip"in e&&"boolean"!==typeof e.commaRoundTrip)throw new TypeError("`commaRoundTrip` must be a boolean, or absent");var p="comma"===f&&e&&e.commaRoundTrip;r||(r=Object.keys(o)),s.sort&&r.sort(s.sort);for(var d=n(),y=0;y<r.length;++y){var m=r[y];s.skipNulls&&null===o[m]||l(h,b(o[m],m,f,p,s.strictNullHandling,s.skipNulls,s.encode?s.encoder:null,s.filter,s.sort,s.allowDots,s.serializeDate,s.format,s.formatter,s.encodeValuesOnly,s.charset,d))}var v=h.join(s.delimiter),_=!0===s.addQueryPrefix?"?":"";return s.charsetSentinel&&("iso-8859-1"===s.charset?_+="utf8=%26%2310003%3B&":_+="utf8=%E2%9C%93&"),v.length>0?_+v:""}},f214:function(t,e,r){e=t.exports=r("0e8b"),e.Stream=e,e.Readable=e,e.Writable=r("f6ba"),e.Duplex=r("a493"),e.Transform=r("fe34"),e.PassThrough=r("9d37"),e.finished=r("bf09"),e.pipeline=r("386b")},f482:function(t,e,r){"use strict";(function(e){function r(t,r){var o=this,a=this._readableState&&this._readableState.destroyed,c=this._writableState&&this._writableState.destroyed;return a||c?(r?r(t):t&&(this._writableState?this._writableState.errorEmitted||(this._writableState.errorEmitted=!0,e.nextTick(s,this,t)):e.nextTick(s,this,t)),this):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(t||null,(function(t){!r&&t?o._writableState?o._writableState.errorEmitted?e.nextTick(i,o):(o._writableState.errorEmitted=!0,e.nextTick(n,o,t)):e.nextTick(n,o,t):r?(e.nextTick(i,o),r(t)):e.nextTick(i,o)})),this)}function n(t,e){s(t,e),i(t)}function i(t){t._writableState&&!t._writableState.emitClose||t._readableState&&!t._readableState.emitClose||t.emit("close")}function o(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finalCalled=!1,this._writableState.prefinished=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)}function s(t,e){t.emit("error",e)}function a(t,e){var r=t._readableState,n=t._writableState;r&&r.autoDestroy||n&&n.autoDestroy?t.destroy(e):t.emit("error",e)}t.exports={destroy:r,undestroy:o,errorOrDestroy:a}}).call(this,r("4362"))},f4ea:function(t,e,r){(function(e,n,i){t.exports=n(r("21bf"),r("38ba"))})(0,(function(t){return t.mode.CTR=function(){var e=t.lib.BlockCipherMode.extend(),r=e.Encryptor=e.extend({processBlock:function(t,e){var r=this._cipher,n=r.blockSize,i=this._iv,o=this._counter;i&&(o=this._counter=i.slice(0),this._iv=void 0);var s=o.slice(0);r.encryptBlock(s,0),o[n-1]=o[n-1]+1|0;for(var a=0;a<n;a++)t[e+a]^=s[a]}});return e.Decryptor=r,e}(),t.mode.CTR}))},f688:function(t,e,r){"use strict";function n(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function i(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?n(Object(r),!0).forEach((function(e){o(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function o(t,e,r){return e=u(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function s(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function a(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,u(n.key),n)}}function c(t,e,r){return e&&a(t.prototype,e),r&&a(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function u(t){var e=l(t,"string");return"symbol"===typeof e?e:String(e)}function l(t,e){if("object"!==typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var h=r("b639"),f=h.Buffer,p=r(4),d=p.inspect,y=d&&d.custom||"inspect";function b(t,e,r){f.prototype.copy.call(t,e,r)}t.exports=function(){function t(){s(this,t),this.head=null,this.tail=null,this.length=0}return c(t,[{key:"push",value:function(t){var e={data:t,next:null};this.length>0?this.tail.next=e:this.head=e,this.tail=e,++this.length}},{key:"unshift",value:function(t){var e={data:t,next:this.head};0===this.length&&(this.tail=e),this.head=e,++this.length}},{key:"shift",value:function(){if(0!==this.length){var t=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,t}}},{key:"clear",value:function(){this.head=this.tail=null,this.length=0}},{key:"join",value:function(t){if(0===this.length)return"";var e=this.head,r=""+e.data;while(e=e.next)r+=t+e.data;return r}},{key:"concat",value:function(t){if(0===this.length)return f.alloc(0);var e=f.allocUnsafe(t>>>0),r=this.head,n=0;while(r)b(r.data,e,n),n+=r.data.length,r=r.next;return e}},{key:"consume",value:function(t,e){var r;return t<this.head.data.length?(r=this.head.data.slice(0,t),this.head.data=this.head.data.slice(t)):r=t===this.head.data.length?this.shift():e?this._getString(t):this._getBuffer(t),r}},{key:"first",value:function(){return this.head.data}},{key:"_getString",value:function(t){var e=this.head,r=1,n=e.data;t-=n.length;while(e=e.next){var i=e.data,o=t>i.length?i.length:t;if(o===i.length?n+=i:n+=i.slice(0,t),t-=o,0===t){o===i.length?(++r,e.next?this.head=e.next:this.head=this.tail=null):(this.head=e,e.data=i.slice(o));break}++r}return this.length-=r,n}},{key:"_getBuffer",value:function(t){var e=f.allocUnsafe(t),r=this.head,n=1;r.data.copy(e),t-=r.data.length;while(r=r.next){var i=r.data,o=t>i.length?i.length:t;if(i.copy(e,e.length-t,0,o),t-=o,0===t){o===i.length?(++n,r.next?this.head=r.next:this.head=this.tail=null):(this.head=r,r.data=i.slice(o));break}++n}return this.length-=n,e}},{key:y,value:function(t,e){return d(this,i(i({},e),{},{depth:0,customInspect:!1}))}}]),t}()},f6ba:function(t,e,r){"use strict";(function(e,n){function i(t){var e=this;this.next=null,this.entry=null,this.finish=function(){z(e,t)}}var o;t.exports=O,O.WritableState=P;var s={deprecate:r("b7d1")},a=r("b98b"),c=r("b639").Buffer,u=("undefined"!==typeof e?e:"undefined"!==typeof window?window:"undefined"!==typeof self?self:{}).Uint8Array||function(){};function l(t){return c.from(t)}function h(t){return c.isBuffer(t)||t instanceof u}var f,p=r("f482"),d=r("86c6"),y=d.getHighWaterMark,b=r("9bfc").codes,g=b.ERR_INVALID_ARG_TYPE,m=b.ERR_METHOD_NOT_IMPLEMENTED,v=b.ERR_MULTIPLE_CALLBACK,_=b.ERR_STREAM_CANNOT_PIPE,w=b.ERR_STREAM_DESTROYED,S=b.ERR_STREAM_NULL_VALUES,E=b.ERR_STREAM_WRITE_AFTER_END,k=b.ERR_UNKNOWN_ENCODING,A=p.errorOrDestroy;function x(){}function P(t,e,n){o=o||r("a493"),t=t||{},"boolean"!==typeof n&&(n=e instanceof o),this.objectMode=!!t.objectMode,n&&(this.objectMode=this.objectMode||!!t.writableObjectMode),this.highWaterMark=y(this,t,"writableHighWaterMark",n),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var s=!1===t.decodeStrings;this.decodeStrings=!s,this.defaultEncoding=t.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(t){N(e,t)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.emitClose=!1!==t.emitClose,this.autoDestroy=!!t.autoDestroy,this.bufferedRequestCount=0,this.corkedRequestsFree=new i(this)}function O(t){o=o||r("a493");var e=this instanceof o;if(!e&&!f.call(O,this))return new O(t);this._writableState=new P(t,this,e),this.writable=!0,t&&("function"===typeof t.write&&(this._write=t.write),"function"===typeof t.writev&&(this._writev=t.writev),"function"===typeof t.destroy&&(this._destroy=t.destroy),"function"===typeof t.final&&(this._final=t.final)),a.call(this)}function R(t,e){var r=new E;A(t,r),n.nextTick(e,r)}function M(t,e,r,i){var o;return null===r?o=new S:"string"===typeof r||e.objectMode||(o=new g("chunk",["string","Buffer"],r)),!o||(A(t,o),n.nextTick(i,o),!1)}function C(t,e,r){return t.objectMode||!1===t.decodeStrings||"string"!==typeof e||(e=c.from(e,r)),e}function T(t,e,r,n,i,o){if(!r){var s=C(e,n,i);n!==s&&(r=!0,i="buffer",n=s)}var a=e.objectMode?1:n.length;e.length+=a;var c=e.length<e.highWaterMark;if(c||(e.needDrain=!0),e.writing||e.corked){var u=e.lastBufferedRequest;e.lastBufferedRequest={chunk:n,encoding:i,isBuf:r,callback:o,next:null},u?u.next=e.lastBufferedRequest:e.bufferedRequest=e.lastBufferedRequest,e.bufferedRequestCount+=1}else B(t,e,!1,a,n,i,o);return c}function B(t,e,r,n,i,o,s){e.writelen=n,e.writecb=s,e.writing=!0,e.sync=!0,e.destroyed?e.onwrite(new w("write")):r?t._writev(i,e.onwrite):t._write(i,o,e.onwrite),e.sync=!1}function I(t,e,r,i,o){--e.pendingcb,r?(n.nextTick(o,i),n.nextTick(W,t,e),t._writableState.errorEmitted=!0,A(t,i)):(o(i),t._writableState.errorEmitted=!0,A(t,i),W(t,e))}function j(t){t.writing=!1,t.writecb=null,t.length-=t.writelen,t.writelen=0}function N(t,e){var r=t._writableState,i=r.sync,o=r.writecb;if("function"!==typeof o)throw new v;if(j(r),e)I(t,r,i,e,o);else{var s=U(r)||t.destroyed;s||r.corked||r.bufferProcessing||!r.bufferedRequest||F(t,r),i?n.nextTick(L,t,r,s,o):L(t,r,s,o)}}function L(t,e,r,n){r||D(t,e),e.pendingcb--,n(),W(t,e)}function D(t,e){0===e.length&&e.needDrain&&(e.needDrain=!1,t.emit("drain"))}function F(t,e){e.bufferProcessing=!0;var r=e.bufferedRequest;if(t._writev&&r&&r.next){var n=e.bufferedRequestCount,o=new Array(n),s=e.corkedRequestsFree;s.entry=r;var a=0,c=!0;while(r)o[a]=r,r.isBuf||(c=!1),r=r.next,a+=1;o.allBuffers=c,B(t,e,!0,e.length,o,"",s.finish),e.pendingcb++,e.lastBufferedRequest=null,s.next?(e.corkedRequestsFree=s.next,s.next=null):e.corkedRequestsFree=new i(e),e.bufferedRequestCount=0}else{while(r){var u=r.chunk,l=r.encoding,h=r.callback,f=e.objectMode?1:u.length;if(B(t,e,!1,f,u,l,h),r=r.next,e.bufferedRequestCount--,e.writing)break}null===r&&(e.lastBufferedRequest=null)}e.bufferedRequest=r,e.bufferProcessing=!1}function U(t){return t.ending&&0===t.length&&null===t.bufferedRequest&&!t.finished&&!t.writing}function H(t,e){t._final((function(r){e.pendingcb--,r&&A(t,r),e.prefinished=!0,t.emit("prefinish"),W(t,e)}))}function q(t,e){e.prefinished||e.finalCalled||("function"!==typeof t._final||e.destroyed?(e.prefinished=!0,t.emit("prefinish")):(e.pendingcb++,e.finalCalled=!0,n.nextTick(H,t,e)))}function W(t,e){var r=U(e);if(r&&(q(t,e),0===e.pendingcb&&(e.finished=!0,t.emit("finish"),e.autoDestroy))){var n=t._readableState;(!n||n.autoDestroy&&n.endEmitted)&&t.destroy()}return r}function K(t,e,r){e.ending=!0,W(t,e),r&&(e.finished?n.nextTick(r):t.once("finish",r)),e.ended=!0,t.writable=!1}function z(t,e,r){var n=t.entry;t.entry=null;while(n){var i=n.callback;e.pendingcb--,i(r),n=n.next}e.corkedRequestsFree.next=t}r("3fb5")(O,a),P.prototype.getBuffer=function(){var t=this.bufferedRequest,e=[];while(t)e.push(t),t=t.next;return e},function(){try{Object.defineProperty(P.prototype,"buffer",{get:s.deprecate((function(){return this.getBuffer()}),"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch(t){}}(),"function"===typeof Symbol&&Symbol.hasInstance&&"function"===typeof Function.prototype[Symbol.hasInstance]?(f=Function.prototype[Symbol.hasInstance],Object.defineProperty(O,Symbol.hasInstance,{value:function(t){return!!f.call(this,t)||this===O&&(t&&t._writableState instanceof P)}})):f=function(t){return t instanceof this},O.prototype.pipe=function(){A(this,new _)},O.prototype.write=function(t,e,r){var n=this._writableState,i=!1,o=!n.objectMode&&h(t);return o&&!c.isBuffer(t)&&(t=l(t)),"function"===typeof e&&(r=e,e=null),o?e="buffer":e||(e=n.defaultEncoding),"function"!==typeof r&&(r=x),n.ending?R(this,r):(o||M(this,n,t,r))&&(n.pendingcb++,i=T(this,n,o,t,e,r)),i},O.prototype.cork=function(){this._writableState.corked++},O.prototype.uncork=function(){var t=this._writableState;t.corked&&(t.corked--,t.writing||t.corked||t.bufferProcessing||!t.bufferedRequest||F(this,t))},O.prototype.setDefaultEncoding=function(t){if("string"===typeof t&&(t=t.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((t+"").toLowerCase())>-1))throw new k(t);return this._writableState.defaultEncoding=t,this},Object.defineProperty(O.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}}),Object.defineProperty(O.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),O.prototype._write=function(t,e,r){r(new m("_write()"))},O.prototype._writev=null,O.prototype.end=function(t,e,r){var n=this._writableState;return"function"===typeof t?(r=t,t=null,e=null):"function"===typeof e&&(r=e,e=null),null!==t&&void 0!==t&&this.write(t,e),n.corked&&(n.corked=1,this.uncork()),n.ending||K(this,n,r),this},Object.defineProperty(O.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}}),Object.defineProperty(O.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._writableState&&this._writableState.destroyed},set:function(t){this._writableState&&(this._writableState.destroyed=t)}}),O.prototype.destroy=p.destroy,O.prototype._undestroy=p.undestroy,O.prototype._destroy=function(t,e){e(t)}}).call(this,r("c8ba"),r("4362"))},f9c1:function(t,e){function r(t){var e=t._readableState;return e?e.objectMode||"number"===typeof t._duplexState?t.read():t.read(n(e)):null}function n(t){return t.buffer.length?t.buffer.head?t.buffer.head.data.length:t.buffer[0].length:t.length}t.exports=r},faa1:function(t,e,r){"use strict";var n,i="object"===typeof Reflect?Reflect:null,o=i&&"function"===typeof i.apply?i.apply:function(t,e,r){return Function.prototype.apply.call(t,e,r)};function s(t){console&&console.warn&&console.warn(t)}n=i&&"function"===typeof i.ownKeys?i.ownKeys:Object.getOwnPropertySymbols?function(t){return Object.getOwnPropertyNames(t).concat(Object.getOwnPropertySymbols(t))}:function(t){return Object.getOwnPropertyNames(t)};var a=Number.isNaN||function(t){return t!==t};function c(){c.init.call(this)}t.exports=c,t.exports.once=_,c.EventEmitter=c,c.prototype._events=void 0,c.prototype._eventsCount=0,c.prototype._maxListeners=void 0;var u=10;function l(t){if("function"!==typeof t)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof t)}function h(t){return void 0===t._maxListeners?c.defaultMaxListeners:t._maxListeners}function f(t,e,r,n){var i,o,a;if(l(r),o=t._events,void 0===o?(o=t._events=Object.create(null),t._eventsCount=0):(void 0!==o.newListener&&(t.emit("newListener",e,r.listener?r.listener:r),o=t._events),a=o[e]),void 0===a)a=o[e]=r,++t._eventsCount;else if("function"===typeof a?a=o[e]=n?[r,a]:[a,r]:n?a.unshift(r):a.push(r),i=h(t),i>0&&a.length>i&&!a.warned){a.warned=!0;var c=new Error("Possible EventEmitter memory leak detected. "+a.length+" "+String(e)+" listeners added. Use emitter.setMaxListeners() to increase limit");c.name="MaxListenersExceededWarning",c.emitter=t,c.type=e,c.count=a.length,s(c)}return t}function p(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function d(t,e,r){var n={fired:!1,wrapFn:void 0,target:t,type:e,listener:r},i=p.bind(n);return i.listener=r,n.wrapFn=i,i}function y(t,e,r){var n=t._events;if(void 0===n)return[];var i=n[e];return void 0===i?[]:"function"===typeof i?r?[i.listener||i]:[i]:r?v(i):g(i,i.length)}function b(t){var e=this._events;if(void 0!==e){var r=e[t];if("function"===typeof r)return 1;if(void 0!==r)return r.length}return 0}function g(t,e){for(var r=new Array(e),n=0;n<e;++n)r[n]=t[n];return r}function m(t,e){for(;e+1<t.length;e++)t[e]=t[e+1];t.pop()}function v(t){for(var e=new Array(t.length),r=0;r<e.length;++r)e[r]=t[r].listener||t[r];return e}function _(t,e){return new Promise((function(r,n){function i(r){t.removeListener(e,o),n(r)}function o(){"function"===typeof t.removeListener&&t.removeListener("error",i),r([].slice.call(arguments))}S(t,e,o,{once:!0}),"error"!==e&&w(t,i,{once:!0})}))}function w(t,e,r){"function"===typeof t.on&&S(t,"error",e,r)}function S(t,e,r,n){if("function"===typeof t.on)n.once?t.once(e,r):t.on(e,r);else{if("function"!==typeof t.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof t);t.addEventListener(e,(function i(o){n.once&&t.removeEventListener(e,i),r(o)}))}}Object.defineProperty(c,"defaultMaxListeners",{enumerable:!0,get:function(){return u},set:function(t){if("number"!==typeof t||t<0||a(t))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+t+".");u=t}}),c.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},c.prototype.setMaxListeners=function(t){if("number"!==typeof t||t<0||a(t))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+t+".");return this._maxListeners=t,this},c.prototype.getMaxListeners=function(){return h(this)},c.prototype.emit=function(t){for(var e=[],r=1;r<arguments.length;r++)e.push(arguments[r]);var n="error"===t,i=this._events;if(void 0!==i)n=n&&void 0===i.error;else if(!n)return!1;if(n){var s;if(e.length>0&&(s=e[0]),s instanceof Error)throw s;var a=new Error("Unhandled error."+(s?" ("+s.message+")":""));throw a.context=s,a}var c=i[t];if(void 0===c)return!1;if("function"===typeof c)o(c,this,e);else{var u=c.length,l=g(c,u);for(r=0;r<u;++r)o(l[r],this,e)}return!0},c.prototype.addListener=function(t,e){return f(this,t,e,!1)},c.prototype.on=c.prototype.addListener,c.prototype.prependListener=function(t,e){return f(this,t,e,!0)},c.prototype.once=function(t,e){return l(e),this.on(t,d(this,t,e)),this},c.prototype.prependOnceListener=function(t,e){return l(e),this.prependListener(t,d(this,t,e)),this},c.prototype.removeListener=function(t,e){var r,n,i,o,s;if(l(e),n=this._events,void 0===n)return this;if(r=n[t],void 0===r)return this;if(r===e||r.listener===e)0===--this._eventsCount?this._events=Object.create(null):(delete n[t],n.removeListener&&this.emit("removeListener",t,r.listener||e));else if("function"!==typeof r){for(i=-1,o=r.length-1;o>=0;o--)if(r[o]===e||r[o].listener===e){s=r[o].listener,i=o;break}if(i<0)return this;0===i?r.shift():m(r,i),1===r.length&&(n[t]=r[0]),void 0!==n.removeListener&&this.emit("removeListener",t,s||e)}return this},c.prototype.off=c.prototype.removeListener,c.prototype.removeAllListeners=function(t){var e,r,n;if(r=this._events,void 0===r)return this;if(void 0===r.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==r[t]&&(0===--this._eventsCount?this._events=Object.create(null):delete r[t]),this;if(0===arguments.length){var i,o=Object.keys(r);for(n=0;n<o.length;++n)i=o[n],"removeListener"!==i&&this.removeAllListeners(i);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(e=r[t],"function"===typeof e)this.removeListener(t,e);else if(void 0!==e)for(n=e.length-1;n>=0;n--)this.removeListener(t,e[n]);return this},c.prototype.listeners=function(t){return y(this,t,!0)},c.prototype.rawListeners=function(t){return y(this,t,!1)},c.listenerCount=function(t,e){return"function"===typeof t.listenerCount?t.listenerCount(e):b.call(t,e)},c.prototype.listenerCount=b,c.prototype.eventNames=function(){return this._eventsCount>0?n(this._events):[]}},fbd7:function(t,e,r){"use strict";function n(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.__proto__=e}var i={};function o(t,e,r){function o(t,r,n){return"string"===typeof e?e:e(t,r,n)}r||(r=Error);var s=function(t){function e(e,r,n){return t.call(this,o(e,r,n))||this}return n(e,t),e}(r);s.prototype.name=r.name,s.prototype.code=t,i[t]=s}function s(t,e){if(Array.isArray(t)){var r=t.length;return t=t.map((function(t){return String(t)})),r>2?"one of ".concat(e," ").concat(t.slice(0,r-1).join(", "),", or ")+t[r-1]:2===r?"one of ".concat(e," ").concat(t[0]," or ").concat(t[1]):"of ".concat(e," ").concat(t[0])}return"of ".concat(e," ").concat(String(t))}function a(t,e,r){return t.substr(!r||r<0?0:+r,e.length)===e}function c(t,e,r){return(void 0===r||r>t.length)&&(r=t.length),t.substring(r-e.length,r)===e}function u(t,e,r){return"number"!==typeof r&&(r=0),!(r+e.length>t.length)&&-1!==t.indexOf(e,r)}o("ERR_INVALID_OPT_VALUE",(function(t,e){return'The value "'+e+'" is invalid for option "'+t+'"'}),TypeError),o("ERR_INVALID_ARG_TYPE",(function(t,e,r){var n,i;if("string"===typeof e&&a(e,"not ")?(n="must not be",e=e.replace(/^not /,"")):n="must be",c(t," argument"))i="The ".concat(t," ").concat(n," ").concat(s(e,"type"));else{var o=u(t,".")?"property":"argument";i='The "'.concat(t,'" ').concat(o," ").concat(n," ").concat(s(e,"type"))}return i+=". Received type ".concat(typeof r),i}),TypeError),o("ERR_STREAM_PUSH_AFTER_EOF","stream.push() after EOF"),o("ERR_METHOD_NOT_IMPLEMENTED",(function(t){return"The "+t+" method is not implemented"})),o("ERR_STREAM_PREMATURE_CLOSE","Premature close"),o("ERR_STREAM_DESTROYED",(function(t){return"Cannot call "+t+" after a stream was destroyed"})),o("ERR_MULTIPLE_CALLBACK","Callback called multiple times"),o("ERR_STREAM_CANNOT_PIPE","Cannot pipe, not readable"),o("ERR_STREAM_WRITE_AFTER_END","write after end"),o("ERR_STREAM_NULL_VALUES","May not write null values to stream",TypeError),o("ERR_UNKNOWN_ENCODING",(function(t){return"Unknown encoding: "+t}),TypeError),o("ERR_STREAM_UNSHIFT_AFTER_END_EVENT","stream.unshift() after end event"),t.exports.codes=i},fcb9:function(t,e,r){"use strict";(function(e){const{Buffer:n}=r("b639"),i=r("a9b9"),o=r("34eb")("mqttjs:ws"),s=r("56ac"),a=r("035d").Transform,c=["rejectUnauthorized","ca","cert","key","pfx","passphrase"],u="undefined"!==typeof e&&"browser"===e.title||"function"===typeof r;function l(t,e){let r=t.protocol+"://"+t.hostname+":"+t.port+t.path;return"function"===typeof t.transformWsUrl&&(r=t.transformWsUrl(r,t,e)),r}function h(t){const e=t;return t.hostname||(e.hostname="localhost"),t.port||("wss"===t.protocol?e.port=443:e.port=80),t.path||(e.path="/"),t.wsOptions||(e.wsOptions={}),u||"wss"!==t.protocol||c.forEach((function(r){Object.prototype.hasOwnProperty.call(t,r)&&!Object.prototype.hasOwnProperty.call(t.wsOptions,r)&&(e.wsOptions[r]=t[r])})),e}function f(t){const e=h(t);if(e.hostname||(e.hostname=e.host),!e.hostname){if("undefined"===typeof document)throw new Error("Could not determine host. Specify host manually.");const t=new URL(document.URL);e.hostname=t.hostname,e.port||(e.port=t.port)}return void 0===e.objectMode&&(e.objectMode=!(!0===e.binary||void 0===e.binary)),e}function p(t,e,r){o("createWebSocket"),o("protocol: "+r.protocolId+" "+r.protocolVersion);const n="MQIsdp"===r.protocolId&&3===r.protocolVersion?"mqttv3.1":"mqtt";o("creating new Websocket for url: "+e+" and protocol: "+n);const s=new i(e,[n],r.wsOptions);return s}function d(t,e){const r="MQIsdp"===e.protocolId&&3===e.protocolVersion?"mqttv3.1":"mqtt",n=l(e,t),i=new WebSocket(n,[r]);return i.binaryType="arraybuffer",i}function y(t,e){o("streamBuilder");const r=h(e),n=l(r,t),s=p(t,n,r),a=i.createWebSocketStream(s,r.wsOptions);return a.url=n,s.on("close",()=>{a.destroy()}),a}function b(t,e){let r;o("browserStreamBuilder");const i=f(e),c=i.browserBufferSize||524288,u=e.browserBufferTimeout||1e3,l=!e.objectMode,h=d(t,e),p=b(e,S,E);e.objectMode||(p._writev=w),p.on("close",()=>{h.close()});const y="undefined"!==typeof h.addEventListener;function b(t,e,r){const n=new a({objectModeMode:t.objectMode});return n._write=e,n._flush=r,n}function g(){r.setReadable(p),r.setWritable(p),r.emit("connect")}function m(){r.end(),r.destroy()}function v(t){r.destroy(t)}function _(t){let e=t.data;e=e instanceof ArrayBuffer?n.from(e):n.from(e,"utf8"),p.push(e)}function w(t,e){const r=new Array(t.length);for(let i=0;i<t.length;i++)"string"===typeof t[i].chunk?r[i]=n.from(t[i],"utf8"):r[i]=t[i].chunk;this._write(n.concat(r),"binary",e)}function S(t,e,r){h.bufferedAmount>c&&setTimeout(S,u,t,e,r),l&&"string"===typeof t&&(t=n.from(t,"utf8"));try{h.send(t)}catch(i){return r(i)}r()}function E(t){h.close(),t()}return h.readyState===h.OPEN?r=p:(r=r=s(void 0,void 0,e),e.objectMode||(r._writev=w),y?h.addEventListener("open",g):h.onopen=g),r.socket=h,y?(h.addEventListener("close",m),h.addEventListener("error",v),h.addEventListener("message",_)):(h.onclose=m,h.onerror=v,h.onmessage=_),r}t.exports=u?b:y}).call(this,r("4362"))},fe34:function(t,e,r){"use strict";t.exports=l;var n=r("9bfc").codes,i=n.ERR_METHOD_NOT_IMPLEMENTED,o=n.ERR_MULTIPLE_CALLBACK,s=n.ERR_TRANSFORM_ALREADY_TRANSFORMING,a=n.ERR_TRANSFORM_WITH_LENGTH_0,c=r("a493");function u(t,e){var r=this._transformState;r.transforming=!1;var n=r.writecb;if(null===n)return this.emit("error",new o);r.writechunk=null,r.writecb=null,null!=e&&this.push(e),n(t);var i=this._readableState;i.reading=!1,(i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}function l(t){if(!(this instanceof l))return new l(t);c.call(this,t),this._transformState={afterTransform:u.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,t&&("function"===typeof t.transform&&(this._transform=t.transform),"function"===typeof t.flush&&(this._flush=t.flush)),this.on("prefinish",h)}function h(){var t=this;"function"!==typeof this._flush||this._readableState.destroyed?f(this,null,null):this._flush((function(e,r){f(t,e,r)}))}function f(t,e,r){if(e)return t.emit("error",e);if(null!=r&&t.push(r),t._writableState.length)throw new a;if(t._transformState.transforming)throw new s;return t.push(null)}r("3fb5")(l,c),l.prototype.push=function(t,e){return this._transformState.needTransform=!1,c.prototype.push.call(this,t,e)},l.prototype._transform=function(t,e,r){r(new i("_transform()"))},l.prototype._write=function(t,e,r){var n=this._transformState;if(n.writecb=r,n.writechunk=t,n.writeencoding=e,!n.transforming){var i=this._readableState;(n.needTransform||i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}},l.prototype._read=function(t){var e=this._transformState;null===e.writechunk||e.transforming?e.needTransform=!0:(e.transforming=!0,this._transform(e.writechunk,e.writeencoding,e.afterTransform))},l.prototype._destroy=function(t,e){c.prototype._destroy.call(this,t,(function(t){e(t)}))}},fe3c:function(t,e,r){"use strict";const n=r(8),i=r(1),o=r("34eb")("mqttjs:tls");function s(t,e){e.port=e.port||8883,e.host=e.hostname||e.host||"localhost",0===i.isIP(e.host)&&(e.servername=e.host),e.rejectUnauthorized=!1!==e.rejectUnauthorized,delete e.path,o("port %d host %s rejectUnauthorized %b",e.port,e.host,e.rejectUnauthorized);const r=n.connect(e);function s(n){e.rejectUnauthorized&&t.emit("error",n),r.end()}return r.on("secureConnect",(function(){e.rejectUnauthorized&&!r.authorized?r.emit("error",new Error("TLS not authorized")):r.removeListener("error",s)})),r.on("error",s),r}t.exports=s}}]);