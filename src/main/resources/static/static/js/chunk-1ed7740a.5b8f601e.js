(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1ed7740a"],{"0f18":function(t,s,a){"use strict";a("31e8")},"31e8":function(t,s,a){},7229:function(t,s,a){"use strict";a.r(s);var i=function(){var t=this,s=t.$createElement,a=t._self._c||s;return a("div",{staticClass:"warp"},[a("div",{staticClass:"workbench"},t._l(t.linkList.data,(function(s,i){return a("el-card",{key:i,staticClass:"box-card cardItem"},[a("div",{staticClass:"linkItem",attrs:{title:"点击"+s.name},on:{click:function(a){return t.ClickTo(s)}}},[a("div",{staticClass:"linkName",style:{background:s.color}},[s.icon.includes("el-icon")?a("i",{class:s.icon,style:{"font-size":"18px"}}):a("svg-icon",{style:{"font-size":"18px"},attrs:{"icon-class":s.icon}}),a("span",{staticStyle:{"margin-left":"10px"}},[t._v(t._s(s.name))])],1),a("div",{staticClass:"linkcontent"},[a("div",{staticClass:"linkcontent-body",domProps:{innerHTML:t._s(s.content)}})])])])})),1),a("div",{staticClass:"workbench"},[a("el-button",{staticStyle:{margin:"10px"},attrs:{loading:t.loading,type:"primary"},on:{click:function(s){return t.$router.push("/login")}}},[t._v("返回登录")])],1),a("el-dialog",{attrs:{title:"初始化密码",visible:t.dialogVisible,width:"30%"},on:{"update:visible":function(s){t.dialogVisible=s}}},[a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("div",{staticClass:"baseInfo"},[a("el-form",{ref:"pwdformdata",staticClass:"custInfo",attrs:{model:t.pwdformdata,rules:t.pwdformdataRule,"label-width":"100px","auto-complete":"on"}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"密码",prop:"originalpassword"}},[a("div",{staticStyle:{position:"relative"}},[a("el-input",{attrs:{placeholder:"密码",type:t.passwordType1},model:{value:t.pwdformdata.originalpassword,callback:function(s){t.$set(t.pwdformdata,"originalpassword",s)},expression:"pwdformdata.originalpassword"}}),a("span",{staticClass:"show-pwd",on:{click:function(s){return t.showPwd("passwordType1")}}},[a("svg-icon",{attrs:{"icon-class":"password"===t.passwordType1?"eye":"eye-open"}})],1)],1)])],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"验证密码",prop:"password"}},[a("div",{staticStyle:{position:"relative"}},[a("el-input",{attrs:{placeholder:"确认密码",type:t.passwordType},model:{value:t.pwdformdata.password,callback:function(s){t.$set(t.pwdformdata,"password",s)},expression:"pwdformdata.password"}}),a("span",{staticClass:"show-pwd",on:{click:function(s){return t.showPwd("passwordType")}}},[a("svg-icon",{attrs:{"icon-class":"password"===t.passwordType?"eye":"eye-open"}})],1)],1)])],1)],1),a("el-row",[a("el-col",{attrs:{span:22,offset:2}},[a("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],staticStyle:{"background-color":"#1890ff"},attrs:{type:"primary",size:"small"},on:{click:function(s){return t.changPwd()}}},[t._v(" 修 改 密 码")]),a("el-button",{attrs:{size:"small"},on:{click:function(s){return t.resetBtn("pwdformdata")}}},[t._v("重置")])],1)],1)],1)],1)])])],1)},o=[],e=(a("ac1f"),a("00b4"),a("d3b7"),a("159b"),a("b775")),n={data:function(){var t=this,s=function(t,s,a){return s?/^[a-zA-Z0-9]+$/.test(s)?void a():a(new Error("密码最少6位数字和字母组合")):a(new Error("密码不能为空"))},a=function(s,a,i){return a?a!==t.pwdformdata.originalpassword?i(new Error("两次密码不一致")):void i():i(new Error("密码不能为空"))};return{linkList:{data:[{key:"S16",name:"数据库初始化",icon:"D05M07B1",color:"",url:"/initTable",content:"<div>客户信息</div> <div> 货品信息 </div> \n      <div> 送货单</div> <div>标签打印 </div> <div>场景</div> \n      <div>报表</div> "},{key:"S17",name:"初始化管理员密码",icon:"D05M07B1",color:"",url:"/initpassWord",content:"<div>默认密码</div> <div> 修改密码 </div>"}]},loading:null,dialogVisible:!1,pwdformdata:{password:"",originalpassword:""},listForm:{},pwdformdataRule:{originalpassword:[{required:!0,trigger:"blur",message:"密码不能为空"},{trigger:"blur",validator:s}],password:[{required:!0,trigger:"blur",message:"密码不能为空"},{trigger:"blur",validator:a}]},passwordType:"password",passwordType1:"password"}},created:function(){},mounted:function(){this.bindData()},methods:{bindData:function(){var t=["#2a7aff","#7ed321","#f31876","#f65a5a","#ff9800"];this.linkList.data.forEach((function(s,a){s.color=s.color?s.color:t[a%5]})),console.log(this.linkList)},ClickTo:function(t){var s=this;"/initpassWord"===t.url?this.dialogVisible=!0:(this.selRows=t,this.$confirm("此操作不可逆, 是否确认初始化?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){s.submitReq()})))},showPwd:function(t){var s=this;this.$nextTick((function(){"password"===s[t]?s[t]="":s[t]="password"}))},resetBtn:function(t){this.$refs[t].resetFields()},changPwd:function(){var t=this;this.$refs["pwdformdata"].validate((function(s){if(!s)return console.log("error submit!!"),!1;e["a"].get("/S16M91S1/initPass?password="+t.pwdformdata.password).then((function(s){200==s.data.code?(t.$message.success(s.data.msg||"密码修改成功"),t.$router.push({path:"/login"})):t.$message.warning(s.data.msg||"密码重置失败")}))}))},submitReq:function(){var t=this;this.loading=this.$loading({fullscreen:!0,text:"拼命加载中...",background:"rgba(0, 0, 0, 0.8)"}),e["a"].get(this.selRows.url).then((function(s){200==s.data.code?(t.loading.close(),t.$message.success(s.data.msg||"初始化成功")):(t.loading.close(),t.$message.warning(s.data.msg||"初始化失败"))})).catch((function(s){t.loading.close(),t.$message.error(s||"请求错误")}))}}},r=n,l=(a("0f18"),a("2877")),d=Object(l["a"])(r,i,o,!1,null,"84090ac4",null);s["default"]=d.exports}}]);