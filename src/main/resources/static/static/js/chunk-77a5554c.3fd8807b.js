(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-77a5554c"],{"03af":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),a("div",[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"350px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},"row-class-name":t.rowIndex},on:{"row-click":t.rowClick}},[1==t.multi?a("el-table-column",{attrs:{type:"selection",width:"40"}}):a("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(a){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),a("el-table-column",{attrs:{label:"用户账号",align:"center","min-width":"100px","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.username))])]}}])}),a("el-table-column",{attrs:{label:"姓名",align:"center","min-width":"80px","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.realname))])]}}])}),a("el-table-column",{attrs:{label:"手机",align:"center",prop:"mobile","min-width":"60"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.mobile?e.row.mobile:"-"))])]}}])}),a("el-table-column",{attrs:{label:"邮箱",align:"center","min-width":"60"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.email?e.row.email:"-"))])]}}])}),a("el-table-column",{attrs:{label:"身份",align:"center","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[0==e.row.isadmin?a("el-tag",{attrs:{size:"medium"}},[t._v("普通用户")]):a("el-tag",{attrs:{type:"warning",size:"medium"}},[t._v("管理员")])]}}])}),a("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createdate","min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("dateFormat")(e.row.createdate)))])]}}])})],1)],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},n=[],r=(a("e9c4"),a("4d90"),a("d3b7"),a("25f0"),a("99af"),a("b775")),s=a("333d"),o={components:{Pagination:s["a"]},props:["multi"],data:function(){return{title:"用户信息",listLoading:!0,lst:[],searchstr:" ",strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},created:function(){this.searchstr="",this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;this.listLoading=!0,r["a"].post("/system/SYSM01B4/getPageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={username:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},rowIndex:function(t){var e=t.row,a=t.rowIndex;e.row_index=a},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)}},filters:{dateFormat:function(t){if(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),n=e.getDate().toString().padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(n)}}}},l=o,c=(a("5b71"),a("2877")),d=Object(c["a"])(l,i,n,!1,null,"5443452c",null);e["a"]=d.exports},"0a50":function(t,e,a){},"1a31":function(t,e,a){"use strict";a("c2f7")},3499:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{directives:[{name:"show",rawName:"v-show",value:t.treeVisble,expression:"treeVisble"}],attrs:{span:4}},[a("div",{staticClass:"departLeft",style:{height:t.departLeftHeight}},[a("div",{staticClass:"groupTitle"},[a("span",[t._v("组织架构")])]),a("el-tree",{attrs:{data:t.departData,"node-key":"id","default-expand-all":"","expand-on-click-node":!1,props:t.defaultProps},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.node,n=e.data;return a("div",{staticClass:"custom-tree-node",staticStyle:{width:"100%"}},[a("p",{on:{click:function(){return t.handleNodeClick(n,i)}}},[t._v(" "+t._s(n.deptname)+" ")])])}}])})],1)]),a("el-col",{attrs:{span:t.treeVisble?20:24}},[a("div",{staticClass:"departHeader",staticStyle:{height:"300px"}},[a("h3",[t._v(t._s(t.departName))]),a("p",{staticClass:"title"},[t._v("下级部门")]),a("div",{staticClass:"header"},[a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(e){t.gropuFormVisible=!0,t.idx=t.departId}}},[t._v("设置")]),a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(e){t.gropuFormVisible=!0,t.idx=0}}},[t._v("添加子部门")]),a("el-button",{attrs:{type:"danger",size:"mini"},on:{click:function(e){return t.deleteItem()}}},[t._v("删除子部门")])],1),a("div",{staticClass:"content"},[0!=t.selectDepart.length?a("div",t._l(t.selectDepart,(function(e,i){return a("div",{key:i,staticClass:"content-item",class:e.isActive?"isDeptActive":"",on:{click:function(a){return t.selectDeptRow(e,i)}}},[a("div",{staticStyle:{"font-size":"14px"}},[t._v(t._s(e.deptname))])])})),0):a("div",{staticClass:"noData"},[t._v(" 当前部门不包含下级部门 "),a("span",{staticClass:"addDepartment",on:{click:function(e){t.gropuFormVisible=!0,t.idx=0}}},[t._v("添加子部门")])])])]),a("div",{staticClass:"departTable"},[a("div",[a("p",{staticClass:"title"},[t._v("部门人员")]),a("listheader",{attrs:{deptid:t.departId,selectUser:t.selectUser,isopensort:t.isopensort},on:{bindData:t.bindData,btnAdd:function(e){t.addUserVisible=!0},delItem:t.delItem,btnsort:t.btnsort}})],1),a("el-table",{staticClass:"tableBox",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"2px 0px"},"cell-style":{padding:"4px 0px"},height:t.tableMaxHeight,"row-key":"id"},on:{"selection-change":t.handleSelection}},[a("el-table-column",{attrs:{type:"selection",width:"50",align:"center"}}),a("el-table-column",{attrs:{align:"center",label:"ID","min-width":"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.$index+1)+" ")]}}])}),a("el-table-column",{attrs:{label:"登录名",align:"center","min-width":"100","show-overflow-tooltip":"",prop:"groupuid"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(" "+t._s(e.row.username))])]}}])}),a("el-table-column",{attrs:{label:"姓名",align:"center","min-width":"120","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.realname))])]}}])}),a("el-table-column",{attrs:{label:"职位",align:"center","min-width":"120","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[1==e.row.isdeptadmin?a("el-tag",{attrs:{type:"warning",size:"medium"}},[t._v("主管")]):t._e()]}}])}),a("el-table-column",{attrs:{label:"备注",align:"center","min-width":"120","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.remark))])]}}])}),a("el-table-column",{attrs:{label:"操作","min-width":"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[0==e.row.isdeptadmin?a("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(a){return t.changeisAdmin(e.row)}}},[t._v("授权为主管")]):a("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(a){return t.changeisCommon(e.row)}}},[t._v("授权为成员")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)])],1)],1),t.gropuFormVisible?a("el-dialog",{attrs:{title:"组织架构","append-to-body":!0,visible:t.gropuFormVisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.gropuFormVisible=e}}},[a("div",[a("group",{ref:"group",attrs:{idx:t.idx,pid:t.departId},on:{getdepartData:t.getdepartData,closeDialog:function(e){t.gropuFormVisible=!1}}})],1)]):t._e(),t.addUserVisible?a("el-dialog",{attrs:{title:"用户信息","append-to-body":!0,visible:t.addUserVisible,width:"60vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.addUserVisible=e}}},[a("selUser",{ref:"selUser",attrs:{multi:1}}),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.submitsaelUser()}}},[t._v("确 定")]),a("el-button",{on:{click:function(e){t.addUserVisible=!1}}},[t._v("取 消")])],1)],1):t._e()],1)},n=[],r=a("c7eb"),s=a("1da1"),o=a("2909"),l=(a("4d90"),a("d3b7"),a("25f0"),a("99af"),a("e9c4"),a("3ca3"),a("ddb0"),a("159b"),a("a434"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{class:t.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[a("div",[a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnSearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnSearch},slot:"append"},[t._v("搜索 ")])],1),t.deptid!=t.$store.getters.userinfo.tenantinfo.tenantid?a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-plus",size:"mini",plain:""},on:{click:t.btnAdd}},[t._v(" 添加 ")]):t._e(),t.deptid!=t.$store.getters.userinfo.tenantinfo.tenantid?a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{disabled:0==t.selectUser.length,type:"danger",icon:"el-icon-delete",size:"mini",plain:""},on:{click:t.delItem}},[t._v(" 批量删除 ")]):t._e(),t.deptid!=t.$store.getters.userinfo.tenantinfo.tenantid?a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{icon:"el-icon-sort",size:"mini",plain:""},on:{click:t.btnsort}},[t._v(" "+t._s(t.isopensort?"关闭排序":"开始排序")+" ")]):t._e()],1),a("div",{staticClass:"iShowBtn"})]),a("el-collapse-transition",[a("div",{directives:[{name:"show",rawName:"v-show",value:t.iShow,expression:"iShow"}],staticClass:"searchDetail"},[a("el-form",{ref:"formdata",attrs:{model:t.formdata,"label-width":"100px","auto-complete":"on"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"部门名称"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入部门名称",size:"small"},model:{value:t.formdata.deptname,callback:function(e){t.$set(t.formdata,"deptname",e)},expression:"formdata.deptname"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"负责人"}},[a("el-input",{attrs:{clearable:"",placeholder:"负责人",size:"small"},model:{value:t.formdata.leader,callback:function(e){t.$set(t.formdata,"leader",e)},expression:"formdata.leader"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px","margin-top":"6px"},attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.advancedSearch()}}},[t._v(" 搜索 ")])],1)],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"手机"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入手机",clearable:"",size:"small"},model:{value:t.formdata.phone,callback:function(e){t.$set(t.formdata,"phone",e)},expression:"formdata.phone"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"邮箱"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入邮箱",clearable:"",size:"small"},model:{value:t.formdata.email,callback:function(e){t.$set(t.formdata,"email",e)},expression:"formdata.email"}})],1)],1)],1)],1)],1)])],1)}),c=[],d={name:"Listheader",props:["deptid","selectUser","isopensort"],data:function(){return{strfilter:"",iShow:!1,formdata:{}}},methods:{advancedSearch:function(){this.iShow=!1,this.$emit("advancedSearch",this.formdata)},btnAdd:function(){this.$emit("btnAdd")},delItem:function(){this.$emit("delItem")},btnsort:function(){this.$emit("btnsort")},btnSearch:function(){this.$emit("btnSearch",this.strfilter)},bindData:function(){this.$emit("bindData")}}},m=d,u=(a("6835"),a("2877")),p=Object(u["a"])(m,l,c,!1,null,"7dd5d882",null),f=p.exports,h=a("b775"),g=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",size:"small"},nativeOn:{click:function(e){return t.submitForm("formdata")}}},[t._v(" 保 存")]),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.closeForm(e)}}},[t._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth,"auto-complete":"off",rules:t.formRules}},[a("el-row",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}]},[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"上级部门"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.parentid))])])],1)],1),a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"部门编码"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"部门编码",clearable:"",size:"small"},model:{value:t.formdata.deptcode,callback:function(e){t.$set(t.formdata,"deptcode",e)},expression:"formdata.deptcode"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"部门名称"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入部门名称",clearable:"",size:"small"},model:{value:t.formdata.deptname,callback:function(e){t.$set(t.formdata,"deptname",e)},expression:"formdata.deptname"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"负责人"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入负责人",clearable:"",size:"small"},model:{value:t.formdata.leader,callback:function(e){t.$set(t.formdata,"leader",e)},expression:"formdata.leader"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"电话"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入电话",clearable:"",size:"small"},model:{value:t.formdata.phone,callback:function(e){t.$set(t.formdata,"phone",e)},expression:"formdata.phone"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"邮箱"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入邮箱",clearable:"",size:"small"},model:{value:t.formdata.email,callback:function(e){t.$set(t.formdata,"email",e)},expression:"formdata.email"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"部门状态"}},[a("el-radio",{attrs:{label:1},model:{value:t.formdata.enabledmark,callback:function(e){t.$set(t.formdata,"enabledmark",e)},expression:"formdata.enabledmark"}},[t._v("正常")]),a("el-radio",{attrs:{label:0},model:{value:t.formdata.enabledmark,callback:function(e){t.$set(t.formdata,"enabledmark",e)},expression:"formdata.enabledmark"}},[t._v("停用")])],1)],1)],1)],1),a("el-divider")],1),a("el-form",{staticClass:"footFormContent",attrs:{"label-width":t.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"新建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.modifydate)))])])],1)],1)],1)],1)])])},b=[];a("498a"),a("b64b");const w={add(t){return new Promise((e,a)=>{var i=JSON.stringify(t);h["a"].post("/system/SYSM01B5/create",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},update(t){return new Promise((e,a)=>{var i=JSON.stringify(t);h["a"].post("/system/SYSM01B5/update",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},delete(t){return new Promise((e,a)=>{h["a"].get("/system/SYSM01B5/delete?key="+t).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})}};var v=w,y={name:"Formedit",components:{},filters:{dateFormat:function(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),n=e.getDate().toString().padStart(2,"0"),r=e.getHours().toString().padStart(2,"0"),s=e.getMinutes().toString().padStart(2,"0"),o=e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(n," ").concat(r,":").concat(s,":").concat(o)}},props:["idx","title","getType"],data:function(){var t=function(t,e,a){console.log(e),0==e.trim().length?a(new Error("请选择员工")):a()};return{formdata:{id:"",parentid:"root",ancestors:"",deptcode:"",deptname:"",enabledmark:1,rownum:0,leader:"",phone:"",email:"",remark:"",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname},formRules:{test:[{required:!0,trigger:"blur",validator:t}]},formLabelWidth:"100px",formheight:"500px"}},computed:{formcontainHeight:function(){return window.innerHeight-50-33-50+"px"}},watch:{idx:function(t,e){console.log("new: %s, old: %s",t,e),this.bindData()}},created:function(){this.bindData()},methods:{bindData:function(){var t=this;this.listLoading=!0,console.log("绑定数据"),"update"==this.getType&&h["a"].get("/system/SYSM01B5/getEntity?key=".concat(this.idx)).then((function(e){console.log(e),console.log("get了idx=".concat(t.idx)),200==e.data.code&&(t.formdata=e.data.data),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},submitForm:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return console.log("error submit!!"),!1;console.log("可保存"),e.saveForm()}))},saveForm:function(){var t=this;0==this.idx&&"create"==this.getType?v.add(this.formdata).then((function(e){console.log("新建保存====",e),200==e.code&&(t.$message.success("保存成功"),t.$emit("compForm"))})).catch((function(e){t.$message.warning("保存失败")})):0!=this.idx&&"create"==this.getType?(this.formdata.parentid=this.idx,v.add(this.formdata).then((function(e){console.log("新建保存====",e),200==e.code&&(t.$message.success("保存成功"),t.$emit("compForm"))})).catch((function(e){t.$message.warning("保存失败")}))):"update"==this.getType&&v.update(this.formdata).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.bindData())})).catch((function(e){t.$message.warning("保存失败")}))},closeForm:function(){this.$emit("closeForm"),console.log("关闭窗口")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){console.log("执行删除"),v.delete(t).then((function(t){200==t.code&&e.$message.success("删除成功"),e.$emit("compForm")})).catch((function(){e.$message.warning("删除失败")}))})).catch((function(){}))},check:function(){console.log("check")}}},S=y,x=(a("91d3"),Object(u["a"])(S,g,b,!1,null,"5b5ffdce",null)),_=x.exports,k=a("333d"),$=a("03af"),D=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticStyle:{width:"100%"}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth,rules:t.formRules}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("deptname")}}},[a("el-form-item",{attrs:{label:"组织名称",prop:"deptname"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入组织名称",size:"small"},on:{input:t.writeCode},model:{value:t.formdata.deptname,callback:function(e){t.$set(t.formdata,"deptname",e)},expression:"formdata.deptname"}})],1)],1)]),a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("deptcode")}}},[a("el-form-item",{attrs:{label:"组织编码",prop:"deptcode"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入组织编码",size:"small"},model:{value:t.formdata.deptcode,callback:function(e){t.$set(t.formdata,"deptcode",e)},expression:"formdata.deptcode"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("leader")}}},[a("el-form-item",{attrs:{label:"负责人",prop:"leader"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入负责人",size:"small"},model:{value:t.formdata.leader,callback:function(e){t.$set(t.formdata,"leader",e)},expression:"formdata.leader"}})],1)],1)]),a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("phone")}}},[a("el-form-item",{attrs:{label:"电话",prop:"phone"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入电话",size:"small"},model:{value:t.formdata.phone,callback:function(e){t.$set(t.formdata,"phone",e)},expression:"formdata.phone"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("email")}}},[a("el-form-item",{attrs:{label:"邮箱",prop:"email"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入邮箱",size:"small"},model:{value:t.formdata.email,callback:function(e){t.$set(t.formdata,"email",e)},expression:"formdata.email"}})],1)],1)])],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"排序"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{"controls-position":"right",min:0,size:"small"},model:{value:t.formdata.rownum,callback:function(e){t.$set(t.formdata,"rownum",e)},expression:"formdata.rownum"}})],1)],1)],1)],1)],1),a("el-form",{staticClass:"footFormContent",attrs:{"label-width":t.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.modifydate)))])])],1)],1)],1)],1)]),a("div",{staticClass:"button-container flex j-end"},[a("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",size:"small"},nativeOn:{click:function(e){return t.submitForm("formdata")}}},[t._v(" 保 存")]),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.$emit("closeDialog")}}},[t._v(" 关 闭")])],1)])},C=[],P=a("b0b8"),F={name:"Formedit",filters:{dateFormat:function(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),n=e.getDate().toString().padStart(2,"0"),r=e.getHours().toString().padStart(2,"0"),s=e.getMinutes().toString().padStart(2,"0"),o=e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(n," ").concat(r,":").concat(s,":").concat(o)}},props:["idx","pid"],data:function(){return{formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,enabledmark:1,ancestors:"",deptcode:"",deptname:"",email:"",leader:"",parentid:"",phone:"",remark:"",rownum:0},formRules:{deptname:[{required:!0,trigger:"blur",message:"组织编码为必填项"}],deptcode:[{required:!0,trigger:"blur",message:"组织编码为必填项"}]},formLabelWidth:"100px",dialogVisible:!1,delDialogVisible:!1,option:""}},computed:{formcontainHeight:function(){return"300px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindData()},methods:{bindData:function(){var t=this;0!=this.idx?h["a"].get("/system/SYSM01B5/getEntity?key=".concat(this.idx)).then((function(e){console.log("==================",e),200==e.data.code&&(t.formdata=e.data.data)})):this.formdata.parentid=this.pid},submitForm:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;this.pid&&(this.formdata.parentid=this.pid),0==this.idx?h["a"].post("/system/SYSM01B5/create",JSON.stringify(this.formdata)).then((function(e){console.log("SYSM01B5",e),t.$emit("getdepartData"),t.$emit("closeDialog")})):h["a"].post("/system/SYSM01B5/update",JSON.stringify(this.formdata)).then((function(e){t.$emit("closeDialog"),t.$emit("getdepartData")}))},closeForm:function(){this.$emit("closeForm")},check:function(){console.log("check")},cleValidate:function(t){this.$refs.formdata.clearValidate(t)},writeCode:function(t){P.setOptions({checkPolyphone:!1,charCase:1}),this.formdata.deptcode=P.getFullChars(t)}}},z=F,N=(a("cc5b"),Object(u["a"])(z,D,C,!1,null,"7f5305ef",null)),O=N.exports,V=a("aa47"),I={components:{listheader:f,formedit:_,Pagination:k["a"],selUser:$["a"],group:O},filters:{dateFormat:function(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),n=e.getDate().toString().padStart(2,"0"),r=e.getHours().toString().padStart(2,"0"),s=e.getMinutes().toString().padStart(2,"0"),o=e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(n," ").concat(r,":").concat(s,":").concat(o)}},data:function(){return{title:"组织架构",lst:[],searchstr:" ",formvisible:!1,refreshTable:!1,isShowAll:!0,idx:0,total:0,queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:0,OrderBy:"deptrownum"},departData:[],departId:this.$store.getters.userinfo.tenantinfo.tenantid,departName:this.$store.getters.userinfo.tenantinfo.tenantname,selectDepart:[],treeVisble:!0,defaultProps:{id:"id",label:"deptname",pid:"parentid",children:"children"},treeEditable:!1,addUserVisible:!1,selectUser:[],gropuFormVisible:!1,isopensort:!1,sortObj:new Object,selDeptRow:{}}},computed:{tableMaxHeight:function(){return window.innerHeight-480+"px"},departLeftHeight:function(){return window.innerHeight-100+"px"}},created:function(){this.searchstr="",this.bindData()},mounted:function(){},methods:{bindData:function(){this.getdepartData()},getdepartData:function(){var t=this,e={PageNum:1,PageSize:1e3,OrderType:1,SearchType:1};h["a"].post("/system/SYSM01B5/getPageList",JSON.stringify(e)).then((function(e){if(200==e.data.code){var a=[{id:t.$store.getters.userinfo.tenantinfo.tenantid,parentid:"root",deptname:t.$store.getters.userinfo.tenantinfo.tenantname}];console.log(t.$store.getters.userinfo.tenantinfo);var i=e.data.data.list,n=[].concat(Object(o["a"])(i),a);t.departData=t.changeFormat(n),t.handleNodeClick(t.departData[0]),t.getUser()}}))},handleNodeClick:function(t,e){if(console.log("dianji ",t),this.idx=t.id,this.departName=t.deptname,this.departId=t.id,this.selDeptRow={},this.getUser(),t.children){this.selectDepart=t.children;for(var a=0;a<this.selectDepart.length;a++)this.selectDepart[a].isActive=!1}else this.selectDepart=[]},selectDeptRow:function(t,e){for(var a=0;a<this.selectDepart.length;a++)this.selectDepart[a].isActive=!1;this.selectDepart[e].isActive=!0,this.selDeptRow=t,this.$forceUpdate()},deleteItem:function(){var t=this;this.selDeptRow?this.$confirm("此操作将永久删除部门“"+this.selDeptRow.deptname+"”，是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){h["a"].get("/system/SYSM01B5/delete?key=".concat(t.selDeptRow.id)).then((function(e){200==e.data.code?(t.$message.success("删除成功"),t.selDeptRow={},t.bindData()):t.$message.warning("删除成功")})).catch((function(e){t.$message.error("请求错误")}))})):this.$message.warning("请选择要删除的部门")},getUser:function(){var t=this;if(this.departId==this.$store.getters.userinfo.tenantinfo.tenantid)var e="";else e=this.departId;this.queryParams.SearchPojo={deptid:e,tenantid:this.$store.getters.userinfo.tenantinfo.tenantid},this.queryParams.SearchType=0,h["a"].post("/system/SYSM01B4/getPageList",JSON.stringify(this.queryParams)).then((function(e){t.lst=e.data.data.list}))},submitsaelUser:function(){var t=this;return Object(s["a"])(Object(r["a"])().mark((function e(){var a,i,n,s,o;return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:for(a=t,i=t.$refs.selUser.$refs.selectVal.selection,console.log(i),n=[],s=0;s<i.length;s++)o=new Promise((function(e,a){var n={deptid:t.departId,deptname:t.departName,isadmin:i[s].isadmin,isdeptadmin:0,realname:i[s].realname,rownum:s,id:i[s].id,tenantid:i[s].tenantid,tenantname:i[s].tenantname,userid:i[s].userid,username:i[s].username};h["a"].post("/system/SYSM01B4/update",JSON.stringify(n)).then((function(t){200==t.data.code?e("保存成功"):a("保存失败")}))})),n.push(o);return e.next=7,Promise.all(n).then((function(t){a.$message.success("添加成功"),a.addUserVisible=!1,a.getUser()})).catch((function(t){a.$message.warning("添加失败")}));case 7:case"end":return e.stop()}}),e)})))()},delItem:function(){var t=this;return Object(s["a"])(Object(r["a"])().mark((function e(){var a,i,n,s,o;return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(0!=t.selectUser.length){e.next=3;break}return t.$message.warning("请选择部门人员"),e.abrupt("return");case 3:for(a=t,i=t.selectUser,n=[],s=0;s<i.length;s++)o=new Promise((function(t,e){var a={deptid:"",deptname:"",isadmin:i[s].isadmin,isdeptadmin:0,realname:i[s].realname,rownum:s,id:i[s].id,tenantid:i[s].tenantid,tenantname:i[s].tenantname,userid:i[s].userid,username:i[s].username};h["a"].post("/system/SYSM01B4/update",JSON.stringify(a)).then((function(a){200==a.data.code?t("删除成功"):e("删除失败")}))})),n.push(o);return e.next=9,Promise.all(n).then((function(t){a.$message.success("删除成功"),a.addUserVisible=!1,a.getUser()})).catch((function(t){a.$message.warning("删除失败")}));case 9:case"end":return e.stop()}}),e)})))()},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},handleSelection:function(t){this.selectUser=t},changeisAdmin:function(t){var e=this;this.$confirm("是否将"+t.realname+"的职位改为负责人?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.isdeptadmin?t.isdeptadmin=0:t.isdeptadmin=1,h["a"].post("/system/SYSM01B4/update",JSON.stringify(t)).then((function(t){200==t.data.code?e.$message.success("授权成功"):e.$message.warning("授权失败")}))})).catch((function(){}))},changeisCommon:function(t){var e=this;this.$confirm("是否将"+t.realname+"的职位改为成员?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.isdeptadmin?t.isdeptadmin=0:t.isdeptadmin=1,h["a"].post("/system/SYSM01B4/update",JSON.stringify(t)).then((function(t){200==t.data.code?e.$message.success("授权成功"):e.$message.warning("授权失败")}))})).catch((function(){}))},changeFormat:function(t){var e=[];if(!Array.isArray(t))return e;t.forEach((function(t){delete t.children}));var a={};return t.forEach((function(t){a[t.id]=t})),t.forEach((function(t){var i=a[t.parentid];i?(i.children||(i.children=[])).push(t):e.push(t)})),e},btnsort:function(){var t=this;this.isopensort=!this.isopensort,this.isopensort?this.$nextTick((function(){t.rowDrop()})):this.sortObj.destroy()},rowDrop:function(){var t=document.querySelector(".tableBox .el-table__body-wrapper tbody"),e=this;this.sortObj=V["a"].create(t,{onEnd:function(t){var a=t.newIndex,i=t.oldIndex;console.log(a,i);var n=e.lst.splice(i,1)[0];e.lst.splice(a,0,n);for(var r=0;r<e.lst.length;r++)if(e.lst[r].deptrownum!=r){var s={id:e.lst[r].id,deptrownum:r};h["a"].post("/system/SYSM01B4/update",JSON.stringify(s)).then((function(t){200==t.data.code||e.$message.warning("排序失败")}))}}})}}},B=I,U=(a("1a31"),Object(u["a"])(B,i,n,!1,null,"41b3478e",null));e["default"]=U.exports},"46c6":function(t,e,a){},"4cd2":function(t,e,a){},"5b71":function(t,e,a){"use strict";a("0a50")},"668d":function(t,e,a){},6835:function(t,e,a){"use strict";a("46c6")},"91d3":function(t,e,a){"use strict";a("4cd2")},c2f7:function(t,e,a){},cc5b:function(t,e,a){"use strict";a("668d")}}]);