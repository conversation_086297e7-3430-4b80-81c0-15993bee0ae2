(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-f46de62e"],{"207f":function(t,e,i){},2665:function(t,e,i){"use strict";i("207f")},"2cae":function(t,e,i){},"2f85":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{},[i("div",{staticClass:"dictionaries_ul myscrollbar"},[0!=t.lst.length?i("ul",{},t._l(t.lst,(function(e,a){return i("li",{key:a,class:t.radio==a?"active":"",on:{click:function(i){t.getCurrentRow(e),t.radio=a}}},[i("span",[t._v(t._s(e.dictvalue))])])})),0):i("div",{staticClass:"noData"},[t._v("暂无数据")])]),i("div",{staticClass:"foots"},[i("span",{staticStyle:{"border-right":"1px solid #dcdfe6"},on:{click:function(e){t.SYSM07B1Visible=!0,t.lst=t.formdata.item}}},[t._v("编辑")]),i("span",{on:{click:function(e){return t.$emit("closedic")}}},[t._v("关闭")])])]),t.SYSM07B1Visible?i("el-dialog",{attrs:{width:"400px",title:"编辑","append-to-body":!0,visible:t.SYSM07B1Visible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.SYSM07B1Visible=e}}},[i("div",{staticClass:"dialog-body customDialog"},[i("div",{staticClass:"left"},t._l(t.lst,(function(e,a){return i("p",{key:a,class:t.ActiveIndex==a?"isActive":"",on:{click:function(e){t.ActiveIndex=a}}},[t._v(" "+t._s(e.dictvalue)+" ")])})),0),i("div",{staticClass:"right"},[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.addInput()}}},[t._v("添 加")]),i("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.editInput()}}},[t._v("编 辑")]),i("el-button",{attrs:{type:"danger",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.delItem()}}},[t._v("删 除")]),i("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveUp()}}},[t._v("上 移")]),i("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveDown()}}},[t._v("下 移")])],1)]),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitUpdate}},[t._v("确 定")]),i("el-button",{attrs:{size:"small"},on:{click:function(e){t.SYSM07B1Visible=!1,t.bindData()}}},[t._v("取 消")])],1)]):t._e()],1)},o=[],n=(i("a434"),i("e9c4"),i("b775")),s=i("333d"),r=i("b0b8"),l={components:{Pagination:s["a"]},props:["multi","billcode"],data:function(){return{title:"数据字典",formdata:{item:[]},lst:[],total:0,radio:-1,selrows:"",SYSM07B1Visible:!1,ActiveIndex:-1,queryParams:{PageNum:1,PageSize:100,OrderType:1,SearchType:1}}},watch:{lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i}},created:function(){},methods:{getCurrentRow:function(t){this.selrows=t,this.$forceUpdate(),this.$emit("singleSel",t)},bindData:function(){var t=this;this.lst=[],n["a"].get("/system/SYSM07B1/getBillEntityByDictCode?key="+this.billcode).then((function(e){if(200==e.data.code){if(null==e.data.data)return t.formdata={item:[]},void(t.listLoading=!1);if(t.formdata=e.data.data,0==t.formdata.item.length)return void(t.lst=[]);for(var i=0;i<t.formdata.item.length;i++)t.lst.push(t.formdata.item[i])}})).catch((function(e){t.$message.error("请求出错")}))},rowIndex:function(t){var e=t.row,i=t.rowIndex;e.row_index=i},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)},addInput:function(){var t=this;this.$prompt("数值：","添加",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:"",inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var i=e.value;t.addItem(i)})).catch((function(t){console.log(t)}))},addItem:function(t){r.setOptions({checkPolyphone:!1,charCase:1});var e={cssclass:"",defaultmark:0,dictcode:r.getFullChars(t),dictvalue:t,enabledmark:1,essential:0,pid:this.formdata.id,remark:"",rownum:0};this.lst.push(e)},editInput:function(){if(-1!=this.ActiveIndex){var t=this;this.$prompt("数值：","编辑",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:t.lst[t.ActiveIndex].dictvalue,inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var i=e.value;t.lst[t.ActiveIndex].dictvalue=i,r.setOptions({checkPolyphone:!1,charCase:1}),t.lst[t.ActiveIndex].dictcode=r.getFullChars(i)})).catch((function(t){console.log(t)}))}else this.$message.warning("请选择内容")},delItem:function(){if(-1!=this.ActiveIndex){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.lst.splice(t.ActiveIndex,1),t.ActiveIndex=-1})).catch((function(){}))}else this.$message.warning("请选择内容")},getMoveUp:function(){if(0!=this.ActiveIndex){var t=this.lst[this.ActiveIndex],e=this.lst[this.ActiveIndex].rownum;this.lst.splice(e,1),this.lst.splice(e-1,0,t),this.ActiveIndex-=1;for(var i=0;i<this.lst.length;i++)this.lst[i].rownum=i}else this.$message.warning("已经是第一行了！")},getMoveDown:function(){if(this.ActiveIndex!=this.lst.length-1){var t=this.lst[this.ActiveIndex],e=this.lst[this.ActiveIndex].rownum;this.lst.splice(e,1),this.lst.splice(e+1,0,t),this.ActiveIndex+=1;for(var i=0;i<this.lst.length;i++)this.lst[i].rownum=i}else this.$message.warning("已经是最后一行了！")},submitUpdate:function(){var t=this;this.formdata.item=this.lst,n["a"].post("/system/SYSM07B1/update",JSON.stringify(this.formdata)).then((function(e){200==e.data.code?(t.$message.success(e.data.msg||"保存成功"),t.SYSM07B1Visible=!1):t.$message.warning(e.data.msg||"保存失败")})).catch((function(e){t.$message.error("请求错误")}))}}},c=l,d=(i("6156"),i("2877")),m=Object(d["a"])(c,a,o,!1,null,"2d6fcc02",null);e["a"]=m.exports},"3a29":function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.formvisible?i("div",{staticClass:"formedit"},[i("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}],ref:"index",staticClass:"index"},[i("ListHeader",{attrs:{tableForm:t.tableForm},on:{btnHelp:t.btnHelp,bindData:t.bindData,btnAdd:function(e){return t.showForm(0)},btnSearch:function(e){return t.$refs.tableList.search(e)},advancedSearch:function(e){return t.$refs.tableList.advancedSearch(e)},btnExport:function(e){return t.$refs.tableList.btnExport()},bindColumn:function(e){return t.$refs.tableList.getColumn()}}}),i("div",{staticClass:"page-container"},[i("el-row",[i("el-col",{attrs:{span:t.showhelp?20:24}},[i("TableList",{ref:"tableList",on:{changeIdx:t.changeIdx,showForm:t.showForm,sendTableForm:t.sendTableForm}})],1),i("el-col",{attrs:{span:t.showhelp?4:0}},[i("HelpModel",{ref:"helpmodel",attrs:{code:"SYSM07B15"}})],1)],1)],1)],1)])},o=[],n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{class:"filter-container flex j-s"},[i("div",[i("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnSearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[i("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnSearch},slot:"append"},[t._v("搜索 ")])],1),i("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-plus",plain:"",size:"mini"},on:{click:t.btnAdd}},[t._v(" 添加 ")])],1),i("div",{staticClass:"iShowBtn"},[i("el-button",{attrs:{size:"mini",icon:"el-icon-search",title:"高级筛选",type:t.$store.state.advancedSearch.modulecode==t.tableForm.formcode?"primary":"default"},on:{click:function(e){return t.openSearchForm()}}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:t.btnExport}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setcolumsvisible=!0}}})],1)]),t.setcolumsvisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setcolumsvisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.setcolumsvisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm},on:{bindData:function(e){return t.$emit("bindColumn")},closeDialog:function(e){t.setcolumsvisible=!1}}})],1):t._e(),i("el-dialog",{attrs:{title:"高级筛选",width:"720px",visible:t.searchvisible,"append-to-body":!0,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.searchvisible=e}}},[i("SearchForm",{ref:"searchForm",attrs:{code:t.tableForm.formcode},on:{advancedSearch:t.advancedSearch,closedDialog:function(e){t.searchvisible=!1},bindData:t.bindData}})],1)],1)},s=[],r={name:"Listheader",props:["tableForm"],data:function(){return{strfilter:"",setcolumsvisible:!1,searchvisible:!1}},methods:{btnAdd:function(){this.$emit("btnAdd")},btnSearch:function(){this.$emit("btnSearch",this.strfilter)},advancedSearch:function(t){this.$emit("advancedSearch",t),this.searchvisible=!1},openSearchForm:function(){var t=this;this.searchvisible=!0,setTimeout((function(){t.$refs.searchForm.getInit()}),100)},btnExport:function(){this.$emit("btnExport")},bindData:function(){this.$emit("bindData")}}},l=r,c=(i("fdc8"),i("2877")),d=Object(c["a"])(l,n,s,!1,null,"7e1d6e8d",null),m=d.exports,u=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"99"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("EditGroupBtns",{ref:"EditGroupBtns",attrs:{showcontent:["save","operate"],formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,closeForm:t.closeForm,clickMethods:t.clickMethods}})],1)]),i("div",{staticStyle:{padding:"20px","box-sizing":"content-box",height:"100%",overflow:"auto"}},[i("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate},on:{clickMethods:t.clickMethods},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[i("div",{staticClass:"form form-head p-r"},[i("EditHeader",{ref:"formHeader",attrs:{title:t.formtemplate.header.title?t.formtemplate.header.title:t.title,formdata:t.formdata}})],1)]},proxy:!0}],null,!0)})],1)])])},f=[],h=(i("b64b"),i("b775"));const p={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);h["a"].post("/system/SYSM07B15/create",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);h["a"].post("/system/SYSM07B15/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){h["a"].get("/system/SYSM07B15/delete?key="+t.formdata.id).then(e=>{200==e.data.code?0==e.data.data?t.$message.warning("删除失败,"+e.data.msg+"中已使用"):(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})}};var b=p,v=["id","modulecode","billname","orgcolumns","exprtemp","tgcolumn","decnum","enabledmark","rownum","remark","returntype","custom1","custom2","custom3","custom4","custom5"],g={params:v},y=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-divider",{attrs:{"content-position":"left"}},[t._v("基础信息")]),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("modulecode")}}},[i("el-form-item",{attrs:{label:"模块编码",prop:"modulecode"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入模块编码",size:"small"},model:{value:t.formdata.modulecode,callback:function(e){t.$set(t.formdata,"modulecode",e)},expression:"formdata.modulecode"}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("billname")}}},[i("el-form-item",{attrs:{label:"单据名称",prop:"billname"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入单据名称",size:"small"},model:{value:t.formdata.billname,callback:function(e){t.$set(t.formdata,"billname",e)},expression:"formdata.billname"}})],1)],1)])],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("orgcolumns")}}},[i("el-form-item",{attrs:{label:"源字段集",prop:"orgcolumns"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入源字段集",size:"small"},model:{value:t.formdata.orgcolumns,callback:function(e){t.$set(t.formdata,"orgcolumns",e)},expression:"formdata.orgcolumns"}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("tgcolumn")}}},[i("el-form-item",{attrs:{label:"目标字段",prop:"tgcolumn"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入目标字段",size:"small"},model:{value:t.formdata.tgcolumn,callback:function(e){t.$set(t.formdata,"tgcolumn",e)},expression:"formdata.tgcolumn"}})],1)],1)])],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"字段类型"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择字段类型",size:"small"},model:{value:t.formdata.returntype,callback:function(e){t.$set(t.formdata,"returntype",e)},expression:"formdata.returntype"}},[i("el-option",{attrs:{label:"数字",value:0}}),i("el-option",{attrs:{label:"字符串",value:1}})],1)],1)],1),i("el-col",{attrs:{span:3}},[i("el-form-item",{attrs:{label:"小数位"}},[i("el-input-number",{staticClass:"inputNumberContent",staticStyle:{width:"100%"},attrs:{controls:!0,type:"number",min:0,size:"small","controls-position":"right"},model:{value:t.formdata.decnum,callback:function(e){t.$set(t.formdata,"decnum",e)},expression:"formdata.decnum"}})],1)],1),i("el-col",{attrs:{span:3}},[i("el-form-item",{attrs:{label:"排序"}},[i("el-input-number",{staticClass:"inputNumberContent",staticStyle:{width:"100%"},attrs:{controls:!0,type:"number",min:0,size:"small","controls-position":"right"},model:{value:t.formdata.rownum,callback:function(e){t.$set(t.formdata,"rownum",e)},expression:"formdata.rownum"}})],1)],1),i("el-col",{attrs:{span:2}},[i("el-form-item",{attrs:{label:""}},[i("el-checkbox",{attrs:{label:"有效","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.enabledmark,callback:function(e){t.$set(t.formdata,"enabledmark",e)},expression:"formdata.enabledmark"}})],1)],1)],1),i("p",{staticStyle:{margin:"0 0 0 100px","font-size":"14px",color:"coral"}},[t._v(" 注：目标字段为[quantity,price,amount,taxprice,taxamount,itemtaxrate]时，会激活通用金额计算公式")]),i("el-divider",{attrs:{"content-position":"left"}},[t._v("模板设置")]),i("el-row",{staticStyle:{"margin-top":"15px"}},[i("el-col",{attrs:{span:22}},[i("el-form-item",{attrs:{label:"公式模板"}},[i("div",{staticClass:"box-card"},[i("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入公式模板",size:"small",type:"textarea",autosize:{minRows:20,maxRows:24}},model:{value:t.formdata.exprtemp,callback:function(e){t.$set(t.formdata,"exprtemp",e)},expression:"formdata.exprtemp"}})],1)])],1)],1)],1)},x=[],w=(i("e9c4"),{props:{formdata:{type:Object},title:{type:String}},data:function(){return{partGroup:[],formRules:{modulecode:[{required:!0,trigger:"blur",message:"模块编码为必填项"}]}}},created:function(){this.bindTreeData()},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)},bindTreeData:function(){var t=this,e={PageNum:1,PageSize:1e3,OrderType:1,SearchType:1};this.$request.post("/sale/D01M02S1/getGroupPageList",JSON.stringify(e)).then((function(e){200==e.data.code&&(t.partGroup=e.data.data.list)}))}}}),S=w,k=(i("f524"),Object(c["a"])(S,y,x,!1,null,"ddf5bca6",null)),$=k.exports,C=i("dcb4"),F=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]}],B=[],I={header:{type:0,title:"自定义公式",content:[{type:"divider",center:"left",label:"基础信息"},{type:"form",rowitem:[{col:5,code:"modulecode",label:"模块编码",type:"input",methods:"",param:"",required:!0}]},{rowitem:[{col:5,code:"billname",label:"单据名称",type:"input",methods:"",param:""},{col:3,code:"decnum",label:"小数位",type:"number",methods:"",param:""},{col:2,code:"enabledmark",label:"有效",type:"checkbox",methods:"",param:""}]},{rowitem:[{col:5,code:"orgcolumns",label:"源字段集",type:"input",methods:"",param:""},{col:5,code:"tgcolumn",label:"目标字段",type:"input",methods:"",param:""}]},{type:"divider",center:"left",label:"模板设置"},{rowitem:[{col:22,code:"exprtemp",label:"公式模板",type:"textarea",autosize:{minRows:20,maxRows:24},methods:"",param:""}]}]},item:{type:0,content:[]},footer:{type:0,content:[{type:"divider",center:"left",label:""},{rowitem:[{col:22,code:"summary",label:"摘要",type:"input",methods:"",param:""}]},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"}]}]}},D={name:"Formedit",components:{EditHeader:$,FormTemp:C["a"]},props:["idx"],data:function(){return{title:"自定义公式",operateBar:F,processBar:B,formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,defccjson:"",deftojson:"",emailcode:"",emailname:"",emailtemplate:"",emailtype:"",billname:"",decnum:2,returntype:0,enabledmark:1,exprtemp:"",modulecode:"",orgcolumns:"",remark:"",rownum:0,tgcolumn:""},formstate:0,submitting:0,formtemplate:I}},computed:{formcontainHeight:function(){var t=window.innerHeight-113;return t<600&&(t=600),t+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp()},mounted:function(){this.bindData()},methods:{bindTemp:function(){this.formtemplate=I},bindData:function(){var t=this;this.formstate=0,0!=this.idx&&h["a"].get("/system/SYSM07B15/getEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.formstate=t.formdata.id?1:0):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")}))},submitForm:function(){var t,e=this;t=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,t.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;this.submitting=1;var e={};e=this.$getParam(g,e,this.formdata),0==this.idx?b.add(e).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.id?1:0)})).catch((function(e){t.$message.warning("保存失败"),t.submitting=0})):b.update(e).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.id?1:0)})).catch((function(e){t.$message.warning("保存失败"),t.submitting=0}))},clickMethods:function(t){this[t.meth](t.param)},closeForm:function(){this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){b.delete(e)})).catch((function(){}))}}},_=D,O=(i("2665"),Object(c["a"])(_,u,f,!1,null,"43243f6d",null)),P=O.exports,z=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("ve-table",{key:t.keynum,ref:"tableList",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"checkbox-option":t.checkboxOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}})],1),i("div",{staticStyle:{"margin-right":"40px"}},[i("Scene",{ref:"scene",attrs:{code:t.tableForm.formcode},on:{bindData:t.bindData}})],1)])],1)},E=[],M=i("c7eb"),T=i("1da1"),L=(i("a9e3"),i("d3b7"),i("159b"),i("c7cd"),{formcode:"SYSM07B15List",item:[{itemcode:"modulecode",itemname:"模块编码",minwidth:"70",displaymark:1,overflow:1,datasheet:"CiBillExpression.modulecode"},{itemcode:"billname",itemname:"单据名称",minwidth:"70",displaymark:1,overflow:1,datasheet:"CiBillExpression.billname"},{itemcode:"orgcolumns",itemname:"源字段集",minwidth:"80",displaymark:1,overflow:1,datasheet:"CiBillExpression.orgcolumns"},{itemcode:"exprtemp",itemname:"公式模板",minwidth:"80",displaymark:1,overflow:1,datasheet:"CiBillExpression.exprtemp"},{itemcode:"tgcolumn",itemname:"目标字段",minwidth:"80",displaymark:1,overflow:1,datasheet:"CiBillExpression.tgcolumn"},{itemcode:"decnum",itemname:"小数位",minwidth:"80",displaymark:1,overflow:1,datasheet:"CiBillExpression.tgcolumn"},{itemcode:"rownum",itemname:"排序",minwidth:"80",displaymark:1,overflow:1,datasheet:"CiBillExpression.rownum"},{itemcode:"remark",itemname:"备注",minwidth:"70",displaymark:1,overflow:1,datasheet:"CiBillExpression.remark"},{itemcode:"lister",itemname:"制表",minwidth:"70",displaymark:1,overflow:1,datasheet:"CiBillExpression.lister"}]}),A={components:{},props:["online"],data:function(){var t=this;return{lst:[],total:0,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:L,customList:[],selectList:[],totalfields:["refno"],exportitle:"自定义公式",customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.selectList=[],t.checkboxOption.selectedRowKeys=i,0!=i.length)for(var a=0;a<i.length;a++)t.selectList.push({id:i[a]})},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;if(i){if(t.checkboxOption.selectedRowKeys=a,0!=a.length)for(var o=0;o<a.length;o++)t.selectList.push({id:a[o]})}else t.selectList=[],t.checkboxOption.selectedRowKeys=[]}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},methods:{bindData:function(){var t=this;this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1));var e="/system/SYSM07B15/getPageList";h["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code?(t.lst=e.data.data.list,t.total=e.data.data.total):t.$message.warning(e.data.msg||"请求失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(T["a"])(Object(M["a"])().mark((function e(){return Object(M["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$getColumn(t.tableForm.formcode,L).then((function(e){t.customList=e.customList,t.tableForm=Object.assign({},e.colList),t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm)}));case 1:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row;i.column,i.rowIndex;if("createdate"==t.itemcode||"modifydate"==t.itemcode)return e.$options.filters.dateFormats(o[t.itemcode]);if("modulecode"==t.itemcode){var n=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.id)}}},[o[t.itemcode]?o[t.itemcode]:"名称"]);return n}return o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i,this.keynum+=1},countCellData:function(){var t=this.$refs.tableList.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableList.getRangeCellSelection().selectionRangeIndexes,i=["baseprice","rebate","price","pricemin","pricemax"];this.$countCellData(this,i,t,e)},getSummary:function(){this.$getSummary(this,this.totalfields)},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){this.$search(this,t,1)},advancedSearch:function(t){this.$advancedSearch(this,t)},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}},showForm:function(t){this.$emit("showForm",t)},btnExport:function(){this.$btnExport(this.lst,this.tableForm,this.exportitle)},groupsearch:function(t){""!=t?this.queryParams.SearchPojo={partgroupid:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()}}},H=A,N=(i("729f"),Object(c["a"])(H,z,E,!1,null,"438bd386",null)),q=N.exports,R={name:"SYSM07B15",components:{TableList:q,ListHeader:m,FormEdit:P},data:function(){return{idx:0,formvisible:!1,tableForm:{},showhelp:!1}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},watch:{},mounted:function(){this.bindData(),this.$refs.tableList.getColumn()},methods:{bindData:function(){this.$refs.tableList.bindData()},btnHelp:function(){this.showhelp=!this.showhelp,this.showhelp&&this.$refs.helpmodel.bindData()},sendTableForm:function(t){this.tableForm=t},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t}}},j=R,V=(i("9b84"),Object(c["a"])(j,a,o,!1,null,"6e6e7924",null));e["default"]=V.exports},6156:function(t,e,i){"use strict";i("a1b8")},"729f":function(t,e,i){"use strict";i("7a59")},"7a59":function(t,e,i){},"9b84":function(t,e,i){"use strict";i("d8f4")},a1b8:function(t,e,i){},bdd2:function(t,e,i){},d8f4:function(t,e,i){},f524:function(t,e,i){"use strict";i("2cae")},fdc8:function(t,e,i){"use strict";i("bdd2")}}]);