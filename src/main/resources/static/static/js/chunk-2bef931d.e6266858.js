(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2bef931d"],{"5cca":function(t,a,s){},"6d7d":function(t,a,s){"use strict";s("5cca")},cf82:function(t,a,s){"use strict";s.r(a);var e=function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[s("div",{staticStyle:{padding:"20px"}},[s("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%",padding:"20px"},style:{height:t.formcontainHeight}},[t._m(0),s("div",{staticClass:"info"},[s("div",{staticClass:"info-top"}),s("div",{staticClass:"info-detail"},[t._m(1),s("div",{staticClass:"info-content"},[s("div",{staticStyle:{width:"30%"}},[s("div",{staticClass:"info-contnet-item"},[s("span",{staticClass:"title"},[t._v("服务单号")]),s("span",[t._v(t._s(t.formdata.refno))])]),s("div",{staticClass:"info-contnet-item"},[s("span",{staticClass:"title"},[t._v("交易单号")]),s("span",[t._v(t._s(t.formdata.paybillcode))])]),s("div",{staticClass:"info-contnet-item"},[s("span",{staticClass:"title"},[t._v("支付方式")]),s("span",[t._v(t._s(t.formdata.payment))])]),s("div",{staticClass:"info-contnet-item"},[s("span",{staticClass:"title"},[t._v("交易时间")]),s("span",[t._v(t._s(t._f("dateFormat")(t.formdata.statedate)))])])]),s("div",{staticStyle:{width:"30%"}},[s("div",{staticClass:"info-contnet-item"},[s("span",{staticClass:"title"},[t._v("订单金额")]),s("span",[t._v("¥ "+t._s(t.formdata.billtaxamount.toFixed(2)))])]),t._m(2),s("div",{staticClass:"info-contnet-item"},[s("span",{staticClass:"title"},[t._v("实收金额")]),s("span",[t._v("¥ "+t._s(t.formdata.payamount.toFixed(2)))])]),t._m(3)])])])]),s("div",{staticClass:"otherInfo"},[s("div",{staticClass:"otherInfo-item"},[s("div",{staticClass:"title"},[t._v("其他信息")]),s("div",{staticClass:"otherInfo-content"},[s("div",{staticClass:"otherInfo-content-item"},[s("span",{staticClass:"otherInfo-title"},[t._v("付款人名称")]),s("span",[t._v(t._s(t.formdata.realname))])]),s("div",{staticClass:"otherInfo-content-item"},[s("span",{staticClass:"otherInfo-title"},[t._v("付款人手机")]),s("span",[t._v(t._s(t.formdata.mobile))])]),s("div",{staticClass:"otherInfo-content-item"},[s("span",{staticClass:"otherInfo-title"},[t._v("交易来源")]),s("span",[t._v(t._s(t.formdata.company))])])])]),s("div",{staticStyle:{"align-self":"flex-end"}},[s("el-button",{attrs:{type:"primary"},on:{click:function(a){return t.goback()}}},[t._v("返回上一级页面")])],1)])])])])},i=[function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{staticClass:"trade-header"},[s("i",{staticClass:"el-icon-success"}),s("span",[t._v("交易状态：支付成功")])])},function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{staticClass:"info-title"},[s("span"),t._v(" 基本信息")])},function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{staticClass:"info-contnet-item"},[s("span",{staticClass:"title"},[t._v("优惠金额")]),s("span",[t._v("¥ 0.00")])])},function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{staticClass:"info-contnet-item"},[s("span",{staticClass:"title"},[t._v("收款门店")]),s("span",[t._v("应凯科技")])])}],n=(s("e9c4"),s("4d90"),s("d3b7"),s("25f0"),s("99af"),s("fb6a"),s("b775")),o=(s("5c96"),{name:"Formedit",components:{},props:["idx","selectServer"],data:function(){return{formdata:{refno:""}}},computed:{formcontainHeight:function(){return window.innerHeight-50-23-50+"px"}},watch:{idx:function(t,a){}},created:function(){this.bindData()},methods:{bindData:function(){var t=this,a=this.$route.query.id;console.log("支付",a),n["a"].get("/system/SYSM10B2/getBillEntity?key="+a).then((function(a){200==a.data.code&&(t.formdata=a.data.data,t.formdata.payment="支付宝")}))},goback:function(){this.readnav(),this.$store.dispatch("tagsView/delView",this.$route),this.$router.push({path:"/SYSM02/B1"})},readnav:function(){var t=this;n["a"].get("/system/SYSM02B2/getMenuWebListBySelf").then((function(a){if(200==a.data.code){var s=a.data.data;localStorage.setItem("navjson",JSON.stringify(s)),t.$store.dispatch("app/setnavdata",s)}})).catch((function(t){console.log(t)}))},cleAddValidate:function(){this.$refs.formdata.clearValidate("Address")}},filters:{dateFormat:function(t){if(t){var a=new Date(t),s=a.getFullYear(),e=(a.getMonth()+1).toString().padStart(2,"0"),i=a.getDate().toString().padStart(2,"0"),n=a.getHours().toString().padStart(2,"0"),o=a.getMinutes().toString().padStart(2,"0"),c=a.getSeconds().toString().padStart(2,"0");return"".concat(s,"-").concat(e,"-").concat(i," ").concat(n,":").concat(o,":").concat(c)}},dataCycleFormat:function(t){var a=t.slice(0),s="";switch(a[0]){case"W":s=a[1]+"周";break;case"M":s=a[1]+"月";break;case"Y":s=a[1]+"年";break;default:s=t;break}return s}}}),c=o,r=(s("6d7d"),s("2877")),d=Object(r["a"])(c,e,i,!1,null,"75100f48",null);a["default"]=d.exports}}]);