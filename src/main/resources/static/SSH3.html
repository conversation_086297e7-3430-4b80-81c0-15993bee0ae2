<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSH流水线管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 1.1rem;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 1fr 1.5fr;  /* 让右侧执行状态框更宽 */
            gap: 30px;
            margin-bottom: 30px;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .icon {
            width: 24px;
            height: 24px;
            fill: #667eea;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-weight: 600;
            margin-bottom: 8px;
            color: #555;
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }
        .btn:disabled { /* Style for disabled buttons */
            opacity: 0.6;
            cursor: not-allowed;
            background: #ccc; /* Generic disabled background */
            box-shadow: none;
        }
        .btn:disabled:hover {
            transform: none;
            box-shadow: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #56ab2f, #a8e6cf);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(86, 171, 47, 0.3);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f093fb, #f5576c);
            color: white;
        }

        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(245, 87, 108, 0.3);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff416c, #ff4b2b);
            color: white;
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(255, 65, 108, 0.3);
        }

        .btn-group {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .status-success {
            background: rgba(86, 171, 47, 0.1);
            color: #56ab2f;
        }

        .status-running {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        .status-failed {
            background: rgba(255, 65, 108, 0.1);
            color: #ff416c;
        }

        .status-pending {
            background: rgba(255, 193, 7, 0.1);
            color: #ffc107;
        }

        .execution-log {
            background: #1a1a1a;
            color: white;
            padding: 20px;
            border-radius: 12px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 600px;  /* 增大到600px */
            min-height: 400px;  /* 添加最小高度 */
            overflow-y: auto;
            margin-top: 20px;
            white-space: pre-wrap;
            word-break: break-all;
            border: 1px solid #333;  /* 添加边框 */
        }

        /* 为执行状态卡片添加特殊样式 */
        .execution-status-card {
            min-height: 700px;  /* 设置最小高度 */
            max-height: none;    /* 移除最大高度限制 */
        }

        /* 格式化日志样式 */
        .formatted-log {
            line-height: 1.2;
            letter-spacing: 0.5px;
        }

        .log-entry {
            margin: 2px 0;
            padding: 1px 0;
        }

        /* 美化滚动条 */
        .execution-log::-webkit-scrollbar {
            width: 8px;
        }

        .execution-log::-webkit-scrollbar-track {
            background: #2a2a2a;
            border-radius: 4px;
        }

        .execution-log::-webkit-scrollbar-thumb {
            background: #555;
            border-radius: 4px;
        }

        .execution-log::-webkit-scrollbar-thumb:hover {
            background: #777;
        }

        /* 全屏模式样式 */
        .fullscreen-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.95);
            z-index: 9999;
            display: none;
            padding: 20px;
            box-sizing: border-box;
        }

        .fullscreen-content {
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
        }

        .fullscreen-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #eee;
        }

        .fullscreen-log {
            flex: 1;
            background: #1a1a1a;
            color: white;
            padding: 20px;
            border-radius: 12px;
            font-family: 'Courier New', monospace;
            font-size: 1rem;
            overflow-y: auto;
            white-space: pre-wrap;
            word-break: break-all;
            border: 1px solid #333;
        }

        /* 响应式调整 */
        @media (max-width: 1200px) {
            .main-grid {
                grid-template-columns: 1fr;  /* 小屏幕时垂直排列 */
            }

            .execution-log {
                max-height: 500px;
                min-height: 300px;
            }
        }

        .pipeline-steps {
            margin-top: 20px;
        }

        .step-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border-radius: 10px;
            margin-bottom: 8px;
            background: rgba(102, 126, 234, 0.05);
            transition: all 0.3s ease;
        }

        .step-item:hover {
            background: rgba(102, 126, 234, 0.1);
        }

        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #667eea;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.9rem;
            flex-shrink: 0;
        }

        .step-content {
            flex: 1;
            min-width: 0;
        }

        .step-name {
            font-weight: 600;
            margin-bottom: 4px;
        }

        .step-command {
            font-family: 'Courier New', monospace;
            background: rgba(0, 0, 0, 0.05);
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 0.8rem;
            display: inline-block;
            word-break: break-all;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e1e5e9;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.3s ease;
            width: 0%;
        }

        .server-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .server-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .server-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #56ab2f, #a8e6cf);
        }

        .server-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
        }

        .server-name {
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 8px;
        }

        .server-info {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 16px;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 20px;
            padding: 30px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: background 0.3s ease;
        }

        .close-btn:hover {
            background: rgba(0, 0, 0, 0.1);
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 16px 20px;
            border-radius: 12px;
            color: white;
            font-weight: 600;
            z-index: 1001;
            animation: notificationSlideIn 0.3s ease;
            max-width: 400px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .notification.success {
            background: linear-gradient(135deg, #56ab2f, #a8e6cf);
        }

        .notification.error {
            background: linear-gradient(135deg, #ff416c, #ff4b2b);
        }

        .notification.info {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }
        .notification.warning {
            background: linear-gradient(135deg, #ffc107, #ff9800);
        }

        @keyframes notificationSlideIn {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes notificationSlideOut {
            to {
                opacity: 0;
                transform: translateX(100%);
            }
        }

        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
            }

            .btn-group {
                flex-direction: column;
            }

            .btn {
                justify-content: center;
            }

            .modal-content {
                width: 95%;
            }
            .notification {
                width: calc(100% - 40px);
                max-width: none;
                right: 20px;
                left: 20px;
                top: 10px;
            }
        }

        /* 流水线详情样式 */
        .steps-table-container {
            margin-top: 20px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .steps-table {
            width: 100%;
            border-collapse: collapse;
            border-spacing: 0;
            background: #fff;
        }

        .steps-table th,
        .steps-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e1e5e9;
        }

        .steps-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
        }

        .steps-table tbody tr:hover {
            background-color: rgba(102, 126, 234, 0.05);
        }

        .steps-table td:first-child {
            width: 60px;
            text-align: center;
            font-weight: 600;
            color: #667eea;
        }

        #pipelineDetailName {
            margin: 10px 0;
            font-size: 1.3rem;
            color: #444;
            border-bottom: 2px solid #e1e5e9;
            padding-bottom: 8px;
        }

        /* 步骤颜色样式 */
        .step-odd-color {
            color: #ffff33; /* 黄色 */
        }
        .step-even-color {
            color: #00ff00; /* 绿色 */
        }

        /* 终端输出样式 */
        .terminal-command {
            color: #00ff00; /* 绿色 - 用户输入的命令 */
        }
        .terminal-output {
            color: #ffff33; /* 黄色 - 命令执行结果 */
        }
        .terminal-info {
            color: #87ceeb; /* 天蓝色 - 系统信息 */
        }
        .terminal-error {
            color: #ff6b6b; /* 红色 - 错误信息 */
        }

        /* 执行总结相关样式 - 全新设计 */
        .summary-section {
            margin: 40px 0;
            padding: 0;
            background: #ffffff;
            border-radius: 24px;
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.1),
                0 8px 16px rgba(0, 0, 0, 0.06),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            overflow: hidden;
            position: relative;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .summary-header {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #a855f7 100%);
            padding: 32px 40px;
            position: relative;
            overflow: hidden;
        }

        .summary-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.15"/><circle cx="20" cy="80" r="0.5" fill="white" opacity="0.15"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }

        .summary-title {
            font-size: 2rem;
            font-weight: 800;
            margin: 0;
            color: white;
            display: flex;
            align-items: center;
            gap: 16px;
            position: relative;
            z-index: 1;
        }

        .summary-title::before {
            content: '📈';
            font-size: 2.4rem;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
        }

        .summary-content {
            padding: 40px;
            background: #ffffff;
        }

        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: #ffffff;
            padding: 28px 24px;
            border-radius: 20px;
            text-align: center;
            border: 2px solid #f1f5f9;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.15),
                0 12px 24px rgba(0, 0, 0, 0.1);
            border-color: transparent;
        }

        .stat-number {
            font-size: 3.2rem;
            font-weight: 900;
            margin-bottom: 8px;
            display: block;
            line-height: 1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            font-size: 1rem;
            color: #64748b;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .stat-total::before {
            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
        }
        .stat-total .stat-number {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .stat-success::before {
            background: linear-gradient(90deg, #10b981, #059669);
        }
        .stat-success .stat-number {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .stat-failed::before {
            background: linear-gradient(90deg, #ef4444, #dc2626);
        }
        .stat-failed .stat-number {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .stat-time::before {
            background: linear-gradient(90deg, #f59e0b, #d97706);
        }
        .stat-time .stat-number {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .summary-status {
            text-align: center;
            margin: 32px 0;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 12px;
            padding: 16px 32px;
            border-radius: 60px;
            font-size: 1.1rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
            transition: all 0.3s ease;
        }

        .status-badge:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.18);
        }

        .status-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }

        .status-failed {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
        }

        .status-partial {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
        }

        .summary-failed-step {
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            border: 2px solid #fecaca;
            border-radius: 16px;
            padding: 20px 24px;
            margin-top: 24px;
            color: #dc2626;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.1);
        }

        .summary-failed-step::before {
            content: '⚠️ ';
            margin-right: 8px;
            font-size: 1.2rem;
        }
    </style>
</head>
<body>
<div class="container">
    <div class="header">
        <h1>SSH流水线管理系统</h1>
        <p>高效的服务器批量管理和自动化运维平台</p>
    </div>

    <div class="main-grid">
        <div class="card">
            <div class="card-title">
                <svg class="icon" viewBox="0 0 24 24">
                    <path d="M13,9H11V7H13M13,17H11V15H13M13,11H21V13H13M3,3H21V5H3M3,7H17V9H3M3,11H21V13H3M3,15H17V17H3M3,19H21V21H3V19Z"/>
                </svg>
                快速操作
            </div>

            <div class="form-group">
                <label class="form-label" for="serverSelect">选择服务器</label>
                <select id="serverSelect" class="form-select">
                    <option value="">请选择服务器...</option>
                </select>
            </div>

            <div class="btn-group">
                <button id="testConnectionBtn" class="btn btn-primary">
                    <svg class="icon" viewBox="0 0 24 24" style="fill: white;">
                        <path d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"/>
                    </svg>
                    测试连接
                </button>
                <button id="quickShutdownBtn" class="btn btn-danger">
                    <svg class="icon" viewBox="0 0 24 24" style="fill: white;">
                        <path d="M16.56,5.44L15.11,6.89C16.84,7.94 18,9.83 18,12A6,6 0 0,1 12,18A6,6 0 0,1 6,12C6,9.83 7.16,7.94 8.88,6.88L7.44,5.44C5.36,6.88 4,9.28 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12Z"/>
                    </svg>
                    关机
                </button>
                <button id="quickRestartBtn" class="btn btn-warning">
                    <svg class="icon" viewBox="0 0 24 24" style="fill: white;">
                        <path d="M9,5V9H21V5M9,19H21V15H9M9,14H21V10H9M4,9H8V5H4M4,19H8V15H4M4,14H8V10H4"/>
                    </svg>
                    重启
                </button>
            </div>
        </div>

        <div class="card">
            <div class="card-title">
                <svg class="icon" viewBox="0 0 24 24">
                    <path d="M3,3H21V5H3V3M3,7H17V9H3M3,11H21V13H3M3,15H17V17H3M3,19H21V21H3V19Z"/>
                </svg>
                流水线执行
            </div>

            <div class="form-group">
                <label class="form-label" for="pipelineSelect">选择流水线</label>
                <div style="display: flex; gap: 10px;">
                    <select id="pipelineSelect" class="form-select" style="flex: 1;">
                        <option value="">请选择流水线...</option>
                    </select>
                    <button id="viewPipelineDetailsBtn" class="btn btn-primary" style="white-space: nowrap;" disabled>
                        <svg class="icon" viewBox="0 0 24 24" style="fill: white;">
                            <path d="M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17H7L4.5,13.5L12,10L19.5,13.5L12,17Z"/>
                        </svg>
                        查看详情
                    </button>
                </div>
            </div>

            <div class="btn-group">
                <button id="executeIncrementalPipelineBtn" class="btn btn-success">
                    <svg class="icon" viewBox="0 0 24 24" style="fill: white;">
                        <path d="M8,5.14V19.14L19,12.14L8,5.14Z"/>
                    </svg>
                    执行流水线
                </button>
                <button id="executeAsyncBtn" class="btn btn-primary">
                    <svg class="icon" viewBox="0 0 24 24" style="fill: white;">
                        <path d="M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
                    </svg>
                    异步执行
                </button>
                <button id="batchExecuteBtn" class="btn btn-warning">
                    <svg class="icon" viewBox="0 0 24 24" style="fill: white;">
                        <path d="M9,5V9H21V5M9,19H21V15H9M9,14H21V10H9M4,9H8V5H4M4,19H8V15H4M4,14H8V10H4"/>
                    </svg>
                    批量执行
                </button>
            </div>
        </div>
    </div>

    <div class="card execution-status-card">
        <div class="card-title">
            <svg class="icon" viewBox="0 0 24 24">
                <path d="M13,9A1,1 0 0,0 12,8A1,1 0 0,0 11,9V11A1,1 0 0,0 12,12A1,1 0 0,0 13,11M13,17H11V15H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2"/>
            </svg>
            执行状态
            <button id="fullscreenBtn" class="btn btn-secondary" style="margin-left: auto; margin-right: 10px; padding: 5px 10px; font-size: 0.8rem;" onclick="toggleFullscreen()">
                📺 全屏
            </button>
            <div id="executionStatus" class="status-indicator status-pending">
                <div class="loading" style="display: none;"></div>
                <span>就绪</span>
            </div>
        </div>

        <div class="progress-bar">
            <div id="progressFill" class="progress-fill"></div>
        </div>

        <div id="pipelineSteps" class="pipeline-steps">
            <p>请选择一个流水线以查看步骤.</p>
        </div>

        <div id="executionLog" class="execution-log" style="display: none;">
            # SSH流水线执行日志
        </div>
    </div>

    <div class="card" style="margin-top: 30px;">
        <div class="card-title">
            <svg class="icon" viewBox="0 0 24 24">
                <path d="M4,1H20A1,1 0 0,1 21,2V6A1,1 0 0,1 20,7H4A1,1 0 0,1 3,6V2A1,1 0 0,1 4,1M4,9H8V5H4M4,19H8V15H4M4,14H8V10H4"/>
            </svg>
            服务器管理
        </div>

        <div class="server-list">
            <p>点击上方"选择服务器"下拉框以加载列表.</p>
        </div>
    </div>
</div>

<!-- 流水线详情模态窗口 -->
<div id="pipelineDetailsModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <div class="modal-title">流水线详情</div>
            <button class="close-btn">&times;</button>
        </div>
        <div id="pipelineDetailsContent">
            <h3 id="pipelineDetailName">流水线名称</h3>
            <div class="steps-table-container">
                <table class="steps-table">
                    <thead>
                    <tr>
                        <th>步骤</th>
                        <th>步骤名称</th>
                        <th>执行命令</th>
                    </tr>
                    </thead>
                    <tbody id="pipelineStepsTableBody">
                    <!-- 步骤数据将被动态添加 -->
                    </tbody>
                </table>
            </div>
        </div>
        <div class="btn-group">
            <button class="btn btn-primary" onclick="closeModal('pipelineDetailsModal')">关闭</button>
        </div>
    </div>
</div>

<div id="terminalModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <div class="modal-title">SSH终端</div>
            <button class="close-btn">&times;</button>
        </div>
        <div class="form-group">
            <label class="form-label" for="terminalCommand">执行命令</label>
            <input type="text" id="terminalCommand" class="form-input" placeholder="输入SSH命令..." autocomplete="off">
            <small style="color: #666; margin-top: 5px; display: block;">提示: 按回车键快速执行命令</small>
        </div>
        <div class="btn-group">
            <button id="executeCommandBtn" class="btn btn-primary">执行命令</button>
            <button class="btn btn-warning">清空输出</button>
        </div>
        <div id="terminalOutput" class="execution-log" style="margin-top:20px;">
            $ 等待命令输入...
        </div>
    </div>
</div>

<div id="batchModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <div class="modal-title">批量执行流水线</div>
            <button class="close-btn">&times;</button>
        </div>
        <div class="form-group">
            <label class="form-label">选择服务器 (请先选择上方主要操作区的流水线)</label>
            <div>
                <p>服务器列表将在此处显示 (请先点击"选择服务器"下拉框).</p>
            </div>
        </div>
        <div class="btn-group">
            <button id="executeBatchPipelineBtn" class="btn btn-success">批量执行选中服务器</button>
            <button class="btn btn-danger" onclick="closeModal('batchModal')">取消</button>
        </div>
    </div>
</div>

<script>
    // API base URLs
    const API_SERVER_PREFIX = 'http://*************:10684';


    const API_BASE_URL_S34M11B1 = `${API_SERVER_PREFIX}/S34M11B1`;
    const API_BASE_URL_S34M11B2 = `${API_SERVER_PREFIX}/S34M11B2`;


    // Global state
    let currentSessionId = null;
    let executionStatusInterval = null;
    let serversData = {};
    let pipelinesData = {};
    let currentTerminalServerId = null;

    let serversLoaded = false;
    let pipelinesLoaded = false;

    // DOM Elements
    const serverSelect = document.getElementById('serverSelect');
    const pipelineSelect = document.getElementById('pipelineSelect');
    const executionLog = document.getElementById('executionLog');
    const terminalOutput = document.getElementById('terminalOutput');
    const executionStatusDiv = document.getElementById('executionStatus');
    const progressFill = document.getElementById('progressFill');
    const pipelineStepsDiv = document.getElementById('pipelineSteps');
    const loadingSpinner = executionStatusDiv.querySelector('.loading');
    const statusText = executionStatusDiv.querySelector('span');
    const terminalModal = document.getElementById('terminalModal');
    const batchModal = document.getElementById('batchModal');
    const pipelineDetailsModal = document.getElementById('pipelineDetailsModal');

    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM Content Loaded - Initializing SSH3 application');

        // 检查关键DOM元素是否存在
        const requiredElements = [
            'serverSelect', 'pipelineSelect', 'executionLog', 'terminalOutput',
            'executionStatus', 'progressFill', 'pipelineSteps', 'terminalModal',
            'batchModal', 'pipelineDetailsModal'
        ];

        const missingElements = requiredElements.filter(id => !document.getElementById(id));
        if (missingElements.length > 0) {
            console.error('Missing required DOM elements:', missingElements);
            showNotification('页面初始化失败，缺少必要元素!', 'error');
            return;
        }

        console.log('All required DOM elements found');
        initializeEventListeners();
        updatePipelineStepsUI();

        // Disable specified buttons
        document.getElementById('quickShutdownBtn').disabled = true;
        document.getElementById('quickRestartBtn').disabled = true;
        document.getElementById('executeAsyncBtn').disabled = true;

        console.log('SSH3 application initialized successfully');
    });

    function initializeEventListeners() {
        document.getElementById('testConnectionBtn').addEventListener('click', () => handleTestConnection());
        // Event listeners for other buttons are still set up, but the buttons themselves are disabled above
        document.getElementById('quickShutdownBtn').addEventListener('click', handleQuickShutdown);
        document.getElementById('quickRestartBtn').addEventListener('click', handleQuickRestart);
        document.getElementById('executeAsyncBtn').addEventListener('click', handleExecuteAsyncPipeline);

        document.getElementById('executeIncrementalPipelineBtn').addEventListener('click', handleExecuteIncrementalPipeline);
        document.getElementById('batchExecuteBtn').addEventListener('click', () => openModal('batchModal'));
        document.getElementById('executeBatchPipelineBtn').addEventListener('click', handleExecuteBatchPipeline);
        document.getElementById('executeCommandBtn').addEventListener('click', handleExecuteTerminalCommand);

        terminalModal.querySelector('.close-btn').addEventListener('click', () => closeModal('terminalModal'));
        batchModal.querySelector('.close-btn').addEventListener('click', () => closeModal('batchModal'));
        pipelineDetailsModal.querySelector('.close-btn').addEventListener('click', () => closeModal('pipelineDetailsModal'));
        terminalModal.querySelector('button.btn-warning').addEventListener('click', clearTerminalOutput);

        pipelineSelect.addEventListener('change', function() {
            updatePipelineStepsUI();
            // 启用或禁用查看详情按钮
            document.getElementById('viewPipelineDetailsBtn').disabled = !this.value;
        });

        serverSelect.addEventListener('mousedown', async () => {
            if (!serversLoaded) {
                const originalFirstOption = serverSelect.options[0];
                addTemporaryLoadingOption(serverSelect, "正在加载服务器...");
                await loadServers();
                removeTemporaryLoadingOption(serverSelect, "正在加载服务器...", originalFirstOption);
            }
        });

        pipelineSelect.addEventListener('mousedown', async () => {
            if (!pipelinesLoaded) {
                const originalFirstOption = pipelineSelect.options[0];
                addTemporaryLoadingOption(pipelineSelect, "正在加载流水线...");
                await loadPipelines();
                removeTemporaryLoadingOption(pipelineSelect, "正在加载流水线...", originalFirstOption);
            }
        });

        document.getElementById('executeIncrementalPipelineBtn').addEventListener('click', handleExecuteIncrementalPipeline);
        document.getElementById('viewPipelineDetailsBtn').addEventListener('click', () => {
            const pipelineId = pipelineSelect.value;
            if (!pipelineId) {
                showNotification('请选择一个流水线!', 'warning');
                return;
            }
            updatePipelineDetailsUI(pipelineId);
            openModal('pipelineDetailsModal');
        });

        // 添加终端命令输入框的回车键监听
        const terminalCommandInput = document.getElementById('terminalCommand');
        if (terminalCommandInput) {
            terminalCommandInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    handleExecuteTerminalCommand();
                }
            });
        }
    }

    function addTemporaryLoadingOption(selectElement, text) {
        if (selectElement.options.length > 0 && selectElement.options[0].dataset.tempLoading) {
            selectElement.remove(0);
        }
        const loadingOption = document.createElement('option');
        loadingOption.value = "";
        loadingOption.textContent = text;
        loadingOption.disabled = true;
        loadingOption.dataset.tempLoading = "true";
        if (selectElement.options.length > 0 && !selectElement.options[0].dataset.tempLoading) {
            selectElement.add(loadingOption, selectElement.options[0]);
        } else {
            selectElement.add(loadingOption, null);
        }
        selectElement.value = "";
    }

    function removeTemporaryLoadingOption(selectElement, loadingText, originalFirstOption) {
        for (let i = 0; i < selectElement.options.length; i++) {
            if (selectElement.options[i].dataset.tempLoading === "true" && selectElement.options[i].textContent === loadingText) {
                selectElement.remove(i);
                break;
            }
        }
        if (selectElement.options.length === 0 || (selectElement.options.length === 1 && selectElement.options[0].value === "")) {
            if (originalFirstOption && originalFirstOption.value === "") {
                if (!Array.from(selectElement.options).find(opt => opt.value === "")) {
                    selectElement.add(originalFirstOption, selectElement.options[0]);
                }
            }
            selectElement.value = "";
        }
    }

    async function loadServers() {
        try {
            const response = await fetch(`${API_BASE_URL_S34M11B1}/getPageList`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ pageSize: 1000, pageNum: 1 })
            });
            if (!response.ok) throw new Error(`服务器列表加载失败: ${response.statusText} (Code: ${response.status})`);
            const result = await response.json();

            for (let i = serverSelect.options.length - 1; i >= 0; i--) {
                if (serverSelect.options[i].dataset.tempLoading !== "true") {
                    serverSelect.remove(i);
                }
            }

            if (result.code === 200 && result.data && result.data.list) {
                serversData = {};
                const servers = result.data.list;

                const defaultOption = document.createElement('option');
                defaultOption.value = "";
                defaultOption.textContent = "请选择服务器...";
                serverSelect.appendChild(defaultOption);

                servers.forEach(server => {
                    serversData[server.id] = server;
                    const option = document.createElement('option');
                    option.value = server.id;
                    option.textContent = `${server.servername || '未命名服务器'} (${server.host || 'N/A'})`;
                    serverSelect.appendChild(option);
                });
                serverSelect.value = "";

                const serverListDiv = document.querySelector('.server-list');
                serverListDiv.innerHTML = '';
                const batchModalServerList = batchModal.querySelector('.form-group > div');
                batchModalServerList.innerHTML = '';

                if (servers.length === 0) {
                    serverListDiv.innerHTML = '<p>没有可用的服务器.</p>';
                    batchModalServerList.innerHTML = '<p>没有可用的服务器.</p>';
                } else {
                    servers.forEach(server => {
                        // 创建服务器卡片容器
                        const serverCard = document.createElement('div');
                        serverCard.className = 'server-card';

                        // 服务器名称
                        const serverName = document.createElement('div');
                        serverName.className = 'server-name';
                        serverName.textContent = server.servername || '未命名服务器';

                        // 服务器信息
                        const serverInfo = document.createElement('div');
                        serverInfo.className = 'server-info';
                        serverInfo.textContent = `${server.host || 'N/A'}:${server.port || 'N/A'} | ${server.username || 'N/A'} | ${server.groupname || 'N/A'}`;

                        // 按钮组
                        const btnGroup = document.createElement('div');
                        btnGroup.className = 'btn-group';

                        // 测试连接按钮
                        const testBtn = document.createElement('button');
                        testBtn.className = 'btn btn-primary btn-sm';
                        testBtn.textContent = '测试连接';
                        testBtn.addEventListener('click', () => handleTestConnection(server.id));

                        // 终端按钮
                        const terminalBtn = document.createElement('button');
                        terminalBtn.className = 'btn btn-success btn-sm';
                        terminalBtn.textContent = '终端';
                        terminalBtn.addEventListener('click', () => {
                            console.log('Terminal button clicked for server:', server.id);
                            openTerminalModal(server.id);
                        });

                        // 组装元素
                        btnGroup.appendChild(testBtn);
                        btnGroup.appendChild(terminalBtn);
                        serverCard.appendChild(serverName);
                        serverCard.appendChild(serverInfo);
                        serverCard.appendChild(btnGroup);
                        serverListDiv.appendChild(serverCard);

                        // 批量执行复选框
                        const checkboxLabel = document.createElement('label');
                        checkboxLabel.style.display = 'block';
                        checkboxLabel.style.marginBottom = '5px';
                        checkboxLabel.innerHTML = `<input type="checkbox" value="${server.id}" name="batchServers" style="margin-right: 5px;"> ${server.servername || '未命名服务器'} (${server.host || 'N/A'})`;
                        batchModalServerList.appendChild(checkboxLabel);
                    });
                }
                serversLoaded = true;
                showNotification('服务器列表加载成功!', 'success');
            } else {
                serverSelect.innerHTML = '<option value="" disabled>加载服务器失败</option>';
                throw new Error(result.msg || '服务器列表数据格式错误');
            }
        } catch (error) {
            console.error('Error loading servers:', error);
            showNotification(`加载服务器列表失败: ${error.message}`, 'error');
            const errorOption = document.createElement('option');
            errorOption.value = "";
            errorOption.textContent = "加载失败,点击重试";
            if (serverSelect.options.length === 0 || serverSelect.options[0].dataset.tempLoading === "true") {
                if(serverSelect.options[0] && serverSelect.options[0].dataset.tempLoading === "true") serverSelect.remove(0);
                serverSelect.add(errorOption,0);
            } else {
                serverSelect.options[0].textContent = "加载失败,点击重试";
            }
            serverSelect.value = "";
            document.querySelector('.server-list').innerHTML = `<p style="color:red;">加载服务器列表失败.</p>`;
            batchModal.querySelector('.form-group > div').innerHTML = `<p style="color:red;">加载服务器列表失败.</p>`;
        }
    }

    async function loadPipelines() {
        try {
            const response = await fetch(`${API_BASE_URL_S34M11B2}/getPageTh`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ pageSize: 1000, pageNum: 1 })
            });
            if (!response.ok) throw new Error(`流水线列表加载失败: ${response.statusText} (Code: ${response.status})`);
            const result = await response.json();

            if (result.code === 200 && result.data && result.data.list) {
                pipelinesData = {};
                const pipelines = result.data.list;

                const defaultOption = document.createElement('option');
                defaultOption.value = "";
                defaultOption.textContent = "请选择流水线...";
                pipelineSelect.appendChild(defaultOption);

                if (pipelines.length > 0) {
                    pipelines.forEach(pipeline => {
                        pipelinesData[pipeline.id] = {
                            name: pipeline.pipelinename || '未命名流水线',
                            steps: (pipeline.item || []).map(p_item => ({
                                name: p_item.stepName || p_item.name || '未命名步骤',
                                command: p_item.command || '无命令'
                            }))
                        };
                        const option = document.createElement('option');
                        option.value = pipeline.id;
                        option.textContent = pipeline.pipelinename || '未命名流水线';
                        pipelineSelect.appendChild(option);
                    });
                }
                pipelineSelect.value = "";
                pipelinesLoaded = true;
                showNotification('流水线列表加载成功!', 'success');
            } else {
                pipelineSelect.innerHTML = '<option value="" disabled>加载流水线失败</option>';
                throw new Error(result.msg || '流水线列表数据格式错误');
            }
        } catch (error) {
            console.error('Error loading pipelines:', error);
            showNotification(`加载流水线列表失败: ${error.message}`, 'error');
            const errorOption = document.createElement('option');
            errorOption.value = "";
            errorOption.textContent = "加载失败,点击重试";
            if (pipelineSelect.options.length === 0 || pipelineSelect.options[0].dataset.tempLoading === "true") {
                if(pipelineSelect.options[0] && pipelineSelect.options[0].dataset.tempLoading === "true") pipelineSelect.remove(0);
                pipelineSelect.add(errorOption,0);
            } else {
                pipelineSelect.options[0].textContent = "加载失败,点击重试";
            }
            pipelineSelect.value = "";
        }
    }

    function updateExecutionStatusUI(statusType, message, isLoading = false) {
        executionStatusDiv.className = 'status-indicator';
        switch (statusType) {
            case 'running': executionStatusDiv.classList.add('status-running'); break;
            case 'success': executionStatusDiv.classList.add('status-success'); break;
            case 'failed': executionStatusDiv.classList.add('status-failed'); break;
            case 'pending': default: executionStatusDiv.classList.add('status-pending'); break;
        }
        loadingSpinner.style.display = isLoading ? 'inline-block' : 'none';
        statusText.textContent = message || '就绪';
    }

    function updateProgressBarUI(percentage) {
        progressFill.style.width = `${Math.max(0, Math.min(100, percentage))}%`;
    }

    function appendToLog(text, area = 'execution', logType = 'default') {
        const logArea = area === 'terminal' ? terminalOutput : executionLog;
        logArea.style.display = 'block';
        const timestamp = new Date().toLocaleTimeString();

        // 终端区域的特殊处理
        if (area === 'terminal') {
            const line = document.createElement('div');

            // 根据日志类型设置样式
            if (logType === 'command') {
                line.className = 'terminal-command';
                line.textContent = text; // 命令不加时间戳
            } else if (logType === 'output') {
                line.className = 'terminal-output';
                line.textContent = text; // 输出不加时间戳
            } else if (logType === 'error') {
                line.className = 'terminal-error';
                line.textContent = text;
            } else if (logType === 'info') {
                line.className = 'terminal-info';
                line.textContent = `[${timestamp}] ${text}`;
            } else {
                // 默认样式
                line.textContent = `[${timestamp}] ${text}`;
            }

            logArea.appendChild(line);
            logArea.scrollTop = logArea.scrollHeight;
            return;
        }

        // 检查是否是执行总结行
        if (text.includes('[执行总结]')) {
            // 创建总结容器
            const summarySection = document.createElement('div');
            summarySection.className = 'summary-section';

            // 添加总结头部
            const summaryHeader = document.createElement('div');
            summaryHeader.className = 'summary-header';

            const summaryTitle = document.createElement('div');
            summaryTitle.className = 'summary-title';
            summaryTitle.textContent = '流水线执行总结';
            summaryHeader.appendChild(summaryTitle);
            summarySection.appendChild(summaryHeader);

            // 创建总结内容容器
            const summaryContent = document.createElement('div');
            summaryContent.className = 'summary-content';
            summarySection.appendChild(summaryContent);

            // 保存总结容器引用
            logArea.summaryContent = summaryContent;
            logArea.summarySection = summarySection;
            logArea.summaryStats = {}; // 用于收集统计数据
            logArea.appendChild(summarySection);
            return;
        }

        // 检查是否是失败原因信息（在执行总结内容中）
        if (logArea.summaryContent && text.includes('失败原因:')) {
            const line = document.createElement('div');
            line.className = 'summary-failed-step';
            line.textContent = text;
            logArea.summaryContent.appendChild(line);
            logArea.scrollTop = logArea.scrollHeight;
            return;
        }

        // 检查是否是总结内容（在执行总结后的其他文本）
        if (logArea.summaryContent && !text.includes('[步骤 ')) {
            // 解析统计数据
            if (text.includes('总步骤数:')) {
                const match = text.match(/总步骤数:\s*(\d+)/);
                if (match) logArea.summaryStats.total = parseInt(match[1]);
            } else if (text.includes('成功步骤:')) {
                const match = text.match(/成功步骤:\s*(\d+)/);
                if (match) logArea.summaryStats.success = parseInt(match[1]);
            } else if (text.includes('失败步骤:')) {
                const match = text.match(/失败步骤:\s*(\d+)/);
                if (match) logArea.summaryStats.failed = parseInt(match[1]);
            } else if (text.includes('总耗时:')) {
                const match = text.match(/总耗时:\s*(\d+)/);
                if (match) logArea.summaryStats.duration = parseInt(match[1]);
            } else if (text.includes('失败原因:')) {
                logArea.summaryStats.failureReason = text.replace('失败原因:', '').trim();
            }

            // 如果收集到了基本统计数据，生成美化的总结
            if (logArea.summaryStats.total !== undefined &&
                logArea.summaryStats.success !== undefined &&
                logArea.summaryStats.failed !== undefined) {
                generateBeautifulSummary(logArea);
            }
            return;
        }

        // 检查是否是步骤开始行（清除总结内容引用）
        if (text.includes('[步骤 ')) {
            logArea.summaryContent = null;

            // 提取步骤编号来应用不同颜色
            const stepMatch = text.match(/\[步骤 (\d+):/);
            if (stepMatch && stepMatch[1]) {
                const stepNumber = parseInt(stepMatch[1], 10);
                const colorClass = stepNumber % 2 === 1 ? 'step-odd-color' : 'step-even-color';

                const line = document.createElement('div');
                line.className = colorClass;
                line.textContent = `[${timestamp}] ${text}`;
                logArea.appendChild(line);
                logArea.scrollTop = logArea.scrollHeight;
                return;
            }
        }

        // 默认日志行
        const line = document.createElement('div');
        line.textContent = `[${timestamp}] ${text}`;
        logArea.appendChild(line);
        logArea.scrollTop = logArea.scrollHeight;
    }

    async function handleTestConnection(serverId = null) {
        console.log('handleTestConnection called with serverId:', serverId);
        console.log('serverSelect.value:', serverSelect.value);
        console.log('Available servers:', Object.keys(serversData));

        // 如果没有传入serverId参数，则从下拉框获取
        const targetServerId = serverId || serverSelect.value;

        console.log('Final targetServerId:', targetServerId);

        if (!targetServerId || targetServerId === '[object PointerEvent]') {
            showNotification('请选择一个服务器!', 'warning');
            console.error('Invalid serverId:', targetServerId);
            return;
        }
        const serverName = serversData[targetServerId]?.servername || targetServerId;
        updateExecutionStatusUI('running', `测试 ${serverName} 连接...`, true);
        appendToLog(`[INFO] 发起连接测试: ${serverName}`);
        try {
            const response = await fetch(`${API_BASE_URL_S34M11B2}/testConnection?serverId=${encodeURIComponent(targetServerId)}`);
            const result = await response.json();

            if (result.code === 200 && result.data === true) {
                showNotification(`${serverName} 连接成功!`, 'success');
                updateExecutionStatusUI('success', `连接成功: ${serverName}`, false);
                appendToLog(`[SUCCESS] ${serverName} 连接成功.`);
            } else {
                throw new Error(result.msg || '连接返回失败状态');
            }
        } catch (error) {
            console.error('Test connection error:', error);
            showNotification(`${serverName} 连接测试失败: ${error.message}`, 'error');
            updateExecutionStatusUI('failed', `连接测试失败: ${error.message}`, false);
            appendToLog(`[ERROR] ${serverName} 连接失败: ${error.message}`);
        }
    }

    async function handleQuickAction(actionType, endpoint, confirmMsgTemplate, successMsgTemplate) {
        const serverId = serverSelect.value;
        if (!serverId) {
            showNotification('请选择一个服务器!', 'warning');
            return;
        }
        const serverName = serversData[serverId]?.servername || serverId;
        if (!confirm(confirmMsgTemplate.replace('{serverName}', serverName))) return;

        updateExecutionStatusUI('running', `${actionType} ${serverName}...`, true);
        clearLog();
        appendToLog(`[INFO] 发起 ${actionType} 命令: ${serverName}`);
        try {
            const formData = new FormData();
            formData.append('serverId', serverId);
            const response = await fetch(`${API_BASE_URL_S34M11B2}${endpoint}`, { method: 'POST', body: formData });
            const result = await response.json();

            if (result.code === 200) {
                showNotification(successMsgTemplate.replace('{serverName}', serverName), 'success');
                updateExecutionStatusUI('success', `${actionType} 命令发送成功: ${result.data?.output || '已发送'}`, false);
                appendToLog(`[SUCCESS] ${actionType}: ${JSON.stringify(result.data)}`);
            } else {
                throw new Error(result.msg || `${actionType} 命令发送失败`);
            }
        } catch (error) {
            console.error(`Quick ${actionType} error:`, error);
            showNotification(`${serverName} ${actionType}失败: ${error.message}`, 'error');
            updateExecutionStatusUI('failed', `${actionType}失败: ${error.message}`, false);
            appendToLog(`[ERROR] ${actionType}失败: ${error.message}`);
        }
    }

    function handleQuickShutdown() {
        // This button is disabled, so this function won't be called by user click
        if(document.getElementById('quickShutdownBtn').disabled) return;
        handleQuickAction('关机', '/quickShutdown', '确定要关闭服务器 {serverName} 吗?', '服务器 {serverName} 关机命令已发送!');
    }

    function handleQuickRestart() {
        // This button is disabled
        if(document.getElementById('quickRestartBtn').disabled) return;
        handleQuickAction('重启', '/quickRestart', '确定要重启服务器 {serverName} 吗?', '服务器 {serverName} 重启命令已发送!');
    }

    async function handleExecutePipeline() {
        const pipelineId = pipelineSelect.value;
        const serverId = serverSelect.value;

        if (!pipelineId || !serverId) {
            showNotification('请选择流水线和服务器!', 'warning');
            return;
        }
        if (executionStatusInterval) clearInterval(executionStatusInterval);

        const pipelineName = pipelinesData[pipelineId]?.name || pipelineId;
        updateExecutionStatusUI('running', `执行 ${pipelineName}...`, true);
        updateProgressBarUI(0);
        clearLog();
        updatePipelineStepsUI();
        appendToLog(`[INFO] 开始执行流水线: ${pipelineName} on ${serversData[serverId]?.servername || serverId}`);

        try {
            const formData = new FormData();
            formData.append('pipelineId', pipelineId);
            formData.append('serverId', serverId);

            const response = await fetch(`${API_BASE_URL_S34M11B2}/executePipeline`, { method: 'POST', body: formData });
            const result = await response.json();

            if (result.code === 200 && result.data) {
                const executionResult = result.data;
                appendToLog(`[PIPELINE LOG]\n${executionResult.output || '无输出.'}`);
                const stepsToUpdate = pipelinesData[pipelineId]?.steps || [];
                if (executionResult.success) {
                    showNotification('流水线执行成功!', 'success');
                    updateExecutionStatusUI('success', '流水线执行成功', false);
                    updateProgressBarUI(100);
                    stepsToUpdate.forEach((_,idx) => updateStepStatusUI(idx, 'success', '完成'));
                } else {
                    showNotification('流水线执行失败!', 'error');
                    updateExecutionStatusUI('failed', `流水线执行失败: ${executionResult.errorMessage || '未知错误'}`, false);
                    stepsToUpdate.forEach((_,idx) => updateStepStatusUI(idx, 'failed', '失败'));
                }
            } else {
                throw new Error(result.msg || '流水线执行请求失败');
            }
        } catch (error) {
            console.error('Execute pipeline error:', error);
            showNotification(`流水线执行出错: ${error.message}`, 'error');
            updateExecutionStatusUI('failed', `执行出错: ${error.message}`, false);
            (pipelinesData[pipelineId]?.steps || []).forEach((_,idx) => updateStepStatusUI(idx, 'failed', '错误'));
        }
    }

    /**
     * 增量执行流水线 - 每步都能实时查看SSH控制台输出
     */
    async function handleExecuteIncrementalPipeline() {
        const pipelineId = pipelineSelect.value;
        const serverId = serverSelect.value;

        if (!pipelineId || !serverId) {
            showNotification('请选择流水线和服务器!', 'warning');
            return;
        }

        const pipelineName = pipelinesData[pipelineId]?.name || pipelineId;
        updateExecutionStatusUI('running', `执行 ${pipelineName}...`, true);
        updateProgressBarUI(0);
        clearLog();
        updatePipelineStepsUI();
        appendToLog(`[INFO] 开始执行流水线: ${pipelineName} on ${serversData[serverId]?.servername || serverId}`);

        try {
            const formData = new FormData();
            formData.append('pipelineId', pipelineId);
            formData.append('serverId', serverId);

            // 调用增量执行API
            const response = await fetch(`${API_BASE_URL_S34M11B2}/executeIncrementalPipeline`, { method: 'POST', body: formData });
            const result = await response.json();

            if (result.code === 200 && result.data) {
                const sessionId = result.data;
                showNotification('流水线增量执行已开始!', 'info');

                // 使用轮询获取执行状态和步骤输出
                pollIncrementalExecutionStatus(sessionId, pipelineId);
            } else {
                throw new Error(result.msg || '流水线执行请求失败');
            }
        } catch (error) {
            console.error('Execute incremental pipeline error:', error);
            showNotification(`流水线执行出错: ${error.message}`, 'error');
            updateExecutionStatusUI('failed', `执行出错: ${error.message}`, false);
            (pipelinesData[pipelineId]?.steps || []).forEach((_,idx) => updateStepStatusUI(idx, 'failed', '错误'));
        }
    }

    /**
     * 轮询增量执行状态 - 实时更新每个步骤的SSH控制台输出
     */
    async function pollIncrementalExecutionStatus(sessionId, pipelineId) {
        let lastCompletedSteps = -1; // 上次完成的步骤数
        let lastStepOutput = {};     // 存储每个步骤的输出
        let executionResult = null;  // 存储最新的执行结果

        // 启动轮询
        const interval = setInterval(async () => {
            try {
                // 获取执行状态
                const response = await fetch(`${API_BASE_URL_S34M11B2}/getExecutionStatus?sessionId=${sessionId}`);
                const result = await response.json();

                if (result.code !== 200) {
                    throw new Error(result.msg || '获取执行状态失败');
                }

                executionResult = result.data;
                if (!executionResult) {
                    throw new Error('会话已失效或未找到');
                }

                // 更新整体进度
                updateProgressBarUI(executionResult.progressPercentage || 0);

                // 获取步骤结果和日志
                const stepResults = executionResult.stepResults || [];
                const logs = executionResult.logs || [];
                const currentStepIndex = executionResult.currentStepIndex;
                const completedSteps = executionResult.completedSteps || 0;
                const stepsToUpdate = pipelinesData[pipelineId]?.steps || [];

                // 如果有新的日志，优先显示日志格式
                if (logs.length > 0) {
                    displayFormattedLogs(logs);
                } else if (stepResults.length > 0) {
                    // 如果没有新日志但有步骤结果，显示传统格式
                    displayStepResults(stepResults);
                }

                // 只处理新完成的步骤
                if (completedSteps > lastCompletedSteps) {
                    // 更新新完成步骤的状态和输出
                    for (let i = lastCompletedSteps + 1; i <= completedSteps; i++) {
                        if (i <= 0 || i > stepResults.length) continue;

                        const stepResult = stepResults[i-1];
                        const stepId = i-1;

                        // 检查是否为系统日志
                        if (stepResult.isSystemLog) {
                            // 系统日志不显示为独立步骤，而是作为前一步骤的补充信息
                            appendToLog(`${stepResult.output}`);
                            continue; // 跳过后续处理
                        }

                        // 如果这个步骤的输出没有显示过，则添加到日志
                        if (!lastStepOutput[stepId] && stepResult) {
                            // 确定步骤名称
                            let stepName = stepResult.stepName || `步骤 ${stepId+1}`;
                            if (!stepResult.stepName && stepsToUpdate[stepId]?.name) {
                                stepName = stepsToUpdate[stepId].name;
                            }

                            // 添加步骤信息到日志
                            appendToLog(`\n[步骤 ${stepId+1}: ${stepName}]`);
                            appendToLog(`命令: ${stepResult.commandText || '未知命令'}`);
                            
                            if (stepResult.output) {
                                appendToLog(`输出:\n${stepResult.output}`);
                            }
                            
                            if (stepResult.error) {
                                appendToLog(`错误:\n${stepResult.error}`);
                            }
                            
                            appendToLog(`状态: ${stepResult.status || '未知'}`);
                            appendToLog(`耗时: ${stepResult.durationMs || 0}毫秒`);
                            appendToLog('----------------------------');

                            // 更新步骤UI状态
                            const statusType = stepResult.status === 'Success' ? 'success' : 'failed';
                            updateStepStatusUI(stepId, statusType, stepResult.status);

                            // 标记此步骤输出已显示
                            lastStepOutput[stepId] = true;
                        }
                    }

                    // 更新最后完成的步骤计数
                    lastCompletedSteps = completedSteps;
                }

                // 更新执行状态UI
                if (executionResult.status === 'Completed' || executionResult.status === 'Failed') {
                    clearInterval(interval);
                    
                    // 查找最后一个失败的步骤
                    let failedStepInfo = "";
                    if (executionResult.status === 'Failed') {
                        for (let i = stepResults.length - 1; i >= 0; i--) {
                            if (stepResults[i].status === 'Failed' && !stepResults[i].isSystemLog) {
                                failedStepInfo = `在步骤 ${i+1} (${stepResults[i].stepName || '未命名'}) 执行失败，流水线终止`;
                                break;
                            }
                        }
                    }
                    
                    // 添加执行总结
                    appendToLog(`\n[执行总结]`);
                    appendToLog(`总步骤数: ${executionResult.totalSteps}`);
                    appendToLog(`成功步骤: ${executionResult.completedSteps}`);
                    appendToLog(`失败步骤: ${executionResult.totalSteps - executionResult.completedSteps}`);
                    if (failedStepInfo) {
                        appendToLog(`失败原因: ${failedStepInfo}`);
                    }
                    appendToLog(`总耗时: ${executionResult.durationMs}毫秒`);
                    appendToLog(`最终状态: ${executionResult.status}`);
                    
                    // 更新UI状态
                    updateExecutionStatusUI(
                        executionResult.status === 'Completed' ? 'success' : 'failed', 
                        `执行${executionResult.status === 'Completed' ? '成功' : '失败'}: ${pipelinesData[pipelineId]?.name || pipelineId}`,
                        false
                    );
                } else {
                    updateExecutionStatusUI('running', `执行中... (${executionResult.progressPercentage || 0}%)`, true);
                }
            } catch (error) {
                console.error('Poll status error:', error);
                updateExecutionStatusUI('failed', `轮询状态出错: ${error.message}`, false);
                clearInterval(interval);
            }
        }, 2000);
    }

    async function handleExecuteAsyncPipeline() {
        // This button is disabled
        if(document.getElementById('executeAsyncBtn').disabled) return;

        const pipelineId = pipelineSelect.value;
        const serverId = serverSelect.value;

        if (!pipelineId || !serverId) {
            showNotification('请选择流水线和服务器!', 'warning');
            return;
        }
        if (executionStatusInterval) clearInterval(executionStatusInterval);

        const pipelineName = pipelinesData[pipelineId]?.name || pipelineId;
        updateExecutionStatusUI('running', `提交异步任务 ${pipelineName}...`, true);
        updateProgressBarUI(0);
        clearLog();
        updatePipelineStepsUI();
        appendToLog(`[INFO] 提交异步流水线: ${pipelineName} on ${serversData[serverId]?.servername || serverId}`);

        try {
            const formData = new FormData();
            formData.append('pipelineId', pipelineId);
            formData.append('serverId', serverId);

            const response = await fetch(`${API_BASE_URL_S34M11B2}/executeAsyncPipeline`, { method: 'POST', body: formData });
            const result = await response.json();

            if (result.code === 200 && result.data) {
                currentSessionId = result.data;
                showNotification(`异步任务已提交, 会话ID: ${currentSessionId}`, 'info');
                updateExecutionStatusUI('running', `任务执行中 (ID: ${currentSessionId})...`, true);
                appendToLog(`[INFO] 异步任务提交成功. Session ID: ${currentSessionId}`);
                pollExecutionStatus(currentSessionId, pipelineId);
            } else {
                throw new Error(result.msg || '异步任务提交失败');
            }
        } catch (error) {
            console.error('Execute async pipeline error:', error);
            showNotification(`异步任务提交出错: ${error.message}`, 'error');
            updateExecutionStatusUI('failed', `提交出错: ${error.message}`, false);
        }
    }

    async function handleExecuteBatchPipeline() {
        const pipelineId = pipelineSelect.value;
        if (!pipelineId) {
            showNotification('请选择一个流水线!', 'warning');
            return;
        }

        const selectedServersCheckboxes = batchModal.querySelectorAll('input[name="batchServers"]:checked');
        const serverIds = Array.from(selectedServersCheckboxes).map(cb => cb.value);

        if (serverIds.length === 0) {
            showNotification('请至少选择一个服务器进行批量执行!', 'warning');
            return;
        }

        closeModal('batchModal');
        const pipelineName = pipelinesData[pipelineId]?.name || pipelineId;
        updateExecutionStatusUI('running', `批量执行 ${pipelineName} 在 ${serverIds.length}台服务器...`, true);
        clearLog();
        updateProgressBarUI(0);
        appendToLog(`[INFO] 开始批量执行流水线: ${pipelineName} on servers: ${serverIds.map(id => serversData[id]?.servername || id).join(', ')}`);

        try {
            const response = await fetch(`${API_BASE_URL_S34M11B2}/executePipelineMultiple?pipelineId=${encodeURIComponent(pipelineId)}`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(serverIds)
            });
            const result = await response.json();

            if (result.code === 200 && result.data) {
                let allSuccess = true;
                let successfulServers = 0;
                result.data.forEach((execResult, index) => {
                    const sName = serversData[execResult.serverId]?.servername || execResult.serverId;
                    appendToLog(`\n[BATCH SERVER: ${sName}] Success: ${execResult.success}\nOutput:\n${execResult.output || 'N/A'}\n`);
                    if (!execResult.success) allSuccess = false;
                    else successfulServers++;
                    updateProgressBarUI( ((index + 1) / result.data.length) * 100 );
                });

                const finalMessage = allSuccess ? '批量流水线执行全部成功!' : `批量执行完成: ${successfulServers}/${result.data.length} 成功 (详情见日志)`;
                showNotification(finalMessage, allSuccess ? 'success' : (successfulServers > 0 ? 'warning' : 'error'));
                updateExecutionStatusUI(allSuccess ? 'success' : 'failed', finalMessage, false);
            } else {
                throw new Error(result.msg || '批量执行请求失败');
            }
        } catch (error) {
            console.error('Execute batch pipeline error:', error);
            showNotification(`批量执行出错: ${error.message}`, 'error');
            updateExecutionStatusUI('failed', `批量执行出错: ${error.message}`, false);
        }
    }

    async function handleExecuteTerminalCommand() {
        const commandInput = document.getElementById('terminalCommand');
        const command = commandInput.value.trim();

        if (!command) {
            showNotification('请输入要执行的命令!', 'warning');
            appendToLog('$ 无命令输入.', 'terminal', 'info');
            commandInput.focus();
            return;
        }

        if (!currentTerminalServerId || !serversData[currentTerminalServerId]) {
            showNotification('无法确定当前终端服务器!', 'error');
            console.error('Terminal server ID:', currentTerminalServerId);
            console.error('Available servers:', Object.keys(serversData));
            return;
        }

        const serverDetails = serversData[currentTerminalServerId];
        console.log('Executing command on server:', serverDetails.servername, 'Command:', command);

        // 显示正在执行的命令
        appendToLog(`$ ${command}`, 'terminal', 'command');
        commandInput.value = '';

        // 禁用执行按钮防止重复提交
        const executeBtn = document.getElementById('executeCommandBtn');
        const originalText = executeBtn.textContent;
        executeBtn.disabled = true;
        executeBtn.innerHTML = '<div class="loading" style="display: inline-block; margin-right: 5px;"></div>执行中...';

        const payload = {
            serverId: serverDetails.id,
            host: serverDetails.host,
            port: serverDetails.port,
            username: serverDetails.username,
            password: serverDetails.password,
            command: command,
            timeoutMs: 60000
        };

        try {
            const response = await fetch(`${API_BASE_URL_S34M11B2}/executeCommand`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            console.log('Command execution result:', result);

            if (result.code === 200 && result.data) {
                const output = result.data.output || (result.data.success ? '命令执行成功,无输出.' : '命令执行失败,无输出.');
                appendToLog(output, 'terminal', 'output');

                if (!result.data.success && result.data.errorMessage) {
                    appendToLog(`[ERROR] ${result.data.errorMessage}`, 'terminal', 'error');
                    showNotification(`命令执行失败: ${result.data.errorMessage}`, 'error');
                } else if (result.data.success) {
                    showNotification('命令执行成功!', 'success');
                }
            } else {
                throw new Error(result.msg || '命令执行请求失败');
            }
        } catch (error) {
            console.error('Execute terminal command error:', error);
            appendToLog(`[CLIENT ERROR] 命令执行出错: ${error.message}`, 'terminal', 'error');
            showNotification(`命令执行出错: ${error.message}`, 'error');
        } finally {
            // 恢复执行按钮
            executeBtn.disabled = false;
            executeBtn.textContent = originalText;
            commandInput.focus();
        }
    }

    /**
     * 更新流水线详情UI
     * 从API获取最新的流水线数据
     */
    async function updatePipelineDetailsUI(pipelineId) {
        try {
            // 显示加载状态
            const pipelineDetailsContent = document.getElementById('pipelineDetailsContent');
            const pipelineStepsTableBody = document.getElementById('pipelineStepsTableBody');

            document.getElementById('pipelineDetailName').textContent = "加载中...";
            pipelineStepsTableBody.innerHTML = '<tr><td colspan="3" style="text-align: center;">正在加载数据...</td></tr>';

            // 调用getBillEntity API获取最新流水线数据
            const response = await fetch(`${API_BASE_URL_S34M11B2}/getBillEntity?key=${pipelineId}`);
            const result = await response.json();

            if (result.code !== 200 || !result.data) {
                throw new Error(result.msg || '获取流水线详情失败');
            }

            const pipelineData = result.data;
            document.getElementById('pipelineDetailName').textContent = pipelineData.pipelinename || '未命名流水线';

            // 清空表格
            pipelineStepsTableBody.innerHTML = '';

            // 获取步骤列表
            const steps = pipelineData.item || [];

            if (steps.length === 0) {
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="3" style="text-align: center;">该流水线没有步骤</td>';
                pipelineStepsTableBody.appendChild(row);
                return;
            }

            // 按行号排序步骤
            steps.sort((a, b) => (a.rownum || 0) - (b.rownum || 0));

            // 填充步骤表格
            steps.forEach((step, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${step.rownum || index + 1}</td>
                    <td>${step.stepname || '未命名步骤'}</td>
                    <td><pre style="margin: 0; white-space: pre-wrap;">${step.commandtext || '无命令'}</pre></td>
                `;
                pipelineStepsTableBody.appendChild(row);
            });

        } catch (error) {
            console.error('获取流水线详情失败:', error);
            document.getElementById('pipelineDetailName').textContent = '加载失败';
            document.getElementById('pipelineStepsTableBody').innerHTML =
                `<tr><td colspan="3" style="text-align: center; color: red;">
                    获取流水线详情失败: ${error.message}
                </td></tr>`;
            showNotification(`获取流水线详情失败: ${error.message}`, 'error');
        }
    }

    // 初始化时禁用查看详情按钮，直到选择了流水线
    // 注意：这个事件监听器已经移动到initializeEventListeners函数中

    function updatePipelineStepsUI() {
        const selectedPipelineId = pipelineSelect.value;
        pipelineStepsDiv.innerHTML = '';
        updateProgressBarUI(0);

        if (selectedPipelineId && pipelinesData[selectedPipelineId]) {
            const steps = pipelinesData[selectedPipelineId].steps;
            if (steps && steps.length > 0) {
                steps.forEach((step, index) => {
                    const stepElement = `
                            <div class="step-item" id="step-${index}">
                                <div class="step-number">${index + 1}</div>
                                <div class="step-content">
                                    <div class="step-name">${step.name || '未命名步骤'}</div>
                                    <div class="step-command">${step.command || '无命令'}</div>
                                </div>
                                <div class="status-indicator status-pending" style="margin-left: auto; flex-shrink: 0;">待执行</div>
                            </div>`;
                    pipelineStepsDiv.insertAdjacentHTML('beforeend', stepElement);
                });
            } else {
                pipelineStepsDiv.innerHTML = '<p>此流水线没有定义步骤.</p>';
            }
        } else {
            pipelineStepsDiv.innerHTML = '<p>请先选择一个流水线以查看步骤.</p>';
        }
    }

    function updateStepStatusUI(stepIndex, statusType, message) {
        const stepElement = document.getElementById(`step-${stepIndex}`);
        if (stepElement) {
            const statusIndicator = stepElement.querySelector('.status-indicator');
            statusIndicator.className = 'status-indicator';
            statusIndicator.textContent = message;
            switch(statusType) {
                case 'running': statusIndicator.classList.add('status-running'); break;
                case 'success': statusIndicator.classList.add('status-success'); break;
                case 'failed': statusIndicator.classList.add('status-failed'); break;
                default: statusIndicator.classList.add('status-pending'); break;
            }
        }
    }

    function showNotification(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.animation = 'notificationSlideOut 0.3s ease forwards';
            setTimeout(() => notification.remove(), 300);
        }, duration);
    }

    function openModal(modalId) {
        console.log('Opening modal:', modalId);
        const modalElement = document.getElementById(modalId);
        if(modalElement) {
            modalElement.classList.add('show');
            console.log('Modal opened successfully:', modalId);
        } else {
            console.error('Modal element not found:', modalId);
            showNotification('模态窗口打开失败!', 'error');
        }
    }

    function closeModal(modalId) {
        const modalElement = document.getElementById(modalId);
        if(modalElement) modalElement.classList.remove('show');
    }

    function openTerminalModal(serverId) {
        console.log('Opening terminal for server:', serverId);
        console.log('Server data:', serversData[serverId]);
        console.log('All servers loaded:', serversLoaded);

        // 检查服务器是否已加载
        if (!serversLoaded) {
            showNotification('请先加载服务器列表!', 'warning');
            return;
        }

        currentTerminalServerId = serverId;
        const serverDetails = serversData[serverId];
        if (!serverDetails) {
            console.error('Server details not found for ID:', serverId);
            console.error('Available server IDs:', Object.keys(serversData));
            showNotification('无法找到服务器详情!', 'error');
            return;
        }

        console.log('Opening terminal modal for:', serverDetails.servername);
        terminalModal.querySelector('.modal-title').textContent = `SSH终端 - ${serverDetails.servername || serverId}`;
        clearTerminalOutput();

        // 添加欢迎信息
        appendToLog(`连接到服务器: ${serverDetails.servername} (${serverDetails.host}:${serverDetails.port})`, 'terminal', 'info');
        appendToLog('$ 请输入命令...', 'terminal', 'info');

        openModal('terminalModal');

        // 聚焦到命令输入框
        setTimeout(() => {
            const commandInput = document.getElementById('terminalCommand');
            if (commandInput) {
                commandInput.focus();
            }
        }, 100);
    }

    function clearLog(area = 'execution') {
        const logArea = area === 'terminal' ? terminalOutput : executionLog;
        if (logArea) {
            logArea.innerHTML = area === 'terminal' ? '$ 等待命令输入...' : '# SSH流水线执行日志';
            logArea.summaryContent = null; // 清除总结内容引用
        }
    }

    function clearTerminalOutput() {
        clearLog('terminal');
        appendToLog('$ 终端已清空，请输入命令...', 'terminal', 'info');
    }

    // 调试函数：测试终端功能
    function testTerminalFunction() {
        console.log('Testing terminal function...');
        console.log('Servers loaded:', serversLoaded);
        console.log('Available servers:', Object.keys(serversData));
        console.log('Terminal modal element:', document.getElementById('terminalModal'));

        if (Object.keys(serversData).length > 0) {
            const firstServerId = Object.keys(serversData)[0];
            console.log('Testing with first server:', firstServerId);
            openTerminalModal(firstServerId);
        } else {
            console.log('No servers available for testing');
        }
    }

    // 将测试函数暴露到全局作用域，方便在控制台调用
    window.testTerminalFunction = testTerminalFunction;

    // 调试函数：检查服务器数据状态
    function debugServerData() {
        console.log('=== 服务器数据调试信息 ===');
        console.log('serversLoaded:', serversLoaded);
        console.log('serversData:', serversData);
        console.log('serverSelect.value:', serverSelect.value);
        console.log('serverSelect.options:', Array.from(serverSelect.options).map(opt => ({
            value: opt.value,
            text: opt.textContent
        })));
        console.log('========================');
    }

    // 显示格式化的日志
    function displayFormattedLogs(logs) {
        if (!logs || logs.length === 0) return;

        // 清空现有的执行日志
        executionLog.innerHTML = '';
        executionLog.style.display = 'block';

        // 显示每条日志
        logs.forEach(logEntry => {
            const logLine = document.createElement('div');
            logLine.className = 'log-entry';

            // 检查是否是特殊格式的日志（包含Unicode字符）
            if (logEntry.includes('╔') || logEntry.includes('║') || logEntry.includes('╚') ||
                logEntry.includes('┌') || logEntry.includes('│') || logEntry.includes('└')) {
                logLine.className += ' formatted-log';
                logLine.style.fontFamily = 'monospace';
                logLine.style.whiteSpace = 'pre';
            }

            // 添加颜色样式
            if (logEntry.includes('🚀') || logEntry.includes('✅') || logEntry.includes('⚡')) {
                logLine.style.color = '#28a745'; // 绿色
            } else if (logEntry.includes('❌') || logEntry.includes('🚫') || logEntry.includes('🛑')) {
                logLine.style.color = '#dc3545'; // 红色
            } else if (logEntry.includes('⚠️') || logEntry.includes('🔄') || logEntry.includes('⏰')) {
                logLine.style.color = '#ffc107'; // 黄色
            } else if (logEntry.includes('📋') || logEntry.includes('📡') || logEntry.includes('📤') || logEntry.includes('🏁')) {
                logLine.style.color = '#17a2b8'; // 蓝色
            } else if (logEntry.includes('│ 📤') || logEntry.includes('│ ❌')) {
                logLine.style.color = '#6c757d'; // 灰色（实时输出）
                logLine.style.fontSize = '0.85rem';
            }

            logLine.textContent = logEntry;
            executionLog.appendChild(logLine);
        });

        // 滚动到底部
        executionLog.scrollTop = executionLog.scrollHeight;

        // 如果全屏模式开启，同步更新全屏日志
        const fullscreenOverlay = document.getElementById('fullscreenOverlay');
        const fullscreenLog = document.getElementById('fullscreenLog');
        if (fullscreenOverlay && fullscreenOverlay.style.display === 'block') {
            fullscreenLog.innerHTML = executionLog.innerHTML;
            fullscreenLog.scrollTop = fullscreenLog.scrollHeight;
        }
    }

    // 显示传统步骤结果格式
    function displayStepResults(stepResults) {
        if (!stepResults || stepResults.length === 0) return;

        // 清空现有的执行日志
        executionLog.innerHTML = '';
        executionLog.style.display = 'block';

        // 显示每个步骤结果
        stepResults.forEach((step, index) => {
            if (step.isSystemLog) return; // 跳过系统日志

            const stepDiv = document.createElement('div');
            stepDiv.className = 'step-result';
            stepDiv.style.marginBottom = '15px';
            stepDiv.style.padding = '10px';
            stepDiv.style.border = '1px solid #333';
            stepDiv.style.borderRadius = '5px';

            const statusIcon = step.status === 'Success' ? '✅' : '❌';
            const statusColor = step.status === 'Success' ? '#28a745' : '#dc3545';

            stepDiv.innerHTML = `
                <div style="color: ${statusColor}; font-weight: bold; margin-bottom: 5px;">
                    ${statusIcon} 步骤 ${index + 1}: ${step.stepName || '未知步骤'}
                </div>
                <div style="color: #ccc; font-size: 0.9rem;">命令: ${step.commandText}</div>
                ${step.output ? `<div style="color: #fff; margin: 5px 0;">输出: ${step.output}</div>` : ''}
                ${step.error ? `<div style="color: #ff6b6b; margin: 5px 0;">错误: ${step.error}</div>` : ''}
                <div style="color: #999; font-size: 0.8rem;">
                    状态: ${step.status} | 耗时: ${step.durationMs}ms
                    ${step.retryCount > 0 ? ` | 重试: ${step.retryCount}次` : ''}
                </div>
            `;

            executionLog.appendChild(stepDiv);
        });

        // 滚动到底部
        executionLog.scrollTop = executionLog.scrollHeight;
    }

    // 生成美化的执行总结
    function generateBeautifulSummary(logArea) {
        const stats = logArea.summaryStats;
        const content = logArea.summaryContent;

        // 清空现有内容
        content.innerHTML = '';

        // 创建统计卡片容器
        const statsContainer = document.createElement('div');
        statsContainer.className = 'summary-stats';

        // 总步骤数卡片
        const totalCard = document.createElement('div');
        totalCard.className = 'stat-card stat-total';
        totalCard.innerHTML = `
            <span class="stat-number">${stats.total || 0}</span>
            <span class="stat-label">总步骤数</span>
        `;
        statsContainer.appendChild(totalCard);

        // 成功步骤卡片
        const successCard = document.createElement('div');
        successCard.className = 'stat-card stat-success';
        successCard.innerHTML = `
            <span class="stat-number">${stats.success || 0}</span>
            <span class="stat-label">成功步骤</span>
        `;
        statsContainer.appendChild(successCard);

        // 失败步骤卡片
        const failedCard = document.createElement('div');
        failedCard.className = 'stat-card stat-failed';
        failedCard.innerHTML = `
            <span class="stat-number">${stats.failed || 0}</span>
            <span class="stat-label">失败步骤</span>
        `;
        statsContainer.appendChild(failedCard);

        // 耗时卡片
        if (stats.duration !== undefined) {
            const timeCard = document.createElement('div');
            timeCard.className = 'stat-card stat-time';
            const timeText = stats.duration > 1000 ?
                `${(stats.duration / 1000).toFixed(1)}s` :
                `${stats.duration}ms`;
            timeCard.innerHTML = `
                <span class="stat-number">${timeText}</span>
                <span class="stat-label">总耗时</span>
            `;
            statsContainer.appendChild(timeCard);
        }

        content.appendChild(statsContainer);

        // 创建状态徽章
        const statusContainer = document.createElement('div');
        statusContainer.className = 'summary-status';

        const statusBadge = document.createElement('div');
        statusBadge.className = 'status-badge';

        if (stats.failed === 0) {
            statusBadge.className += ' status-success';
            statusBadge.innerHTML = '✅ 执行成功';
        } else if (stats.success > 0) {
            statusBadge.className += ' status-partial';
            statusBadge.innerHTML = '⚠️ 部分成功';
        } else {
            statusBadge.className += ' status-failed';
            statusBadge.innerHTML = '❌ 执行失败';
        }

        statusContainer.appendChild(statusBadge);
        content.appendChild(statusContainer);

        // 如果有失败原因，显示失败信息
        if (stats.failureReason) {
            const failureDiv = document.createElement('div');
            failureDiv.className = 'summary-failed-step';
            failureDiv.textContent = stats.failureReason;
            content.appendChild(failureDiv);
        }

        // 滚动到底部
        logArea.scrollTop = logArea.scrollHeight;
    }

    // 暴露调试函数
    window.debugServerData = debugServerData;

    // 全局错误处理器
    window.addEventListener('error', function(e) {
        console.error('Global JavaScript Error:', e.error);
        console.error('Error details:', {
            message: e.message,
            filename: e.filename,
            lineno: e.lineno,
            colno: e.colno,
            stack: e.error?.stack
        });
        showNotification('页面发生JavaScript错误，请检查控制台', 'error');
    });

    // 未处理的Promise拒绝处理器
    window.addEventListener('unhandledrejection', function(e) {
        console.error('Unhandled Promise Rejection:', e.reason);
        showNotification('发生异步操作错误，请检查控制台', 'error');
    });

    async function pollExecutionStatus(sessionId, pipelineId) {
        if (executionStatusInterval) clearInterval(executionStatusInterval);

        executionStatusInterval = setInterval(async () => {
            if (!currentSessionId) {
                clearInterval(executionStatusInterval);
                executionStatusInterval = null;
                return;
            }
            try {
                const response = await fetch(`${API_BASE_URL_S34M11B2}/getExecutionStatus?sessionId=${encodeURIComponent(sessionId)}`);
                const result = await response.json();

                if (result.code === 200 && result.data) {
                    const statusData = result.data;
                    if(statusData.outputSinceLastPoll && statusData.outputSinceLastPoll.trim() !== "") {
                        appendToLog(`[POLL STATUS ${sessionId}] Output:\n${statusData.outputSinceLastPoll}`);
                    }

                    updateProgressBarUI(statusData.progress || 0);

                    const pipelineDefinitionSteps = pipelinesData[pipelineId]?.steps;
                    if(pipelineDefinitionSteps){
                        pipelineDefinitionSteps.forEach((step, index) => {
                            if (index < statusData.currentStepIndex) {
                                updateStepStatusUI(index, 'success', '完成');
                            } else if (index === statusData.currentStepIndex && (statusData.status === 'RUNNING' || statusData.status === 'PENDING')) {
                                updateStepStatusUI(index, 'running', '执行中');
                            } else if (statusData.status === 'COMPLETED' || statusData.status === 'FAILED' || statusData.status === 'CANCELLED') {
                                if (statusData.success && index <= statusData.currentStepIndex) {
                                    updateStepStatusUI(index, 'success', '完成');
                                } else if (!statusData.success && index === statusData.currentStepIndex) {
                                    updateStepStatusUI(index, 'failed', '失败');
                                } else if (!statusData.success && index < statusData.currentStepIndex) {
                                    updateStepStatusUI(index, 'success', '完成');
                                } else {
                                    updateStepStatusUI(index, 'pending', '待执行');
                                }
                            } else {
                                updateStepStatusUI(index, 'pending', '待执行');
                            }
                        });
                    }

                    if (statusData.status === 'COMPLETED' || statusData.status === 'CANCELLED' || statusData.status === 'FAILED') {
                        // 停止轮询
                        clearInterval(executionStatusInterval);
                        executionStatusInterval = null;
                        currentSessionId = null;
                        const finalMessage = statusData.success ? `任务 ${sessionId} 执行成功!` : `任务 ${sessionId} ${statusData.status.toLowerCase()}: ${statusData.errorMessage || '完成但有错误'}`;
                        updateExecutionStatusUI(statusData.success ? 'success' : 'failed', finalMessage, false);
                        showNotification(finalMessage, statusData.success ? 'success' : 'error');
                        updateProgressBarUI(statusData.success ? 100 : (statusData.progress || 0));
                        if(statusData.output && (!statusData.outputSinceLastPoll || statusData.outputSinceLastPoll.length < statusData.output.length)) {
                            appendToLog(`[POLL STATUS ${sessionId}] Final Output (if different or full):\n${statusData.output}`);
                        }
                    } else {
                        updateExecutionStatusUI('running', `任务执行中 (ID: ${sessionId}, ${statusData.progress || 0}%)`, true);
                    }
                } else {
                    console.warn(`Polling for ${sessionId}: ${result.msg || '无法获取状态'}. Might be completed or invalid.`);
                }
            } catch (error) {
                console.error(`Error polling status for ${sessionId}:`, error);
            }
        }, 3000);
    }

    // 全屏模式切换函数
    function toggleFullscreen() {
        const overlay = document.getElementById('fullscreenOverlay');
        const fullscreenLog = document.getElementById('fullscreenLog');
        const executionLog = document.getElementById('executionLog');

        if (overlay.style.display === 'none' || !overlay.style.display) {
            // 进入全屏模式
            overlay.style.display = 'block';
            // 复制当前日志内容到全屏显示
            fullscreenLog.innerHTML = executionLog.innerHTML;
            // 滚动到底部
            fullscreenLog.scrollTop = fullscreenLog.scrollHeight;
            document.body.style.overflow = 'hidden'; // 禁止背景滚动
        } else {
            // 退出全屏模式
            overlay.style.display = 'none';
            document.body.style.overflow = 'auto'; // 恢复背景滚动
        }
    }

    // ESC键退出全屏
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const overlay = document.getElementById('fullscreenOverlay');
            if (overlay.style.display === 'block') {
                toggleFullscreen();
            }
        }
    });
</script>

<!-- 全屏执行状态覆盖层 -->
<div id="fullscreenOverlay" class="fullscreen-overlay">
    <div class="fullscreen-content">
        <div class="fullscreen-header">
            <h2 style="margin: 0; color: #333;">
                <svg class="icon" viewBox="0 0 24 24" style="width: 24px; height: 24px; margin-right: 10px;">
                    <path d="M13,9A1,1 0 0,0 12,8A1,1 0 0,0 11,9V11A1,1 0 0,0 12,12A1,1 0 0,0 13,11M13,17H11V15H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2"/>
                </svg>
                SSH流水线执行状态 - 全屏模式
            </h2>
            <button class="btn btn-secondary" onclick="toggleFullscreen()">
                ❌ 退出全屏
            </button>
        </div>
        <div id="fullscreenLog" class="fullscreen-log"></div>
    </div>
</div>

</body>
</html>