<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <title>企业微信审批模板转换工具</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <link href="https://fonts.googleapis.cnpmjs.org/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap"
          rel="stylesheet" />
    <style>
        :root {
            --primary: #2a6df4;
            --primary-dark: #1d5acd;
            --secondary: #6c41dc;
            --light: #f8f9ff;
            --dark: #1a2a4e;
            --success: #06c592;
            --warning: #ffb946;
            --danger: #ff5c7c;
            --gray: #5c667b;
            --light-gray: #e6ebf5;
            --border-radius: 18px;
            --box-shadow: 0 10px 40px rgba(42,109,244,0.11), 0 2px 8px rgba(42,109,244,0.09);
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "PingFang SC",
            "Microsoft YaHei", sans-serif;
            background: linear-gradient(135deg, #f2f7ff 0%, #e8f0ff 100%);
            color: var(--dark);
            line-height: 1.7;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1800px;
            margin: 0 auto;
        }

        header {
            text-align: center;
            margin-bottom: 10px;
            padding: 8px;
        }

        h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 3px;
            letter-spacing: -0.5px;
            position: relative;
            display: inline-block;
            transition: color 0.3s, text-shadow 0.3s;
        }

        h1::after {
            content: '';
            position: absolute;
            bottom: -6px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: var(--primary);
            border-radius: 2px;
        }

        h1:hover {
            color: #2357d5;
            text-shadow: 0 2px 8px #c0d2ff;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            margin-bottom: 20px;
        }

        .card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            overflow: hidden;
            transition: var(--transition);
            height: 100%;
            display: flex;
            flex-direction: column;
            border: 1px solid rgba(232, 240, 255, 0.8);
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 14px 40px rgba(42,109,244,0.13);
        }

        .card-header {
            background: linear-gradient(45deg, var(--primary), var(--primary-dark));
            color: white;
            padding: 16px 20px;
            display: flex;
            align-items: center;
            gap: 12px;
            transition: color 0.3s, text-shadow 0.3s;
        }

        .output-container {
            position: relative;
            height: 100%;
            display: flex;
            flex-direction: column;
            margin-bottom: 40px; /* 为下方按钮留出空间 */
        }

        .floating-copy-btn {
            position: absolute;
            bottom: -25px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--primary);
            color: white;
            border: none;
            padding: 12px 24px;
            font-size: 14px;
            font-weight: 500;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.25s;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 12px rgba(42, 109, 244, 0.3);
            z-index: 10;
            opacity: 0.95;
        }

        .floating-copy-btn:hover {
            background: var(--primary-dark);
            transform: translateX(-50%) translateY(-3px);
            box-shadow: 0 6px 16px rgba(42, 109, 244, 0.4);
            opacity: 1;
        }

        .floating-copy-btn:active {
            transform: translateX(-50%) translateY(-1px);
        }

        .floating-copy-btn.copied {
            background: var(--success);
            box-shadow: 0 4px 12px rgba(6, 197, 146, 0.3);
        }

        .card-header h3 {
            font-size: 1.4rem;
            font-weight: 600;
            margin: 0;
        }

        .card-body {
            padding: 20px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }

        textarea {
            width: 100%;
            flex-grow: 1;
            padding: 20px;
            border: 1.5px solid #e0e7ff;
            border-radius: 10px;
            font-family: 'Fira Code', 'Consolas', monospace;
            font-size: 15.5px;
            line-height: 1.6;
            resize: none;
            transition: border 0.25s, box-shadow 0.25s;
            background-color: #fcfdff;
            box-shadow: inset 0 2px 5px rgba(0,0,0,0.04);
            min-height: 600px;
        }

        textarea:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 4px rgba(42,109,244,0.12);
        }

        .mappings-container {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 20px;
            overflow: hidden;
            border: 1px solid rgba(232, 240, 255, 0.8);
        }

        .mappings-header {
            background: linear-gradient(45deg, var(--secondary), #5a28c3, #2a6df4 80%);
            color: white;
            padding: 14px 20px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .mappings-header h3 {
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0;
        }

        .mappings-body {
            padding: 15px 20px;
        }

        .mapping-fields {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 14px;
        }

        .mapping-item {
            background: var(--light);
            border-radius: 8px;
            padding: 12px;
            display: flex;
            align-items: center;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            border: 1px solid #eef2f8;
        }

        .mapping-item:hover {
            background: #eff6ff;
        }

        .mapping-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 4px;
            background: var(--primary);
        }

        .mapping-item label {
            font-weight: 500;
            color: var(--dark);
            min-width: 160px;
            padding-right: 12px;
            font-size: 14px;
            line-height: 1.4;
        }

        .mapping-item input, .mapping-item select {
            flex-grow: 1;
            padding: 10px 14px;
            border: 1.5px solid #e0e7ff;
            border-radius: 10px;
            font-family: 'Fira Code', 'Consolas', monospace;
            font-size: 15.5px;
            transition: border 0.25s, box-shadow 0.25s;
            background: white;
        }

        .mapping-item input:focus, .mapping-item select:focus {
            outline: none;
            border-color: #2a6df4;
            box-shadow: 0 0 0 4px rgba(42,109,244,0.13);
        }

        .table-label {
            grid-column: 1 / -1; /* Span full width */
            font-weight: 600;
            color: #fff;
            background: linear-gradient(45deg, #8456dc, #6c41dc, #2a6df4 85%);
            border-radius: 10px;
            margin: 16px 0 6px 0;
            font-size: 15px;
            padding: 8px 18px;
            box-shadow: 0 2px 8px rgba(42,109,244,0.11);
        }

        .btn-container {
            text-align: center;
            margin-top: 15px;
            margin-bottom: 10px;
        }

        .btn-convert, button {
            background: linear-gradient(90deg, #4f8cfb 0%, #2357d5 100%);
            color: white;
            border: none;
            padding: 16px 60px;
            font-size: 18px;
            font-weight: 600;
            border-radius: 100px;
            cursor: pointer;
            transition: background 0.25s, transform 0.15s;
            box-shadow: 0 6px 16px rgba(42,109,244,0.12);
            letter-spacing: 1px;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        button {
            font-size: 14px;
            padding: 8px 16px;
            border-radius: 8px;
            font-weight: 500;
        }

        .btn-convert:hover, button:hover {
            background: linear-gradient(90deg, #2a6df4 0%, #3d59fb 100%);
            transform: scale(1.03);
        }

        .btn-remove {
            background: var(--danger);
            min-width: 30px;
            padding: 5px 10px;
        }
        .btn-remove:hover {
            background: #e44260;
        }


        .icon {
            font-size: 1.1em;
        }

        .note {
            background: rgba(255, 229, 143, 0.25);
            border-left: 4px solid var(--warning);
            padding: 14px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
            font-size: 14px;
        }

        .note-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            transition: all 0.25s;
            padding: 5px;
            border-radius: 5px;
            margin: -5px -5px 10px -5px;
        }

        .note-header:hover {
            background: rgba(255, 229, 143, 0.4);
        }

        .note-header h4 {
            margin: 0;
        }

        .manual-refresh-hint {
            font-size: 12px;
            color: var(--primary);
            opacity: 0.7;
            transition: opacity 0.25s;
        }

        .note-header:hover .manual-refresh-hint {
            opacity: 1;
        }

        @media (max-width: 1200px) {
            .main-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>

<div class="container">
    <header>
        <h1><i class="fas fa-exchange-alt icon"></i> 企业微信审批模板转换工具</h1>
    </header>

    <div class="main-grid">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-paste icon"></i>
                <h3>1. 粘贴企业微信审批模板JSON</h3>
            </div>
            <div class="card-body">
                <textarea id="inputJson" placeholder='请在此处粘贴企业微信审批模板的JSON数据 {"code":200, ...}' oninput="debouncedConversion()" onpaste="setTimeout(debouncedConversion, 100)"></textarea>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <i class="fas fa-file-code icon"></i>
                <h3>3. 转换后的OMS审批模版输出</h3>
            </div>
            <div class="card-body">
                <div class="output-container">
                    <textarea id="outputJson" readonly placeholder="转换后的JSON将显示在此区域..."></textarea>
                    <button class="floating-copy-btn" onclick="copyToClipboard()" title="复制到剪贴板">
                        <i class="fas fa-copy"></i> 复制
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="mappings-container">
        <div class="mappings-header">
            <i class="fas fa-sliders-h icon"></i>
            <h3>2. 自定义字段及流程映射</h3>
        </div>
        <div class="mappings-body">
            <div id="mapping-fields-container">
                <h4 style="color: var(--primary); margin-bottom: 10px;"><i class="fas fa-pen-ruler icon"></i> 字段映射</h4>
                <div class="mapping-fields" id="mapping-fields">
                    <p>粘贴JSON数据后点击"转换"按钮，系统将自动生成字段映射关系</p>
                </div>
            </div>

            <div id="process-config" style="margin-top: 20px;">
                <h4 style="color: var(--primary);"><i class="fas fa-sitemap icon"></i> 审批流程配置 (按接口指定)</h4>

                <div id="approval-nodes-wrapper" style="margin-top: 10px;">
                    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 10px; background: #f7faff; padding: 10px; border-radius: 8px;">
                        <strong style="color: var(--dark);">审批节点 (将按添加顺序依次审批)</strong>
                        <button type="button" onclick="addApprovalNode()">➕ 添加审批节点</button>
                    </div>
                    <div id="approval-nodes-container">
                    </div>
                </div>

                <div id="notifier-wrapper" style="margin-top: 20px;">
                    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 10px; background: #f7faff; padding: 10px; border-radius: 8px;">
                        <strong style="color: var(--dark);">抄送人 (在流程结束后收到通知)</strong>
                    </div>
                    <div class="mapping-item">
                        <label for="notifier-userids">抄送人 UserIDs (逗号分隔)</label>
                        <input type="text" id="notifier-userids" placeholder="user1,user2,user3" oninput="debouncedConversion()">
                    </div>
                </div>
            </div>
        </div>
    </div>



    <div class="note">
        <div class="note-header" onclick="manualConversion()" title="点击手动刷新转换">
            <h4><i class="fas fa-lightbulb icon"></i> 使用说明</h4>
            <span class="manual-refresh-hint"><i class="fas fa-sync-alt"></i> 手动刷新</span>
        </div>
        <p>1. 在左上角框中粘贴从企业微信获取的审批模板完整JSON数据。</p>
        <p>2. 系统会自动实时转换并生成各模板字段的映射关系，您可以按需修改。</p>
        <p>3. 在 "审批流程配置" 中添加审批节点和抄送人，修改后会自动更新转换结果。</p>
        <p>4. 右侧框中会实时生成最终可用的OMS审批模板，复制并使用。</p>
        <p><strong>💡 提示：如果实时转换出现问题，可以点击上方标题进行手动刷新。</strong></p>
    </div>
</div>

<script>
    let approvalNodeCount = 0;
    let conversionTimeout = null;

    // 防抖函数，避免频繁转换
    function debouncedConversion() {
        clearTimeout(conversionTimeout);
        conversionTimeout = setTimeout(() => {
            runConversion(true); // 静默转换
        }, 800); // 800ms 延迟
    }

    function toCamelCase(str) {
        return str.toLowerCase().replace(/[^a-zA-Z0-9\s]+(.)?/g, '').replace(/\s(.)/g, function($1) {
            return $1.toUpperCase();
        }).replace(/\s/g, '');
    }

    function addApprovalNode() {
        approvalNodeCount++;
        const container = document.getElementById('approval-nodes-container');
        const div = document.createElement('div');
        div.className = 'mapping-item approval-node-item';
        div.id = `approval-node-${approvalNodeCount}`;
        div.style.cssText = 'display: flex; flex-direction: row; gap: 15px; margin-top: 10px; align-items: center;';

        div.innerHTML = `
            <strong style="min-width: 60px;">节点 ${approvalNodeCount}:</strong>
            <select class="node-apv-rel" style="min-width: 150px; flex-grow: 0.5;">
                <option value="1">会签 (需所有人同意)</option>
                <option value="2">或签 (任意一人同意即可)</option>
            </select>
            <input type="text" class="node-userids" placeholder="审批人企业微信userid, 逗号分隔" style="flex-grow:1;">
            <button class="btn-remove" onclick="removeApprovalNode(this)">✖</button>
        `;
        container.appendChild(div);

        // 为新添加的节点添加实时转换监听
        const select = div.querySelector('.node-apv-rel');
        const input = div.querySelector('.node-userids');
        select.addEventListener('change', debouncedConversion);
        input.addEventListener('input', debouncedConversion);
    }

    function removeApprovalNode(button) {
        button.parentElement.remove();
        debouncedConversion(); // 删除节点后重新转换
    }

    // 手动转换功能（隐藏在使用说明按钮中）
    function manualConversion() {
        // 清除防抖定时器
        clearTimeout(conversionTimeout);

        // 立即执行转换，显示弹窗提示
        runConversion(false);

        // 给用户反馈
        const refreshHint = document.querySelector('.manual-refresh-hint');
        const originalText = refreshHint.innerHTML;
        refreshHint.innerHTML = '<i class="fas fa-check"></i> 已刷新';
        refreshHint.style.color = 'var(--success)';

        setTimeout(() => {
            refreshHint.innerHTML = originalText;
            refreshHint.style.color = 'var(--primary)';
        }, 2000);
    }

    // 复制到剪贴板功能
    function copyToClipboard() {
        const outputTextarea = document.getElementById('outputJson');
        const copyBtn = document.querySelector('.floating-copy-btn');

        if (!outputTextarea.value.trim()) {
            alert('没有可复制的内容');
            return;
        }

        // 选择并复制文本
        outputTextarea.select();
        outputTextarea.setSelectionRange(0, 99999); // 兼容移动设备

        try {
            document.execCommand('copy');

            // 更新按钮状态
            const originalHTML = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check"></i> 已复制';
            copyBtn.classList.add('copied');

            // 2秒后恢复原状
            setTimeout(() => {
                copyBtn.innerHTML = originalHTML;
                copyBtn.classList.remove('copied');
            }, 2000);

        } catch (err) {
            // 如果execCommand失败，尝试使用现代API
            if (navigator.clipboard) {
                navigator.clipboard.writeText(outputTextarea.value).then(() => {
                    const originalHTML = copyBtn.innerHTML;
                    copyBtn.innerHTML = '<i class="fas fa-check"></i> 已复制';
                    copyBtn.classList.add('copied');

                    setTimeout(() => {
                        copyBtn.innerHTML = originalHTML;
                        copyBtn.classList.remove('copied');
                    }, 2000);
                }).catch(() => {
                    alert('复制失败，请手动选择并复制');
                });
            } else {
                alert('复制失败，请手动选择并复制');
            }
        }
    }

    function getProcessConfig() {
        const nodeList = [];

        const approvalNodes = document.querySelectorAll('.approval-node-item');
        approvalNodes.forEach(node => {
            const apv_rel = parseInt(node.querySelector('.node-apv-rel').value);
            const userids = node.querySelector('.node-userids').value.split(',').map(id => id.trim()).filter(Boolean);
            if (userids.length > 0) {
                nodeList.push({
                    type: 1,
                    apv_rel: apv_rel,
                    userid: userids
                });
            }
        });

        const notifierUserids = document.getElementById('notifier-userids').value.split(',').map(id => id.trim()).filter(Boolean);
        if (notifierUserids.length > 0) {
            nodeList.push({
                type: 2,
                userid: notifierUserids
            });
        }

        return nodeList;
    }

    function runConversion(silent = false) {
        const inputJsonStr = document.getElementById('inputJson').value.trim();
        const outputTextarea = document.getElementById('outputJson');

        if (!inputJsonStr) {
            if (!silent) {
                alert("请先粘贴企业微信审批模板的JSON数据");
            } else {
                outputTextarea.placeholder = "请在左侧输入JSON数据，系统将自动转换...";
                outputTextarea.value = "";
            }
            return;
        }

        try {
            const data = JSON.parse(inputJsonStr).data;
            const controls = data.template_content.controls;
            const mappingFields = document.getElementById('mapping-fields');

            if (!mappingFields.querySelector('input')) {
                mappingFields.innerHTML = '';
                controls.forEach(control => {
                    const title = control.property.title[0].text;
                    const id = control.property.id;

                    if (control.property.control === 'Table') {
                        const tableLabel = document.createElement('div');
                        tableLabel.className = 'table-label';
                        tableLabel.innerHTML = `<i class="fas fa-table"></i> 表格字段: ${title}`;
                        mappingFields.appendChild(tableLabel);

                        control.config.table.children.forEach(child => {
                            const childTitle = child.property.title[0].text;
                            const childId = child.property.id;
                            createMappingInput(`↳ ${childTitle}`, childId, toCamelCase(childTitle));
                        });
                    } else {
                        createMappingInput(title, id, toCamelCase(title));
                    }
                });

                // 为新生成的映射字段添加实时转换监听
                addMappingListeners();
            }

            convertJson(data);
            outputTextarea.placeholder = "转换完成！";
        } catch (e) {
            if (!silent) {
                alert("JSON格式无效，请检查输入数据是否正确");
            } else {
                outputTextarea.value = "";
                outputTextarea.placeholder = "JSON格式无效，请检查输入数据...";
            }
            console.error(e);
        }
    }

    function createMappingInput(title, id, defaultValue) {
        const mappingFields = document.getElementById('mapping-fields');
        const item = document.createElement('div');
        item.className = 'mapping-item';
        item.innerHTML = `
            <label for="map-${id}">${title}</label>
            <input type="text" id="map-${id}" value="${defaultValue}" placeholder="输入字段映射名">
        `;
        mappingFields.appendChild(item);
    }

    // 为映射字段添加实时转换监听
    function addMappingListeners() {
        const mappingInputs = document.querySelectorAll('#mapping-fields input');
        mappingInputs.forEach(input => {
            input.addEventListener('input', debouncedConversion);
        });

        // 为审批节点配置也添加监听
        const approvalInputs = document.querySelectorAll('#approval-nodes-container input, #approval-nodes-container select, #notifier-userids');
        approvalInputs.forEach(input => {
            input.addEventListener('input', debouncedConversion);
            input.addEventListener('change', debouncedConversion);
        });
    }

    function convertJson(data) {
        const controls = data.template_content.controls;
        let contentsStrings = [];

        controls.forEach(control => {
            const { id, control: controlType } = control.property;
            const mappingValue = document.getElementById(`map-${id}`)?.value || toCamelCase(control.property.title[0].text);

            if (controlType === 'Table') {
                let tableItems = [];
                control.config.table.children.forEach(child => {
                    const childMappingValue = document.getElementById(`map-${child.property.id}`)?.value || toCamelCase(child.property.title[0].text);
                    const childItem = {
                        control: child.property.control,
                        id: child.property.id,
                        value: { text: `\$!{object.${childMappingValue}}` }
                    };
                    tableItems.push(JSON.stringify(childItem, null, 14).trim());
                });

                let tableString = `
{
    "control": "Table",
    "id": "${id}",
    "value": {
        "children": [
            #foreach($object in $approvePojo.object.item)
            {
                "list": [
                    ${tableItems.join(',\n                    ')}
                ]
            }
            #if($foreach.hasNext),#end
            #end
        ]
    }
}`;
                contentsStrings.push(tableString.trim());
            } else {
                const textItem = {
                    control: controlType,
                    id: id,
                    value: { text: `\$!{approvePojo.object.${mappingValue}}` }
                };
                contentsStrings.push(JSON.stringify(textItem, null, 6).trim());
            }
        });

        const processNodes = getProcessConfig();
        const nodeStrings = processNodes.map(node => {
            const useridsString = node.userid.map(id => `"${id.trim()}"`).join(',');
            if (node.type === 1) { // Approver
                return `        {
            "type": 1,
            "apv_rel": ${node.apv_rel},
            "userid": [${useridsString}]
        }`;
            } else { // Notifier (type 2)
                return `        {
            "type": 2,
            "userid": [${useridsString}]
        }`;
            }
        }).join(',\n');

        const finalJsonString = `{
    "creator_userid": "\$!{approvePojo.creatoruserid}",
    "template_id": "\$!{approvePojo.modelcode}",
    "use_template_approver": 0,
    "process": {
        "node_list": [
${nodeStrings}
        ]
    },
    "apply_data": {
        "contents": [
            ${contentsStrings.join(',\n            ')}
        ]
    }
}`;

        document.getElementById('outputJson').value = finalJsonString;
    }
</script>

</body>
</html>