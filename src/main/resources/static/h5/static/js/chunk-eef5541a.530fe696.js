(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-eef5541a"],{"1c2d":function(t,e,a){"use strict";a("983c")},"1ed0":function(t,e,a){},4499:function(t,e,a){},4680:function(t,e,a){t.exports=a.p+"static/img/qrcode.fe966133.png"},"568a":function(t,e,a){t.exports=a.p+"static/img/备案图标.d0289dc0.png"},7579:function(t,e){window._iconfont_svg_string_='<svg><symbol id="icon-gengduoPDF" viewBox="0 0 1024 1024"><path d="M832 226.928V864H192V128h552.048z" fill="#FFFFFF" ></path><path d="M735.256 128L832 226.928V800H288V128h447.256z m-13.496 32H320v608h480V239.968L721.76 160z" fill="#4D5697" ></path><path d="M607.256 192L704 290.928V864H160V192h447.256z m-13.496 32H192v608h480V303.968L593.76 224z" fill="#4D5697" ></path><path d="M192 224h416l64 64v544H192z" fill="#FFFFFF" ></path><path d="M704 320H576V192z" fill="#4D5697" ></path><path d="M288 320h192v160H352v128h-64V320z m64 128h64V352h-64v96zM288 736h64v-64h-64zM384 736h64v-64h-64zM480 736h64v-64h-64z" fill="#FF8C84" ></path></symbol><symbol id="icon-pdf" viewBox="0 0 1024 1024"><path d="M581.235745 0a42.641614 42.641614 0 0 1 28.081063 10.530398l314.579404 275.35042a42.674115 42.674115 0 0 1 14.5443 32.127466v577.92062A127.973593 127.973593 0 0 1 817.990954 1023.788742l-7.491533 0.211258h-597.047594a127.957342 127.957342 0 0 1-127.729833-120.482059l-0.211258-7.507784V127.989843A127.973593 127.973593 0 0 1 205.944043 0.211258L213.451827 0h367.783918z m271.840287 826.782537H170.793963v69.146367a42.657864 42.657864 0 0 0 37.652675 42.365353l4.988939 0.292511h597.063844a42.657864 42.657864 0 0 0 42.349102-37.668926l0.292511-5.005189v-69.130116zM511.975624 85.331979h-298.523797a42.657864 42.657864 0 0 0-42.349102 37.668926l-0.292511 5.005189v694.388777h682.265818V383.96953H639.916716a127.957342 127.957342 0 0 1-127.09606-113.071779l-0.633774-7.41028-0.211258-7.507784z m85.299478 18.476949v152.187009a42.657864 42.657864 0 0 0 37.652675 42.365354l4.988939 0.292511H807.168045L597.258851 103.792678z" fill="#2A3648" ></path><path d="M938.603018 468.748988a85.413232 85.413232 0 0 1 85.33198 85.494486v213.744338a85.413232 85.413232 0 0 1-85.33198 85.494485H85.331979A85.413232 85.413232 0 0 1 0 767.987812V554.243474a85.413232 85.413232 0 0 1 85.331979-85.494486z m-423.734784 67.277546h-100.266295v274.635393h100.266295c44.494184 0 78.002952-12.317966 100.981321-36.937648a142.046625 142.046625 0 0 0 33.021249-100.41255 140.58407 140.58407 0 0 0-32.972497-100.347547c-23.043372-24.619682-56.487138-36.937648-100.981322-36.937648z m338.402805 0h-162.213638v274.635393h44.93295v-121.505848h117.280688V650.674623h-117.231936v-76.166633h117.231936v-38.481456z m-576.474315 0H170.631457v274.635393h37.376414v-105.352737h67.960072c66.806278 0 100.217542-28.471077 100.217543-85.023217 0-56.178376-33.443766-84.259439-99.486265-84.259439z m229.572438 38.481456c34.223795 0 59.135988 7.702791 74.866583 23.465888 15.356831 15.389332 23.043372 40.789044 23.043372 75.402854a105.628997 105.628997 0 0 1-23.043372 75.029089 102.378874 102.378874 0 0 1-74.866583 23.855903h-46.834272V574.50799z m-233.033818 0a69.877644 69.877644 0 0 1 43.34039 10.774158 41.016552 41.016552 0 0 1 14.203037 35.003825 42.852872 42.852872 0 0 1-13.829273 35.751353 73.127767 73.127767 0 0 1-43.779157 10.774157H208.007871v-92.303493h65.278721z" fill="#FF6051" ></path></symbol></svg>',function(t){var e=(e=document.getElementsByTagName("script"))[e.length-1],a=e.getAttribute("data-injectcss");e=e.getAttribute("data-disable-injectsvg");if(!e){var s,i,o,n,l,r=function(t,e){e.parentNode.insertBefore(t,e)};if(a&&!t.__iconfont__svg__cssinject__){t.__iconfont__svg__cssinject__=!0;try{document.write("<style>.svgfont {display: inline-block;width: 1em;height: 1em;fill: currentColor;vertical-align: -0.1em;font-size:16px;}</style>")}catch(e){console&&console.log(e)}}s=function(){var e,a=document.createElement("div");a.innerHTML=t._iconfont_svg_string_,(a=a.getElementsByTagName("svg")[0])&&(a.setAttribute("aria-hidden","true"),a.style.position="absolute",a.style.width=0,a.style.height=0,a.style.overflow="hidden",a=a,(e=document.body).firstChild?r(a,e.firstChild):e.appendChild(a))},document.addEventListener?~["complete","loaded","interactive"].indexOf(document.readyState)?setTimeout(s,0):(i=function(){document.removeEventListener("DOMContentLoaded",i,!1),s()},document.addEventListener("DOMContentLoaded",i,!1)):document.attachEvent&&(o=s,n=t.document,l=!1,d(),n.onreadystatechange=function(){"complete"==n.readyState&&(n.onreadystatechange=null,c())})}function c(){l||(l=!0,o())}function d(){try{n.documentElement.doScroll("left")}catch(e){return void setTimeout(d,50)}c()}}(window)},"983c":function(t,e,a){},d382:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticStyle:{display:"flex",height:"100%","flex-direction":"column","justify-content":"space-between"}},[s("div",{staticClass:"box"},[s("div",{staticClass:"heade-box"},[s("div",{staticClass:"btn-box-item"},[s("div",{on:{click:t.toHome}},[t._v("首页")])]),s("h1",{staticStyle:{"font-size":"1rem"}},[t._v("设置")]),s("el-dropdown",{staticClass:"dropdown",attrs:{trigger:"click"}},[s("span",{staticClass:"el-dropdown-link"},[s("i",{staticClass:"el-icon-info"})]),s("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[s("el-dropdown-item",{nativeOn:{click:function(e){return t.RegActive(e)}}},[t._v("软件注册")]),s("el-dropdown-item",[s("a",{attrs:{href:"http://www.inkstech.com/",target:"_black"}},[t._v("关于我们")])])],1)],1)],1),s("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{model:t.fromData,"label-width":"80px"}},[s("el-collapse",{attrs:{accordion:""},model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},[s("el-collapse-item",{attrs:{title:"头部设置",name:"1"}},[s("el-form-item",{attrs:{label:"标题栏"}},[s("el-checkbox",{attrs:{label:"显示标题栏","true-label":1,"false-label":0,size:"mini"},model:{value:t.fromData.headerjson[0].status,callback:function(e){t.$set(t.fromData.headerjson[0],"status",e)},expression:"fromData.headerjson[0].status"}})],1),s("el-form-item",{attrs:{label:"高度"}},[s("el-input",{model:{value:t.fromData.headerjson[0].height,callback:function(e){t.$set(t.fromData.headerjson[0],"height",e)},expression:"fromData.headerjson[0].height"}})],1),s("el-form-item",{attrs:{label:"背景色"}},[s("color-select",{attrs:{value:t.fromData.headerjson[0].bgcolour,placeholder:"请输入背景色"},on:{change:function(e){t.fromData.headerjson[0].bgcolour=e}}})],1),s("el-form-item",{attrs:{label:"文本HTML"}},[s("el-input",{attrs:{type:"textarea",autosize:{minRows:4}},model:{value:t.fromData.headerjson[0].htmltext,callback:function(e){t.$set(t.fromData.headerjson[0],"htmltext",e)},expression:"fromData.headerjson[0].htmltext"}})],1)],1),s("el-collapse-item",{attrs:{title:"链接设置",name:"2"}},[s("el-form-item",{attrs:{label:"当前页面",prop:"url"}},[s("el-input",{attrs:{autosize:{minRows:4},type:"textarea"},model:{value:t.fromData.urljson[0].url,callback:function(e){t.$set(t.fromData.urljson[0],"url",e)},expression:"fromData.urljson[0].url"}})],1),s("el-form-item",{staticStyle:{"margin-bottom":"0"}},[s("div",{staticClass:"icon-link"},[s("div",{staticClass:"icon-box"},[s("el-tooltip",{staticClass:"data-tooltip",attrs:{effect:"dark",content:"pdf1",placement:"bottom"}},[s("svg",{staticClass:"icon pdf",attrs:{"aria-hidden":"true"},on:{click:function(e){return t.hrefLink("pdf")}}},[s("use",{attrs:{"xlink:href":"#icon-pdf"}})])]),s("el-tooltip",{staticClass:"data-tooltip",attrs:{effect:"dark",content:"pdf4",placement:"bottom"}},[s("svg",{staticClass:"icon gengduoPDF",attrs:{"aria-hidden":"true"},on:{click:function(e){return t.hrefLink("gengduoPDF")}}},[s("use",{attrs:{"xlink:href":"#icon-gengduoPDF"}})])])],1)])])],1),s("el-collapse-item",{attrs:{title:"底部设置",name:"3"}},[s("el-form-item",{attrs:{label:"活动时间"}},[s("el-checkbox",{attrs:{label:"显示状态栏","true-label":1,"false-label":0,size:"mini"},model:{value:t.fromData.statusbarjson[0].status,callback:function(e){t.$set(t.fromData.statusbarjson[0],"status",e)},expression:"fromData.statusbarjson[0].status"}})],1),s("el-form-item",{attrs:{label:"高度"}},[s("el-input",{model:{value:t.fromData.statusbarjson[0].height,callback:function(e){t.$set(t.fromData.statusbarjson[0],"height",e)},expression:"fromData.statusbarjson[0].height"}})],1),s("el-form-item",{attrs:{label:"模式选择"}},[s("el-select",{attrs:{disabled:0==t.fromData.statusbarjson[0].status,placeholder:"请选择显示模式"},model:{value:t.fromData.statusbarjson[0].mode,callback:function(e){t.$set(t.fromData.statusbarjson[0],"mode",e)},expression:"fromData.statusbarjson[0].mode"}},[s("el-option",{attrs:{label:"固定",value:0}}),s("el-option",{attrs:{label:"跑马灯模式",value:1}})],1)],1),1==t.fromData.statusbarjson[0].mode&&0!=t.fromData.statusbarjson[0].status?s("el-form-item",{attrs:{label:"放映时间",prop:"url"}},[s("el-input",{model:{value:t.fromData.statusbarjson[0].time,callback:function(e){t.$set(t.fromData.statusbarjson[0],"time",e)},expression:"fromData.statusbarjson[0].time"}})],1):t._e(),s("el-form-item",{attrs:{label:"背景色"}},[s("color-select",{attrs:{value:t.fromData.statusbarjson[0].bgcolour,placeholder:"请输入背景色"},on:{change:function(e){t.fromData.statusbarjson[0].bgcolour=e}}})],1),s("el-form-item",{attrs:{label:"文本HTML"}},[s("el-input",{attrs:{type:"textarea",autosize:{minRows:4}},model:{value:t.fromData.statusbarjson[0].htmltext,callback:function(e){t.$set(t.fromData.statusbarjson[0],"htmltext",e)},expression:"fromData.statusbarjson[0].htmltext"}})],1)],1)],1),s("div",{staticClass:"btn-box"},[s("div",{staticClass:"btn-box-item"},[s("el-button",{attrs:{type:"primary"},on:{click:t.onSubmit}},[t._v("保存")]),s("el-button",{on:{click:t.showSubmitTemplate}},[t._v("保存模板")]),s("el-button",{on:{click:t.selectTemplate}},[t._v("引入模板")])],1),s("div",{staticStyle:{"margin-top":"10px"}},[s("el-button",{on:{click:t.downloadOffline}},[t._v("下载离线")]),s("el-button",{on:{click:function(e){return t.$refs.upload.click()}}},[t._v("加载离线")])],1)]),s("div",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}]},[s("input",{ref:"upload",attrs:{type:"file"},on:{change:t.getFile}})])],1),t.intervalTime(t.passkey.ex)<=30?s("div",{staticStyle:{"font-size":"16px"},on:{click:function(e){t.isContactShow=!0}}},[t._v(" 软件 "),s("strong",{staticStyle:{color:"blue",cursor:"pointer","text-decoration":"underline",padding:"0 3px"}},[t._v(t._s(t.intervalTime(t.passkey.ex)))]),t._v(" "+t._s(t.intervalTime(t.passkey.ex)>0?"天":"已")+"到期 ")]):t._e(),t.gropuFormVisible?s("el-dialog",{attrs:{title:"选择模板","append-to-body":!0,visible:t.gropuFormVisible,width:"80vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.gropuFormVisible=e}}},[s("div",{staticStyle:{height:"30vh",overflow:"hidden"}},[s("div",{directives:[{name:"infinite-scroll",rawName:"v-infinite-scroll",value:t.load,expression:"load"}],staticClass:"infinite-list",staticStyle:{overflow:"auto"}},t._l(t.templateData.list,(function(e){return s("div",{key:e.id,staticClass:"infinite-list-item"},[s("div",{staticClass:"item-name"},[t._v(t._s(e.templatename))]),s("div",{staticClass:"item-set"},[s("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(a){return t.selectSubmit(e)}}},[t._v("选择")]),s("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(a){return t.deleteSubmit(e)}}},[t._v("删除")])],1)])})),0),s("Pagination",{attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}})],1)]):t._e(),t.isShowSubmitTemplate?s("el-dialog",{attrs:{title:"模板名称","append-to-body":!0,visible:t.isShowSubmitTemplate,width:"80vw","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.isShowSubmitTemplate=e}}},[s("div",[s("el-form",{ref:"form",staticStyle:{width:"100%"},attrs:{"label-width":"80px"}},[s("el-form-item",{attrs:{label:"模板名称"}},[s("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"","allow-create":"","default-first-option":"",placeholder:"模板名称"},on:{change:t.selectChange},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}},t._l(t.templateData.list,(function(t){return s("el-option",{key:t.id,attrs:{value:t,label:t.templatename}})})),1)],1),s("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"center"}},[s("el-button",{staticStyle:{"margin-right":"4vw"},attrs:{type:"primary"},on:{click:t.onSubmitTemplate}},[t._v("确定")]),s("el-button",{attrs:{type:"danger"},on:{click:t.cancellation}},[t._v("取消")])],1)],1)],1)]):t._e(),t.isContactShow?s("el-dialog",{attrs:{title:"联系我们",width:"80vw",visible:t.isContactShow,"close-on-click-modal":!1},on:{"update:visible":function(e){t.isContactShow=e}}},[s("div",{staticClass:"contactBox"},[s("div",{staticClass:"leftBox"},[s("p",[t._v("产品咨询：186-0685-8808")]),s("p",[t._v("邮 箱：<EMAIL>")]),s("p",[t._v("Q Q：841850740")]),s("p",[t._v("地址：浙江省嘉兴市嘉善县惠民街道台升大道3号3号楼306室")])]),s("div",{staticClass:"qrCode"},[s("img",{staticStyle:{width:"120px",height:"120px"},attrs:{src:a("4680")}}),s("p",{staticStyle:{"font-size":"16px","font-weight":"700",color:"#000"}},[t._v("激活咨询")])])])]):t._e(),t.regactivevisible?s("el-dialog",{attrs:{title:"激活注册","custom-class":"zc-class",visible:t.regactivevisible,"close-on-click-modal":!1},on:{"update:visible":function(e){t.regactivevisible=e}}},[s("RegActive",{ref:"regactive",on:{closeDialog:function(e){t.regactivevisible=!1},publish:t.publish}})],1):t._e()],1),t._m(0)])},i=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"container-fluid",staticStyle:{"background-color":"rgb(48, 52, 63)"}},[s("div",{staticClass:"footer"},[s("div",{staticClass:"container footer-wrap"},[s("div",{staticClass:"footer-h2"},[t._v("买软件找我们")]),s("div",{staticClass:"footer-box"},[s("div",{staticClass:"box-left"},[s("div",{staticClass:"box-content"},[s("dt",[t._v("联系我们")]),s("dd",{staticClass:"c"},[t._v("产品咨询：186-0685-8808")]),s("dd",{staticClass:"c"},[t._v("邮箱：<EMAIL>")]),s("dd",{staticClass:"c"},[t._v("Q Q：841850740")]),s("dd",{staticClass:"c"},[t._v(" 地址：浙江省嘉兴市嘉善县惠民街道台升大道3号3号楼306室 ")])])]),s("div",{staticClass:"box-right"},[s("div",{staticClass:"footer-img-o"},[s("div",{staticClass:"footer-img"},[s("img",{attrs:{src:a("4680"),alt:""}}),s("span",{staticStyle:{"margin-top":"10px"}},[t._v("售前咨询")])])])])])]),s("div",{staticClass:"footer-beian footer-beian-pc",staticStyle:{"background-color":"rgb(41, 44, 51)"}},[s("span",{staticClass:"beian-one"},[t._v("Copyright © inkstech . All Rights Reserved 2020 - 2023")]),s("span",{staticClass:"beian-one"},[s("a",{attrs:{href:"https://www.beian.gov.cn/portal/registerSystemInfo?recordcode=33042102000748",target:"_blank"}},[s("img",{staticClass:"footer-beian-img",attrs:{src:a("568a"),alt:""}}),t._v("浙公网安备33042102000748号")])]),s("span",{staticClass:"beian-one"},[s("a",{attrs:{href:"https://beian.miit.gov.cn/",target:"_blank"}},[t._v(" 浙ICP备2020032908号 ")])]),t._v(" 嘉兴应凯科技有限公司 ")])])])}],o=a("c7eb"),n=a("1da1"),l=(a("b64b"),a("e9c4"),a("ac1f"),a("5319"),a("d3b7"),a("3ca3"),a("ddb0"),a("2b3d"),a("9861"),a("b0c0"),a("466d"),a("ace4"),a("5cc6"),a("9a8c"),a("a975"),a("735e"),a("c1ac"),a("d139"),a("3a7b"),a("d5d6"),a("82f8"),a("e91f"),a("60bd"),a("5f96"),a("3280"),a("3fcc"),a("ca91"),a("25a1"),a("cd26"),a("3c5d"),a("2954"),a("649e"),a("219c"),a("170b"),a("b39a"),a("72f7"),a("7579"),a("b775")),r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t._v(" 12323 ")])},c=[],d=a("2877"),u={},m=Object(d["a"])(u,r,c,!1,null,null,null),f=m.exports,p=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"public-input-color-picker"},[a("el-input",{attrs:{placeholder:t.placeholder},on:{input:t._change},model:{value:t.value2,callback:function(e){t.value2=e},expression:"value2"}}),a("el-color-picker",{attrs:{"show-alpha":t.showAlpha,predefine:t.predefineColors},on:{change:t._change},model:{value:t.value2,callback:function(e){t.value2=e},expression:"value2"}})],1)},h=[],v={name:"public-input-color-picker",data:function(){return{value2:""}},props:{showAlpha:{type:Boolean,default:!1},predefineColors:{type:Array,default:function(){return["#ff4500","#ff8c00","#ffd700","#90ee90","#00ced1","#1e90ff","#c71585","#FF0000"]}},value:{type:String,default:""},placeholder:{type:String,default:"请输入颜色编码"}},components:{},computed:{},created:function(){},onload:function(){},onShow:function(){},watch:{value:{handler:function(t,e){this.value2=JSON.parse(JSON.stringify(t))},immediate:!0,deep:!0}},mounted:function(){},methods:{_change:function(t){t&&this.$emit("change",t)}}},g=v,b=(a("1c2d"),Object(d["a"])(g,p,h,!1,null,"677d7ec9",null)),y=b.exports,S=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.passkeyShow?a("div",[a("el-result",{staticStyle:{padding:"0px 30px"},attrs:{icon:"已"==t.intervalTime(t.passkey.ex)?"warning":"success",title:"已"==t.intervalTime(t.passkey.ex)?"未注册":"已注册"}},[a("template",{slot:"subTitle"},[a("div",{staticStyle:{"font-size":"16px"}},[t._v(" 软件 "),a("strong",{staticStyle:{color:"#f56c6c"}},[t._v(t._s(t.intervalTime(t.passkey.ex)))]),t._v(" 到期 ")])]),a("template",{slot:"extra"},[a("el-button",{attrs:{type:"primary",size:"medium"},on:{click:function(e){t.passkeyShow=!t.passkeyShow}}},[t._v("注册")]),a("el-button",{attrs:{type:"primary",size:"medium"},on:{click:function(e){return t.$emit("closeDialog")}}},[t._v("返回")])],1)],2)],1):a("div",[a("div",[a("el-form",[a("el-form-item",{attrs:{label:"设备"}},[t.sn?a("div",[t._v(t._s(t.sn))]):a("div",[a("el-button",{on:{click:t.getSn}},[t._v(" 获取设备SN")])],1)]),a("el-form-item",{attrs:{label:"授权码"}},[a("el-input",{attrs:{placeholder:"请输入授权码"},model:{value:t.licensekey,callback:function(e){t.licensekey=e},expression:"licensekey"}})],1)],1)],1),a("div",{staticClass:"activeBox"},[a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.submitForm("formdata")}}},[t._v("激活")]),a("el-button",{on:{click:function(e){return t.$emit("closeDialog")}}},[t._v("取消")])],1)])])},j=[],w={data:function(){return{sn:this.$store.state.user.userinfo.sn,licensekey:"",passkeyShow:!1,passkey:{}}},mounted:function(){this.bindData()},methods:{bindData:function(){this.decrypt()},getSn:function(){var t=this;this.$request.get("/S16M91S1/getSN").then((function(e){if(200==e.data.code){var a={};localStorage.getItem("getInfoLeb")&&(a=JSON.parse(localStorage.getItem("getInfoLeb"))),a.sn=e.data.data,localStorage.setItem("getInfoLeb",JSON.stringify(a)),t.sn=a.sn}else t.$message.warning(e.data.msg||"获取设备SN信息失败")}))},submitForm:function(){var t=this;if(this.sn)if(this.licensekey){var e="http://192.168.99.96:10676";"dev"==this.$store.state.app.config.env?e="http://dev.inksyun.com:30471":"prod"==this.$store.state.app.config.env&&(e="http://regapi.inksyun.com"),this.axios.post(e+"/S21M02B1/register?sn="+this.sn+"&licensekey="+this.licensekey).then((function(e){200==e.data.code?(t.registrkey=e.data.data,t.saveConfig(),t.$emit("publish"),t.bindData(),t.$message.success(e.data.msg||"激活授权码成功")):t.$message.warning(e.data.msg||"激活授权码失败")}))}else this.$message.warning("授权码不能为空!");else this.$message.warning("设备Sn不能为空!")},decrypt:function(){var t=this;this.$request.get("/S16M91S1/checkHard").then((function(e){if(200==e.data.code)if(null==e.data.data);else if(""!=e.data.data){t.passkey=JSON.parse(e.data.data),t.passkeyShow=!0;var a=JSON.parse(localStorage.getItem("getInfo"));a["registrkey"]=e.data.data,localStorage.setItem("getInfo",JSON.stringify(a)),t.$store.dispatch("user/getInfo",JSON.parse(JSON.stringify(a)))}})).catch((function(t){console.log(t||"请求错误")}))},saveConfig:function(){var t=this,e={cfgkey:"system.registrkey",cfgvalue:this.registrkey};this.$request.post("/S26M93B1/setConfig",JSON.stringify(e)).then((function(e){200==e.data.code&&t.decrypt()}))},intervalTime:function(t){var e=Date.parse(new Date)/1e3,a=e,s=t/1e3,i=1e3*(s-a),o=Math.floor(i/864e5),n=i%864e5,l=Math.floor(n/36e5),r=n%36e5,c=(Math.floor(r/6e4),r%6e4);Math.round(c/1e3);return o<0?"已":o+"天 "+l+"小时 "}}},k=w,D=(a("dd20"),Object(d["a"])(k,S,j,!1,null,"0abe6a99",null)),_=D.exports,x=a("27ae"),C=(a("a78e"),{name:"setup",components:{tableList:f,ColorSelect:y,RegActive:_},inject:["mqtt"],data:function(){return{initData:null,fromData:{headerjson:[{bgcolour:"",height:"",mode:"",status:"",time:"",htmltext:""}],statusbarjson:[{bgcolour:"",height:"",status:"",time:""}],urljson:[{time:"",url:""}]},copyInitData:{},gropuFormVisible:!1,count:0,queryParams:{PageNum:1,PageSize:5,OrderType:1,SearchType:1},total:0,templateData:{},templatename:"",isShowSubmitTemplate:!1,value:"",templateDataSetect:{},activeName:"1",isContactShow:!1,regactivevisible:!1,rptname:"index"}},created:function(){var t=this;localStorage.getItem("getInfoLeb")?this.passkey=JSON.parse(localStorage.getItem("getInfoLeb")).registrkey?JSON.parse(JSON.parse(localStorage.getItem("getInfoLeb")).registrkey):{}:this.passkey={},this.copyInitData=JSON.parse(JSON.stringify(this.fromData)),this.$request.get("/S16M01B1/getHomePage").then((function(e){e.data.data?(e.data.data.headerjson?e.data.data.headerjson=JSON.parse(e.data.data.headerjson):JSON.parse(JSON.stringify(t.copyInitData.headerjson)),e.data.data.statusbarjson?e.data.data.statusbarjson=JSON.parse(e.data.data.statusbarjson):JSON.parse(JSON.stringify(t.copyInitData.statusbarjson)),e.data.data.urljson?e.data.data.urljson=JSON.parse(e.data.data.urljson):JSON.parse(JSON.stringify(t.copyInitData.urljson)),t.fromData=e.data.data):t.$router.replace("/init")})),this.init()},methods:{playAudioTip:function(){var t=new Audio,e=a("f2b0");t.src=e,t.play()},hrefLink:function(t){this.fromData.urljson[0].url="pdf"==t?this.$store.state.app.config.h5site+"/#/pdf/view1":this.$store.state.app.config.h5site+"/#/pdf/view4"},downloadOffline:function(){this.downloadJp()},downloadJp:function(){var t=x["Base64"].encode(JSON.stringify(this.fromData)),e=new Blob([this.dataURLtoBlob(t)],{type:"text/json"}),a=document.createElement("a");a.download=this.rptname+".inks",a.style.display="none",a.href=URL.createObjectURL(e),document.body.appendChild(a),a.click(),document.body.removeChild(a)},getFile:function(){var t=this,e=this.$refs.upload,a=e.files[0],s=new FileReader;s.readAsDataURL(a),s.onload=function(e){var s=e.target.result.split(";base64,")[1],i=a.name.lastIndexOf("."),o=a.name.substr(i+1);if("grf"==o)t.grfBase64Data=x["Base64"].decode(s);else if("inks"==o){t.reportBase64Data=x["Base64"].decode(s);try{t.fromData=JSON.parse(x["Base64"].decode(s))}catch(n){t.$message.warning("文件内容需要是JSON格式")}}else t.$message.warning("请上传正确的inks文件")}},dataURLtoBlob:function(t){try{var e=t.split(","),a=e[0].match(/:(.*?);/)[1],s=atob(e[1]),i=s.length,o=new Uint8Array(i);while(i--)o[i]=s.charCodeAt(i);return new Blob([o],{type:a})}catch(d){var n=t.split(","),l=atob(n[0]),r=l.length,c=new Uint8Array(r);while(r--)c[r]=l.charCodeAt(r);return new Blob([c])}},RegActive:function(){this.regactivevisible=!0},cancellation:function(){this.isShowSubmitTemplate=!1,this.value=""},init:function(){var t=this;this.$request.post("/S26M02B1/getPageList",JSON.stringify(this.queryParams)).then((function(e){t.templateData=e.data.data,t.total=t.templateData.total}))},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.init()},onSubmit:function(){var t=this;l["a"].post("/S26M01B1/setHomePage",JSON.stringify(this.fromData)).then((function(e){200==e.data.code&&(t.publish(),t.playAudioTip(),t.$message.success("设置成功,请重启系统"))}),(function(t){console.log(t,"resssjjjjjjjj")}))},load:function(){this.count+=2},selectChange:function(t){if(t.id)return this.value=t.templatename,void(this.templateDataSetect=t);this.templateDataSetect={},this.templateDataSetect.templatename=t},onSubmitTemplate:function(){var t=this;if(this.templateDataSetect.id?(this.fromData.id=this.templateDataSetect.id,this.fromData.templatename=this.templateDataSetect.templatename):(this.fromData.templatename=this.templateDataSetect.templatename,delete this.fromData.id),this.fromData.templatename)if(this.fromData.statusbarjson&&this.fromData.urljson&&this.fromData.headerjson){var e=JSON.parse(JSON.stringify(this.fromData));e.id?l["a"].post("/S26M02B1/update",JSON.stringify(this.fromData)).then((function(e){200==e.data.code&&(t.$message.success("修改成功"),t.isShowSubmitTemplate=!1,t.templateDataSetect={},t.value="",t.init(),t.$forceUpdate())}),(function(t){console.log(t,"resssjjjjjjjj")})):l["a"].post("/S26M02B1/create",JSON.stringify(e)).then((function(e){200==e.data.code&&(t.$message.success("保存成功"),t.isShowSubmitTemplate=!1,t.templateDataSetect={},t.init(),t.value="")}),(function(t){console.log(t,"resssjjjjjjjj")}))}else this.$message.warning("请填写数据");else this.$message.warning("请填写模板名称")},selectSubmit:function(t){var e=this;l["a"].get("/S26M02B1/getEntity?key=".concat(t.id)).then((function(t){t.data.data.statusbarjson=t.data.data.statusbarjson?JSON.parse(t.data.data.statusbarjson):JSON.parse(JSON.stringify(e.copyInitData.statusbarjson)),t.data.data.headerjson=t.data.data.headerjson?JSON.parse(t.data.data.headerjson):JSON.parse(JSON.stringify(e.copyInitData.headerjson)),t.data.data.urljson=t.data.data.urljson?JSON.parse(t.data.data.urljson):JSON.parse(JSON.stringify(e.copyInitData.urljson)),e.fromData=t.data.data,e.gropuFormVisible=!1}),(function(t){console.log(t,"resssjjjjjjjj")}))},selectTemplate:function(){this.templateData.list&&this.templateData.list.length?(this.queryParams={PageNum:1,PageSize:5,OrderType:1,SearchType:1},this.gropuFormVisible=!0,this.init()):this.$message.warning("还未有模板，请先添加模板！")},showSubmitTemplate:function(){this.queryParams={PageNum:1,PageSize:1e3,OrderType:1,SearchType:1},this.init(),this.isShowSubmitTemplate=!0},deleteSubmit:function(t){var e=this;this.$confirm("确定要删除该模板吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l["a"].get("/S26M02B1/delete?key=".concat(t.id)).then((function(t){200==t.data.code&&(e.init(),e.$message.success("删除成功"),e.$forceUpdate(),e.gropuFormVisible=!1)}),(function(t){console.log(t,"resssjjjjjjjj")}))}))},toHome:function(){this.$router.push("/home")},intervalTime:function(t){var e=Date.parse(new Date)/1e3,a=e,s=t/1e3,i=1e3*(s-a),o=Math.floor(i/864e5),n=i%864e5,l=(Math.floor(n/36e5),n%36e5),r=(Math.floor(l/6e4),l%6e4);Math.round(r/1e3);return o},publish:function(){var t=this;return Object(n["a"])(Object(o["a"])().mark((function e(){var a,s;return Object(o["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:a=t.$store.state.mqtt,s={msg:{cmd:{code:"refresh",url:""},msgtype:"cmd",sn:JSON.parse(localStorage.getItem("getInfoLeb")).sn},modulecode:"system"},a.publish("inks/saled/".concat(JSON.parse(localStorage.getItem("getInfoLeb")).sn),JSON.stringify(s),{qos:2});case 3:case"end":return e.stop()}}),e)})))()}}}),N=C,O=(a("eb08"),Object(d["a"])(N,s,i,!1,null,"6203172b",null));e["default"]=O.exports},dd20:function(t,e,a){"use strict";a("1ed0")},eb08:function(t,e,a){"use strict";a("4499")}}]);