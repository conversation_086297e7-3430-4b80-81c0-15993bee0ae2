(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-625684ae"],{10:function(t,e){},11:function(t,e){},12:function(t,e){},13:function(t,e){},"2af9":function(t,e,i){"use strict";var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"PhotoUploading"},[i("el-upload",{ref:"upload",staticClass:"upload-demo",attrs:{action:t.actionsUrl,"on-success":t.successFn,"on-exceed":t.onExceed,"before-upload":t.beforeUpload,"before-remove":t.beforeRemove,"on-change":t.onChangeFn,"auto-upload":!1,"file-list":t.fileLists,"on-error":t.onErrorFn,accept:t.fileFormat,limit:t.limitQuantity,drag:"",multiple:""}},[i("i",{staticClass:"el-icon-upload"}),i("div",{staticClass:"el-upload__text"},[t._v("将文件拖到此处，或"),i("em",[t._v("点击上传")])]),i("div",{staticClass:"el-upload__tip",staticStyle:{"font-size":"14px"},attrs:{slot:"tip"},slot:"tip"},[t._v(" 只能上传 "+t._s(t.strString)+" 文件，一次性最多上传 "+t._s(t.limitQuantity)+" 个文件，每个文件不得超过 "+t._s(t.sizeLimit)+"MB ")])])],1)},n=[];i("a9e3"),i("a15b"),i("4de4"),i("d3b7"),i("b0c0"),i("d81d"),i("99af"),i("53ca"),i("ac1f"),i("00b4"),i("5319"),i("4d63"),i("2c3e"),i("25f0"),i("4d90"),i("159b");function o(t,e,i){var s,n,o,a,r;console.log("6666666666666666666666");var l=function l(){var c=+new Date-a;c<e&&c>0?s=setTimeout(l,e-c):(s=null,i||(r=t.apply(o,n),s||(o=n=null)))};return function(){for(var n=arguments.length,c=new Array(n),u=0;u<n;u++)c[u]=arguments[u];o=this,a=+new Date;var d=i&&!s;return s||(s=setTimeout(l,e)),d&&(r=t.apply(o,c),o=c=null),r}}var a={data:function(){return{fileList:[],fileIdList:[],fileSize:2097152,limitList:[],errList:[],strString:"",fileLists:[]}},watch:{isTrue:function(t,e){console.log("清除上传图片"),this.fileList=[],this.fileIdList=[],this.limitList=[],this.fileLists=[]},upLoads:function(t,e){this.fileLists.length||this.$message({type:"error",message:"请选择上传的文件"}),this.$refs.upload.submit()}},props:{actionsUrl:{type:String,require:!1,default:""},isTrue:{type:Boolean,require:!1,default:"false"},isWidth:{type:String,require:!1},upLoads:{type:Boolean,require:!0},fileFormat:{type:String,require:!1,default:".jpg,.mp4,.gif,.pdf,.JPG,.MP4,.GIF,.PDF"},limitQuantity:{type:Number,require:!1,default:5},sizeLimit:{type:Number,require:!1,default:2}},mounted:function(){if(void 0!==this.isWidth){var t=document.querySelector(".PhotoUploading");t.style.width=this.isWidth}},created:function(){var t=this.fileFormat.split(",");this.strString=t.join(" ");var e=1024*this.sizeLimit*1024;this.fileSize=e},methods:{onChangeFn:function(t,e){console.log(t,e),this.limitList=e,this.fileLists=this.fileLists.length?this.fileLists:[],console.log(this.limitList,"this.fileLists"),this.limitFn()},beforeRemove:function(t,e){console.log(t,e);var i=e.filter((function(e){return e.name!==t.name}));this.fileLists=i},beforeUpload:function(t){console.log(t,"fileeeeeeeeee")},successFn:function(t,e,i){this.fileIdList=i,this.fileLsitfn()},onErrorFn:function(t,e,i){"error"===t.type&&this.errList.push(e),this.errorFn()},errorFn:o((function(){var t=[];this.errList.map((function(e){t.push(e.name)})),this.$message({type:"error",message:"".concat(t.map((function(t){return t+"/"})),"上传失败")})}),500),limitFn:o((function(){var t=this,e=[],i=[];this.limitList.map((function(s){var n=s.name.substr(-4,4),o=t.fileFormat.split(","),a=o.map((function(t){return n!==t}));s.size<t.fileSize&&!a.every((function(t){return!0===t}))?i.push(s):e.push(s)})),e.length&&this.$message({type:"error",message:"只能上传  ".concat(this.strString,"  文件，每张不得超过 ").concat(this.sizeLimit,"MB,")}),this.fileLists=i}),500),fileLsitfn:o((function(){var t=[];this.fileIdList.map((function(e){t.push({status:e.status,name:e.name,url:e.response.data[0].fileId})})),this.fileList=t,this.$emit("fileId",this.fileList)}),500),onExceed:function(t,e){this.$message({type:"error",message:"上传的图片超过最大限制，一次性最多上传".concat(this.limitQuantity,"张")})}}},r=a,l=(i("e854"),i("2877")),c=Object(l["a"])(r,s,n,!1,null,"53ac21f1",null);e["a"]=c.exports},"7d46":function(t,e,i){},8:function(t,e){},"8c17":function(t,e,i){"use strict";i("9008")},9:function(t,e){},9008:function(t,e,i){},b20b:function(t,e,i){"use strict";i.r(e);var s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"contenter"},[i("div",{staticClass:"content-box"},[t.pdfUrl[0]?i("div",{staticClass:"content-item"},[i("pdf",{ref:"pdf",attrs:{src:t.pdfUrl[0]}}),i("div",{staticClass:"upload-btn",on:{click:function(e){return t.showUploadBoxFn("2a")}}},[i("i",{staticClass:"el-icon-upload"})])],1):t._e(),t.pdfUrl[1]?i("div",{staticClass:"content-item"},[i("pdf",{ref:"pdf",attrs:{src:t.pdfUrl[1]}}),i("div",{staticClass:"upload-btn upload-btn2",on:{click:function(e){return t.showUploadBoxFn("2b")}}},[i("i",{staticClass:"el-icon-upload"})])],1):t._e()]),t.showUploadBox?i("el-dialog",{attrs:{title:"上传pdf","append-to-body":!0,visible:t.showUploadBox,width:"500px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.showUploadBox=e}}},[i("div",{staticStyle:{height:"30vh",overflow:"hidden"}},[i("upload",{attrs:{"is-width":"80%","is-true":t.istrue,"up-loads":t.upLoads,limitQuantity:1,"file-format":".pdf,.PDF",actionsUrl:t.$store.state.app.config.baseURL+"/S26M03B1/upload?code="+t.codeNumble},on:{fileId:t.fileListFn,disabledBtn:t.disFn}})],1),i("div",[i("el-button",{attrs:{type:"primary"},on:{click:function(e){t.upLoads=!1}}},[t._v("确定")]),i("el-button",{attrs:{type:"info"},on:{click:function(e){t.istrue=!0}}},[t._v("取消")])],1)]):t._e()],1)},n=[],o=i("c7eb"),a=i("1da1"),r=(i("d3b7"),i("3ca3"),i("ddb0"),i("2b3d"),i("9861"),i("2af9")),l=i("858e"),c={name:"view1",components:{upload:r["a"],pdf:l["a"]},data:function(){return{initData:null,istrue:!1,upLoads:!0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},showUploadBox:!1,pdfUrl:[],numPages:0,pdfSrc:"",codeNumble:"2a"}},created:function(){this.bindData()},mounted:function(){},methods:{bindData:function(){var t=this;return Object(a["a"])(Object(o["a"])().mark((function e(){return Object(o["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.pdfUrl=[],e.next=3,t.$request.get("/S26M03B1/getFile?code=2a",{responseType:"blob"}).then((function(e){var i=[];i.push(e.data);var s=window.URL.createObjectURL(new Blob(i,{type:"application/pdf"}));t.pdfUrl.push(s)}));case 3:return e.next=5,t.$request.get("/S26M03B1/getFile?code=2b",{responseType:"blob"}).then((function(e){var i=[];i.push(e.data);var s=window.URL.createObjectURL(new Blob(i,{type:"application/pdf"}));t.pdfUrl.push(s)}));case 5:case"end":return e.stop()}}),e)})))()},disFn:function(t){this.disisTrue=t},showUploadBoxFn:function(t){this.codeNumble=t,this.showUploadBox=!0},fileListFn:function(t){this.showUploadBox=!1,this.upLoads=!0,this.istrue=!1,this.bindData()}}},u=c,d=(i("8c17"),i("2877")),f=Object(d["a"])(u,s,n,!1,null,"3a8b64c9",null);e["default"]=f.exports},e854:function(t,e,i){"use strict";i("7d46")}}]);