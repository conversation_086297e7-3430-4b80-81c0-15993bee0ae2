(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-274fe984"],{2639:function(e,t,r){e.exports=function(){return new Worker(r.p+"d21cf2c10c32bcc34455.worker.js")}},"2c3e":function(e,t,r){var n=r("83ab"),i=r("9f7f").UNSUPPORTED_Y,a=r("9bf2").f,o=r("69f3").get,s=RegExp.prototype;n&&i&&a(RegExp.prototype,"sticky",{configurable:!0,get:function(){if(this!==s){if(this instanceof RegExp)return!!o(this).sticky;throw TypeError("Incompatible receiver, RegExp required")}}})},"2eda":function(e,t,r){},4383:function(module,exports,__webpack_require__){(function(Buffer,process,global){
/**
 * @licstart The following is the entire license notice for the
 * Javascript code in this page
 *
 * Copyright 2020 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * @licend The above is the entire license notice for the
 * Javascript code in this page
 */
(function(e,t){module.exports=t()})(0,(function(){return function(e){var t={};function r(n){if(t[n])return t[n].exports;var i=t[n]={i:n,l:!1,exports:{}};return e[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}return r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)r.d(n,i,function(t){return e[t]}.bind(null,i));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=0)}([function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addLinkAttributes",{enumerable:!0,get:function(){return n.addLinkAttributes}}),Object.defineProperty(t,"getFilenameFromUrl",{enumerable:!0,get:function(){return n.getFilenameFromUrl}}),Object.defineProperty(t,"LinkTarget",{enumerable:!0,get:function(){return n.LinkTarget}}),Object.defineProperty(t,"loadScript",{enumerable:!0,get:function(){return n.loadScript}}),Object.defineProperty(t,"PDFDateString",{enumerable:!0,get:function(){return n.PDFDateString}}),Object.defineProperty(t,"RenderingCancelledException",{enumerable:!0,get:function(){return n.RenderingCancelledException}}),Object.defineProperty(t,"build",{enumerable:!0,get:function(){return i.build}}),Object.defineProperty(t,"getDocument",{enumerable:!0,get:function(){return i.getDocument}}),Object.defineProperty(t,"LoopbackPort",{enumerable:!0,get:function(){return i.LoopbackPort}}),Object.defineProperty(t,"PDFDataRangeTransport",{enumerable:!0,get:function(){return i.PDFDataRangeTransport}}),Object.defineProperty(t,"PDFWorker",{enumerable:!0,get:function(){return i.PDFWorker}}),Object.defineProperty(t,"version",{enumerable:!0,get:function(){return i.version}}),Object.defineProperty(t,"CMapCompressionType",{enumerable:!0,get:function(){return a.CMapCompressionType}}),Object.defineProperty(t,"createObjectURL",{enumerable:!0,get:function(){return a.createObjectURL}}),Object.defineProperty(t,"createPromiseCapability",{enumerable:!0,get:function(){return a.createPromiseCapability}}),Object.defineProperty(t,"createValidAbsoluteUrl",{enumerable:!0,get:function(){return a.createValidAbsoluteUrl}}),Object.defineProperty(t,"InvalidPDFException",{enumerable:!0,get:function(){return a.InvalidPDFException}}),Object.defineProperty(t,"MissingPDFException",{enumerable:!0,get:function(){return a.MissingPDFException}}),Object.defineProperty(t,"OPS",{enumerable:!0,get:function(){return a.OPS}}),Object.defineProperty(t,"PasswordResponses",{enumerable:!0,get:function(){return a.PasswordResponses}}),Object.defineProperty(t,"PermissionFlag",{enumerable:!0,get:function(){return a.PermissionFlag}}),Object.defineProperty(t,"removeNullCharacters",{enumerable:!0,get:function(){return a.removeNullCharacters}}),Object.defineProperty(t,"shadow",{enumerable:!0,get:function(){return a.shadow}}),Object.defineProperty(t,"UnexpectedResponseException",{enumerable:!0,get:function(){return a.UnexpectedResponseException}}),Object.defineProperty(t,"UNSUPPORTED_FEATURES",{enumerable:!0,get:function(){return a.UNSUPPORTED_FEATURES}}),Object.defineProperty(t,"Util",{enumerable:!0,get:function(){return a.Util}}),Object.defineProperty(t,"VerbosityLevel",{enumerable:!0,get:function(){return a.VerbosityLevel}}),Object.defineProperty(t,"AnnotationLayer",{enumerable:!0,get:function(){return o.AnnotationLayer}}),Object.defineProperty(t,"apiCompatibilityParams",{enumerable:!0,get:function(){return s.apiCompatibilityParams}}),Object.defineProperty(t,"GlobalWorkerOptions",{enumerable:!0,get:function(){return u.GlobalWorkerOptions}}),Object.defineProperty(t,"renderTextLayer",{enumerable:!0,get:function(){return c.renderTextLayer}}),Object.defineProperty(t,"SVGGraphics",{enumerable:!0,get:function(){return l.SVGGraphics}});var n=r(1),i=r(202),a=r(5),o=r(216),s=r(206),u=r(209),c=r(217),l=r(218),h=r(7),f=h.isNodeJS;if(f){var d=r(219).PDFNodeStream;(0,i.setPDFNetworkStreamFactory)((function(e){return new d(e)}))}else{var p,v=r(222).PDFNetworkStream;(0,n.isFetchSupported)()&&(p=r(223).PDFFetchStream),(0,i.setPDFNetworkStreamFactory)((function(e){return p&&(0,n.isValidFetchUrl)(e.url)?new p(e):new v(e)}))}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.addLinkAttributes=O,t.getFilenameFromUrl=I,t.isFetchSupported=N,t.isValidFetchUrl=D,t.loadScript=j,t.deprecated=U,t.PDFDateString=t.StatTimer=t.DOMSVGFactory=t.DOMCMapReaderFactory=t.BaseCMapReaderFactory=t.DOMCanvasFactory=t.BaseCanvasFactory=t.DEFAULT_LINK_REL=t.LinkTarget=t.RenderingCancelledException=t.PageViewport=void 0;var n=a(r(2)),i=r(5);function a(e){return e&&e.__esModule?e:{default:e}}function o(e,t){var r;if("undefined"===typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(r=s(e))||t&&e&&"number"===typeof e.length){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,u=!1;return{s:function(){r=e[Symbol.iterator]()},n:function(){var e=r.next();return o=e.done,e},e:function(e){u=!0,a=e},f:function(){try{o||null==r["return"]||r["return"]()}finally{if(u)throw a}}}}function s(e,t){if(e){if("string"===typeof e)return u(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?u(e,t):void 0}}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function c(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(c){return void r(c)}s.done?t(u):Promise.resolve(u).then(n,i)}function l(e){return function(){var t=this,r=arguments;return new Promise((function(n,i){var a=e.apply(t,r);function o(e){c(a,n,i,o,s,"next",e)}function s(e){c(a,n,i,o,s,"throw",e)}o(void 0)}))}}function h(e){return h="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},h(e)}function f(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&d(e,t)}function d(e,t){return d=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},d(e,t)}function p(e){var t=y();return function(){var r,n=m(e);if(t){var i=m(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return v(this,r)}}function v(e,t){return!t||"object"!==h(t)&&"function"!==typeof t?g(e):t}function g(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function y(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function m(e){return m=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},m(e)}function b(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function w(e,t,r){return t&&_(e.prototype,t),r&&_(e,r),e}var S="noopener noreferrer nofollow";t.DEFAULT_LINK_REL=S;var A="http://www.w3.org/2000/svg",k=function(){function e(){b(this,e),this.constructor===e&&(0,i.unreachable)("Cannot initialize BaseCanvasFactory.")}return w(e,[{key:"create",value:function(e,t){(0,i.unreachable)("Abstract method `create` called.")}},{key:"reset",value:function(e,t,r){if(!e.canvas)throw new Error("Canvas is not specified");if(t<=0||r<=0)throw new Error("Invalid canvas size");e.canvas.width=t,e.canvas.height=r}},{key:"destroy",value:function(e){if(!e.canvas)throw new Error("Canvas is not specified");e.canvas.width=0,e.canvas.height=0,e.canvas=null,e.context=null}}]),e}();t.BaseCanvasFactory=k;var x=function(e){f(r,e);var t=p(r);function r(){var e,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=n.ownerDocument,a=void 0===i?globalThis.document:i;return b(this,r),e=t.call(this),e._document=a,e}return w(r,[{key:"create",value:function(e,t){if(e<=0||t<=0)throw new Error("Invalid canvas size");var r=this._document.createElement("canvas"),n=r.getContext("2d");return r.width=e,r.height=t,{canvas:r,context:n}}}]),r}(k);t.DOMCanvasFactory=x;var P=function(){function e(t){var r=t.baseUrl,n=void 0===r?null:r,a=t.isCompressed,o=void 0!==a&&a;b(this,e),this.constructor===e&&(0,i.unreachable)("Cannot initialize BaseCMapReaderFactory."),this.baseUrl=n,this.isCompressed=o}return w(e,[{key:"fetch",value:function(){var e=l(n["default"].mark((function e(t){var r,a,o,s=this;return n["default"].wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r=t.name,this.baseUrl){e.next=3;break}throw new Error('The CMap "baseUrl" parameter must be specified, ensure that the "cMapUrl" and "cMapPacked" API parameters are provided.');case 3:if(r){e.next=5;break}throw new Error("CMap name must be specified.");case 5:return a=this.baseUrl+r+(this.isCompressed?".bcmap":""),o=this.isCompressed?i.CMapCompressionType.BINARY:i.CMapCompressionType.NONE,e.abrupt("return",this._fetchData(a,o)["catch"]((function(e){throw new Error("Unable to load ".concat(s.isCompressed?"binary ":"","CMap at: ").concat(a))})));case 8:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}()},{key:"_fetchData",value:function(e,t){(0,i.unreachable)("Abstract method `_fetchData` called.")}}]),e}();t.BaseCMapReaderFactory=P;var C=function(e){f(r,e);var t=p(r);function r(){return b(this,r),t.apply(this,arguments)}return w(r,[{key:"_fetchData",value:function(e,t){var r=this;return N()&&D(e,document.baseURI)?fetch(e).then(function(){var e=l(n["default"].mark((function e(a){var o;return n["default"].wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(a.ok){e.next=2;break}throw new Error(a.statusText);case 2:if(!r.isCompressed){e.next=10;break}return e.t0=Uint8Array,e.next=6,a.arrayBuffer();case 6:e.t1=e.sent,o=new e.t0(e.t1),e.next=15;break;case 10:return e.t2=i.stringToBytes,e.next=13,a.text();case 13:e.t3=e.sent,o=(0,e.t2)(e.t3);case 15:return e.abrupt("return",{cMapData:o,compressionType:t});case 16:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()):new Promise((function(n,a){var o=new XMLHttpRequest;o.open("GET",e,!0),r.isCompressed&&(o.responseType="arraybuffer"),o.onreadystatechange=function(){if(o.readyState===XMLHttpRequest.DONE){var e;if(200===o.status||0===o.status)if(r.isCompressed&&o.response?e=new Uint8Array(o.response):!r.isCompressed&&o.responseText&&(e=(0,i.stringToBytes)(o.responseText)),e)return void n({cMapData:e,compressionType:t});a(new Error(o.statusText))}},o.send(null)}))}}]),r}(P);t.DOMCMapReaderFactory=C;var T=function(){function e(){b(this,e)}return w(e,[{key:"create",value:function(e,t){(0,i.assert)(e>0&&t>0,"Invalid SVG dimensions");var r=document.createElementNS(A,"svg:svg");return r.setAttribute("version","1.1"),r.setAttribute("width",e+"px"),r.setAttribute("height",t+"px"),r.setAttribute("preserveAspectRatio","none"),r.setAttribute("viewBox","0 0 "+e+" "+t),r}},{key:"createElement",value:function(e){return(0,i.assert)("string"===typeof e,"Invalid SVG element type"),document.createElementNS(A,e)}}]),e}();t.DOMSVGFactory=T;var R=function(){function e(t){var r=t.viewBox,n=t.scale,i=t.rotation,a=t.offsetX,o=void 0===a?0:a,s=t.offsetY,u=void 0===s?0:s,c=t.dontFlip,l=void 0!==c&&c;b(this,e),this.viewBox=r,this.scale=n,this.rotation=i,this.offsetX=o,this.offsetY=u;var h,f,d,p,v,g,y,m,_=(r[2]+r[0])/2,w=(r[3]+r[1])/2;switch(i%=360,i=i<0?i+360:i,i){case 180:h=-1,f=0,d=0,p=1;break;case 90:h=0,f=1,d=1,p=0;break;case 270:h=0,f=-1,d=-1,p=0;break;case 0:h=1,f=0,d=0,p=-1;break;default:throw new Error("PageViewport: Invalid rotation, must be a multiple of 90 degrees.")}l&&(d=-d,p=-p),0===h?(v=Math.abs(w-r[1])*n+o,g=Math.abs(_-r[0])*n+u,y=Math.abs(r[3]-r[1])*n,m=Math.abs(r[2]-r[0])*n):(v=Math.abs(_-r[0])*n+o,g=Math.abs(w-r[1])*n+u,y=Math.abs(r[2]-r[0])*n,m=Math.abs(r[3]-r[1])*n),this.transform=[h*n,f*n,d*n,p*n,v-h*n*_-d*n*w,g-f*n*_-p*n*w],this.width=y,this.height=m}return w(e,[{key:"clone",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=t.scale,n=void 0===r?this.scale:r,i=t.rotation,a=void 0===i?this.rotation:i,o=t.offsetX,s=void 0===o?this.offsetX:o,u=t.offsetY,c=void 0===u?this.offsetY:u,l=t.dontFlip,h=void 0!==l&&l;return new e({viewBox:this.viewBox.slice(),scale:n,rotation:a,offsetX:s,offsetY:c,dontFlip:h})}},{key:"convertToViewportPoint",value:function(e,t){return i.Util.applyTransform([e,t],this.transform)}},{key:"convertToViewportRectangle",value:function(e){var t=i.Util.applyTransform([e[0],e[1]],this.transform),r=i.Util.applyTransform([e[2],e[3]],this.transform);return[t[0],t[1],r[0],r[1]]}},{key:"convertToPdfPoint",value:function(e,t){return i.Util.applyInverseTransform([e,t],this.transform)}}]),e}();t.PageViewport=R;var E=function(e){f(r,e);var t=p(r);function r(e,n){var i;return b(this,r),i=t.call(this,e),i.type=n,i}return r}(i.BaseException);t.RenderingCancelledException=E;var L={NONE:0,SELF:1,BLANK:2,PARENT:3,TOP:4};function O(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.url,n=t.target,a=t.rel,o=t.enabled,s=void 0===o||o;(0,i.assert)(r&&"string"===typeof r,'addLinkAttributes: A valid "url" parameter must provided.');var u=(0,i.removeNullCharacters)(r);s?e.href=e.title=u:(e.href="",e.title="Disabled: ".concat(u),e.onclick=function(){return!1});var c="";switch(n){case L.NONE:break;case L.SELF:c="_self";break;case L.BLANK:c="_blank";break;case L.PARENT:c="_parent";break;case L.TOP:c="_top";break}e.target=c,e.rel="string"===typeof a?a:S}function I(e){var t=e.indexOf("#"),r=e.indexOf("?"),n=Math.min(t>0?t:e.length,r>0?r:e.length);return e.substring(e.lastIndexOf("/",n)+1,n)}t.LinkTarget=L;var M,F=function(){function e(){b(this,e),this.started=Object.create(null),this.times=[]}return w(e,[{key:"time",value:function(e){e in this.started&&(0,i.warn)("Timer is already running for ".concat(e)),this.started[e]=Date.now()}},{key:"timeEnd",value:function(e){e in this.started||(0,i.warn)("Timer has not been started for ".concat(e)),this.times.push({name:e,start:this.started[e],end:Date.now()}),delete this.started[e]}},{key:"toString",value:function(){var e,t=[],r=0,n=o(this.times);try{for(n.s();!(e=n.n()).done;){var i=e.value,a=i.name;a.length>r&&(r=a.length)}}catch(h){n.e(h)}finally{n.f()}var s,u=o(this.times);try{for(u.s();!(s=u.n()).done;){var c=s.value,l=c.end-c.start;t.push("".concat(c.name.padEnd(r)," ").concat(l,"ms\n"))}}catch(h){u.e(h)}finally{u.f()}return t.join("")}}]),e}();function N(){return"undefined"!==typeof fetch&&"undefined"!==typeof Response&&"body"in Response.prototype&&"undefined"!==typeof ReadableStream}function D(e,t){try{var r=t?new URL(e,t):new URL(e),n=r.protocol;return"http:"===n||"https:"===n}catch(i){return!1}}function j(e){return new Promise((function(t,r){var n=document.createElement("script");n.src=e,n.onload=t,n.onerror=function(){r(new Error("Cannot load script at: ".concat(n.src)))},(document.head||document.documentElement).appendChild(n)}))}function U(e){console.log("Deprecated API usage: "+e)}t.StatTimer=F;var q=function(){function e(){b(this,e)}return w(e,null,[{key:"toDateObject",value:function(e){if(!e||!(0,i.isString)(e))return null;M||(M=new RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?"));var t=M.exec(e);if(!t)return null;var r=parseInt(t[1],10),n=parseInt(t[2],10);n=n>=1&&n<=12?n-1:0;var a=parseInt(t[3],10);a=a>=1&&a<=31?a:1;var o=parseInt(t[4],10);o=o>=0&&o<=23?o:0;var s=parseInt(t[5],10);s=s>=0&&s<=59?s:0;var u=parseInt(t[6],10);u=u>=0&&u<=59?u:0;var c=t[7]||"Z",l=parseInt(t[8],10);l=l>=0&&l<=23?l:0;var h=parseInt(t[9],10)||0;return h=h>=0&&h<=59?h:0,"-"===c?(o+=l,s+=h):"+"===c&&(o-=l,s-=h),new Date(Date.UTC(r,n,a,o,s,u))}}]),e}();t.PDFDateString=q},function(e,t,r){"use strict";e.exports=r(3)},function(e,t,r){"use strict";(function(e){function t(e){return t="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t(e)}var r=function(e){var r,n=Object.prototype,i=n.hasOwnProperty,a="function"===typeof Symbol?Symbol:{},o=a.iterator||"@@iterator",s=a.asyncIterator||"@@asyncIterator",u=a.toStringTag||"@@toStringTag";function c(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(I){c=function(e,t,r){return e[t]=r}}function l(e,t,r,n){var i=t&&t.prototype instanceof y?t:y,a=Object.create(i.prototype),o=new E(n||[]);return a._invoke=P(e,r,o),a}function h(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(I){return{type:"throw",arg:I}}}e.wrap=l;var f="suspendedStart",d="suspendedYield",p="executing",v="completed",g={};function y(){}function m(){}function b(){}var _={};_[o]=function(){return this};var w=Object.getPrototypeOf,S=w&&w(w(L([])));S&&S!==n&&i.call(S,o)&&(_=S);var A=b.prototype=y.prototype=Object.create(_);function k(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function x(e,r){function n(a,o,s,u){var c=h(e[a],e,o);if("throw"!==c.type){var l=c.arg,f=l.value;return f&&"object"===t(f)&&i.call(f,"__await")?r.resolve(f.__await).then((function(e){n("next",e,s,u)}),(function(e){n("throw",e,s,u)})):r.resolve(f).then((function(e){l.value=e,s(l)}),(function(e){return n("throw",e,s,u)}))}u(c.arg)}var a;function o(e,t){function i(){return new r((function(r,i){n(e,t,r,i)}))}return a=a?a.then(i,i):i()}this._invoke=o}function P(e,t,r){var n=f;return function(i,a){if(n===p)throw new Error("Generator is already running");if(n===v){if("throw"===i)throw a;return O()}r.method=i,r.arg=a;while(1){var o=r.delegate;if(o){var s=C(o,r);if(s){if(s===g)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(n===f)throw n=v,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n=p;var u=h(e,t,r);if("normal"===u.type){if(n=r.done?v:d,u.arg===g)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(n=v,r.method="throw",r.arg=u.arg)}}}function C(e,t){var n=e.iterator[t.method];if(n===r){if(t.delegate=null,"throw"===t.method){if(e.iterator["return"]&&(t.method="return",t.arg=r,C(e,t),"throw"===t.method))return g;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return g}var i=h(n,e.iterator,t.arg);if("throw"===i.type)return t.method="throw",t.arg=i.arg,t.delegate=null,g;var a=i.arg;return a?a.done?(t[e.resultName]=a.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=r),t.delegate=null,g):a:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,g)}function T(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function R(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function E(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(T,this),this.reset(!0)}function L(e){if(e){var t=e[o];if(t)return t.call(e);if("function"===typeof e.next)return e;if(!isNaN(e.length)){var n=-1,a=function t(){while(++n<e.length)if(i.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=r,t.done=!0,t};return a.next=a}}return{next:O}}function O(){return{value:r,done:!0}}return m.prototype=A.constructor=b,b.constructor=m,m.displayName=c(b,u,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"===typeof e&&e.constructor;return!!t&&(t===m||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,c(e,u,"GeneratorFunction")),e.prototype=Object.create(A),e},e.awrap=function(e){return{__await:e}},k(x.prototype),x.prototype[s]=function(){return this},e.AsyncIterator=x,e.async=function(t,r,n,i,a){void 0===a&&(a=Promise);var o=new x(l(t,r,n,i),a);return e.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},k(A),c(A,u,"Generator"),A[o]=function(){return this},A.toString=function(){return"[object Generator]"},e.keys=function(e){var t=[];for(var r in e)t.push(r);return t.reverse(),function r(){while(t.length){var n=t.pop();if(n in e)return r.value=n,r.done=!1,r}return r.done=!0,r}},e.values=L,E.prototype={constructor:E,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=r,this.done=!1,this.delegate=null,this.method="next",this.arg=r,this.tryEntries.forEach(R),!e)for(var t in this)"t"===t.charAt(0)&&i.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=r)},stop:function(){this.done=!0;var e=this.tryEntries[0],t=e.completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(n,i){return s.type="throw",s.arg=e,t.next=n,i&&(t.method="next",t.arg=r),!!i}for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a],s=o.completion;if("root"===o.tryLoc)return n("end");if(o.tryLoc<=this.prev){var u=i.call(o,"catchLoc"),c=i.call(o,"finallyLoc");if(u&&c){if(this.prev<o.catchLoc)return n(o.catchLoc,!0);if(this.prev<o.finallyLoc)return n(o.finallyLoc)}else if(u){if(this.prev<o.catchLoc)return n(o.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return n(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&i.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var a=n;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=e,o.arg=t,a?(this.method="next",this.next=a.finallyLoc,g):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),R(r),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var i=n.arg;R(r)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:L(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=r),g}},e}("object"===t(e)?e.exports:{});try{regeneratorRuntime=r}catch(n){Function("r","regeneratorRuntime = r")(r)}}).call(this,r(4)(e))},function(e,t,r){"use strict";e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},function(e,t,r){"use strict";function n(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function i(e,t,r){return t&&n(e.prototype,t),r&&n(e,r),e}function a(e){return a="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&u(e,t)}function u(e,t){return u=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},u(e,t)}function c(e){var t=f();return function(){var r,n=d(e);if(t){var i=d(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return l(this,r)}}function l(e,t){return!t||"object"!==a(t)&&"function"!==typeof t?h(e):t}function h(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function f(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function d(e){return d=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},d(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.arrayByteLength=ne,t.arraysToBytes=ie,t.assert=q,t.bytesToString=te,t.createPromiseCapability=Ae,t.escapeString=pe,t.getModificationDate=Se,t.getVerbosityLevel=N,t.info=D,t.isArrayBuffer=_e,t.isArrayEqual=we,t.isBool=ye,t.isNum=me,t.isString=be,t.isSameOrigin=B,t.createValidAbsoluteUrl=V,t.removeNullCharacters=ee,t.setVerbosityLevel=F,t.shadow=z,t.string32=ae,t.stringToBytes=re,t.stringToPDFString=de,t.stringToUTF8String=ve,t.utf8StringToString=ge,t.warn=j,t.unreachable=U,t.IsEvalSupportedCached=t.IsLittleEndianCached=t.createObjectURL=t.FormatError=t.Util=t.UnknownErrorException=t.UnexpectedResponseException=t.TextRenderingMode=t.StreamType=t.PermissionFlag=t.PasswordResponses=t.PasswordException=t.MissingPDFException=t.InvalidPDFException=t.AbortException=t.CMapCompressionType=t.ImageKind=t.FontType=t.AnnotationType=t.AnnotationStateModelType=t.AnnotationReviewState=t.AnnotationReplyType=t.AnnotationMarkedState=t.AnnotationFlag=t.AnnotationFieldFlag=t.AnnotationBorderStyleType=t.UNSUPPORTED_FEATURES=t.VerbosityLevel=t.OPS=t.IDENTITY_MATRIX=t.FONT_IDENTITY_MATRIX=t.BaseException=void 0,r(6);var p=[1,0,0,1,0,0];t.IDENTITY_MATRIX=p;var v=[.001,0,0,.001,0,0];t.FONT_IDENTITY_MATRIX=v;var g={PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048};t.PermissionFlag=g;var y={FILL:0,STROKE:1,FILL_STROKE:2,INVISIBLE:3,FILL_ADD_TO_PATH:4,STROKE_ADD_TO_PATH:5,FILL_STROKE_ADD_TO_PATH:6,ADD_TO_PATH:7,FILL_STROKE_MASK:3,ADD_TO_PATH_FLAG:4};t.TextRenderingMode=y;var m={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3};t.ImageKind=m;var b={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,SOUND:18,MOVIE:19,WIDGET:20,SCREEN:21,PRINTERMARK:22,TRAPNET:23,WATERMARK:24,THREED:25,REDACT:26};t.AnnotationType=b;var _={MARKED:"Marked",REVIEW:"Review"};t.AnnotationStateModelType=_;var w={MARKED:"Marked",UNMARKED:"Unmarked"};t.AnnotationMarkedState=w;var S={ACCEPTED:"Accepted",REJECTED:"Rejected",CANCELLED:"Cancelled",COMPLETED:"Completed",NONE:"None"};t.AnnotationReviewState=S;var A={GROUP:"Group",REPLY:"R"};t.AnnotationReplyType=A;var k={INVISIBLE:1,HIDDEN:2,PRINT:4,NOZOOM:8,NOROTATE:16,NOVIEW:32,READONLY:64,LOCKED:128,TOGGLENOVIEW:256,LOCKEDCONTENTS:512};t.AnnotationFlag=k;var x={READONLY:1,REQUIRED:2,NOEXPORT:4,MULTILINE:4096,PASSWORD:8192,NOTOGGLETOOFF:16384,RADIO:32768,PUSHBUTTON:65536,COMBO:131072,EDIT:262144,SORT:524288,FILESELECT:1048576,MULTISELECT:2097152,DONOTSPELLCHECK:4194304,DONOTSCROLL:8388608,COMB:16777216,RICHTEXT:33554432,RADIOSINUNISON:33554432,COMMITONSELCHANGE:67108864};t.AnnotationFieldFlag=x;var P={SOLID:1,DASHED:2,BEVELED:3,INSET:4,UNDERLINE:5};t.AnnotationBorderStyleType=P;var C={UNKNOWN:"UNKNOWN",FLATE:"FLATE",LZW:"LZW",DCT:"DCT",JPX:"JPX",JBIG:"JBIG",A85:"A85",AHX:"AHX",CCF:"CCF",RLX:"RLX"};t.StreamType=C;var T={UNKNOWN:"UNKNOWN",TYPE1:"TYPE1",TYPE1C:"TYPE1C",CIDFONTTYPE0:"CIDFONTTYPE0",CIDFONTTYPE0C:"CIDFONTTYPE0C",TRUETYPE:"TRUETYPE",CIDFONTTYPE2:"CIDFONTTYPE2",TYPE3:"TYPE3",OPENTYPE:"OPENTYPE",TYPE0:"TYPE0",MMTYPE1:"MMTYPE1"};t.FontType=T;var R={ERRORS:0,WARNINGS:1,INFOS:5};t.VerbosityLevel=R;var E={NONE:0,BINARY:1,STREAM:2};t.CMapCompressionType=E;var L={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotations:78,endAnnotations:79,beginAnnotation:80,endAnnotation:81,paintJpegXObject:82,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91};t.OPS=L;var O={unknown:"unknown",forms:"forms",javaScript:"javaScript",smask:"smask",shadingPattern:"shadingPattern",font:"font",errorTilingPattern:"errorTilingPattern",errorExtGState:"errorExtGState",errorXObject:"errorXObject",errorFontLoadType3:"errorFontLoadType3",errorFontState:"errorFontState",errorFontMissing:"errorFontMissing",errorFontTranslate:"errorFontTranslate",errorColorSpace:"errorColorSpace",errorOperatorList:"errorOperatorList",errorFontToUnicode:"errorFontToUnicode",errorFontLoadNative:"errorFontLoadNative",errorFontGetPath:"errorFontGetPath",errorMarkedContent:"errorMarkedContent"};t.UNSUPPORTED_FEATURES=O;var I={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};t.PasswordResponses=I;var M=R.WARNINGS;function F(e){Number.isInteger(e)&&(M=e)}function N(){return M}function D(e){M>=R.INFOS&&console.log("Info: ".concat(e))}function j(e){M>=R.WARNINGS&&console.log("Warning: ".concat(e))}function U(e){throw new Error(e)}function q(e,t){e||U(t)}function B(e,t){var r;try{if(r=new URL(e),!r.origin||"null"===r.origin)return!1}catch(i){return!1}var n=new URL(t,r);return r.origin===n.origin}function W(e){if(!e)return!1;switch(e.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}function V(e,t){if(!e)return null;try{var r=t?new URL(e,t):new URL(e);if(W(r))return r}catch(n){}return null}function z(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!1}),r}var H=function(){function e(t){this.constructor===e&&U("Cannot initialize BaseException."),this.message=t,this.name=this.constructor.name}return e.prototype=new Error,e.constructor=e,e}();t.BaseException=H;var G=function(e){s(r,e);var t=c(r);function r(e,n){var i;return o(this,r),i=t.call(this,e),i.code=n,i}return r}(H);t.PasswordException=G;var Y=function(e){s(r,e);var t=c(r);function r(e,n){var i;return o(this,r),i=t.call(this,e),i.details=n,i}return r}(H);t.UnknownErrorException=Y;var X=function(e){s(r,e);var t=c(r);function r(){return o(this,r),t.apply(this,arguments)}return r}(H);t.InvalidPDFException=X;var Q=function(e){s(r,e);var t=c(r);function r(){return o(this,r),t.apply(this,arguments)}return r}(H);t.MissingPDFException=Q;var K=function(e){s(r,e);var t=c(r);function r(e,n){var i;return o(this,r),i=t.call(this,e),i.status=n,i}return r}(H);t.UnexpectedResponseException=K;var J=function(e){s(r,e);var t=c(r);function r(){return o(this,r),t.apply(this,arguments)}return r}(H);t.FormatError=J;var $=function(e){s(r,e);var t=c(r);function r(){return o(this,r),t.apply(this,arguments)}return r}(H);t.AbortException=$;var Z=/\x00/g;function ee(e){return"string"!==typeof e?(j("The argument for removeNullCharacters must be a string."),e):e.replace(Z,"")}function te(e){q(null!==e&&"object"===a(e)&&void 0!==e.length,"Invalid argument for bytesToString");var t=e.length,r=8192;if(t<r)return String.fromCharCode.apply(null,e);for(var n=[],i=0;i<t;i+=r){var o=Math.min(i+r,t),s=e.subarray(i,o);n.push(String.fromCharCode.apply(null,s))}return n.join("")}function re(e){q("string"===typeof e,"Invalid argument for stringToBytes");for(var t=e.length,r=new Uint8Array(t),n=0;n<t;++n)r[n]=255&e.charCodeAt(n);return r}function ne(e){return void 0!==e.length?e.length:(q(void 0!==e.byteLength,"arrayByteLength - invalid argument."),e.byteLength)}function ie(e){var t=e.length;if(1===t&&e[0]instanceof Uint8Array)return e[0];for(var r=0,n=0;n<t;n++)r+=ne(e[n]);for(var i=0,a=new Uint8Array(r),o=0;o<t;o++){var s=e[o];s instanceof Uint8Array||(s="string"===typeof s?re(s):new Uint8Array(s));var u=s.byteLength;a.set(s,i),i+=u}return a}function ae(e){return String.fromCharCode(e>>24&255,e>>16&255,e>>8&255,255&e)}function oe(){var e=new Uint8Array(4);e[0]=1;var t=new Uint32Array(e.buffer,0,1);return 1===t[0]}var se={get value(){return z(this,"value",oe())}};function ue(){try{return new Function(""),!0}catch(e){return!1}}t.IsLittleEndianCached=se;var ce={get value(){return z(this,"value",ue())}};t.IsEvalSupportedCached=ce;var le=["rgb(",0,",",0,",",0,")"],he=function(){function e(){o(this,e)}return i(e,null,[{key:"makeCssRgb",value:function(e,t,r){return le[1]=e,le[3]=t,le[5]=r,le.join("")}},{key:"transform",value:function(e,t){return[e[0]*t[0]+e[2]*t[1],e[1]*t[0]+e[3]*t[1],e[0]*t[2]+e[2]*t[3],e[1]*t[2]+e[3]*t[3],e[0]*t[4]+e[2]*t[5]+e[4],e[1]*t[4]+e[3]*t[5]+e[5]]}},{key:"applyTransform",value:function(e,t){var r=e[0]*t[0]+e[1]*t[2]+t[4],n=e[0]*t[1]+e[1]*t[3]+t[5];return[r,n]}},{key:"applyInverseTransform",value:function(e,t){var r=t[0]*t[3]-t[1]*t[2],n=(e[0]*t[3]-e[1]*t[2]+t[2]*t[5]-t[4]*t[3])/r,i=(-e[0]*t[1]+e[1]*t[0]+t[4]*t[1]-t[5]*t[0])/r;return[n,i]}},{key:"getAxialAlignedBoundingBox",value:function(t,r){var n=e.applyTransform(t,r),i=e.applyTransform(t.slice(2,4),r),a=e.applyTransform([t[0],t[3]],r),o=e.applyTransform([t[2],t[1]],r);return[Math.min(n[0],i[0],a[0],o[0]),Math.min(n[1],i[1],a[1],o[1]),Math.max(n[0],i[0],a[0],o[0]),Math.max(n[1],i[1],a[1],o[1])]}},{key:"inverseTransform",value:function(e){var t=e[0]*e[3]-e[1]*e[2];return[e[3]/t,-e[1]/t,-e[2]/t,e[0]/t,(e[2]*e[5]-e[4]*e[3])/t,(e[4]*e[1]-e[5]*e[0])/t]}},{key:"apply3dTransform",value:function(e,t){return[e[0]*t[0]+e[1]*t[1]+e[2]*t[2],e[3]*t[0]+e[4]*t[1]+e[5]*t[2],e[6]*t[0]+e[7]*t[1]+e[8]*t[2]]}},{key:"singularValueDecompose2dScale",value:function(e){var t=[e[0],e[2],e[1],e[3]],r=e[0]*t[0]+e[1]*t[2],n=e[0]*t[1]+e[1]*t[3],i=e[2]*t[0]+e[3]*t[2],a=e[2]*t[1]+e[3]*t[3],o=(r+a)/2,s=Math.sqrt((r+a)*(r+a)-4*(r*a-i*n))/2,u=o+s||1,c=o-s||1;return[Math.sqrt(u),Math.sqrt(c)]}},{key:"normalizeRect",value:function(e){var t=e.slice(0);return e[0]>e[2]&&(t[0]=e[2],t[2]=e[0]),e[1]>e[3]&&(t[1]=e[3],t[3]=e[1]),t}},{key:"intersect",value:function(t,r){function n(e,t){return e-t}var i=[t[0],t[2],r[0],r[2]].sort(n),a=[t[1],t[3],r[1],r[3]].sort(n),o=[];return t=e.normalizeRect(t),r=e.normalizeRect(r),i[0]===t[0]&&i[1]===r[0]||i[0]===r[0]&&i[1]===t[0]?(o[0]=i[1],o[2]=i[2],a[0]===t[1]&&a[1]===r[1]||a[0]===r[1]&&a[1]===t[1]?(o[1]=a[1],o[3]=a[2],o):null):null}}]),e}();t.Util=he;var fe=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,728,711,710,729,733,731,730,732,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,8226,8224,8225,8230,8212,8211,402,8260,8249,8250,8722,8240,8222,8220,8221,8216,8217,8218,8482,64257,64258,321,338,352,376,381,305,322,339,353,382,0,8364];function de(e){var t=e.length,r=[];if("þ"===e[0]&&"ÿ"===e[1])for(var n=2;n<t;n+=2)r.push(String.fromCharCode(e.charCodeAt(n)<<8|e.charCodeAt(n+1)));else if("ÿ"===e[0]&&"þ"===e[1])for(var i=2;i<t;i+=2)r.push(String.fromCharCode(e.charCodeAt(i+1)<<8|e.charCodeAt(i)));else for(var a=0;a<t;++a){var o=fe[e.charCodeAt(a)];r.push(o?String.fromCharCode(o):e.charAt(a))}return r.join("")}function pe(e){return e.replace(/([\(\)\\])/g,"\\$1")}function ve(e){return decodeURIComponent(escape(e))}function ge(e){return unescape(encodeURIComponent(e))}function ye(e){return"boolean"===typeof e}function me(e){return"number"===typeof e}function be(e){return"string"===typeof e}function _e(e){return"object"===a(e)&&null!==e&&void 0!==e.byteLength}function we(e,t){return e.length===t.length&&e.every((function(e,r){return e===t[r]}))}function Se(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Date(Date.now()),t=[e.getUTCFullYear().toString(),(e.getUTCMonth()+1).toString().padStart(2,"0"),(e.getUTCDate()+1).toString().padStart(2,"0"),e.getUTCHours().toString().padStart(2,"0"),e.getUTCMinutes().toString().padStart(2,"0"),e.getUTCSeconds().toString().padStart(2,"0")];return t.join("")}function Ae(){var e=Object.create(null),t=!1;return Object.defineProperty(e,"settled",{get:function(){return t}}),e.promise=new Promise((function(r,n){e.resolve=function(e){t=!0,r(e)},e.reject=function(e){t=!0,n(e)}})),e}var ke=function(){var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";return function(t,r){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(!n&&URL.createObjectURL){var i=new Blob([t],{type:r});return URL.createObjectURL(i)}for(var a="data:".concat(r,";base64,"),o=0,s=t.length;o<s;o+=3){var u=255&t[o],c=255&t[o+1],l=255&t[o+2],h=u>>2,f=(3&u)<<4|c>>4,d=o+1<s?(15&c)<<2|l>>6:64,p=o+2<s?63&l:64;a+=e[h]+e[f]+e[d]+e[p]}return a}}();t.createObjectURL=ke},function(e,t,r){"use strict";var n=r(7);function i(e){return i="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}if("undefined"===typeof globalThis||!globalThis._pdfjsCompatibilityChecked){"undefined"!==typeof globalThis&&globalThis.Math===Math||(globalThis=r(8)),globalThis._pdfjsCompatibilityChecked=!0;var a="object"===("undefined"===typeof window?"undefined":i(window))&&"object"===("undefined"===typeof document?"undefined":i(document)),o="undefined"!==typeof navigator&&navigator.userAgent||"",s=/Trident/.test(o);(function(){!globalThis.btoa&&n.isNodeJS&&(globalThis.btoa=function(e){return Buffer.from(e,"binary").toString("base64")})})(),function(){!globalThis.atob&&n.isNodeJS&&(globalThis.atob=function(e){return Buffer.from(e,"base64").toString("binary")})}(),function(){a&&"undefined"===typeof Element.prototype.remove&&(Element.prototype.remove=function(){this.parentNode&&this.parentNode.removeChild(this)})}(),function(){if(a&&!n.isNodeJS){var e=document.createElement("div");if(e.classList.add("testOne","testTwo"),!0!==e.classList.contains("testOne")||!0!==e.classList.contains("testTwo")){var t=DOMTokenList.prototype.add,r=DOMTokenList.prototype.remove;DOMTokenList.prototype.add=function(){for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];for(var i=0,a=r;i<a.length;i++){var o=a[i];t.call(this,o)}},DOMTokenList.prototype.remove=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];for(var i=0,a=t;i<a.length;i++){var o=a[i];r.call(this,o)}}}}}(),function(){if(a&&!n.isNodeJS){var e=document.createElement("div");!1!==e.classList.toggle("test",0)&&(DOMTokenList.prototype.toggle=function(e){var t=arguments.length>1?!!arguments[1]:!this.contains(e);return this[t?"add":"remove"](e),t})}}(),function(){if(a&&s){var e=window.history.pushState,t=window.history.replaceState;window.history.pushState=function(t,r,n){var i=void 0===n?[t,r]:[t,r,n];e.apply(this,i)},window.history.replaceState=function(e,r,n){var i=void 0===n?[e,r]:[e,r,n];t.apply(this,i)}}}(),function(){String.prototype.startsWith||r(53)}(),function(){String.prototype.endsWith||r(64)}(),function(){String.prototype.includes||r(66)}(),function(){Array.prototype.includes||r(68)}(),function(){Array.from||r(76)}(),function(){Object.assign||r(98)}(),function(){Object.fromEntries||r(101)}(),function(){Math.log2||(Math.log2=r(105))}(),function(){Number.isNaN||(Number.isNaN=r(107))}(),function(){Number.isInteger||(Number.isInteger=r(109))}(),function(){Uint8Array.prototype.slice||r(112)}(),function(){globalThis.Promise&&globalThis.Promise.allSettled||(globalThis.Promise=r(117))}(),function(){globalThis.URL=r(138)}(),function(){var e=!1;if("undefined"!==typeof ReadableStream)try{new ReadableStream({start:function(e){e.close()}}),e=!0}catch(t){}e||(globalThis.ReadableStream=r(145).ReadableStream)}(),function(){globalThis.Map&&globalThis.Map.prototype.entries||(globalThis.Map=r(146))}(),function(){globalThis.Set&&globalThis.Set.prototype.entries||(globalThis.Set=r(153))}(),function(){globalThis.WeakMap||(globalThis.WeakMap=r(155))}(),function(){globalThis.WeakSet||(globalThis.WeakSet=r(161))}(),function(){String.prototype.codePointAt||r(163)}(),function(){String.fromCodePoint||(String.fromCodePoint=r(165))}(),function(){globalThis.Symbol||r(167)}(),function(){String.prototype.padStart||r(190)}(),function(){String.prototype.padEnd||r(195)}(),function(){Object.values||(Object.values=r(197))}(),function(){Object.entries||(Object.entries=r(200))}()}},function(e,t,r){"use strict";function n(e){return n="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.isNodeJS=void 0;var i="object"===("undefined"===typeof process?"undefined":n(process))&&process+""==="[object process]"&&!process.versions.nw&&!(process.versions.electron&&process.type&&"browser"!==process.type);t.isNodeJS=i},function(e,t,r){r(9),e.exports=r(11)},function(e,t,r){var n=r(10),i=r(11);n({global:!0},{globalThis:i})},function(e,t,r){var n=r(11),i=r(12).f,a=r(26),o=r(29),s=r(30),u=r(40),c=r(52);e.exports=function(e,t){var r,l,h,f,d,p,v=e.target,g=e.global,y=e.stat;if(l=g?n:y?n[v]||s(v,{}):(n[v]||{}).prototype,l)for(h in t){if(d=t[h],e.noTargetGet?(p=i(l,h),f=p&&p.value):f=l[h],r=c(g?h:v+(y?".":"#")+h,e.forced),!r&&void 0!==f){if(typeof d===typeof f)continue;u(d,f)}(e.sham||f&&f.sham)&&a(d,"sham",!0),o(l,h,d,e)}}},function(e,t){var r=function(e){return e&&e.Math==Math&&e};e.exports=r("object"==typeof globalThis&&globalThis)||r("object"==typeof window&&window)||r("object"==typeof self&&self)||r("object"==typeof global&&global)||Function("return this")()},function(e,t,r){var n=r(13),i=r(15),a=r(16),o=r(17),s=r(21),u=r(23),c=r(24),l=Object.getOwnPropertyDescriptor;t.f=n?l:function(e,t){if(e=o(e),t=s(t,!0),c)try{return l(e,t)}catch(r){}if(u(e,t))return a(!i.f.call(e,t),e[t])}},function(e,t,r){var n=r(14);e.exports=!n((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]}))},function(e,t){e.exports=function(e){try{return!!e()}catch(t){return!0}}},function(e,t,r){"use strict";var n={}.propertyIsEnumerable,i=Object.getOwnPropertyDescriptor,a=i&&!n.call({1:2},1);t.f=a?function(e){var t=i(this,e);return!!t&&t.enumerable}:n},function(e,t){e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},function(e,t,r){var n=r(18),i=r(20);e.exports=function(e){return n(i(e))}},function(e,t,r){var n=r(14),i=r(19),a="".split;e.exports=n((function(){return!Object("z").propertyIsEnumerable(0)}))?function(e){return"String"==i(e)?a.call(e,""):Object(e)}:Object},function(e,t){var r={}.toString;e.exports=function(e){return r.call(e).slice(8,-1)}},function(e,t){e.exports=function(e){if(void 0==e)throw TypeError("Can't call method on "+e);return e}},function(e,t,r){var n=r(22);e.exports=function(e,t){if(!n(e))return e;var r,i;if(t&&"function"==typeof(r=e.toString)&&!n(i=r.call(e)))return i;if("function"==typeof(r=e.valueOf)&&!n(i=r.call(e)))return i;if(!t&&"function"==typeof(r=e.toString)&&!n(i=r.call(e)))return i;throw TypeError("Can't convert object to primitive value")}},function(e,t){e.exports=function(e){return"object"===typeof e?null!==e:"function"===typeof e}},function(e,t){var r={}.hasOwnProperty;e.exports=function(e,t){return r.call(e,t)}},function(e,t,r){var n=r(13),i=r(14),a=r(25);e.exports=!n&&!i((function(){return 7!=Object.defineProperty(a("div"),"a",{get:function(){return 7}}).a}))},function(e,t,r){var n=r(11),i=r(22),a=n.document,o=i(a)&&i(a.createElement);e.exports=function(e){return o?a.createElement(e):{}}},function(e,t,r){var n=r(13),i=r(27),a=r(16);e.exports=n?function(e,t,r){return i.f(e,t,a(1,r))}:function(e,t,r){return e[t]=r,e}},function(e,t,r){var n=r(13),i=r(24),a=r(28),o=r(21),s=Object.defineProperty;t.f=n?s:function(e,t,r){if(a(e),t=o(t,!0),a(r),i)try{return s(e,t,r)}catch(n){}if("get"in r||"set"in r)throw TypeError("Accessors not supported");return"value"in r&&(e[t]=r.value),e}},function(e,t,r){var n=r(22);e.exports=function(e){if(!n(e))throw TypeError(String(e)+" is not an object");return e}},function(e,t,r){var n=r(11),i=r(26),a=r(23),o=r(30),s=r(31),u=r(33),c=u.get,l=u.enforce,h=String(String).split("String");(e.exports=function(e,t,r,s){var u=!!s&&!!s.unsafe,c=!!s&&!!s.enumerable,f=!!s&&!!s.noTargetGet;"function"==typeof r&&("string"!=typeof t||a(r,"name")||i(r,"name",t),l(r).source=h.join("string"==typeof t?t:"")),e!==n?(u?!f&&e[t]&&(c=!0):delete e[t],c?e[t]=r:i(e,t,r)):c?e[t]=r:o(t,r)})(Function.prototype,"toString",(function(){return"function"==typeof this&&c(this).source||s(this)}))},function(e,t,r){var n=r(11),i=r(26);e.exports=function(e,t){try{i(n,e,t)}catch(r){n[e]=t}return t}},function(e,t,r){var n=r(32),i=Function.toString;"function"!=typeof n.inspectSource&&(n.inspectSource=function(e){return i.call(e)}),e.exports=n.inspectSource},function(e,t,r){var n=r(11),i=r(30),a="__core-js_shared__",o=n[a]||i(a,{});e.exports=o},function(e,t,r){var n,i,a,o=r(34),s=r(11),u=r(22),c=r(26),l=r(23),h=r(35),f=r(39),d=s.WeakMap,p=function(e){return a(e)?i(e):n(e,{})},v=function(e){return function(t){var r;if(!u(t)||(r=i(t)).type!==e)throw TypeError("Incompatible receiver, "+e+" required");return r}};if(o){var g=new d,y=g.get,m=g.has,b=g.set;n=function(e,t){return b.call(g,e,t),t},i=function(e){return y.call(g,e)||{}},a=function(e){return m.call(g,e)}}else{var _=h("state");f[_]=!0,n=function(e,t){return c(e,_,t),t},i=function(e){return l(e,_)?e[_]:{}},a=function(e){return l(e,_)}}e.exports={set:n,get:i,has:a,enforce:p,getterFor:v}},function(e,t,r){var n=r(11),i=r(31),a=n.WeakMap;e.exports="function"===typeof a&&/native code/.test(i(a))},function(e,t,r){var n=r(36),i=r(38),a=n("keys");e.exports=function(e){return a[e]||(a[e]=i(e))}},function(e,t,r){var n=r(37),i=r(32);(e.exports=function(e,t){return i[e]||(i[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.6.5",mode:n?"pure":"global",copyright:"© 2020 Denis Pushkarev (zloirock.ru)"})},function(e,t){e.exports=!1},function(e,t){var r=0,n=Math.random();e.exports=function(e){return"Symbol("+String(void 0===e?"":e)+")_"+(++r+n).toString(36)}},function(e,t){e.exports={}},function(e,t,r){var n=r(23),i=r(41),a=r(12),o=r(27);e.exports=function(e,t){for(var r=i(t),s=o.f,u=a.f,c=0;c<r.length;c++){var l=r[c];n(e,l)||s(e,l,u(t,l))}}},function(e,t,r){var n=r(42),i=r(44),a=r(51),o=r(28);e.exports=n("Reflect","ownKeys")||function(e){var t=i.f(o(e)),r=a.f;return r?t.concat(r(e)):t}},function(e,t,r){var n=r(43),i=r(11),a=function(e){return"function"==typeof e?e:void 0};e.exports=function(e,t){return arguments.length<2?a(n[e])||a(i[e]):n[e]&&n[e][t]||i[e]&&i[e][t]}},function(e,t,r){var n=r(11);e.exports=n},function(e,t,r){var n=r(45),i=r(50),a=i.concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return n(e,a)}},function(e,t,r){var n=r(23),i=r(17),a=r(46).indexOf,o=r(39);e.exports=function(e,t){var r,s=i(e),u=0,c=[];for(r in s)!n(o,r)&&n(s,r)&&c.push(r);while(t.length>u)n(s,r=t[u++])&&(~a(c,r)||c.push(r));return c}},function(e,t,r){var n=r(17),i=r(47),a=r(49),o=function(e){return function(t,r,o){var s,u=n(t),c=i(u.length),l=a(o,c);if(e&&r!=r){while(c>l)if(s=u[l++],s!=s)return!0}else for(;c>l;l++)if((e||l in u)&&u[l]===r)return e||l||0;return!e&&-1}};e.exports={includes:o(!0),indexOf:o(!1)}},function(e,t,r){var n=r(48),i=Math.min;e.exports=function(e){return e>0?i(n(e),9007199254740991):0}},function(e,t){var r=Math.ceil,n=Math.floor;e.exports=function(e){return isNaN(e=+e)?0:(e>0?n:r)(e)}},function(e,t,r){var n=r(48),i=Math.max,a=Math.min;e.exports=function(e,t){var r=n(e);return r<0?i(r+t,0):a(r,t)}},function(e,t){e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},function(e,t){t.f=Object.getOwnPropertySymbols},function(e,t,r){var n=r(14),i=/#|\.prototype\./,a=function(e,t){var r=s[o(e)];return r==c||r!=u&&("function"==typeof t?n(t):!!t)},o=a.normalize=function(e){return String(e).replace(i,".").toLowerCase()},s=a.data={},u=a.NATIVE="N",c=a.POLYFILL="P";e.exports=a},function(e,t,r){r(54);var n=r(61);e.exports=n("String","startsWith")},function(e,t,r){"use strict";var n=r(10),i=r(12).f,a=r(47),o=r(55),s=r(20),u=r(60),c=r(37),l="".startsWith,h=Math.min,f=u("startsWith"),d=!c&&!f&&!!function(){var e=i(String.prototype,"startsWith");return e&&!e.writable}();n({target:"String",proto:!0,forced:!d&&!f},{startsWith:function(e){var t=String(s(this));o(e);var r=a(h(arguments.length>1?arguments[1]:void 0,t.length)),n=String(e);return l?l.call(t,n,r):t.slice(r,r+n.length)===n}})},function(e,t,r){var n=r(56);e.exports=function(e){if(n(e))throw TypeError("The method doesn't accept regular expressions");return e}},function(e,t,r){var n=r(22),i=r(19),a=r(57),o=a("match");e.exports=function(e){var t;return n(e)&&(void 0!==(t=e[o])?!!t:"RegExp"==i(e))}},function(e,t,r){var n=r(11),i=r(36),a=r(23),o=r(38),s=r(58),u=r(59),c=i("wks"),l=n.Symbol,h=u?l:l&&l.withoutSetter||o;e.exports=function(e){return a(c,e)||(s&&a(l,e)?c[e]=l[e]:c[e]=h("Symbol."+e)),c[e]}},function(e,t,r){var n=r(14);e.exports=!!Object.getOwnPropertySymbols&&!n((function(){return!String(Symbol())}))},function(e,t,r){var n=r(58);e.exports=n&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},function(e,t,r){var n=r(57),i=n("match");e.exports=function(e){var t=/./;try{"/./"[e](t)}catch(r){try{return t[i]=!1,"/./"[e](t)}catch(n){}}return!1}},function(e,t,r){var n=r(11),i=r(62),a=Function.call;e.exports=function(e,t,r){return i(a,n[e].prototype[t],r)}},function(e,t,r){var n=r(63);e.exports=function(e,t,r){if(n(e),void 0===t)return e;switch(r){case 0:return function(){return e.call(t)};case 1:return function(r){return e.call(t,r)};case 2:return function(r,n){return e.call(t,r,n)};case 3:return function(r,n,i){return e.call(t,r,n,i)}}return function(){return e.apply(t,arguments)}}},function(e,t){e.exports=function(e){if("function"!=typeof e)throw TypeError(String(e)+" is not a function");return e}},function(e,t,r){r(65);var n=r(61);e.exports=n("String","endsWith")},function(e,t,r){"use strict";var n=r(10),i=r(12).f,a=r(47),o=r(55),s=r(20),u=r(60),c=r(37),l="".endsWith,h=Math.min,f=u("endsWith"),d=!c&&!f&&!!function(){var e=i(String.prototype,"endsWith");return e&&!e.writable}();n({target:"String",proto:!0,forced:!d&&!f},{endsWith:function(e){var t=String(s(this));o(e);var r=arguments.length>1?arguments[1]:void 0,n=a(t.length),i=void 0===r?n:h(a(r),n),u=String(e);return l?l.call(t,u,i):t.slice(i-u.length,i)===u}})},function(e,t,r){r(67);var n=r(61);e.exports=n("String","includes")},function(e,t,r){"use strict";var n=r(10),i=r(55),a=r(20),o=r(60);n({target:"String",proto:!0,forced:!o("includes")},{includes:function(e){return!!~String(a(this)).indexOf(i(e),arguments.length>1?arguments[1]:void 0)}})},function(e,t,r){r(69);var n=r(61);e.exports=n("Array","includes")},function(e,t,r){"use strict";var n=r(10),i=r(46).includes,a=r(70),o=r(75),s=o("indexOf",{ACCESSORS:!0,1:0});n({target:"Array",proto:!0,forced:!s},{includes:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0)}}),a("includes")},function(e,t,r){var n=r(57),i=r(71),a=r(27),o=n("unscopables"),s=Array.prototype;void 0==s[o]&&a.f(s,o,{configurable:!0,value:i(null)}),e.exports=function(e){s[o][e]=!0}},function(e,t,r){var n,i=r(28),a=r(72),o=r(50),s=r(39),u=r(74),c=r(25),l=r(35),h=">",f="<",d="prototype",p="script",v=l("IE_PROTO"),g=function(){},y=function(e){return f+p+h+e+f+"/"+p+h},m=function(e){e.write(y("")),e.close();var t=e.parentWindow.Object;return e=null,t},b=function(){var e,t=c("iframe"),r="java"+p+":";return t.style.display="none",u.appendChild(t),t.src=String(r),e=t.contentWindow.document,e.open(),e.write(y("document.F=Object")),e.close(),e.F},_=function(){try{n=document.domain&&new ActiveXObject("htmlfile")}catch(t){}_=n?m(n):b();var e=o.length;while(e--)delete _[d][o[e]];return _()};s[v]=!0,e.exports=Object.create||function(e,t){var r;return null!==e?(g[d]=i(e),r=new g,g[d]=null,r[v]=e):r=_(),void 0===t?r:a(r,t)}},function(e,t,r){var n=r(13),i=r(27),a=r(28),o=r(73);e.exports=n?Object.defineProperties:function(e,t){a(e);var r,n=o(t),s=n.length,u=0;while(s>u)i.f(e,r=n[u++],t[r]);return e}},function(e,t,r){var n=r(45),i=r(50);e.exports=Object.keys||function(e){return n(e,i)}},function(e,t,r){var n=r(42);e.exports=n("document","documentElement")},function(e,t,r){var n=r(13),i=r(14),a=r(23),o=Object.defineProperty,s={},u=function(e){throw e};e.exports=function(e,t){if(a(s,e))return s[e];t||(t={});var r=[][e],c=!!a(t,"ACCESSORS")&&t.ACCESSORS,l=a(t,0)?t[0]:u,h=a(t,1)?t[1]:void 0;return s[e]=!!r&&!i((function(){if(c&&!n)return!0;var e={length:-1};c?o(e,1,{enumerable:!0,get:u}):e[1]=1,r.call(e,l,h)}))}},function(e,t,r){r(77),r(89);var n=r(43);e.exports=n.Array.from},function(e,t,r){"use strict";var n=r(78).charAt,i=r(33),a=r(79),o="String Iterator",s=i.set,u=i.getterFor(o);a(String,"String",(function(e){s(this,{type:o,string:String(e),index:0})}),(function(){var e,t=u(this),r=t.string,i=t.index;return i>=r.length?{value:void 0,done:!0}:(e=n(r,i),t.index+=e.length,{value:e,done:!1})}))},function(e,t,r){var n=r(48),i=r(20),a=function(e){return function(t,r){var a,o,s=String(i(t)),u=n(r),c=s.length;return u<0||u>=c?e?"":void 0:(a=s.charCodeAt(u),a<55296||a>56319||u+1===c||(o=s.charCodeAt(u+1))<56320||o>57343?e?s.charAt(u):a:e?s.slice(u,u+2):o-56320+(a-55296<<10)+65536)}};e.exports={codeAt:a(!1),charAt:a(!0)}},function(e,t,r){"use strict";var n=r(10),i=r(80),a=r(82),o=r(87),s=r(85),u=r(26),c=r(29),l=r(57),h=r(37),f=r(86),d=r(81),p=d.IteratorPrototype,v=d.BUGGY_SAFARI_ITERATORS,g=l("iterator"),y="keys",m="values",b="entries",_=function(){return this};e.exports=function(e,t,r,l,d,w,S){i(r,t,l);var A,k,x,P=function(e){if(e===d&&L)return L;if(!v&&e in R)return R[e];switch(e){case y:return function(){return new r(this,e)};case m:return function(){return new r(this,e)};case b:return function(){return new r(this,e)}}return function(){return new r(this)}},C=t+" Iterator",T=!1,R=e.prototype,E=R[g]||R["@@iterator"]||d&&R[d],L=!v&&E||P(d),O="Array"==t&&R.entries||E;if(O&&(A=a(O.call(new e)),p!==Object.prototype&&A.next&&(h||a(A)===p||(o?o(A,p):"function"!=typeof A[g]&&u(A,g,_)),s(A,C,!0,!0),h&&(f[C]=_))),d==m&&E&&E.name!==m&&(T=!0,L=function(){return E.call(this)}),h&&!S||R[g]===L||u(R,g,L),f[t]=L,d)if(k={values:P(m),keys:w?L:P(y),entries:P(b)},S)for(x in k)(v||T||!(x in R))&&c(R,x,k[x]);else n({target:t,proto:!0,forced:v||T},k);return k}},function(e,t,r){"use strict";var n=r(81).IteratorPrototype,i=r(71),a=r(16),o=r(85),s=r(86),u=function(){return this};e.exports=function(e,t,r){var c=t+" Iterator";return e.prototype=i(n,{next:a(1,r)}),o(e,c,!1,!0),s[c]=u,e}},function(e,t,r){"use strict";var n,i,a,o=r(82),s=r(26),u=r(23),c=r(57),l=r(37),h=c("iterator"),f=!1,d=function(){return this};[].keys&&(a=[].keys(),"next"in a?(i=o(o(a)),i!==Object.prototype&&(n=i)):f=!0),void 0==n&&(n={}),l||u(n,h)||s(n,h,d),e.exports={IteratorPrototype:n,BUGGY_SAFARI_ITERATORS:f}},function(e,t,r){var n=r(23),i=r(83),a=r(35),o=r(84),s=a("IE_PROTO"),u=Object.prototype;e.exports=o?Object.getPrototypeOf:function(e){return e=i(e),n(e,s)?e[s]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?u:null}},function(e,t,r){var n=r(20);e.exports=function(e){return Object(n(e))}},function(e,t,r){var n=r(14);e.exports=!n((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype}))},function(e,t,r){var n=r(27).f,i=r(23),a=r(57),o=a("toStringTag");e.exports=function(e,t,r){e&&!i(e=r?e:e.prototype,o)&&n(e,o,{configurable:!0,value:t})}},function(e,t){e.exports={}},function(e,t,r){var n=r(28),i=r(88);e.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,r={};try{e=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set,e.call(r,[]),t=r instanceof Array}catch(a){}return function(r,a){return n(r),i(a),t?e.call(r,a):r.__proto__=a,r}}():void 0)},function(e,t,r){var n=r(22);e.exports=function(e){if(!n(e)&&null!==e)throw TypeError("Can't set "+String(e)+" as a prototype");return e}},function(e,t,r){var n=r(10),i=r(90),a=r(97),o=!a((function(e){Array.from(e)}));n({target:"Array",stat:!0,forced:o},{from:i})},function(e,t,r){"use strict";var n=r(62),i=r(83),a=r(91),o=r(92),s=r(47),u=r(93),c=r(94);e.exports=function(e){var t,r,l,h,f,d,p=i(e),v="function"==typeof this?this:Array,g=arguments.length,y=g>1?arguments[1]:void 0,m=void 0!==y,b=c(p),_=0;if(m&&(y=n(y,g>2?arguments[2]:void 0,2)),void 0==b||v==Array&&o(b))for(t=s(p.length),r=new v(t);t>_;_++)d=m?y(p[_],_):p[_],u(r,_,d);else for(h=b.call(p),f=h.next,r=new v;!(l=f.call(h)).done;_++)d=m?a(h,y,[l.value,_],!0):l.value,u(r,_,d);return r.length=_,r}},function(e,t,r){var n=r(28);e.exports=function(e,t,r,i){try{return i?t(n(r)[0],r[1]):t(r)}catch(o){var a=e["return"];throw void 0!==a&&n(a.call(e)),o}}},function(e,t,r){var n=r(57),i=r(86),a=n("iterator"),o=Array.prototype;e.exports=function(e){return void 0!==e&&(i.Array===e||o[a]===e)}},function(e,t,r){"use strict";var n=r(21),i=r(27),a=r(16);e.exports=function(e,t,r){var o=n(t);o in e?i.f(e,o,a(0,r)):e[o]=r}},function(e,t,r){var n=r(95),i=r(86),a=r(57),o=a("iterator");e.exports=function(e){if(void 0!=e)return e[o]||e["@@iterator"]||i[n(e)]}},function(e,t,r){var n=r(96),i=r(19),a=r(57),o=a("toStringTag"),s="Arguments"==i(function(){return arguments}()),u=function(e,t){try{return e[t]}catch(r){}};e.exports=n?i:function(e){var t,r,n;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(r=u(t=Object(e),o))?r:s?i(t):"Object"==(n=i(t))&&"function"==typeof t.callee?"Arguments":n}},function(e,t,r){var n=r(57),i=n("toStringTag"),a={};a[i]="z",e.exports="[object z]"===String(a)},function(e,t,r){var n=r(57),i=n("iterator"),a=!1;try{var o=0,s={next:function(){return{done:!!o++}},return:function(){a=!0}};s[i]=function(){return this},Array.from(s,(function(){throw 2}))}catch(u){}e.exports=function(e,t){if(!t&&!a)return!1;var r=!1;try{var n={};n[i]=function(){return{next:function(){return{done:r=!0}}}},e(n)}catch(u){}return r}},function(e,t,r){r(99);var n=r(43);e.exports=n.Object.assign},function(e,t,r){var n=r(10),i=r(100);n({target:"Object",stat:!0,forced:Object.assign!==i},{assign:i})},function(e,t,r){"use strict";var n=r(13),i=r(14),a=r(73),o=r(51),s=r(15),u=r(83),c=r(18),l=Object.assign,h=Object.defineProperty;e.exports=!l||i((function(){if(n&&1!==l({b:1},l(h({},"a",{enumerable:!0,get:function(){h(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var e={},t={},r=Symbol(),i="abcdefghijklmnopqrst";return e[r]=7,i.split("").forEach((function(e){t[e]=e})),7!=l({},e)[r]||a(l({},t)).join("")!=i}))?function(e,t){var r=u(e),i=arguments.length,l=1,h=o.f,f=s.f;while(i>l){var d,p=c(arguments[l++]),v=h?a(p).concat(h(p)):a(p),g=v.length,y=0;while(g>y)d=v[y++],n&&!f.call(p,d)||(r[d]=p[d])}return r}:l},function(e,t,r){r(102),r(103);var n=r(43);e.exports=n.Object.fromEntries},function(e,t,r){"use strict";var n=r(17),i=r(70),a=r(86),o=r(33),s=r(79),u="Array Iterator",c=o.set,l=o.getterFor(u);e.exports=s(Array,"Array",(function(e,t){c(this,{type:u,target:n(e),index:0,kind:t})}),(function(){var e=l(this),t=e.target,r=e.kind,n=e.index++;return!t||n>=t.length?(e.target=void 0,{value:void 0,done:!0}):"keys"==r?{value:n,done:!1}:"values"==r?{value:t[n],done:!1}:{value:[n,t[n]],done:!1}}),"values"),a.Arguments=a.Array,i("keys"),i("values"),i("entries")},function(e,t,r){var n=r(10),i=r(104),a=r(93);n({target:"Object",stat:!0},{fromEntries:function(e){var t={};return i(e,(function(e,r){a(t,e,r)}),void 0,!0),t}})},function(e,t,r){var n=r(28),i=r(92),a=r(47),o=r(62),s=r(94),u=r(91),c=function(e,t){this.stopped=e,this.result=t},l=e.exports=function(e,t,r,l,h){var f,d,p,v,g,y,m,b=o(t,r,l?2:1);if(h)f=e;else{if(d=s(e),"function"!=typeof d)throw TypeError("Target is not iterable");if(i(d)){for(p=0,v=a(e.length);v>p;p++)if(g=l?b(n(m=e[p])[0],m[1]):b(e[p]),g&&g instanceof c)return g;return new c(!1)}f=d.call(e)}y=f.next;while(!(m=y.call(f)).done)if(g=u(f,b,m.value,l),"object"==typeof g&&g&&g instanceof c)return g;return new c(!1)};l.stop=function(e){return new c(!0,e)}},function(e,t,r){r(106);var n=r(43);e.exports=n.Math.log2},function(e,t,r){var n=r(10),i=Math.log,a=Math.LN2;n({target:"Math",stat:!0},{log2:function(e){return i(e)/a}})},function(e,t,r){r(108);var n=r(43);e.exports=n.Number.isNaN},function(e,t,r){var n=r(10);n({target:"Number",stat:!0},{isNaN:function(e){return e!=e}})},function(e,t,r){r(110);var n=r(43);e.exports=n.Number.isInteger},function(e,t,r){var n=r(10),i=r(111);n({target:"Number",stat:!0},{isInteger:i})},function(e,t,r){var n=r(22),i=Math.floor;e.exports=function(e){return!n(e)&&isFinite(e)&&i(e)===e}},function(e,t,r){r(113)},function(e,t,r){"use strict";var n=r(114),i=r(116),a=r(14),o=n.aTypedArray,s=n.aTypedArrayConstructor,u=n.exportTypedArrayMethod,c=[].slice,l=a((function(){new Int8Array(1).slice()}));u("slice",(function(e,t){var r=c.call(o(this),e,t),n=i(this,this.constructor),a=0,u=r.length,l=new(s(n))(u);while(u>a)l[a]=r[a++];return l}),l)},function(e,t,r){"use strict";var n,i=r(115),a=r(13),o=r(11),s=r(22),u=r(23),c=r(95),l=r(26),h=r(29),f=r(27).f,d=r(82),p=r(87),v=r(57),g=r(38),y=o.Int8Array,m=y&&y.prototype,b=o.Uint8ClampedArray,_=b&&b.prototype,w=y&&d(y),S=m&&d(m),A=Object.prototype,k=A.isPrototypeOf,x=v("toStringTag"),P=g("TYPED_ARRAY_TAG"),C=i&&!!p&&"Opera"!==c(o.opera),T=!1,R={Int8Array:1,Uint8Array:1,Uint8ClampedArray:1,Int16Array:2,Uint16Array:2,Int32Array:4,Uint32Array:4,Float32Array:4,Float64Array:8},E=function(e){var t=c(e);return"DataView"===t||u(R,t)},L=function(e){return s(e)&&u(R,c(e))},O=function(e){if(L(e))return e;throw TypeError("Target is not a typed array")},I=function(e){if(p){if(k.call(w,e))return e}else for(var t in R)if(u(R,n)){var r=o[t];if(r&&(e===r||k.call(r,e)))return e}throw TypeError("Target is not a typed array constructor")},M=function(e,t,r){if(a){if(r)for(var n in R){var i=o[n];i&&u(i.prototype,e)&&delete i.prototype[e]}S[e]&&!r||h(S,e,r?t:C&&m[e]||t)}},F=function(e,t,r){var n,i;if(a){if(p){if(r)for(n in R)i=o[n],i&&u(i,e)&&delete i[e];if(w[e]&&!r)return;try{return h(w,e,r?t:C&&y[e]||t)}catch(s){}}for(n in R)i=o[n],!i||i[e]&&!r||h(i,e,t)}};for(n in R)o[n]||(C=!1);if((!C||"function"!=typeof w||w===Function.prototype)&&(w=function(){throw TypeError("Incorrect invocation")},C))for(n in R)o[n]&&p(o[n],w);if((!C||!S||S===A)&&(S=w.prototype,C))for(n in R)o[n]&&p(o[n].prototype,S);if(C&&d(_)!==S&&p(_,S),a&&!u(S,x))for(n in T=!0,f(S,x,{get:function(){return s(this)?this[P]:void 0}}),R)o[n]&&l(o[n],P,n);e.exports={NATIVE_ARRAY_BUFFER_VIEWS:C,TYPED_ARRAY_TAG:T&&P,aTypedArray:O,aTypedArrayConstructor:I,exportTypedArrayMethod:M,exportTypedArrayStaticMethod:F,isView:E,isTypedArray:L,TypedArray:w,TypedArrayPrototype:S}},function(e,t){e.exports="undefined"!==typeof ArrayBuffer&&"undefined"!==typeof DataView},function(e,t,r){var n=r(28),i=r(63),a=r(57),o=a("species");e.exports=function(e,t){var r,a=n(e).constructor;return void 0===a||void 0==(r=n(a)[o])?t:i(r)}},function(e,t,r){r(118),r(77),r(120),r(122),r(136),r(137);var n=r(43);e.exports=n.Promise},function(e,t,r){var n=r(96),i=r(29),a=r(119);n||i(Object.prototype,"toString",a,{unsafe:!0})},function(e,t,r){"use strict";var n=r(96),i=r(95);e.exports=n?{}.toString:function(){return"[object "+i(this)+"]"}},function(e,t,r){var n=r(11),i=r(121),a=r(102),o=r(26),s=r(57),u=s("iterator"),c=s("toStringTag"),l=a.values;for(var h in i){var f=n[h],d=f&&f.prototype;if(d){if(d[u]!==l)try{o(d,u,l)}catch(v){d[u]=l}if(d[c]||o(d,c,h),i[h])for(var p in a)if(d[p]!==a[p])try{o(d,p,a[p])}catch(v){d[p]=a[p]}}}},function(e,t){e.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},function(e,t,r){"use strict";var n,i,a,o,s=r(10),u=r(37),c=r(11),l=r(42),h=r(123),f=r(29),d=r(124),p=r(85),v=r(125),g=r(22),y=r(63),m=r(126),b=r(19),_=r(31),w=r(104),S=r(97),A=r(116),k=r(127).set,x=r(130),P=r(131),C=r(133),T=r(132),R=r(134),E=r(33),L=r(52),O=r(57),I=r(135),M=O("species"),F="Promise",N=E.get,D=E.set,j=E.getterFor(F),U=h,q=c.TypeError,B=c.document,W=c.process,V=l("fetch"),z=T.f,H=z,G="process"==b(W),Y=!!(B&&B.createEvent&&c.dispatchEvent),X="unhandledrejection",Q="rejectionhandled",K=0,J=1,$=2,Z=1,ee=2,te=L(F,(function(){var e=_(U)!==String(U);if(!e){if(66===I)return!0;if(!G&&"function"!=typeof PromiseRejectionEvent)return!0}if(u&&!U.prototype["finally"])return!0;if(I>=51&&/native code/.test(U))return!1;var t=U.resolve(1),r=function(e){e((function(){}),(function(){}))},n=t.constructor={};return n[M]=r,!(t.then((function(){}))instanceof r)})),re=te||!S((function(e){U.all(e)["catch"]((function(){}))})),ne=function(e){var t;return!(!g(e)||"function"!=typeof(t=e.then))&&t},ie=function(e,t,r){if(!t.notified){t.notified=!0;var n=t.reactions;x((function(){var i=t.value,a=t.state==J,o=0;while(n.length>o){var s,u,c,l=n[o++],h=a?l.ok:l.fail,f=l.resolve,d=l.reject,p=l.domain;try{h?(a||(t.rejection===ee&&ue(e,t),t.rejection=Z),!0===h?s=i:(p&&p.enter(),s=h(i),p&&(p.exit(),c=!0)),s===l.promise?d(q("Promise-chain cycle")):(u=ne(s))?u.call(s,f,d):f(s)):d(i)}catch(v){p&&!c&&p.exit(),d(v)}}t.reactions=[],t.notified=!1,r&&!t.rejection&&oe(e,t)}))}},ae=function(e,t,r){var n,i;Y?(n=B.createEvent("Event"),n.promise=t,n.reason=r,n.initEvent(e,!1,!0),c.dispatchEvent(n)):n={promise:t,reason:r},(i=c["on"+e])?i(n):e===X&&C("Unhandled promise rejection",r)},oe=function(e,t){k.call(c,(function(){var r,n=t.value,i=se(t);if(i&&(r=R((function(){G?W.emit("unhandledRejection",n,e):ae(X,e,n)})),t.rejection=G||se(t)?ee:Z,r.error))throw r.value}))},se=function(e){return e.rejection!==Z&&!e.parent},ue=function(e,t){k.call(c,(function(){G?W.emit("rejectionHandled",e):ae(Q,e,t.value)}))},ce=function(e,t,r,n){return function(i){e(t,r,i,n)}},le=function(e,t,r,n){t.done||(t.done=!0,n&&(t=n),t.value=r,t.state=$,ie(e,t,!0))},he=function(e,t,r,n){if(!t.done){t.done=!0,n&&(t=n);try{if(e===r)throw q("Promise can't be resolved itself");var i=ne(r);i?x((function(){var n={done:!1};try{i.call(r,ce(he,e,n,t),ce(le,e,n,t))}catch(a){le(e,n,a,t)}})):(t.value=r,t.state=J,ie(e,t,!1))}catch(a){le(e,{done:!1},a,t)}}};te&&(U=function(e){m(this,U,F),y(e),n.call(this);var t=N(this);try{e(ce(he,this,t),ce(le,this,t))}catch(r){le(this,t,r)}},n=function(e){D(this,{type:F,done:!1,notified:!1,parent:!1,reactions:[],rejection:!1,state:K,value:void 0})},n.prototype=d(U.prototype,{then:function(e,t){var r=j(this),n=z(A(this,U));return n.ok="function"!=typeof e||e,n.fail="function"==typeof t&&t,n.domain=G?W.domain:void 0,r.parent=!0,r.reactions.push(n),r.state!=K&&ie(this,r,!1),n.promise},catch:function(e){return this.then(void 0,e)}}),i=function(){var e=new n,t=N(e);this.promise=e,this.resolve=ce(he,e,t),this.reject=ce(le,e,t)},T.f=z=function(e){return e===U||e===a?new i(e):H(e)},u||"function"!=typeof h||(o=h.prototype.then,f(h.prototype,"then",(function(e,t){var r=this;return new U((function(e,t){o.call(r,e,t)})).then(e,t)}),{unsafe:!0}),"function"==typeof V&&s({global:!0,enumerable:!0,forced:!0},{fetch:function(e){return P(U,V.apply(c,arguments))}}))),s({global:!0,wrap:!0,forced:te},{Promise:U}),p(U,F,!1,!0),v(F),a=l(F),s({target:F,stat:!0,forced:te},{reject:function(e){var t=z(this);return t.reject.call(void 0,e),t.promise}}),s({target:F,stat:!0,forced:u||te},{resolve:function(e){return P(u&&this===a?U:this,e)}}),s({target:F,stat:!0,forced:re},{all:function(e){var t=this,r=z(t),n=r.resolve,i=r.reject,a=R((function(){var r=y(t.resolve),a=[],o=0,s=1;w(e,(function(e){var u=o++,c=!1;a.push(void 0),s++,r.call(t,e).then((function(e){c||(c=!0,a[u]=e,--s||n(a))}),i)})),--s||n(a)}));return a.error&&i(a.value),r.promise},race:function(e){var t=this,r=z(t),n=r.reject,i=R((function(){var i=y(t.resolve);w(e,(function(e){i.call(t,e).then(r.resolve,n)}))}));return i.error&&n(i.value),r.promise}})},function(e,t,r){var n=r(11);e.exports=n.Promise},function(e,t,r){var n=r(29);e.exports=function(e,t,r){for(var i in t)n(e,i,t[i],r);return e}},function(e,t,r){"use strict";var n=r(42),i=r(27),a=r(57),o=r(13),s=a("species");e.exports=function(e){var t=n(e),r=i.f;o&&t&&!t[s]&&r(t,s,{configurable:!0,get:function(){return this}})}},function(e,t){e.exports=function(e,t,r){if(!(e instanceof t))throw TypeError("Incorrect "+(r?r+" ":"")+"invocation");return e}},function(e,t,r){var n,i,a,o=r(11),s=r(14),u=r(19),c=r(62),l=r(74),h=r(25),f=r(128),d=o.location,p=o.setImmediate,v=o.clearImmediate,g=o.process,y=o.MessageChannel,m=o.Dispatch,b=0,_={},w="onreadystatechange",S=function(e){if(_.hasOwnProperty(e)){var t=_[e];delete _[e],t()}},A=function(e){return function(){S(e)}},k=function(e){S(e.data)},x=function(e){o.postMessage(e+"",d.protocol+"//"+d.host)};p&&v||(p=function(e){var t=[],r=1;while(arguments.length>r)t.push(arguments[r++]);return _[++b]=function(){("function"==typeof e?e:Function(e)).apply(void 0,t)},n(b),b},v=function(e){delete _[e]},"process"==u(g)?n=function(e){g.nextTick(A(e))}:m&&m.now?n=function(e){m.now(A(e))}:y&&!f?(i=new y,a=i.port2,i.port1.onmessage=k,n=c(a.postMessage,a,1)):!o.addEventListener||"function"!=typeof postMessage||o.importScripts||s(x)||"file:"===d.protocol?n=w in h("script")?function(e){l.appendChild(h("script"))[w]=function(){l.removeChild(this),S(e)}}:function(e){setTimeout(A(e),0)}:(n=x,o.addEventListener("message",k,!1))),e.exports={set:p,clear:v}},function(e,t,r){var n=r(129);e.exports=/(iphone|ipod|ipad).*applewebkit/i.test(n)},function(e,t,r){var n=r(42);e.exports=n("navigator","userAgent")||""},function(e,t,r){var n,i,a,o,s,u,c,l,h=r(11),f=r(12).f,d=r(19),p=r(127).set,v=r(128),g=h.MutationObserver||h.WebKitMutationObserver,y=h.process,m=h.Promise,b="process"==d(y),_=f(h,"queueMicrotask"),w=_&&_.value;w||(n=function(){var e,t;b&&(e=y.domain)&&e.exit();while(i){t=i.fn,i=i.next;try{t()}catch(r){throw i?o():a=void 0,r}}a=void 0,e&&e.enter()},b?o=function(){y.nextTick(n)}:g&&!v?(s=!0,u=document.createTextNode(""),new g(n).observe(u,{characterData:!0}),o=function(){u.data=s=!s}):m&&m.resolve?(c=m.resolve(void 0),l=c.then,o=function(){l.call(c,n)}):o=function(){p.call(h,n)}),e.exports=w||function(e){var t={fn:e,next:void 0};a&&(a.next=t),i||(i=t,o()),a=t}},function(e,t,r){var n=r(28),i=r(22),a=r(132);e.exports=function(e,t){if(n(e),i(t)&&t.constructor===e)return t;var r=a.f(e),o=r.resolve;return o(t),r.promise}},function(e,t,r){"use strict";var n=r(63),i=function(e){var t,r;this.promise=new e((function(e,n){if(void 0!==t||void 0!==r)throw TypeError("Bad Promise constructor");t=e,r=n})),this.resolve=n(t),this.reject=n(r)};e.exports.f=function(e){return new i(e)}},function(e,t,r){var n=r(11);e.exports=function(e,t){var r=n.console;r&&r.error&&(1===arguments.length?r.error(e):r.error(e,t))}},function(e,t){e.exports=function(e){try{return{error:!1,value:e()}}catch(t){return{error:!0,value:t}}}},function(e,t,r){var n,i,a=r(11),o=r(129),s=a.process,u=s&&s.versions,c=u&&u.v8;c?(n=c.split("."),i=n[0]+n[1]):o&&(n=o.match(/Edge\/(\d+)/),(!n||n[1]>=74)&&(n=o.match(/Chrome\/(\d+)/),n&&(i=n[1]))),e.exports=i&&+i},function(e,t,r){"use strict";var n=r(10),i=r(63),a=r(132),o=r(134),s=r(104);n({target:"Promise",stat:!0},{allSettled:function(e){var t=this,r=a.f(t),n=r.resolve,u=r.reject,c=o((function(){var r=i(t.resolve),a=[],o=0,u=1;s(e,(function(e){var i=o++,s=!1;a.push(void 0),u++,r.call(t,e).then((function(e){s||(s=!0,a[i]={status:"fulfilled",value:e},--u||n(a))}),(function(e){s||(s=!0,a[i]={status:"rejected",reason:e},--u||n(a))}))})),--u||n(a)}));return c.error&&u(c.value),r.promise}})},function(e,t,r){"use strict";var n=r(10),i=r(37),a=r(123),o=r(14),s=r(42),u=r(116),c=r(131),l=r(29),h=!!a&&o((function(){a.prototype["finally"].call({then:function(){}},(function(){}))}));n({target:"Promise",proto:!0,real:!0,forced:h},{finally:function(e){var t=u(this,s("Promise")),r="function"==typeof e;return this.then(r?function(r){return c(t,e()).then((function(){return r}))}:e,r?function(r){return c(t,e()).then((function(){throw r}))}:e)}}),i||"function"!=typeof a||a.prototype["finally"]||l(a.prototype,"finally",s("Promise").prototype["finally"])},function(e,t,r){r(139),r(144),r(142);var n=r(43);e.exports=n.URL},function(e,t,r){"use strict";r(77);var n,i=r(10),a=r(13),o=r(140),s=r(11),u=r(72),c=r(29),l=r(126),h=r(23),f=r(100),d=r(90),p=r(78).codeAt,v=r(141),g=r(85),y=r(142),m=r(33),b=s.URL,_=y.URLSearchParams,w=y.getState,S=m.set,A=m.getterFor("URL"),k=Math.floor,x=Math.pow,P="Invalid authority",C="Invalid scheme",T="Invalid host",R="Invalid port",E=/[A-Za-z]/,L=/[\d+-.A-Za-z]/,O=/\d/,I=/^(0x|0X)/,M=/^[0-7]+$/,F=/^\d+$/,N=/^[\dA-Fa-f]+$/,D=/[\u0000\u0009\u000A\u000D #%/:?@[\\]]/,j=/[\u0000\u0009\u000A\u000D #/:?@[\\]]/,U=/^[\u0000-\u001F ]+|[\u0000-\u001F ]+$/g,q=/[\u0009\u000A\u000D]/g,B=function(e,t){var r,n,i;if("["==t.charAt(0)){if("]"!=t.charAt(t.length-1))return T;if(r=V(t.slice(1,-1)),!r)return T;e.host=r}else if($(e)){if(t=v(t),D.test(t))return T;if(r=W(t),null===r)return T;e.host=r}else{if(j.test(t))return T;for(r="",n=d(t),i=0;i<n.length;i++)r+=K(n[i],G);e.host=r}},W=function(e){var t,r,n,i,a,o,s,u=e.split(".");if(u.length&&""==u[u.length-1]&&u.pop(),t=u.length,t>4)return e;for(r=[],n=0;n<t;n++){if(i=u[n],""==i)return e;if(a=10,i.length>1&&"0"==i.charAt(0)&&(a=I.test(i)?16:8,i=i.slice(8==a?1:2)),""===i)o=0;else{if(!(10==a?F:8==a?M:N).test(i))return e;o=parseInt(i,a)}r.push(o)}for(n=0;n<t;n++)if(o=r[n],n==t-1){if(o>=x(256,5-t))return null}else if(o>255)return null;for(s=r.pop(),n=0;n<r.length;n++)s+=r[n]*x(256,3-n);return s},V=function(e){var t,r,n,i,a,o,s,u=[0,0,0,0,0,0,0,0],c=0,l=null,h=0,f=function(){return e.charAt(h)};if(":"==f()){if(":"!=e.charAt(1))return;h+=2,c++,l=c}while(f()){if(8==c)return;if(":"!=f()){t=r=0;while(r<4&&N.test(f()))t=16*t+parseInt(f(),16),h++,r++;if("."==f()){if(0==r)return;if(h-=r,c>6)return;n=0;while(f()){if(i=null,n>0){if(!("."==f()&&n<4))return;h++}if(!O.test(f()))return;while(O.test(f())){if(a=parseInt(f(),10),null===i)i=a;else{if(0==i)return;i=10*i+a}if(i>255)return;h++}u[c]=256*u[c]+i,n++,2!=n&&4!=n||c++}if(4!=n)return;break}if(":"==f()){if(h++,!f())return}else if(f())return;u[c++]=t}else{if(null!==l)return;h++,c++,l=c}}if(null!==l){o=c-l,c=7;while(0!=c&&o>0)s=u[c],u[c--]=u[l+o-1],u[l+--o]=s}else if(8!=c)return;return u},z=function(e){for(var t=null,r=1,n=null,i=0,a=0;a<8;a++)0!==e[a]?(i>r&&(t=n,r=i),n=null,i=0):(null===n&&(n=a),++i);return i>r&&(t=n,r=i),t},H=function(e){var t,r,n,i;if("number"==typeof e){for(t=[],r=0;r<4;r++)t.unshift(e%256),e=k(e/256);return t.join(".")}if("object"==typeof e){for(t="",n=z(e),r=0;r<8;r++)i&&0===e[r]||(i&&(i=!1),n===r?(t+=r?":":"::",i=!0):(t+=e[r].toString(16),r<7&&(t+=":")));return"["+t+"]"}return e},G={},Y=f({},G,{" ":1,'"':1,"<":1,">":1,"`":1}),X=f({},Y,{"#":1,"?":1,"{":1,"}":1}),Q=f({},X,{"/":1,":":1,";":1,"=":1,"@":1,"[":1,"\\":1,"]":1,"^":1,"|":1}),K=function(e,t){var r=p(e,0);return r>32&&r<127&&!h(t,e)?e:encodeURIComponent(e)},J={ftp:21,file:null,http:80,https:443,ws:80,wss:443},$=function(e){return h(J,e.scheme)},Z=function(e){return""!=e.username||""!=e.password},ee=function(e){return!e.host||e.cannotBeABaseURL||"file"==e.scheme},te=function(e,t){var r;return 2==e.length&&E.test(e.charAt(0))&&(":"==(r=e.charAt(1))||!t&&"|"==r)},re=function(e){var t;return e.length>1&&te(e.slice(0,2))&&(2==e.length||"/"===(t=e.charAt(2))||"\\"===t||"?"===t||"#"===t)},ne=function(e){var t=e.path,r=t.length;!r||"file"==e.scheme&&1==r&&te(t[0],!0)||t.pop()},ie=function(e){return"."===e||"%2e"===e.toLowerCase()},ae=function(e){return e=e.toLowerCase(),".."===e||"%2e."===e||".%2e"===e||"%2e%2e"===e},oe={},se={},ue={},ce={},le={},he={},fe={},de={},pe={},ve={},ge={},ye={},me={},be={},_e={},we={},Se={},Ae={},ke={},xe={},Pe={},Ce=function(e,t,r,i){var a,o,s,u,c=r||oe,l=0,f="",p=!1,v=!1,g=!1;r||(e.scheme="",e.username="",e.password="",e.host=null,e.port=null,e.path=[],e.query=null,e.fragment=null,e.cannotBeABaseURL=!1,t=t.replace(U,"")),t=t.replace(q,""),a=d(t);while(l<=a.length){switch(o=a[l],c){case oe:if(!o||!E.test(o)){if(r)return C;c=ue;continue}f+=o.toLowerCase(),c=se;break;case se:if(o&&(L.test(o)||"+"==o||"-"==o||"."==o))f+=o.toLowerCase();else{if(":"!=o){if(r)return C;f="",c=ue,l=0;continue}if(r&&($(e)!=h(J,f)||"file"==f&&(Z(e)||null!==e.port)||"file"==e.scheme&&!e.host))return;if(e.scheme=f,r)return void($(e)&&J[e.scheme]==e.port&&(e.port=null));f="","file"==e.scheme?c=be:$(e)&&i&&i.scheme==e.scheme?c=ce:$(e)?c=de:"/"==a[l+1]?(c=le,l++):(e.cannotBeABaseURL=!0,e.path.push(""),c=ke)}break;case ue:if(!i||i.cannotBeABaseURL&&"#"!=o)return C;if(i.cannotBeABaseURL&&"#"==o){e.scheme=i.scheme,e.path=i.path.slice(),e.query=i.query,e.fragment="",e.cannotBeABaseURL=!0,c=Pe;break}c="file"==i.scheme?be:he;continue;case ce:if("/"!=o||"/"!=a[l+1]){c=he;continue}c=pe,l++;break;case le:if("/"==o){c=ve;break}c=Ae;continue;case he:if(e.scheme=i.scheme,o==n)e.username=i.username,e.password=i.password,e.host=i.host,e.port=i.port,e.path=i.path.slice(),e.query=i.query;else if("/"==o||"\\"==o&&$(e))c=fe;else if("?"==o)e.username=i.username,e.password=i.password,e.host=i.host,e.port=i.port,e.path=i.path.slice(),e.query="",c=xe;else{if("#"!=o){e.username=i.username,e.password=i.password,e.host=i.host,e.port=i.port,e.path=i.path.slice(),e.path.pop(),c=Ae;continue}e.username=i.username,e.password=i.password,e.host=i.host,e.port=i.port,e.path=i.path.slice(),e.query=i.query,e.fragment="",c=Pe}break;case fe:if(!$(e)||"/"!=o&&"\\"!=o){if("/"!=o){e.username=i.username,e.password=i.password,e.host=i.host,e.port=i.port,c=Ae;continue}c=ve}else c=pe;break;case de:if(c=pe,"/"!=o||"/"!=f.charAt(l+1))continue;l++;break;case pe:if("/"!=o&&"\\"!=o){c=ve;continue}break;case ve:if("@"==o){p&&(f="%40"+f),p=!0,s=d(f);for(var y=0;y<s.length;y++){var m=s[y];if(":"!=m||g){var b=K(m,Q);g?e.password+=b:e.username+=b}else g=!0}f=""}else if(o==n||"/"==o||"?"==o||"#"==o||"\\"==o&&$(e)){if(p&&""==f)return P;l-=d(f).length+1,f="",c=ge}else f+=o;break;case ge:case ye:if(r&&"file"==e.scheme){c=we;continue}if(":"!=o||v){if(o==n||"/"==o||"?"==o||"#"==o||"\\"==o&&$(e)){if($(e)&&""==f)return T;if(r&&""==f&&(Z(e)||null!==e.port))return;if(u=B(e,f),u)return u;if(f="",c=Se,r)return;continue}"["==o?v=!0:"]"==o&&(v=!1),f+=o}else{if(""==f)return T;if(u=B(e,f),u)return u;if(f="",c=me,r==ye)return}break;case me:if(!O.test(o)){if(o==n||"/"==o||"?"==o||"#"==o||"\\"==o&&$(e)||r){if(""!=f){var _=parseInt(f,10);if(_>65535)return R;e.port=$(e)&&_===J[e.scheme]?null:_,f=""}if(r)return;c=Se;continue}return R}f+=o;break;case be:if(e.scheme="file","/"==o||"\\"==o)c=_e;else{if(!i||"file"!=i.scheme){c=Ae;continue}if(o==n)e.host=i.host,e.path=i.path.slice(),e.query=i.query;else if("?"==o)e.host=i.host,e.path=i.path.slice(),e.query="",c=xe;else{if("#"!=o){re(a.slice(l).join(""))||(e.host=i.host,e.path=i.path.slice(),ne(e)),c=Ae;continue}e.host=i.host,e.path=i.path.slice(),e.query=i.query,e.fragment="",c=Pe}}break;case _e:if("/"==o||"\\"==o){c=we;break}i&&"file"==i.scheme&&!re(a.slice(l).join(""))&&(te(i.path[0],!0)?e.path.push(i.path[0]):e.host=i.host),c=Ae;continue;case we:if(o==n||"/"==o||"\\"==o||"?"==o||"#"==o){if(!r&&te(f))c=Ae;else if(""==f){if(e.host="",r)return;c=Se}else{if(u=B(e,f),u)return u;if("localhost"==e.host&&(e.host=""),r)return;f="",c=Se}continue}f+=o;break;case Se:if($(e)){if(c=Ae,"/"!=o&&"\\"!=o)continue}else if(r||"?"!=o)if(r||"#"!=o){if(o!=n&&(c=Ae,"/"!=o))continue}else e.fragment="",c=Pe;else e.query="",c=xe;break;case Ae:if(o==n||"/"==o||"\\"==o&&$(e)||!r&&("?"==o||"#"==o)){if(ae(f)?(ne(e),"/"==o||"\\"==o&&$(e)||e.path.push("")):ie(f)?"/"==o||"\\"==o&&$(e)||e.path.push(""):("file"==e.scheme&&!e.path.length&&te(f)&&(e.host&&(e.host=""),f=f.charAt(0)+":"),e.path.push(f)),f="","file"==e.scheme&&(o==n||"?"==o||"#"==o))while(e.path.length>1&&""===e.path[0])e.path.shift();"?"==o?(e.query="",c=xe):"#"==o&&(e.fragment="",c=Pe)}else f+=K(o,X);break;case ke:"?"==o?(e.query="",c=xe):"#"==o?(e.fragment="",c=Pe):o!=n&&(e.path[0]+=K(o,G));break;case xe:r||"#"!=o?o!=n&&("'"==o&&$(e)?e.query+="%27":e.query+="#"==o?"%23":K(o,G)):(e.fragment="",c=Pe);break;case Pe:o!=n&&(e.fragment+=K(o,Y));break}l++}},Te=function(e){var t,r,n=l(this,Te,"URL"),i=arguments.length>1?arguments[1]:void 0,o=String(e),s=S(n,{type:"URL"});if(void 0!==i)if(i instanceof Te)t=A(i);else if(r=Ce(t={},String(i)),r)throw TypeError(r);if(r=Ce(s,o,null,t),r)throw TypeError(r);var u=s.searchParams=new _,c=w(u);c.updateSearchParams(s.query),c.updateURL=function(){s.query=String(u)||null},a||(n.href=Ee.call(n),n.origin=Le.call(n),n.protocol=Oe.call(n),n.username=Ie.call(n),n.password=Me.call(n),n.host=Fe.call(n),n.hostname=Ne.call(n),n.port=De.call(n),n.pathname=je.call(n),n.search=Ue.call(n),n.searchParams=qe.call(n),n.hash=Be.call(n))},Re=Te.prototype,Ee=function(){var e=A(this),t=e.scheme,r=e.username,n=e.password,i=e.host,a=e.port,o=e.path,s=e.query,u=e.fragment,c=t+":";return null!==i?(c+="//",Z(e)&&(c+=r+(n?":"+n:"")+"@"),c+=H(i),null!==a&&(c+=":"+a)):"file"==t&&(c+="//"),c+=e.cannotBeABaseURL?o[0]:o.length?"/"+o.join("/"):"",null!==s&&(c+="?"+s),null!==u&&(c+="#"+u),c},Le=function(){var e=A(this),t=e.scheme,r=e.port;if("blob"==t)try{return new URL(t.path[0]).origin}catch(n){return"null"}return"file"!=t&&$(e)?t+"://"+H(e.host)+(null!==r?":"+r:""):"null"},Oe=function(){return A(this).scheme+":"},Ie=function(){return A(this).username},Me=function(){return A(this).password},Fe=function(){var e=A(this),t=e.host,r=e.port;return null===t?"":null===r?H(t):H(t)+":"+r},Ne=function(){var e=A(this).host;return null===e?"":H(e)},De=function(){var e=A(this).port;return null===e?"":String(e)},je=function(){var e=A(this),t=e.path;return e.cannotBeABaseURL?t[0]:t.length?"/"+t.join("/"):""},Ue=function(){var e=A(this).query;return e?"?"+e:""},qe=function(){return A(this).searchParams},Be=function(){var e=A(this).fragment;return e?"#"+e:""},We=function(e,t){return{get:e,set:t,configurable:!0,enumerable:!0}};if(a&&u(Re,{href:We(Ee,(function(e){var t=A(this),r=String(e),n=Ce(t,r);if(n)throw TypeError(n);w(t.searchParams).updateSearchParams(t.query)})),origin:We(Le),protocol:We(Oe,(function(e){var t=A(this);Ce(t,String(e)+":",oe)})),username:We(Ie,(function(e){var t=A(this),r=d(String(e));if(!ee(t)){t.username="";for(var n=0;n<r.length;n++)t.username+=K(r[n],Q)}})),password:We(Me,(function(e){var t=A(this),r=d(String(e));if(!ee(t)){t.password="";for(var n=0;n<r.length;n++)t.password+=K(r[n],Q)}})),host:We(Fe,(function(e){var t=A(this);t.cannotBeABaseURL||Ce(t,String(e),ge)})),hostname:We(Ne,(function(e){var t=A(this);t.cannotBeABaseURL||Ce(t,String(e),ye)})),port:We(De,(function(e){var t=A(this);ee(t)||(e=String(e),""==e?t.port=null:Ce(t,e,me))})),pathname:We(je,(function(e){var t=A(this);t.cannotBeABaseURL||(t.path=[],Ce(t,e+"",Se))})),search:We(Ue,(function(e){var t=A(this);e=String(e),""==e?t.query=null:("?"==e.charAt(0)&&(e=e.slice(1)),t.query="",Ce(t,e,xe)),w(t.searchParams).updateSearchParams(t.query)})),searchParams:We(qe),hash:We(Be,(function(e){var t=A(this);e=String(e),""!=e?("#"==e.charAt(0)&&(e=e.slice(1)),t.fragment="",Ce(t,e,Pe)):t.fragment=null}))}),c(Re,"toJSON",(function(){return Ee.call(this)}),{enumerable:!0}),c(Re,"toString",(function(){return Ee.call(this)}),{enumerable:!0}),b){var Ve=b.createObjectURL,ze=b.revokeObjectURL;Ve&&c(Te,"createObjectURL",(function(e){return Ve.apply(b,arguments)})),ze&&c(Te,"revokeObjectURL",(function(e){return ze.apply(b,arguments)}))}g(Te,"URL"),i({global:!0,forced:!o,sham:!a},{URL:Te})},function(e,t,r){var n=r(14),i=r(57),a=r(37),o=i("iterator");e.exports=!n((function(){var e=new URL("b?a=1&b=2&c=3","http://a"),t=e.searchParams,r="";return e.pathname="c%20d",t.forEach((function(e,n){t["delete"]("b"),r+=n+e})),a&&!e.toJSON||!t.sort||"http://a/c%20d?a=1&c=3"!==e.href||"3"!==t.get("c")||"a=1"!==String(new URLSearchParams("?a=1"))||!t[o]||"a"!==new URL("https://a@b").username||"b"!==new URLSearchParams(new URLSearchParams("a=b")).get("a")||"xn--e1aybc"!==new URL("http://тест").host||"#%D0%B1"!==new URL("http://a#б").hash||"a1c3"!==r||"x"!==new URL("http://x",void 0).host}))},function(e,t,r){"use strict";var n=2147483647,i=36,a=1,o=26,s=38,u=700,c=72,l=128,h="-",f=/[^\0-\u007E]/,d=/[.\u3002\uFF0E\uFF61]/g,p="Overflow: input needs wider integers to process",v=i-a,g=Math.floor,y=String.fromCharCode,m=function(e){var t=[],r=0,n=e.length;while(r<n){var i=e.charCodeAt(r++);if(i>=55296&&i<=56319&&r<n){var a=e.charCodeAt(r++);56320==(64512&a)?t.push(((1023&i)<<10)+(1023&a)+65536):(t.push(i),r--)}else t.push(i)}return t},b=function(e){return e+22+75*(e<26)},_=function(e,t,r){var n=0;for(e=r?g(e/u):e>>1,e+=g(e/t);e>v*o>>1;n+=i)e=g(e/v);return g(n+(v+1)*e/(e+s))},w=function(e){var t=[];e=m(e);var r,s,u=e.length,f=l,d=0,v=c;for(r=0;r<e.length;r++)s=e[r],s<128&&t.push(y(s));var w=t.length,S=w;w&&t.push(h);while(S<u){var A=n;for(r=0;r<e.length;r++)s=e[r],s>=f&&s<A&&(A=s);var k=S+1;if(A-f>g((n-d)/k))throw RangeError(p);for(d+=(A-f)*k,f=A,r=0;r<e.length;r++){if(s=e[r],s<f&&++d>n)throw RangeError(p);if(s==f){for(var x=d,P=i;;P+=i){var C=P<=v?a:P>=v+o?o:P-v;if(x<C)break;var T=x-C,R=i-C;t.push(y(b(C+T%R))),x=g(T/R)}t.push(y(b(x))),v=_(d,k,S==w),d=0,++S}}++d,++f}return t.join("")};e.exports=function(e){var t,r,n=[],i=e.toLowerCase().replace(d,".").split(".");for(t=0;t<i.length;t++)r=i[t],n.push(f.test(r)?"xn--"+w(r):r);return n.join(".")}},function(e,t,r){"use strict";r(102);var n=r(10),i=r(42),a=r(140),o=r(29),s=r(124),u=r(85),c=r(80),l=r(33),h=r(126),f=r(23),d=r(62),p=r(95),v=r(28),g=r(22),y=r(71),m=r(16),b=r(143),_=r(94),w=r(57),S=i("fetch"),A=i("Headers"),k=w("iterator"),x="URLSearchParams",P=x+"Iterator",C=l.set,T=l.getterFor(x),R=l.getterFor(P),E=/\+/g,L=Array(4),O=function(e){return L[e-1]||(L[e-1]=RegExp("((?:%[\\da-f]{2}){"+e+"})","gi"))},I=function(e){try{return decodeURIComponent(e)}catch(t){return e}},M=function(e){var t=e.replace(E," "),r=4;try{return decodeURIComponent(t)}catch(n){while(r)t=t.replace(O(r--),I);return t}},F=/[!'()~]|%20/g,N={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+"},D=function(e){return N[e]},j=function(e){return encodeURIComponent(e).replace(F,D)},U=function(e,t){if(t){var r,n,i=t.split("&"),a=0;while(a<i.length)r=i[a++],r.length&&(n=r.split("="),e.push({key:M(n.shift()),value:M(n.join("="))}))}},q=function(e){this.entries.length=0,U(this.entries,e)},B=function(e,t){if(e<t)throw TypeError("Not enough arguments")},W=c((function(e,t){C(this,{type:P,iterator:b(T(e).entries),kind:t})}),"Iterator",(function(){var e=R(this),t=e.kind,r=e.iterator.next(),n=r.value;return r.done||(r.value="keys"===t?n.key:"values"===t?n.value:[n.key,n.value]),r})),V=function(){h(this,V,x);var e,t,r,n,i,a,o,s,u,c=arguments.length>0?arguments[0]:void 0,l=this,d=[];if(C(l,{type:x,entries:d,updateURL:function(){},updateSearchParams:q}),void 0!==c)if(g(c))if(e=_(c),"function"===typeof e){t=e.call(c),r=t.next;while(!(n=r.call(t)).done){if(i=b(v(n.value)),a=i.next,(o=a.call(i)).done||(s=a.call(i)).done||!a.call(i).done)throw TypeError("Expected sequence with length 2");d.push({key:o.value+"",value:s.value+""})}}else for(u in c)f(c,u)&&d.push({key:u,value:c[u]+""});else U(d,"string"===typeof c?"?"===c.charAt(0)?c.slice(1):c:c+"")},z=V.prototype;s(z,{append:function(e,t){B(arguments.length,2);var r=T(this);r.entries.push({key:e+"",value:t+""}),r.updateURL()},delete:function(e){B(arguments.length,1);var t=T(this),r=t.entries,n=e+"",i=0;while(i<r.length)r[i].key===n?r.splice(i,1):i++;t.updateURL()},get:function(e){B(arguments.length,1);for(var t=T(this).entries,r=e+"",n=0;n<t.length;n++)if(t[n].key===r)return t[n].value;return null},getAll:function(e){B(arguments.length,1);for(var t=T(this).entries,r=e+"",n=[],i=0;i<t.length;i++)t[i].key===r&&n.push(t[i].value);return n},has:function(e){B(arguments.length,1);var t=T(this).entries,r=e+"",n=0;while(n<t.length)if(t[n++].key===r)return!0;return!1},set:function(e,t){B(arguments.length,1);for(var r,n=T(this),i=n.entries,a=!1,o=e+"",s=t+"",u=0;u<i.length;u++)r=i[u],r.key===o&&(a?i.splice(u--,1):(a=!0,r.value=s));a||i.push({key:o,value:s}),n.updateURL()},sort:function(){var e,t,r,n=T(this),i=n.entries,a=i.slice();for(i.length=0,r=0;r<a.length;r++){for(e=a[r],t=0;t<r;t++)if(i[t].key>e.key){i.splice(t,0,e);break}t===r&&i.push(e)}n.updateURL()},forEach:function(e){var t,r=T(this).entries,n=d(e,arguments.length>1?arguments[1]:void 0,3),i=0;while(i<r.length)t=r[i++],n(t.value,t.key,this)},keys:function(){return new W(this,"keys")},values:function(){return new W(this,"values")},entries:function(){return new W(this,"entries")}},{enumerable:!0}),o(z,k,z.entries),o(z,"toString",(function(){var e,t=T(this).entries,r=[],n=0;while(n<t.length)e=t[n++],r.push(j(e.key)+"="+j(e.value));return r.join("&")}),{enumerable:!0}),u(V,x),n({global:!0,forced:!a},{URLSearchParams:V}),a||"function"!=typeof S||"function"!=typeof A||n({global:!0,enumerable:!0,forced:!0},{fetch:function(e){var t,r,n,i=[e];return arguments.length>1&&(t=arguments[1],g(t)&&(r=t.body,p(r)===x&&(n=t.headers?new A(t.headers):new A,n.has("content-type")||n.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"),t=y(t,{body:m(0,String(r)),headers:m(0,n)}))),i.push(t)),S.apply(this,i)}}),e.exports={URLSearchParams:V,getState:T}},function(e,t,r){var n=r(28),i=r(94);e.exports=function(e){var t=i(e);if("function"!=typeof t)throw TypeError(String(e)+" is not iterable");return n(t.call(e))}},function(e,t,r){"use strict";var n=r(10);n({target:"URL",proto:!0,enumerable:!0},{toJSON:function(){return URL.prototype.toString.call(this)}})},function(e,t,r){(function(e,r){r(t)})(0,(function(e){"use strict";var t="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?Symbol:function(e){return"Symbol("+e+")"};function r(){}var n=Number.isNaN||function(e){return e!==e},i=r;function a(e){return"object"===typeof e&&null!==e||"function"===typeof e}function o(e){return e.slice()}function s(e,t,r,n,i){new Uint8Array(e).set(new Uint8Array(r,n,i),t)}function u(e){return!1!==c(e)&&e!==1/0}function c(e){return"number"===typeof e&&(!n(e)&&!(e<0))}function l(e,t,r){if("function"!==typeof e)throw new TypeError("Argument is not a function");return Function.prototype.apply.call(e,t,r)}function h(e,t,r,n){var i=e[t];if(void 0!==i){if("function"!==typeof i)throw new TypeError(i+" is not a method");switch(r){case 0:return function(){return d(i,e,n)};case 1:return function(t){var r=[t].concat(n);return d(i,e,r)}}}return function(){return A(void 0)}}function f(e,t,r){var n=e[t];if(void 0!==n)return l(n,e,r)}function d(e,t,r){try{return A(l(e,t,r))}catch(n){return k(n)}}function p(e){return e}function v(e){return!1}function g(e){if(e=Number(e),n(e)||e<0)throw new RangeError("highWaterMark property of a queuing strategy must be non-negative and non-NaN");return e}function y(e){if(void 0===e)return function(){return 1};if("function"!==typeof e)throw new TypeError("size property of a queuing strategy must be a function");return function(t){return e(t)}}var m=Promise,b=Promise.prototype.then,_=Promise.resolve.bind(m),w=Promise.reject.bind(m);function S(e){return new m(e)}function A(e){return _(e)}function k(e){return w(e)}function x(e,t,r){return b.call(e,t,r)}function P(e,t,r){x(x(e,t,r),void 0,i)}function C(e,t){P(e,t)}function T(e,t){P(e,void 0,t)}function R(e,t,r){return x(e,t,r)}function E(e){x(e,void 0,i)}var L=16384,O=function(){function e(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}return Object.defineProperty(e.prototype,"length",{get:function(){return this._size},enumerable:!0,configurable:!0}),e.prototype.push=function(e){var t=this._back,r=t;t._elements.length===L-1&&(r={_elements:[],_next:void 0}),t._elements.push(e),r!==t&&(this._back=r,t._next=r),++this._size},e.prototype.shift=function(){var e=this._front,t=e,r=this._cursor,n=r+1,i=e._elements,a=i[r];return n===L&&(t=e._next,n=0),--this._size,this._cursor=n,e!==t&&(this._front=t),i[r]=void 0,a},e.prototype.forEach=function(e){var t=this._cursor,r=this._front,n=r._elements;while(t!==n.length||void 0!==r._next){if(t===n.length&&(r=r._next,n=r._elements,t=0,0===n.length))break;e(n[t]),++t}},e.prototype.peek=function(){var e=this._front,t=this._cursor;return e._elements[t]},e}();function I(e,t,r){var n=null;!0===r&&(n=Object.prototype);var i=Object.create(n);return i.value=e,i.done=t,i}function M(e,t){e._forAuthorCode=!0,e._ownerReadableStream=t,t._reader=e,"readable"===t._state?j(e):"closed"===t._state?q(e):U(e,t._storedError)}function F(e,t){var r=e._ownerReadableStream;return Nr(r,t)}function N(e){"readable"===e._ownerReadableStream._state?B(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):W(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")),e._ownerReadableStream._reader=void 0,e._ownerReadableStream=void 0}function D(e){return new TypeError("Cannot "+e+" a stream using a released reader")}function j(e){e._closedPromise=S((function(t,r){e._closedPromise_resolve=t,e._closedPromise_reject=r}))}function U(e,t){j(e),B(e,t)}function q(e){j(e),V(e)}function B(e,t){E(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0}function W(e,t){U(e,t)}function V(e){e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0}var z=t("[[CancelSteps]]"),H=t("[[PullSteps]]");function G(e,t){void 0===t&&(t=!1);var r=new Z(e);return r._forAuthorCode=t,r}function Y(e){var t=S((function(t,r){var n={_resolve:t,_reject:r};e._reader._readRequests.push(n)}));return t}function X(e,t,r){var n=e._reader,i=n._readRequests.shift();i._resolve(I(t,r,n._forAuthorCode))}function Q(e){return e._reader._readRequests.length}function K(e){var t=e._reader;return void 0!==t&&!!ee(t)}var J,$,Z=function(){function e(e){if(!1===Mr(e))throw new TypeError("ReadableStreamDefaultReader can only be constructed with a ReadableStream instance");if(!0===Fr(e))throw new TypeError("This stream has already been locked for exclusive reading by another reader");M(this,e),this._readRequests=new O}return Object.defineProperty(e.prototype,"closed",{get:function(){return ee(this)?this._closedPromise:k(re("closed"))},enumerable:!0,configurable:!0}),e.prototype.cancel=function(e){return ee(this)?void 0===this._ownerReadableStream?k(D("cancel")):F(this,e):k(re("cancel"))},e.prototype.read=function(){return ee(this)?void 0===this._ownerReadableStream?k(D("read from")):te(this):k(re("read"))},e.prototype.releaseLock=function(){if(!ee(this))throw re("releaseLock");if(void 0!==this._ownerReadableStream){if(this._readRequests.length>0)throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");N(this)}},e}();function ee(e){return!!a(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readRequests")}function te(e){var t=e._ownerReadableStream;return t._disturbed=!0,"closed"===t._state?A(I(void 0,!0,e._forAuthorCode)):"errored"===t._state?k(t._storedError):t._readableStreamController[H]()}function re(e){return new TypeError("ReadableStreamDefaultReader.prototype."+e+" can only be used on a ReadableStreamDefaultReader")}"symbol"===typeof t.asyncIterator&&(J={},J[t.asyncIterator]=function(){return this},$=J,Object.defineProperty($,t.asyncIterator,{enumerable:!1}));var ne={next:function(){if(!1===ae(this))return k(oe("next"));var e=this._asyncIteratorReader;return void 0===e._ownerReadableStream?k(D("iterate")):R(te(e),(function(t){var r=t.done;r&&N(e);var n=t.value;return I(n,r,!0)}))},return:function(e){if(!1===ae(this))return k(oe("next"));var t=this._asyncIteratorReader;if(void 0===t._ownerReadableStream)return k(D("finish iterating"));if(t._readRequests.length>0)return k(new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled"));if(!1===this._preventCancel){var r=F(t,e);return N(t),R(r,(function(){return I(e,!0,!0)}))}return N(t),A(I(e,!0,!0))}};function ie(e,t){void 0===t&&(t=!1);var r=G(e),n=Object.create(ne);return n._asyncIteratorReader=r,n._preventCancel=Boolean(t),n}function ae(e){return!!a(e)&&!!Object.prototype.hasOwnProperty.call(e,"_asyncIteratorReader")}function oe(e){return new TypeError("ReadableStreamAsyncIterator."+e+" can only be used on a ReadableSteamAsyncIterator")}function se(e){var t=e._queue.shift();return e._queueTotalSize-=t.size,e._queueTotalSize<0&&(e._queueTotalSize=0),t.value}function ue(e,t,r){if(r=Number(r),!u(r))throw new RangeError("Size must be a finite, non-NaN, non-negative number.");e._queue.push({value:t,size:r}),e._queueTotalSize+=r}function ce(e){var t=e._queue.peek();return t.value}function le(e){e._queue=new O,e._queueTotalSize=0}void 0!==$&&Object.setPrototypeOf(ne,$),Object.defineProperty(ne,"next",{enumerable:!1}),Object.defineProperty(ne,"return",{enumerable:!1});var he=t("[[AbortSteps]]"),fe=t("[[ErrorSteps]]"),de=function(){function e(e,t){void 0===e&&(e={}),void 0===t&&(t={}),ge(this);var r=t.size,n=t.highWaterMark,i=e.type;if(void 0!==i)throw new RangeError("Invalid type is specified");var a=y(r);void 0===n&&(n=1),n=g(n),Xe(this,e,n,a)}return Object.defineProperty(e.prototype,"locked",{get:function(){if(!1===ye(this))throw ot("locked");return me(this)},enumerable:!0,configurable:!0}),e.prototype.abort=function(e){return!1===ye(this)?k(ot("abort")):!0===me(this)?k(new TypeError("Cannot abort a stream that already has a writer")):be(this,e)},e.prototype.close=function(){return!1===ye(this)?k(ot("close")):!0===me(this)?k(new TypeError("Cannot close a stream that already has a writer")):!0===Re(this)?k(new TypeError("Cannot close an already-closing stream")):_e(this)},e.prototype.getWriter=function(){if(!1===ye(this))throw ot("getWriter");return pe(this)},e}();function pe(e){return new Fe(e)}function ve(e,t,r,n,i,a){void 0===i&&(i=1),void 0===a&&(a=function(){return 1});var o=Object.create(de.prototype);ge(o);var s=Object.create(He.prototype);return Ye(o,s,e,t,r,n,i,a),o}function ge(e){e._state="writable",e._storedError=void 0,e._writer=void 0,e._writableStreamController=void 0,e._writeRequests=new O,e._inFlightWriteRequest=void 0,e._closeRequest=void 0,e._inFlightCloseRequest=void 0,e._pendingAbortRequest=void 0,e._backpressure=!1}function ye(e){return!!a(e)&&!!Object.prototype.hasOwnProperty.call(e,"_writableStreamController")}function me(e){return void 0!==e._writer}function be(e,t){var r=e._state;if("closed"===r||"errored"===r)return A(void 0);if(void 0!==e._pendingAbortRequest)return e._pendingAbortRequest._promise;var n=!1;"erroring"===r&&(n=!0,t=void 0);var i=S((function(r,i){e._pendingAbortRequest={_promise:void 0,_resolve:r,_reject:i,_reason:t,_wasAlreadyErroring:n}}));return e._pendingAbortRequest._promise=i,!1===n&&Ae(e,t),i}function _e(e){var t=e._state;if("closed"===t||"errored"===t)return k(new TypeError("The stream (in "+t+" state) is not in the writable state and cannot be closed"));var r=S((function(t,r){var n={_resolve:t,_reject:r};e._closeRequest=n})),n=e._writer;return void 0!==n&&!0===e._backpressure&&"writable"===t&&wt(n),Ke(e._writableStreamController),r}function we(e){var t=S((function(t,r){var n={_resolve:t,_reject:r};e._writeRequests.push(n)}));return t}function Se(e,t){var r=e._state;"writable"!==r?ke(e):Ae(e,t)}function Ae(e,t){var r=e._writableStreamController;e._state="erroring",e._storedError=t;var n=e._writer;void 0!==n&&Be(n,t),!1===Ee(e)&&!0===r._started&&ke(e)}function ke(e){e._state="errored",e._writableStreamController[fe]();var t=e._storedError;if(e._writeRequests.forEach((function(e){e._reject(t)})),e._writeRequests=new O,void 0!==e._pendingAbortRequest){var r=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,!0===r._wasAlreadyErroring)return r._reject(t),void Ie(e);var n=e._writableStreamController[he](r._reason);P(n,(function(){r._resolve(),Ie(e)}),(function(t){r._reject(t),Ie(e)}))}else Ie(e)}function xe(e){e._inFlightWriteRequest._resolve(void 0),e._inFlightWriteRequest=void 0}function Pe(e,t){e._inFlightWriteRequest._reject(t),e._inFlightWriteRequest=void 0,Se(e,t)}function Ce(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0;var t=e._state;"erroring"===t&&(e._storedError=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";var r=e._writer;void 0!==r&&pt(r)}function Te(e,t){e._inFlightCloseRequest._reject(t),e._inFlightCloseRequest=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._reject(t),e._pendingAbortRequest=void 0),Se(e,t)}function Re(e){return void 0!==e._closeRequest||void 0!==e._inFlightCloseRequest}function Ee(e){return void 0!==e._inFlightWriteRequest||void 0!==e._inFlightCloseRequest}function Le(e){e._inFlightCloseRequest=e._closeRequest,e._closeRequest=void 0}function Oe(e){e._inFlightWriteRequest=e._writeRequests.shift()}function Ie(e){void 0!==e._closeRequest&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);var t=e._writer;void 0!==t&&ft(t,e._storedError)}function Me(e,t){var r=e._writer;void 0!==r&&t!==e._backpressure&&(!0===t?bt(r):wt(r)),e._backpressure=t}var Fe=function(){function e(e){if(!1===ye(e))throw new TypeError("WritableStreamDefaultWriter can only be constructed with a WritableStream instance");if(!0===me(e))throw new TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=e,e._writer=this;var t=e._state;if("writable"===t)!1===Re(e)&&!0===e._backpressure?vt(this):yt(this),ct(this);else if("erroring"===t)gt(this,e._storedError),ct(this);else if("closed"===t)yt(this),ht(this);else{var r=e._storedError;gt(this,r),lt(this,r)}}return Object.defineProperty(e.prototype,"closed",{get:function(){return!1===Ne(this)?k(st("closed")):this._closedPromise},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"desiredSize",{get:function(){if(!1===Ne(this))throw st("desiredSize");if(void 0===this._ownerWritableStream)throw ut("desiredSize");return We(this)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"ready",{get:function(){return!1===Ne(this)?k(st("ready")):this._readyPromise},enumerable:!0,configurable:!0}),e.prototype.abort=function(e){return!1===Ne(this)?k(st("abort")):void 0===this._ownerWritableStream?k(ut("abort")):De(this,e)},e.prototype.close=function(){if(!1===Ne(this))return k(st("close"));var e=this._ownerWritableStream;return void 0===e?k(ut("close")):!0===Re(e)?k(new TypeError("Cannot close an already-closing stream")):je(this)},e.prototype.releaseLock=function(){if(!1===Ne(this))throw st("releaseLock");var e=this._ownerWritableStream;void 0!==e&&Ve(this)},e.prototype.write=function(e){return!1===Ne(this)?k(st("write")):void 0===this._ownerWritableStream?k(ut("write to")):ze(this,e)},e}();function Ne(e){return!!a(e)&&!!Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream")}function De(e,t){var r=e._ownerWritableStream;return be(r,t)}function je(e){var t=e._ownerWritableStream;return _e(t)}function Ue(e){var t=e._ownerWritableStream,r=t._state;return!0===Re(t)||"closed"===r?A(void 0):"errored"===r?k(t._storedError):je(e)}function qe(e,t){"pending"===e._closedPromiseState?ft(e,t):dt(e,t)}function Be(e,t){"pending"===e._readyPromiseState?mt(e,t):_t(e,t)}function We(e){var t=e._ownerWritableStream,r=t._state;return"errored"===r||"erroring"===r?null:"closed"===r?0:$e(t._writableStreamController)}function Ve(e){var t=e._ownerWritableStream,r=new TypeError("Writer was released and can no longer be used to monitor the stream's closedness");Be(e,r),qe(e,r),t._writer=void 0,e._ownerWritableStream=void 0}function ze(e,t){var r=e._ownerWritableStream,n=r._writableStreamController,i=Je(n,t);if(r!==e._ownerWritableStream)return k(ut("write to"));var a=r._state;if("errored"===a)return k(r._storedError);if(!0===Re(r)||"closed"===a)return k(new TypeError("The stream is closing or closed and cannot be written to"));if("erroring"===a)return k(r._storedError);var o=we(r);return Ze(n,t,i),o}var He=function(){function e(){throw new TypeError("WritableStreamDefaultController cannot be constructed explicitly")}return e.prototype.error=function(e){if(!1===Ge(this))throw new TypeError("WritableStreamDefaultController.prototype.error can only be used on a WritableStreamDefaultController");var t=this._controlledWritableStream._state;"writable"===t&&at(this,e)},e.prototype[he]=function(e){var t=this._abortAlgorithm(e);return Qe(this),t},e.prototype[fe]=function(){le(this)},e}();function Ge(e){return!!a(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledWritableStream")}function Ye(e,t,r,n,i,a,o,s){t._controlledWritableStream=e,e._writableStreamController=t,t._queue=void 0,t._queueTotalSize=void 0,le(t),t._started=!1,t._strategySizeAlgorithm=s,t._strategyHWM=o,t._writeAlgorithm=n,t._closeAlgorithm=i,t._abortAlgorithm=a;var u=it(t);Me(e,u);var c=r(),l=A(c);P(l,(function(){t._started=!0,et(t)}),(function(r){t._started=!0,Se(e,r)}))}function Xe(e,t,r,n){var i=Object.create(He.prototype);function a(){return f(t,"start",[i])}var o=h(t,"write",1,[i]),s=h(t,"close",0,[]),u=h(t,"abort",1,[]);Ye(e,i,a,o,s,u,r,n)}function Qe(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function Ke(e){ue(e,"close",0),et(e)}function Je(e,t){try{return e._strategySizeAlgorithm(t)}catch(r){return tt(e,r),1}}function $e(e){return e._strategyHWM-e._queueTotalSize}function Ze(e,t,r){var n={chunk:t};try{ue(e,n,r)}catch(o){return void tt(e,o)}var i=e._controlledWritableStream;if(!1===Re(i)&&"writable"===i._state){var a=it(e);Me(i,a)}et(e)}function et(e){var t=e._controlledWritableStream;if(!1!==e._started&&void 0===t._inFlightWriteRequest){var r=t._state;if("erroring"!==r){if(0!==e._queue.length){var n=ce(e);"close"===n?rt(e):nt(e,n.chunk)}}else ke(t)}}function tt(e,t){"writable"===e._controlledWritableStream._state&&at(e,t)}function rt(e){var t=e._controlledWritableStream;Le(t),se(e);var r=e._closeAlgorithm();Qe(e),P(r,(function(){Ce(t)}),(function(e){Te(t,e)}))}function nt(e,t){var r=e._controlledWritableStream;Oe(r);var n=e._writeAlgorithm(t);P(n,(function(){xe(r);var t=r._state;if(se(e),!1===Re(r)&&"writable"===t){var n=it(e);Me(r,n)}et(e)}),(function(t){"writable"===r._state&&Qe(e),Pe(r,t)}))}function it(e){var t=$e(e);return t<=0}function at(e,t){var r=e._controlledWritableStream;Qe(e),Ae(r,t)}function ot(e){return new TypeError("WritableStream.prototype."+e+" can only be used on a WritableStream")}function st(e){return new TypeError("WritableStreamDefaultWriter.prototype."+e+" can only be used on a WritableStreamDefaultWriter")}function ut(e){return new TypeError("Cannot "+e+" a stream using a released writer")}function ct(e){e._closedPromise=S((function(t,r){e._closedPromise_resolve=t,e._closedPromise_reject=r,e._closedPromiseState="pending"}))}function lt(e,t){ct(e),ft(e,t)}function ht(e){ct(e),pt(e)}function ft(e,t){E(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected"}function dt(e,t){lt(e,t)}function pt(e){e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved"}function vt(e){e._readyPromise=S((function(t,r){e._readyPromise_resolve=t,e._readyPromise_reject=r})),e._readyPromiseState="pending"}function gt(e,t){vt(e),mt(e,t)}function yt(e){vt(e),wt(e)}function mt(e,t){E(e._readyPromise),e._readyPromise_reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected"}function bt(e){vt(e)}function _t(e,t){gt(e,t)}function wt(e){e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled"}function St(e){if("object"!==typeof e||null===e)return!1;try{return"boolean"===typeof e.aborted}catch(J){return!1}}var At="undefined"!==typeof DOMException?DOMException:void 0;function kt(e){if("function"!==typeof e&&"object"!==typeof e)return!1;try{return new e,!0}catch(J){return!1}}function xt(){var e=function(e,t){this.message=e||"",this.name=t||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)};return e.prototype=Object.create(Error.prototype),Object.defineProperty(e.prototype,"constructor",{value:e,writable:!0,configurable:!0}),e}var Pt=kt(At)?At:xt();function Ct(e,t,n,i,a,o){var s=G(e),u=pe(t);e._disturbed=!0;var c=!1,l=A(void 0);return S((function(h,f){var d;if(void 0!==o){if(d=function(){var r=new Pt("Aborted","AbortError"),n=[];!1===i&&n.push((function(){return"writable"===t._state?be(t,r):A(void 0)})),!1===a&&n.push((function(){return"readable"===e._state?Nr(e,r):A(void 0)})),_((function(){return Promise.all(n.map((function(e){return e()})))}),!0,r)},!0===o.aborted)return void d();o.addEventListener("abort",d)}function p(){return S((function(e,t){function r(n){n?e():x(v(),r,t)}r(!1)}))}function v(){return!0===c?A(!0):x(u._readyPromise,(function(){return x(te(s),(function(e){return!0===e.done||(l=x(ze(u,e.value),void 0,r),!1)}))}))}if(m(e,s._closedPromise,(function(e){!1===i?_((function(){return be(t,e)}),!0,e):w(!0,e)})),m(t,u._closedPromise,(function(t){!1===a?_((function(){return Nr(e,t)}),!0,t):w(!0,t)})),b(e,s._closedPromise,(function(){!1===n?_((function(){return Ue(u)})):w()})),!0===Re(t)||"closed"===t._state){var g=new TypeError("the destination writable stream closed before all data could be piped to it");!1===a?_((function(){return Nr(e,g)}),!0,g):w(!0,g)}function y(){var e=l;return x(l,(function(){return e!==l?y():void 0}))}function m(e,t,r){"errored"===e._state?r(e._storedError):T(t,r)}function b(e,t,r){"closed"===e._state?r():C(t,r)}function _(e,r,n){function i(){P(e(),(function(){return k(r,n)}),(function(e){return k(!0,e)}))}!0!==c&&(c=!0,"writable"===t._state&&!1===Re(t)?C(y(),i):i())}function w(e,r){!0!==c&&(c=!0,"writable"===t._state&&!1===Re(t)?C(y(),(function(){return k(e,r)})):k(e,r))}function k(e,t){Ve(u),N(s),void 0!==o&&o.removeEventListener("abort",d),e?f(t):h(void 0)}E(p())}))}var Tt=function(){function e(){throw new TypeError}return Object.defineProperty(e.prototype,"desiredSize",{get:function(){if(!1===Rt(this))throw Bt("desiredSize");return Nt(this)},enumerable:!0,configurable:!0}),e.prototype.close=function(){if(!1===Rt(this))throw Bt("close");if(!1===jt(this))throw new TypeError("The stream is not in a state that permits close");It(this)},e.prototype.enqueue=function(e){if(!1===Rt(this))throw Bt("enqueue");if(!1===jt(this))throw new TypeError("The stream is not in a state that permits enqueue");return Mt(this,e)},e.prototype.error=function(e){if(!1===Rt(this))throw Bt("error");Ft(this,e)},e.prototype[z]=function(e){le(this);var t=this._cancelAlgorithm(e);return Ot(this),t},e.prototype[H]=function(){var e=this._controlledReadableStream;if(this._queue.length>0){var t=se(this);return!0===this._closeRequested&&0===this._queue.length?(Ot(this),Dr(e)):Et(this),A(I(t,!1,e._reader._forAuthorCode))}var r=Y(e);return Et(this),r},e}();function Rt(e){return!!a(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableStream")}function Et(e){var t=Lt(e);if(!1!==t)if(!0!==e._pulling){e._pulling=!0;var r=e._pullAlgorithm();P(r,(function(){e._pulling=!1,!0===e._pullAgain&&(e._pullAgain=!1,Et(e))}),(function(t){Ft(e,t)}))}else e._pullAgain=!0}function Lt(e){var t=e._controlledReadableStream;if(!1===jt(e))return!1;if(!1===e._started)return!1;if(!0===Fr(t)&&Q(t)>0)return!0;var r=Nt(e);return r>0}function Ot(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function It(e){var t=e._controlledReadableStream;e._closeRequested=!0,0===e._queue.length&&(Ot(e),Dr(t))}function Mt(e,t){var r=e._controlledReadableStream;if(!0===Fr(r)&&Q(r)>0)X(r,t,!1);else{var n=void 0;try{n=e._strategySizeAlgorithm(t)}catch(i){throw Ft(e,i),i}try{ue(e,t,n)}catch(a){throw Ft(e,a),a}}Et(e)}function Ft(e,t){var r=e._controlledReadableStream;"readable"===r._state&&(le(e),Ot(e),jr(r,t))}function Nt(e){var t=e._controlledReadableStream,r=t._state;return"errored"===r?null:"closed"===r?0:e._strategyHWM-e._queueTotalSize}function Dt(e){return!0!==Lt(e)}function jt(e){var t=e._controlledReadableStream._state;return!1===e._closeRequested&&"readable"===t}function Ut(e,t,r,n,i,a,o){t._controlledReadableStream=e,t._queue=void 0,t._queueTotalSize=void 0,le(t),t._started=!1,t._closeRequested=!1,t._pullAgain=!1,t._pulling=!1,t._strategySizeAlgorithm=o,t._strategyHWM=a,t._pullAlgorithm=n,t._cancelAlgorithm=i,e._readableStreamController=t;var s=r();P(A(s),(function(){t._started=!0,Et(t)}),(function(e){Ft(t,e)}))}function qt(e,t,r,n){var i=Object.create(Tt.prototype);function a(){return f(t,"start",[i])}var o=h(t,"pull",0,[i]),s=h(t,"cancel",1,[]);Ut(e,i,a,o,s,r,n)}function Bt(e){return new TypeError("ReadableStreamDefaultController.prototype."+e+" can only be used on a ReadableStreamDefaultController")}function Wt(e,t){var r,n,i,a,s,u=G(e),c=!1,l=!1,h=!1,f=S((function(e){s=e}));function d(){if(!0===c)return A(void 0);c=!0;var e=R(te(u),(function(e){c=!1;var t=e.done;if(!0===t)return!1===l&&It(i._readableStreamController),void(!1===h&&It(a._readableStreamController));var r=e.value,n=r,o=r;!1===l&&Mt(i._readableStreamController,n),!1===h&&Mt(a._readableStreamController,o)}));return E(e),A(void 0)}function p(t){if(l=!0,r=t,!0===h){var i=o([r,n]),a=Nr(e,i);s(a)}return f}function v(t){if(h=!0,n=t,!0===l){var i=o([r,n]),a=Nr(e,i);s(a)}return f}function g(){}return i=Or(g,d,p),a=Or(g,d,v),T(u._closedPromise,(function(e){Ft(i._readableStreamController,e),Ft(a._readableStreamController,e)})),[i,a]}var Vt=Number.isInteger||function(e){return"number"===typeof e&&isFinite(e)&&Math.floor(e)===e},zt=function(){function e(){throw new TypeError("ReadableStreamBYOBRequest cannot be used directly")}return Object.defineProperty(e.prototype,"view",{get:function(){if(!1===Yt(this))throw _r("view");return this._view},enumerable:!0,configurable:!0}),e.prototype.respond=function(e){if(!1===Yt(this))throw _r("respond");if(void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");v(this._view.buffer),vr(this._associatedReadableByteStreamController,e)},e.prototype.respondWithNewView=function(e){if(!1===Yt(this))throw _r("respond");if(void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");if(!ArrayBuffer.isView(e))throw new TypeError("You can only respond with array buffer views");v(e.buffer),gr(this._associatedReadableByteStreamController,e)},e}(),Ht=function(){function e(){throw new TypeError("ReadableByteStreamController constructor cannot be used directly")}return Object.defineProperty(e.prototype,"byobRequest",{get:function(){if(!1===Gt(this))throw wr("byobRequest");if(void 0===this._byobRequest&&this._pendingPullIntos.length>0){var e=this._pendingPullIntos.peek(),t=new Uint8Array(e.buffer,e.byteOffset+e.bytesFilled,e.byteLength-e.bytesFilled),r=Object.create(zt.prototype);br(r,this,t),this._byobRequest=r}return this._byobRequest},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"desiredSize",{get:function(){if(!1===Gt(this))throw wr("desiredSize");return pr(this)},enumerable:!0,configurable:!0}),e.prototype.close=function(){if(!1===Gt(this))throw wr("close");if(!0===this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");var e=this._controlledReadableByteStream._state;if("readable"!==e)throw new TypeError("The stream (in "+e+" state) is not in the readable state and cannot be closed");hr(this)},e.prototype.enqueue=function(e){if(!1===Gt(this))throw wr("enqueue");if(!0===this._closeRequested)throw new TypeError("stream is closed or draining");var t=this._controlledReadableByteStream._state;if("readable"!==t)throw new TypeError("The stream (in "+t+" state) is not in the readable state and cannot be enqueued to");if(!ArrayBuffer.isView(e))throw new TypeError("You can only enqueue array buffer views when using a ReadableByteStreamController");v(e.buffer),fr(this,e)},e.prototype.error=function(e){if(!1===Gt(this))throw wr("error");dr(this,e)},e.prototype[z]=function(e){if(this._pendingPullIntos.length>0){var t=this._pendingPullIntos.peek();t.bytesFilled=0}le(this);var r=this._cancelAlgorithm(e);return lr(this),r},e.prototype[H]=function(){var e=this._controlledReadableByteStream;if(this._queueTotalSize>0){var t=this._queue.shift();this._queueTotalSize-=t.byteLength,tr(this);var r=void 0;try{r=new Uint8Array(t.buffer,t.byteOffset,t.byteLength)}catch(s){return k(s)}return A(I(r,!1,e._reader._forAuthorCode))}var n=this._autoAllocateChunkSize;if(void 0!==n){var i=void 0;try{i=new ArrayBuffer(n)}catch(u){return k(u)}var a={buffer:i,byteOffset:0,byteLength:n,bytesFilled:0,elementSize:1,ctor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(a)}var o=Y(e);return Xt(this),o},e}();function Gt(e){return!!a(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableByteStream")}function Yt(e){return!!a(e)&&!!Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController")}function Xt(e){var t=cr(e);if(!1!==t)if(!0!==e._pulling){e._pulling=!0;var r=e._pullAlgorithm();P(r,(function(){e._pulling=!1,!0===e._pullAgain&&(e._pullAgain=!1,Xt(e))}),(function(t){dr(e,t)}))}else e._pullAgain=!0}function Qt(e){rr(e),e._pendingPullIntos=new O}function Kt(e,t){var r=!1;"closed"===e._state&&(r=!0);var n=Jt(t);"default"===t.readerType?X(e,n,r):kr(e,n,r)}function Jt(e){var t=e.bytesFilled,r=e.elementSize;return new e.ctor(e.buffer,e.byteOffset,t/r)}function $t(e,t,r,n){e._queue.push({buffer:t,byteOffset:r,byteLength:n}),e._queueTotalSize+=n}function Zt(e,t){var r=t.elementSize,n=t.bytesFilled-t.bytesFilled%r,i=Math.min(e._queueTotalSize,t.byteLength-t.bytesFilled),a=t.bytesFilled+i,o=a-a%r,u=i,c=!1;o>n&&(u=o-t.bytesFilled,c=!0);var l=e._queue;while(u>0){var h=l.peek(),f=Math.min(u,h.byteLength),d=t.byteOffset+t.bytesFilled;s(t.buffer,d,h.buffer,h.byteOffset,f),h.byteLength===f?l.shift():(h.byteOffset+=f,h.byteLength-=f),e._queueTotalSize-=f,er(e,f,t),u-=f}return c}function er(e,t,r){rr(e),r.bytesFilled+=t}function tr(e){0===e._queueTotalSize&&!0===e._closeRequested?(lr(e),Dr(e._controlledReadableByteStream)):Xt(e)}function rr(e){void 0!==e._byobRequest&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=void 0,e._byobRequest=void 0)}function nr(e){while(e._pendingPullIntos.length>0){if(0===e._queueTotalSize)return;var t=e._pendingPullIntos.peek();!0===Zt(e,t)&&(ur(e),Kt(e._controlledReadableByteStream,t))}}function ir(e,t){var r=e._controlledReadableByteStream,n=1;t.constructor!==DataView&&(n=t.constructor.BYTES_PER_ELEMENT);var i=t.constructor,a=p(t.buffer),o={buffer:a,byteOffset:t.byteOffset,byteLength:t.byteLength,bytesFilled:0,elementSize:n,ctor:i,readerType:"byob"};if(e._pendingPullIntos.length>0)return e._pendingPullIntos.push(o),Ar(r);if("closed"===r._state){var s=new i(o.buffer,o.byteOffset,0);return A(I(s,!0,r._reader._forAuthorCode))}if(e._queueTotalSize>0){if(!0===Zt(e,o)){var u=Jt(o);return tr(e),A(I(u,!1,r._reader._forAuthorCode))}if(!0===e._closeRequested){var c=new TypeError("Insufficient bytes to fill elements in the given buffer");return dr(e,c),k(c)}}e._pendingPullIntos.push(o);var l=Ar(r);return Xt(e),l}function ar(e,t){t.buffer=p(t.buffer);var r=e._controlledReadableByteStream;if(!0===Pr(r))while(xr(r)>0){var n=ur(e);Kt(r,n)}}function or(e,t,r){if(r.bytesFilled+t>r.byteLength)throw new RangeError("bytesWritten out of range");if(er(e,t,r),!(r.bytesFilled<r.elementSize)){ur(e);var n=r.bytesFilled%r.elementSize;if(n>0){var i=r.byteOffset+r.bytesFilled,a=r.buffer.slice(i-n,i);$t(e,a,0,a.byteLength)}r.buffer=p(r.buffer),r.bytesFilled-=n,Kt(e._controlledReadableByteStream,r),nr(e)}}function sr(e,t){var r=e._pendingPullIntos.peek(),n=e._controlledReadableByteStream;if("closed"===n._state){if(0!==t)throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream");ar(e,r)}else or(e,t,r);Xt(e)}function ur(e){var t=e._pendingPullIntos.shift();return rr(e),t}function cr(e){var t=e._controlledReadableByteStream;if("readable"!==t._state)return!1;if(!0===e._closeRequested)return!1;if(!1===e._started)return!1;if(!0===K(t)&&Q(t)>0)return!0;if(!0===Pr(t)&&xr(t)>0)return!0;var r=pr(e);return r>0}function lr(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}function hr(e){var t=e._controlledReadableByteStream;if(e._queueTotalSize>0)e._closeRequested=!0;else{if(e._pendingPullIntos.length>0){var r=e._pendingPullIntos.peek();if(r.bytesFilled>0){var n=new TypeError("Insufficient bytes to fill elements in the given buffer");throw dr(e,n),n}}lr(e),Dr(t)}}function fr(e,t){var r=e._controlledReadableByteStream,n=t.buffer,i=t.byteOffset,a=t.byteLength,o=p(n);if(!0===K(r))if(0===Q(r))$t(e,o,i,a);else{var s=new Uint8Array(o,i,a);X(r,s,!1)}else!0===Pr(r)?($t(e,o,i,a),nr(e)):$t(e,o,i,a);Xt(e)}function dr(e,t){var r=e._controlledReadableByteStream;"readable"===r._state&&(Qt(e),le(e),lr(e),jr(r,t))}function pr(e){var t=e._controlledReadableByteStream,r=t._state;return"errored"===r?null:"closed"===r?0:e._strategyHWM-e._queueTotalSize}function vr(e,t){if(t=Number(t),!1===u(t))throw new RangeError("bytesWritten must be a finite");sr(e,t)}function gr(e,t){var r=e._pendingPullIntos.peek();if(r.byteOffset+r.bytesFilled!==t.byteOffset)throw new RangeError("The region specified by view does not match byobRequest");if(r.byteLength!==t.byteLength)throw new RangeError("The buffer of view has different capacity than byobRequest");r.buffer=t.buffer,sr(e,t.byteLength)}function yr(e,t,r,n,i,a,o){t._controlledReadableByteStream=e,t._pullAgain=!1,t._pulling=!1,t._byobRequest=void 0,t._queue=t._queueTotalSize=void 0,le(t),t._closeRequested=!1,t._started=!1,t._strategyHWM=g(a),t._pullAlgorithm=n,t._cancelAlgorithm=i,t._autoAllocateChunkSize=o,t._pendingPullIntos=new O,e._readableStreamController=t;var s=r();P(A(s),(function(){t._started=!0,Xt(t)}),(function(e){dr(t,e)}))}function mr(e,t,r){var n=Object.create(Ht.prototype);function i(){return f(t,"start",[n])}var a=h(t,"pull",0,[n]),o=h(t,"cancel",1,[]),s=t.autoAllocateChunkSize;if(void 0!==s&&(s=Number(s),!1===Vt(s)||s<=0))throw new RangeError("autoAllocateChunkSize must be a positive integer");yr(e,n,i,a,o,r,s)}function br(e,t,r){e._associatedReadableByteStreamController=t,e._view=r}function _r(e){return new TypeError("ReadableStreamBYOBRequest.prototype."+e+" can only be used on a ReadableStreamBYOBRequest")}function wr(e){return new TypeError("ReadableByteStreamController.prototype."+e+" can only be used on a ReadableByteStreamController")}function Sr(e,t){void 0===t&&(t=!1);var r=new Cr(e);return r._forAuthorCode=t,r}function Ar(e){var t=S((function(t,r){var n={_resolve:t,_reject:r};e._reader._readIntoRequests.push(n)}));return t}function kr(e,t,r){var n=e._reader,i=n._readIntoRequests.shift();i._resolve(I(t,r,n._forAuthorCode))}function xr(e){return e._reader._readIntoRequests.length}function Pr(e){var t=e._reader;return void 0!==t&&!!Tr(t)}var Cr=function(){function e(e){if(!Mr(e))throw new TypeError("ReadableStreamBYOBReader can only be constructed with a ReadableStream instance given a byte source");if(!1===Gt(e._readableStreamController))throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");if(Fr(e))throw new TypeError("This stream has already been locked for exclusive reading by another reader");M(this,e),this._readIntoRequests=new O}return Object.defineProperty(e.prototype,"closed",{get:function(){return Tr(this)?this._closedPromise:k(Er("closed"))},enumerable:!0,configurable:!0}),e.prototype.cancel=function(e){return Tr(this)?void 0===this._ownerReadableStream?k(D("cancel")):F(this,e):k(Er("cancel"))},e.prototype.read=function(e){return Tr(this)?void 0===this._ownerReadableStream?k(D("read from")):ArrayBuffer.isView(e)?(v(e.buffer),0===e.byteLength?k(new TypeError("view must have non-zero byteLength")):Rr(this,e)):k(new TypeError("view must be an array buffer view")):k(Er("read"))},e.prototype.releaseLock=function(){if(!Tr(this))throw Er("releaseLock");if(void 0!==this._ownerReadableStream){if(this._readIntoRequests.length>0)throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");N(this)}},e}();function Tr(e){return!!a(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readIntoRequests")}function Rr(e,t){var r=e._ownerReadableStream;return r._disturbed=!0,"errored"===r._state?k(r._storedError):ir(r._readableStreamController,t)}function Er(e){return new TypeError("ReadableStreamBYOBReader.prototype."+e+" can only be used on a ReadableStreamBYOBReader")}var Lr=function(){function e(e,t){void 0===e&&(e={}),void 0===t&&(t={}),Ir(this);var r=t.size,n=t.highWaterMark,i=e.type,a=String(i);if("bytes"===a){if(void 0!==r)throw new RangeError("The strategy for a byte stream cannot have a size function");void 0===n&&(n=0),n=g(n),mr(this,e,n)}else{if(void 0!==i)throw new RangeError("Invalid type is specified");var o=y(r);void 0===n&&(n=1),n=g(n),qt(this,e,n,o)}}return Object.defineProperty(e.prototype,"locked",{get:function(){if(!1===Mr(this))throw Ur("locked");return Fr(this)},enumerable:!0,configurable:!0}),e.prototype.cancel=function(e){return!1===Mr(this)?k(Ur("cancel")):!0===Fr(this)?k(new TypeError("Cannot cancel a stream that already has a reader")):Nr(this,e)},e.prototype.getReader=function(e){var t=(void 0===e?{}:e).mode;if(!1===Mr(this))throw Ur("getReader");if(void 0===t)return G(this,!0);if(t=String(t),"byob"===t)return Sr(this,!0);throw new RangeError("Invalid mode is specified")},e.prototype.pipeThrough=function(e,t){var r=e.writable,n=e.readable,i=void 0===t?{}:t,a=i.preventClose,o=i.preventAbort,s=i.preventCancel,u=i.signal;if(!1===Mr(this))throw Ur("pipeThrough");if(!1===ye(r))throw new TypeError("writable argument to pipeThrough must be a WritableStream");if(!1===Mr(n))throw new TypeError("readable argument to pipeThrough must be a ReadableStream");if(a=Boolean(a),o=Boolean(o),s=Boolean(s),void 0!==u&&!St(u))throw new TypeError("ReadableStream.prototype.pipeThrough's signal option must be an AbortSignal");if(!0===Fr(this))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(!0===me(r))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");var c=Ct(this,r,a,o,s,u);return E(c),n},e.prototype.pipeTo=function(e,t){var r=void 0===t?{}:t,n=r.preventClose,i=r.preventAbort,a=r.preventCancel,o=r.signal;return!1===Mr(this)?k(Ur("pipeTo")):!1===ye(e)?k(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream")):(n=Boolean(n),i=Boolean(i),a=Boolean(a),void 0===o||St(o)?!0===Fr(this)?k(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):!0===me(e)?k(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):Ct(this,e,n,i,a,o):k(new TypeError("ReadableStream.prototype.pipeTo's signal option must be an AbortSignal")))},e.prototype.tee=function(){if(!1===Mr(this))throw Ur("tee");var e=Wt(this);return o(e)},e.prototype.getIterator=function(e){var t=(void 0===e?{}:e).preventCancel,r=void 0!==t&&t;if(!1===Mr(this))throw Ur("getIterator");return ie(this,r)},e}();function Or(e,t,r,n,i){void 0===n&&(n=1),void 0===i&&(i=function(){return 1});var a=Object.create(Lr.prototype);Ir(a);var o=Object.create(Tt.prototype);return Ut(a,o,e,t,r,n,i),a}function Ir(e){e._state="readable",e._reader=void 0,e._storedError=void 0,e._disturbed=!1}function Mr(e){return!!a(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readableStreamController")}function Fr(e){return void 0!==e._reader}function Nr(e,t){if(e._disturbed=!0,"closed"===e._state)return A(void 0);if("errored"===e._state)return k(e._storedError);Dr(e);var n=e._readableStreamController[z](t);return R(n,r)}function Dr(e){e._state="closed";var t=e._reader;void 0!==t&&(ee(t)&&(t._readRequests.forEach((function(e){e._resolve(I(void 0,!0,t._forAuthorCode))})),t._readRequests=new O),V(t))}function jr(e,t){e._state="errored",e._storedError=t;var r=e._reader;void 0!==r&&(ee(r)?(r._readRequests.forEach((function(e){e._reject(t)})),r._readRequests=new O):(r._readIntoRequests.forEach((function(e){e._reject(t)})),r._readIntoRequests=new O),B(r,t))}function Ur(e){return new TypeError("ReadableStream.prototype."+e+" can only be used on a ReadableStream")}"symbol"===typeof t.asyncIterator&&Object.defineProperty(Lr.prototype,t.asyncIterator,{value:Lr.prototype.getIterator,enumerable:!1,writable:!0,configurable:!0});var qr=function(){function e(e){var t=e.highWaterMark;this.highWaterMark=t}return e.prototype.size=function(e){return e.byteLength},e}(),Br=function(){function e(e){var t=e.highWaterMark;this.highWaterMark=t}return e.prototype.size=function(){return 1},e}(),Wr=function(){function e(e,t,r){void 0===e&&(e={}),void 0===t&&(t={}),void 0===r&&(r={});var n=t.size,i=t.highWaterMark,a=r.size,o=r.highWaterMark,s=e.writableType;if(void 0!==s)throw new RangeError("Invalid writable type specified");var u=y(n);void 0===i&&(i=1),i=g(i);var c=e.readableType;if(void 0!==c)throw new RangeError("Invalid readable type specified");var l,h=y(a);void 0===o&&(o=0),o=g(o);var d=S((function(e){l=e}));Vr(this,d,i,u,o,h),Jr(this,e);var p=f(e,"start",[this._transformStreamController]);l(p)}return Object.defineProperty(e.prototype,"readable",{get:function(){if(!1===zr(this))throw cn("readable");return this._readable},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"writable",{get:function(){if(!1===zr(this))throw cn("writable");return this._writable},enumerable:!0,configurable:!0}),e}();function Vr(e,t,r,n,i,a){function o(){return t}function s(t){return nn(e,t)}function u(t){return an(e,t)}function c(){return on(e)}function l(){return sn(e)}function h(t){return Gr(e,t),A(void 0)}e._writable=ve(o,s,c,u,r,n),e._readable=Or(o,l,h,i,a),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,Yr(e,!0),e._transformStreamController=void 0}function zr(e){return!!a(e)&&!!Object.prototype.hasOwnProperty.call(e,"_transformStreamController")}function Hr(e,t){Ft(e._readable._readableStreamController,t),Gr(e,t)}function Gr(e,t){$r(e._transformStreamController),tt(e._writable._writableStreamController,t),!0===e._backpressure&&Yr(e,!1)}function Yr(e,t){void 0!==e._backpressureChangePromise&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=S((function(t){e._backpressureChangePromise_resolve=t})),e._backpressure=t}var Xr=function(){function e(){throw new TypeError("TransformStreamDefaultController instances cannot be created directly")}return Object.defineProperty(e.prototype,"desiredSize",{get:function(){if(!1===Qr(this))throw un("desiredSize");var e=this._controlledTransformStream._readable._readableStreamController;return Nt(e)},enumerable:!0,configurable:!0}),e.prototype.enqueue=function(e){if(!1===Qr(this))throw un("enqueue");Zr(this,e)},e.prototype.error=function(e){if(!1===Qr(this))throw un("error");en(this,e)},e.prototype.terminate=function(){if(!1===Qr(this))throw un("terminate");rn(this)},e}();function Qr(e){return!!a(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream")}function Kr(e,t,r,n){t._controlledTransformStream=e,e._transformStreamController=t,t._transformAlgorithm=r,t._flushAlgorithm=n}function Jr(e,t){var r=Object.create(Xr.prototype),n=function(e){try{return Zr(r,e),A(void 0)}catch(t){return k(t)}},i=t.transform;if(void 0!==i){if("function"!==typeof i)throw new TypeError("transform is not a method");n=function(e){return d(i,t,[e,r])}}var a=h(t,"flush",0,[r]);Kr(e,r,n,a)}function $r(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0}function Zr(e,t){var r=e._controlledTransformStream,n=r._readable._readableStreamController;if(!1===jt(n))throw new TypeError("Readable side is not in a state that permits enqueue");try{Mt(n,t)}catch(a){throw Gr(r,a),r._readable._storedError}var i=Dt(n);i!==r._backpressure&&Yr(r,!0)}function en(e,t){Hr(e._controlledTransformStream,t)}function tn(e,t){var r=e._transformAlgorithm(t);return R(r,void 0,(function(t){throw Hr(e._controlledTransformStream,t),t}))}function rn(e){var t=e._controlledTransformStream,r=t._readable._readableStreamController;!0===jt(r)&&It(r);var n=new TypeError("TransformStream terminated");Gr(t,n)}function nn(e,t){var r=e._transformStreamController;if(!0===e._backpressure){var n=e._backpressureChangePromise;return R(n,(function(){var n=e._writable,i=n._state;if("erroring"===i)throw n._storedError;return tn(r,t)}))}return tn(r,t)}function an(e,t){return Hr(e,t),A(void 0)}function on(e){var t=e._readable,r=e._transformStreamController,n=r._flushAlgorithm();return $r(r),R(n,(function(){if("errored"===t._state)throw t._storedError;var e=t._readableStreamController;!0===jt(e)&&It(e)}),(function(r){throw Hr(e,r),t._storedError}))}function sn(e){return Yr(e,!1),e._backpressureChangePromise}function un(e){return new TypeError("TransformStreamDefaultController.prototype."+e+" can only be used on a TransformStreamDefaultController")}function cn(e){return new TypeError("TransformStream.prototype."+e+" can only be used on a TransformStream")}e.ByteLengthQueuingStrategy=qr,e.CountQueuingStrategy=Br,e.ReadableStream=Lr,e.TransformStream=Wr,e.WritableStream=de,Object.defineProperty(e,"__esModule",{value:!0})}))},function(e,t,r){r(147),r(118),r(77),r(120);var n=r(43);e.exports=n.Map},function(e,t,r){"use strict";var n=r(148),i=r(152);e.exports=n("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),i)},function(e,t,r){"use strict";var n=r(10),i=r(11),a=r(52),o=r(29),s=r(149),u=r(104),c=r(126),l=r(22),h=r(14),f=r(97),d=r(85),p=r(151);e.exports=function(e,t,r){var v=-1!==e.indexOf("Map"),g=-1!==e.indexOf("Weak"),y=v?"set":"add",m=i[e],b=m&&m.prototype,_=m,w={},S=function(e){var t=b[e];o(b,e,"add"==e?function(e){return t.call(this,0===e?0:e),this}:"delete"==e?function(e){return!(g&&!l(e))&&t.call(this,0===e?0:e)}:"get"==e?function(e){return g&&!l(e)?void 0:t.call(this,0===e?0:e)}:"has"==e?function(e){return!(g&&!l(e))&&t.call(this,0===e?0:e)}:function(e,r){return t.call(this,0===e?0:e,r),this})};if(a(e,"function"!=typeof m||!(g||b.forEach&&!h((function(){(new m).entries().next()})))))_=r.getConstructor(t,e,v,y),s.REQUIRED=!0;else if(a(e,!0)){var A=new _,k=A[y](g?{}:-0,1)!=A,x=h((function(){A.has(1)})),P=f((function(e){new m(e)})),C=!g&&h((function(){var e=new m,t=5;while(t--)e[y](t,t);return!e.has(-0)}));P||(_=t((function(t,r){c(t,_,e);var n=p(new m,t,_);return void 0!=r&&u(r,n[y],n,v),n})),_.prototype=b,b.constructor=_),(x||C)&&(S("delete"),S("has"),v&&S("get")),(C||k)&&S(y),g&&b.clear&&delete b.clear}return w[e]=_,n({global:!0,forced:_!=m},w),d(_,e),g||r.setStrong(_,e,v),_}},function(e,t,r){var n=r(39),i=r(22),a=r(23),o=r(27).f,s=r(38),u=r(150),c=s("meta"),l=0,h=Object.isExtensible||function(){return!0},f=function(e){o(e,c,{value:{objectID:"O"+ ++l,weakData:{}}})},d=function(e,t){if(!i(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!a(e,c)){if(!h(e))return"F";if(!t)return"E";f(e)}return e[c].objectID},p=function(e,t){if(!a(e,c)){if(!h(e))return!0;if(!t)return!1;f(e)}return e[c].weakData},v=function(e){return u&&g.REQUIRED&&h(e)&&!a(e,c)&&f(e),e},g=e.exports={REQUIRED:!1,fastKey:d,getWeakData:p,onFreeze:v};n[c]=!0},function(e,t,r){var n=r(14);e.exports=!n((function(){return Object.isExtensible(Object.preventExtensions({}))}))},function(e,t,r){var n=r(22),i=r(87);e.exports=function(e,t,r){var a,o;return i&&"function"==typeof(a=t.constructor)&&a!==r&&n(o=a.prototype)&&o!==r.prototype&&i(e,o),e}},function(e,t,r){"use strict";var n=r(27).f,i=r(71),a=r(124),o=r(62),s=r(126),u=r(104),c=r(79),l=r(125),h=r(13),f=r(149).fastKey,d=r(33),p=d.set,v=d.getterFor;e.exports={getConstructor:function(e,t,r,c){var l=e((function(e,n){s(e,l,t),p(e,{type:t,index:i(null),first:void 0,last:void 0,size:0}),h||(e.size=0),void 0!=n&&u(n,e[c],e,r)})),d=v(t),g=function(e,t,r){var n,i,a=d(e),o=y(e,t);return o?o.value=r:(a.last=o={index:i=f(t,!0),key:t,value:r,previous:n=a.last,next:void 0,removed:!1},a.first||(a.first=o),n&&(n.next=o),h?a.size++:e.size++,"F"!==i&&(a.index[i]=o)),e},y=function(e,t){var r,n=d(e),i=f(t);if("F"!==i)return n.index[i];for(r=n.first;r;r=r.next)if(r.key==t)return r};return a(l.prototype,{clear:function(){var e=this,t=d(e),r=t.index,n=t.first;while(n)n.removed=!0,n.previous&&(n.previous=n.previous.next=void 0),delete r[n.index],n=n.next;t.first=t.last=void 0,h?t.size=0:e.size=0},delete:function(e){var t=this,r=d(t),n=y(t,e);if(n){var i=n.next,a=n.previous;delete r.index[n.index],n.removed=!0,a&&(a.next=i),i&&(i.previous=a),r.first==n&&(r.first=i),r.last==n&&(r.last=a),h?r.size--:t.size--}return!!n},forEach:function(e){var t,r=d(this),n=o(e,arguments.length>1?arguments[1]:void 0,3);while(t=t?t.next:r.first){n(t.value,t.key,this);while(t&&t.removed)t=t.previous}},has:function(e){return!!y(this,e)}}),a(l.prototype,r?{get:function(e){var t=y(this,e);return t&&t.value},set:function(e,t){return g(this,0===e?0:e,t)}}:{add:function(e){return g(this,e=0===e?0:e,e)}}),h&&n(l.prototype,"size",{get:function(){return d(this).size}}),l},setStrong:function(e,t,r){var n=t+" Iterator",i=v(t),a=v(n);c(e,t,(function(e,t){p(this,{type:n,target:e,state:i(e),kind:t,last:void 0})}),(function(){var e=a(this),t=e.kind,r=e.last;while(r&&r.removed)r=r.previous;return e.target&&(e.last=r=r?r.next:e.state.first)?"keys"==t?{value:r.key,done:!1}:"values"==t?{value:r.value,done:!1}:{value:[r.key,r.value],done:!1}:(e.target=void 0,{value:void 0,done:!0})}),r?"entries":"values",!r,!0),l(t)}}},function(e,t,r){r(154),r(118),r(77),r(120);var n=r(43);e.exports=n.Set},function(e,t,r){"use strict";var n=r(148),i=r(152);e.exports=n("Set",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),i)},function(e,t,r){r(118),r(156),r(120);var n=r(43);e.exports=n.WeakMap},function(e,t,r){"use strict";var n,i=r(11),a=r(124),o=r(149),s=r(148),u=r(157),c=r(22),l=r(33).enforce,h=r(34),f=!i.ActiveXObject&&"ActiveXObject"in i,d=Object.isExtensible,p=function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}},v=e.exports=s("WeakMap",p,u);if(h&&f){n=u.getConstructor(p,"WeakMap",!0),o.REQUIRED=!0;var g=v.prototype,y=g["delete"],m=g.has,b=g.get,_=g.set;a(g,{delete:function(e){if(c(e)&&!d(e)){var t=l(this);return t.frozen||(t.frozen=new n),y.call(this,e)||t.frozen["delete"](e)}return y.call(this,e)},has:function(e){if(c(e)&&!d(e)){var t=l(this);return t.frozen||(t.frozen=new n),m.call(this,e)||t.frozen.has(e)}return m.call(this,e)},get:function(e){if(c(e)&&!d(e)){var t=l(this);return t.frozen||(t.frozen=new n),m.call(this,e)?b.call(this,e):t.frozen.get(e)}return b.call(this,e)},set:function(e,t){if(c(e)&&!d(e)){var r=l(this);r.frozen||(r.frozen=new n),m.call(this,e)?_.call(this,e,t):r.frozen.set(e,t)}else _.call(this,e,t);return this}})}},function(e,t,r){"use strict";var n=r(124),i=r(149).getWeakData,a=r(28),o=r(22),s=r(126),u=r(104),c=r(158),l=r(23),h=r(33),f=h.set,d=h.getterFor,p=c.find,v=c.findIndex,g=0,y=function(e){return e.frozen||(e.frozen=new m)},m=function(){this.entries=[]},b=function(e,t){return p(e.entries,(function(e){return e[0]===t}))};m.prototype={get:function(e){var t=b(this,e);if(t)return t[1]},has:function(e){return!!b(this,e)},set:function(e,t){var r=b(this,e);r?r[1]=t:this.entries.push([e,t])},delete:function(e){var t=v(this.entries,(function(t){return t[0]===e}));return~t&&this.entries.splice(t,1),!!~t}},e.exports={getConstructor:function(e,t,r,c){var h=e((function(e,n){s(e,h,t),f(e,{type:t,id:g++,frozen:void 0}),void 0!=n&&u(n,e[c],e,r)})),p=d(t),v=function(e,t,r){var n=p(e),o=i(a(t),!0);return!0===o?y(n).set(t,r):o[n.id]=r,e};return n(h.prototype,{delete:function(e){var t=p(this);if(!o(e))return!1;var r=i(e);return!0===r?y(t)["delete"](e):r&&l(r,t.id)&&delete r[t.id]},has:function(e){var t=p(this);if(!o(e))return!1;var r=i(e);return!0===r?y(t).has(e):r&&l(r,t.id)}}),n(h.prototype,r?{get:function(e){var t=p(this);if(o(e)){var r=i(e);return!0===r?y(t).get(e):r?r[t.id]:void 0}},set:function(e,t){return v(this,e,t)}}:{add:function(e){return v(this,e,!0)}}),h}}},function(e,t,r){var n=r(62),i=r(18),a=r(83),o=r(47),s=r(159),u=[].push,c=function(e){var t=1==e,r=2==e,c=3==e,l=4==e,h=6==e,f=5==e||h;return function(d,p,v,g){for(var y,m,b=a(d),_=i(b),w=n(p,v,3),S=o(_.length),A=0,k=g||s,x=t?k(d,S):r?k(d,0):void 0;S>A;A++)if((f||A in _)&&(y=_[A],m=w(y,A,b),e))if(t)x[A]=m;else if(m)switch(e){case 3:return!0;case 5:return y;case 6:return A;case 2:u.call(x,y)}else if(l)return!1;return h?-1:c||l?l:x}};e.exports={forEach:c(0),map:c(1),filter:c(2),some:c(3),every:c(4),find:c(5),findIndex:c(6)}},function(e,t,r){var n=r(22),i=r(160),a=r(57),o=a("species");e.exports=function(e,t){var r;return i(e)&&(r=e.constructor,"function"!=typeof r||r!==Array&&!i(r.prototype)?n(r)&&(r=r[o],null===r&&(r=void 0)):r=void 0),new(void 0===r?Array:r)(0===t?0:t)}},function(e,t,r){var n=r(19);e.exports=Array.isArray||function(e){return"Array"==n(e)}},function(e,t,r){r(118),r(162),r(120);var n=r(43);e.exports=n.WeakSet},function(e,t,r){"use strict";var n=r(148),i=r(157);n("WeakSet",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),i)},function(e,t,r){r(164);var n=r(61);e.exports=n("String","codePointAt")},function(e,t,r){"use strict";var n=r(10),i=r(78).codeAt;n({target:"String",proto:!0},{codePointAt:function(e){return i(this,e)}})},function(e,t,r){r(166);var n=r(43);e.exports=n.String.fromCodePoint},function(e,t,r){var n=r(10),i=r(49),a=String.fromCharCode,o=String.fromCodePoint,s=!!o&&1!=o.length;n({target:"String",stat:!0,forced:s},{fromCodePoint:function(e){var t,r=[],n=arguments.length,o=0;while(n>o){if(t=+arguments[o++],i(t,1114111)!==t)throw RangeError(t+" is not a valid code point");r.push(t<65536?a(t):a(55296+((t-=65536)>>10),t%1024+56320))}return r.join("")}})},function(e,t,r){r(168),r(118),r(170),r(174),r(175),r(176),r(177),r(178),r(179),r(180),r(181),r(182),r(183),r(184),r(185),r(186),r(187),r(188),r(189);var n=r(43);e.exports=n.Symbol},function(e,t,r){"use strict";var n=r(10),i=r(14),a=r(160),o=r(22),s=r(83),u=r(47),c=r(93),l=r(159),h=r(169),f=r(57),d=r(135),p=f("isConcatSpreadable"),v=9007199254740991,g="Maximum allowed index exceeded",y=d>=51||!i((function(){var e=[];return e[p]=!1,e.concat()[0]!==e})),m=h("concat"),b=function(e){if(!o(e))return!1;var t=e[p];return void 0!==t?!!t:a(e)},_=!y||!m;n({target:"Array",proto:!0,forced:_},{concat:function(e){var t,r,n,i,a,o=s(this),h=l(o,0),f=0;for(t=-1,n=arguments.length;t<n;t++)if(a=-1===t?o:arguments[t],b(a)){if(i=u(a.length),f+i>v)throw TypeError(g);for(r=0;r<i;r++,f++)r in a&&c(h,f,a[r])}else{if(f>=v)throw TypeError(g);c(h,f++,a)}return h.length=f,h}})},function(e,t,r){var n=r(14),i=r(57),a=r(135),o=i("species");e.exports=function(e){return a>=51||!n((function(){var t=[],r=t.constructor={};return r[o]=function(){return{foo:1}},1!==t[e](Boolean).foo}))}},function(e,t,r){"use strict";var n=r(10),i=r(11),a=r(42),o=r(37),s=r(13),u=r(58),c=r(59),l=r(14),h=r(23),f=r(160),d=r(22),p=r(28),v=r(83),g=r(17),y=r(21),m=r(16),b=r(71),_=r(73),w=r(44),S=r(171),A=r(51),k=r(12),x=r(27),P=r(15),C=r(26),T=r(29),R=r(36),E=r(35),L=r(39),O=r(38),I=r(57),M=r(172),F=r(173),N=r(85),D=r(33),j=r(158).forEach,U=E("hidden"),q="Symbol",B="prototype",W=I("toPrimitive"),V=D.set,z=D.getterFor(q),H=Object[B],G=i.Symbol,Y=a("JSON","stringify"),X=k.f,Q=x.f,K=S.f,J=P.f,$=R("symbols"),Z=R("op-symbols"),ee=R("string-to-symbol-registry"),te=R("symbol-to-string-registry"),re=R("wks"),ne=i.QObject,ie=!ne||!ne[B]||!ne[B].findChild,ae=s&&l((function(){return 7!=b(Q({},"a",{get:function(){return Q(this,"a",{value:7}).a}})).a}))?function(e,t,r){var n=X(H,t);n&&delete H[t],Q(e,t,r),n&&e!==H&&Q(H,t,n)}:Q,oe=function(e,t){var r=$[e]=b(G[B]);return V(r,{type:q,tag:e,description:t}),s||(r.description=t),r},se=c?function(e){return"symbol"==typeof e}:function(e){return Object(e)instanceof G},ue=function(e,t,r){e===H&&ue(Z,t,r),p(e);var n=y(t,!0);return p(r),h($,n)?(r.enumerable?(h(e,U)&&e[U][n]&&(e[U][n]=!1),r=b(r,{enumerable:m(0,!1)})):(h(e,U)||Q(e,U,m(1,{})),e[U][n]=!0),ae(e,n,r)):Q(e,n,r)},ce=function(e,t){p(e);var r=g(t),n=_(r).concat(pe(r));return j(n,(function(t){s&&!he.call(r,t)||ue(e,t,r[t])})),e},le=function(e,t){return void 0===t?b(e):ce(b(e),t)},he=function(e){var t=y(e,!0),r=J.call(this,t);return!(this===H&&h($,t)&&!h(Z,t))&&(!(r||!h(this,t)||!h($,t)||h(this,U)&&this[U][t])||r)},fe=function(e,t){var r=g(e),n=y(t,!0);if(r!==H||!h($,n)||h(Z,n)){var i=X(r,n);return!i||!h($,n)||h(r,U)&&r[U][n]||(i.enumerable=!0),i}},de=function(e){var t=K(g(e)),r=[];return j(t,(function(e){h($,e)||h(L,e)||r.push(e)})),r},pe=function(e){var t=e===H,r=K(t?Z:g(e)),n=[];return j(r,(function(e){!h($,e)||t&&!h(H,e)||n.push($[e])})),n};if(u||(G=function(){if(this instanceof G)throw TypeError("Symbol is not a constructor");var e=arguments.length&&void 0!==arguments[0]?String(arguments[0]):void 0,t=O(e),r=function(e){this===H&&r.call(Z,e),h(this,U)&&h(this[U],t)&&(this[U][t]=!1),ae(this,t,m(1,e))};return s&&ie&&ae(H,t,{configurable:!0,set:r}),oe(t,e)},T(G[B],"toString",(function(){return z(this).tag})),T(G,"withoutSetter",(function(e){return oe(O(e),e)})),P.f=he,x.f=ue,k.f=fe,w.f=S.f=de,A.f=pe,M.f=function(e){return oe(I(e),e)},s&&(Q(G[B],"description",{configurable:!0,get:function(){return z(this).description}}),o||T(H,"propertyIsEnumerable",he,{unsafe:!0}))),n({global:!0,wrap:!0,forced:!u,sham:!u},{Symbol:G}),j(_(re),(function(e){F(e)})),n({target:q,stat:!0,forced:!u},{for:function(e){var t=String(e);if(h(ee,t))return ee[t];var r=G(t);return ee[t]=r,te[r]=t,r},keyFor:function(e){if(!se(e))throw TypeError(e+" is not a symbol");if(h(te,e))return te[e]},useSetter:function(){ie=!0},useSimple:function(){ie=!1}}),n({target:"Object",stat:!0,forced:!u,sham:!s},{create:le,defineProperty:ue,defineProperties:ce,getOwnPropertyDescriptor:fe}),n({target:"Object",stat:!0,forced:!u},{getOwnPropertyNames:de,getOwnPropertySymbols:pe}),n({target:"Object",stat:!0,forced:l((function(){A.f(1)}))},{getOwnPropertySymbols:function(e){return A.f(v(e))}}),Y){var ve=!u||l((function(){var e=G();return"[null]"!=Y([e])||"{}"!=Y({a:e})||"{}"!=Y(Object(e))}));n({target:"JSON",stat:!0,forced:ve},{stringify:function(e,t,r){var n,i=[e],a=1;while(arguments.length>a)i.push(arguments[a++]);if(n=t,(d(t)||void 0!==e)&&!se(e))return f(t)||(t=function(e,t){if("function"==typeof n&&(t=n.call(this,e,t)),!se(t))return t}),i[1]=t,Y.apply(null,i)}})}G[B][W]||C(G[B],W,G[B].valueOf),N(G,q),L[U]=!0},function(e,t,r){var n=r(17),i=r(44).f,a={}.toString,o="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],s=function(e){try{return i(e)}catch(t){return o.slice()}};e.exports.f=function(e){return o&&"[object Window]"==a.call(e)?s(e):i(n(e))}},function(e,t,r){var n=r(57);t.f=n},function(e,t,r){var n=r(43),i=r(23),a=r(172),o=r(27).f;e.exports=function(e){var t=n.Symbol||(n.Symbol={});i(t,e)||o(t,e,{value:a.f(e)})}},function(e,t,r){var n=r(173);n("asyncIterator")},function(e,t,r){"use strict";var n=r(10),i=r(13),a=r(11),o=r(23),s=r(22),u=r(27).f,c=r(40),l=a.Symbol;if(i&&"function"==typeof l&&(!("description"in l.prototype)||void 0!==l().description)){var h={},f=function(){var e=arguments.length<1||void 0===arguments[0]?void 0:String(arguments[0]),t=this instanceof f?new l(e):void 0===e?l():l(e);return""===e&&(h[t]=!0),t};c(f,l);var d=f.prototype=l.prototype;d.constructor=f;var p=d.toString,v="Symbol(test)"==String(l("test")),g=/^Symbol\((.*)\)[^)]+$/;u(d,"description",{configurable:!0,get:function(){var e=s(this)?this.valueOf():this,t=p.call(e);if(o(h,e))return"";var r=v?t.slice(7,-1):t.replace(g,"$1");return""===r?void 0:r}}),n({global:!0,forced:!0},{Symbol:f})}},function(e,t,r){var n=r(173);n("hasInstance")},function(e,t,r){var n=r(173);n("isConcatSpreadable")},function(e,t,r){var n=r(173);n("iterator")},function(e,t,r){var n=r(173);n("match")},function(e,t,r){var n=r(173);n("matchAll")},function(e,t,r){var n=r(173);n("replace")},function(e,t,r){var n=r(173);n("search")},function(e,t,r){var n=r(173);n("species")},function(e,t,r){var n=r(173);n("split")},function(e,t,r){var n=r(173);n("toPrimitive")},function(e,t,r){var n=r(173);n("toStringTag")},function(e,t,r){var n=r(173);n("unscopables")},function(e,t,r){var n=r(85);n(Math,"Math",!0)},function(e,t,r){var n=r(11),i=r(85);i(n.JSON,"JSON",!0)},function(e,t,r){r(191);var n=r(61);e.exports=n("String","padStart")},function(e,t,r){"use strict";var n=r(10),i=r(192).start,a=r(194);n({target:"String",proto:!0,forced:a},{padStart:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0)}})},function(e,t,r){var n=r(47),i=r(193),a=r(20),o=Math.ceil,s=function(e){return function(t,r,s){var u,c,l=String(a(t)),h=l.length,f=void 0===s?" ":String(s),d=n(r);return d<=h||""==f?l:(u=d-h,c=i.call(f,o(u/f.length)),c.length>u&&(c=c.slice(0,u)),e?l+c:c+l)}};e.exports={start:s(!1),end:s(!0)}},function(e,t,r){"use strict";var n=r(48),i=r(20);e.exports="".repeat||function(e){var t=String(i(this)),r="",a=n(e);if(a<0||a==1/0)throw RangeError("Wrong number of repetitions");for(;a>0;(a>>>=1)&&(t+=t))1&a&&(r+=t);return r}},function(e,t,r){var n=r(129);e.exports=/Version\/10\.\d+(\.\d+)?( Mobile\/\w+)? Safari\//.test(n)},function(e,t,r){r(196);var n=r(61);e.exports=n("String","padEnd")},function(e,t,r){"use strict";var n=r(10),i=r(192).end,a=r(194);n({target:"String",proto:!0,forced:a},{padEnd:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0)}})},function(e,t,r){r(198);var n=r(43);e.exports=n.Object.values},function(e,t,r){var n=r(10),i=r(199).values;n({target:"Object",stat:!0},{values:function(e){return i(e)}})},function(e,t,r){var n=r(13),i=r(73),a=r(17),o=r(15).f,s=function(e){return function(t){var r,s=a(t),u=i(s),c=u.length,l=0,h=[];while(c>l)r=u[l++],n&&!o.call(s,r)||h.push(e?[r,s[r]]:s[r]);return h}};e.exports={entries:s(!0),values:s(!1)}},function(e,t,r){r(201);var n=r(43);e.exports=n.Object.entries},function(e,t,r){var n=r(10),i=r(199).entries;n({target:"Object",stat:!0},{entries:function(e){return i(e)}})},function(module,exports,__w_pdfjs_require__){"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.getDocument=getDocument,exports.setPDFNetworkStreamFactory=setPDFNetworkStreamFactory,exports.build=exports.version=exports.PDFPageProxy=exports.PDFDocumentProxy=exports.PDFWorker=exports.PDFDataRangeTransport=exports.LoopbackPort=void 0;var _regenerator=_interopRequireDefault(__w_pdfjs_require__(2)),_util=__w_pdfjs_require__(5),_display_utils=__w_pdfjs_require__(1),_font_loader=__w_pdfjs_require__(203),_node_utils=__w_pdfjs_require__(204),_annotation_storage=__w_pdfjs_require__(205),_api_compatibility=__w_pdfjs_require__(206),_canvas=__w_pdfjs_require__(207),_worker_options=__w_pdfjs_require__(209),_is_node=__w_pdfjs_require__(7),_message_handler=__w_pdfjs_require__(210),_metadata=__w_pdfjs_require__(211),_optional_content_config=__w_pdfjs_require__(213),_transport_stream=__w_pdfjs_require__(214),_webgl=__w_pdfjs_require__(215);function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function asyncGeneratorStep(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(c){return void r(c)}s.done?t(u):Promise.resolve(u).then(n,i)}function _asyncToGenerator(e){return function(){var t=this,r=arguments;return new Promise((function(n,i){var a=e.apply(t,r);function o(e){asyncGeneratorStep(a,n,i,o,s,"next",e)}function s(e){asyncGeneratorStep(a,n,i,o,s,"throw",e)}o(void 0)}))}}function _toConsumableArray(e){return _arrayWithoutHoles(e)||_iterableToArray(e)||_unsupportedIterableToArray(e)||_nonIterableSpread()}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _iterableToArray(e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}function _arrayWithoutHoles(e){if(Array.isArray(e))return _arrayLikeToArray(e)}function _createForOfIteratorHelper(e,t){var r;if("undefined"===typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(r=_unsupportedIterableToArray(e))||t&&e&&"number"===typeof e.length){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){r=e[Symbol.iterator]()},n:function(){var e=r.next();return o=e.done,e},e:function(e){s=!0,a=e},f:function(){try{o||null==r["return"]||r["return"]()}finally{if(s)throw a}}}}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function _createClass(e,t,r){return t&&_defineProperties(e.prototype,t),r&&_defineProperties(e,r),e}function _slicedToArray(e,t){return _arrayWithHoles(e)||_iterableToArrayLimit(e,t)||_unsupportedIterableToArray(e,t)||_nonIterableRest()}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){if(e){if("string"===typeof e)return _arrayLikeToArray(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray(e,t):void 0}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function _iterableToArrayLimit(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var r=[],n=!0,i=!1,a=void 0;try{for(var o,s=e[Symbol.iterator]();!(n=(o=s.next()).done);n=!0)if(r.push(o.value),t&&r.length===t)break}catch(u){i=!0,a=u}finally{try{n||null==s["return"]||s["return"]()}finally{if(i)throw a}}return r}}function _arrayWithHoles(e){if(Array.isArray(e))return e}function _typeof(e){return _typeof="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_typeof(e)}var DEFAULT_RANGE_CHUNK_SIZE=65536,RENDERING_CANCELLED_TIMEOUT=100,DefaultCanvasFactory=_is_node.isNodeJS?_node_utils.NodeCanvasFactory:_display_utils.DOMCanvasFactory,DefaultCMapReaderFactory=_is_node.isNodeJS?_node_utils.NodeCMapReaderFactory:_display_utils.DOMCMapReaderFactory,createPDFNetworkStream;function setPDFNetworkStreamFactory(e){createPDFNetworkStream=e}function getDocument(e){var t,r=new PDFDocumentLoadingTask;if("string"===typeof e)t={url:e};else if((0,_util.isArrayBuffer)(e))t={data:e};else if(e instanceof PDFDataRangeTransport)t={range:e};else{if("object"!==_typeof(e))throw new Error("Invalid parameter in getDocument, need either Uint8Array, string or a parameter object");if(!e.url&&!e.data&&!e.range)throw new Error("Invalid parameter object: need either .data, .range or .url");t=e}var n=Object.create(null),i=null,a=null;for(var o in t)if("url"!==o||"undefined"===typeof window)if("range"!==o)if("worker"!==o)if("data"!==o||t[o]instanceof Uint8Array)n[o]=t[o];else{var s=t[o];if("string"===typeof s)n[o]=(0,_util.stringToBytes)(s);else if("object"!==_typeof(s)||null===s||isNaN(s.length)){if(!(0,_util.isArrayBuffer)(s))throw new Error("Invalid PDF binary data: either typed array, string or array-like object is expected in the data property.");n[o]=new Uint8Array(s)}else n[o]=new Uint8Array(s)}else a=t[o];else i=t[o];else n[o]=new URL(t[o],window.location).href;if(n.rangeChunkSize=n.rangeChunkSize||DEFAULT_RANGE_CHUNK_SIZE,n.CMapReaderFactory=n.CMapReaderFactory||DefaultCMapReaderFactory,n.ignoreErrors=!0!==n.stopAtErrors,n.fontExtraProperties=!0===n.fontExtraProperties,n.pdfBug=!0===n.pdfBug,Number.isInteger(n.maxImageSize)||(n.maxImageSize=-1),"boolean"!==typeof n.isEvalSupported&&(n.isEvalSupported=!0),"boolean"!==typeof n.disableFontFace&&(n.disableFontFace=_api_compatibility.apiCompatibilityParams.disableFontFace||!1),"undefined"===typeof n.ownerDocument&&(n.ownerDocument=globalThis.document),"boolean"!==typeof n.disableRange&&(n.disableRange=!1),"boolean"!==typeof n.disableStream&&(n.disableStream=!1),"boolean"!==typeof n.disableAutoFetch&&(n.disableAutoFetch=!1),(0,_util.setVerbosityLevel)(n.verbosity),!a){var u={verbosity:n.verbosity,port:_worker_options.GlobalWorkerOptions.workerPort};a=u.port?PDFWorker.fromPort(u):new PDFWorker(u),r._worker=a}var c=r.docId;return a.promise.then((function(){if(r.destroyed)throw new Error("Loading aborted");var e=_fetchDocument(a,n,i,c),t=new Promise((function(e){var t;i?t=new _transport_stream.PDFDataTransportStream({length:n.length,initialData:n.initialData,progressiveDone:n.progressiveDone,disableRange:n.disableRange,disableStream:n.disableStream},i):n.data||(t=createPDFNetworkStream({url:n.url,length:n.length,httpHeaders:n.httpHeaders,withCredentials:n.withCredentials,rangeChunkSize:n.rangeChunkSize,disableRange:n.disableRange,disableStream:n.disableStream})),e(t)}));return Promise.all([e,t]).then((function(e){var t=_slicedToArray(e,2),i=t[0],o=t[1];if(r.destroyed)throw new Error("Loading aborted");var s=new _message_handler.MessageHandler(c,i,a.port);s.postMessageTransfers=a.postMessageTransfers;var u=new WorkerTransport(s,r,o,n);r._transport=u,s.send("Ready",null)}))}))["catch"](r._capability.reject),r}function _fetchDocument(e,t,r,n){return e.destroyed?Promise.reject(new Error("Worker was destroyed")):(r&&(t.length=r.length,t.initialData=r.initialData,t.progressiveDone=r.progressiveDone),e.messageHandler.sendWithPromise("GetDocRequest",{docId:n,apiVersion:"2.6.347",source:{data:t.data,url:t.url,password:t.password,disableAutoFetch:t.disableAutoFetch,rangeChunkSize:t.rangeChunkSize,length:t.length},maxImageSize:t.maxImageSize,disableFontFace:t.disableFontFace,postMessageTransfers:e.postMessageTransfers,docBaseUrl:t.docBaseUrl,ignoreErrors:t.ignoreErrors,isEvalSupported:t.isEvalSupported,fontExtraProperties:t.fontExtraProperties}).then((function(t){if(e.destroyed)throw new Error("Worker was destroyed");return t})))}var PDFDocumentLoadingTask=function(){var e=0,t=function(){function t(){_classCallCheck(this,t),this._capability=(0,_util.createPromiseCapability)(),this._transport=null,this._worker=null,this.docId="d"+e++,this.destroyed=!1,this.onPassword=null,this.onProgress=null,this.onUnsupportedFeature=null}return _createClass(t,[{key:"destroy",value:function(){var e=this;this.destroyed=!0;var t=this._transport?this._transport.destroy():Promise.resolve();return t.then((function(){e._transport=null,e._worker&&(e._worker.destroy(),e._worker=null)}))}},{key:"promise",get:function(){return this._capability.promise}}]),t}();return t}(),PDFDataRangeTransport=function(){function e(t,r){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];_classCallCheck(this,e),this.length=t,this.initialData=r,this.progressiveDone=n,this._rangeListeners=[],this._progressListeners=[],this._progressiveReadListeners=[],this._progressiveDoneListeners=[],this._readyCapability=(0,_util.createPromiseCapability)()}return _createClass(e,[{key:"addRangeListener",value:function(e){this._rangeListeners.push(e)}},{key:"addProgressListener",value:function(e){this._progressListeners.push(e)}},{key:"addProgressiveReadListener",value:function(e){this._progressiveReadListeners.push(e)}},{key:"addProgressiveDoneListener",value:function(e){this._progressiveDoneListeners.push(e)}},{key:"onDataRange",value:function(e,t){var r,n=_createForOfIteratorHelper(this._rangeListeners);try{for(n.s();!(r=n.n()).done;){var i=r.value;i(e,t)}}catch(a){n.e(a)}finally{n.f()}}},{key:"onDataProgress",value:function(e,t){var r=this;this._readyCapability.promise.then((function(){var n,i=_createForOfIteratorHelper(r._progressListeners);try{for(i.s();!(n=i.n()).done;){var a=n.value;a(e,t)}}catch(o){i.e(o)}finally{i.f()}}))}},{key:"onDataProgressiveRead",value:function(e){var t=this;this._readyCapability.promise.then((function(){var r,n=_createForOfIteratorHelper(t._progressiveReadListeners);try{for(n.s();!(r=n.n()).done;){var i=r.value;i(e)}}catch(a){n.e(a)}finally{n.f()}}))}},{key:"onDataProgressiveDone",value:function(){var e=this;this._readyCapability.promise.then((function(){var t,r=_createForOfIteratorHelper(e._progressiveDoneListeners);try{for(r.s();!(t=r.n()).done;){var n=t.value;n()}}catch(i){r.e(i)}finally{r.f()}}))}},{key:"transportReady",value:function(){this._readyCapability.resolve()}},{key:"requestDataRange",value:function(e,t){(0,_util.unreachable)("Abstract method PDFDataRangeTransport.requestDataRange")}},{key:"abort",value:function(){}}]),e}();exports.PDFDataRangeTransport=PDFDataRangeTransport;var PDFDocumentProxy=function(){function e(t,r){_classCallCheck(this,e),this._pdfInfo=t,this._transport=r}return _createClass(e,[{key:"getPage",value:function(e){return this._transport.getPage(e)}},{key:"getPageIndex",value:function(e){return this._transport.getPageIndex(e)}},{key:"getDestinations",value:function(){return this._transport.getDestinations()}},{key:"getDestination",value:function(e){return this._transport.getDestination(e)}},{key:"getPageLabels",value:function(){return this._transport.getPageLabels()}},{key:"getPageLayout",value:function(){return this._transport.getPageLayout()}},{key:"getPageMode",value:function(){return this._transport.getPageMode()}},{key:"getViewerPreferences",value:function(){return this._transport.getViewerPreferences()}},{key:"getOpenAction",value:function(){return this._transport.getOpenAction()}},{key:"getAttachments",value:function(){return this._transport.getAttachments()}},{key:"getJavaScript",value:function(){return this._transport.getJavaScript()}},{key:"getOutline",value:function(){return this._transport.getOutline()}},{key:"getOptionalContentConfig",value:function(){return this._transport.getOptionalContentConfig()}},{key:"getPermissions",value:function(){return this._transport.getPermissions()}},{key:"getMetadata",value:function(){return this._transport.getMetadata()}},{key:"getData",value:function(){return this._transport.getData()}},{key:"getDownloadInfo",value:function(){return this._transport.downloadInfoCapability.promise}},{key:"getStats",value:function(){return this._transport.getStats()}},{key:"cleanup",value:function(){return this._transport.startCleanup()}},{key:"destroy",value:function(){return this.loadingTask.destroy()}},{key:"saveDocument",value:function(e){return this._transport.saveDocument(e)}},{key:"annotationStorage",get:function(){return(0,_util.shadow)(this,"annotationStorage",new _annotation_storage.AnnotationStorage)}},{key:"numPages",get:function(){return this._pdfInfo.numPages}},{key:"fingerprint",get:function(){return this._pdfInfo.fingerprint}},{key:"loadingParams",get:function(){return this._transport.loadingParams}},{key:"loadingTask",get:function(){return this._transport.loadingTask}}]),e}();exports.PDFDocumentProxy=PDFDocumentProxy;var PDFPageProxy=function(){function e(t,r,n,i){var a=arguments.length>4&&void 0!==arguments[4]&&arguments[4];_classCallCheck(this,e),this._pageIndex=t,this._pageInfo=r,this._ownerDocument=i,this._transport=n,this._stats=a?new _display_utils.StatTimer:null,this._pdfBug=a,this.commonObjs=n.commonObjs,this.objs=new PDFObjects,this.cleanupAfterRender=!1,this.pendingCleanup=!1,this._intentStates=new Map,this.destroyed=!1}return _createClass(e,[{key:"getViewport",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.scale,r=e.rotation,n=void 0===r?this.rotate:r,i=e.offsetX,a=void 0===i?0:i,o=e.offsetY,s=void 0===o?0:o,u=e.dontFlip,c=void 0!==u&&u;return new _display_utils.PageViewport({viewBox:this.view,scale:t,rotation:n,offsetX:a,offsetY:s,dontFlip:c})}},{key:"getAnnotations",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.intent,r=void 0===t?null:t;return this.annotationsPromise&&this.annotationsIntent===r||(this.annotationsPromise=this._transport.getAnnotations(this._pageIndex,r),this.annotationsIntent=r),this.annotationsPromise}},{key:"render",value:function(e){var t=this,r=e.canvasContext,n=e.viewport,i=e.intent,a=void 0===i?"display":i,o=e.enableWebGL,s=void 0!==o&&o,u=e.renderInteractiveForms,c=void 0!==u&&u,l=e.transform,h=void 0===l?null:l,f=e.imageLayer,d=void 0===f?null:f,p=e.canvasFactory,v=void 0===p?null:p,g=e.background,y=void 0===g?null:g,m=e.annotationStorage,b=void 0===m?null:m,_=e.optionalContentConfigPromise,w=void 0===_?null:_;this._stats&&this._stats.time("Overall");var S="print"===a?"print":"display";this.pendingCleanup=!1,w||(w=this._transport.getOptionalContentConfig());var A=this._intentStates.get(S);A||(A=Object.create(null),this._intentStates.set(S,A)),A.streamReaderCancelTimeout&&(clearTimeout(A.streamReaderCancelTimeout),A.streamReaderCancelTimeout=null);var k=v||new DefaultCanvasFactory({ownerDocument:this._ownerDocument}),x=new _webgl.WebGLContext({enable:s});A.displayReadyCapability||(A.displayReadyCapability=(0,_util.createPromiseCapability)(),A.operatorList={fnArray:[],argsArray:[],lastChunk:!1},this._stats&&this._stats.time("Page Request"),this._pumpOperatorList({pageIndex:this._pageIndex,intent:S,renderInteractiveForms:!0===c,annotationStorage:b&&b.getAll()||null}));var P=function(e){var r=A.renderTasks.indexOf(C);r>=0&&A.renderTasks.splice(r,1),(t.cleanupAfterRender||"print"===S)&&(t.pendingCleanup=!0),t._tryCleanup(),e?(C.capability.reject(e),t._abortOperatorList({intentState:A,reason:e})):C.capability.resolve(),t._stats&&(t._stats.timeEnd("Rendering"),t._stats.timeEnd("Overall"))},C=new InternalRenderTask({callback:P,params:{canvasContext:r,viewport:n,transform:h,imageLayer:d,background:y},objs:this.objs,commonObjs:this.commonObjs,operatorList:A.operatorList,pageIndex:this._pageIndex,canvasFactory:k,webGLContext:x,useRequestAnimationFrame:"print"!==S,pdfBug:this._pdfBug});A.renderTasks||(A.renderTasks=[]),A.renderTasks.push(C);var T=C.task;return Promise.all([A.displayReadyCapability.promise,w]).then((function(e){var r=_slicedToArray(e,2),n=r[0],i=r[1];t.pendingCleanup?P():(t._stats&&t._stats.time("Rendering"),C.initializeGraphics({transparency:n,optionalContentConfig:i}),C.operatorListChanged())}))["catch"](P),T}},{key:"getOperatorList",value:function(){function e(){if(n.operatorList.lastChunk){n.opListReadCapability.resolve(n.operatorList);var e=n.renderTasks.indexOf(t);e>=0&&n.renderTasks.splice(e,1)}}var t,r="oplist",n=this._intentStates.get(r);return n||(n=Object.create(null),this._intentStates.set(r,n)),n.opListReadCapability||(t=Object.create(null),t.operatorListChanged=e,n.opListReadCapability=(0,_util.createPromiseCapability)(),n.renderTasks=[],n.renderTasks.push(t),n.operatorList={fnArray:[],argsArray:[],lastChunk:!1},this._stats&&this._stats.time("Page Request"),this._pumpOperatorList({pageIndex:this._pageIndex,intent:r})),n.opListReadCapability.promise}},{key:"streamTextContent",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.normalizeWhitespace,r=void 0!==t&&t,n=e.disableCombineTextItems,i=void 0!==n&&n,a=100;return this._transport.messageHandler.sendWithStream("GetTextContent",{pageIndex:this._pageIndex,normalizeWhitespace:!0===r,combineTextItems:!0!==i},{highWaterMark:a,size:function(e){return e.items.length}})}},{key:"getTextContent",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=this.streamTextContent(e);return new Promise((function(e,r){function n(){i.read().then((function(t){var r,i=t.value,o=t.done;o?e(a):(Object.assign(a.styles,i.styles),(r=a.items).push.apply(r,_toConsumableArray(i.items)),n())}),r)}var i=t.getReader(),a={items:[],styles:Object.create(null)};n()}))}},{key:"_destroy",value:function(){this.destroyed=!0,this._transport.pageCache[this._pageIndex]=null;var e,t=[],r=_createForOfIteratorHelper(this._intentStates);try{for(r.s();!(e=r.n()).done;){var n=_slicedToArray(e.value,2),i=n[0],a=n[1];if(this._abortOperatorList({intentState:a,reason:new Error("Page was destroyed."),force:!0}),"oplist"!==i){var o,s=_createForOfIteratorHelper(a.renderTasks);try{for(s.s();!(o=s.n()).done;){var u=o.value;t.push(u.completed),u.cancel()}}catch(c){s.e(c)}finally{s.f()}}}}catch(c){r.e(c)}finally{r.f()}return this.objs.clear(),this.annotationsPromise=null,this.pendingCleanup=!1,Promise.all(t)}},{key:"cleanup",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return this.pendingCleanup=!0,this._tryCleanup(e)}},{key:"_tryCleanup",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this.pendingCleanup)return!1;var t,r=_createForOfIteratorHelper(this._intentStates.values());try{for(r.s();!(t=r.n()).done;){var n=t.value,i=n.renderTasks,a=n.operatorList;if(0!==i.length||!a.lastChunk)return!1}}catch(o){r.e(o)}finally{r.f()}return this._intentStates.clear(),this.objs.clear(),this.annotationsPromise=null,e&&this._stats&&(this._stats=new _display_utils.StatTimer),this.pendingCleanup=!1,!0}},{key:"_startRenderPage",value:function(e,t){var r=this._intentStates.get(t);r&&(this._stats&&this._stats.timeEnd("Page Request"),r.displayReadyCapability&&r.displayReadyCapability.resolve(e))}},{key:"_renderPageChunk",value:function(e,t){for(var r=0,n=e.length;r<n;r++)t.operatorList.fnArray.push(e.fnArray[r]),t.operatorList.argsArray.push(e.argsArray[r]);t.operatorList.lastChunk=e.lastChunk;for(var i=0;i<t.renderTasks.length;i++)t.renderTasks[i].operatorListChanged();e.lastChunk&&this._tryCleanup()}},{key:"_pumpOperatorList",value:function(e){var t=this;(0,_util.assert)(e.intent,'PDFPageProxy._pumpOperatorList: Expected "intent" argument.');var r=this._transport.messageHandler.sendWithStream("GetOperatorList",e),n=r.getReader(),i=this._intentStates.get(e.intent);i.streamReader=n;var a=function e(){n.read().then((function(r){var n=r.value,a=r.done;a?i.streamReader=null:t._transport.destroyed||(t._renderPageChunk(n,i),e())}),(function(e){if(i.streamReader=null,!t._transport.destroyed){if(i.operatorList){i.operatorList.lastChunk=!0;for(var r=0;r<i.renderTasks.length;r++)i.renderTasks[r].operatorListChanged();t._tryCleanup()}if(i.displayReadyCapability)i.displayReadyCapability.reject(e);else{if(!i.opListReadCapability)throw e;i.opListReadCapability.reject(e)}}}))};a()}},{key:"_abortOperatorList",value:function(e){var t=this,r=e.intentState,n=e.reason,i=e.force,a=void 0!==i&&i;if((0,_util.assert)(n instanceof Error||"object"===_typeof(n)&&null!==n,'PDFPageProxy._abortOperatorList: Expected "reason" argument.'),r.streamReader){if(!a){if(0!==r.renderTasks.length)return;if(n instanceof _display_utils.RenderingCancelledException)return void(r.streamReaderCancelTimeout=setTimeout((function(){t._abortOperatorList({intentState:r,reason:n,force:!0}),r.streamReaderCancelTimeout=null}),RENDERING_CANCELLED_TIMEOUT))}if(r.streamReader.cancel(new _util.AbortException(n&&n.message)),r.streamReader=null,!this._transport.destroyed){var o,s=_createForOfIteratorHelper(this._intentStates);try{for(s.s();!(o=s.n()).done;){var u=_slicedToArray(o.value,2),c=u[0],l=u[1];if(l===r){this._intentStates["delete"](c);break}}}catch(h){s.e(h)}finally{s.f()}this.cleanup()}}}},{key:"pageNumber",get:function(){return this._pageIndex+1}},{key:"rotate",get:function(){return this._pageInfo.rotate}},{key:"ref",get:function(){return this._pageInfo.ref}},{key:"userUnit",get:function(){return this._pageInfo.userUnit}},{key:"view",get:function(){return this._pageInfo.view}},{key:"stats",get:function(){return this._stats}}]),e}();exports.PDFPageProxy=PDFPageProxy;var LoopbackPort=function(){function e(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];_classCallCheck(this,e),this._listeners=[],this._defer=t,this._deferred=Promise.resolve(void 0)}return _createClass(e,[{key:"postMessage",value:function(e,t){var r=this;function n(e){if("object"!==_typeof(e)||null===e)return e;if(i.has(e))return i.get(e);var r,a;if((r=e.buffer)&&(0,_util.isArrayBuffer)(r)){var o=t&&t.includes(r);return a=o?new e.constructor(r,e.byteOffset,e.byteLength):new e.constructor(e),i.set(e,a),a}for(var s in a=Array.isArray(e)?[]:{},i.set(e,a),e){var u=void 0,c=e;while(!(u=Object.getOwnPropertyDescriptor(c,s)))c=Object.getPrototypeOf(c);if("undefined"!==typeof u.value)if("function"!==typeof u.value)a[s]=n(u.value);else if(e.hasOwnProperty&&e.hasOwnProperty(s))throw new Error("LoopbackPort.postMessage - cannot clone: ".concat(e[s]))}return a}if(this._defer){var i=new WeakMap,a={data:n(e)};this._deferred.then((function(){r._listeners.forEach((function(e){e.call(r,a)}))}))}else this._listeners.forEach((function(t){t.call(r,{data:e})}))}},{key:"addEventListener",value:function(e,t){this._listeners.push(t)}},{key:"removeEventListener",value:function(e,t){var r=this._listeners.indexOf(t);this._listeners.splice(r,1)}},{key:"terminate",value:function(){this._listeners.length=0}}]),e}();exports.LoopbackPort=LoopbackPort;var PDFWorker=function PDFWorkerClosure(){var pdfWorkerPorts=new WeakMap,isWorkerDisabled=!1,fallbackWorkerSrc,nextFakeWorkerId=0,fakeWorkerCapability;if(_is_node.isNodeJS)isWorkerDisabled=!0,fallbackWorkerSrc="./pdf.worker.js";else if("object"===("undefined"===typeof document?"undefined":_typeof(document))&&"currentScript"in document){var pdfjsFilePath=document.currentScript&&document.currentScript.src;pdfjsFilePath&&(fallbackWorkerSrc=pdfjsFilePath.replace(/(\.(?:min\.)?js)(\?.*)?$/i,".worker$1$2"))}function _getWorkerSrc(){if(_worker_options.GlobalWorkerOptions.workerSrc)return _worker_options.GlobalWorkerOptions.workerSrc;if("undefined"!==typeof fallbackWorkerSrc)return _is_node.isNodeJS||(0,_display_utils.deprecated)('No "GlobalWorkerOptions.workerSrc" specified.'),fallbackWorkerSrc;throw new Error('No "GlobalWorkerOptions.workerSrc" specified.')}function getMainThreadWorkerMessageHandler(){var e;try{e=globalThis.pdfjsWorker&&globalThis.pdfjsWorker.WorkerMessageHandler}catch(t){}return e||null}function setupFakeWorkerGlobal(){if(fakeWorkerCapability)return fakeWorkerCapability.promise;fakeWorkerCapability=(0,_util.createPromiseCapability)();var loader=function(){var _ref12=_asyncToGenerator(_regenerator["default"].mark((function _callee(){var mainWorkerMessageHandler,worker;return _regenerator["default"].wrap((function _callee$(_context){while(1)switch(_context.prev=_context.next){case 0:if(mainWorkerMessageHandler=getMainThreadWorkerMessageHandler(),!mainWorkerMessageHandler){_context.next=3;break}return _context.abrupt("return",mainWorkerMessageHandler);case 3:if(!_is_node.isNodeJS){_context.next=6;break}return worker=eval("require")(_getWorkerSrc()),_context.abrupt("return",worker.WorkerMessageHandler);case 6:return _context.next=8,(0,_display_utils.loadScript)(_getWorkerSrc());case 8:return _context.abrupt("return",window.pdfjsWorker.WorkerMessageHandler);case 9:case"end":return _context.stop()}}),_callee)})));return function(){return _ref12.apply(this,arguments)}}();return loader().then(fakeWorkerCapability.resolve,fakeWorkerCapability.reject),fakeWorkerCapability.promise}function createCDNWrapper(e){var t="importScripts('"+e+"');";return URL.createObjectURL(new Blob([t]))}var PDFWorker=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=t.name,n=void 0===r?null:r,i=t.port,a=void 0===i?null:i,o=t.verbosity,s=void 0===o?(0,_util.getVerbosityLevel)():o;if(_classCallCheck(this,e),a&&pdfWorkerPorts.has(a))throw new Error("Cannot use more than one PDFWorker per port");if(this.name=n,this.destroyed=!1,this.postMessageTransfers=!0,this.verbosity=s,this._readyCapability=(0,_util.createPromiseCapability)(),this._port=null,this._webWorker=null,this._messageHandler=null,a)return pdfWorkerPorts.set(a,this),void this._initializeFromPort(a);this._initialize()}return _createClass(e,[{key:"_initializeFromPort",value:function(e){this._port=e,this._messageHandler=new _message_handler.MessageHandler("main","worker",e),this._messageHandler.on("ready",(function(){})),this._readyCapability.resolve()}},{key:"_initialize",value:function(){var e=this;if("undefined"!==typeof Worker&&!isWorkerDisabled&&!getMainThreadWorkerMessageHandler()){var t=_getWorkerSrc();try{(0,_util.isSameOrigin)(window.location.href,t)||(t=createCDNWrapper(new URL(t,window.location).href));var r=new Worker(t),n=new _message_handler.MessageHandler("main","worker",r),i=function(){r.removeEventListener("error",a),n.destroy(),r.terminate(),e.destroyed?e._readyCapability.reject(new Error("Worker was destroyed")):e._setupFakeWorker()},a=function(){e._webWorker||i()};r.addEventListener("error",a),n.on("test",(function(t){r.removeEventListener("error",a),e.destroyed?i():t?(e._messageHandler=n,e._port=r,e._webWorker=r,t.supportTransfers||(e.postMessageTransfers=!1),e._readyCapability.resolve(),n.send("configure",{verbosity:e.verbosity})):(e._setupFakeWorker(),n.destroy(),r.terminate())})),n.on("ready",(function(t){if(r.removeEventListener("error",a),e.destroyed)i();else try{o()}catch(n){e._setupFakeWorker()}}));var o=function(){var t=new Uint8Array([e.postMessageTransfers?255:0]);try{n.send("test",t,[t.buffer])}catch(r){(0,_util.warn)("Cannot use postMessage transfers."),t[0]=0,n.send("test",t)}};return void o()}catch(s){(0,_util.info)("The worker has been disabled.")}}this._setupFakeWorker()}},{key:"_setupFakeWorker",value:function(){var e=this;isWorkerDisabled||((0,_util.warn)("Setting up fake worker."),isWorkerDisabled=!0),setupFakeWorkerGlobal().then((function(t){if(e.destroyed)e._readyCapability.reject(new Error("Worker was destroyed"));else{var r=new LoopbackPort;e._port=r;var n="fake"+nextFakeWorkerId++,i=new _message_handler.MessageHandler(n+"_worker",n,r);t.setup(i,r);var a=new _message_handler.MessageHandler(n,n+"_worker",r);e._messageHandler=a,e._readyCapability.resolve(),a.send("configure",{verbosity:e.verbosity})}}))["catch"]((function(t){e._readyCapability.reject(new Error('Setting up fake worker failed: "'.concat(t.message,'".')))}))}},{key:"destroy",value:function(){this.destroyed=!0,this._webWorker&&(this._webWorker.terminate(),this._webWorker=null),pdfWorkerPorts["delete"](this._port),this._port=null,this._messageHandler&&(this._messageHandler.destroy(),this._messageHandler=null)}},{key:"promise",get:function(){return this._readyCapability.promise}},{key:"port",get:function(){return this._port}},{key:"messageHandler",get:function(){return this._messageHandler}}],[{key:"fromPort",value:function(t){if(!t||!t.port)throw new Error("PDFWorker.fromPort - invalid method signature.");return pdfWorkerPorts.has(t.port)?pdfWorkerPorts.get(t.port):new e(t)}},{key:"getWorkerSrc",value:function(){return _getWorkerSrc()}}]),e}();return PDFWorker}();exports.PDFWorker=PDFWorker;var WorkerTransport=function(){function e(t,r,n,i){_classCallCheck(this,e),this.messageHandler=t,this.loadingTask=r,this.commonObjs=new PDFObjects,this.fontLoader=new _font_loader.FontLoader({docId:r.docId,onUnsupportedFeature:this._onUnsupportedFeature.bind(this),ownerDocument:i.ownerDocument}),this._params=i,this.CMapReaderFactory=new i.CMapReaderFactory({baseUrl:i.cMapUrl,isCompressed:i.cMapPacked}),this.destroyed=!1,this.destroyCapability=null,this._passwordCapability=null,this._networkStream=n,this._fullReader=null,this._lastProgress=null,this.pageCache=[],this.pagePromises=[],this.downloadInfoCapability=(0,_util.createPromiseCapability)(),this.setupMessageHandler()}return _createClass(e,[{key:"destroy",value:function(){var e=this;if(this.destroyCapability)return this.destroyCapability.promise;this.destroyed=!0,this.destroyCapability=(0,_util.createPromiseCapability)(),this._passwordCapability&&this._passwordCapability.reject(new Error("Worker was destroyed during onPassword callback"));var t=[];this.pageCache.forEach((function(e){e&&t.push(e._destroy())})),this.pageCache.length=0,this.pagePromises.length=0;var r=this.messageHandler.sendWithPromise("Terminate",null);return t.push(r),Promise.all(t).then((function(){e.fontLoader.clear(),e._networkStream&&e._networkStream.cancelAllRequests(new _util.AbortException("Worker was terminated.")),e.messageHandler&&(e.messageHandler.destroy(),e.messageHandler=null),e.destroyCapability.resolve()}),this.destroyCapability.reject),this.destroyCapability.promise}},{key:"setupMessageHandler",value:function(){var e=this,t=this.messageHandler,r=this.loadingTask;t.on("GetReader",(function(t,r){(0,_util.assert)(e._networkStream,"GetReader - no `IPDFStream` instance available."),e._fullReader=e._networkStream.getFullReader(),e._fullReader.onProgress=function(t){e._lastProgress={loaded:t.loaded,total:t.total}},r.onPull=function(){e._fullReader.read().then((function(e){var t=e.value,n=e.done;n?r.close():((0,_util.assert)((0,_util.isArrayBuffer)(t),"GetReader - expected an ArrayBuffer."),r.enqueue(new Uint8Array(t),1,[t]))}))["catch"]((function(e){r.error(e)}))},r.onCancel=function(t){e._fullReader.cancel(t),r.ready["catch"]((function(t){if(!e.destroyed)throw t}))}})),t.on("ReaderHeadersReady",(function(t){var n=(0,_util.createPromiseCapability)(),i=e._fullReader;return i.headersReady.then((function(){i.isStreamingSupported&&i.isRangeSupported||(e._lastProgress&&r.onProgress&&r.onProgress(e._lastProgress),i.onProgress=function(e){r.onProgress&&r.onProgress({loaded:e.loaded,total:e.total})}),n.resolve({isStreamingSupported:i.isStreamingSupported,isRangeSupported:i.isRangeSupported,contentLength:i.contentLength})}),n.reject),n.promise})),t.on("GetRangeReader",(function(t,r){(0,_util.assert)(e._networkStream,"GetRangeReader - no `IPDFStream` instance available.");var n=e._networkStream.getRangeReader(t.begin,t.end);n?(r.onPull=function(){n.read().then((function(e){var t=e.value,n=e.done;n?r.close():((0,_util.assert)((0,_util.isArrayBuffer)(t),"GetRangeReader - expected an ArrayBuffer."),r.enqueue(new Uint8Array(t),1,[t]))}))["catch"]((function(e){r.error(e)}))},r.onCancel=function(t){n.cancel(t),r.ready["catch"]((function(t){if(!e.destroyed)throw t}))}):r.close()})),t.on("GetDoc",(function(t){var n=t.pdfInfo;e._numPages=n.numPages,r._capability.resolve(new PDFDocumentProxy(n,e))})),t.on("DocException",(function(e){var t;switch(e.name){case"PasswordException":t=new _util.PasswordException(e.message,e.code);break;case"InvalidPDFException":t=new _util.InvalidPDFException(e.message);break;case"MissingPDFException":t=new _util.MissingPDFException(e.message);break;case"UnexpectedResponseException":t=new _util.UnexpectedResponseException(e.message,e.status);break;case"UnknownErrorException":t=new _util.UnknownErrorException(e.message,e.details);break}if(!(t instanceof Error)){var n="DocException - expected a valid Error.";(0,_util.warn)(n)}r._capability.reject(t)})),t.on("PasswordRequest",(function(t){if(e._passwordCapability=(0,_util.createPromiseCapability)(),r.onPassword){var n=function(t){e._passwordCapability.resolve({password:t})};try{r.onPassword(n,t.code)}catch(i){e._passwordCapability.reject(i)}}else e._passwordCapability.reject(new _util.PasswordException(t.message,t.code));return e._passwordCapability.promise})),t.on("DataLoaded",(function(t){r.onProgress&&r.onProgress({loaded:t.length,total:t.length}),e.downloadInfoCapability.resolve(t)})),t.on("StartRenderPage",(function(t){if(!e.destroyed){var r=e.pageCache[t.pageIndex];r._startRenderPage(t.transparency,t.intent)}})),t.on("commonobj",(function(r){if(!e.destroyed){var n=_slicedToArray(r,3),i=n[0],a=n[1],o=n[2];if(!e.commonObjs.has(i))switch(a){case"Font":var s=e._params;if("error"in o){var u=o.error;(0,_util.warn)("Error during font loading: ".concat(u)),e.commonObjs.resolve(i,u);break}var c=null;s.pdfBug&&globalThis.FontInspector&&globalThis.FontInspector.enabled&&(c={registerFont:function(e,t){globalThis.FontInspector.fontAdded(e,t)}});var l=new _font_loader.FontFaceObject(o,{isEvalSupported:s.isEvalSupported,disableFontFace:s.disableFontFace,ignoreErrors:s.ignoreErrors,onUnsupportedFeature:e._onUnsupportedFeature.bind(e),fontRegistry:c});e.fontLoader.bind(l)["catch"]((function(e){return t.sendWithPromise("FontFallback",{id:i})}))["finally"]((function(){!s.fontExtraProperties&&l.data&&(l.data=null),e.commonObjs.resolve(i,l)}));break;case"FontPath":case"Image":e.commonObjs.resolve(i,o);break;default:throw new Error("Got unknown common object type ".concat(a))}}})),t.on("obj",(function(t){if(!e.destroyed){var r=_slicedToArray(t,4),n=r[0],i=r[1],a=r[2],o=r[3],s=e.pageCache[i];if(!s.objs.has(n))switch(a){case"Image":s.objs.resolve(n,o);var u=8e6;o&&"data"in o&&o.data.length>u&&(s.cleanupAfterRender=!0);break;default:throw new Error("Got unknown object type ".concat(a))}}})),t.on("DocProgress",(function(t){e.destroyed||r.onProgress&&r.onProgress({loaded:t.loaded,total:t.total})})),t.on("UnsupportedFeature",this._onUnsupportedFeature.bind(this)),t.on("FetchBuiltInCMap",(function(t,r){if(e.destroyed)r.error(new Error("Worker was destroyed"));else{var n=!1;r.onPull=function(){n?r.close():(n=!0,e.CMapReaderFactory.fetch(t).then((function(e){r.enqueue(e,1,[e.cMapData.buffer])}))["catch"]((function(e){r.error(e)})))}}}))}},{key:"_onUnsupportedFeature",value:function(e){var t=e.featureId;this.destroyed||this.loadingTask.onUnsupportedFeature&&this.loadingTask.onUnsupportedFeature(t)}},{key:"getData",value:function(){return this.messageHandler.sendWithPromise("GetData",null)}},{key:"getPage",value:function(e){var t=this;if(!Number.isInteger(e)||e<=0||e>this._numPages)return Promise.reject(new Error("Invalid page request"));var r=e-1;if(r in this.pagePromises)return this.pagePromises[r];var n=this.messageHandler.sendWithPromise("GetPage",{pageIndex:r}).then((function(e){if(t.destroyed)throw new Error("Transport destroyed");var n=new PDFPageProxy(r,e,t,t._params.ownerDocument,t._params.pdfBug);return t.pageCache[r]=n,n}));return this.pagePromises[r]=n,n}},{key:"getPageIndex",value:function(e){return this.messageHandler.sendWithPromise("GetPageIndex",{ref:e})["catch"]((function(e){return Promise.reject(new Error(e))}))}},{key:"getAnnotations",value:function(e,t){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:e,intent:t})}},{key:"saveDocument",value:function(e){return this.messageHandler.sendWithPromise("SaveDocument",{numPages:this._numPages,annotationStorage:e&&e.getAll()||null,filename:this._fullReader?this._fullReader.filename:null})["finally"]((function(){e&&e.resetModified()}))}},{key:"getDestinations",value:function(){return this.messageHandler.sendWithPromise("GetDestinations",null)}},{key:"getDestination",value:function(e){return"string"!==typeof e?Promise.reject(new Error("Invalid destination request.")):this.messageHandler.sendWithPromise("GetDestination",{id:e})}},{key:"getPageLabels",value:function(){return this.messageHandler.sendWithPromise("GetPageLabels",null)}},{key:"getPageLayout",value:function(){return this.messageHandler.sendWithPromise("GetPageLayout",null)}},{key:"getPageMode",value:function(){return this.messageHandler.sendWithPromise("GetPageMode",null)}},{key:"getViewerPreferences",value:function(){return this.messageHandler.sendWithPromise("GetViewerPreferences",null)}},{key:"getOpenAction",value:function(){return this.messageHandler.sendWithPromise("GetOpenAction",null)}},{key:"getAttachments",value:function(){return this.messageHandler.sendWithPromise("GetAttachments",null)}},{key:"getJavaScript",value:function(){return this.messageHandler.sendWithPromise("GetJavaScript",null)}},{key:"getOutline",value:function(){return this.messageHandler.sendWithPromise("GetOutline",null)}},{key:"getOptionalContentConfig",value:function(){return this.messageHandler.sendWithPromise("GetOptionalContentConfig",null).then((function(e){return new _optional_content_config.OptionalContentConfig(e)}))}},{key:"getPermissions",value:function(){return this.messageHandler.sendWithPromise("GetPermissions",null)}},{key:"getMetadata",value:function(){var e=this;return this.messageHandler.sendWithPromise("GetMetadata",null).then((function(t){return{info:t[0],metadata:t[1]?new _metadata.Metadata(t[1]):null,contentDispositionFilename:e._fullReader?e._fullReader.filename:null}}))}},{key:"getStats",value:function(){return this.messageHandler.sendWithPromise("GetStats",null)}},{key:"startCleanup",value:function(){var e=this;return this.messageHandler.sendWithPromise("Cleanup",null).then((function(){for(var t=0,r=e.pageCache.length;t<r;t++){var n=e.pageCache[t];if(n){var i=n.cleanup();if(!i)throw new Error("startCleanup: Page ".concat(t+1," is currently rendering."))}}e.commonObjs.clear(),e.fontLoader.clear()}))}},{key:"loadingParams",get:function(){var e=this._params;return(0,_util.shadow)(this,"loadingParams",{disableAutoFetch:e.disableAutoFetch,disableFontFace:e.disableFontFace})}}]),e}(),PDFObjects=function(){function e(){_classCallCheck(this,e),this._objs=Object.create(null)}return _createClass(e,[{key:"_ensureObj",value:function(e){return this._objs[e]?this._objs[e]:this._objs[e]={capability:(0,_util.createPromiseCapability)(),data:null,resolved:!1}}},{key:"get",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(t)return this._ensureObj(e).capability.promise.then(t),null;var r=this._objs[e];if(!r||!r.resolved)throw new Error("Requesting object that isn't resolved yet ".concat(e,"."));return r.data}},{key:"has",value:function(e){var t=this._objs[e];return!!t&&t.resolved}},{key:"resolve",value:function(e,t){var r=this._ensureObj(e);r.resolved=!0,r.data=t,r.capability.resolve(t)}},{key:"clear",value:function(){this._objs=Object.create(null)}}]),e}(),RenderTask=function(){function e(t){_classCallCheck(this,e),this._internalRenderTask=t,this.onContinue=null}return _createClass(e,[{key:"cancel",value:function(){this._internalRenderTask.cancel()}},{key:"promise",get:function(){return this._internalRenderTask.capability.promise}}]),e}(),InternalRenderTask=function(){var e=new WeakSet,t=function(){function t(e){var r=e.callback,n=e.params,i=e.objs,a=e.commonObjs,o=e.operatorList,s=e.pageIndex,u=e.canvasFactory,c=e.webGLContext,l=e.useRequestAnimationFrame,h=void 0!==l&&l,f=e.pdfBug,d=void 0!==f&&f;_classCallCheck(this,t),this.callback=r,this.params=n,this.objs=i,this.commonObjs=a,this.operatorListIdx=null,this.operatorList=o,this._pageIndex=s,this.canvasFactory=u,this.webGLContext=c,this._pdfBug=d,this.running=!1,this.graphicsReadyCallback=null,this.graphicsReady=!1,this._useRequestAnimationFrame=!0===h&&"undefined"!==typeof window,this.cancelled=!1,this.capability=(0,_util.createPromiseCapability)(),this.task=new RenderTask(this),this._continueBound=this._continue.bind(this),this._scheduleNextBound=this._scheduleNext.bind(this),this._nextBound=this._next.bind(this),this._canvas=n.canvasContext.canvas}return _createClass(t,[{key:"initializeGraphics",value:function(t){var r=t.transparency,n=void 0!==r&&r,i=t.optionalContentConfig;if(!this.cancelled){if(this._canvas){if(e.has(this._canvas))throw new Error("Cannot use the same canvas during multiple render() operations. Use different canvas or ensure previous operations were cancelled or completed.");e.add(this._canvas)}this._pdfBug&&globalThis.StepperManager&&globalThis.StepperManager.enabled&&(this.stepper=globalThis.StepperManager.create(this._pageIndex),this.stepper.init(this.operatorList),this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint());var a=this.params,o=a.canvasContext,s=a.viewport,u=a.transform,c=a.imageLayer,l=a.background;this.gfx=new _canvas.CanvasGraphics(o,this.commonObjs,this.objs,this.canvasFactory,this.webGLContext,c,i),this.gfx.beginDrawing({transform:u,viewport:s,transparency:n,background:l}),this.operatorListIdx=0,this.graphicsReady=!0,this.graphicsReadyCallback&&this.graphicsReadyCallback()}}},{key:"cancel",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;this.running=!1,this.cancelled=!0,this.gfx&&this.gfx.endDrawing(),this._canvas&&e["delete"](this._canvas),this.callback(t||new _display_utils.RenderingCancelledException("Rendering cancelled, page ".concat(this._pageIndex+1),"canvas"))}},{key:"operatorListChanged",value:function(){this.graphicsReady?(this.stepper&&this.stepper.updateOperatorList(this.operatorList),this.running||this._continue()):this.graphicsReadyCallback||(this.graphicsReadyCallback=this._continueBound)}},{key:"_continue",value:function(){this.running=!0,this.cancelled||(this.task.onContinue?this.task.onContinue(this._scheduleNextBound):this._scheduleNext())}},{key:"_scheduleNext",value:function(){var e=this;this._useRequestAnimationFrame?window.requestAnimationFrame((function(){e._nextBound()["catch"](e.cancel.bind(e))})):Promise.resolve().then(this._nextBound)["catch"](this.cancel.bind(this))}},{key:"_next",value:function(){var t=_asyncToGenerator(_regenerator["default"].mark((function t(){return _regenerator["default"].wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!this.cancelled){t.next=2;break}return t.abrupt("return");case 2:this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper),this.operatorListIdx===this.operatorList.argsArray.length&&(this.running=!1,this.operatorList.lastChunk&&(this.gfx.endDrawing(),this._canvas&&e["delete"](this._canvas),this.callback()));case 4:case"end":return t.stop()}}),t,this)})));function r(){return t.apply(this,arguments)}return r}()},{key:"completed",get:function(){return this.capability.promise["catch"]((function(){}))}}]),t}();return t}(),version="2.6.347";exports.version=version;var build="3be9c65f";exports.build=build},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.FontLoader=t.FontFaceObject=void 0;var n=a(r(2)),i=r(5);function a(e){return e&&e.__esModule?e:{default:e}}function o(e){return o="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function s(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&u(e,t)}function u(e,t){return u=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},u(e,t)}function c(e){var t=f();return function(){var r,n=d(e);if(t){var i=d(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return l(this,r)}}function l(e,t){return!t||"object"!==o(t)&&"function"!==typeof t?h(e):t}function h(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function f(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function d(e){return d=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},d(e)}function p(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(c){return void r(c)}s.done?t(u):Promise.resolve(u).then(n,i)}function v(e){return function(){var t=this,r=arguments;return new Promise((function(n,i){var a=e.apply(t,r);function o(e){p(a,n,i,o,s,"next",e)}function s(e){p(a,n,i,o,s,"throw",e)}o(void 0)}))}}function g(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function y(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function m(e,t,r){return t&&y(e.prototype,t),r&&y(e,r),e}var b,_=function(){function e(t){var r=t.docId,n=t.onUnsupportedFeature,a=t.ownerDocument,o=void 0===a?globalThis.document:a;g(this,e),this.constructor===e&&(0,i.unreachable)("Cannot initialize BaseFontLoader."),this.docId=r,this._onUnsupportedFeature=n,this._document=o,this.nativeFontFaces=[],this.styleElement=null}return m(e,[{key:"addNativeFontFace",value:function(e){this.nativeFontFaces.push(e),this._document.fonts.add(e)}},{key:"insertRule",value:function(e){var t=this.styleElement;t||(t=this.styleElement=this._document.createElement("style"),t.id="PDFJS_FONT_STYLE_TAG_".concat(this.docId),this._document.documentElement.getElementsByTagName("head")[0].appendChild(t));var r=t.sheet;r.insertRule(e,r.cssRules.length)}},{key:"clear",value:function(){var e=this;this.nativeFontFaces.forEach((function(t){e._document.fonts["delete"](t)})),this.nativeFontFaces.length=0,this.styleElement&&(this.styleElement.remove(),this.styleElement=null)}},{key:"bind",value:function(){var e=v(n["default"].mark((function e(t){var r,a,o=this;return n["default"].wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t.attached&&!t.missingFile){e.next=2;break}return e.abrupt("return");case 2:if(t.attached=!0,!this.isFontLoadingAPISupported){e.next=19;break}if(r=t.createNativeFontFace(),!r){e.next=18;break}return this.addNativeFontFace(r),e.prev=7,e.next=10,r.loaded;case 10:e.next=18;break;case 12:throw e.prev=12,e.t0=e["catch"](7),this._onUnsupportedFeature({featureId:i.UNSUPPORTED_FEATURES.errorFontLoadNative}),(0,i.warn)("Failed to load font '".concat(r.family,"': '").concat(e.t0,"'.")),t.disableFontFace=!0,e.t0;case 18:return e.abrupt("return");case 19:if(a=t.createFontFaceRule(),!a){e.next=26;break}if(this.insertRule(a),!this.isSyncFontLoadingSupported){e.next=24;break}return e.abrupt("return");case 24:return e.next=26,new Promise((function(e){var r=o._queueLoadingCallback(e);o._prepareFontLoadEvent([a],[t],r)}));case 26:case"end":return e.stop()}}),e,this,[[7,12]])})));function t(t){return e.apply(this,arguments)}return t}()},{key:"_queueLoadingCallback",value:function(e){(0,i.unreachable)("Abstract method `_queueLoadingCallback`.")}},{key:"_prepareFontLoadEvent",value:function(e,t,r){(0,i.unreachable)("Abstract method `_prepareFontLoadEvent`.")}},{key:"isFontLoadingAPISupported",get:function(){var e="undefined"!==typeof this._document&&!!this._document.fonts;return(0,i.shadow)(this,"isFontLoadingAPISupported",e)}},{key:"isSyncFontLoadingSupported",get:function(){(0,i.unreachable)("Abstract method `isSyncFontLoadingSupported`.")}},{key:"_loadTestFont",get:function(){(0,i.unreachable)("Abstract method `_loadTestFont`.")}}]),e}();t.FontLoader=b,t.FontLoader=b=function(e){s(r,e);var t=c(r);function r(e){var n;return g(this,r),n=t.call(this,e),n.loadingContext={requests:[],nextRequestId:0},n.loadTestFontId=0,n}return m(r,[{key:"_queueLoadingCallback",value:function(e){function t(){(0,i.assert)(!n.done,"completeRequest() cannot be called twice."),n.done=!0;while(r.requests.length>0&&r.requests[0].done){var e=r.requests.shift();setTimeout(e.callback,0)}}var r=this.loadingContext,n={id:"pdfjs-font-loading-".concat(r.nextRequestId++),done:!1,complete:t,callback:e};return r.requests.push(n),n}},{key:"_prepareFontLoadEvent",value:function(e,t,r){var n,a,o=this;function s(e,t){return e.charCodeAt(t)<<24|e.charCodeAt(t+1)<<16|e.charCodeAt(t+2)<<8|255&e.charCodeAt(t+3)}function u(e,t,r,n){var i=e.substring(0,t),a=e.substring(t+r);return i+n+a}var c=this._document.createElement("canvas");c.width=1,c.height=1;var l=c.getContext("2d"),h=0;function f(e,t){if(h++,h>30)return(0,i.warn)("Load test font never loaded."),void t();l.font="30px "+e,l.fillText(".",0,20);var r=l.getImageData(0,0,1,1);r.data[3]>0?t():setTimeout(f.bind(null,e,t))}var d="lt".concat(Date.now()).concat(this.loadTestFontId++),p=this._loadTestFont,v=976;p=u(p,v,d.length,d);var g=16,y=1482184792,m=s(p,g);for(n=0,a=d.length-3;n<a;n+=4)m=m-y+s(d,n)|0;n<d.length&&(m=m-y+s(d+"XXX",n)|0),p=u(p,g,4,(0,i.string32)(m));var b="url(data:font/opentype;base64,".concat(btoa(p),");"),_='@font-face {font-family:"'.concat(d,'";src:').concat(b,"}");this.insertRule(_);var w=[];for(n=0,a=t.length;n<a;n++)w.push(t[n].loadedName);w.push(d);var S=this._document.createElement("div");for(S.style.visibility="hidden",S.style.width=S.style.height="10px",S.style.position="absolute",S.style.top=S.style.left="0px",n=0,a=w.length;n<a;++n){var A=this._document.createElement("span");A.textContent="Hi",A.style.fontFamily=w[n],S.appendChild(A)}this._document.body.appendChild(S),f(d,(function(){o._document.body.removeChild(S),r.complete()}))}},{key:"isSyncFontLoadingSupported",get:function(){var e=!1;if("undefined"===typeof navigator)e=!0;else{var t=/Mozilla\/5.0.*?rv:(\d+).*? Gecko/.exec(navigator.userAgent);t&&t[1]>=14&&(e=!0)}return(0,i.shadow)(this,"isSyncFontLoadingSupported",e)}},{key:"_loadTestFont",get:function(){var e=function(){return atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA==")};return(0,i.shadow)(this,"_loadTestFont",e())}}]),r}(_);var w=function(){function e(t,r){var n=r.isEvalSupported,i=void 0===n||n,a=r.disableFontFace,o=void 0!==a&&a,s=r.ignoreErrors,u=void 0!==s&&s,c=r.onUnsupportedFeature,l=void 0===c?null:c,h=r.fontRegistry,f=void 0===h?null:h;for(var d in g(this,e),this.compiledGlyphs=Object.create(null),t)this[d]=t[d];this.isEvalSupported=!1!==i,this.disableFontFace=!0===o,this.ignoreErrors=!0===u,this._onUnsupportedFeature=l,this.fontRegistry=f}return m(e,[{key:"createNativeFontFace",value:function(){if(!this.data||this.disableFontFace)return null;var e=new FontFace(this.loadedName,this.data,{});return this.fontRegistry&&this.fontRegistry.registerFont(this),e}},{key:"createFontFaceRule",value:function(){if(!this.data||this.disableFontFace)return null;var e=(0,i.bytesToString)(new Uint8Array(this.data)),t="url(data:".concat(this.mimetype,";base64,").concat(btoa(e),");"),r='@font-face {font-family:"'.concat(this.loadedName,'";src:').concat(t,"}");return this.fontRegistry&&this.fontRegistry.registerFont(this,t),r}},{key:"getPathGenerator",value:function(e,t){if(void 0!==this.compiledGlyphs[t])return this.compiledGlyphs[t];var r,n;try{r=e.get(this.loadedName+"_path_"+t)}catch(c){if(!this.ignoreErrors)throw c;return this._onUnsupportedFeature&&this._onUnsupportedFeature({featureId:i.UNSUPPORTED_FEATURES.errorFontGetPath}),(0,i.warn)('getPathGenerator - ignoring character: "'.concat(c,'".')),this.compiledGlyphs[t]=function(e,t){}}if(this.isEvalSupported&&i.IsEvalSupportedCached.value){for(var a,o="",s=0,u=r.length;s<u;s++)n=r[s],a=void 0!==n.args?n.args.join(","):"",o+="c."+n.cmd+"("+a+");\n";return this.compiledGlyphs[t]=new Function("c","size",o)}return this.compiledGlyphs[t]=function(e,t){for(var i=0,a=r.length;i<a;i++)n=r[i],"scale"===n.cmd&&(n.args=[t,-t]),e[n.cmd].apply(e,n.args)}}}]),e}();t.FontFaceObject=w},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.NodeCMapReaderFactory=t.NodeCanvasFactory=void 0;var n=r(1),i=r(7),a=r(5);function o(e){return o="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function s(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function u(e,t,r){return t&&s(e.prototype,t),r&&s(e,r),e}function c(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&l(e,t)}function l(e,t){return l=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},l(e,t)}function h(e){var t=p();return function(){var r,n=v(e);if(t){var i=v(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return f(this,r)}}function f(e,t){return!t||"object"!==o(t)&&"function"!==typeof t?d(e):t}function d(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function p(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function v(e){return v=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},v(e)}function g(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var y=function e(){g(this,e),(0,a.unreachable)("Not implemented: NodeCanvasFactory")};t.NodeCanvasFactory=y;var m=function e(){g(this,e),(0,a.unreachable)("Not implemented: NodeCMapReaderFactory")};t.NodeCMapReaderFactory=m,i.isNodeJS&&(t.NodeCanvasFactory=y=function(e){c(r,e);var t=h(r);function r(){return g(this,r),t.apply(this,arguments)}return u(r,[{key:"create",value:function(e,t){if(e<=0||t<=0)throw new Error("Invalid canvas size");var r=__webpack_require__(9),n=r.createCanvas(e,t);return{canvas:n,context:n.getContext("2d")}}}]),r}(n.BaseCanvasFactory),t.NodeCMapReaderFactory=m=function(e){c(r,e);var t=h(r);function r(){return g(this,r),t.apply(this,arguments)}return u(r,[{key:"_fetchData",value:function(e,t){return new Promise((function(r,n){var i=__webpack_require__(8);i.readFile(e,(function(e,i){!e&&i?r({cMapData:new Uint8Array(i),compressionType:t}):n(new Error(e))}))}))}}]),r}(n.BaseCMapReaderFactory))},function(e,t,r){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function a(e,t,r){return t&&i(e.prototype,t),r&&i(e,r),e}Object.defineProperty(t,"__esModule",{value:!0}),t.AnnotationStorage=void 0;var o=function(){function e(){n(this,e),this._storage=new Map,this._modified=!1,this.onSetModified=null,this.onResetModified=null}return a(e,[{key:"getOrCreateValue",value:function(e,t){return this._storage.has(e)?this._storage.get(e):(this._storage.set(e,t),t)}},{key:"setValue",value:function(e,t){this._storage.get(e)!==t&&this._setModified(),this._storage.set(e,t)}},{key:"getAll",value:function(){return 0===this._storage.size?null:Object.fromEntries(this._storage)}},{key:"_setModified",value:function(){this._modified||(this._modified=!0,"function"===typeof this.onSetModified&&this.onSetModified())}},{key:"resetModified",value:function(){this._modified&&(this._modified=!1,"function"===typeof this.onResetModified&&this.onResetModified())}},{key:"size",get:function(){return this._storage.size}}]),e}();t.AnnotationStorage=o},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.apiCompatibilityParams=void 0;var n=r(7),i=Object.create(null);(function(){n.isNodeJS&&(i.disableFontFace=!0)})();var a=Object.freeze(i);t.apiCompatibilityParams=a},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CanvasGraphics=void 0;var n=r(5),i=r(208);function a(e,t){var r;if("undefined"===typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(r=o(e))||t&&e&&"number"===typeof e.length){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,u=!1;return{s:function(){r=e[Symbol.iterator]()},n:function(){var e=r.next();return s=e.done,e},e:function(e){u=!0,a=e},f:function(){try{s||null==r["return"]||r["return"]()}finally{if(u)throw a}}}}function o(e,t){if(e){if("string"===typeof e)return s(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?s(e,t):void 0}}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var u=16,c=100,l=4096,h=.65,f=!0,d=1e3,p=16;function v(e){e.mozCurrentTransform||(e._originalSave=e.save,e._originalRestore=e.restore,e._originalRotate=e.rotate,e._originalScale=e.scale,e._originalTranslate=e.translate,e._originalTransform=e.transform,e._originalSetTransform=e.setTransform,e._transformMatrix=e._transformMatrix||[1,0,0,1,0,0],e._transformStack=[],Object.defineProperty(e,"mozCurrentTransform",{get:function(){return this._transformMatrix}}),Object.defineProperty(e,"mozCurrentTransformInverse",{get:function(){var e=this._transformMatrix,t=e[0],r=e[1],n=e[2],i=e[3],a=e[4],o=e[5],s=t*i-r*n,u=r*n-t*i;return[i/s,r/u,n/u,t/s,(i*a-n*o)/u,(r*a-t*o)/s]}}),e.save=function(){var e=this._transformMatrix;this._transformStack.push(e),this._transformMatrix=e.slice(0,6),this._originalSave()},e.restore=function(){var e=this._transformStack.pop();e&&(this._transformMatrix=e,this._originalRestore())},e.translate=function(e,t){var r=this._transformMatrix;r[4]=r[0]*e+r[2]*t+r[4],r[5]=r[1]*e+r[3]*t+r[5],this._originalTranslate(e,t)},e.scale=function(e,t){var r=this._transformMatrix;r[0]=r[0]*e,r[1]=r[1]*e,r[2]=r[2]*t,r[3]=r[3]*t,this._originalScale(e,t)},e.transform=function(t,r,n,i,a,o){var s=this._transformMatrix;this._transformMatrix=[s[0]*t+s[2]*r,s[1]*t+s[3]*r,s[0]*n+s[2]*i,s[1]*n+s[3]*i,s[0]*a+s[2]*o+s[4],s[1]*a+s[3]*o+s[5]],e._originalTransform(t,r,n,i,a,o)},e.setTransform=function(t,r,n,i,a,o){this._transformMatrix=[t,r,n,i,a,o],e._originalSetTransform(t,r,n,i,a,o)},e.rotate=function(e){var t=Math.cos(e),r=Math.sin(e),n=this._transformMatrix;this._transformMatrix=[n[0]*t+n[2]*r,n[1]*t+n[3]*r,n[0]*-r+n[2]*t,n[1]*-r+n[3]*t,n[4],n[5]],this._originalRotate(e)})}var g=function(){function e(e){this.canvasFactory=e,this.cache=Object.create(null)}return e.prototype={getCanvas:function(e,t,r,n){var i;return void 0!==this.cache[e]?(i=this.cache[e],this.canvasFactory.reset(i,t,r),i.context.setTransform(1,0,0,1,0,0)):(i=this.canvasFactory.create(t,r),this.cache[e]=i),n&&v(i.context),i},clear:function(){for(var e in this.cache){var t=this.cache[e];this.canvasFactory.destroy(t),delete this.cache[e]}}},e}();function y(e){var t,r,n,i,a=1e3,o=e.width,s=e.height,u=o+1,c=new Uint8Array(u*(s+1)),l=new Uint8Array([0,2,4,0,1,0,5,4,8,10,0,8,0,2,1,0]),h=o+7&-8,f=e.data,d=new Uint8Array(h*s),p=0;for(t=0,i=f.length;t<i;t++){var v=128,g=f[t];while(v>0)d[p++]=g&v?0:255,v>>=1}var y=0;for(p=0,0!==d[p]&&(c[0]=1,++y),r=1;r<o;r++)d[p]!==d[p+1]&&(c[r]=d[p]?2:1,++y),p++;for(0!==d[p]&&(c[r]=2,++y),t=1;t<s;t++){p=t*h,n=t*u,d[p-h]!==d[p]&&(c[n]=d[p]?1:8,++y);var m=(d[p]?4:0)+(d[p-h]?8:0);for(r=1;r<o;r++)m=(m>>2)+(d[p+1]?4:0)+(d[p-h+1]?8:0),l[m]&&(c[n+r]=l[m],++y),p++;if(d[p-h]!==d[p]&&(c[n+r]=d[p]?2:4,++y),y>a)return null}for(p=h*(s-1),n=t*u,0!==d[p]&&(c[n]=8,++y),r=1;r<o;r++)d[p]!==d[p+1]&&(c[n+r]=d[p]?4:8,++y),p++;if(0!==d[p]&&(c[n+r]=4,++y),y>a)return null;var b=new Int32Array([0,u,-1,0,-u,0,0,0,1]),_=[];for(t=0;y&&t<=s;t++){var w=t*u,S=w+o;while(w<S&&!c[w])w++;if(w!==S){var A,k=[w%u,t],x=c[w],P=w;do{var C=b[x];do{w+=C}while(!c[w]);A=c[w],5!==A&&10!==A?(x=A,c[w]=0):(x=A&51*x>>4,c[w]&=x>>2|x<<2),k.push(w%u),k.push(w/u|0),c[w]||--y}while(P!==w);_.push(k),--t}}var T=function(e){e.save(),e.scale(1/o,-1/s),e.translate(0,-s),e.beginPath();for(var t=0,r=_.length;t<r;t++){var n=_[t];e.moveTo(n[0],n[1]);for(var i=2,a=n.length;i<a;i+=2)e.lineTo(n[i],n[i+1])}e.fill(),e.beginPath(),e.restore()};return T}var m=function(){function e(){this.alphaIsShape=!1,this.fontSize=0,this.fontSizeScale=1,this.textMatrix=n.IDENTITY_MATRIX,this.textMatrixScale=1,this.fontMatrix=n.FONT_IDENTITY_MATRIX,this.leading=0,this.x=0,this.y=0,this.lineX=0,this.lineY=0,this.charSpacing=0,this.wordSpacing=0,this.textHScale=1,this.textRenderingMode=n.TextRenderingMode.FILL,this.textRise=0,this.fillColor="#000000",this.strokeColor="#000000",this.patternFill=!1,this.fillAlpha=1,this.strokeAlpha=1,this.lineWidth=1,this.activeSMask=null,this.resumeSMaskCtx=null,this.transferMaps=null}return e.prototype={clone:function(){return Object.create(this)},setCurrentPoint:function(e,t){this.x=e,this.y=t}},e}(),b=function(){var e=15,t=10;function r(e,t,r,n,i,a,o){this.ctx=e,this.current=new m,this.stateStack=[],this.pendingClip=null,this.pendingEOFill=!1,this.res=null,this.xobjs=null,this.commonObjs=t,this.objs=r,this.canvasFactory=n,this.webGLContext=i,this.imageLayer=a,this.groupStack=[],this.processingType3=null,this.baseTransform=null,this.baseTransformStack=[],this.groupLevel=0,this.smaskStack=[],this.smaskCounter=0,this.tempSMask=null,this.contentVisible=!0,this.markedContentStack=[],this.optionalContentConfig=o,this.cachedCanvases=new g(this.canvasFactory),e&&v(e),this._cachedGetSinglePixelWidth=null}function o(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if("undefined"!==typeof ImageData&&t instanceof ImageData)e.putImageData(t,0,0);else{var i,a,o,s,u,c,l,h,f,d=t.height,v=t.width,g=d%p,y=(d-g)/p,m=0===g?y:y+1,b=e.createImageData(v,p),_=0,w=t.data,S=b.data;if(r)switch(r.length){case 1:c=r[0],l=r[0],h=r[0],f=r[0];break;case 4:c=r[0],l=r[1],h=r[2],f=r[3];break}if(t.kind===n.ImageKind.GRAYSCALE_1BPP){var A=w.byteLength,k=new Uint32Array(S.buffer,0,S.byteLength>>2),x=k.length,P=v+7>>3,C=4294967295,T=n.IsLittleEndianCached.value?4278190080:255;if(f&&255===f[0]&&0===f[255]){var R=[T,C];C=R[0],T=R[1]}for(a=0;a<m;a++){for(s=a<y?p:g,i=0,o=0;o<s;o++){for(var E=A-_,L=0,O=E>P?v:8*E-7,I=-8&O,M=0,F=0;L<I;L+=8)F=w[_++],k[i++]=128&F?C:T,k[i++]=64&F?C:T,k[i++]=32&F?C:T,k[i++]=16&F?C:T,k[i++]=8&F?C:T,k[i++]=4&F?C:T,k[i++]=2&F?C:T,k[i++]=1&F?C:T;for(;L<O;L++)0===M&&(F=w[_++],M=128),k[i++]=F&M?C:T,M>>=1}while(i<x)k[i++]=0;e.putImageData(b,0,a*p)}}else if(t.kind===n.ImageKind.RGBA_32BPP){var N=!!(c||l||h);for(o=0,u=v*p*4,a=0;a<y;a++){if(S.set(w.subarray(_,_+u)),_+=u,N)for(var D=0;D<u;D+=4)c&&(S[D+0]=c[S[D+0]]),l&&(S[D+1]=l[S[D+1]]),h&&(S[D+2]=h[S[D+2]]);e.putImageData(b,0,o),o+=p}if(a<m){if(u=v*g*4,S.set(w.subarray(_,_+u)),N)for(var j=0;j<u;j+=4)c&&(S[j+0]=c[S[j+0]]),l&&(S[j+1]=l[S[j+1]]),h&&(S[j+2]=h[S[j+2]]);e.putImageData(b,0,o)}}else{if(t.kind!==n.ImageKind.RGB_24BPP)throw new Error("bad image kind: ".concat(t.kind));var U=!!(c||l||h);for(s=p,u=v*s,a=0;a<m;a++){for(a>=y&&(s=g,u=v*s),i=0,o=u;o--;)S[i++]=w[_++],S[i++]=w[_++],S[i++]=w[_++],S[i++]=255;if(U)for(var q=0;q<i;q+=4)c&&(S[q+0]=c[S[q+0]]),l&&(S[q+1]=l[S[q+1]]),h&&(S[q+2]=h[S[q+2]]);e.putImageData(b,0,a*p)}}}}function s(e,t){for(var r=t.height,n=t.width,i=r%p,a=(r-i)/p,o=0===i?a:a+1,s=e.createImageData(n,p),u=0,c=t.data,l=s.data,h=0;h<o;h++){for(var f=h<a?p:i,d=3,v=0;v<f;v++)for(var g=0,y=0;y<n;y++){if(!g){var m=c[u++];g=128}l[d]=m&g?0:255,d+=4,g>>=1}e.putImageData(s,0,h*p)}}function b(e,t){for(var r=["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font"],n=0,i=r.length;n<i;n++){var a=r[n];void 0!==e[a]&&(t[a]=e[a])}void 0!==e.setLineDash&&(t.setLineDash(e.getLineDash()),t.lineDashOffset=e.lineDashOffset)}function _(e){e.strokeStyle="#000000",e.fillStyle="#000000",e.fillRule="nonzero",e.globalAlpha=1,e.lineWidth=1,e.lineCap="butt",e.lineJoin="miter",e.miterLimit=10,e.globalCompositeOperation="source-over",e.font="10px sans-serif",void 0!==e.setLineDash&&(e.setLineDash([]),e.lineDashOffset=0)}function w(e,t,r,n){for(var i=e.length,a=3;a<i;a+=4){var o=e[a];if(0===o)e[a-3]=t,e[a-2]=r,e[a-1]=n;else if(o<255){var s=255-o;e[a-3]=e[a-3]*o+t*s>>8,e[a-2]=e[a-2]*o+r*s>>8,e[a-1]=e[a-1]*o+n*s>>8}}}function S(e,t,r){for(var n=e.length,i=1/255,a=3;a<n;a+=4){var o=r?r[e[a]]:e[a];t[a]=t[a]*o*i|0}}function A(e,t,r){for(var n=e.length,i=3;i<n;i+=4){var a=77*e[i-3]+152*e[i-2]+28*e[i-1];t[i]=r?t[i]*r[a>>8]>>8:t[i]*a>>16}}function k(e,t,r,n,i,a,o){var s,u=!!a,c=u?a[0]:0,l=u?a[1]:0,h=u?a[2]:0;s="Luminosity"===i?A:S;for(var f=1048576,d=Math.min(n,Math.ceil(f/r)),p=0;p<n;p+=d){var v=Math.min(d,n-p),g=e.getImageData(0,p,r,v),y=t.getImageData(0,p,r,v);u&&w(g.data,c,l,h),s(g.data,y.data,o),e.putImageData(y,0,p)}}function x(e,t,r,n){var i=t.canvas,a=t.context;e.setTransform(t.scaleX,0,0,t.scaleY,t.offsetX,t.offsetY);var o=t.backdrop||null;if(!t.transferMap&&n.isEnabled){var s=n.composeSMask({layer:r.canvas,mask:i,properties:{subtype:t.subtype,backdrop:o}});return e.setTransform(1,0,0,1,0,0),void e.drawImage(s,t.offsetX,t.offsetY)}k(a,r,i.width,i.height,t.subtype,o,t.transferMap),e.drawImage(i,0,0)}var P=["butt","round","square"],C=["miter","round","bevel"],T={},R={};for(var E in r.prototype={beginDrawing:function(e){var t=e.transform,r=e.viewport,n=e.transparency,i=void 0!==n&&n,a=e.background,o=void 0===a?null:a,s=this.ctx.canvas.width,u=this.ctx.canvas.height;if(this.ctx.save(),this.ctx.fillStyle=o||"rgb(255, 255, 255)",this.ctx.fillRect(0,0,s,u),this.ctx.restore(),i){var c=this.cachedCanvases.getCanvas("transparent",s,u,!0);this.compositeCtx=this.ctx,this.transparentCanvas=c.canvas,this.ctx=c.context,this.ctx.save(),this.ctx.transform.apply(this.ctx,this.compositeCtx.mozCurrentTransform)}this.ctx.save(),_(this.ctx),t&&this.ctx.transform.apply(this.ctx,t),this.ctx.transform.apply(this.ctx,r.transform),this.baseTransform=this.ctx.mozCurrentTransform.slice(),this.imageLayer&&this.imageLayer.beginLayout()},executeOperatorList:function(r,i,o,s){var u=r.argsArray,c=r.fnArray,l=i||0,h=u.length;if(h===l)return l;var f,d=h-l>t&&"function"===typeof o,p=d?Date.now()+e:0,v=0,g=this.commonObjs,y=this.objs;while(1){if(void 0!==s&&l===s.nextBreakPoint)return s.breakIt(l,o),l;if(f=c[l],f!==n.OPS.dependency)this[f].apply(this,u[l]);else{var m,b=a(u[l]);try{for(b.s();!(m=b.n()).done;){var _=m.value,w=_.startsWith("g_")?g:y;if(!w.has(_))return w.get(_,o),l}}catch(S){b.e(S)}finally{b.f()}}if(l++,l===h)return l;if(d&&++v>t){if(Date.now()>p)return o(),l;v=0}}},endDrawing:function(){null!==this.current.activeSMask&&this.endSMaskGroup(),this.ctx.restore(),this.transparentCanvas&&(this.ctx=this.compositeCtx,this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.drawImage(this.transparentCanvas,0,0),this.ctx.restore(),this.transparentCanvas=null),this.cachedCanvases.clear(),this.webGLContext.clear(),this.imageLayer&&this.imageLayer.endLayout()},setLineWidth:function(e){this.current.lineWidth=e,this.ctx.lineWidth=e},setLineCap:function(e){this.ctx.lineCap=P[e]},setLineJoin:function(e){this.ctx.lineJoin=C[e]},setMiterLimit:function(e){this.ctx.miterLimit=e},setDash:function(e,t){var r=this.ctx;void 0!==r.setLineDash&&(r.setLineDash(e),r.lineDashOffset=t)},setRenderingIntent:function(e){},setFlatness:function(e){},setGState:function(e){for(var t=0,r=e.length;t<r;t++){var n=e[t],i=n[0],a=n[1];switch(i){case"LW":this.setLineWidth(a);break;case"LC":this.setLineCap(a);break;case"LJ":this.setLineJoin(a);break;case"ML":this.setMiterLimit(a);break;case"D":this.setDash(a[0],a[1]);break;case"RI":this.setRenderingIntent(a);break;case"FL":this.setFlatness(a);break;case"Font":this.setFont(a[0],a[1]);break;case"CA":this.current.strokeAlpha=n[1];break;case"ca":this.current.fillAlpha=n[1],this.ctx.globalAlpha=n[1];break;case"BM":this.ctx.globalCompositeOperation=a;break;case"SMask":this.current.activeSMask&&(this.stateStack.length>0&&this.stateStack[this.stateStack.length-1].activeSMask===this.current.activeSMask?this.suspendSMaskGroup():this.endSMaskGroup()),this.current.activeSMask=a?this.tempSMask:null,this.current.activeSMask&&this.beginSMaskGroup(),this.tempSMask=null;break;case"TR":this.current.transferMaps=a}}},beginSMaskGroup:function(){var e=this.current.activeSMask,t=e.canvas.width,r=e.canvas.height,n="smaskGroupAt"+this.groupLevel,i=this.cachedCanvases.getCanvas(n,t,r,!0),a=this.ctx,o=a.mozCurrentTransform;this.ctx.save();var s=i.context;s.scale(1/e.scaleX,1/e.scaleY),s.translate(-e.offsetX,-e.offsetY),s.transform.apply(s,o),e.startTransformInverse=s.mozCurrentTransformInverse,b(a,s),this.ctx=s,this.setGState([["BM","source-over"],["ca",1],["CA",1]]),this.groupStack.push(a),this.groupLevel++},suspendSMaskGroup:function(){var e=this.ctx;this.groupLevel--,this.ctx=this.groupStack.pop(),x(this.ctx,this.current.activeSMask,e,this.webGLContext),this.ctx.restore(),this.ctx.save(),b(e,this.ctx),this.current.resumeSMaskCtx=e;var t=n.Util.transform(this.current.activeSMask.startTransformInverse,e.mozCurrentTransform);this.ctx.transform.apply(this.ctx,t),e.save(),e.setTransform(1,0,0,1,0,0),e.clearRect(0,0,e.canvas.width,e.canvas.height),e.restore()},resumeSMaskGroup:function(){var e=this.current.resumeSMaskCtx,t=this.ctx;this.ctx=e,this.groupStack.push(t),this.groupLevel++},endSMaskGroup:function(){var e=this.ctx;this.groupLevel--,this.ctx=this.groupStack.pop(),x(this.ctx,this.current.activeSMask,e,this.webGLContext),this.ctx.restore(),b(e,this.ctx);var t=n.Util.transform(this.current.activeSMask.startTransformInverse,e.mozCurrentTransform);this.ctx.transform.apply(this.ctx,t)},save:function(){this.ctx.save();var e=this.current;this.stateStack.push(e),this.current=e.clone(),this.current.resumeSMaskCtx=null},restore:function(){this.current.resumeSMaskCtx&&this.resumeSMaskGroup(),null===this.current.activeSMask||0!==this.stateStack.length&&this.stateStack[this.stateStack.length-1].activeSMask===this.current.activeSMask||this.endSMaskGroup(),0!==this.stateStack.length&&(this.current=this.stateStack.pop(),this.ctx.restore(),this.pendingClip=null,this._cachedGetSinglePixelWidth=null)},transform:function(e,t,r,n,i,a){this.ctx.transform(e,t,r,n,i,a),this._cachedGetSinglePixelWidth=null},constructPath:function(e,t){for(var r=this.ctx,i=this.current,a=i.x,o=i.y,s=0,u=0,c=e.length;s<c;s++)switch(0|e[s]){case n.OPS.rectangle:a=t[u++],o=t[u++];var l=t[u++],h=t[u++];0===l&&r.lineWidth<this.getSinglePixelWidth()&&(l=this.getSinglePixelWidth()),0===h&&r.lineWidth<this.getSinglePixelWidth()&&(h=this.getSinglePixelWidth());var f=a+l,d=o+h;r.moveTo(a,o),r.lineTo(f,o),r.lineTo(f,d),r.lineTo(a,d),r.lineTo(a,o),r.closePath();break;case n.OPS.moveTo:a=t[u++],o=t[u++],r.moveTo(a,o);break;case n.OPS.lineTo:a=t[u++],o=t[u++],r.lineTo(a,o);break;case n.OPS.curveTo:a=t[u+4],o=t[u+5],r.bezierCurveTo(t[u],t[u+1],t[u+2],t[u+3],a,o),u+=6;break;case n.OPS.curveTo2:r.bezierCurveTo(a,o,t[u],t[u+1],t[u+2],t[u+3]),a=t[u+2],o=t[u+3],u+=4;break;case n.OPS.curveTo3:a=t[u+2],o=t[u+3],r.bezierCurveTo(t[u],t[u+1],a,o,a,o),u+=4;break;case n.OPS.closePath:r.closePath();break}i.setCurrentPoint(a,o)},closePath:function(){this.ctx.closePath()},stroke:function(e){e="undefined"===typeof e||e;var t=this.ctx,r=this.current.strokeColor;if(t.globalAlpha=this.current.strokeAlpha,this.contentVisible)if(r&&r.hasOwnProperty("type")&&"Pattern"===r.type){t.save();var i=t.mozCurrentTransform,a=n.Util.singularValueDecompose2dScale(i)[0];t.strokeStyle=r.getPattern(t,this),t.lineWidth=Math.max(this.getSinglePixelWidth()*h,this.current.lineWidth*a),t.stroke(),t.restore()}else t.lineWidth=Math.max(this.getSinglePixelWidth()*h,this.current.lineWidth),t.stroke();e&&this.consumePath(),t.globalAlpha=this.current.fillAlpha},closeStroke:function(){this.closePath(),this.stroke()},fill:function(e){e="undefined"===typeof e||e;var t=this.ctx,r=this.current.fillColor,n=this.current.patternFill,i=!1;n&&(t.save(),this.baseTransform&&t.setTransform.apply(t,this.baseTransform),t.fillStyle=r.getPattern(t,this),i=!0),this.contentVisible&&(this.pendingEOFill?(t.fill("evenodd"),this.pendingEOFill=!1):t.fill()),i&&t.restore(),e&&this.consumePath()},eoFill:function(){this.pendingEOFill=!0,this.fill()},fillStroke:function(){this.fill(!1),this.stroke(!1),this.consumePath()},eoFillStroke:function(){this.pendingEOFill=!0,this.fillStroke()},closeFillStroke:function(){this.closePath(),this.fillStroke()},closeEOFillStroke:function(){this.pendingEOFill=!0,this.closePath(),this.fillStroke()},endPath:function(){this.consumePath()},clip:function(){this.pendingClip=T},eoClip:function(){this.pendingClip=R},beginText:function(){this.current.textMatrix=n.IDENTITY_MATRIX,this.current.textMatrixScale=1,this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0},endText:function(){var e=this.pendingTextPaths,t=this.ctx;if(void 0!==e){t.save(),t.beginPath();for(var r=0;r<e.length;r++){var n=e[r];t.setTransform.apply(t,n.transform),t.translate(n.x,n.y),n.addToPath(t,n.fontSize)}t.restore(),t.clip(),t.beginPath(),delete this.pendingTextPaths}else t.beginPath()},setCharSpacing:function(e){this.current.charSpacing=e},setWordSpacing:function(e){this.current.wordSpacing=e},setHScale:function(e){this.current.textHScale=e/100},setLeading:function(e){this.current.leading=-e},setFont:function(e,t){var r=this.commonObjs.get(e),i=this.current;if(!r)throw new Error("Can't find font for ".concat(e));if(i.fontMatrix=r.fontMatrix?r.fontMatrix:n.FONT_IDENTITY_MATRIX,0!==i.fontMatrix[0]&&0!==i.fontMatrix[3]||(0,n.warn)("Invalid font matrix for font "+e),t<0?(t=-t,i.fontDirection=-1):i.fontDirection=1,this.current.font=r,this.current.fontSize=t,!r.isType3Font){var a=r.loadedName||"sans-serif",o="normal";r.black?o="900":r.bold&&(o="bold");var s=r.italic?"italic":"normal",l='"'.concat(a,'", ').concat(r.fallbackName),h=t;t<u?h=u:t>c&&(h=c),this.current.fontSizeScale=t/h,this.ctx.font="".concat(s," ").concat(o," ").concat(h,"px ").concat(l)}},setTextRenderingMode:function(e){this.current.textRenderingMode=e},setTextRise:function(e){this.current.textRise=e},moveText:function(e,t){this.current.x=this.current.lineX+=e,this.current.y=this.current.lineY+=t},setLeadingMoveText:function(e,t){this.setLeading(-t),this.moveText(e,t)},setTextMatrix:function(e,t,r,n,i,a){this.current.textMatrix=[e,t,r,n,i,a],this.current.textMatrixScale=Math.sqrt(e*e+t*t),this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0},nextLine:function(){this.moveText(0,this.current.leading)},paintChar:function(e,t,r,i){var a,o=this.ctx,s=this.current,u=s.font,c=s.textRenderingMode,l=s.fontSize/s.fontSizeScale,h=c&n.TextRenderingMode.FILL_STROKE_MASK,f=!!(c&n.TextRenderingMode.ADD_TO_PATH_FLAG),d=s.patternFill&&!u.missingFile;if((u.disableFontFace||f||d)&&(a=u.getPathGenerator(this.commonObjs,e)),u.disableFontFace||d?(o.save(),o.translate(t,r),o.beginPath(),a(o,l),i&&o.setTransform.apply(o,i),h!==n.TextRenderingMode.FILL&&h!==n.TextRenderingMode.FILL_STROKE||o.fill(),h!==n.TextRenderingMode.STROKE&&h!==n.TextRenderingMode.FILL_STROKE||o.stroke(),o.restore()):(h!==n.TextRenderingMode.FILL&&h!==n.TextRenderingMode.FILL_STROKE||o.fillText(e,t,r),h!==n.TextRenderingMode.STROKE&&h!==n.TextRenderingMode.FILL_STROKE||o.strokeText(e,t,r)),f){var p=this.pendingTextPaths||(this.pendingTextPaths=[]);p.push({transform:o.mozCurrentTransform,x:t,y:r,fontSize:l,addToPath:a})}},get isFontSubpixelAAEnabled(){var e=this.cachedCanvases.getCanvas("isFontSubpixelAAEnabled",10,10),t=e.context;t.scale(1.5,1),t.fillText("I",0,10);for(var r=t.getImageData(0,0,10,10).data,i=!1,a=3;a<r.length;a+=4)if(r[a]>0&&r[a]<255){i=!0;break}return(0,n.shadow)(this,"isFontSubpixelAAEnabled",i)},showText:function(e){var t=this.current,r=t.font;if(r.isType3Font)return this.showType3Text(e);var i=t.fontSize;if(0!==i){var a,o=this.ctx,s=t.fontSizeScale,u=t.charSpacing,c=t.wordSpacing,l=t.fontDirection,f=t.textHScale*l,d=e.length,p=r.vertical,v=p?1:-1,g=r.defaultVMetrics,y=i*t.fontMatrix[0],m=t.textRenderingMode===n.TextRenderingMode.FILL&&!r.disableFontFace&&!t.patternFill;if(o.save(),t.patternFill){o.save();var b=t.fillColor.getPattern(o,this);a=o.mozCurrentTransform,o.restore(),o.fillStyle=b}o.transform.apply(o,t.textMatrix),o.translate(t.x,t.y+t.textRise),l>0?o.scale(f,-1):o.scale(f,1);var _=t.lineWidth,w=t.textMatrixScale;if(0===w||0===_){var S=t.textRenderingMode&n.TextRenderingMode.FILL_STROKE_MASK;S!==n.TextRenderingMode.STROKE&&S!==n.TextRenderingMode.FILL_STROKE||(this._cachedGetSinglePixelWidth=null,_=this.getSinglePixelWidth()*h)}else _/=w;1!==s&&(o.scale(s,s),_/=s),o.lineWidth=_;var A,k=0;for(A=0;A<d;++A){var x=e[A];if((0,n.isNum)(x))k+=v*x*i/1e3;else{var P,C,T,R,E,L,O,I,M=!1,F=(x.isSpace?c:0)+u,N=x.fontChar,D=x.accent,j=x.width;if(p)E=x.vmetric||g,L=x.vmetric?E[1]:.5*j,L=-L*y,O=E[2]*y,j=E?-E[0]:j,P=L/s,C=(k+O)/s;else P=k/s,C=0;if(r.remeasure&&j>0){var U=1e3*o.measureText(N).width/i*s;if(j<U&&this.isFontSubpixelAAEnabled){var q=j/U;M=!0,o.save(),o.scale(q,1),P/=q}else j!==U&&(P+=(j-U)/2e3*i/s)}this.contentVisible&&(x.isInFont||r.missingFile)&&(m&&!D?o.fillText(N,P,C):(this.paintChar(N,P,C,a),D&&(T=P+i*D.offset.x/s,R=C-i*D.offset.y/s,this.paintChar(D.fontChar,T,R,a)))),I=p?j*y-F*l:j*y+F*l,k+=I,M&&o.restore()}}p?t.y-=k:t.x+=k*f,o.restore()}},showType3Text:function(e){var t,r,i,a,o=this.ctx,s=this.current,u=s.font,c=s.fontSize,l=s.fontDirection,h=u.vertical?1:-1,f=s.charSpacing,d=s.wordSpacing,p=s.textHScale*l,v=s.fontMatrix||n.FONT_IDENTITY_MATRIX,g=e.length,y=s.textRenderingMode===n.TextRenderingMode.INVISIBLE;if(!y&&0!==c){for(this._cachedGetSinglePixelWidth=null,o.save(),o.transform.apply(o,s.textMatrix),o.translate(s.x,s.y),o.scale(p,l),t=0;t<g;++t)if(r=e[t],(0,n.isNum)(r))a=h*r*c/1e3,this.ctx.translate(a,0),s.x+=a*p;else{var m=(r.isSpace?d:0)+f,b=u.charProcOperatorList[r.operatorListId];if(b){this.contentVisible&&(this.processingType3=r,this.save(),o.scale(c,c),o.transform.apply(o,v),this.executeOperatorList(b),this.restore());var _=n.Util.applyTransform([r.width,0],v);i=_[0]*c+m,o.translate(i,0),s.x+=i*p}else(0,n.warn)('Type3 character "'.concat(r.operatorListId,'" is not available.'))}o.restore(),this.processingType3=null}},setCharWidth:function(e,t){},setCharWidthAndBounds:function(e,t,r,n,i,a){this.ctx.rect(r,n,i-r,a-n),this.clip(),this.endPath()},getColorN_Pattern:function(e){var t,n=this;if("TilingPattern"===e[0]){var a=e[1],o=this.baseTransform||this.ctx.mozCurrentTransform.slice(),s={createCanvasGraphics:function(e){return new r(e,n.commonObjs,n.objs,n.canvasFactory,n.webGLContext)}};t=new i.TilingPattern(e,a,this.ctx,s,o)}else t=(0,i.getShadingPatternFromIR)(e);return t},setStrokeColorN:function(){this.current.strokeColor=this.getColorN_Pattern(arguments)},setFillColorN:function(){this.current.fillColor=this.getColorN_Pattern(arguments),this.current.patternFill=!0},setStrokeRGBColor:function(e,t,r){var i=n.Util.makeCssRgb(e,t,r);this.ctx.strokeStyle=i,this.current.strokeColor=i},setFillRGBColor:function(e,t,r){var i=n.Util.makeCssRgb(e,t,r);this.ctx.fillStyle=i,this.current.fillColor=i,this.current.patternFill=!1},shadingFill:function(e){if(this.contentVisible){var t=this.ctx;this.save();var r=(0,i.getShadingPatternFromIR)(e);t.fillStyle=r.getPattern(t,this,!0);var a=t.mozCurrentTransformInverse;if(a){var o=t.canvas,s=o.width,u=o.height,c=n.Util.applyTransform([0,0],a),l=n.Util.applyTransform([0,u],a),h=n.Util.applyTransform([s,0],a),f=n.Util.applyTransform([s,u],a),d=Math.min(c[0],l[0],h[0],f[0]),p=Math.min(c[1],l[1],h[1],f[1]),v=Math.max(c[0],l[0],h[0],f[0]),g=Math.max(c[1],l[1],h[1],f[1]);this.ctx.fillRect(d,p,v-d,g-p)}else this.ctx.fillRect(-1e10,-1e10,2e10,2e10);this.restore()}},beginInlineImage:function(){(0,n.unreachable)("Should not call beginInlineImage")},beginImageData:function(){(0,n.unreachable)("Should not call beginImageData")},paintFormXObjectBegin:function(e,t){if(this.contentVisible&&(this.save(),this.baseTransformStack.push(this.baseTransform),Array.isArray(e)&&6===e.length&&this.transform.apply(this,e),this.baseTransform=this.ctx.mozCurrentTransform,t)){var r=t[2]-t[0],n=t[3]-t[1];this.ctx.rect(t[0],t[1],r,n),this.clip(),this.endPath()}},paintFormXObjectEnd:function(){this.contentVisible&&(this.restore(),this.baseTransform=this.baseTransformStack.pop())},beginGroup:function(e){if(this.contentVisible){this.save();var t=this.ctx;e.isolated||(0,n.info)("TODO: Support non-isolated groups."),e.knockout&&(0,n.warn)("Knockout groups not supported.");var r=t.mozCurrentTransform;if(e.matrix&&t.transform.apply(t,e.matrix),!e.bbox)throw new Error("Bounding box is required.");var i=n.Util.getAxialAlignedBoundingBox(e.bbox,t.mozCurrentTransform),a=[0,0,t.canvas.width,t.canvas.height];i=n.Util.intersect(i,a)||[0,0,0,0];var o=Math.floor(i[0]),s=Math.floor(i[1]),u=Math.max(Math.ceil(i[2])-o,1),c=Math.max(Math.ceil(i[3])-s,1),h=1,f=1;u>l&&(h=u/l,u=l),c>l&&(f=c/l,c=l);var d="groupAt"+this.groupLevel;e.smask&&(d+="_smask_"+this.smaskCounter++%2);var p=this.cachedCanvases.getCanvas(d,u,c,!0),v=p.context;v.scale(1/h,1/f),v.translate(-o,-s),v.transform.apply(v,r),e.smask?this.smaskStack.push({canvas:p.canvas,context:v,offsetX:o,offsetY:s,scaleX:h,scaleY:f,subtype:e.smask.subtype,backdrop:e.smask.backdrop,transferMap:e.smask.transferMap||null,startTransformInverse:null}):(t.setTransform(1,0,0,1,0,0),t.translate(o,s),t.scale(h,f)),b(t,v),this.ctx=v,this.setGState([["BM","source-over"],["ca",1],["CA",1]]),this.groupStack.push(t),this.groupLevel++,this.current.activeSMask=null}},endGroup:function(e){if(this.contentVisible){this.groupLevel--;var t=this.ctx;this.ctx=this.groupStack.pop(),void 0!==this.ctx.imageSmoothingEnabled?this.ctx.imageSmoothingEnabled=!1:this.ctx.mozImageSmoothingEnabled=!1,e.smask?this.tempSMask=this.smaskStack.pop():this.ctx.drawImage(t.canvas,0,0),this.restore()}},beginAnnotations:function(){this.save(),this.baseTransform&&this.ctx.setTransform.apply(this.ctx,this.baseTransform)},endAnnotations:function(){this.restore()},beginAnnotation:function(e,t,r){if(this.save(),_(this.ctx),this.current=new m,Array.isArray(e)&&4===e.length){var n=e[2]-e[0],i=e[3]-e[1];this.ctx.rect(e[0],e[1],n,i),this.clip(),this.endPath()}this.transform.apply(this,t),this.transform.apply(this,r)},endAnnotation:function(){this.restore()},paintImageMaskXObject:function(e){if(this.contentVisible){var t=this.ctx,r=e.width,n=e.height,i=this.current.fillColor,a=this.current.patternFill,o=this.processingType3;if(f&&o&&void 0===o.compiled&&(o.compiled=r<=d&&n<=d?y({data:e.data,width:r,height:n}):null),o&&o.compiled)o.compiled(t);else{var u=this.cachedCanvases.getCanvas("maskCanvas",r,n),c=u.context;c.save(),s(c,e),c.globalCompositeOperation="source-in",c.fillStyle=a?i.getPattern(c,this):i,c.fillRect(0,0,r,n),c.restore(),this.paintInlineImageXObject(u.canvas)}}},paintImageMaskXObjectRepeat:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=arguments.length>4?arguments[4]:void 0,a=arguments.length>5?arguments[5]:void 0;if(this.contentVisible){var o=e.width,u=e.height,c=this.current.fillColor,l=this.current.patternFill,h=this.cachedCanvases.getCanvas("maskCanvas",o,u),f=h.context;f.save(),s(f,e),f.globalCompositeOperation="source-in",f.fillStyle=l?c.getPattern(f,this):c,f.fillRect(0,0,o,u),f.restore();for(var d=this.ctx,p=0,v=a.length;p<v;p+=2)d.save(),d.transform(t,r,n,i,a[p],a[p+1]),d.scale(1,-1),d.drawImage(h.canvas,0,0,o,u,0,-1,1,1),d.restore()}},paintImageMaskXObjectGroup:function(e){if(this.contentVisible)for(var t=this.ctx,r=this.current.fillColor,n=this.current.patternFill,i=0,a=e.length;i<a;i++){var o=e[i],u=o.width,c=o.height,l=this.cachedCanvases.getCanvas("maskCanvas",u,c),h=l.context;h.save(),s(h,o),h.globalCompositeOperation="source-in",h.fillStyle=n?r.getPattern(h,this):r,h.fillRect(0,0,u,c),h.restore(),t.save(),t.transform.apply(t,o.transform),t.scale(1,-1),t.drawImage(l.canvas,0,0,u,c,0,-1,1,1),t.restore()}},paintImageXObject:function(e){if(this.contentVisible){var t=e.startsWith("g_")?this.commonObjs.get(e):this.objs.get(e);t?this.paintInlineImageXObject(t):(0,n.warn)("Dependent image isn't ready yet")}},paintImageXObjectRepeat:function(e,t,r,i){if(this.contentVisible){var a=e.startsWith("g_")?this.commonObjs.get(e):this.objs.get(e);if(a){for(var o=a.width,s=a.height,u=[],c=0,l=i.length;c<l;c+=2)u.push({transform:[t,0,0,r,i[c],i[c+1]],x:0,y:0,w:o,h:s});this.paintInlineImageXObjectGroup(a,u)}else(0,n.warn)("Dependent image isn't ready yet")}},paintInlineImageXObject:function(e){if(this.contentVisible){var t=e.width,r=e.height,n=this.ctx;this.save(),n.scale(1/t,-1/r);var i,a,s=n.mozCurrentTransformInverse,u=s[0],c=s[1],l=Math.max(Math.sqrt(u*u+c*c),1),h=s[2],f=s[3],d=Math.max(Math.sqrt(h*h+f*f),1);if("function"===typeof HTMLElement&&e instanceof HTMLElement||!e.data)i=e;else{a=this.cachedCanvases.getCanvas("inlineImage",t,r);var p=a.context;o(p,e,this.current.transferMaps),i=a.canvas}var v=t,g=r,y="prescale1";while(l>2&&v>1||d>2&&g>1){var m=v,b=g;l>2&&v>1&&(m=Math.ceil(v/2),l/=v/m),d>2&&g>1&&(b=Math.ceil(g/2),d/=g/b),a=this.cachedCanvases.getCanvas(y,m,b),p=a.context,p.clearRect(0,0,m,b),p.drawImage(i,0,0,v,g,0,0,m,b),i=a.canvas,v=m,g=b,y="prescale1"===y?"prescale2":"prescale1"}if(n.drawImage(i,0,0,v,g,0,-r,t,r),this.imageLayer){var _=this.getCanvasPosition(0,-r);this.imageLayer.appendImage({imgData:e,left:_[0],top:_[1],width:t/s[0],height:r/s[3]})}this.restore()}},paintInlineImageXObjectGroup:function(e,t){if(this.contentVisible){var r=this.ctx,n=e.width,i=e.height,a=this.cachedCanvases.getCanvas("inlineImage",n,i),s=a.context;o(s,e,this.current.transferMaps);for(var u=0,c=t.length;u<c;u++){var l=t[u];if(r.save(),r.transform.apply(r,l.transform),r.scale(1,-1),r.drawImage(a.canvas,l.x,l.y,l.w,l.h,0,-1,1,1),this.imageLayer){var h=this.getCanvasPosition(l.x,l.y);this.imageLayer.appendImage({imgData:e,left:h[0],top:h[1],width:n,height:i})}r.restore()}}},paintSolidColorImageMask:function(){this.contentVisible&&this.ctx.fillRect(0,0,1,1)},markPoint:function(e){},markPointProps:function(e,t){},beginMarkedContent:function(e){this.markedContentStack.push({visible:!0})},beginMarkedContentProps:function(e,t){"OC"===e?this.markedContentStack.push({visible:this.optionalContentConfig.isVisible(t)}):this.markedContentStack.push({visible:!0}),this.contentVisible=this.isContentVisible()},endMarkedContent:function(){this.markedContentStack.pop(),this.contentVisible=this.isContentVisible()},beginCompat:function(){},endCompat:function(){},consumePath:function(){var e=this.ctx;this.pendingClip&&(this.pendingClip===R?e.clip("evenodd"):e.clip(),this.pendingClip=null),e.beginPath()},getSinglePixelWidth:function(e){if(null===this._cachedGetSinglePixelWidth){var t=this.ctx.mozCurrentTransformInverse;this._cachedGetSinglePixelWidth=Math.sqrt(Math.max(t[0]*t[0]+t[1]*t[1],t[2]*t[2]+t[3]*t[3]))}return this._cachedGetSinglePixelWidth},getCanvasPosition:function(e,t){var r=this.ctx.mozCurrentTransform;return[r[0]*e+r[2]*t+r[4],r[1]*e+r[3]*t+r[5]]},isContentVisible:function(){for(var e=this.markedContentStack.length-1;e>=0;e--)if(!this.markedContentStack[e].visible)return!1;return!0}},n.OPS)r.prototype[n.OPS[E]]=r.prototype[E];return r}();t.CanvasGraphics=b},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getShadingPatternFromIR=s,t.TilingPattern=void 0;var n=r(5),i={};function a(e,t){if(t&&"undefined"!==typeof Path2D){var r=t[2]-t[0],n=t[3]-t[1],i=new Path2D;i.rect(t[0],t[1],r,n),e.clip(i)}}i.RadialAxial={fromIR:function(e){var t=e[1],r=e[2],n=e[3],i=e[4],o=e[5],s=e[6],u=e[7];return{type:"Pattern",getPattern:function(e){var c;a(e,r),"axial"===t?c=e.createLinearGradient(i[0],i[1],o[0],o[1]):"radial"===t&&(c=e.createRadialGradient(i[0],i[1],s,o[0],o[1],u));for(var l=0,h=n.length;l<h;++l){var f=n[l];c.addColorStop(f[0],f[1])}return c}}}};var o=function(){function e(e,t,r,n,i,a,o,s){var u,c=t.coords,l=t.colors,h=e.data,f=4*e.width;c[r+1]>c[n+1]&&(u=r,r=n,n=u,u=a,a=o,o=u),c[n+1]>c[i+1]&&(u=n,n=i,i=u,u=o,o=s,s=u),c[r+1]>c[n+1]&&(u=r,r=n,n=u,u=a,a=o,o=u);var d=(c[r]+t.offsetX)*t.scaleX,p=(c[r+1]+t.offsetY)*t.scaleY,v=(c[n]+t.offsetX)*t.scaleX,g=(c[n+1]+t.offsetY)*t.scaleY,y=(c[i]+t.offsetX)*t.scaleX,m=(c[i+1]+t.offsetY)*t.scaleY;if(!(p>=m))for(var b,_,w,S,A,k,x,P,C=l[a],T=l[a+1],R=l[a+2],E=l[o],L=l[o+1],O=l[o+2],I=l[s],M=l[s+1],F=l[s+2],N=Math.round(p),D=Math.round(m),j=N;j<=D;j++){if(j<g){var U=void 0;U=j<p?0:p===g?1:(p-j)/(p-g),b=d-(d-v)*U,_=C-(C-E)*U,w=T-(T-L)*U,S=R-(R-O)*U}else{var q=void 0;q=j>m?1:g===m?0:(g-j)/(g-m),b=v-(v-y)*q,_=E-(E-I)*q,w=L-(L-M)*q,S=O-(O-F)*q}var B=void 0;B=j<p?0:j>m?1:(p-j)/(p-m),A=d-(d-y)*B,k=C-(C-I)*B,x=T-(T-M)*B,P=R-(R-F)*B;for(var W=Math.round(Math.min(b,A)),V=Math.round(Math.max(b,A)),z=f*j+4*W,H=W;H<=V;H++)B=(b-H)/(b-A),B<0?B=0:B>1&&(B=1),h[z++]=_-(_-k)*B|0,h[z++]=w-(w-x)*B|0,h[z++]=S-(S-P)*B|0,h[z++]=255}}function t(t,r,n){var i,a,o=r.coords,s=r.colors;switch(r.type){case"lattice":var u=r.verticesPerRow,c=Math.floor(o.length/u)-1,l=u-1;for(i=0;i<c;i++)for(var h=i*u,f=0;f<l;f++,h++)e(t,n,o[h],o[h+1],o[h+u],s[h],s[h+1],s[h+u]),e(t,n,o[h+u+1],o[h+1],o[h+u],s[h+u+1],s[h+1],s[h+u]);break;case"triangles":for(i=0,a=o.length;i<a;i+=3)e(t,n,o[i],o[i+1],o[i+2],s[i],s[i+1],s[i+2]);break;default:throw new Error("illegal figure")}}function r(e,r,n,i,a,o,s,u){var c,l,h,f,d=1.1,p=3e3,v=2,g=Math.floor(e[0]),y=Math.floor(e[1]),m=Math.ceil(e[2])-g,b=Math.ceil(e[3])-y,_=Math.min(Math.ceil(Math.abs(m*r[0]*d)),p),w=Math.min(Math.ceil(Math.abs(b*r[1]*d)),p),S=m/_,A=b/w,k={coords:n,colors:i,offsetX:-g,offsetY:-y,scaleX:1/S,scaleY:1/A},x=_+2*v,P=w+2*v;if(u.isEnabled)c=u.drawFigures({width:_,height:w,backgroundColor:o,figures:a,context:k}),l=s.getCanvas("mesh",x,P,!1),l.context.drawImage(c,v,v),c=l.canvas;else{l=s.getCanvas("mesh",x,P,!1);var C=l.context,T=C.createImageData(_,w);if(o){var R=T.data;for(h=0,f=R.length;h<f;h+=4)R[h]=o[0],R[h+1]=o[1],R[h+2]=o[2],R[h+3]=255}for(h=0;h<a.length;h++)t(T,a[h],k);C.putImageData(T,v,v),c=l.canvas}return{canvas:c,offsetX:g-v*S,offsetY:y-v*A,scaleX:S,scaleY:A}}return r}();function s(e){var t=i[e[0]];if(!t)throw new Error("Unknown IR type: ".concat(e[0]));return t.fromIR(e)}i.Mesh={fromIR:function(e){var t=e[2],r=e[3],i=e[4],s=e[5],u=e[6],c=e[7],l=e[8];return{type:"Pattern",getPattern:function(e,h,f){var d;if(a(e,c),f)d=n.Util.singularValueDecompose2dScale(e.mozCurrentTransform);else if(d=n.Util.singularValueDecompose2dScale(h.baseTransform),u){var p=n.Util.singularValueDecompose2dScale(u);d=[d[0]*p[0],d[1]*p[1]]}var v=o(s,d,t,r,i,f?null:l,h.cachedCanvases,h.webGLContext);return f||(e.setTransform.apply(e,h.baseTransform),u&&e.transform.apply(e,u)),e.translate(v.offsetX,v.offsetY),e.scale(v.scaleX,v.scaleY),e.createPattern(v.canvas,"no-repeat")}}}},i.Dummy={fromIR:function(){return{type:"Pattern",getPattern:function(){return"hotpink"}}}};var u=function(){var e={COLORED:1,UNCOLORED:2},t=3e3;function r(e,t,r,n,i){this.operatorList=e[2],this.matrix=e[3]||[1,0,0,1,0,0],this.bbox=e[4],this.xstep=e[5],this.ystep=e[6],this.paintType=e[7],this.tilingType=e[8],this.color=t,this.canvasGraphicsFactory=n,this.baseTransform=i,this.type="Pattern",this.ctx=r}return r.prototype={createPatternCanvas:function(e){var t=this.operatorList,r=this.bbox,i=this.xstep,a=this.ystep,o=this.paintType,s=this.tilingType,u=this.color,c=this.canvasGraphicsFactory;(0,n.info)("TilingType: "+s);var l=r[0],h=r[1],f=r[2],d=r[3],p=n.Util.singularValueDecompose2dScale(this.matrix),v=n.Util.singularValueDecompose2dScale(this.baseTransform),g=[p[0]*v[0],p[1]*v[1]],y=this.getSizeAndScale(i,this.ctx.canvas.width,g[0]),m=this.getSizeAndScale(a,this.ctx.canvas.height,g[1]),b=e.cachedCanvases.getCanvas("pattern",y.size,m.size,!0),_=b.context,w=c.createCanvasGraphics(_);return w.groupLevel=e.groupLevel,this.setFillAndStrokeStyleToContext(w,o,u),w.transform(y.scale,0,0,m.scale,0,0),w.transform(1,0,0,1,-l,-h),this.clipBbox(w,r,l,h,f,d),w.executeOperatorList(t),this.ctx.transform(1,0,0,1,l,h),this.ctx.scale(1/y.scale,1/m.scale),b.canvas},getSizeAndScale:function(e,r,n){e=Math.abs(e);var i=Math.max(t,r),a=Math.ceil(e*n);return a>=i?a=i:n=a/e,{scale:n,size:a}},clipBbox:function(e,t,r,n,i,a){if(Array.isArray(t)&&4===t.length){var o=i-r,s=a-n;e.ctx.rect(r,n,o,s),e.clip(),e.endPath()}},setFillAndStrokeStyleToContext:function(t,r,i){var a=t.ctx,o=t.current;switch(r){case e.COLORED:var s=this.ctx;a.fillStyle=s.fillStyle,a.strokeStyle=s.strokeStyle,o.fillColor=s.fillStyle,o.strokeColor=s.strokeStyle;break;case e.UNCOLORED:var u=n.Util.makeCssRgb(i[0],i[1],i[2]);a.fillStyle=u,a.strokeStyle=u,o.fillColor=u,o.strokeColor=u;break;default:throw new n.FormatError("Unsupported paint type: ".concat(r))}},getPattern:function(e,t){e=this.ctx,e.setTransform.apply(e,this.baseTransform),e.transform.apply(e,this.matrix);var r=this.createPatternCanvas(t);return e.createPattern(r,"repeat")}},r}();t.TilingPattern=u},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.GlobalWorkerOptions=void 0;var n=Object.create(null);t.GlobalWorkerOptions=n,n.workerPort=void 0===n.workerPort?null:n.workerPort,n.workerSrc=void 0===n.workerSrc?"":n.workerSrc},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MessageHandler=void 0;var n=a(r(2)),i=r(5);function a(e){return e&&e.__esModule?e:{default:e}}function o(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(c){return void r(c)}s.done?t(u):Promise.resolve(u).then(n,i)}function s(e){return function(){var t=this,r=arguments;return new Promise((function(n,i){var a=e.apply(t,r);function s(e){o(a,n,i,s,u,"next",e)}function u(e){o(a,n,i,s,u,"throw",e)}s(void 0)}))}}function u(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function c(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function l(e,t,r){return t&&c(e.prototype,t),r&&c(e,r),e}function h(e){return h="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},h(e)}var f={UNKNOWN:0,DATA:1,ERROR:2},d={UNKNOWN:0,CANCEL:1,CANCEL_COMPLETE:2,CLOSE:3,ENQUEUE:4,ERROR:5,PULL:6,PULL_COMPLETE:7,START_COMPLETE:8};function p(e){if("object"!==h(e)||null===e)return e;switch(e.name){case"AbortException":return new i.AbortException(e.message);case"MissingPDFException":return new i.MissingPDFException(e.message);case"UnexpectedResponseException":return new i.UnexpectedResponseException(e.message,e.status);case"UnknownErrorException":return new i.UnknownErrorException(e.message,e.details);default:return new i.UnknownErrorException(e.message,e.toString())}}var v=function(){function e(t,r,n){var i=this;u(this,e),this.sourceName=t,this.targetName=r,this.comObj=n,this.callbackId=1,this.streamId=1,this.postMessageTransfers=!0,this.streamSinks=Object.create(null),this.streamControllers=Object.create(null),this.callbackCapabilities=Object.create(null),this.actionHandler=Object.create(null),this._onComObjOnMessage=function(e){var t=e.data;if(t.targetName===i.sourceName)if(t.stream)i._processStreamMessage(t);else if(t.callback){var r=t.callbackId,a=i.callbackCapabilities[r];if(!a)throw new Error("Cannot resolve callback ".concat(r));if(delete i.callbackCapabilities[r],t.callback===f.DATA)a.resolve(t.data);else{if(t.callback!==f.ERROR)throw new Error("Unexpected callback case");a.reject(p(t.reason))}}else{var o=i.actionHandler[t.action];if(!o)throw new Error("Unknown action from worker: ".concat(t.action));if(t.callbackId){var s=i.sourceName,u=t.sourceName;new Promise((function(e){e(o(t.data))})).then((function(e){n.postMessage({sourceName:s,targetName:u,callback:f.DATA,callbackId:t.callbackId,data:e})}),(function(e){n.postMessage({sourceName:s,targetName:u,callback:f.ERROR,callbackId:t.callbackId,reason:p(e)})}))}else t.streamId?i._createStreamSink(t):o(t.data)}},n.addEventListener("message",this._onComObjOnMessage)}return l(e,[{key:"on",value:function(e,t){var r=this.actionHandler;if(r[e])throw new Error('There is already an actionName called "'.concat(e,'"'));r[e]=t}},{key:"send",value:function(e,t,r){this._postMessage({sourceName:this.sourceName,targetName:this.targetName,action:e,data:t},r)}},{key:"sendWithPromise",value:function(e,t,r){var n=this.callbackId++,a=(0,i.createPromiseCapability)();this.callbackCapabilities[n]=a;try{this._postMessage({sourceName:this.sourceName,targetName:this.targetName,action:e,callbackId:n,data:t},r)}catch(o){a.reject(o)}return a.promise}},{key:"sendWithStream",value:function(e,t,r,n){var a=this,o=this.streamId++,s=this.sourceName,u=this.targetName,c=this.comObj;return new ReadableStream({start:function(r){var c=(0,i.createPromiseCapability)();return a.streamControllers[o]={controller:r,startCall:c,pullCall:null,cancelCall:null,isClosed:!1},a._postMessage({sourceName:s,targetName:u,action:e,streamId:o,data:t,desiredSize:r.desiredSize},n),c.promise},pull:function(e){var t=(0,i.createPromiseCapability)();return a.streamControllers[o].pullCall=t,c.postMessage({sourceName:s,targetName:u,stream:d.PULL,streamId:o,desiredSize:e.desiredSize}),t.promise},cancel:function(e){(0,i.assert)(e instanceof Error,"cancel must have a valid reason");var t=(0,i.createPromiseCapability)();return a.streamControllers[o].cancelCall=t,a.streamControllers[o].isClosed=!0,c.postMessage({sourceName:s,targetName:u,stream:d.CANCEL,streamId:o,reason:p(e)}),t.promise}},r)}},{key:"_createStreamSink",value:function(e){var t=this,r=this.actionHandler[e.action],n=e.streamId,a=this.sourceName,o=e.sourceName,s=this.comObj,u={enqueue:function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,s=arguments.length>2?arguments[2]:void 0;if(!this.isCancelled){var u=this.desiredSize;this.desiredSize-=r,u>0&&this.desiredSize<=0&&(this.sinkCapability=(0,i.createPromiseCapability)(),this.ready=this.sinkCapability.promise),t._postMessage({sourceName:a,targetName:o,stream:d.ENQUEUE,streamId:n,chunk:e},s)}},close:function(){this.isCancelled||(this.isCancelled=!0,s.postMessage({sourceName:a,targetName:o,stream:d.CLOSE,streamId:n}),delete t.streamSinks[n])},error:function(e){(0,i.assert)(e instanceof Error,"error must have a valid reason"),this.isCancelled||(this.isCancelled=!0,s.postMessage({sourceName:a,targetName:o,stream:d.ERROR,streamId:n,reason:p(e)}))},sinkCapability:(0,i.createPromiseCapability)(),onPull:null,onCancel:null,isCancelled:!1,desiredSize:e.desiredSize,ready:null};u.sinkCapability.resolve(),u.ready=u.sinkCapability.promise,this.streamSinks[n]=u,new Promise((function(t){t(r(e.data,u))})).then((function(){s.postMessage({sourceName:a,targetName:o,stream:d.START_COMPLETE,streamId:n,success:!0})}),(function(e){s.postMessage({sourceName:a,targetName:o,stream:d.START_COMPLETE,streamId:n,reason:p(e)})}))}},{key:"_processStreamMessage",value:function(e){var t=e.streamId,r=this.sourceName,n=e.sourceName,a=this.comObj;switch(e.stream){case d.START_COMPLETE:e.success?this.streamControllers[t].startCall.resolve():this.streamControllers[t].startCall.reject(p(e.reason));break;case d.PULL_COMPLETE:e.success?this.streamControllers[t].pullCall.resolve():this.streamControllers[t].pullCall.reject(p(e.reason));break;case d.PULL:if(!this.streamSinks[t]){a.postMessage({sourceName:r,targetName:n,stream:d.PULL_COMPLETE,streamId:t,success:!0});break}this.streamSinks[t].desiredSize<=0&&e.desiredSize>0&&this.streamSinks[t].sinkCapability.resolve(),this.streamSinks[t].desiredSize=e.desiredSize;var o=this.streamSinks[e.streamId].onPull;new Promise((function(e){e(o&&o())})).then((function(){a.postMessage({sourceName:r,targetName:n,stream:d.PULL_COMPLETE,streamId:t,success:!0})}),(function(e){a.postMessage({sourceName:r,targetName:n,stream:d.PULL_COMPLETE,streamId:t,reason:p(e)})}));break;case d.ENQUEUE:if((0,i.assert)(this.streamControllers[t],"enqueue should have stream controller"),this.streamControllers[t].isClosed)break;this.streamControllers[t].controller.enqueue(e.chunk);break;case d.CLOSE:if((0,i.assert)(this.streamControllers[t],"close should have stream controller"),this.streamControllers[t].isClosed)break;this.streamControllers[t].isClosed=!0,this.streamControllers[t].controller.close(),this._deleteStreamController(t);break;case d.ERROR:(0,i.assert)(this.streamControllers[t],"error should have stream controller"),this.streamControllers[t].controller.error(p(e.reason)),this._deleteStreamController(t);break;case d.CANCEL_COMPLETE:e.success?this.streamControllers[t].cancelCall.resolve():this.streamControllers[t].cancelCall.reject(p(e.reason)),this._deleteStreamController(t);break;case d.CANCEL:if(!this.streamSinks[t])break;var s=this.streamSinks[e.streamId].onCancel;new Promise((function(t){t(s&&s(p(e.reason)))})).then((function(){a.postMessage({sourceName:r,targetName:n,stream:d.CANCEL_COMPLETE,streamId:t,success:!0})}),(function(e){a.postMessage({sourceName:r,targetName:n,stream:d.CANCEL_COMPLETE,streamId:t,reason:p(e)})})),this.streamSinks[t].sinkCapability.reject(p(e.reason)),this.streamSinks[t].isCancelled=!0,delete this.streamSinks[t];break;default:throw new Error("Unexpected stream case")}}},{key:"_deleteStreamController",value:function(){var e=s(n["default"].mark((function e(t){return n["default"].wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Promise.allSettled([this.streamControllers[t].startCall,this.streamControllers[t].pullCall,this.streamControllers[t].cancelCall].map((function(e){return e&&e.promise})));case 2:delete this.streamControllers[t];case 3:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}()},{key:"_postMessage",value:function(e,t){t&&this.postMessageTransfers?this.comObj.postMessage(e,t):this.comObj.postMessage(e)}},{key:"destroy",value:function(){this.comObj.removeEventListener("message",this._onComObjOnMessage)}}]),e}();t.MessageHandler=v},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Metadata=void 0;var n=r(5),i=r(212);function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function s(e,t,r){return t&&o(e.prototype,t),r&&o(e,r),e}var u=function(){function e(t){a(this,e),(0,n.assert)("string"===typeof t,"Metadata: input is not a string"),t=this._repair(t);var r=new i.SimpleXMLParser,o=r.parseFromString(t);this._metadataMap=new Map,o&&this._parse(o)}return s(e,[{key:"_repair",value:function(e){return e.replace(/^[^<]+/,"").replace(/>\\376\\377([^<]+)/g,(function(e,t){for(var r=t.replace(/\\([0-3])([0-7])([0-7])/g,(function(e,t,r,n){return String.fromCharCode(64*t+8*r+1*n)})).replace(/&(amp|apos|gt|lt|quot);/g,(function(e,t){switch(t){case"amp":return"&";case"apos":return"'";case"gt":return">";case"lt":return"<";case"quot":return'"'}throw new Error("_repair: ".concat(t," isn't defined."))})),n="",i=0,a=r.length;i<a;i+=2){var o=256*r.charCodeAt(i)+r.charCodeAt(i+1);n+=o>=32&&o<127&&60!==o&&62!==o&&38!==o?String.fromCharCode(o):"&#x"+(65536+o).toString(16).substring(1)+";"}return">"+n}))}},{key:"_parse",value:function(e){var t=e.documentElement;if("rdf:rdf"!==t.nodeName.toLowerCase()){t=t.firstChild;while(t&&"rdf:rdf"!==t.nodeName.toLowerCase())t=t.nextSibling}var r=t?t.nodeName.toLowerCase():null;if(t&&"rdf:rdf"===r&&t.hasChildNodes())for(var n=t.childNodes,i=0,a=n.length;i<a;i++){var o=n[i];if("rdf:description"===o.nodeName.toLowerCase())for(var s=0,u=o.childNodes.length;s<u;s++)if("#text"!==o.childNodes[s].nodeName.toLowerCase()){var c=o.childNodes[s],l=c.nodeName.toLowerCase();this._metadataMap.set(l,c.textContent.trim())}}}},{key:"get",value:function(e){return this._metadataMap.has(e)?this._metadataMap.get(e):null}},{key:"getAll",value:function(){return Object.fromEntries(this._metadataMap)}},{key:"has",value:function(e){return this._metadataMap.has(e)}}]),e}();t.Metadata=u},function(e,t,r){"use strict";function n(e){return n="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function i(e,t){return c(e)||u(e,t)||o(e,t)||a()}function a(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function o(e,t){if(e){if("string"===typeof e)return s(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?s(e,t):void 0}}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function u(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var r=[],n=!0,i=!1,a=void 0;try{for(var o,s=e[Symbol.iterator]();!(n=(o=s.next()).done);n=!0)if(r.push(o.value),t&&r.length===t)break}catch(u){i=!0,a=u}finally{try{n||null==s["return"]||s["return"]()}finally{if(i)throw a}}return r}}function c(e){if(Array.isArray(e))return e}function l(e,t,r){return l="undefined"!==typeof Reflect&&Reflect.get?Reflect.get:function(e,t,r){var n=h(e,t);if(n){var i=Object.getOwnPropertyDescriptor(n,t);return i.get?i.get.call(r):i.value}},l(e,t,r||e)}function h(e,t){while(!Object.prototype.hasOwnProperty.call(e,t))if(e=m(e),null===e)break;return e}function f(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&d(e,t)}function d(e,t){return d=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},d(e,t)}function p(e){var t=y();return function(){var r,n=m(e);if(t){var i=m(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return v(this,r)}}function v(e,t){return!t||"object"!==n(t)&&"function"!==typeof t?g(e):t}function g(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function y(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function m(e){return m=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},m(e)}function b(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function w(e,t,r){return t&&_(e.prototype,t),r&&_(e,r),e}Object.defineProperty(t,"__esModule",{value:!0}),t.SimpleXMLParser=void 0;var S={NoError:0,EndOfDocument:-1,UnterminatedCdat:-2,UnterminatedXmlDeclaration:-3,UnterminatedDoctypeDeclaration:-4,UnterminatedComment:-5,MalformedElement:-6,OutOfMemory:-7,UnterminatedAttributeValue:-8,UnterminatedElement:-9,ElementNeverBegun:-10};function A(e,t){var r=e[t];return" "===r||"\n"===r||"\r"===r||"\t"===r}function k(e){for(var t=0,r=e.length;t<r;t++)if(!A(e,t))return!1;return!0}var x=function(){function e(){b(this,e)}return w(e,[{key:"_resolveEntities",value:function(e){var t=this;return e.replace(/&([^;]+);/g,(function(e,r){if("#x"===r.substring(0,2))return String.fromCharCode(parseInt(r.substring(2),16));if("#"===r.substring(0,1))return String.fromCharCode(parseInt(r.substring(1),10));switch(r){case"lt":return"<";case"gt":return">";case"amp":return"&";case"quot":return'"'}return t.onResolveEntity(r)}))}},{key:"_parseContent",value:function(e,t){var r=[],n=t;function i(){while(n<e.length&&A(e,n))++n}while(n<e.length&&!A(e,n)&&">"!==e[n]&&"/"!==e[n])++n;var a=e.substring(t,n);i();while(n<e.length&&">"!==e[n]&&"/"!==e[n]&&"?"!==e[n]){i();var o="",s="";while(n<e.length&&!A(e,n)&&"="!==e[n])o+=e[n],++n;if(i(),"="!==e[n])return null;++n,i();var u=e[n];if('"'!==u&&"'"!==u)return null;var c=e.indexOf(u,++n);if(c<0)return null;s=e.substring(n,c),r.push({name:o,value:this._resolveEntities(s)}),n=c+1,i()}return{name:a,attributes:r,parsed:n-t}}},{key:"_parseProcessingInstruction",value:function(e,t){var r=t;function n(){while(r<e.length&&A(e,r))++r}while(r<e.length&&!A(e,r)&&">"!==e[r]&&"/"!==e[r])++r;var i=e.substring(t,r);n();var a=r;while(r<e.length&&("?"!==e[r]||">"!==e[r+1]))++r;var o=e.substring(a,r);return{name:i,value:o,parsed:r-t}}},{key:"parseXml",value:function(e){var t=0;while(t<e.length){var r=e[t],n=t;if("<"===r){++n;var i=e[n],a=void 0;switch(i){case"/":if(++n,a=e.indexOf(">",n),a<0)return void this.onError(S.UnterminatedElement);this.onEndElement(e.substring(n,a)),n=a+1;break;case"?":++n;var o=this._parseProcessingInstruction(e,n);if("?>"!==e.substring(n+o.parsed,n+o.parsed+2))return void this.onError(S.UnterminatedXmlDeclaration);this.onPi(o.name,o.value),n+=o.parsed+2;break;case"!":if("--"===e.substring(n+1,n+3)){if(a=e.indexOf("--\x3e",n+3),a<0)return void this.onError(S.UnterminatedComment);this.onComment(e.substring(n+3,a)),n=a+3}else if("[CDATA["===e.substring(n+1,n+8)){if(a=e.indexOf("]]>",n+8),a<0)return void this.onError(S.UnterminatedCdat);this.onCdata(e.substring(n+8,a)),n=a+3}else{if("DOCTYPE"!==e.substring(n+1,n+8))return void this.onError(S.MalformedElement);var s=e.indexOf("[",n+8),u=!1;if(a=e.indexOf(">",n+8),a<0)return void this.onError(S.UnterminatedDoctypeDeclaration);if(s>0&&a>s){if(a=e.indexOf("]>",n+8),a<0)return void this.onError(S.UnterminatedDoctypeDeclaration);u=!0}var c=e.substring(n+8,a+(u?1:0));this.onDoctype(c),n=a+(u?2:1)}break;default:var l=this._parseContent(e,n);if(null===l)return void this.onError(S.MalformedElement);var h=!1;if("/>"===e.substring(n+l.parsed,n+l.parsed+2))h=!0;else if(">"!==e.substring(n+l.parsed,n+l.parsed+1))return void this.onError(S.UnterminatedElement);this.onBeginElement(l.name,l.attributes,h),n+=l.parsed+(h?2:1);break}}else{while(n<e.length&&"<"!==e[n])n++;var f=e.substring(t,n);this.onText(this._resolveEntities(f))}t=n}}},{key:"onResolveEntity",value:function(e){return"&".concat(e,";")}},{key:"onPi",value:function(e,t){}},{key:"onComment",value:function(e){}},{key:"onCdata",value:function(e){}},{key:"onDoctype",value:function(e){}},{key:"onText",value:function(e){}},{key:"onBeginElement",value:function(e,t,r){}},{key:"onEndElement",value:function(e){}},{key:"onError",value:function(e){}}]),e}(),P=function(){function e(t,r){b(this,e),this.nodeName=t,this.nodeValue=r,Object.defineProperty(this,"parentNode",{value:null,writable:!0})}return w(e,[{key:"hasChildNodes",value:function(){return this.childNodes&&this.childNodes.length>0}},{key:"firstChild",get:function(){return this.childNodes&&this.childNodes[0]}},{key:"nextSibling",get:function(){var e=this.parentNode.childNodes;if(e){var t=e.indexOf(this);if(-1!==t)return e[t+1]}}},{key:"textContent",get:function(){return this.childNodes?this.childNodes.map((function(e){return e.textContent})).join(""):this.nodeValue||""}}]),e}(),C=function(e){f(r,e);var t=p(r);function r(){var e;return b(this,r),e=t.call(this),e._currentFragment=null,e._stack=null,e._errorCode=S.NoError,e}return w(r,[{key:"parseFromString",value:function(e){if(this._currentFragment=[],this._stack=[],this._errorCode=S.NoError,this.parseXml(e),this._errorCode===S.NoError){var t=i(this._currentFragment,1),r=t[0];if(r)return{documentElement:r}}}},{key:"onResolveEntity",value:function(e){switch(e){case"apos":return"'"}return l(m(r.prototype),"onResolveEntity",this).call(this,e)}},{key:"onText",value:function(e){if(!k(e)){var t=new P("#text",e);this._currentFragment.push(t)}}},{key:"onCdata",value:function(e){var t=new P("#text",e);this._currentFragment.push(t)}},{key:"onBeginElement",value:function(e,t,r){var n=new P(e);n.childNodes=[],this._currentFragment.push(n),r||(this._stack.push(this._currentFragment),this._currentFragment=n.childNodes)}},{key:"onEndElement",value:function(e){this._currentFragment=this._stack.pop()||[];var t=this._currentFragment[this._currentFragment.length-1];if(t)for(var r=0,n=t.childNodes.length;r<n;r++)t.childNodes[r].parentNode=t}},{key:"onError",value:function(e){this._errorCode=e}}]),r}(x);t.SimpleXMLParser=C},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.OptionalContentConfig=void 0;var n=r(5);function i(e,t){var r;if("undefined"===typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(r=a(e))||t&&e&&"number"===typeof e.length){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,u=!1;return{s:function(){r=e[Symbol.iterator]()},n:function(){var e=r.next();return s=e.done,e},e:function(e){u=!0,o=e},f:function(){try{s||null==r["return"]||r["return"]()}finally{if(u)throw o}}}}function a(e,t){if(e){if("string"===typeof e)return o(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?o(e,t):void 0}}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function s(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function u(e,t,r){return t&&s(e.prototype,t),r&&s(e,r),e}function c(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var l=function e(t,r){c(this,e),this.visible=!0,this.name=t,this.intent=r},h=function(){function e(t){if(c(this,e),this.name=null,this.creator=null,this._order=null,this._groups=new Map,null!==t){this.name=t.name,this.creator=t.creator,this._order=t.order;var r,n=i(t.groups);try{for(n.s();!(r=n.n()).done;){var a=r.value;this._groups.set(a.id,new l(a.name,a.intent))}}catch(y){n.e(y)}finally{n.f()}if("OFF"===t.baseState){var o,s=i(this._groups);try{for(s.s();!(o=s.n()).done;){var u=o.value;u.visible=!1}}catch(y){s.e(y)}finally{s.f()}}var h,f=i(t.on);try{for(f.s();!(h=f.n()).done;){var d=h.value;this._groups.get(d).visible=!0}}catch(y){f.e(y)}finally{f.f()}var p,v=i(t.off);try{for(v.s();!(p=v.n()).done;){var g=p.value;this._groups.get(g).visible=!1}}catch(y){v.e(y)}finally{v.f()}}}return u(e,[{key:"isVisible",value:function(e){if("OCG"===e.type)return this._groups.has(e.id)?this._groups.get(e.id).visible:((0,n.warn)("Optional content group not found: ".concat(e.id)),!0);if("OCMD"===e.type){if(e.expression&&(0,n.warn)("Visibility expression not supported yet."),!e.policy||"AnyOn"===e.policy){var t,r=i(e.ids);try{for(r.s();!(t=r.n()).done;){var a=t.value;if(!this._groups.has(a))return(0,n.warn)("Optional content group not found: ".concat(a)),!0;if(this._groups.get(a).visible)return!0}}catch(v){r.e(v)}finally{r.f()}return!1}if("AllOn"===e.policy){var o,s=i(e.ids);try{for(s.s();!(o=s.n()).done;){var u=o.value;if(!this._groups.has(u))return(0,n.warn)("Optional content group not found: ".concat(u)),!0;if(!this._groups.get(u).visible)return!1}}catch(v){s.e(v)}finally{s.f()}return!0}if("AnyOff"===e.policy){var c,l=i(e.ids);try{for(l.s();!(c=l.n()).done;){var h=c.value;if(!this._groups.has(h))return(0,n.warn)("Optional content group not found: ".concat(h)),!0;if(!this._groups.get(h).visible)return!0}}catch(v){l.e(v)}finally{l.f()}return!1}if("AllOff"===e.policy){var f,d=i(e.ids);try{for(d.s();!(f=d.n()).done;){var p=f.value;if(!this._groups.has(p))return(0,n.warn)("Optional content group not found: ".concat(p)),!0;if(this._groups.get(p).visible)return!1}}catch(v){d.e(v)}finally{d.f()}return!0}return(0,n.warn)("Unknown optional content policy ".concat(e.policy,".")),!0}return(0,n.warn)("Unknown group type ".concat(e.type,".")),!0}},{key:"setVisibility",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this._groups.has(e)?this._groups.get(e).visible=!!t:(0,n.warn)("Optional content group not found: ".concat(e))}},{key:"getOrder",value:function(){return this._groups.size?this._order?this._order.slice():Array.from(this._groups.keys()):null}},{key:"getGroups",value:function(){return this._groups.size?Object.fromEntries(this._groups):null}},{key:"getGroup",value:function(e){return this._groups.get(e)||null}}]),e}();t.OptionalContentConfig=h},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PDFDataTransportStream=void 0;var n=a(r(2)),i=r(5);function a(e){return e&&e.__esModule?e:{default:e}}function o(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(c){return void r(c)}s.done?t(u):Promise.resolve(u).then(n,i)}function s(e){return function(){var t=this,r=arguments;return new Promise((function(n,i){var a=e.apply(t,r);function s(e){o(a,n,i,s,u,"next",e)}function u(e){o(a,n,i,s,u,"throw",e)}s(void 0)}))}}function u(e,t){var r;if("undefined"===typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(r=c(e))||t&&e&&"number"===typeof e.length){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){r=e[Symbol.iterator]()},n:function(){var e=r.next();return o=e.done,e},e:function(e){s=!0,a=e},f:function(){try{o||null==r["return"]||r["return"]()}finally{if(s)throw a}}}}function c(e,t){if(e){if("string"===typeof e)return l(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?l(e,t):void 0}}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function h(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function f(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function d(e,t,r){return t&&f(e.prototype,t),r&&f(e,r),e}var p=function(){function e(t,r){var n=this;h(this,e),(0,i.assert)(r,'PDFDataTransportStream - missing required "pdfDataRangeTransport" argument.'),this._queuedChunks=[],this._progressiveDone=t.progressiveDone||!1;var a=t.initialData;if(a&&a.length>0){var o=new Uint8Array(a).buffer;this._queuedChunks.push(o)}this._pdfDataRangeTransport=r,this._isStreamingSupported=!t.disableStream,this._isRangeSupported=!t.disableRange,this._contentLength=t.length,this._fullRequestReader=null,this._rangeReaders=[],this._pdfDataRangeTransport.addRangeListener((function(e,t){n._onReceiveData({begin:e,chunk:t})})),this._pdfDataRangeTransport.addProgressListener((function(e,t){n._onProgress({loaded:e,total:t})})),this._pdfDataRangeTransport.addProgressiveReadListener((function(e){n._onReceiveData({chunk:e})})),this._pdfDataRangeTransport.addProgressiveDoneListener((function(){n._onProgressiveDone()})),this._pdfDataRangeTransport.transportReady()}return d(e,[{key:"_onReceiveData",value:function(e){var t=new Uint8Array(e.chunk).buffer;if(void 0===e.begin)this._fullRequestReader?this._fullRequestReader._enqueue(t):this._queuedChunks.push(t);else{var r=this._rangeReaders.some((function(r){return r._begin===e.begin&&(r._enqueue(t),!0)}));(0,i.assert)(r,"_onReceiveData - no `PDFDataTransportStreamRangeReader` instance found.")}}},{key:"_onProgress",value:function(e){if(void 0===e.total){var t=this._rangeReaders[0];t&&t.onProgress&&t.onProgress({loaded:e.loaded})}else{var r=this._fullRequestReader;r&&r.onProgress&&r.onProgress({loaded:e.loaded,total:e.total})}}},{key:"_onProgressiveDone",value:function(){this._fullRequestReader&&this._fullRequestReader.progressiveDone(),this._progressiveDone=!0}},{key:"_removeRangeReader",value:function(e){var t=this._rangeReaders.indexOf(e);t>=0&&this._rangeReaders.splice(t,1)}},{key:"getFullReader",value:function(){(0,i.assert)(!this._fullRequestReader,"PDFDataTransportStream.getFullReader can only be called once.");var e=this._queuedChunks;return this._queuedChunks=null,new v(this,e,this._progressiveDone)}},{key:"getRangeReader",value:function(e,t){if(t<=this._progressiveDataLength)return null;var r=new g(this,e,t);return this._pdfDataRangeTransport.requestDataRange(e,t),this._rangeReaders.push(r),r}},{key:"cancelAllRequests",value:function(e){this._fullRequestReader&&this._fullRequestReader.cancel(e);var t=this._rangeReaders.slice(0);t.forEach((function(t){t.cancel(e)})),this._pdfDataRangeTransport.abort()}},{key:"_progressiveDataLength",get:function(){return this._fullRequestReader?this._fullRequestReader._loaded:0}}]),e}();t.PDFDataTransportStream=p;var v=function(){function e(t,r){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];h(this,e),this._stream=t,this._done=n||!1,this._filename=null,this._queuedChunks=r||[],this._loaded=0;var i,a=u(this._queuedChunks);try{for(a.s();!(i=a.n()).done;){var o=i.value;this._loaded+=o.byteLength}}catch(s){a.e(s)}finally{a.f()}this._requests=[],this._headersReady=Promise.resolve(),t._fullRequestReader=this,this.onProgress=null}return d(e,[{key:"_enqueue",value:function(e){if(!this._done){if(this._requests.length>0){var t=this._requests.shift();t.resolve({value:e,done:!1})}else this._queuedChunks.push(e);this._loaded+=e.byteLength}}},{key:"read",value:function(){var e=s(n["default"].mark((function e(){var t,r;return n["default"].wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!(this._queuedChunks.length>0)){e.next=3;break}return t=this._queuedChunks.shift(),e.abrupt("return",{value:t,done:!1});case 3:if(!this._done){e.next=5;break}return e.abrupt("return",{value:void 0,done:!0});case 5:return r=(0,i.createPromiseCapability)(),this._requests.push(r),e.abrupt("return",r.promise);case 8:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"cancel",value:function(e){this._done=!0,this._requests.forEach((function(e){e.resolve({value:void 0,done:!0})})),this._requests=[]}},{key:"progressiveDone",value:function(){this._done||(this._done=!0)}},{key:"headersReady",get:function(){return this._headersReady}},{key:"filename",get:function(){return this._filename}},{key:"isRangeSupported",get:function(){return this._stream._isRangeSupported}},{key:"isStreamingSupported",get:function(){return this._stream._isStreamingSupported}},{key:"contentLength",get:function(){return this._stream._contentLength}}]),e}(),g=function(){function e(t,r,n){h(this,e),this._stream=t,this._begin=r,this._end=n,this._queuedChunk=null,this._requests=[],this._done=!1,this.onProgress=null}return d(e,[{key:"_enqueue",value:function(e){if(!this._done){if(0===this._requests.length)this._queuedChunk=e;else{var t=this._requests.shift();t.resolve({value:e,done:!1}),this._requests.forEach((function(e){e.resolve({value:void 0,done:!0})})),this._requests=[]}this._done=!0,this._stream._removeRangeReader(this)}}},{key:"read",value:function(){var e=s(n["default"].mark((function e(){var t,r;return n["default"].wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!this._queuedChunk){e.next=4;break}return t=this._queuedChunk,this._queuedChunk=null,e.abrupt("return",{value:t,done:!1});case 4:if(!this._done){e.next=6;break}return e.abrupt("return",{value:void 0,done:!0});case 6:return r=(0,i.createPromiseCapability)(),this._requests.push(r),e.abrupt("return",r.promise);case 9:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"cancel",value:function(e){this._done=!0,this._requests.forEach((function(e){e.resolve({value:void 0,done:!0})})),this._requests=[],this._stream._removeRangeReader(this)}},{key:"isStreamingSupported",get:function(){return!1}}]),e}()},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.WebGLContext=void 0;var n=r(5);function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function o(e,t,r){return t&&a(e.prototype,t),r&&a(e,r),e}var s=function(){function e(t){var r=t.enable,n=void 0!==r&&r;i(this,e),this._enabled=!0===n}return o(e,[{key:"composeSMask",value:function(e){var t=e.layer,r=e.mask,n=e.properties;return u.composeSMask(t,r,n)}},{key:"drawFigures",value:function(e){var t=e.width,r=e.height,n=e.backgroundColor,i=e.figures,a=e.context;return u.drawFigures(t,r,n,i,a)}},{key:"clear",value:function(){u.cleanup()}},{key:"isEnabled",get:function(){var e=this._enabled;return e&&(e=u.tryInitGL()),(0,n.shadow)(this,"isEnabled",e)}}]),e}();t.WebGLContext=s;var u=function(){function e(e,t,r){var n=e.createShader(r);e.shaderSource(n,t),e.compileShader(n);var i=e.getShaderParameter(n,e.COMPILE_STATUS);if(!i){var a=e.getShaderInfoLog(n);throw new Error("Error during shader compilation: "+a)}return n}function t(t,r){return e(t,r,t.VERTEX_SHADER)}function r(t,r){return e(t,r,t.FRAGMENT_SHADER)}function n(e,t){for(var r=e.createProgram(),n=0,i=t.length;n<i;++n)e.attachShader(r,t[n]);e.linkProgram(r);var a=e.getProgramParameter(r,e.LINK_STATUS);if(!a){var o=e.getProgramInfoLog(r);throw new Error("Error during program linking: "+o)}return r}function i(e,t,r){e.activeTexture(r);var n=e.createTexture();return e.bindTexture(e.TEXTURE_2D,n),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,e.NEAREST),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,e.NEAREST),e.texImage2D(e.TEXTURE_2D,0,e.RGBA,e.RGBA,e.UNSIGNED_BYTE,t),n}var a,o;function s(){a||(o=document.createElement("canvas"),a=o.getContext("webgl",{premultipliedalpha:!1}))}var u="  attribute vec2 a_position;                                      attribute vec2 a_texCoord;                                                                                                      uniform vec2 u_resolution;                                                                                                      varying vec2 v_texCoord;                                                                                                        void main() {                                                     vec2 clipSpace = (a_position / u_resolution) * 2.0 - 1.0;       gl_Position = vec4(clipSpace * vec2(1, -1), 0, 1);                                                                              v_texCoord = a_texCoord;                                      }                                                             ",c="  precision mediump float;                                                                                                        uniform vec4 u_backdrop;                                        uniform int u_subtype;                                          uniform sampler2D u_image;                                      uniform sampler2D u_mask;                                                                                                       varying vec2 v_texCoord;                                                                                                        void main() {                                                     vec4 imageColor = texture2D(u_image, v_texCoord);               vec4 maskColor = texture2D(u_mask, v_texCoord);                 if (u_backdrop.a > 0.0) {                                         maskColor.rgb = maskColor.rgb * maskColor.a +                                   u_backdrop.rgb * (1.0 - maskColor.a);         }                                                               float lum;                                                      if (u_subtype == 0) {                                             lum = maskColor.a;                                            } else {                                                          lum = maskColor.r * 0.3 + maskColor.g * 0.59 +                        maskColor.b * 0.11;                                     }                                                               imageColor.a *= lum;                                            imageColor.rgb *= imageColor.a;                                 gl_FragColor = imageColor;                                    }                                                             ",l=null;function h(){var e,i;s(),e=o,o=null,i=a,a=null;var h=t(i,u),f=r(i,c),d=n(i,[h,f]);i.useProgram(d);var p={};p.gl=i,p.canvas=e,p.resolutionLocation=i.getUniformLocation(d,"u_resolution"),p.positionLocation=i.getAttribLocation(d,"a_position"),p.backdropLocation=i.getUniformLocation(d,"u_backdrop"),p.subtypeLocation=i.getUniformLocation(d,"u_subtype");var v=i.getAttribLocation(d,"a_texCoord"),g=i.getUniformLocation(d,"u_image"),y=i.getUniformLocation(d,"u_mask"),m=i.createBuffer();i.bindBuffer(i.ARRAY_BUFFER,m),i.bufferData(i.ARRAY_BUFFER,new Float32Array([0,0,1,0,0,1,0,1,1,0,1,1]),i.STATIC_DRAW),i.enableVertexAttribArray(v),i.vertexAttribPointer(v,2,i.FLOAT,!1,0,0),i.uniform1i(g,0),i.uniform1i(y,1),l=p}function f(e,t,r){var n=e.width,a=e.height;l||h();var o=l,s=o.canvas,u=o.gl;s.width=n,s.height=a,u.viewport(0,0,u.drawingBufferWidth,u.drawingBufferHeight),u.uniform2f(o.resolutionLocation,n,a),r.backdrop?u.uniform4f(o.resolutionLocation,r.backdrop[0],r.backdrop[1],r.backdrop[2],1):u.uniform4f(o.resolutionLocation,0,0,0,0),u.uniform1i(o.subtypeLocation,"Luminosity"===r.subtype?1:0);var c=i(u,e,u.TEXTURE0),f=i(u,t,u.TEXTURE1),d=u.createBuffer();return u.bindBuffer(u.ARRAY_BUFFER,d),u.bufferData(u.ARRAY_BUFFER,new Float32Array([0,0,n,0,0,a,0,a,n,0,n,a]),u.STATIC_DRAW),u.enableVertexAttribArray(o.positionLocation),u.vertexAttribPointer(o.positionLocation,2,u.FLOAT,!1,0,0),u.clearColor(0,0,0,0),u.enable(u.BLEND),u.blendFunc(u.ONE,u.ONE_MINUS_SRC_ALPHA),u.clear(u.COLOR_BUFFER_BIT),u.drawArrays(u.TRIANGLES,0,6),u.flush(),u.deleteTexture(c),u.deleteTexture(f),u.deleteBuffer(d),s}var d="  attribute vec2 a_position;                                      attribute vec3 a_color;                                                                                                         uniform vec2 u_resolution;                                      uniform vec2 u_scale;                                           uniform vec2 u_offset;                                                                                                          varying vec4 v_color;                                                                                                           void main() {                                                     vec2 position = (a_position + u_offset) * u_scale;              vec2 clipSpace = (position / u_resolution) * 2.0 - 1.0;         gl_Position = vec4(clipSpace * vec2(1, -1), 0, 1);                                                                              v_color = vec4(a_color / 255.0, 1.0);                         }                                                             ",p="  precision mediump float;                                                                                                        varying vec4 v_color;                                                                                                           void main() {                                                     gl_FragColor = v_color;                                       }                                                             ",v=null;function g(){var e,i;s(),e=o,o=null,i=a,a=null;var u=t(i,d),c=r(i,p),l=n(i,[u,c]);i.useProgram(l);var h={};h.gl=i,h.canvas=e,h.resolutionLocation=i.getUniformLocation(l,"u_resolution"),h.scaleLocation=i.getUniformLocation(l,"u_scale"),h.offsetLocation=i.getUniformLocation(l,"u_offset"),h.positionLocation=i.getAttribLocation(l,"a_position"),h.colorLocation=i.getAttribLocation(l,"a_color"),v=h}function y(e,t,r,n,i){v||g();var a=v,o=a.canvas,s=a.gl;o.width=e,o.height=t,s.viewport(0,0,s.drawingBufferWidth,s.drawingBufferHeight),s.uniform2f(a.resolutionLocation,e,t);var u,c,l,h=0;for(u=0,c=n.length;u<c;u++)switch(n[u].type){case"lattice":l=n[u].coords.length/n[u].verticesPerRow|0,h+=(l-1)*(n[u].verticesPerRow-1)*6;break;case"triangles":h+=n[u].coords.length;break}var f=new Float32Array(2*h),d=new Uint8Array(3*h),p=i.coords,y=i.colors,m=0,b=0;for(u=0,c=n.length;u<c;u++){var _=n[u],w=_.coords,S=_.colors;switch(_.type){case"lattice":var A=_.verticesPerRow;l=w.length/A|0;for(var k=1;k<l;k++)for(var x=k*A+1,P=1;P<A;P++,x++)f[m]=p[w[x-A-1]],f[m+1]=p[w[x-A-1]+1],f[m+2]=p[w[x-A]],f[m+3]=p[w[x-A]+1],f[m+4]=p[w[x-1]],f[m+5]=p[w[x-1]+1],d[b]=y[S[x-A-1]],d[b+1]=y[S[x-A-1]+1],d[b+2]=y[S[x-A-1]+2],d[b+3]=y[S[x-A]],d[b+4]=y[S[x-A]+1],d[b+5]=y[S[x-A]+2],d[b+6]=y[S[x-1]],d[b+7]=y[S[x-1]+1],d[b+8]=y[S[x-1]+2],f[m+6]=f[m+2],f[m+7]=f[m+3],f[m+8]=f[m+4],f[m+9]=f[m+5],f[m+10]=p[w[x]],f[m+11]=p[w[x]+1],d[b+9]=d[b+3],d[b+10]=d[b+4],d[b+11]=d[b+5],d[b+12]=d[b+6],d[b+13]=d[b+7],d[b+14]=d[b+8],d[b+15]=y[S[x]],d[b+16]=y[S[x]+1],d[b+17]=y[S[x]+2],m+=12,b+=18;break;case"triangles":for(var C=0,T=w.length;C<T;C++)f[m]=p[w[C]],f[m+1]=p[w[C]+1],d[b]=y[S[C]],d[b+1]=y[S[C]+1],d[b+2]=y[S[C]+2],m+=2,b+=3;break}}r?s.clearColor(r[0]/255,r[1]/255,r[2]/255,1):s.clearColor(0,0,0,0),s.clear(s.COLOR_BUFFER_BIT);var R=s.createBuffer();s.bindBuffer(s.ARRAY_BUFFER,R),s.bufferData(s.ARRAY_BUFFER,f,s.STATIC_DRAW),s.enableVertexAttribArray(a.positionLocation),s.vertexAttribPointer(a.positionLocation,2,s.FLOAT,!1,0,0);var E=s.createBuffer();return s.bindBuffer(s.ARRAY_BUFFER,E),s.bufferData(s.ARRAY_BUFFER,d,s.STATIC_DRAW),s.enableVertexAttribArray(a.colorLocation),s.vertexAttribPointer(a.colorLocation,3,s.UNSIGNED_BYTE,!1,0,0),s.uniform2f(a.scaleLocation,i.scaleX,i.scaleY),s.uniform2f(a.offsetLocation,i.offsetX,i.offsetY),s.drawArrays(s.TRIANGLES,0,h),s.flush(),s.deleteBuffer(R),s.deleteBuffer(E),o}return{tryInitGL:function(){try{return s(),!!a}catch(e){}return!1},composeSMask:f,drawFigures:y,cleanup:function(){l&&l.canvas&&(l.canvas.width=0,l.canvas.height=0),v&&v.canvas&&(v.canvas.width=0,v.canvas.height=0),l=null,v=null}}}()},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AnnotationLayer=void 0;var n=r(1),i=r(5),a=r(205);function o(e,t,r){return o="undefined"!==typeof Reflect&&Reflect.get?Reflect.get:function(e,t,r){var n=s(e,t);if(n){var i=Object.getOwnPropertyDescriptor(n,t);return i.get?i.get.call(r):i.value}},o(e,t,r||e)}function s(e,t){while(!Object.prototype.hasOwnProperty.call(e,t))if(e=m(e),null===e)break;return e}function u(e,t){var r;if("undefined"===typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(r=c(e))||t&&e&&"number"===typeof e.length){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){r=e[Symbol.iterator]()},n:function(){var e=r.next();return o=e.done,e},e:function(e){s=!0,a=e},f:function(){try{o||null==r["return"]||r["return"]()}finally{if(s)throw a}}}}function c(e,t){if(e){if("string"===typeof e)return l(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?l(e,t):void 0}}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function h(e){return h="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},h(e)}function f(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&d(e,t)}function d(e,t){return d=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},d(e,t)}function p(e){var t=y();return function(){var r,n=m(e);if(t){var i=m(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return v(this,r)}}function v(e,t){return!t||"object"!==h(t)&&"function"!==typeof t?g(e):t}function g(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function y(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function m(e){return m=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},m(e)}function b(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function w(e,t,r){return t&&_(e.prototype,t),r&&_(e,r),e}var S=function(){function e(){b(this,e)}return w(e,null,[{key:"create",value:function(e){var t=e.data.annotationType;switch(t){case i.AnnotationType.LINK:return new k(e);case i.AnnotationType.TEXT:return new x(e);case i.AnnotationType.WIDGET:var r=e.data.fieldType;switch(r){case"Tx":return new C(e);case"Btn":return e.data.radioButton?new R(e):e.data.checkBox?new T(e):new E(e);case"Ch":return new L(e)}return new P(e);case i.AnnotationType.POPUP:return new O(e);case i.AnnotationType.FREETEXT:return new M(e);case i.AnnotationType.LINE:return new F(e);case i.AnnotationType.SQUARE:return new N(e);case i.AnnotationType.CIRCLE:return new D(e);case i.AnnotationType.POLYLINE:return new j(e);case i.AnnotationType.CARET:return new q(e);case i.AnnotationType.INK:return new B(e);case i.AnnotationType.POLYGON:return new U(e);case i.AnnotationType.HIGHLIGHT:return new W(e);case i.AnnotationType.UNDERLINE:return new V(e);case i.AnnotationType.SQUIGGLY:return new z(e);case i.AnnotationType.STRIKEOUT:return new H(e);case i.AnnotationType.STAMP:return new G(e);case i.AnnotationType.FILEATTACHMENT:return new Y(e);default:return new A(e)}}}]),e}(),A=function(){function e(t){var r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];b(this,e),this.isRenderable=r,this.data=t.data,this.layer=t.layer,this.page=t.page,this.viewport=t.viewport,this.linkService=t.linkService,this.downloadManager=t.downloadManager,this.imageResourcesPath=t.imageResourcesPath,this.renderInteractiveForms=t.renderInteractiveForms,this.svgFactory=t.svgFactory,this.annotationStorage=t.annotationStorage,r&&(this.container=this._createContainer(n))}return w(e,[{key:"_createContainer",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this.data,r=this.page,n=this.viewport,a=document.createElement("section"),o=t.rect[2]-t.rect[0],s=t.rect[3]-t.rect[1];a.setAttribute("data-annotation-id",t.id);var u=i.Util.normalizeRect([t.rect[0],r.view[3]-t.rect[1]+r.view[1],t.rect[2],r.view[3]-t.rect[3]+r.view[1]]);if(a.style.transform="matrix(".concat(n.transform.join(","),")"),a.style.transformOrigin="-".concat(u[0],"px -").concat(u[1],"px"),!e&&t.borderStyle.width>0){a.style.borderWidth="".concat(t.borderStyle.width,"px"),t.borderStyle.style!==i.AnnotationBorderStyleType.UNDERLINE&&(o-=2*t.borderStyle.width,s-=2*t.borderStyle.width);var c=t.borderStyle.horizontalCornerRadius,l=t.borderStyle.verticalCornerRadius;if(c>0||l>0){var h="".concat(c,"px / ").concat(l,"px");a.style.borderRadius=h}switch(t.borderStyle.style){case i.AnnotationBorderStyleType.SOLID:a.style.borderStyle="solid";break;case i.AnnotationBorderStyleType.DASHED:a.style.borderStyle="dashed";break;case i.AnnotationBorderStyleType.BEVELED:(0,i.warn)("Unimplemented border style: beveled");break;case i.AnnotationBorderStyleType.INSET:(0,i.warn)("Unimplemented border style: inset");break;case i.AnnotationBorderStyleType.UNDERLINE:a.style.borderBottomStyle="solid";break;default:break}t.color?a.style.borderColor=i.Util.makeCssRgb(0|t.color[0],0|t.color[1],0|t.color[2]):a.style.borderWidth=0}return a.style.left="".concat(u[0],"px"),a.style.top="".concat(u[1],"px"),a.style.width="".concat(o,"px"),a.style.height="".concat(s,"px"),a}},{key:"_createPopup",value:function(e,t,r){t||(t=document.createElement("div"),t.style.height=e.style.height,t.style.width=e.style.width,e.appendChild(t));var n=new I({container:e,trigger:t,color:r.color,title:r.title,modificationDate:r.modificationDate,contents:r.contents,hideWrapper:!0}),i=n.render();i.style.left=e.style.width,e.appendChild(i)}},{key:"render",value:function(){(0,i.unreachable)("Abstract method `AnnotationElement.render` called")}}]),e}(),k=function(e){f(r,e);var t=p(r);function r(e){b(this,r);var n=!!(e.data.url||e.data.dest||e.data.action);return t.call(this,e,n)}return w(r,[{key:"render",value:function(){this.container.className="linkAnnotation";var e=this.data,t=this.linkService,r=document.createElement("a");return e.url?(0,n.addLinkAttributes)(r,{url:e.url,target:e.newWindow?n.LinkTarget.BLANK:t.externalLinkTarget,rel:t.externalLinkRel,enabled:t.externalLinkEnabled}):e.action?this._bindNamedAction(r,e.action):this._bindLink(r,e.dest),this.container.appendChild(r),this.container}},{key:"_bindLink",value:function(e,t){var r=this;e.href=this.linkService.getDestinationHash(t),e.onclick=function(){return t&&r.linkService.navigateTo(t),!1},t&&(e.className="internalLink")}},{key:"_bindNamedAction",value:function(e,t){var r=this;e.href=this.linkService.getAnchorUrl(""),e.onclick=function(){return r.linkService.executeNamedAction(t),!1},e.className="internalLink"}}]),r}(A),x=function(e){f(r,e);var t=p(r);function r(e){b(this,r);var n=!!(e.data.hasPopup||e.data.title||e.data.contents);return t.call(this,e,n)}return w(r,[{key:"render",value:function(){this.container.className="textAnnotation";var e=document.createElement("img");return e.style.height=this.container.style.height,e.style.width=this.container.style.width,e.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg",e.alt="[{{type}} Annotation]",e.dataset.l10nId="text_annotation_type",e.dataset.l10nArgs=JSON.stringify({type:this.data.name}),this.data.hasPopup||this._createPopup(this.container,e,this.data),this.container.appendChild(e),this.container}}]),r}(A),P=function(e){f(r,e);var t=p(r);function r(){return b(this,r),t.apply(this,arguments)}return w(r,[{key:"render",value:function(){return this.container}}]),r}(A),C=function(e){f(r,e);var t=p(r);function r(e){b(this,r);var n=e.renderInteractiveForms||!e.data.hasAppearance&&!!e.data.fieldValue;return t.call(this,e,n)}return w(r,[{key:"render",value:function(){var e=["left","center","right"],t=this.annotationStorage,r=this.data.id;this.container.className="textWidgetAnnotation";var n=null;if(this.renderInteractiveForms){var i=t.getOrCreateValue(r,this.data.fieldValue);if(this.data.multiLine?(n=document.createElement("textarea"),n.textContent=i):(n=document.createElement("input"),n.type="text",n.setAttribute("value",i)),n.addEventListener("input",(function(e){t.setValue(r,e.target.value)})),n.disabled=this.data.readOnly,n.name=this.data.fieldName,null!==this.data.maxLen&&(n.maxLength=this.data.maxLen),this.data.comb){var a=this.data.rect[2]-this.data.rect[0],o=a/this.data.maxLen;n.classList.add("comb"),n.style.letterSpacing="calc(".concat(o,"px - 1ch)")}}else{n=document.createElement("div"),n.textContent=this.data.fieldValue,n.style.verticalAlign="middle",n.style.display="table-cell";var s=null;this.data.fontRefName&&this.page.commonObjs.has(this.data.fontRefName)&&(s=this.page.commonObjs.get(this.data.fontRefName)),this._setTextStyle(n,s)}return null!==this.data.textAlignment&&(n.style.textAlign=e[this.data.textAlignment]),this.container.appendChild(n),this.container}},{key:"_setTextStyle",value:function(e,t){var r=e.style;if(r.fontSize="".concat(this.data.fontSize,"px"),r.direction=this.data.fontDirection<0?"rtl":"ltr",t){var n="normal";t.black?n="900":t.bold&&(n="bold"),r.fontWeight=n,r.fontStyle=t.italic?"italic":"normal";var i=t.loadedName?'"'.concat(t.loadedName,'", '):"",a=t.fallbackName||"Helvetica, sans-serif";r.fontFamily=i+a}}}]),r}(P),T=function(e){f(r,e);var t=p(r);function r(e){return b(this,r),t.call(this,e,e.renderInteractiveForms)}return w(r,[{key:"render",value:function(){var e=this.annotationStorage,t=this.data,r=t.id,n=e.getOrCreateValue(r,t.fieldValue&&"Off"!==t.fieldValue);this.container.className="buttonWidgetAnnotation checkBox";var i=document.createElement("input");return i.disabled=t.readOnly,i.type="checkbox",i.name=this.data.fieldName,n&&i.setAttribute("checked",!0),i.addEventListener("change",(function(t){e.setValue(r,t.target.checked)})),this.container.appendChild(i),this.container}}]),r}(P),R=function(e){f(r,e);var t=p(r);function r(e){return b(this,r),t.call(this,e,e.renderInteractiveForms)}return w(r,[{key:"render",value:function(){this.container.className="buttonWidgetAnnotation radioButton";var e=this.annotationStorage,t=this.data,r=t.id,n=e.getOrCreateValue(r,t.fieldValue===t.buttonValue),i=document.createElement("input");return i.disabled=t.readOnly,i.type="radio",i.name=t.fieldName,n&&i.setAttribute("checked",!0),i.addEventListener("change",(function(t){var n,i=t.target.name,a=u(document.getElementsByName(i));try{for(a.s();!(n=a.n()).done;){var o=n.value;o!==t.target&&e.setValue(o.parentNode.getAttribute("data-annotation-id"),!1)}}catch(s){a.e(s)}finally{a.f()}e.setValue(r,t.target.checked)})),this.container.appendChild(i),this.container}}]),r}(P),E=function(e){f(r,e);var t=p(r);function r(){return b(this,r),t.apply(this,arguments)}return w(r,[{key:"render",value:function(){var e=o(m(r.prototype),"render",this).call(this);return e.className="buttonWidgetAnnotation pushButton",e}}]),r}(k),L=function(e){f(r,e);var t=p(r);function r(e){return b(this,r),t.call(this,e,e.renderInteractiveForms)}return w(r,[{key:"render",value:function(){this.container.className="choiceWidgetAnnotation";var e=this.annotationStorage,t=this.data.id;e.getOrCreateValue(t,this.data.fieldValue.length>0?this.data.fieldValue[0]:null);var r=document.createElement("select");r.disabled=this.data.readOnly,r.name=this.data.fieldName,this.data.combo||(r.size=this.data.options.length,this.data.multiSelect&&(r.multiple=!0));var n,i=u(this.data.options);try{for(i.s();!(n=i.n()).done;){var a=n.value,o=document.createElement("option");o.textContent=a.displayValue,o.value=a.exportValue,this.data.fieldValue.includes(a.exportValue)&&o.setAttribute("selected",!0),r.appendChild(o)}}catch(s){i.e(s)}finally{i.f()}return r.addEventListener("input",(function(r){var n=r.target.options,i=n[n.selectedIndex].value;e.setValue(t,i)})),this.container.appendChild(r),this.container}}]),r}(P),O=function(e){f(r,e);var t=p(r);function r(e){b(this,r);var n=!(!e.data.title&&!e.data.contents);return t.call(this,e,n)}return w(r,[{key:"render",value:function(){var e=["Line","Square","Circle","PolyLine","Polygon","Ink"];if(this.container.className="popupAnnotation",e.includes(this.data.parentType))return this.container;var t='[data-annotation-id="'.concat(this.data.parentId,'"]'),r=this.layer.querySelector(t);if(!r)return this.container;var n=new I({container:this.container,trigger:r,color:this.data.color,title:this.data.title,modificationDate:this.data.modificationDate,contents:this.data.contents}),i=parseFloat(r.style.left),a=parseFloat(r.style.width);return this.container.style.transformOrigin="-".concat(i+a,"px -").concat(r.style.top),this.container.style.left="".concat(i+a,"px"),this.container.appendChild(n.render()),this.container}}]),r}(A),I=function(){function e(t){b(this,e),this.container=t.container,this.trigger=t.trigger,this.color=t.color,this.title=t.title,this.modificationDate=t.modificationDate,this.contents=t.contents,this.hideWrapper=t.hideWrapper||!1,this.pinned=!1}return w(e,[{key:"render",value:function(){var e=.7,t=document.createElement("div");t.className="popupWrapper",this.hideElement=this.hideWrapper?t:this.container,this.hideElement.setAttribute("hidden",!0);var r=document.createElement("div");r.className="popup";var a=this.color;if(a){var o=e*(255-a[0])+a[0],s=e*(255-a[1])+a[1],u=e*(255-a[2])+a[2];r.style.backgroundColor=i.Util.makeCssRgb(0|o,0|s,0|u)}var c=document.createElement("h1");c.textContent=this.title,r.appendChild(c);var l=n.PDFDateString.toDateObject(this.modificationDate);if(l){var h=document.createElement("span");h.textContent="{{date}}, {{time}}",h.dataset.l10nId="annotation_date_string",h.dataset.l10nArgs=JSON.stringify({date:l.toLocaleDateString(),time:l.toLocaleTimeString()}),r.appendChild(h)}var f=this._formatContents(this.contents);return r.appendChild(f),this.trigger.addEventListener("click",this._toggle.bind(this)),this.trigger.addEventListener("mouseover",this._show.bind(this,!1)),this.trigger.addEventListener("mouseout",this._hide.bind(this,!1)),r.addEventListener("click",this._hide.bind(this,!0)),t.appendChild(r),t}},{key:"_formatContents",value:function(e){for(var t=document.createElement("p"),r=e.split(/(?:\r\n?|\n)/),n=0,i=r.length;n<i;++n){var a=r[n];t.appendChild(document.createTextNode(a)),n<i-1&&t.appendChild(document.createElement("br"))}return t}},{key:"_toggle",value:function(){this.pinned?this._hide(!0):this._show(!0)}},{key:"_show",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];e&&(this.pinned=!0),this.hideElement.hasAttribute("hidden")&&(this.hideElement.removeAttribute("hidden"),this.container.style.zIndex+=1)}},{key:"_hide",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];e&&(this.pinned=!1),this.hideElement.hasAttribute("hidden")||this.pinned||(this.hideElement.setAttribute("hidden",!0),this.container.style.zIndex-=1)}}]),e}(),M=function(e){f(r,e);var t=p(r);function r(e){b(this,r);var n=!!(e.data.hasPopup||e.data.title||e.data.contents);return t.call(this,e,n,!0)}return w(r,[{key:"render",value:function(){return this.container.className="freeTextAnnotation",this.data.hasPopup||this._createPopup(this.container,null,this.data),this.container}}]),r}(A),F=function(e){f(r,e);var t=p(r);function r(e){b(this,r);var n=!!(e.data.hasPopup||e.data.title||e.data.contents);return t.call(this,e,n,!0)}return w(r,[{key:"render",value:function(){this.container.className="lineAnnotation";var e=this.data,t=e.rect[2]-e.rect[0],r=e.rect[3]-e.rect[1],n=this.svgFactory.create(t,r),i=this.svgFactory.createElement("svg:line");return i.setAttribute("x1",e.rect[2]-e.lineCoordinates[0]),i.setAttribute("y1",e.rect[3]-e.lineCoordinates[1]),i.setAttribute("x2",e.rect[2]-e.lineCoordinates[2]),i.setAttribute("y2",e.rect[3]-e.lineCoordinates[3]),i.setAttribute("stroke-width",e.borderStyle.width||1),i.setAttribute("stroke","transparent"),n.appendChild(i),this.container.append(n),this._createPopup(this.container,i,e),this.container}}]),r}(A),N=function(e){f(r,e);var t=p(r);function r(e){b(this,r);var n=!!(e.data.hasPopup||e.data.title||e.data.contents);return t.call(this,e,n,!0)}return w(r,[{key:"render",value:function(){this.container.className="squareAnnotation";var e=this.data,t=e.rect[2]-e.rect[0],r=e.rect[3]-e.rect[1],n=this.svgFactory.create(t,r),i=e.borderStyle.width,a=this.svgFactory.createElement("svg:rect");return a.setAttribute("x",i/2),a.setAttribute("y",i/2),a.setAttribute("width",t-i),a.setAttribute("height",r-i),a.setAttribute("stroke-width",i||1),a.setAttribute("stroke","transparent"),a.setAttribute("fill","none"),n.appendChild(a),this.container.append(n),this._createPopup(this.container,a,e),this.container}}]),r}(A),D=function(e){f(r,e);var t=p(r);function r(e){b(this,r);var n=!!(e.data.hasPopup||e.data.title||e.data.contents);return t.call(this,e,n,!0)}return w(r,[{key:"render",value:function(){this.container.className="circleAnnotation";var e=this.data,t=e.rect[2]-e.rect[0],r=e.rect[3]-e.rect[1],n=this.svgFactory.create(t,r),i=e.borderStyle.width,a=this.svgFactory.createElement("svg:ellipse");return a.setAttribute("cx",t/2),a.setAttribute("cy",r/2),a.setAttribute("rx",t/2-i/2),a.setAttribute("ry",r/2-i/2),a.setAttribute("stroke-width",i||1),a.setAttribute("stroke","transparent"),a.setAttribute("fill","none"),n.appendChild(a),this.container.append(n),this._createPopup(this.container,a,e),this.container}}]),r}(A),j=function(e){f(r,e);var t=p(r);function r(e){var n;b(this,r);var i=!!(e.data.hasPopup||e.data.title||e.data.contents);return n=t.call(this,e,i,!0),n.containerClassName="polylineAnnotation",n.svgElementName="svg:polyline",n}return w(r,[{key:"render",value:function(){this.container.className=this.containerClassName;var e,t=this.data,r=t.rect[2]-t.rect[0],n=t.rect[3]-t.rect[1],i=this.svgFactory.create(r,n),a=[],o=u(t.vertices);try{for(o.s();!(e=o.n()).done;){var s=e.value,c=s.x-t.rect[0],l=t.rect[3]-s.y;a.push(c+","+l)}}catch(f){o.e(f)}finally{o.f()}a=a.join(" ");var h=this.svgFactory.createElement(this.svgElementName);return h.setAttribute("points",a),h.setAttribute("stroke-width",t.borderStyle.width||1),h.setAttribute("stroke","transparent"),h.setAttribute("fill","none"),i.appendChild(h),this.container.append(i),this._createPopup(this.container,h,t),this.container}}]),r}(A),U=function(e){f(r,e);var t=p(r);function r(e){var n;return b(this,r),n=t.call(this,e),n.containerClassName="polygonAnnotation",n.svgElementName="svg:polygon",n}return r}(j),q=function(e){f(r,e);var t=p(r);function r(e){b(this,r);var n=!!(e.data.hasPopup||e.data.title||e.data.contents);return t.call(this,e,n,!0)}return w(r,[{key:"render",value:function(){return this.container.className="caretAnnotation",this.data.hasPopup||this._createPopup(this.container,null,this.data),this.container}}]),r}(A),B=function(e){f(r,e);var t=p(r);function r(e){var n;b(this,r);var i=!!(e.data.hasPopup||e.data.title||e.data.contents);return n=t.call(this,e,i,!0),n.containerClassName="inkAnnotation",n.svgElementName="svg:polyline",n}return w(r,[{key:"render",value:function(){this.container.className=this.containerClassName;var e,t=this.data,r=t.rect[2]-t.rect[0],n=t.rect[3]-t.rect[1],i=this.svgFactory.create(r,n),a=u(t.inkLists);try{for(a.s();!(e=a.n()).done;){var o,s=e.value,c=[],l=u(s);try{for(l.s();!(o=l.n()).done;){var h=o.value,f=h.x-t.rect[0],d=t.rect[3]-h.y;c.push("".concat(f,",").concat(d))}}catch(v){l.e(v)}finally{l.f()}c=c.join(" ");var p=this.svgFactory.createElement(this.svgElementName);p.setAttribute("points",c),p.setAttribute("stroke-width",t.borderStyle.width||1),p.setAttribute("stroke","transparent"),p.setAttribute("fill","none"),this._createPopup(this.container,p,t),i.appendChild(p)}}catch(v){a.e(v)}finally{a.f()}return this.container.append(i),this.container}}]),r}(A),W=function(e){f(r,e);var t=p(r);function r(e){b(this,r);var n=!!(e.data.hasPopup||e.data.title||e.data.contents);return t.call(this,e,n,!0)}return w(r,[{key:"render",value:function(){return this.container.className="highlightAnnotation",this.data.hasPopup||this._createPopup(this.container,null,this.data),this.container}}]),r}(A),V=function(e){f(r,e);var t=p(r);function r(e){b(this,r);var n=!!(e.data.hasPopup||e.data.title||e.data.contents);return t.call(this,e,n,!0)}return w(r,[{key:"render",value:function(){return this.container.className="underlineAnnotation",this.data.hasPopup||this._createPopup(this.container,null,this.data),this.container}}]),r}(A),z=function(e){f(r,e);var t=p(r);function r(e){b(this,r);var n=!!(e.data.hasPopup||e.data.title||e.data.contents);return t.call(this,e,n,!0)}return w(r,[{key:"render",value:function(){return this.container.className="squigglyAnnotation",this.data.hasPopup||this._createPopup(this.container,null,this.data),this.container}}]),r}(A),H=function(e){f(r,e);var t=p(r);function r(e){b(this,r);var n=!!(e.data.hasPopup||e.data.title||e.data.contents);return t.call(this,e,n,!0)}return w(r,[{key:"render",value:function(){return this.container.className="strikeoutAnnotation",this.data.hasPopup||this._createPopup(this.container,null,this.data),this.container}}]),r}(A),G=function(e){f(r,e);var t=p(r);function r(e){b(this,r);var n=!!(e.data.hasPopup||e.data.title||e.data.contents);return t.call(this,e,n,!0)}return w(r,[{key:"render",value:function(){return this.container.className="stampAnnotation",this.data.hasPopup||this._createPopup(this.container,null,this.data),this.container}}]),r}(A),Y=function(e){f(r,e);var t=p(r);function r(e){var a;b(this,r),a=t.call(this,e,!0);var o=a.data.file,s=o.filename,u=o.content;return a.filename=(0,n.getFilenameFromUrl)(s),a.content=u,a.linkService.eventBus&&a.linkService.eventBus.dispatch("fileattachmentannotation",{source:g(a),id:(0,i.stringToPDFString)(s),filename:s,content:u}),a}return w(r,[{key:"render",value:function(){this.container.className="fileAttachmentAnnotation";var e=document.createElement("div");return e.style.height=this.container.style.height,e.style.width=this.container.style.width,e.addEventListener("dblclick",this._download.bind(this)),this.data.hasPopup||!this.data.title&&!this.data.contents||this._createPopup(this.container,e,this.data),this.container.appendChild(e),this.container}},{key:"_download",value:function(){this.downloadManager?this.downloadManager.downloadData(this.content,this.filename,""):(0,i.warn)("Download cannot be started due to unavailable download manager")}}]),r}(A),X=function(){function e(){b(this,e)}return w(e,null,[{key:"render",value:function(e){var t,r=[],o=[],s=u(e.annotations);try{for(s.s();!(t=s.n()).done;){var c=t.value;c&&(c.annotationType!==i.AnnotationType.POPUP?r.push(c):o.push(c))}}catch(p){s.e(p)}finally{s.f()}o.length&&r.push.apply(r,o);for(var l=0,h=r;l<h.length;l++){var f=h[l],d=S.create({data:f,layer:e.div,page:e.page,viewport:e.viewport,linkService:e.linkService,downloadManager:e.downloadManager,imageResourcesPath:e.imageResourcesPath||"",renderInteractiveForms:"boolean"!==typeof e.renderInteractiveForms||e.renderInteractiveForms,svgFactory:new n.DOMSVGFactory,annotationStorage:e.annotationStorage||new a.AnnotationStorage});d.isRenderable&&e.div.appendChild(d.render())}}},{key:"update",value:function(e){var t,r=u(e.annotations);try{for(r.s();!(t=r.n()).done;){var n=t.value,i=e.div.querySelector('[data-annotation-id="'.concat(n.id,'"]'));i&&(i.style.transform="matrix(".concat(e.viewport.transform.join(","),")"))}}catch(a){r.e(a)}finally{r.f()}e.div.removeAttribute("hidden")}}]),e}();t.AnnotationLayer=X},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.renderTextLayer=void 0;var n=r(5),i=function(){var e=1e5,t=/\S/;function r(e){return!t.test(e)}function i(e,t,i){var a=document.createElement("span"),o={angle:0,canvasWidth:0,isWhitespace:!1,originalTransform:null,paddingBottom:0,paddingLeft:0,paddingRight:0,paddingTop:0,scale:1};if(e._textDivs.push(a),r(t.str))return o.isWhitespace=!0,void e._textDivProperties.set(a,o);var s=n.Util.transform(e._viewport.transform,t.transform),u=Math.atan2(s[1],s[0]),c=i[t.fontName];c.vertical&&(u+=Math.PI/2);var l,h,f=Math.sqrt(s[2]*s[2]+s[3]*s[3]),d=f;c.ascent?d=c.ascent*d:c.descent&&(d=(1+c.descent)*d),0===u?(l=s[4],h=s[5]-d):(l=s[4]+d*Math.sin(u),h=s[5]-d*Math.cos(u)),a.style.left="".concat(l,"px"),a.style.top="".concat(h,"px"),a.style.fontSize="".concat(f,"px"),a.style.fontFamily=c.fontFamily,a.textContent=t.str,e._fontInspectorEnabled&&(a.dataset.fontName=t.fontName),0!==u&&(o.angle=u*(180/Math.PI));var p=!1;if(t.str.length>1)p=!0;else if(t.transform[0]!==t.transform[3]){var v=Math.abs(t.transform[0]),g=Math.abs(t.transform[3]);v!==g&&Math.max(v,g)/Math.min(v,g)>1.5&&(p=!0)}if(p&&(c.vertical?o.canvasWidth=t.height*e._viewport.scale:o.canvasWidth=t.width*e._viewport.scale),e._textDivProperties.set(a,o),e._textContentStream&&e._layoutText(a),e._enhanceTextSelection){var y=1,m=0;0!==u&&(y=Math.cos(u),m=Math.sin(u));var b,_,w=(c.vertical?t.height:t.width)*e._viewport.scale,S=f;0!==u?(b=[y,m,-m,y,l,h],_=n.Util.getAxialAlignedBoundingBox([0,0,w,S],b)):_=[l,h,l+w,h+S],e._bounds.push({left:_[0],top:_[1],right:_[2],bottom:_[3],div:a,size:[w,S],m:b})}}function a(t){if(!t._canceled){var r=t._textDivs,n=t._capability,i=r.length;if(i>e)return t._renderingDone=!0,void n.resolve();if(!t._textContentStream)for(var a=0;a<i;a++)t._layoutText(r[a]);t._renderingDone=!0,n.resolve()}}function o(e,t,r){for(var n=0,i=0;i<r;i++){var a=e[t++];a>0&&(n=n?Math.min(a,n):a)}return n}function s(e){for(var t=e._bounds,r=e._viewport,i=u(r.width,r.height,t),a=0;a<i.length;a++){var s=t[a].div,c=e._textDivProperties.get(s);if(0!==c.angle){var l=i[a],h=t[a],f=h.m,d=f[0],p=f[1],v=[[0,0],[0,h.size[1]],[h.size[0],0],h.size],g=new Float64Array(64);v.forEach((function(e,t){var r=n.Util.applyTransform(e,f);g[t+0]=d&&(l.left-r[0])/d,g[t+4]=p&&(l.top-r[1])/p,g[t+8]=d&&(l.right-r[0])/d,g[t+12]=p&&(l.bottom-r[1])/p,g[t+16]=p&&(l.left-r[0])/-p,g[t+20]=d&&(l.top-r[1])/d,g[t+24]=p&&(l.right-r[0])/-p,g[t+28]=d&&(l.bottom-r[1])/d,g[t+32]=d&&(l.left-r[0])/-d,g[t+36]=p&&(l.top-r[1])/-p,g[t+40]=d&&(l.right-r[0])/-d,g[t+44]=p&&(l.bottom-r[1])/-p,g[t+48]=p&&(l.left-r[0])/p,g[t+52]=d&&(l.top-r[1])/-d,g[t+56]=p&&(l.right-r[0])/p,g[t+60]=d&&(l.bottom-r[1])/-d}));var y=1+Math.min(Math.abs(d),Math.abs(p));c.paddingLeft=o(g,32,16)/y,c.paddingTop=o(g,48,16)/y,c.paddingRight=o(g,0,16)/y,c.paddingBottom=o(g,16,16)/y,e._textDivProperties.set(s,c)}else c.paddingLeft=t[a].left-i[a].left,c.paddingTop=t[a].top-i[a].top,c.paddingRight=i[a].right-t[a].right,c.paddingBottom=i[a].bottom-t[a].bottom,e._textDivProperties.set(s,c)}}function u(e,t,r){var n=r.map((function(e,t){return{x1:e.left,y1:e.top,x2:e.right,y2:e.bottom,index:t,x1New:void 0,x2New:void 0}}));c(e,n);var i=new Array(r.length);return n.forEach((function(e){var t=e.index;i[t]={left:e.x1New,top:0,right:e.x2New,bottom:0}})),r.map((function(t,r){var a=i[r],o=n[r];o.x1=t.top,o.y1=e-a.right,o.x2=t.bottom,o.y2=e-a.left,o.index=r,o.x1New=void 0,o.x2New=void 0})),c(t,n),n.forEach((function(e){var t=e.index;i[t].top=e.x1New,i[t].bottom=e.x2New})),i}function c(e,t){t.sort((function(e,t){return e.x1-t.x1||e.index-t.index}));var r={x1:-1/0,y1:-1/0,x2:0,y2:1/0,index:-1,x1New:0,x2New:0},n=[{start:-1/0,end:1/0,boundary:r}];t.forEach((function(e){var t=0;while(t<n.length&&n[t].end<=e.y1)t++;var r,i,a=n.length-1;while(a>=0&&n[a].start>=e.y2)a--;var o,s,u=-1/0;for(o=t;o<=a;o++){var c;r=n[o],i=r.boundary,c=i.x2>e.x1?i.index>e.index?i.x1New:e.x1:void 0===i.x2New?(i.x2+e.x1)/2:i.x2New,c>u&&(u=c)}for(e.x1New=u,o=t;o<=a;o++)r=n[o],i=r.boundary,void 0===i.x2New?i.x2>e.x1?i.index>e.index&&(i.x2New=i.x2):i.x2New=u:i.x2New>u&&(i.x2New=Math.max(u,i.x2));var l=[],h=null;for(o=t;o<=a;o++){r=n[o],i=r.boundary;var f=i.x2>e.x2?i:e;h===f?l[l.length-1].end=r.end:(l.push({start:r.start,end:r.end,boundary:f}),h=f)}for(n[t].start<e.y1&&(l[0].start=e.y1,l.unshift({start:n[t].start,end:e.y1,boundary:n[t].boundary})),e.y2<n[a].end&&(l[l.length-1].end=e.y2,l.push({start:e.y2,end:n[a].end,boundary:n[a].boundary})),o=t;o<=a;o++)if(r=n[o],i=r.boundary,void 0===i.x2New){var d=!1;for(s=t-1;!d&&s>=0&&n[s].start>=i.y1;s--)d=n[s].boundary===i;for(s=a+1;!d&&s<n.length&&n[s].end<=i.y2;s++)d=n[s].boundary===i;for(s=0;!d&&s<l.length;s++)d=l[s].boundary===i;d||(i.x2New=u)}Array.prototype.splice.apply(n,[t,a-t+1].concat(l))})),n.forEach((function(t){var r=t.boundary;void 0===r.x2New&&(r.x2New=Math.max(e,r.x2))}))}function l(e){var t=this,r=e.textContent,i=e.textContentStream,a=e.container,o=e.viewport,s=e.textDivs,u=e.textContentItemsStr,c=e.enhanceTextSelection;this._textContent=r,this._textContentStream=i,this._container=a,this._document=a.ownerDocument,this._viewport=o,this._textDivs=s||[],this._textContentItemsStr=u||[],this._enhanceTextSelection=!!c,this._fontInspectorEnabled=!(!globalThis.FontInspector||!globalThis.FontInspector.enabled),this._reader=null,this._layoutTextLastFontSize=null,this._layoutTextLastFontFamily=null,this._layoutTextCtx=null,this._textDivProperties=new WeakMap,this._renderingDone=!1,this._canceled=!1,this._capability=(0,n.createPromiseCapability)(),this._renderTimer=null,this._bounds=[],this._capability.promise["finally"]((function(){t._layoutTextCtx&&(t._layoutTextCtx.canvas.width=0,t._layoutTextCtx.canvas.height=0,t._layoutTextCtx=null)}))["catch"]((function(){}))}function h(e){var t=new l({textContent:e.textContent,textContentStream:e.textContentStream,container:e.container,viewport:e.viewport,textDivs:e.textDivs,textContentItemsStr:e.textContentItemsStr,enhanceTextSelection:e.enhanceTextSelection});return t._render(e.timeout),t}return l.prototype={get promise(){return this._capability.promise},cancel:function(){this._canceled=!0,this._reader&&(this._reader.cancel(new n.AbortException("TextLayer task cancelled.")),this._reader=null),null!==this._renderTimer&&(clearTimeout(this._renderTimer),this._renderTimer=null),this._capability.reject(new Error("TextLayer task cancelled."))},_processItems:function(e,t){for(var r=0,n=e.length;r<n;r++)this._textContentItemsStr.push(e[r].str),i(this,e[r],t)},_layoutText:function(e){var t=this._textDivProperties.get(e);if(!t.isWhitespace){var r="";if(0!==t.canvasWidth){var n=e.style,i=n.fontSize,a=n.fontFamily;i===this._layoutTextLastFontSize&&a===this._layoutTextLastFontFamily||(this._layoutTextCtx.font="".concat(i," ").concat(a),this._layoutTextLastFontSize=i,this._layoutTextLastFontFamily=a);var o=this._layoutTextCtx.measureText(e.textContent),s=o.width;s>0&&(t.scale=t.canvasWidth/s,r="scaleX(".concat(t.scale,")"))}0!==t.angle&&(r="rotate(".concat(t.angle,"deg) ").concat(r)),r.length>0&&(this._enhanceTextSelection&&(t.originalTransform=r),e.style.transform=r),this._textDivProperties.set(e,t),this._container.appendChild(e)}},_render:function(e){var t=this,r=(0,n.createPromiseCapability)(),i=Object.create(null),o=this._document.createElement("canvas");if(o.mozOpaque=!0,this._layoutTextCtx=o.getContext("2d",{alpha:!1}),this._textContent){var s=this._textContent.items,u=this._textContent.styles;this._processItems(s,u),r.resolve()}else{if(!this._textContentStream)throw new Error('Neither "textContent" nor "textContentStream" parameters specified.');var c=function e(){t._reader.read().then((function(n){var a=n.value,o=n.done;o?r.resolve():(Object.assign(i,a.styles),t._processItems(a.items,i),e())}),r.reject)};this._reader=this._textContentStream.getReader(),c()}r.promise.then((function(){i=null,e?t._renderTimer=setTimeout((function(){a(t),t._renderTimer=null}),e):a(t)}),this._capability.reject)},expandTextDivs:function(e){if(this._enhanceTextSelection&&this._renderingDone){null!==this._bounds&&(s(this),this._bounds=null);for(var t=[],r=[],n=0,i=this._textDivs.length;n<i;n++){var a=this._textDivs[n],o=this._textDivProperties.get(a);o.isWhitespace||(e?(t.length=0,r.length=0,o.originalTransform&&t.push(o.originalTransform),o.paddingTop>0?(r.push("".concat(o.paddingTop,"px")),t.push("translateY(".concat(-o.paddingTop,"px)"))):r.push(0),o.paddingRight>0?r.push("".concat(o.paddingRight/o.scale,"px")):r.push(0),o.paddingBottom>0?r.push("".concat(o.paddingBottom,"px")):r.push(0),o.paddingLeft>0?(r.push("".concat(o.paddingLeft/o.scale,"px")),t.push("translateX(".concat(-o.paddingLeft/o.scale,"px)"))):r.push(0),a.style.padding=r.join(" "),t.length&&(a.style.transform=t.join(" "))):(a.style.padding=null,a.style.transform=o.originalTransform))}}}},h}();t.renderTextLayer=i},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SVGGraphics=void 0;var n=r(5),i=r(1),a=r(7);function o(e){return c(e)||u(e)||v(e)||s()}function s(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}function c(e){if(Array.isArray(e))return g(e)}function l(e,t){return d(e)||f(e,t)||v(e,t)||h()}function h(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var r=[],n=!0,i=!1,a=void 0;try{for(var o,s=e[Symbol.iterator]();!(n=(o=s.next()).done);n=!0)if(r.push(o.value),t&&r.length===t)break}catch(u){i=!0,a=u}finally{try{n||null==s["return"]||s["return"]()}finally{if(i)throw a}}return r}}function d(e){if(Array.isArray(e))return e}function p(e,t){var r;if("undefined"===typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(r=v(e))||t&&e&&"number"===typeof e.length){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){r=e[Symbol.iterator]()},n:function(){var e=r.next();return o=e.done,e},e:function(e){s=!0,a=e},f:function(){try{o||null==r["return"]||r["return"]()}finally{if(s)throw a}}}}function v(e,t){if(e){if("string"===typeof e)return g(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?g(e,t):void 0}}function g(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function y(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function m(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function b(e,t,r){return t&&m(e.prototype,t),r&&m(e,r),e}var _=function(){throw new Error("Not implemented: SVGGraphics")};t.SVGGraphics=_;var w=function(e){var t,r=[],n=[],i=p(e);try{for(i.s();!(t=i.n()).done;){var a=t.value;"save"!==a.fn?"restore"===a.fn?r=n.pop():r.push(a):(r.push({fnId:92,fn:"group",items:[]}),n.push(r),r=r[r.length-1].items)}}catch(o){i.e(o)}finally{i.f()}return r},S=function(e){if(Number.isInteger(e))return e.toString();var t=e.toFixed(10),r=t.length-1;if("0"!==t[r])return t;do{r--}while("0"===t[r]);return t.substring(0,"."===t[r]?r:r+1)},A=function(e){if(0===e[4]&&0===e[5]){if(0===e[1]&&0===e[2])return 1===e[0]&&1===e[3]?"":"scale(".concat(S(e[0])," ").concat(S(e[3]),")");if(e[0]===e[3]&&e[1]===-e[2]){var t=180*Math.acos(e[0])/Math.PI;return"rotate(".concat(S(t),")")}}else if(1===e[0]&&0===e[1]&&0===e[2]&&1===e[3])return"translate(".concat(S(e[4])," ").concat(S(e[5]),")");return"matrix(".concat(S(e[0])," ").concat(S(e[1])," ").concat(S(e[2])," ").concat(S(e[3])," ").concat(S(e[4])," ")+"".concat(S(e[5]),")")},k={fontStyle:"normal",fontWeight:"normal",fillColor:"#000000"},x="http://www.w3.org/XML/1998/namespace",P="http://www.w3.org/1999/xlink",C=["butt","round","square"],T=["miter","round","bevel"],R=function(){for(var e=new Uint8Array([137,80,78,71,13,10,26,10]),t=12,r=new Int32Array(256),i=0;i<256;i++){for(var o=i,s=0;s<8;s++)o=1&o?3988292384^o>>1&2147483647:o>>1&2147483647;r[i]=o}function u(e,t,n){for(var i=-1,a=t;a<n;a++){var o=255&(i^e[a]),s=r[o];i=i>>>8^s}return-1^i}function c(e,t,r,n){var i=n,a=t.length;r[i]=a>>24&255,r[i+1]=a>>16&255,r[i+2]=a>>8&255,r[i+3]=255&a,i+=4,r[i]=255&e.charCodeAt(0),r[i+1]=255&e.charCodeAt(1),r[i+2]=255&e.charCodeAt(2),r[i+3]=255&e.charCodeAt(3),i+=4,r.set(t,i),i+=t.length;var o=u(r,n+4,i);r[i]=o>>24&255,r[i+1]=o>>16&255,r[i+2]=o>>8&255,r[i+3]=255&o}function l(e,t,r){for(var n=1,i=0,a=t;a<r;++a)n=(n+(255&e[a]))%65521,i=(i+n)%65521;return i<<16|n}function h(e){if(!a.isNodeJS)return f(e);try{var t;t=parseInt(process.versions.node)>=8?e:Buffer.from(e);var r=__webpack_require__(10).deflateSync(t,{level:9});return r instanceof Uint8Array?r:new Uint8Array(r)}catch(i){(0,n.warn)("Not compressing PNG because zlib.deflateSync is unavailable: "+i)}return f(e)}function f(e){var t=e.length,r=65535,n=Math.ceil(t/r),i=new Uint8Array(2+t+5*n+4),a=0;i[a++]=120,i[a++]=156;var o=0;while(t>r)i[a++]=0,i[a++]=255,i[a++]=255,i[a++]=0,i[a++]=0,i.set(e.subarray(o,o+r),a),a+=r,o+=r,t-=r;i[a++]=1,i[a++]=255&t,i[a++]=t>>8&255,i[a++]=255&~t,i[a++]=(65535&~t)>>8&255,i.set(e.subarray(o),a),a+=e.length-o;var s=l(e,0,e.length);return i[a++]=s>>24&255,i[a++]=s>>16&255,i[a++]=s>>8&255,i[a++]=255&s,i}function d(r,i,a,o){var s,u,l,f=r.width,d=r.height,p=r.data;switch(i){case n.ImageKind.GRAYSCALE_1BPP:u=0,s=1,l=f+7>>3;break;case n.ImageKind.RGB_24BPP:u=2,s=8,l=3*f;break;case n.ImageKind.RGBA_32BPP:u=6,s=8,l=4*f;break;default:throw new Error("invalid format")}for(var v=new Uint8Array((1+l)*d),g=0,y=0,m=0;m<d;++m)v[g++]=0,v.set(p.subarray(y,y+l),g),y+=l,g+=l;if(i===n.ImageKind.GRAYSCALE_1BPP&&o){g=0;for(var b=0;b<d;b++){g++;for(var _=0;_<l;_++)v[g++]^=255}}var w=new Uint8Array([f>>24&255,f>>16&255,f>>8&255,255&f,d>>24&255,d>>16&255,d>>8&255,255&d,s,u,0,0,0]),S=h(v),A=e.length+3*t+w.length+S.length,k=new Uint8Array(A),x=0;return k.set(e,x),x+=e.length,c("IHDR",w,k,x),x+=t+w.length,c("IDATA",S,k,x),x+=t+S.length,c("IEND",new Uint8Array(0),k,x),(0,n.createObjectURL)(k,"image/png",a)}return function(e,t,r){var i=void 0===e.kind?n.ImageKind.GRAYSCALE_1BPP:e.kind;return d(e,i,t,r)}}(),E=function(){function e(){y(this,e),this.fontSizeScale=1,this.fontWeight=k.fontWeight,this.fontSize=0,this.textMatrix=n.IDENTITY_MATRIX,this.fontMatrix=n.FONT_IDENTITY_MATRIX,this.leading=0,this.textRenderingMode=n.TextRenderingMode.FILL,this.textMatrixScale=1,this.x=0,this.y=0,this.lineX=0,this.lineY=0,this.charSpacing=0,this.wordSpacing=0,this.textHScale=1,this.textRise=0,this.fillColor=k.fillColor,this.strokeColor="#000000",this.fillAlpha=1,this.strokeAlpha=1,this.lineWidth=1,this.lineJoin="",this.lineCap="",this.miterLimit=0,this.dashArray=[],this.dashPhase=0,this.dependencies=[],this.activeClipUrl=null,this.clipGroup=null,this.maskId=""}return b(e,[{key:"clone",value:function(){return Object.create(this)}},{key:"setCurrentPoint",value:function(e,t){this.x=e,this.y=t}}]),e}(),L=0,O=0,I=0;t.SVGGraphics=_=function(){function e(t,r){var a=arguments.length>2&&void 0!==arguments[2]&&arguments[2];for(var o in y(this,e),this.svgFactory=new i.DOMSVGFactory,this.current=new E,this.transformMatrix=n.IDENTITY_MATRIX,this.transformStack=[],this.extraStack=[],this.commonObjs=t,this.objs=r,this.pendingClip=null,this.pendingEOFill=!1,this.embedFonts=!1,this.embeddedFonts=Object.create(null),this.cssStyle=null,this.forceDataSchema=!!a,this._operatorIdMapping=[],n.OPS)this._operatorIdMapping[n.OPS[o]]=o}return b(e,[{key:"save",value:function(){this.transformStack.push(this.transformMatrix);var e=this.current;this.extraStack.push(e),this.current=e.clone()}},{key:"restore",value:function(){this.transformMatrix=this.transformStack.pop(),this.current=this.extraStack.pop(),this.pendingClip=null,this.tgrp=null}},{key:"group",value:function(e){this.save(),this.executeOpTree(e),this.restore()}},{key:"loadDependencies",value:function(e){for(var t=this,r=e.fnArray,i=e.argsArray,a=0,o=r.length;a<o;a++)if(r[a]===n.OPS.dependency){var s,u=p(i[a]);try{var c=function(){var e=s.value,r=e.startsWith("g_")?t.commonObjs:t.objs,n=new Promise((function(t){r.get(e,t)}));t.current.dependencies.push(n)};for(u.s();!(s=u.n()).done;)c()}catch(l){u.e(l)}finally{u.f()}}return Promise.all(this.current.dependencies)}},{key:"transform",value:function(e,t,r,i,a,o){var s=[e,t,r,i,a,o];this.transformMatrix=n.Util.transform(this.transformMatrix,s),this.tgrp=null}},{key:"getSVG",value:function(e,t){var r=this;this.viewport=t;var i=this._initialize(t);return this.loadDependencies(e).then((function(){return r.transformMatrix=n.IDENTITY_MATRIX,r.executeOpTree(r.convertOpList(e)),i}))}},{key:"convertOpList",value:function(e){for(var t=this._operatorIdMapping,r=e.argsArray,n=e.fnArray,i=[],a=0,o=n.length;a<o;a++){var s=n[a];i.push({fnId:s,fn:t[s],args:r[a]})}return w(i)}},{key:"executeOpTree",value:function(e){var t,r=p(e);try{for(r.s();!(t=r.n()).done;){var i=t.value,a=i.fn,o=i.fnId,s=i.args;switch(0|o){case n.OPS.beginText:this.beginText();break;case n.OPS.dependency:break;case n.OPS.setLeading:this.setLeading(s);break;case n.OPS.setLeadingMoveText:this.setLeadingMoveText(s[0],s[1]);break;case n.OPS.setFont:this.setFont(s);break;case n.OPS.showText:this.showText(s[0]);break;case n.OPS.showSpacedText:this.showText(s[0]);break;case n.OPS.endText:this.endText();break;case n.OPS.moveText:this.moveText(s[0],s[1]);break;case n.OPS.setCharSpacing:this.setCharSpacing(s[0]);break;case n.OPS.setWordSpacing:this.setWordSpacing(s[0]);break;case n.OPS.setHScale:this.setHScale(s[0]);break;case n.OPS.setTextMatrix:this.setTextMatrix(s[0],s[1],s[2],s[3],s[4],s[5]);break;case n.OPS.setTextRise:this.setTextRise(s[0]);break;case n.OPS.setTextRenderingMode:this.setTextRenderingMode(s[0]);break;case n.OPS.setLineWidth:this.setLineWidth(s[0]);break;case n.OPS.setLineJoin:this.setLineJoin(s[0]);break;case n.OPS.setLineCap:this.setLineCap(s[0]);break;case n.OPS.setMiterLimit:this.setMiterLimit(s[0]);break;case n.OPS.setFillRGBColor:this.setFillRGBColor(s[0],s[1],s[2]);break;case n.OPS.setStrokeRGBColor:this.setStrokeRGBColor(s[0],s[1],s[2]);break;case n.OPS.setStrokeColorN:this.setStrokeColorN(s);break;case n.OPS.setFillColorN:this.setFillColorN(s);break;case n.OPS.shadingFill:this.shadingFill(s[0]);break;case n.OPS.setDash:this.setDash(s[0],s[1]);break;case n.OPS.setRenderingIntent:this.setRenderingIntent(s[0]);break;case n.OPS.setFlatness:this.setFlatness(s[0]);break;case n.OPS.setGState:this.setGState(s[0]);break;case n.OPS.fill:this.fill();break;case n.OPS.eoFill:this.eoFill();break;case n.OPS.stroke:this.stroke();break;case n.OPS.fillStroke:this.fillStroke();break;case n.OPS.eoFillStroke:this.eoFillStroke();break;case n.OPS.clip:this.clip("nonzero");break;case n.OPS.eoClip:this.clip("evenodd");break;case n.OPS.paintSolidColorImageMask:this.paintSolidColorImageMask();break;case n.OPS.paintImageXObject:this.paintImageXObject(s[0]);break;case n.OPS.paintInlineImageXObject:this.paintInlineImageXObject(s[0]);break;case n.OPS.paintImageMaskXObject:this.paintImageMaskXObject(s[0]);break;case n.OPS.paintFormXObjectBegin:this.paintFormXObjectBegin(s[0],s[1]);break;case n.OPS.paintFormXObjectEnd:this.paintFormXObjectEnd();break;case n.OPS.closePath:this.closePath();break;case n.OPS.closeStroke:this.closeStroke();break;case n.OPS.closeFillStroke:this.closeFillStroke();break;case n.OPS.closeEOFillStroke:this.closeEOFillStroke();break;case n.OPS.nextLine:this.nextLine();break;case n.OPS.transform:this.transform(s[0],s[1],s[2],s[3],s[4],s[5]);break;case n.OPS.constructPath:this.constructPath(s[0],s[1]);break;case n.OPS.endPath:this.endPath();break;case 92:this.group(i.items);break;default:(0,n.warn)("Unimplemented operator ".concat(a));break}}}catch(u){r.e(u)}finally{r.f()}}},{key:"setWordSpacing",value:function(e){this.current.wordSpacing=e}},{key:"setCharSpacing",value:function(e){this.current.charSpacing=e}},{key:"nextLine",value:function(){this.moveText(0,this.current.leading)}},{key:"setTextMatrix",value:function(e,t,r,n,i,a){var o=this.current;o.textMatrix=o.lineMatrix=[e,t,r,n,i,a],o.textMatrixScale=Math.sqrt(e*e+t*t),o.x=o.lineX=0,o.y=o.lineY=0,o.xcoords=[],o.ycoords=[],o.tspan=this.svgFactory.createElement("svg:tspan"),o.tspan.setAttributeNS(null,"font-family",o.fontFamily),o.tspan.setAttributeNS(null,"font-size","".concat(S(o.fontSize),"px")),o.tspan.setAttributeNS(null,"y",S(-o.y)),o.txtElement=this.svgFactory.createElement("svg:text"),o.txtElement.appendChild(o.tspan)}},{key:"beginText",value:function(){var e=this.current;e.x=e.lineX=0,e.y=e.lineY=0,e.textMatrix=n.IDENTITY_MATRIX,e.lineMatrix=n.IDENTITY_MATRIX,e.textMatrixScale=1,e.tspan=this.svgFactory.createElement("svg:tspan"),e.txtElement=this.svgFactory.createElement("svg:text"),e.txtgrp=this.svgFactory.createElement("svg:g"),e.xcoords=[],e.ycoords=[]}},{key:"moveText",value:function(e,t){var r=this.current;r.x=r.lineX+=e,r.y=r.lineY+=t,r.xcoords=[],r.ycoords=[],r.tspan=this.svgFactory.createElement("svg:tspan"),r.tspan.setAttributeNS(null,"font-family",r.fontFamily),r.tspan.setAttributeNS(null,"font-size","".concat(S(r.fontSize),"px")),r.tspan.setAttributeNS(null,"y",S(-r.y))}},{key:"showText",value:function(e){var t=this.current,r=t.font,i=t.fontSize;if(0!==i){var a,o=t.fontSizeScale,s=t.charSpacing,u=t.wordSpacing,c=t.fontDirection,l=t.textHScale*c,h=r.vertical,f=h?1:-1,d=r.defaultVMetrics,v=i*t.fontMatrix[0],g=0,y=p(e);try{for(y.s();!(a=y.n()).done;){var m=a.value;if(null!==m)if((0,n.isNum)(m))g+=f*m*i/1e3;else{var b=(m.isSpace?u:0)+s,_=m.fontChar,w=void 0,P=void 0,C=m.width;if(h){var T=void 0,R=m.vmetric||d;T=m.vmetric?R[1]:.5*C,T=-T*v;var E=R[2]*v;C=R?-R[0]:C,w=T/o,P=(g+E)/o}else w=g/o,P=0;(m.isInFont||r.missingFile)&&(t.xcoords.push(t.x+w),h&&t.ycoords.push(-t.y+P),t.tspan.textContent+=_);var L=void 0;L=h?C*v-b*c:C*v+b*c,g+=L}else g+=c*u}}catch(F){y.e(F)}finally{y.f()}t.tspan.setAttributeNS(null,"x",t.xcoords.map(S).join(" ")),h?t.tspan.setAttributeNS(null,"y",t.ycoords.map(S).join(" ")):t.tspan.setAttributeNS(null,"y",S(-t.y)),h?t.y-=g:t.x+=g*l,t.tspan.setAttributeNS(null,"font-family",t.fontFamily),t.tspan.setAttributeNS(null,"font-size","".concat(S(t.fontSize),"px")),t.fontStyle!==k.fontStyle&&t.tspan.setAttributeNS(null,"font-style",t.fontStyle),t.fontWeight!==k.fontWeight&&t.tspan.setAttributeNS(null,"font-weight",t.fontWeight);var O=t.textRenderingMode&n.TextRenderingMode.FILL_STROKE_MASK;if(O===n.TextRenderingMode.FILL||O===n.TextRenderingMode.FILL_STROKE?(t.fillColor!==k.fillColor&&t.tspan.setAttributeNS(null,"fill",t.fillColor),t.fillAlpha<1&&t.tspan.setAttributeNS(null,"fill-opacity",t.fillAlpha)):t.textRenderingMode===n.TextRenderingMode.ADD_TO_PATH?t.tspan.setAttributeNS(null,"fill","transparent"):t.tspan.setAttributeNS(null,"fill","none"),O===n.TextRenderingMode.STROKE||O===n.TextRenderingMode.FILL_STROKE){var I=1/(t.textMatrixScale||1);this._setStrokeAttributes(t.tspan,I)}var M=t.textMatrix;0!==t.textRise&&(M=M.slice(),M[5]+=t.textRise),t.txtElement.setAttributeNS(null,"transform","".concat(A(M)," scale(").concat(S(l),", -1)")),t.txtElement.setAttributeNS(x,"xml:space","preserve"),t.txtElement.appendChild(t.tspan),t.txtgrp.appendChild(t.txtElement),this._ensureTransformGroup().appendChild(t.txtElement)}}},{key:"setLeadingMoveText",value:function(e,t){this.setLeading(-t),this.moveText(e,t)}},{key:"addFontStyle",value:function(e){if(!e.data)throw new Error('addFontStyle: No font data available, ensure that the "fontExtraProperties" API parameter is set.');this.cssStyle||(this.cssStyle=this.svgFactory.createElement("svg:style"),this.cssStyle.setAttributeNS(null,"type","text/css"),this.defs.appendChild(this.cssStyle));var t=(0,n.createObjectURL)(e.data,e.mimetype,this.forceDataSchema);this.cssStyle.textContent+='@font-face { font-family: "'.concat(e.loadedName,'";')+" src: url(".concat(t,"); }\n")}},{key:"setFont",value:function(e){var t=this.current,r=this.commonObjs.get(e[0]),i=e[1];t.font=r,!this.embedFonts||r.missingFile||this.embeddedFonts[r.loadedName]||(this.addFontStyle(r),this.embeddedFonts[r.loadedName]=r),t.fontMatrix=r.fontMatrix?r.fontMatrix:n.FONT_IDENTITY_MATRIX;var a="normal";r.black?a="900":r.bold&&(a="bold");var o=r.italic?"italic":"normal";i<0?(i=-i,t.fontDirection=-1):t.fontDirection=1,t.fontSize=i,t.fontFamily=r.loadedName,t.fontWeight=a,t.fontStyle=o,t.tspan=this.svgFactory.createElement("svg:tspan"),t.tspan.setAttributeNS(null,"y",S(-t.y)),t.xcoords=[],t.ycoords=[]}},{key:"endText",value:function(){var e=this.current;e.textRenderingMode&n.TextRenderingMode.ADD_TO_PATH_FLAG&&e.txtElement&&e.txtElement.hasChildNodes()&&(e.element=e.txtElement,this.clip("nonzero"),this.endPath())}},{key:"setLineWidth",value:function(e){e>0&&(this.current.lineWidth=e)}},{key:"setLineCap",value:function(e){this.current.lineCap=C[e]}},{key:"setLineJoin",value:function(e){this.current.lineJoin=T[e]}},{key:"setMiterLimit",value:function(e){this.current.miterLimit=e}},{key:"setStrokeAlpha",value:function(e){this.current.strokeAlpha=e}},{key:"setStrokeRGBColor",value:function(e,t,r){this.current.strokeColor=n.Util.makeCssRgb(e,t,r)}},{key:"setFillAlpha",value:function(e){this.current.fillAlpha=e}},{key:"setFillRGBColor",value:function(e,t,r){this.current.fillColor=n.Util.makeCssRgb(e,t,r),this.current.tspan=this.svgFactory.createElement("svg:tspan"),this.current.xcoords=[],this.current.ycoords=[]}},{key:"setStrokeColorN",value:function(e){this.current.strokeColor=this._makeColorN_Pattern(e)}},{key:"setFillColorN",value:function(e){this.current.fillColor=this._makeColorN_Pattern(e)}},{key:"shadingFill",value:function(e){var t=this.viewport.width,r=this.viewport.height,i=n.Util.inverseTransform(this.transformMatrix),a=n.Util.applyTransform([0,0],i),o=n.Util.applyTransform([0,r],i),s=n.Util.applyTransform([t,0],i),u=n.Util.applyTransform([t,r],i),c=Math.min(a[0],o[0],s[0],u[0]),l=Math.min(a[1],o[1],s[1],u[1]),h=Math.max(a[0],o[0],s[0],u[0]),f=Math.max(a[1],o[1],s[1],u[1]),d=this.svgFactory.createElement("svg:rect");d.setAttributeNS(null,"x",c),d.setAttributeNS(null,"y",l),d.setAttributeNS(null,"width",h-c),d.setAttributeNS(null,"height",f-l),d.setAttributeNS(null,"fill",this._makeShadingPattern(e)),this.current.fillAlpha<1&&d.setAttributeNS(null,"fill-opacity",this.current.fillAlpha),this._ensureTransformGroup().appendChild(d)}},{key:"_makeColorN_Pattern",value:function(e){return"TilingPattern"===e[0]?this._makeTilingPattern(e):this._makeShadingPattern(e)}},{key:"_makeTilingPattern",value:function(e){var t=e[1],r=e[2],i=e[3]||n.IDENTITY_MATRIX,a=l(e[4],4),s=a[0],u=a[1],c=a[2],h=a[3],f=e[5],d=e[6],p=e[7],v="shading".concat(I++),g=n.Util.applyTransform([s,u],i),y=l(g,2),m=y[0],b=y[1],_=n.Util.applyTransform([c,h],i),w=l(_,2),S=w[0],A=w[1],k=n.Util.singularValueDecompose2dScale(i),x=l(k,2),P=x[0],C=x[1],T=f*P,R=d*C,E=this.svgFactory.createElement("svg:pattern");E.setAttributeNS(null,"id",v),E.setAttributeNS(null,"patternUnits","userSpaceOnUse"),E.setAttributeNS(null,"width",T),E.setAttributeNS(null,"height",R),E.setAttributeNS(null,"x","".concat(m)),E.setAttributeNS(null,"y","".concat(b));var L=this.svg,O=this.transformMatrix,M=this.current.fillColor,F=this.current.strokeColor,N=this.svgFactory.create(S-m,A-b);if(this.svg=N,this.transformMatrix=i,2===p){var D=n.Util.makeCssRgb.apply(n.Util,o(t));this.current.fillColor=D,this.current.strokeColor=D}return this.executeOpTree(this.convertOpList(r)),this.svg=L,this.transformMatrix=O,this.current.fillColor=M,this.current.strokeColor=F,E.appendChild(N.childNodes[0]),this.defs.appendChild(E),"url(#".concat(v,")")}},{key:"_makeShadingPattern",value:function(e){switch(e[0]){case"RadialAxial":var t,r="shading".concat(I++),i=e[3];switch(e[1]){case"axial":var a=e[4],o=e[5];t=this.svgFactory.createElement("svg:linearGradient"),t.setAttributeNS(null,"id",r),t.setAttributeNS(null,"gradientUnits","userSpaceOnUse"),t.setAttributeNS(null,"x1",a[0]),t.setAttributeNS(null,"y1",a[1]),t.setAttributeNS(null,"x2",o[0]),t.setAttributeNS(null,"y2",o[1]);break;case"radial":var s=e[4],u=e[5],c=e[6],l=e[7];t=this.svgFactory.createElement("svg:radialGradient"),t.setAttributeNS(null,"id",r),t.setAttributeNS(null,"gradientUnits","userSpaceOnUse"),t.setAttributeNS(null,"cx",u[0]),t.setAttributeNS(null,"cy",u[1]),t.setAttributeNS(null,"r",l),t.setAttributeNS(null,"fx",s[0]),t.setAttributeNS(null,"fy",s[1]),t.setAttributeNS(null,"fr",c);break;default:throw new Error("Unknown RadialAxial type: ".concat(e[1]))}var h,f=p(i);try{for(f.s();!(h=f.n()).done;){var d=h.value,v=this.svgFactory.createElement("svg:stop");v.setAttributeNS(null,"offset",d[0]),v.setAttributeNS(null,"stop-color",d[1]),t.appendChild(v)}}catch(g){f.e(g)}finally{f.f()}return this.defs.appendChild(t),"url(#".concat(r,")");case"Mesh":return(0,n.warn)("Unimplemented pattern Mesh"),null;case"Dummy":return"hotpink";default:throw new Error("Unknown IR type: ".concat(e[0]))}}},{key:"setDash",value:function(e,t){this.current.dashArray=e,this.current.dashPhase=t}},{key:"constructPath",value:function(e,t){var r,i=this.current,a=i.x,o=i.y,s=[],u=0,c=p(e);try{for(c.s();!(r=c.n()).done;){var l=r.value;switch(0|l){case n.OPS.rectangle:a=t[u++],o=t[u++];var h=t[u++],f=t[u++],d=a+h,v=o+f;s.push("M",S(a),S(o),"L",S(d),S(o),"L",S(d),S(v),"L",S(a),S(v),"Z");break;case n.OPS.moveTo:a=t[u++],o=t[u++],s.push("M",S(a),S(o));break;case n.OPS.lineTo:a=t[u++],o=t[u++],s.push("L",S(a),S(o));break;case n.OPS.curveTo:a=t[u+4],o=t[u+5],s.push("C",S(t[u]),S(t[u+1]),S(t[u+2]),S(t[u+3]),S(a),S(o)),u+=6;break;case n.OPS.curveTo2:s.push("C",S(a),S(o),S(t[u]),S(t[u+1]),S(t[u+2]),S(t[u+3])),a=t[u+2],o=t[u+3],u+=4;break;case n.OPS.curveTo3:a=t[u+2],o=t[u+3],s.push("C",S(t[u]),S(t[u+1]),S(a),S(o),S(a),S(o)),u+=4;break;case n.OPS.closePath:s.push("Z");break}}}catch(g){c.e(g)}finally{c.f()}s=s.join(" "),i.path&&e.length>0&&e[0]!==n.OPS.rectangle&&e[0]!==n.OPS.moveTo?s=i.path.getAttributeNS(null,"d")+s:(i.path=this.svgFactory.createElement("svg:path"),this._ensureTransformGroup().appendChild(i.path)),i.path.setAttributeNS(null,"d",s),i.path.setAttributeNS(null,"fill","none"),i.element=i.path,i.setCurrentPoint(a,o)}},{key:"endPath",value:function(){var e=this.current;if(e.path=null,this.pendingClip)if(e.element){var t="clippath".concat(L++),r=this.svgFactory.createElement("svg:clipPath");r.setAttributeNS(null,"id",t),r.setAttributeNS(null,"transform",A(this.transformMatrix));var n=e.element.cloneNode(!0);"evenodd"===this.pendingClip?n.setAttributeNS(null,"clip-rule","evenodd"):n.setAttributeNS(null,"clip-rule","nonzero"),this.pendingClip=null,r.appendChild(n),this.defs.appendChild(r),e.activeClipUrl&&(e.clipGroup=null,this.extraStack.forEach((function(e){e.clipGroup=null})),r.setAttributeNS(null,"clip-path",e.activeClipUrl)),e.activeClipUrl="url(#".concat(t,")"),this.tgrp=null}else this.pendingClip=null}},{key:"clip",value:function(e){this.pendingClip=e}},{key:"closePath",value:function(){var e=this.current;if(e.path){var t="".concat(e.path.getAttributeNS(null,"d"),"Z");e.path.setAttributeNS(null,"d",t)}}},{key:"setLeading",value:function(e){this.current.leading=-e}},{key:"setTextRise",value:function(e){this.current.textRise=e}},{key:"setTextRenderingMode",value:function(e){this.current.textRenderingMode=e}},{key:"setHScale",value:function(e){this.current.textHScale=e/100}},{key:"setRenderingIntent",value:function(e){}},{key:"setFlatness",value:function(e){}},{key:"setGState",value:function(e){var t,r=p(e);try{for(r.s();!(t=r.n()).done;){var i=l(t.value,2),a=i[0],o=i[1];switch(a){case"LW":this.setLineWidth(o);break;case"LC":this.setLineCap(o);break;case"LJ":this.setLineJoin(o);break;case"ML":this.setMiterLimit(o);break;case"D":this.setDash(o[0],o[1]);break;case"RI":this.setRenderingIntent(o);break;case"FL":this.setFlatness(o);break;case"Font":this.setFont(o);break;case"CA":this.setStrokeAlpha(o);break;case"ca":this.setFillAlpha(o);break;default:(0,n.warn)("Unimplemented graphic state operator ".concat(a));break}}}catch(s){r.e(s)}finally{r.f()}}},{key:"fill",value:function(){var e=this.current;e.element&&(e.element.setAttributeNS(null,"fill",e.fillColor),e.element.setAttributeNS(null,"fill-opacity",e.fillAlpha),this.endPath())}},{key:"stroke",value:function(){var e=this.current;e.element&&(this._setStrokeAttributes(e.element),e.element.setAttributeNS(null,"fill","none"),this.endPath())}},{key:"_setStrokeAttributes",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,r=this.current,n=r.dashArray;1!==t&&n.length>0&&(n=n.map((function(e){return t*e}))),e.setAttributeNS(null,"stroke",r.strokeColor),e.setAttributeNS(null,"stroke-opacity",r.strokeAlpha),e.setAttributeNS(null,"stroke-miterlimit",S(r.miterLimit)),e.setAttributeNS(null,"stroke-linecap",r.lineCap),e.setAttributeNS(null,"stroke-linejoin",r.lineJoin),e.setAttributeNS(null,"stroke-width",S(t*r.lineWidth)+"px"),e.setAttributeNS(null,"stroke-dasharray",n.map(S).join(" ")),e.setAttributeNS(null,"stroke-dashoffset",S(t*r.dashPhase)+"px")}},{key:"eoFill",value:function(){this.current.element&&this.current.element.setAttributeNS(null,"fill-rule","evenodd"),this.fill()}},{key:"fillStroke",value:function(){this.stroke(),this.fill()}},{key:"eoFillStroke",value:function(){this.current.element&&this.current.element.setAttributeNS(null,"fill-rule","evenodd"),this.fillStroke()}},{key:"closeStroke",value:function(){this.closePath(),this.stroke()}},{key:"closeFillStroke",value:function(){this.closePath(),this.fillStroke()}},{key:"closeEOFillStroke",value:function(){this.closePath(),this.eoFillStroke()}},{key:"paintSolidColorImageMask",value:function(){var e=this.svgFactory.createElement("svg:rect");e.setAttributeNS(null,"x","0"),e.setAttributeNS(null,"y","0"),e.setAttributeNS(null,"width","1px"),e.setAttributeNS(null,"height","1px"),e.setAttributeNS(null,"fill",this.current.fillColor),this._ensureTransformGroup().appendChild(e)}},{key:"paintImageXObject",value:function(e){var t=e.startsWith("g_")?this.commonObjs.get(e):this.objs.get(e);t?this.paintInlineImageXObject(t):(0,n.warn)("Dependent image with object ID ".concat(e," is not ready yet"))}},{key:"paintInlineImageXObject",value:function(e,t){var r=e.width,n=e.height,i=R(e,this.forceDataSchema,!!t),a=this.svgFactory.createElement("svg:rect");a.setAttributeNS(null,"x","0"),a.setAttributeNS(null,"y","0"),a.setAttributeNS(null,"width",S(r)),a.setAttributeNS(null,"height",S(n)),this.current.element=a,this.clip("nonzero");var o=this.svgFactory.createElement("svg:image");o.setAttributeNS(P,"xlink:href",i),o.setAttributeNS(null,"x","0"),o.setAttributeNS(null,"y",S(-n)),o.setAttributeNS(null,"width",S(r)+"px"),o.setAttributeNS(null,"height",S(n)+"px"),o.setAttributeNS(null,"transform","scale(".concat(S(1/r)," ").concat(S(-1/n),")")),t?t.appendChild(o):this._ensureTransformGroup().appendChild(o)}},{key:"paintImageMaskXObject",value:function(e){var t=this.current,r=e.width,n=e.height,i=t.fillColor;t.maskId="mask".concat(O++);var a=this.svgFactory.createElement("svg:mask");a.setAttributeNS(null,"id",t.maskId);var o=this.svgFactory.createElement("svg:rect");o.setAttributeNS(null,"x","0"),o.setAttributeNS(null,"y","0"),o.setAttributeNS(null,"width",S(r)),o.setAttributeNS(null,"height",S(n)),o.setAttributeNS(null,"fill",i),o.setAttributeNS(null,"mask","url(#".concat(t.maskId,")")),this.defs.appendChild(a),this._ensureTransformGroup().appendChild(o),this.paintInlineImageXObject(e,a)}},{key:"paintFormXObjectBegin",value:function(e,t){if(Array.isArray(e)&&6===e.length&&this.transform(e[0],e[1],e[2],e[3],e[4],e[5]),t){var r=t[2]-t[0],n=t[3]-t[1],i=this.svgFactory.createElement("svg:rect");i.setAttributeNS(null,"x",t[0]),i.setAttributeNS(null,"y",t[1]),i.setAttributeNS(null,"width",S(r)),i.setAttributeNS(null,"height",S(n)),this.current.element=i,this.clip("nonzero"),this.endPath()}}},{key:"paintFormXObjectEnd",value:function(){}},{key:"_initialize",value:function(e){var t=this.svgFactory.create(e.width,e.height),r=this.svgFactory.createElement("svg:defs");t.appendChild(r),this.defs=r;var n=this.svgFactory.createElement("svg:g");return n.setAttributeNS(null,"transform",A(e.transform)),t.appendChild(n),this.svg=n,t}},{key:"_ensureClipGroup",value:function(){if(!this.current.clipGroup){var e=this.svgFactory.createElement("svg:g");e.setAttributeNS(null,"clip-path",this.current.activeClipUrl),this.svg.appendChild(e),this.current.clipGroup=e}return this.current.clipGroup}},{key:"_ensureTransformGroup",value:function(){return this.tgrp||(this.tgrp=this.svgFactory.createElement("svg:g"),this.tgrp.setAttributeNS(null,"transform",A(this.transformMatrix)),this.current.activeClipUrl?this._ensureClipGroup().appendChild(this.tgrp):this.svg.appendChild(this.tgrp)),this.tgrp}}]),e}()},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PDFNodeStream=void 0;var n=o(r(2)),i=r(5),a=r(220);function o(e){return e&&e.__esModule?e:{default:e}}function s(e){return s="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}function u(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&c(e,t)}function c(e,t){return c=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},c(e,t)}function l(e){var t=d();return function(){var r,n=p(e);if(t){var i=p(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return h(this,r)}}function h(e,t){return!t||"object"!==s(t)&&"function"!==typeof t?f(e):t}function f(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function d(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function p(e){return p=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},p(e)}function v(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(c){return void r(c)}s.done?t(u):Promise.resolve(u).then(n,i)}function g(e){return function(){var t=this,r=arguments;return new Promise((function(n,i){var a=e.apply(t,r);function o(e){v(a,n,i,o,s,"next",e)}function s(e){v(a,n,i,o,s,"throw",e)}o(void 0)}))}}function y(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function m(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function b(e,t,r){return t&&m(e.prototype,t),r&&m(e,r),e}var _=__webpack_require__(8),w=__webpack_require__(11),S=__webpack_require__(12),A=__webpack_require__(13),k=/^file:\/\/\/[a-zA-Z]:\//;function x(e){var t=A.parse(e);return"file:"===t.protocol||t.host?t:/^[a-z]:[/\\]/i.test(e)?A.parse("file:///".concat(e)):(t.host||(t.protocol="file:"),t)}var P=function(){function e(t){y(this,e),this.source=t,this.url=x(t.url),this.isHttp="http:"===this.url.protocol||"https:"===this.url.protocol,this.isFsUrl="file:"===this.url.protocol,this.httpHeaders=this.isHttp&&t.httpHeaders||{},this._fullRequestReader=null,this._rangeRequestReaders=[]}return b(e,[{key:"getFullReader",value:function(){return(0,i.assert)(!this._fullRequestReader,"PDFNodeStream.getFullReader can only be called once."),this._fullRequestReader=this.isFsUrl?new O(this):new E(this),this._fullRequestReader}},{key:"getRangeReader",value:function(e,t){if(t<=this._progressiveDataLength)return null;var r=this.isFsUrl?new I(this,e,t):new L(this,e,t);return this._rangeRequestReaders.push(r),r}},{key:"cancelAllRequests",value:function(e){this._fullRequestReader&&this._fullRequestReader.cancel(e);var t=this._rangeRequestReaders.slice(0);t.forEach((function(t){t.cancel(e)}))}},{key:"_progressiveDataLength",get:function(){return this._fullRequestReader?this._fullRequestReader._loaded:0}}]),e}();t.PDFNodeStream=P;var C=function(){function e(t){y(this,e),this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null;var r=t.source;this._contentLength=r.length,this._loaded=0,this._filename=null,this._disableRange=r.disableRange||!1,this._rangeChunkSize=r.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),this._isStreamingSupported=!r.disableStream,this._isRangeSupported=!r.disableRange,this._readableStream=null,this._readCapability=(0,i.createPromiseCapability)(),this._headersCapability=(0,i.createPromiseCapability)()}return b(e,[{key:"read",value:function(){var e=g(n["default"].mark((function e(){var t,r;return n["default"].wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._readCapability.promise;case 2:if(!this._done){e.next=4;break}return e.abrupt("return",{value:void 0,done:!0});case 4:if(!this._storedError){e.next=6;break}throw this._storedError;case 6:if(t=this._readableStream.read(),null!==t){e.next=10;break}return this._readCapability=(0,i.createPromiseCapability)(),e.abrupt("return",this.read());case 10:return this._loaded+=t.length,this.onProgress&&this.onProgress({loaded:this._loaded,total:this._contentLength}),r=new Uint8Array(t).buffer,e.abrupt("return",{value:r,done:!1});case 14:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"cancel",value:function(e){this._readableStream?this._readableStream.destroy(e):this._error(e)}},{key:"_error",value:function(e){this._storedError=e,this._readCapability.resolve()}},{key:"_setReadableStream",value:function(e){var t=this;this._readableStream=e,e.on("readable",(function(){t._readCapability.resolve()})),e.on("end",(function(){e.destroy(),t._done=!0,t._readCapability.resolve()})),e.on("error",(function(e){t._error(e)})),!this._isStreamingSupported&&this._isRangeSupported&&this._error(new i.AbortException("streaming is disabled")),this._storedError&&this._readableStream.destroy(this._storedError)}},{key:"headersReady",get:function(){return this._headersCapability.promise}},{key:"filename",get:function(){return this._filename}},{key:"contentLength",get:function(){return this._contentLength}},{key:"isRangeSupported",get:function(){return this._isRangeSupported}},{key:"isStreamingSupported",get:function(){return this._isStreamingSupported}}]),e}(),T=function(){function e(t){y(this,e),this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null,this._loaded=0,this._readableStream=null,this._readCapability=(0,i.createPromiseCapability)();var r=t.source;this._isStreamingSupported=!r.disableStream}return b(e,[{key:"read",value:function(){var e=g(n["default"].mark((function e(){var t,r;return n["default"].wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._readCapability.promise;case 2:if(!this._done){e.next=4;break}return e.abrupt("return",{value:void 0,done:!0});case 4:if(!this._storedError){e.next=6;break}throw this._storedError;case 6:if(t=this._readableStream.read(),null!==t){e.next=10;break}return this._readCapability=(0,i.createPromiseCapability)(),e.abrupt("return",this.read());case 10:return this._loaded+=t.length,this.onProgress&&this.onProgress({loaded:this._loaded}),r=new Uint8Array(t).buffer,e.abrupt("return",{value:r,done:!1});case 14:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"cancel",value:function(e){this._readableStream?this._readableStream.destroy(e):this._error(e)}},{key:"_error",value:function(e){this._storedError=e,this._readCapability.resolve()}},{key:"_setReadableStream",value:function(e){var t=this;this._readableStream=e,e.on("readable",(function(){t._readCapability.resolve()})),e.on("end",(function(){e.destroy(),t._done=!0,t._readCapability.resolve()})),e.on("error",(function(e){t._error(e)})),this._storedError&&this._readableStream.destroy(this._storedError)}},{key:"isStreamingSupported",get:function(){return this._isStreamingSupported}}]),e}();function R(e,t){return{protocol:e.protocol,auth:e.auth,host:e.hostname,port:e.port,path:e.path,method:"GET",headers:t}}var E=function(e){u(r,e);var t=l(r);function r(e){var n;y(this,r),n=t.call(this,e);var o=function(t){if(404===t.statusCode){var r=new i.MissingPDFException('Missing PDF "'.concat(n._url,'".'));return n._storedError=r,void n._headersCapability.reject(r)}n._headersCapability.resolve(),n._setReadableStream(t);var o=function(e){return n._readableStream.headers[e.toLowerCase()]},s=(0,a.validateRangeRequestCapabilities)({getResponseHeader:o,isHttp:e.isHttp,rangeChunkSize:n._rangeChunkSize,disableRange:n._disableRange}),u=s.allowRangeRequests,c=s.suggestedLength;n._isRangeSupported=u,n._contentLength=c||n._contentLength,n._filename=(0,a.extractFilenameFromHeader)(o)};return n._request=null,"http:"===n._url.protocol?n._request=w.request(R(n._url,e.httpHeaders),o):n._request=S.request(R(n._url,e.httpHeaders),o),n._request.on("error",(function(e){n._storedError=e,n._headersCapability.reject(e)})),n._request.end(),n}return r}(C),L=function(e){u(r,e);var t=l(r);function r(e,n,a){var o;for(var s in y(this,r),o=t.call(this,e),o._httpHeaders={},e.httpHeaders){var u=e.httpHeaders[s];"undefined"!==typeof u&&(o._httpHeaders[s]=u)}o._httpHeaders.Range="bytes=".concat(n,"-").concat(a-1);var c=function(e){if(404!==e.statusCode)o._setReadableStream(e);else{var t=new i.MissingPDFException('Missing PDF "'.concat(o._url,'".'));o._storedError=t}};return o._request=null,"http:"===o._url.protocol?o._request=w.request(R(o._url,o._httpHeaders),c):o._request=S.request(R(o._url,o._httpHeaders),c),o._request.on("error",(function(e){o._storedError=e})),o._request.end(),o}return r}(T),O=function(e){u(r,e);var t=l(r);function r(e){var n;y(this,r),n=t.call(this,e);var a=decodeURIComponent(n._url.path);return k.test(n._url.href)&&(a=a.replace(/^\//,"")),_.lstat(a,(function(e,t){if(e)return"ENOENT"===e.code&&(e=new i.MissingPDFException('Missing PDF "'.concat(a,'".'))),n._storedError=e,void n._headersCapability.reject(e);n._contentLength=t.size,n._setReadableStream(_.createReadStream(a)),n._headersCapability.resolve()})),n}return r}(C),I=function(e){u(r,e);var t=l(r);function r(e,n,i){var a;y(this,r),a=t.call(this,e);var o=decodeURIComponent(a._url.path);return k.test(a._url.href)&&(o=o.replace(/^\//,"")),a._setReadableStream(_.createReadStream(o,{start:n,end:i-1})),a}return r}(T)},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createResponseStatusError=s,t.extractFilenameFromHeader=o,t.validateRangeRequestCapabilities=a,t.validateResponseStatus=u;var n=r(5),i=r(221);function a(e){var t=e.getResponseHeader,r=e.isHttp,i=e.rangeChunkSize,a=e.disableRange;(0,n.assert)(i>0,"Range chunk size must be larger than zero");var o={allowRangeRequests:!1,suggestedLength:void 0},s=parseInt(t("Content-Length"),10);if(!Number.isInteger(s))return o;if(o.suggestedLength=s,s<=2*i)return o;if(a||!r)return o;if("bytes"!==t("Accept-Ranges"))return o;var u=t("Content-Encoding")||"identity";return"identity"!==u||(o.allowRangeRequests=!0),o}function o(e){var t=e("Content-Disposition");if(t){var r=(0,i.getFilenameFromContentDispositionHeader)(t);if(r.includes("%"))try{r=decodeURIComponent(r)}catch(n){}if(/\.pdf$/i.test(r))return r}return null}function s(e,t){return 404===e||0===e&&t.startsWith("file:")?new n.MissingPDFException('Missing PDF "'+t+'".'):new n.UnexpectedResponseException("Unexpected server response ("+e+') while retrieving PDF "'+t+'".',e)}function u(e){return 200===e||206===e}},function(e,t,r){"use strict";function n(e,t){return u(e)||s(e,t)||a(e,t)||i()}function i(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function a(e,t){if(e){if("string"===typeof e)return o(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?o(e,t):void 0}}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function s(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var r=[],n=!0,i=!1,a=void 0;try{for(var o,s=e[Symbol.iterator]();!(n=(o=s.next()).done);n=!0)if(r.push(o.value),t&&r.length===t)break}catch(u){i=!0,a=u}finally{try{n||null==s["return"]||s["return"]()}finally{if(i)throw a}}return r}}function u(e){if(Array.isArray(e))return e}function c(e){var t=!0,r=s("filename\\*","i").exec(e);if(r){r=r[1];var i=h(r);return i=unescape(i),i=f(i),i=d(i),c(i)}if(r=l(e),r){var a=d(r);return c(a)}if(r=s("filename","i").exec(e),r){r=r[1];var o=h(r);return o=d(o),c(o)}function s(e,t){return new RegExp("(?:^|;)\\s*"+e+'\\s*=\\s*([^";\\s][^;\\s]*|"(?:[^"\\\\]|\\\\"?)+"?)',t)}function u(e,r){if(e){if(!/^[\x00-\xFF]+$/.test(r))return r;try{var n=new TextDecoder(e,{fatal:!0}),i=Array.from(r,(function(e){return 255&e.charCodeAt(0)}));r=n.decode(new Uint8Array(i)),t=!1}catch(a){if(/^utf-?8$/i.test(e))try{r=decodeURIComponent(escape(r)),t=!1}catch(o){}}}return r}function c(e){return t&&/[\x80-\xff]/.test(e)&&(e=u("utf-8",e),t&&(e=u("iso-8859-1",e))),e}function l(e){var t,r=[],i=s("filename\\*((?!0\\d)\\d+)(\\*?)","ig");while(null!==(t=i.exec(e))){var a=t,o=n(a,4),u=o[1],c=o[2],l=o[3];if(u=parseInt(u,10),u in r){if(0===u)break}else r[u]=[c,l]}for(var d=[],p=0;p<r.length;++p){if(!(p in r))break;var v=n(r[p],2),g=v[0],y=v[1];y=h(y),g&&(y=unescape(y),0===p&&(y=f(y))),d.push(y)}return d.join("")}function h(e){if(e.startsWith('"')){for(var t=e.slice(1).split('\\"'),r=0;r<t.length;++r){var n=t[r].indexOf('"');-1!==n&&(t[r]=t[r].slice(0,n),t.length=r+1),t[r]=t[r].replace(/\\(.)/g,"$1")}e=t.join('"')}return e}function f(e){var t=e.indexOf("'");if(-1===t)return e;var r=e.slice(0,t),n=e.slice(t+1),i=n.replace(/^[^']*'/,"");return u(r,i)}function d(e){return!e.startsWith("=?")||/[\x00-\x19\x80-\xff]/.test(e)?e:e.replace(/=\?([\w-]*)\?([QqBb])\?((?:[^?]|\?(?!=))*)\?=/g,(function(e,t,r,n){if("q"===r||"Q"===r)return n=n.replace(/_/g," "),n=n.replace(/=([0-9a-fA-F]{2})/g,(function(e,t){return String.fromCharCode(parseInt(t,16))})),u(t,n);try{n=atob(n)}catch(i){}return u(t,n)}))}return""}Object.defineProperty(t,"__esModule",{value:!0}),t.getFilenameFromContentDispositionHeader=c},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PDFNetworkStream=void 0;var n=o(r(2)),i=r(5),a=r(220);function o(e){return e&&e.__esModule?e:{default:e}}function s(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(c){return void r(c)}s.done?t(u):Promise.resolve(u).then(n,i)}function u(e){return function(){var t=this,r=arguments;return new Promise((function(n,i){var a=e.apply(t,r);function o(e){s(a,n,i,o,u,"next",e)}function u(e){s(a,n,i,o,u,"throw",e)}o(void 0)}))}}function c(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function l(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function h(e,t,r){return t&&l(e.prototype,t),r&&l(e,r),e}var f=200,d=206;function p(e){var t=e.response;if("string"!==typeof t)return t;var r=(0,i.stringToBytes)(t);return r.buffer}var v=function(){function e(t,r){c(this,e),this.url=t,r=r||{},this.isHttp=/^https?:/i.test(t),this.httpHeaders=this.isHttp&&r.httpHeaders||{},this.withCredentials=r.withCredentials||!1,this.getXhr=r.getXhr||function(){return new XMLHttpRequest},this.currXhrId=0,this.pendingRequests=Object.create(null)}return h(e,[{key:"requestRange",value:function(e,t,r){var n={begin:e,end:t};for(var i in r)n[i]=r[i];return this.request(n)}},{key:"requestFull",value:function(e){return this.request(e)}},{key:"request",value:function(e){var t=this.getXhr(),r=this.currXhrId++,n=this.pendingRequests[r]={xhr:t};for(var i in t.open("GET",this.url),t.withCredentials=this.withCredentials,this.httpHeaders){var a=this.httpHeaders[i];"undefined"!==typeof a&&t.setRequestHeader(i,a)}return this.isHttp&&"begin"in e&&"end"in e?(t.setRequestHeader("Range","bytes=".concat(e.begin,"-").concat(e.end-1)),n.expectedStatus=d):n.expectedStatus=f,t.responseType="arraybuffer",e.onError&&(t.onerror=function(r){e.onError(t.status)}),t.onreadystatechange=this.onStateChange.bind(this,r),t.onprogress=this.onProgress.bind(this,r),n.onHeadersReceived=e.onHeadersReceived,n.onDone=e.onDone,n.onError=e.onError,n.onProgress=e.onProgress,t.send(null),r}},{key:"onProgress",value:function(e,t){var r=this.pendingRequests[e];r&&r.onProgress&&r.onProgress(t)}},{key:"onStateChange",value:function(e,t){var r=this.pendingRequests[e];if(r){var n=r.xhr;if(n.readyState>=2&&r.onHeadersReceived&&(r.onHeadersReceived(),delete r.onHeadersReceived),4===n.readyState&&e in this.pendingRequests)if(delete this.pendingRequests[e],0===n.status&&this.isHttp)r.onError&&r.onError(n.status);else{var i=n.status||f,a=i===f&&r.expectedStatus===d;if(a||i===r.expectedStatus){var o=p(n);if(i===d){var s=n.getResponseHeader("Content-Range"),u=/bytes (\d+)-(\d+)\/(\d+)/.exec(s);r.onDone({begin:parseInt(u[1],10),chunk:o})}else o?r.onDone({begin:0,chunk:o}):r.onError&&r.onError(n.status)}else r.onError&&r.onError(n.status)}}}},{key:"getRequestXhr",value:function(e){return this.pendingRequests[e].xhr}},{key:"isPendingRequest",value:function(e){return e in this.pendingRequests}},{key:"abortRequest",value:function(e){var t=this.pendingRequests[e].xhr;delete this.pendingRequests[e],t.abort()}}]),e}(),g=function(){function e(t){c(this,e),this._source=t,this._manager=new v(t.url,{httpHeaders:t.httpHeaders,withCredentials:t.withCredentials}),this._rangeChunkSize=t.rangeChunkSize,this._fullRequestReader=null,this._rangeRequestReaders=[]}return h(e,[{key:"_onRangeRequestReaderClosed",value:function(e){var t=this._rangeRequestReaders.indexOf(e);t>=0&&this._rangeRequestReaders.splice(t,1)}},{key:"getFullReader",value:function(){return(0,i.assert)(!this._fullRequestReader,"PDFNetworkStream.getFullReader can only be called once."),this._fullRequestReader=new y(this._manager,this._source),this._fullRequestReader}},{key:"getRangeReader",value:function(e,t){var r=new m(this._manager,e,t);return r.onClosed=this._onRangeRequestReaderClosed.bind(this),this._rangeRequestReaders.push(r),r}},{key:"cancelAllRequests",value:function(e){this._fullRequestReader&&this._fullRequestReader.cancel(e);var t=this._rangeRequestReaders.slice(0);t.forEach((function(t){t.cancel(e)}))}}]),e}();t.PDFNetworkStream=g;var y=function(){function e(t,r){c(this,e),this._manager=t;var n={onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=r.url,this._fullRequestId=t.requestFull(n),this._headersReceivedCapability=(0,i.createPromiseCapability)(),this._disableRange=r.disableRange||!1,this._contentLength=r.length,this._rangeChunkSize=r.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),this._isStreamingSupported=!1,this._isRangeSupported=!1,this._cachedChunks=[],this._requests=[],this._done=!1,this._storedError=void 0,this._filename=null,this.onProgress=null}return h(e,[{key:"_onHeadersReceived",value:function(){var e=this._fullRequestId,t=this._manager.getRequestXhr(e),r=function(e){return t.getResponseHeader(e)},n=(0,a.validateRangeRequestCapabilities)({getResponseHeader:r,isHttp:this._manager.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange}),i=n.allowRangeRequests,o=n.suggestedLength;i&&(this._isRangeSupported=!0),this._contentLength=o||this._contentLength,this._filename=(0,a.extractFilenameFromHeader)(r),this._isRangeSupported&&this._manager.abortRequest(e),this._headersReceivedCapability.resolve()}},{key:"_onDone",value:function(e){if(e)if(this._requests.length>0){var t=this._requests.shift();t.resolve({value:e.chunk,done:!1})}else this._cachedChunks.push(e.chunk);this._done=!0,this._cachedChunks.length>0||(this._requests.forEach((function(e){e.resolve({value:void 0,done:!0})})),this._requests=[])}},{key:"_onError",value:function(e){var t=this._url,r=(0,a.createResponseStatusError)(e,t);this._storedError=r,this._headersReceivedCapability.reject(r),this._requests.forEach((function(e){e.reject(r)})),this._requests=[],this._cachedChunks=[]}},{key:"_onProgress",value:function(e){this.onProgress&&this.onProgress({loaded:e.loaded,total:e.lengthComputable?e.total:this._contentLength})}},{key:"read",value:function(){var e=u(n["default"].mark((function e(){var t,r;return n["default"].wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!this._storedError){e.next=2;break}throw this._storedError;case 2:if(!(this._cachedChunks.length>0)){e.next=5;break}return t=this._cachedChunks.shift(),e.abrupt("return",{value:t,done:!1});case 5:if(!this._done){e.next=7;break}return e.abrupt("return",{value:void 0,done:!0});case 7:return r=(0,i.createPromiseCapability)(),this._requests.push(r),e.abrupt("return",r.promise);case 10:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"cancel",value:function(e){this._done=!0,this._headersReceivedCapability.reject(e),this._requests.forEach((function(e){e.resolve({value:void 0,done:!0})})),this._requests=[],this._manager.isPendingRequest(this._fullRequestId)&&this._manager.abortRequest(this._fullRequestId),this._fullRequestReader=null}},{key:"filename",get:function(){return this._filename}},{key:"isRangeSupported",get:function(){return this._isRangeSupported}},{key:"isStreamingSupported",get:function(){return this._isStreamingSupported}},{key:"contentLength",get:function(){return this._contentLength}},{key:"headersReady",get:function(){return this._headersReceivedCapability.promise}}]),e}(),m=function(){function e(t,r,n){c(this,e),this._manager=t;var i={onDone:this._onDone.bind(this),onProgress:this._onProgress.bind(this)};this._requestId=t.requestRange(r,n,i),this._requests=[],this._queuedChunk=null,this._done=!1,this.onProgress=null,this.onClosed=null}return h(e,[{key:"_close",value:function(){this.onClosed&&this.onClosed(this)}},{key:"_onDone",value:function(e){var t=e.chunk;if(this._requests.length>0){var r=this._requests.shift();r.resolve({value:t,done:!1})}else this._queuedChunk=t;this._done=!0,this._requests.forEach((function(e){e.resolve({value:void 0,done:!0})})),this._requests=[],this._close()}},{key:"_onProgress",value:function(e){!this.isStreamingSupported&&this.onProgress&&this.onProgress({loaded:e.loaded})}},{key:"read",value:function(){var e=u(n["default"].mark((function e(){var t,r;return n["default"].wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(null===this._queuedChunk){e.next=4;break}return t=this._queuedChunk,this._queuedChunk=null,e.abrupt("return",{value:t,done:!1});case 4:if(!this._done){e.next=6;break}return e.abrupt("return",{value:void 0,done:!0});case 6:return r=(0,i.createPromiseCapability)(),this._requests.push(r),e.abrupt("return",r.promise);case 9:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"cancel",value:function(e){this._done=!0,this._requests.forEach((function(e){e.resolve({value:void 0,done:!0})})),this._requests=[],this._manager.isPendingRequest(this._requestId)&&this._manager.abortRequest(this._requestId),this._close()}},{key:"isStreamingSupported",get:function(){return!1}}]),e}()},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PDFFetchStream=void 0;var n=o(r(2)),i=r(5),a=r(220);function o(e){return e&&e.__esModule?e:{default:e}}function s(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(c){return void r(c)}s.done?t(u):Promise.resolve(u).then(n,i)}function u(e){return function(){var t=this,r=arguments;return new Promise((function(n,i){var a=e.apply(t,r);function o(e){s(a,n,i,o,u,"next",e)}function u(e){s(a,n,i,o,u,"throw",e)}o(void 0)}))}}function c(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function l(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function h(e,t,r){return t&&l(e.prototype,t),r&&l(e,r),e}function f(e,t,r){return{method:"GET",headers:e,signal:r&&r.signal,mode:"cors",credentials:t?"include":"same-origin",redirect:"follow"}}function d(e){var t=new Headers;for(var r in e){var n=e[r];"undefined"!==typeof n&&t.append(r,n)}return t}var p=function(){function e(t){c(this,e),this.source=t,this.isHttp=/^https?:/i.test(t.url),this.httpHeaders=this.isHttp&&t.httpHeaders||{},this._fullRequestReader=null,this._rangeRequestReaders=[]}return h(e,[{key:"getFullReader",value:function(){return(0,i.assert)(!this._fullRequestReader,"PDFFetchStream.getFullReader can only be called once."),this._fullRequestReader=new v(this),this._fullRequestReader}},{key:"getRangeReader",value:function(e,t){if(t<=this._progressiveDataLength)return null;var r=new g(this,e,t);return this._rangeRequestReaders.push(r),r}},{key:"cancelAllRequests",value:function(e){this._fullRequestReader&&this._fullRequestReader.cancel(e);var t=this._rangeRequestReaders.slice(0);t.forEach((function(t){t.cancel(e)}))}},{key:"_progressiveDataLength",get:function(){return this._fullRequestReader?this._fullRequestReader._loaded:0}}]),e}();t.PDFFetchStream=p;var v=function(){function e(t){var r=this;c(this,e),this._stream=t,this._reader=null,this._loaded=0,this._filename=null;var n=t.source;this._withCredentials=n.withCredentials||!1,this._contentLength=n.length,this._headersCapability=(0,i.createPromiseCapability)(),this._disableRange=n.disableRange||!1,this._rangeChunkSize=n.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),"undefined"!==typeof AbortController&&(this._abortController=new AbortController),this._isStreamingSupported=!n.disableStream,this._isRangeSupported=!n.disableRange,this._headers=d(this._stream.httpHeaders);var o=n.url;fetch(o,f(this._headers,this._withCredentials,this._abortController)).then((function(e){if(!(0,a.validateResponseStatus)(e.status))throw(0,a.createResponseStatusError)(e.status,o);r._reader=e.body.getReader(),r._headersCapability.resolve();var t=function(t){return e.headers.get(t)},n=(0,a.validateRangeRequestCapabilities)({getResponseHeader:t,isHttp:r._stream.isHttp,rangeChunkSize:r._rangeChunkSize,disableRange:r._disableRange}),s=n.allowRangeRequests,u=n.suggestedLength;r._isRangeSupported=s,r._contentLength=u||r._contentLength,r._filename=(0,a.extractFilenameFromHeader)(t),!r._isStreamingSupported&&r._isRangeSupported&&r.cancel(new i.AbortException("Streaming is disabled."))}))["catch"](this._headersCapability.reject),this.onProgress=null}return h(e,[{key:"read",value:function(){var e=u(n["default"].mark((function e(){var t,r,i,a;return n["default"].wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._headersCapability.promise;case 2:return e.next=4,this._reader.read();case 4:if(t=e.sent,r=t.value,i=t.done,!i){e.next=9;break}return e.abrupt("return",{value:r,done:i});case 9:return this._loaded+=r.byteLength,this.onProgress&&this.onProgress({loaded:this._loaded,total:this._contentLength}),a=new Uint8Array(r).buffer,e.abrupt("return",{value:a,done:!1});case 13:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"cancel",value:function(e){this._reader&&this._reader.cancel(e),this._abortController&&this._abortController.abort()}},{key:"headersReady",get:function(){return this._headersCapability.promise}},{key:"filename",get:function(){return this._filename}},{key:"contentLength",get:function(){return this._contentLength}},{key:"isRangeSupported",get:function(){return this._isRangeSupported}},{key:"isStreamingSupported",get:function(){return this._isStreamingSupported}}]),e}(),g=function(){function e(t,r,n){var o=this;c(this,e),this._stream=t,this._reader=null,this._loaded=0;var s=t.source;this._withCredentials=s.withCredentials||!1,this._readCapability=(0,i.createPromiseCapability)(),this._isStreamingSupported=!s.disableStream,"undefined"!==typeof AbortController&&(this._abortController=new AbortController),this._headers=d(this._stream.httpHeaders),this._headers.append("Range","bytes=".concat(r,"-").concat(n-1));var u=s.url;fetch(u,f(this._headers,this._withCredentials,this._abortController)).then((function(e){if(!(0,a.validateResponseStatus)(e.status))throw(0,a.createResponseStatusError)(e.status,u);o._readCapability.resolve(),o._reader=e.body.getReader()}))["catch"]((function(e){if(!e||"AbortError"!==e.name)throw e})),this.onProgress=null}return h(e,[{key:"read",value:function(){var e=u(n["default"].mark((function e(){var t,r,i,a;return n["default"].wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._readCapability.promise;case 2:return e.next=4,this._reader.read();case 4:if(t=e.sent,r=t.value,i=t.done,!i){e.next=9;break}return e.abrupt("return",{value:r,done:i});case 9:return this._loaded+=r.byteLength,this.onProgress&&this.onProgress({loaded:this._loaded}),a=new Uint8Array(r).buffer,e.abrupt("return",{value:a,done:!1});case 13:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"cancel",value:function(e){this._reader&&this._reader.cancel(e),this._abortController&&this._abortController.abort()}},{key:"isStreamingSupported",get:function(){return this._isStreamingSupported}}]),e}()}])}))}).call(this,__webpack_require__("b639").Buffer,__webpack_require__("4362"),__webpack_require__("c8ba"))},"4d63":function(e,t,r){var n=r("83ab"),i=r("da84"),a=r("94ca"),o=r("7156"),s=r("9bf2").f,u=r("241c").f,c=r("44e7"),l=r("ad6d"),h=r("9f7f"),f=r("6eeb"),d=r("d039"),p=r("69f3").set,v=r("2626"),g=r("b622"),y=g("match"),m=i.RegExp,b=m.prototype,_=/a/g,w=/a/g,S=new m(_)!==_,A=h.UNSUPPORTED_Y,k=n&&a("RegExp",!S||A||d((function(){return w[y]=!1,m(_)!=_||m(w)==w||"/a/i"!=m(_,"i")})));if(k){var x=function(e,t){var r,n=this instanceof x,i=c(e),a=void 0===t;if(!n&&i&&e.constructor===x&&a)return e;S?i&&!a&&(e=e.source):e instanceof x&&(a&&(t=l.call(e)),e=e.source),A&&(r=!!t&&t.indexOf("y")>-1,r&&(t=t.replace(/y/g,"")));var s=o(S?new m(e,t):m(e,t),n?this:b,x);return A&&r&&p(s,{sticky:r}),s},P=function(e){e in x||s(x,e,{configurable:!0,get:function(){return m[e]},set:function(t){m[e]=t}})},C=u(m),T=0;while(C.length>T)P(C[T++]);b.constructor=x,x.prototype=b,f(i,"RegExp",x)}v("RegExp")},"6f4a":function(e,t,r){"use strict";r("b126")},7866:function(e,t,r){"use strict";r("2eda")},"858e":function(e,t,r){"use strict";var n,i,a={props:{initial:{type:Boolean,default:!1}},data:function(){return{size:{width:-1,height:-1}}},methods:{reset:function(){var e=this.$el.firstChild,t=this.$el.lastChild;e.scrollLeft=1e5,e.scrollTop=1e5,t.scrollLeft=1e5,t.scrollTop=1e5},update:function(){this.size.width=this.$el.offsetWidth,this.size.height=this.$el.offsetHeight}},watch:{size:{deep:!0,handler:function(e){this.reset(),this.$emit("resize",{width:this.size.width,height:this.size.height})}}},render:function(e){var t="position: absolute; left: 0; top: 0; right: 0; bottom: 0; overflow: hidden; z-index: -1; visibility: hidden;",r="position: absolute; left: 0; top: 0;";return e("div",{style:t+"animation-name: resizeSensorVisibility;",on:{"~animationstart":this.update}},[e("div",{style:t,on:{scroll:this.update}},[e("div",{style:r+"width: 100000px; height: 100000px;"})]),e("div",{style:t,on:{scroll:this.update}},[e("div",{style:r+"width: 200%; height: 200%;"})])])},beforeDestroy:function(){this.$emit("resize",{width:0,height:0}),this.$emit("resizeSensorBeforeDestroy")},mounted:function(){if(!0===this.initial&&this.$nextTick(this.update),this.$el.offsetParent!==this.$el.parentNode&&(this.$el.parentNode.style.position="relative"),"attachEvent"in this.$el&&!("AnimationEvent"in window)){var e=function(){this.update(),t()}.bind(this),t=function(){this.$el.detachEvent("onresize",e),this.$off("resizeSensorBeforeDestroy",t)}.bind(this);this.$el.attachEvent("onresize",e),this.$on("resizeSensorBeforeDestroy",t),this.reset()}}},o=a,s=(r("7866"),r("2877")),u=Object(s["a"])(o,n,i,!1,null,null,null),c=u.exports,l=function(e){var t=e.createLoadingTask,r=e.PDFJSWrapper;return{createLoadingTask:t,render:function(e){return e("span",{attrs:{style:"position: relative; display: block"}},[e("canvas",{attrs:{style:"display: inline-block; width: 100%; height: 100%; vertical-align: top"},ref:"canvas"}),e("span",{style:"display: inline-block; width: 100%; height: 100%",class:"annotationLayer",ref:"annotationLayer"}),e(c,{props:{initial:!0},on:{resize:this.resize}})])},props:{src:{type:[String,Object,Uint8Array],default:""},page:{type:Number,default:1},rotate:{type:Number}},watch:{src:function(){this.pdf.loadDocument(this.src)},page:function(){this.pdf.loadPage(this.page,this.rotate)},rotate:function(){this.pdf.renderPage(this.rotate)}},methods:{resize:function(e){if(null!==this.$el.parentNode&&(0!==e.width||0!==e.height)){this.$refs.canvas.style.height=this.$refs.canvas.offsetWidth*(this.$refs.canvas.height/this.$refs.canvas.width)+"px";var t=this.pdf.getResolutionScale();(t<.85||t>1.15)&&this.pdf.renderPage(this.rotate)}},print:function(e,t){this.pdf.printPage(e,t)}},mounted:function(){this.pdf=new r(this.$refs.canvas,this.$refs.annotationLayer,this.$emit.bind(this)),this.$on("loaded",(function(){this.pdf.loadPage(this.page,this.rotate)})),this.$on("page-size",(function(e,t){this.$refs.canvas.style.height=this.$refs.canvas.offsetWidth*(t/e)+"px"})),this.pdf.loadDocument(this.src)},destroyed:function(){this.pdf.destroy()}}};if("server"!==Object({NODE_ENV:"production",VUE_APP_BASE_API:"/prod-api",BASE_URL:"/"}).VUE_ENV){var h=r("a068").default,f=r("4383");if("undefined"!==typeof window&&"Worker"in window&&-1===navigator.appVersion.indexOf("MSIE 10")){var d=r("2639");f.GlobalWorkerOptions.workerPort=new d}var p=l(h(f))}else p=l({});var v,g,y=p,m=y,b=(r("6f4a"),Object(s["a"])(m,v,g,!1,null,null,null));t["a"]=b.exports},a068:function(e,t,r){"use strict";r.r(t);var n=r("f8db"),i=Promise.resolve();t["default"]=function(e){function t(e){return"object"===typeof e&&null!==e&&!0===e.__PDFDocumentLoadingTask}function r(t,r){var n;if("string"===typeof t)n={url:t};else if(t instanceof Uint8Array)n={data:t};else{if("object"!==typeof t||null===t)throw new TypeError("invalid src type");n=Object.assign({},t)}r&&r.withCredentials&&(n.withCredentials=r.withCredentials);var i=e.getDocument(n);return i.__PDFDocumentLoadingTask=!0,r&&r.onPassword&&(i.onPassword=r.onPassword),r&&r.onProgress&&(i.onProgress=r.onProgress),i}function a(a,o,s){var u=null,c=null,l=null,h=!1;function f(){a.getContext("2d").clearRect(0,0,a.width,a.height)}function d(){while(o.firstChild)o.removeChild(o.firstChild)}a.getContext("2d").save(),this.destroy=function(){null!==u&&(i=u.destroy(),u=null)},this.getResolutionScale=function(){return a.offsetWidth/a.width},this.printPage=function(e,t){if(null!==c){var r=void 0===e?150:e,n=r/72,i=96/72,a=document.createElement("iframe");new Promise((function(e,t){a.frameBorder="0",a.scrolling="no",a.width="0px;",a.height="0px;",a.style.cssText="position: absolute; top: 0; left: 0",a.onload=function(){e(this.contentWindow)},window.document.body.appendChild(a)})).then((function(e){return e.document.title="",u.getPage(1).then((function(t){var r=t.getViewport({scale:1});return e.document.head.appendChild(e.document.createElement("style")).textContent="@supports ((size:A4) and (size:1pt 1pt)) {@page { margin: 1pt; size: "+r.width*n/i+"pt "+r.height*n/i+"pt; }}@media print {body { margin: 0 }canvas { page-break-before: avoid; page-break-after: always; page-break-inside: avoid }}@media screen {body { margin: 0 }}",e}))})).then((function(e){for(var r=[],i=1;i<=u.numPages;++i)void 0!==t&&-1===t.indexOf(i)||r.push(u.getPage(i).then((function(t){var r=t.getViewport({scale:1}),i=e.document.body.appendChild(e.document.createElement("canvas"));return i.width=r.width*n,i.height=r.height*n,t.render({canvasContext:i.getContext("2d"),transform:[n,0,0,n,0,0],viewport:r,intent:"print"}).promise})));Promise.all(r).then((function(){e.focus(),e.document.queryCommandSupported("print")?e.document.execCommand("print",!1,null):e.print(),o()})).catch((function(e){o(),s("error",e)}))}))}function o(){a.parentNode.removeChild(a)}},this.renderPage=function(t){if(null!==l){if(h)return;return h=!0,void l.cancel().catch((function(e){s("error",e)}))}if(null!==c){var r=(void 0===c.rotate?0:c.rotate)+(void 0===t?0:t),f=a.offsetWidth/c.getViewport({scale:1}).width*(window.devicePixelRatio||1),p=c.getViewport({scale:f,rotation:r});s("page-size",p.width,p.height,f),a.width=p.width,a.height=p.height,l=c.render({canvasContext:a.getContext("2d"),viewport:p}),o.style.visibility="hidden",d();var v={scrollPageIntoView:function(e){s("link-clicked",e.pageNumber)}},g=new n["PDFLinkService"];g.setDocument(u),g.setViewer(v),i=i.then(function(){var r=c.getAnnotations({intent:"display"}).then((function(t){e.AnnotationLayer.render({viewport:p.clone({dontFlip:!0}),div:o,annotations:t,page:c,linkService:g,renderInteractiveForms:!1})})),n=l.promise.then((function(){o.style.visibility="",h=!1,l=null})).catch(function(r){if(l=null,r instanceof e.RenderingCancelledException)return h=!1,void this.renderPage(t);s("error",r)}.bind(this));return Promise.all([r,n])}.bind(this))}},this.forEachPage=function(e){var t=u.numPages;(function r(n){u.getPage(n).then(e).then((function(){++n<=t&&r(n)}))})(1)},this.loadPage=function(e,t){c=null,null!==u&&(i=i.then((function(){return u.getPage(e)})).then(function(e){c=e,this.renderPage(t),s("page-loaded",e.pageNumber)}.bind(this)).catch((function(e){f(),d(),s("error",e)})))},this.loadDocument=function(n){if(u=null,c=null,s("num-pages",void 0),!n)return a.removeAttribute("width"),a.removeAttribute("height"),void d();i=i.then((function(){var i;if(t(n)){if(n.destroyed)return void s("error",new Error("loadingTask has been destroyed"));i=n}else i=r(n,{onPassword:function(t,r){var n;switch(r){case e.PasswordResponses.NEED_PASSWORD:n="NEED_PASSWORD";break;case e.PasswordResponses.INCORRECT_PASSWORD:n="INCORRECT_PASSWORD";break}s("password",t,n)},onProgress:function(e){var t=e.loaded/e.total;s("progress",Math.min(t,1))}});return i.promise})).then((function(e){u=e,s("num-pages",e.numPages),s("loaded")})).catch((function(e){f(),d(),s("error",e)}))},o.style.transformOrigin="0 0"}return{createLoadingTask:r,PDFJSWrapper:a}}},b126:function(e,t,r){},f8db:function(e,t,r){
/**
 * @licstart The following is the entire license notice for the
 * Javascript code in this page
 *
 * Copyright 2020 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * @licend The above is the entire license notice for the
 * Javascript code in this page
 */
(function(t,r){e.exports=r()})(0,(function(){return function(e){var t={};function r(n){if(t[n])return t[n].exports;var i=t[n]={i:n,l:!1,exports:{}};return e[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}return r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)r.d(n,i,function(t){return e[t]}.bind(null,i));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=0)}([function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AnnotationLayerBuilder",{enumerable:!0,get:function(){return n.AnnotationLayerBuilder}}),Object.defineProperty(t,"DefaultAnnotationLayerFactory",{enumerable:!0,get:function(){return n.DefaultAnnotationLayerFactory}}),Object.defineProperty(t,"DefaultTextLayerFactory",{enumerable:!0,get:function(){return i.DefaultTextLayerFactory}}),Object.defineProperty(t,"TextLayerBuilder",{enumerable:!0,get:function(){return i.TextLayerBuilder}}),Object.defineProperty(t,"EventBus",{enumerable:!0,get:function(){return a.EventBus}}),Object.defineProperty(t,"NullL10n",{enumerable:!0,get:function(){return a.NullL10n}}),Object.defineProperty(t,"ProgressBar",{enumerable:!0,get:function(){return a.ProgressBar}}),Object.defineProperty(t,"PDFLinkService",{enumerable:!0,get:function(){return o.PDFLinkService}}),Object.defineProperty(t,"SimpleLinkService",{enumerable:!0,get:function(){return o.SimpleLinkService}}),Object.defineProperty(t,"DownloadManager",{enumerable:!0,get:function(){return s.DownloadManager}}),Object.defineProperty(t,"GenericL10n",{enumerable:!0,get:function(){return u.GenericL10n}}),Object.defineProperty(t,"PDFFindController",{enumerable:!0,get:function(){return c.PDFFindController}}),Object.defineProperty(t,"PDFHistory",{enumerable:!0,get:function(){return l.PDFHistory}}),Object.defineProperty(t,"PDFPageView",{enumerable:!0,get:function(){return h.PDFPageView}}),Object.defineProperty(t,"PDFSinglePageViewer",{enumerable:!0,get:function(){return f.PDFSinglePageViewer}}),Object.defineProperty(t,"PDFViewer",{enumerable:!0,get:function(){return d.PDFViewer}});var n=r(1),i=r(8),a=r(3),o=r(7),s=r(9),u=r(11),c=r(13),l=r(15),h=r(16),f=r(18),d=r(20)},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DefaultAnnotationLayerFactory=t.AnnotationLayerBuilder=void 0;var n=r(2),i=r(3),a=r(7);function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function u(e,t,r){return t&&s(e.prototype,t),r&&s(e,r),e}var c=function(){function e(t){var r=t.pageDiv,n=t.pdfPage,a=t.linkService,s=t.downloadManager,u=t.annotationStorage,c=void 0===u?null:u,l=t.imageResourcesPath,h=void 0===l?"":l,f=t.renderInteractiveForms,d=void 0===f||f,p=t.l10n,v=void 0===p?i.NullL10n:p;o(this,e),this.pageDiv=r,this.pdfPage=n,this.linkService=a,this.downloadManager=s,this.imageResourcesPath=h,this.renderInteractiveForms=d,this.l10n=v,this.annotationStorage=c,this.div=null,this._cancelled=!1}return u(e,[{key:"render",value:function(e){var t=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"display";return this.pdfPage.getAnnotations({intent:r}).then((function(r){if(!t._cancelled&&0!==r.length){var i={viewport:e.clone({dontFlip:!0}),div:t.div,annotations:r,page:t.pdfPage,imageResourcesPath:t.imageResourcesPath,renderInteractiveForms:t.renderInteractiveForms,linkService:t.linkService,downloadManager:t.downloadManager,annotationStorage:t.annotationStorage};t.div?n.AnnotationLayer.update(i):(t.div=document.createElement("div"),t.div.className="annotationLayer",t.pageDiv.appendChild(t.div),i.div=t.div,n.AnnotationLayer.render(i),t.l10n.translate(t.div))}}))}},{key:"cancel",value:function(){this._cancelled=!0}},{key:"hide",value:function(){this.div&&this.div.setAttribute("hidden","true")}}]),e}();t.AnnotationLayerBuilder=c;var l=function(){function e(){o(this,e)}return u(e,[{key:"createAnnotationLayerBuilder",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",o=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:i.NullL10n;return new c({pageDiv:e,pdfPage:t,imageResourcesPath:n,renderInteractiveForms:o,linkService:new a.SimpleLinkService,l10n:s,annotationStorage:r})}}]),e}();t.DefaultAnnotationLayerFactory=l},function(e,t,n){"use strict";var i;i="undefined"!==typeof window&&window["pdfjs-dist/build/pdf"]?window["pdfjs-dist/build/pdf"]:r("4383"),e.exports=i},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isValidRotation=Q,t.isValidScrollMode=K,t.isValidSpreadMode=J,t.isPortraitOrientation=$,t.clamp=ne,t.getPDFFileNameFromURL=G,t.noContextMenuHandler=z,t.parseQueryString=D,t.backtrackBeforeAllVisibleElements=W,t.getVisibleElements=V,t.roundToDivide=q,t.getPageSizeInches=B,t.approximateFraction=U,t.getOutputScale=M,t.scrollIntoView=F,t.watchScroll=N,t.binarySearchFirstItem=j,t.normalizeWheelEventDirection=Y,t.normalizeWheelEventDelta=X,t.waitOnEventOrTimeout=ee,t.moveToEndOfArray=ae,t.WaitOnType=t.animationStarted=t.ProgressBar=t.EventBus=t.NullL10n=t.SpreadMode=t.ScrollMode=t.TextLayerMode=t.RendererType=t.PresentationModeState=t.VERTICAL_PADDING=t.SCROLLBAR_PADDING=t.MAX_AUTO_SCALE=t.UNKNOWN_SCALE=t.MAX_SCALE=t.MIN_SCALE=t.DEFAULT_SCALE=t.DEFAULT_SCALE_VALUE=t.CSS_UNITS=t.AutoPrintRegExp=void 0;var n=i(r(4));function i(e){return e&&e.__esModule?e:{default:e}}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function s(e,t,r){return t&&o(e.prototype,t),r&&o(e,r),e}function u(e){return u="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u(e)}function c(e,t){return p(e)||d(e,t)||h(e,t)||l()}function l(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function h(e,t){if(e){if("string"===typeof e)return f(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?f(e,t):void 0}}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function d(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var r=[],n=!0,i=!1,a=void 0;try{for(var o,s=e[Symbol.iterator]();!(n=(o=s.next()).done);n=!0)if(r.push(o.value),t&&r.length===t)break}catch(u){i=!0,a=u}finally{try{n||null==s["return"]||s["return"]()}finally{if(i)throw a}}return r}}function p(e){if(Array.isArray(e))return e}function v(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(c){return void r(c)}s.done?t(u):Promise.resolve(u).then(n,i)}function g(e){return function(){var t=this,r=arguments;return new Promise((function(n,i){var a=e.apply(t,r);function o(e){v(a,n,i,o,s,"next",e)}function s(e){v(a,n,i,o,s,"throw",e)}o(void 0)}))}}var y=96/72;t.CSS_UNITS=y;var m="auto";t.DEFAULT_SCALE_VALUE=m;var b=1;t.DEFAULT_SCALE=b;var _=.1;t.MIN_SCALE=_;var w=10;t.MAX_SCALE=w;var S=0;t.UNKNOWN_SCALE=S;var A=1.25;t.MAX_AUTO_SCALE=A;var k=40;t.SCROLLBAR_PADDING=k;var x=5;t.VERTICAL_PADDING=x;var P={UNKNOWN:0,NORMAL:1,CHANGING:2,FULLSCREEN:3};t.PresentationModeState=P;var C={CANVAS:"canvas",SVG:"svg"};t.RendererType=C;var T={DISABLE:0,ENABLE:1,ENABLE_ENHANCE:2};t.TextLayerMode=T;var R={UNKNOWN:-1,VERTICAL:0,HORIZONTAL:1,WRAPPED:2};t.ScrollMode=R;var E={UNKNOWN:-1,NONE:0,ODD:1,EVEN:2};t.SpreadMode=E;var L=/\bprint\s*\(/;function O(e,t){return t?e.replace(/\{\{\s*(\w+)\s*\}\}/g,(function(e,r){return r in t?t[r]:"{{"+r+"}}"})):e}t.AutoPrintRegExp=L;var I={getLanguage:function(){return g(n["default"].mark((function e(){return n["default"].wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return","en-us");case 1:case"end":return e.stop()}}),e)})))()},getDirection:function(){return g(n["default"].mark((function e(){return n["default"].wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return","ltr");case 1:case"end":return e.stop()}}),e)})))()},get:function(e,t,r){return g(n["default"].mark((function e(){return n["default"].wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",O(r,t));case 1:case"end":return e.stop()}}),e)})))()},translate:function(e){return g(n["default"].mark((function e(){return n["default"].wrap((function(e){while(1)switch(e.prev=e.next){case 0:case"end":return e.stop()}}),e)})))()}};function M(e){var t=window.devicePixelRatio||1,r=e.webkitBackingStorePixelRatio||e.mozBackingStorePixelRatio||e.msBackingStorePixelRatio||e.oBackingStorePixelRatio||e.backingStorePixelRatio||1,n=t/r;return{sx:n,sy:n,scaled:1!==n}}function F(e,t){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=e.offsetParent;if(n){var i=e.offsetTop+e.clientTop,a=e.offsetLeft+e.clientLeft;while(n.clientHeight===n.scrollHeight&&n.clientWidth===n.scrollWidth||r&&"hidden"===getComputedStyle(n).overflow)if(n.dataset._scaleY&&(i/=n.dataset._scaleY,a/=n.dataset._scaleX),i+=n.offsetTop,a+=n.offsetLeft,n=n.offsetParent,!n)return;t&&(void 0!==t.top&&(i+=t.top),void 0!==t.left&&(a+=t.left,n.scrollLeft=a)),n.scrollTop=i}else console.error("offsetParent is not set -- cannot scroll")}function N(e,t){var r=function(r){i||(i=window.requestAnimationFrame((function(){i=null;var r=e.scrollLeft,a=n.lastX;r!==a&&(n.right=r>a),n.lastX=r;var o=e.scrollTop,s=n.lastY;o!==s&&(n.down=o>s),n.lastY=o,t(n)})))},n={right:!0,down:!0,lastX:e.scrollLeft,lastY:e.scrollTop,_eventHandler:r},i=null;return e.addEventListener("scroll",r,!0),n}function D(e){for(var t=e.split("&"),r=Object.create(null),n=0,i=t.length;n<i;++n){var a=t[n].split("="),o=a[0].toLowerCase(),s=a.length>1?a[1]:null;r[decodeURIComponent(o)]=decodeURIComponent(s)}return r}function j(e,t){var r=0,n=e.length-1;if(n<0||!t(e[n]))return e.length;if(t(e[r]))return r;while(r<n){var i=r+n>>1,a=e[i];t(a)?n=i:r=i+1}return r}function U(e){if(Math.floor(e)===e)return[e,1];var t=1/e,r=8;if(t>r)return[1,r];if(Math.floor(t)===t)return[1,t];var n,i=e>1?t:e,a=0,o=1,s=1,u=1;while(1){var c=a+s,l=o+u;if(l>r)break;i<=c/l?(s=c,u=l):(a=c,o=l)}return n=i-a/o<s/u-i?i===e?[a,o]:[o,a]:i===e?[s,u]:[u,s],n}function q(e,t){var r=e%t;return 0===r?e:Math.round(e-r+t)}function B(e){var t=e.view,r=e.userUnit,n=e.rotate,i=c(t,4),a=i[0],o=i[1],s=i[2],u=i[3],l=n%180!==0,h=(s-a)/72*r,f=(u-o)/72*r;return{width:l?f:h,height:l?h:f}}function W(e,t,r){if(e<2)return e;var n=t[e].div,i=n.offsetTop+n.clientTop;i>=r&&(n=t[e-1].div,i=n.offsetTop+n.clientTop);for(var a=e-2;a>=0;--a){if(n=t[a].div,n.offsetTop+n.clientTop+n.clientHeight<=i)break;e=a}return e}function V(e,t){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i=e.scrollTop,a=i+e.clientHeight,o=e.scrollLeft,s=o+e.clientWidth;function u(e){var t=e.div,r=t.offsetTop+t.clientTop+t.clientHeight;return r>i}function c(e){var t=e.div,r=t.offsetLeft+t.clientLeft+t.clientWidth;return r>o}var l=[],h=t.length,f=0===h?0:j(t,n?c:u);f>0&&f<h&&!n&&(f=W(f,t,i));for(var d=n?s:-1,p=f;p<h;p++){var v=t[p],g=v.div,y=g.offsetLeft+g.clientLeft,m=g.offsetTop+g.clientTop,b=g.clientWidth,_=g.clientHeight,w=y+b,S=m+_;if(-1===d)S>=a&&(d=S);else if((n?y:m)>d)break;if(!(S<=i||m>=a||w<=o||y>=s)){var A=Math.max(0,i-m)+Math.max(0,S-a),k=Math.max(0,o-y)+Math.max(0,w-s),x=(_-A)*(b-k)*100/_/b|0;l.push({id:v.id,x:y,y:m,view:v,percent:x})}}var P=l[0],C=l[l.length-1];return r&&l.sort((function(e,t){var r=e.percent-t.percent;return Math.abs(r)>.001?-r:e.id-t.id})),{first:P,last:C,views:l}}function z(e){e.preventDefault()}function H(e){var t=0,r=e.length;while(t<r&&""===e[t].trim())t++;return"data:"===e.substring(t,t+5).toLowerCase()}function G(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"document.pdf";if("string"!==typeof e)return t;if(H(e))return console.warn('getPDFFileNameFromURL: ignoring "data:" URL for performance reasons.'),t;var r=/^(?:(?:[^:]+:)?\/\/[^\/]+)?([^?#]*)(\?[^#]*)?(#.*)?$/,n=/[^\/?#=]+\.pdf\b(?!.*\.pdf\b)/i,i=r.exec(e),a=n.exec(i[1])||n.exec(i[2])||n.exec(i[3]);if(a&&(a=a[0],a.includes("%")))try{a=n.exec(decodeURIComponent(a))[0]}catch(o){}return a||t}function Y(e){var t=Math.sqrt(e.deltaX*e.deltaX+e.deltaY*e.deltaY),r=Math.atan2(e.deltaY,e.deltaX);return-.25*Math.PI<r&&r<.75*Math.PI&&(t=-t),t}function X(e){var t=Y(e),r=0,n=1,i=30,a=30;return e.deltaMode===r?t/=i*a:e.deltaMode===n&&(t/=a),t}function Q(e){return Number.isInteger(e)&&e%90===0}function K(e){return Number.isInteger(e)&&Object.values(R).includes(e)&&e!==R.UNKNOWN}function J(e){return Number.isInteger(e)&&Object.values(E).includes(e)&&e!==E.UNKNOWN}function $(e){return e.width<=e.height}t.NullL10n=I;var Z={EVENT:"event",TIMEOUT:"timeout"};function ee(e){var t=e.target,r=e.name,n=e.delay,i=void 0===n?0:n;return new Promise((function(e,n){if("object"!==u(t)||!r||"string"!==typeof r||!(Number.isInteger(i)&&i>=0))throw new Error("waitOnEventOrTimeout - invalid parameters.");function a(n){t instanceof re?t._off(r,o):t.removeEventListener(r,o),c&&clearTimeout(c),e(n)}var o=a.bind(null,Z.EVENT);t instanceof re?t._on(r,o):t.addEventListener(r,o);var s=a.bind(null,Z.TIMEOUT),c=setTimeout(s,i)}))}t.WaitOnType=Z;var te=new Promise((function(e){window.requestAnimationFrame(e)}));t.animationStarted=te;var re=function(){function e(t){a(this,e),this._listeners=Object.create(null)}return s(e,[{key:"on",value:function(e,t){this._on(e,t,{external:!0})}},{key:"off",value:function(e,t){this._off(e,t,{external:!0})}},{key:"dispatch",value:function(e){var t=this._listeners[e];if(t&&0!==t.length){var r,n=Array.prototype.slice.call(arguments,1);t.slice(0).forEach((function(e){var t=e.listener,i=e.external;if(i)return r||(r=[]),void r.push(t);t.apply(null,n)})),r&&(r.forEach((function(e){e.apply(null,n)})),r=null)}}},{key:"_on",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,n=this._listeners[e];n||(this._listeners[e]=n=[]),n.push({listener:t,external:!0===(r&&r.external)})}},{key:"_off",value:function(e,t){var r=this._listeners[e];if(r)for(var n=0,i=r.length;n<i;n++)if(r[n].listener===t)return void r.splice(n,1)}}]),e}();function ne(e,t,r){return Math.min(Math.max(e,t),r)}t.EventBus=re;var ie=function(){function e(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.height,i=r.width,o=r.units;a(this,e),this.visible=!0,this.div=document.querySelector(t+" .progress"),this.bar=this.div.parentNode,this.height=n||100,this.width=i||100,this.units=o||"%",this.div.style.height=this.height+this.units,this.percent=0}return s(e,[{key:"_updateBar",value:function(){if(this._indeterminate)return this.div.classList.add("indeterminate"),void(this.div.style.width=this.width+this.units);this.div.classList.remove("indeterminate");var e=this.width*this._percent/100;this.div.style.width=e+this.units}},{key:"setWidth",value:function(e){if(e){var t=e.parentNode,r=t.offsetWidth-e.offsetWidth;r>0&&(this.bar.style.width="calc(100% - ".concat(r,"px)"))}}},{key:"hide",value:function(){this.visible&&(this.visible=!1,this.bar.classList.add("hidden"),document.body.classList.remove("loadingInProgress"))}},{key:"show",value:function(){this.visible||(this.visible=!0,document.body.classList.add("loadingInProgress"),this.bar.classList.remove("hidden"))}},{key:"percent",get:function(){return this._percent},set:function(e){this._indeterminate=isNaN(e),this._percent=ne(e,0,100),this._updateBar()}}]),e}();function ae(e,t){for(var r=[],n=e.length,i=0,a=0;a<n;++a)t(e[a])?r.push(e[a]):(e[i]=e[a],++i);for(var o=0;i<n;++o,++i)e[i]=r[o]}t.ProgressBar=ie},function(e,t,r){"use strict";e.exports=r(5)},function(e,t,r){"use strict";(function(e){function t(e){return t="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t(e)}var r=function(e){var r,n=Object.prototype,i=n.hasOwnProperty,a="function"===typeof Symbol?Symbol:{},o=a.iterator||"@@iterator",s=a.asyncIterator||"@@asyncIterator",u=a.toStringTag||"@@toStringTag";function c(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(I){c=function(e,t,r){return e[t]=r}}function l(e,t,r,n){var i=t&&t.prototype instanceof y?t:y,a=Object.create(i.prototype),o=new E(n||[]);return a._invoke=P(e,r,o),a}function h(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(I){return{type:"throw",arg:I}}}e.wrap=l;var f="suspendedStart",d="suspendedYield",p="executing",v="completed",g={};function y(){}function m(){}function b(){}var _={};_[o]=function(){return this};var w=Object.getPrototypeOf,S=w&&w(w(L([])));S&&S!==n&&i.call(S,o)&&(_=S);var A=b.prototype=y.prototype=Object.create(_);function k(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function x(e,r){function n(a,o,s,u){var c=h(e[a],e,o);if("throw"!==c.type){var l=c.arg,f=l.value;return f&&"object"===t(f)&&i.call(f,"__await")?r.resolve(f.__await).then((function(e){n("next",e,s,u)}),(function(e){n("throw",e,s,u)})):r.resolve(f).then((function(e){l.value=e,s(l)}),(function(e){return n("throw",e,s,u)}))}u(c.arg)}var a;function o(e,t){function i(){return new r((function(r,i){n(e,t,r,i)}))}return a=a?a.then(i,i):i()}this._invoke=o}function P(e,t,r){var n=f;return function(i,a){if(n===p)throw new Error("Generator is already running");if(n===v){if("throw"===i)throw a;return O()}r.method=i,r.arg=a;while(1){var o=r.delegate;if(o){var s=C(o,r);if(s){if(s===g)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(n===f)throw n=v,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n=p;var u=h(e,t,r);if("normal"===u.type){if(n=r.done?v:d,u.arg===g)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(n=v,r.method="throw",r.arg=u.arg)}}}function C(e,t){var n=e.iterator[t.method];if(n===r){if(t.delegate=null,"throw"===t.method){if(e.iterator["return"]&&(t.method="return",t.arg=r,C(e,t),"throw"===t.method))return g;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return g}var i=h(n,e.iterator,t.arg);if("throw"===i.type)return t.method="throw",t.arg=i.arg,t.delegate=null,g;var a=i.arg;return a?a.done?(t[e.resultName]=a.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=r),t.delegate=null,g):a:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,g)}function T(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function R(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function E(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(T,this),this.reset(!0)}function L(e){if(e){var t=e[o];if(t)return t.call(e);if("function"===typeof e.next)return e;if(!isNaN(e.length)){var n=-1,a=function t(){while(++n<e.length)if(i.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=r,t.done=!0,t};return a.next=a}}return{next:O}}function O(){return{value:r,done:!0}}return m.prototype=A.constructor=b,b.constructor=m,m.displayName=c(b,u,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"===typeof e&&e.constructor;return!!t&&(t===m||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,c(e,u,"GeneratorFunction")),e.prototype=Object.create(A),e},e.awrap=function(e){return{__await:e}},k(x.prototype),x.prototype[s]=function(){return this},e.AsyncIterator=x,e.async=function(t,r,n,i,a){void 0===a&&(a=Promise);var o=new x(l(t,r,n,i),a);return e.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},k(A),c(A,u,"Generator"),A[o]=function(){return this},A.toString=function(){return"[object Generator]"},e.keys=function(e){var t=[];for(var r in e)t.push(r);return t.reverse(),function r(){while(t.length){var n=t.pop();if(n in e)return r.value=n,r.done=!1,r}return r.done=!0,r}},e.values=L,E.prototype={constructor:E,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=r,this.done=!1,this.delegate=null,this.method="next",this.arg=r,this.tryEntries.forEach(R),!e)for(var t in this)"t"===t.charAt(0)&&i.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=r)},stop:function(){this.done=!0;var e=this.tryEntries[0],t=e.completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(n,i){return s.type="throw",s.arg=e,t.next=n,i&&(t.method="next",t.arg=r),!!i}for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a],s=o.completion;if("root"===o.tryLoc)return n("end");if(o.tryLoc<=this.prev){var u=i.call(o,"catchLoc"),c=i.call(o,"finallyLoc");if(u&&c){if(this.prev<o.catchLoc)return n(o.catchLoc,!0);if(this.prev<o.finallyLoc)return n(o.finallyLoc)}else if(u){if(this.prev<o.catchLoc)return n(o.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return n(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&i.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var a=n;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=e,o.arg=t,a?(this.method="next",this.next=a.finallyLoc,g):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),R(r),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var i=n.arg;R(r)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:L(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=r),g}},e}("object"===t(e)?e.exports:{});try{regeneratorRuntime=r}catch(n){Function("r","regeneratorRuntime = r")(r)}}).call(this,r(6)(e))},function(e,t,r){"use strict";e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SimpleLinkService=t.PDFLinkService=void 0;var n=r(3);function i(e){return i="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function s(e,t,r){return t&&o(e.prototype,t),r&&o(e,r),e}var u=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=t.eventBus,n=t.externalLinkTarget,i=void 0===n?null:n,o=t.externalLinkRel,s=void 0===o?null:o,u=t.externalLinkEnabled,c=void 0===u||u,l=t.ignoreDestinationZoom,h=void 0!==l&&l;a(this,e),this.eventBus=r,this.externalLinkTarget=i,this.externalLinkRel=s,this.externalLinkEnabled=c,this._ignoreDestinationZoom=h,this.baseUrl=null,this.pdfDocument=null,this.pdfViewer=null,this.pdfHistory=null,this._pagesRefCache=null}return s(e,[{key:"setDocument",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;this.baseUrl=t,this.pdfDocument=e,this._pagesRefCache=Object.create(null)}},{key:"setViewer",value:function(e){this.pdfViewer=e}},{key:"setHistory",value:function(e){this.pdfHistory=e}},{key:"navigateTo",value:function(e){var t=this,r=function r(n){var i,a=n.namedDest,o=n.explicitDest,s=o[0];if(s instanceof Object){if(i=t._cachedPageNumber(s),null===i)return void t.pdfDocument.getPageIndex(s).then((function(e){t.cachePageRef(e+1,s),r({namedDest:a,explicitDest:o})}))["catch"]((function(){console.error('PDFLinkService.navigateTo: "'.concat(s,'" is not ')+'a valid page reference, for dest="'.concat(e,'".'))}))}else{if(!Number.isInteger(s))return void console.error('PDFLinkService.navigateTo: "'.concat(s,'" is not ')+'a valid destination reference, for dest="'.concat(e,'".'));i=s+1}!i||i<1||i>t.pagesCount?console.error('PDFLinkService.navigateTo: "'.concat(i,'" is not ')+'a valid page number, for dest="'.concat(e,'".')):(t.pdfHistory&&(t.pdfHistory.pushCurrentPosition(),t.pdfHistory.push({namedDest:a,explicitDest:o,pageNumber:i})),t.pdfViewer.scrollPageIntoView({pageNumber:i,destArray:o,ignoreDestinationZoom:t._ignoreDestinationZoom}))};new Promise((function(r,n){"string"!==typeof e?r({namedDest:"",explicitDest:e}):t.pdfDocument.getDestination(e).then((function(t){r({namedDest:e,explicitDest:t})}))})).then((function(t){Array.isArray(t.explicitDest)?r(t):console.error('PDFLinkService.navigateTo: "'.concat(t.explicitDest,'" is')+' not a valid destination array, for dest="'.concat(e,'".'))}))}},{key:"getDestinationHash",value:function(e){if("string"===typeof e)return this.getAnchorUrl("#"+escape(e));if(Array.isArray(e)){var t=JSON.stringify(e);return this.getAnchorUrl("#"+escape(t))}return this.getAnchorUrl("")}},{key:"getAnchorUrl",value:function(e){return(this.baseUrl||"")+e}},{key:"setHash",value:function(e){var t,r;if(e.includes("=")){var i=(0,n.parseQueryString)(e);if("search"in i&&this.eventBus.dispatch("findfromurlhash",{source:this,query:i.search.replace(/"/g,""),phraseSearch:"true"===i.phrase}),"page"in i&&(t=0|i.page||1),"zoom"in i){var a=i.zoom.split(","),o=a[0],s=parseFloat(o);o.includes("Fit")?"Fit"===o||"FitB"===o?r=[null,{name:o}]:"FitH"===o||"FitBH"===o||"FitV"===o||"FitBV"===o?r=[null,{name:o},a.length>1?0|a[1]:null]:"FitR"===o?5!==a.length?console.error('PDFLinkService.setHash: Not enough parameters for "FitR".'):r=[null,{name:o},0|a[1],0|a[2],0|a[3],0|a[4]]:console.error('PDFLinkService.setHash: "'.concat(o,'" is not ')+"a valid zoom value."):r=[null,{name:"XYZ"},a.length>1?0|a[1]:null,a.length>2?0|a[2]:null,s?s/100:o]}r?this.pdfViewer.scrollPageIntoView({pageNumber:t||this.page,destArray:r,allowNegativeOffset:!0}):t&&(this.page=t),"pagemode"in i&&this.eventBus.dispatch("pagemode",{source:this,mode:i.pagemode}),"nameddest"in i&&this.navigateTo(i.nameddest)}else{r=unescape(e);try{r=JSON.parse(r),Array.isArray(r)||(r=r.toString())}catch(u){}if("string"===typeof r||c(r))return void this.navigateTo(r);console.error('PDFLinkService.setHash: "'.concat(unescape(e),'" is not ')+"a valid destination.")}}},{key:"executeNamedAction",value:function(e){switch(e){case"GoBack":this.pdfHistory&&this.pdfHistory.back();break;case"GoForward":this.pdfHistory&&this.pdfHistory.forward();break;case"NextPage":this.page<this.pagesCount&&this.page++;break;case"PrevPage":this.page>1&&this.page--;break;case"LastPage":this.page=this.pagesCount;break;case"FirstPage":this.page=1;break;default:break}this.eventBus.dispatch("namedaction",{source:this,action:e})}},{key:"cachePageRef",value:function(e,t){if(t){var r=0===t.gen?"".concat(t.num,"R"):"".concat(t.num,"R").concat(t.gen);this._pagesRefCache[r]=e}}},{key:"_cachedPageNumber",value:function(e){var t=0===e.gen?"".concat(e.num,"R"):"".concat(e.num,"R").concat(e.gen);return this._pagesRefCache&&this._pagesRefCache[t]||null}},{key:"isPageVisible",value:function(e){return this.pdfViewer.isPageVisible(e)}},{key:"pagesCount",get:function(){return this.pdfDocument?this.pdfDocument.numPages:0}},{key:"page",get:function(){return this.pdfViewer.currentPageNumber},set:function(e){this.pdfViewer.currentPageNumber=e}},{key:"rotation",get:function(){return this.pdfViewer.pagesRotation},set:function(e){this.pdfViewer.pagesRotation=e}}]),e}();function c(e){if(!Array.isArray(e))return!1;var t=e.length;if(t<2)return!1;var r=e[0];if(("object"!==i(r)||!Number.isInteger(r.num)||!Number.isInteger(r.gen))&&!(Number.isInteger(r)&&r>=0))return!1;var n=e[1];if("object"!==i(n)||"string"!==typeof n.name)return!1;var a=!0;switch(n.name){case"XYZ":if(5!==t)return!1;break;case"Fit":case"FitB":return 2===t;case"FitH":case"FitBH":case"FitV":case"FitBV":if(3!==t)return!1;break;case"FitR":if(6!==t)return!1;a=!1;break;default:return!1}for(var o=2;o<t;o++){var s=e[o];if(!("number"===typeof s||a&&null===s))return!1}return!0}t.PDFLinkService=u;var l=function(){function e(){a(this,e),this.externalLinkTarget=null,this.externalLinkRel=null,this.externalLinkEnabled=!0,this._ignoreDestinationZoom=!1}return s(e,[{key:"navigateTo",value:function(e){}},{key:"getDestinationHash",value:function(e){return"#"}},{key:"getAnchorUrl",value:function(e){return"#"}},{key:"setHash",value:function(e){}},{key:"executeNamedAction",value:function(e){}},{key:"cachePageRef",value:function(e,t){}},{key:"isPageVisible",value:function(e){return!0}},{key:"pagesCount",get:function(){return 0}},{key:"page",get:function(){return 0},set:function(e){}},{key:"rotation",get:function(){return 0},set:function(e){}}]),e}();t.SimpleLinkService=l},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DefaultTextLayerFactory=t.TextLayerBuilder=void 0;var n=r(2);function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function o(e,t,r){return t&&a(e.prototype,t),r&&a(e,r),e}var s=300,u=function(){function e(t){var r=t.textLayerDiv,n=t.eventBus,a=t.pageIndex,o=t.viewport,s=t.findController,u=void 0===s?null:s,c=t.enhanceTextSelection,l=void 0!==c&&c;i(this,e),this.textLayerDiv=r,this.eventBus=n,this.textContent=null,this.textContentItemsStr=[],this.textContentStream=null,this.renderingDone=!1,this.pageIdx=a,this.pageNumber=this.pageIdx+1,this.matches=[],this.viewport=o,this.textDivs=[],this.findController=u,this.textLayerRenderTask=null,this.enhanceTextSelection=l,this._onUpdateTextLayerMatches=null,this._bindMouse()}return o(e,[{key:"_finishRendering",value:function(){if(this.renderingDone=!0,!this.enhanceTextSelection){var e=document.createElement("div");e.className="endOfContent",this.textLayerDiv.appendChild(e)}this.eventBus.dispatch("textlayerrendered",{source:this,pageNumber:this.pageNumber,numTextDivs:this.textDivs.length})}},{key:"render",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;if((this.textContent||this.textContentStream)&&!this.renderingDone){this.cancel(),this.textDivs=[];var r=document.createDocumentFragment();this.textLayerRenderTask=(0,n.renderTextLayer)({textContent:this.textContent,textContentStream:this.textContentStream,container:r,viewport:this.viewport,textDivs:this.textDivs,textContentItemsStr:this.textContentItemsStr,timeout:t,enhanceTextSelection:this.enhanceTextSelection}),this.textLayerRenderTask.promise.then((function(){e.textLayerDiv.appendChild(r),e._finishRendering(),e._updateMatches()}),(function(e){})),this._onUpdateTextLayerMatches||(this._onUpdateTextLayerMatches=function(t){t.pageIndex!==e.pageIdx&&-1!==t.pageIndex||e._updateMatches()},this.eventBus._on("updatetextlayermatches",this._onUpdateTextLayerMatches))}}},{key:"cancel",value:function(){this.textLayerRenderTask&&(this.textLayerRenderTask.cancel(),this.textLayerRenderTask=null),this._onUpdateTextLayerMatches&&(this.eventBus._off("updatetextlayermatches",this._onUpdateTextLayerMatches),this._onUpdateTextLayerMatches=null)}},{key:"setTextContentStream",value:function(e){this.cancel(),this.textContentStream=e}},{key:"setTextContent",value:function(e){this.cancel(),this.textContent=e}},{key:"_convertMatches",value:function(e,t){if(!e)return[];for(var r=this.findController,n=this.textContentItemsStr,i=0,a=0,o=n.length-1,s=r.state.query.length,u=[],c=0,l=e.length;c<l;c++){var h=e[c];while(i!==o&&h>=a+n[i].length)a+=n[i].length,i++;i===n.length&&console.error("Could not find a matching mapping");var f={begin:{divIdx:i,offset:h-a}};h+=t?t[c]:s;while(i!==o&&h>a+n[i].length)a+=n[i].length,i++;f.end={divIdx:i,offset:h-a},u.push(f)}return u}},{key:"_renderMatches",value:function(e){if(0!==e.length){var t=this.findController,r=this.pageIdx,n=this.textContentItemsStr,i=this.textDivs,a=r===t.selected.pageIdx,o=t.selected.matchIdx,s=t.state.highlightAll,u=null,c={divIdx:-1,offset:void 0},l=o,h=l+1;if(s)l=0,h=e.length;else if(!a)return;for(var f=l;f<h;f++){var d=e[f],p=d.begin,v=d.end,g=a&&f===o,y=g?" selected":"";if(g&&t.scrollMatchIntoView({element:i[p.divIdx],pageIndex:r,matchIndex:o}),u&&p.divIdx===u.divIdx?w(u.divIdx,u.offset,p.offset):(null!==u&&w(u.divIdx,u.offset,c.offset),_(p)),p.divIdx===v.divIdx)w(p.divIdx,p.offset,v.offset,"highlight"+y);else{w(p.divIdx,p.offset,c.offset,"highlight begin"+y);for(var m=p.divIdx+1,b=v.divIdx;m<b;m++)i[m].className="highlight middle"+y;_(v,"highlight end"+y)}u=v}u&&w(u.divIdx,u.offset,c.offset)}function _(e,t){var r=e.divIdx;i[r].textContent="",w(r,0,e.offset,t)}function w(e,t,r,a){var o=i[e],s=n[e].substring(t,r),u=document.createTextNode(s);if(a){var c=document.createElement("span");return c.className=a,c.appendChild(u),void o.appendChild(c)}o.appendChild(u)}}},{key:"_updateMatches",value:function(){if(this.renderingDone){for(var e=this.findController,t=this.matches,r=this.pageIdx,n=this.textContentItemsStr,i=this.textDivs,a=-1,o=0,s=t.length;o<s;o++){for(var u=t[o],c=Math.max(a,u.begin.divIdx),l=c,h=u.end.divIdx;l<=h;l++){var f=i[l];f.textContent=n[l],f.className=""}a=u.end.divIdx+1}if(e&&e.highlightMatches){var d=e.pageMatches[r]||null,p=e.pageMatchesLength[r]||null;this.matches=this._convertMatches(d,p),this._renderMatches(this.matches)}}}},{key:"_bindMouse",value:function(){var e=this,t=this.textLayerDiv,r=null;t.addEventListener("mousedown",(function(n){if(e.enhanceTextSelection&&e.textLayerRenderTask)return e.textLayerRenderTask.expandTextDivs(!0),void(r&&(clearTimeout(r),r=null));var i=t.querySelector(".endOfContent");if(i){var a=n.target!==t;if(a=a&&"none"!==window.getComputedStyle(i).getPropertyValue("-moz-user-select"),a){var o=t.getBoundingClientRect(),s=Math.max(0,(n.pageY-o.top)/o.height);i.style.top=(100*s).toFixed(2)+"%"}i.classList.add("active")}})),t.addEventListener("mouseup",(function(){if(e.enhanceTextSelection&&e.textLayerRenderTask)r=setTimeout((function(){e.textLayerRenderTask&&e.textLayerRenderTask.expandTextDivs(!1),r=null}),s);else{var n=t.querySelector(".endOfContent");n&&(n.style.top="",n.classList.remove("active"))}}))}}]),e}();t.TextLayerBuilder=u;var c=function(){function e(){i(this,e)}return o(e,[{key:"createTextLayerBuilder",value:function(e,t,r){var n=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i=arguments.length>4?arguments[4]:void 0;return new u({textLayerDiv:e,pageIndex:t,viewport:r,enhanceTextSelection:n,eventBus:i})}}]),e}();t.DefaultTextLayerFactory=c},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DownloadManager=void 0;var n=r(2),i=r(10);function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function s(e,t,r){return t&&o(e.prototype,t),r&&o(e,r),e}function u(e,t){var r=document.createElement("a");if(!r.click)throw new Error('DownloadManager: "a.click()" is not supported.');r.href=e,r.target="_parent","download"in r&&(r.download=t),(document.body||document.documentElement).appendChild(r),r.click(),r.remove()}var c=function(){function e(){a(this,e)}return s(e,[{key:"downloadUrl",value:function(e,t){(0,n.createValidAbsoluteUrl)(e,"http://example.com")&&u(e+"#pdfjs.action=download",t)}},{key:"downloadData",value:function(e,t,r){if(navigator.msSaveBlob)navigator.msSaveBlob(new Blob([e],{type:r}),t);else{var a=(0,n.createObjectURL)(e,r,i.viewerCompatibilityParams.disableCreateObjectURL);u(a,t)}}},{key:"download",value:function(e,t,r){if(navigator.msSaveBlob)navigator.msSaveBlob(e,r)||this.downloadUrl(t,r);else if(i.viewerCompatibilityParams.disableCreateObjectURL)this.downloadUrl(t,r);else{var n=URL.createObjectURL(e);u(n,r)}}}]),e}();t.DownloadManager=c},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.viewerCompatibilityParams=void 0;var n=Object.create(null),i="undefined"!==typeof navigator&&navigator.userAgent||"",a="undefined"!==typeof navigator&&navigator.platform||"",o="undefined"!==typeof navigator&&navigator.maxTouchPoints||1,s=/Android/.test(i),u=/Trident/.test(i),c=/\b(iPad|iPhone|iPod)(?=;)/.test(i)||"MacIntel"===a&&o>1,l=/CriOS/.test(i);(function(){(u||l)&&(n.disableCreateObjectURL=!0)})(),function(){(c||s)&&(n.maxCanvasPixels=5242880)}();var h=Object.freeze(n);t.viewerCompatibilityParams=h},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.GenericL10n=void 0;var n=i(r(4));function i(e){return e&&e.__esModule?e:{default:e}}function a(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(c){return void r(c)}s.done?t(u):Promise.resolve(u).then(n,i)}function o(e){return function(){var t=this,r=arguments;return new Promise((function(n,i){var o=e.apply(t,r);function s(e){a(o,n,i,s,u,"next",e)}function u(e){a(o,n,i,s,u,"throw",e)}s(void 0)}))}}function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function u(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function c(e,t,r){return t&&u(e.prototype,t),r&&u(e,r),e}r(12);var l=document.webL10n,h=function(){function e(t){s(this,e),this._lang=t,this._ready=new Promise((function(e,r){l.setLanguage(t,(function(){e(l)}))}))}return c(e,[{key:"getLanguage",value:function(){var e=o(n["default"].mark((function e(){var t;return n["default"].wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._ready;case 2:return t=e.sent,e.abrupt("return",t.getLanguage());case 4:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"getDirection",value:function(){var e=o(n["default"].mark((function e(){var t;return n["default"].wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._ready;case 2:return t=e.sent,e.abrupt("return",t.getDirection());case 4:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"get",value:function(){var e=o(n["default"].mark((function e(t,r,i){var a;return n["default"].wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._ready;case 2:return a=e.sent,e.abrupt("return",a.get(t,r,i));case 4:case"end":return e.stop()}}),e,this)})));function t(t,r,n){return e.apply(this,arguments)}return t}()},{key:"translate",value:function(){var e=o(n["default"].mark((function e(t){var r;return n["default"].wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._ready;case 2:return r=e.sent,e.abrupt("return",r.translate(t));case 4:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}()}]),e}();t.GenericL10n=h},function(e,t,r){"use strict";document.webL10n=function(e,t,r){var n={},i="",a="textContent",o="",s={},u="loading",c=!0;function l(){return t.querySelectorAll('link[type="application/l10n"]')}function h(){var e=t.querySelector('script[type="application/l10n"]');return e?JSON.parse(e.innerHTML):null}function f(e){return e?e.querySelectorAll("*[data-l10n-id]"):[]}function d(e){if(!e)return{};var t=e.getAttribute("data-l10n-id"),r=e.getAttribute("data-l10n-args"),n={};if(r)try{n=JSON.parse(r)}catch(i){console.warn("could not parse arguments for #"+t)}return{id:t,args:n}}function p(e,t,r){t=t||function(e){},r=r||function(){};var n=new XMLHttpRequest;n.open("GET",e,c),n.overrideMimeType&&n.overrideMimeType("text/plain; charset=utf-8"),n.onreadystatechange=function(){4==n.readyState&&(200==n.status||0===n.status?t(n.responseText):r())},n.onerror=r,n.ontimeout=r;try{n.send(null)}catch(i){r()}}function v(e,t,r,o){var s=e.replace(/[^\/]*$/,"")||"./";function u(e){return e.lastIndexOf("\\")<0?e:e.replace(/\\\\/g,"\\").replace(/\\n/g,"\n").replace(/\\r/g,"\r").replace(/\\t/g,"\t").replace(/\\b/g,"\b").replace(/\\f/g,"\f").replace(/\\{/g,"{").replace(/\\}/g,"}").replace(/\\"/g,'"').replace(/\\'/g,"'")}function c(e,r){var n={},i=/^\s*|\s*$/,a=/^\s*#|^\s*$/,o=/^\s*\[(.*)\]\s*$/,c=/^\s*@import\s+url\((.*)\)\s*$/i,l=/^([^=\s]*)\s*=\s*(.+)$/;function h(e,r,h){var d=e.replace(i,"").split(/[\r\n]+/),p="*",v=t.split("-",1)[0],g=!1,y="";function m(){while(1){if(!d.length)return void h();var e=d.shift();if(!a.test(e)){if(r){if(y=o.exec(e),y){p=y[1].toLowerCase(),g="*"!==p&&p!==t&&p!==v;continue}if(g)continue;if(y=c.exec(e),y)return void f(s+y[1],m)}var i=e.match(l);i&&3==i.length&&(n[i[1]]=u(i[2]))}}}m()}function f(e,t){p(e,(function(e){h(e,!1,t)}),(function(){console.warn(e+" not found."),t()}))}h(e,!0,(function(){r(n)}))}p(e,(function(e){i+=e,c(e,(function(e){for(var t in e){var i,o,s=t.lastIndexOf(".");s>0?(i=t.substring(0,s),o=t.substring(s+1)):(i=t,o=a),n[i]||(n[i]={}),n[i][o]=e[t]}r&&r()}))}),o)}function g(e,t){e&&(e=e.toLowerCase()),t=t||function(){},y(),o=e;var r=l(),i=r.length;if(0!==i){var a=null,s=0;a=function(){s++,s>=i&&(t(),u="complete")};for(var c=0;c<i;c++){var f=new m(r[c]);f.load(e,a)}}else{var d=h();if(d&&d.locales&&d.default_locale){if(console.log("using the embedded JSON directory, early way out"),n=d.locales[e],!n){var p=d.default_locale.toLowerCase();for(var g in d.locales){if(g=g.toLowerCase(),g===e){n=d.locales[e];break}g===p&&(n=d.locales[p])}}t()}else console.log("no resource to load, early way out");u="complete"}function m(e){var t=e.href;this.load=function(e,r){v(t,e,r,(function(){console.warn(t+" not found."),console.warn('"'+e+'" resource not found'),o="",r()}))}}}function y(){n={},i="",o=""}function m(e){var t={af:3,ak:4,am:4,ar:1,asa:3,az:0,be:11,bem:3,bez:3,bg:3,bh:4,bm:0,bn:3,bo:0,br:20,brx:3,bs:11,ca:3,cgg:3,chr:3,cs:12,cy:17,da:3,de:3,dv:3,dz:0,ee:3,el:3,en:3,eo:3,es:3,et:3,eu:3,fa:0,ff:5,fi:3,fil:4,fo:3,fr:5,fur:3,fy:3,ga:8,gd:24,gl:3,gsw:3,gu:3,guw:4,gv:23,ha:3,haw:3,he:2,hi:4,hr:11,hu:0,id:0,ig:0,ii:0,is:3,it:3,iu:7,ja:0,jmc:3,jv:0,ka:0,kab:5,kaj:3,kcg:3,kde:0,kea:0,kk:3,kl:3,km:0,kn:0,ko:0,ksb:3,ksh:21,ku:3,kw:7,lag:18,lb:3,lg:3,ln:4,lo:0,lt:10,lv:6,mas:3,mg:4,mk:16,ml:3,mn:3,mo:9,mr:3,ms:0,mt:15,my:0,nah:3,naq:7,nb:3,nd:3,ne:3,nl:3,nn:3,no:3,nr:3,nso:4,ny:3,nyn:3,om:3,or:3,pa:3,pap:3,pl:13,ps:3,pt:3,rm:3,ro:9,rof:3,ru:11,rwk:3,sah:0,saq:3,se:7,seh:3,ses:0,sg:0,sh:11,shi:19,sk:12,sl:14,sma:7,smi:7,smj:7,smn:7,sms:7,sn:3,so:3,sq:3,sr:11,ss:3,ssy:3,st:3,sv:3,sw:3,syr:3,ta:3,te:3,teo:3,th:0,ti:4,tig:3,tk:3,tl:4,tn:3,to:0,tr:0,ts:3,tzm:22,uk:11,ur:3,ve:3,vi:0,vun:3,wa:4,wae:3,wo:0,xh:3,xog:3,yo:0,zh:0,zu:3};function r(e,t){return-1!==t.indexOf(e)}function n(e,t,r){return t<=e&&e<=r}var i={0:function(e){return"other"},1:function(e){return n(e%100,3,10)?"few":0===e?"zero":n(e%100,11,99)?"many":2==e?"two":1==e?"one":"other"},2:function(e){return 0!==e&&e%10===0?"many":2==e?"two":1==e?"one":"other"},3:function(e){return 1==e?"one":"other"},4:function(e){return n(e,0,1)?"one":"other"},5:function(e){return n(e,0,2)&&2!=e?"one":"other"},6:function(e){return 0===e?"zero":e%10==1&&e%100!=11?"one":"other"},7:function(e){return 2==e?"two":1==e?"one":"other"},8:function(e){return n(e,3,6)?"few":n(e,7,10)?"many":2==e?"two":1==e?"one":"other"},9:function(e){return 0===e||1!=e&&n(e%100,1,19)?"few":1==e?"one":"other"},10:function(e){return n(e%10,2,9)&&!n(e%100,11,19)?"few":e%10!=1||n(e%100,11,19)?"other":"one"},11:function(e){return n(e%10,2,4)&&!n(e%100,12,14)?"few":e%10===0||n(e%10,5,9)||n(e%100,11,14)?"many":e%10==1&&e%100!=11?"one":"other"},12:function(e){return n(e,2,4)?"few":1==e?"one":"other"},13:function(e){return n(e%10,2,4)&&!n(e%100,12,14)?"few":1!=e&&n(e%10,0,1)||n(e%10,5,9)||n(e%100,12,14)?"many":1==e?"one":"other"},14:function(e){return n(e%100,3,4)?"few":e%100==2?"two":e%100==1?"one":"other"},15:function(e){return 0===e||n(e%100,2,10)?"few":n(e%100,11,19)?"many":1==e?"one":"other"},16:function(e){return e%10==1&&11!=e?"one":"other"},17:function(e){return 3==e?"few":0===e?"zero":6==e?"many":2==e?"two":1==e?"one":"other"},18:function(e){return 0===e?"zero":n(e,0,2)&&0!==e&&2!=e?"one":"other"},19:function(e){return n(e,2,10)?"few":n(e,0,1)?"one":"other"},20:function(e){return!n(e%10,3,4)&&e%10!=9||n(e%100,10,19)||n(e%100,70,79)||n(e%100,90,99)?e%1e6===0&&0!==e?"many":e%10!=2||r(e%100,[12,72,92])?e%10!=1||r(e%100,[11,71,91])?"other":"one":"two":"few"},21:function(e){return 0===e?"zero":1==e?"one":"other"},22:function(e){return n(e,0,1)||n(e,11,99)?"one":"other"},23:function(e){return n(e%10,1,2)||e%20===0?"one":"other"},24:function(e){return n(e,3,10)||n(e,13,19)?"few":r(e,[2,12])?"two":r(e,[1,11])?"one":"other"}},a=t[e.replace(/-.*$/,"")];return a in i?i[a]:(console.warn("plural form unknown for ["+e+"]"),function(){return"other"})}function b(e,t,r){var i=n[e];if(!i){if(console.warn("#"+e+" is undefined."),!r)return null;i=r}var a={};for(var o in i){var s=i[o];s=_(s,t,e,o),s=w(s,t,e),a[o]=s}return a}function _(e,t,r,i){var a=/\{\[\s*([a-zA-Z]+)\(([a-zA-Z]+)\)\s*\]\}/,o=a.exec(e);if(!o||!o.length)return e;var u,c=o[1],l=o[2];if(t&&l in t?u=t[l]:l in n&&(u=n[l]),c in s){var h=s[c];e=h(e,u,r,i)}return e}function w(e,t,r){var i=/\{\{\s*(.+?)\s*\}\}/g;return e.replace(i,(function(e,i){return t&&i in t?t[i]:i in n?n[i]:(console.log("argument {{"+i+"}} for #"+r+" is undefined."),e)}))}function S(e){var r=d(e);if(r.id){var n=b(r.id,r.args);if(n){if(n[a]){if(0===A(e))e[a]=n[a];else{for(var i=e.childNodes,o=!1,s=0,u=i.length;s<u;s++)3===i[s].nodeType&&/\S/.test(i[s].nodeValue)&&(o?i[s].nodeValue="":(i[s].nodeValue=n[a],o=!0));if(!o){var c=t.createTextNode(n[a]);e.insertBefore(c,e.firstChild)}}delete n[a]}for(var l in n)e[l]=n[l]}else console.warn("#"+r.id+" is undefined.")}}function A(e){if(e.children)return e.children.length;if("undefined"!==typeof e.childElementCount)return e.childElementCount;for(var t=0,r=0;r<e.childNodes.length;r++)t+=1===e.nodeType?1:0;return t}function k(e){e=e||t.documentElement;for(var r=f(e),n=r.length,i=0;i<n;i++)S(r[i]);S(e)}return s.plural=function(e,t,r,i){var u=parseFloat(t);if(isNaN(u))return e;if(i!=a)return e;s._pluralRules||(s._pluralRules=m(o));var c="["+s._pluralRules(u)+"]";return 0===u&&r+"[zero]"in n?e=n[r+"[zero]"][i]:1==u&&r+"[one]"in n?e=n[r+"[one]"][i]:2==u&&r+"[two]"in n?e=n[r+"[two]"][i]:r+c in n?e=n[r+c][i]:r+"[other]"in n&&(e=n[r+"[other]"][i]),e},{get:function(e,t,r){var n,i=e.lastIndexOf("."),o=a;i>0&&(o=e.substring(i+1),e=e.substring(0,i)),r&&(n={},n[o]=r);var s=b(e,t,n);return s&&o in s?s[o]:"{{"+e+"}}"},getData:function(){return n},getText:function(){return i},getLanguage:function(){return o},setLanguage:function(e,t){g(e,(function(){t&&t()}))},getDirection:function(){var e=["ar","he","fa","ps","ur"],t=o.split("-",1)[0];return e.indexOf(t)>=0?"rtl":"ltr"},translate:k,getReadyState:function(){return u},ready:function(r){r&&("complete"==u||"interactive"==u?e.setTimeout((function(){r()})):t.addEventListener&&t.addEventListener("localized",(function e(){t.removeEventListener("localized",e),r()})))}}}(window,document)},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PDFFindController=t.FindState=void 0;var n=r(2),i=r(14),a=r(3);function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function u(e,t,r){return t&&s(e.prototype,t),r&&s(e,r),e}var c={FOUND:0,NOT_FOUND:1,WRAPPED:2,PENDING:3};t.FindState=c;var l=250,h=-50,f=-400,d={"‘":"'","’":"'","‚":"'","‛":"'","“":'"',"”":'"',"„":'"',"‟":'"',"¼":"1/4","½":"1/2","¾":"3/4"},p=null;function v(e){if(!p){var t=Object.keys(d).join("");p=new RegExp("[".concat(t,"]"),"g")}return e.replace(p,(function(e){return d[e]}))}var g=function(){function e(t){var r=t.linkService,n=t.eventBus;o(this,e),this._linkService=r,this._eventBus=n,this._reset(),n._on("findbarclose",this._onFindBarClose.bind(this))}return u(e,[{key:"setDocument",value:function(e){this._pdfDocument&&this._reset(),e&&(this._pdfDocument=e,this._firstPageCapability.resolve())}},{key:"executeCommand",value:function(e,t){var r=this;if(t){var n=this._pdfDocument;(null===this._state||this._shouldDirtyMatch(e,t))&&(this._dirtyMatch=!0),this._state=t,"findhighlightallchange"!==e&&this._updateUIState(c.PENDING),this._firstPageCapability.promise.then((function(){if(r._pdfDocument&&(!n||r._pdfDocument===n)){r._extractText();var t=!r._highlightMatches,i=!!r._findTimeout;r._findTimeout&&(clearTimeout(r._findTimeout),r._findTimeout=null),"find"===e?r._findTimeout=setTimeout((function(){r._nextMatch(),r._findTimeout=null}),l):r._dirtyMatch?r._nextMatch():"findagain"===e?(r._nextMatch(),t&&r._state.highlightAll&&r._updateAllPages()):"findhighlightallchange"===e?(i?r._nextMatch():r._highlightMatches=!0,r._updateAllPages()):r._nextMatch()}}))}}},{key:"scrollMatchIntoView",value:function(e){var t=e.element,r=void 0===t?null:t,n=e.pageIndex,i=void 0===n?-1:n,o=e.matchIndex,s=void 0===o?-1:o;if(this._scrollMatches&&r&&-1!==s&&s===this._selected.matchIdx&&-1!==i&&i===this._selected.pageIdx){this._scrollMatches=!1;var u={top:h,left:f};(0,a.scrollIntoView)(r,u,!0)}}},{key:"_reset",value:function(){this._highlightMatches=!1,this._scrollMatches=!1,this._pdfDocument=null,this._pageMatches=[],this._pageMatchesLength=[],this._state=null,this._selected={pageIdx:-1,matchIdx:-1},this._offset={pageIdx:null,matchIdx:null,wrapped:!1},this._extractTextPromises=[],this._pageContents=[],this._matchesCountTotal=0,this._pagesToSearch=null,this._pendingFindMatches=Object.create(null),this._resumePageIdx=null,this._dirtyMatch=!1,clearTimeout(this._findTimeout),this._findTimeout=null,this._firstPageCapability=(0,n.createPromiseCapability)()}},{key:"_shouldDirtyMatch",value:function(e,t){if(t.query!==this._state.query)return!0;switch(e){case"findagain":var r=this._selected.pageIdx+1,n=this._linkService;return r>=1&&r<=n.pagesCount&&r!==n.page&&!n.isPageVisible(r);case"findhighlightallchange":return!1}return!0}},{key:"_prepareMatches",value:function(e,t,r){function n(t){var r=e[t],n=e[t+1];if(t<e.length-1&&r.match===n.match)return r.skipped=!0,!0;for(var i=t-1;i>=0;i--){var a=e[i];if(!a.skipped){if(a.match+a.matchLength<r.match)break;if(a.match+a.matchLength>=r.match+r.matchLength)return r.skipped=!0,!0}}return!1}e.sort((function(e,t){return e.match===t.match?e.matchLength-t.matchLength:e.match-t.match}));for(var i=0,a=e.length;i<a;i++)n(i)||(t.push(e[i].match),r.push(e[i].matchLength))}},{key:"_isEntireWord",value:function(e,t,r){if(t>0){var n=e.charCodeAt(t),a=e.charCodeAt(t-1);if((0,i.getCharacterType)(n)===(0,i.getCharacterType)(a))return!1}var o=t+r-1;if(o<e.length-1){var s=e.charCodeAt(o),u=e.charCodeAt(o+1);if((0,i.getCharacterType)(s)===(0,i.getCharacterType)(u))return!1}return!0}},{key:"_calculatePhraseMatch",value:function(e,t,r,n){var i=[],a=e.length,o=-a;while(1){if(o=r.indexOf(e,o+a),-1===o)break;n&&!this._isEntireWord(r,o,a)||i.push(o)}this._pageMatches[t]=i}},{key:"_calculateWordMatch",value:function(e,t,r,n){for(var i=[],a=e.match(/\S+/g),o=0,s=a.length;o<s;o++){var u=a[o],c=u.length,l=-c;while(1){if(l=r.indexOf(u,l+c),-1===l)break;n&&!this._isEntireWord(r,l,c)||i.push({match:l,matchLength:c,skipped:!1})}}this._pageMatchesLength[t]=[],this._pageMatches[t]=[],this._prepareMatches(i,this._pageMatches[t],this._pageMatchesLength[t])}},{key:"_calculateMatch",value:function(e){var t=this._pageContents[e],r=this._query,n=this._state,i=n.caseSensitive,a=n.entireWord,o=n.phraseSearch;if(0!==r.length){i||(t=t.toLowerCase(),r=r.toLowerCase()),o?this._calculatePhraseMatch(r,e,t,a):this._calculateWordMatch(r,e,t,a),this._state.highlightAll&&this._updatePage(e),this._resumePageIdx===e&&(this._resumePageIdx=null,this._nextPageMatch());var s=this._pageMatches[e].length;s>0&&(this._matchesCountTotal+=s,this._updateUIResultsCount())}}},{key:"_extractText",value:function(){var e=this;if(!(this._extractTextPromises.length>0))for(var t=Promise.resolve(),r=function(r,i){var a=(0,n.createPromiseCapability)();e._extractTextPromises[r]=a.promise,t=t.then((function(){return e._pdfDocument.getPage(r+1).then((function(e){return e.getTextContent({normalizeWhitespace:!0})})).then((function(t){for(var n=t.items,i=[],o=0,s=n.length;o<s;o++)i.push(n[o].str);e._pageContents[r]=v(i.join("")),a.resolve(r)}),(function(t){console.error("Unable to get text content for page ".concat(r+1),t),e._pageContents[r]="",a.resolve(r)}))}))},i=0,a=this._linkService.pagesCount;i<a;i++)r(i,a)}},{key:"_updatePage",value:function(e){this._scrollMatches&&this._selected.pageIdx===e&&(this._linkService.page=e+1),this._eventBus.dispatch("updatetextlayermatches",{source:this,pageIndex:e})}},{key:"_updateAllPages",value:function(){this._eventBus.dispatch("updatetextlayermatches",{source:this,pageIndex:-1})}},{key:"_nextMatch",value:function(){var e=this,t=this._state.findPrevious,r=this._linkService.page-1,n=this._linkService.pagesCount;if(this._highlightMatches=!0,this._dirtyMatch){this._dirtyMatch=!1,this._selected.pageIdx=this._selected.matchIdx=-1,this._offset.pageIdx=r,this._offset.matchIdx=null,this._offset.wrapped=!1,this._resumePageIdx=null,this._pageMatches.length=0,this._pageMatchesLength.length=0,this._matchesCountTotal=0,this._updateAllPages();for(var i=0;i<n;i++)!0!==this._pendingFindMatches[i]&&(this._pendingFindMatches[i]=!0,this._extractTextPromises[i].then((function(t){delete e._pendingFindMatches[t],e._calculateMatch(t)})))}if(""!==this._query){if(!this._resumePageIdx){var a=this._offset;if(this._pagesToSearch=n,null!==a.matchIdx){var o=this._pageMatches[a.pageIdx].length;if(!t&&a.matchIdx+1<o||t&&a.matchIdx>0)return a.matchIdx=t?a.matchIdx-1:a.matchIdx+1,void this._updateMatch(!0);this._advanceOffsetPage(t)}this._nextPageMatch()}}else this._updateUIState(c.FOUND)}},{key:"_matchesReady",value:function(e){var t=this._offset,r=e.length,n=this._state.findPrevious;return r?(t.matchIdx=n?r-1:0,this._updateMatch(!0),!0):(this._advanceOffsetPage(n),!!(t.wrapped&&(t.matchIdx=null,this._pagesToSearch<0))&&(this._updateMatch(!1),!0))}},{key:"_nextPageMatch",value:function(){null!==this._resumePageIdx&&console.error("There can only be one pending page.");var e=null;do{var t=this._offset.pageIdx;if(e=this._pageMatches[t],!e){this._resumePageIdx=t;break}}while(!this._matchesReady(e))}},{key:"_advanceOffsetPage",value:function(e){var t=this._offset,r=this._linkService.pagesCount;t.pageIdx=e?t.pageIdx-1:t.pageIdx+1,t.matchIdx=null,this._pagesToSearch--,(t.pageIdx>=r||t.pageIdx<0)&&(t.pageIdx=e?r-1:0,t.wrapped=!0)}},{key:"_updateMatch",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=c.NOT_FOUND,r=this._offset.wrapped;if(this._offset.wrapped=!1,e){var n=this._selected.pageIdx;this._selected.pageIdx=this._offset.pageIdx,this._selected.matchIdx=this._offset.matchIdx,t=r?c.WRAPPED:c.FOUND,-1!==n&&n!==this._selected.pageIdx&&this._updatePage(n)}this._updateUIState(t,this._state.findPrevious),-1!==this._selected.pageIdx&&(this._scrollMatches=!0,this._updatePage(this._selected.pageIdx))}},{key:"_onFindBarClose",value:function(e){var t=this,r=this._pdfDocument;this._firstPageCapability.promise.then((function(){!t._pdfDocument||r&&t._pdfDocument!==r||(t._findTimeout&&(clearTimeout(t._findTimeout),t._findTimeout=null),t._resumePageIdx&&(t._resumePageIdx=null,t._dirtyMatch=!0),t._updateUIState(c.FOUND),t._highlightMatches=!1,t._updateAllPages())}))}},{key:"_requestMatchesCount",value:function(){var e=this._selected,t=e.pageIdx,r=e.matchIdx,n=0,i=this._matchesCountTotal;if(-1!==r){for(var a=0;a<t;a++)n+=this._pageMatches[a]&&this._pageMatches[a].length||0;n+=r+1}return(n<1||n>i)&&(n=i=0),{current:n,total:i}}},{key:"_updateUIResultsCount",value:function(){this._eventBus.dispatch("updatefindmatchescount",{source:this,matchesCount:this._requestMatchesCount()})}},{key:"_updateUIState",value:function(e,t){this._eventBus.dispatch("updatefindcontrolstate",{source:this,state:e,previous:t,matchesCount:this._requestMatchesCount(),rawQuery:this._state?this._state.query:null})}},{key:"highlightMatches",get:function(){return this._highlightMatches}},{key:"pageMatches",get:function(){return this._pageMatches}},{key:"pageMatchesLength",get:function(){return this._pageMatchesLength}},{key:"selected",get:function(){return this._selected}},{key:"state",get:function(){return this._state}},{key:"_query",get:function(){return this._state.query!==this._rawQuery&&(this._rawQuery=this._state.query,this._normalizedQuery=v(this._state.query)),this._normalizedQuery}}]),e}();t.PDFFindController=g},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getCharacterType=p,t.CharacterType=void 0;var n={SPACE:0,ALPHA_LETTER:1,PUNCT:2,HAN_LETTER:3,KATAKANA_LETTER:4,HIRAGANA_LETTER:5,HALFWIDTH_KATAKANA_LETTER:6,THAI_LETTER:7};function i(e){return e<11904}function a(e){return 0===(65408&e)}function o(e){return e>=97&&e<=122||e>=65&&e<=90}function s(e){return e>=48&&e<=57}function u(e){return 32===e||9===e||13===e||10===e}function c(e){return e>=13312&&e<=40959||e>=63744&&e<=64255}function l(e){return e>=12448&&e<=12543}function h(e){return e>=12352&&e<=12447}function f(e){return e>=65376&&e<=65439}function d(e){return 3584===(65408&e)}function p(e){return i(e)?a(e)?u(e)?n.SPACE:o(e)||s(e)||95===e?n.ALPHA_LETTER:n.PUNCT:d(e)?n.THAI_LETTER:160===e?n.SPACE:n.ALPHA_LETTER:c(e)?n.HAN_LETTER:l(e)?n.KATAKANA_LETTER:h(e)?n.HIRAGANA_LETTER:f(e)?n.HALFWIDTH_KATAKANA_LETTER:n.ALPHA_LETTER}t.CharacterType=n},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isDestHashesEqual=b,t.isDestArraysEqual=_,t.PDFHistory=void 0;var n=r(3);function i(e){return i="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function a(e,t){return l(e)||c(e,t)||s(e,t)||o()}function o(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function s(e,t){if(e){if("string"===typeof e)return u(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?u(e,t):void 0}}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function c(e,t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e)){var r=[],n=!0,i=!1,a=void 0;try{for(var o,s=e[Symbol.iterator]();!(n=(o=s.next()).done);n=!0)if(r.push(o.value),t&&r.length===t)break}catch(u){i=!0,a=u}finally{try{n||null==s["return"]||s["return"]()}finally{if(i)throw a}}return r}}function l(e){if(Array.isArray(e))return e}function h(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function f(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function d(e,t,r){return t&&f(e.prototype,t),r&&f(e,r),e}var p=1e3,v=50,g=1e3;function y(){return document.location.hash}var m=function(){function e(t){var r=this,n=t.linkService,i=t.eventBus;h(this,e),this.linkService=n,this.eventBus=i,this._initialized=!1,this._fingerprint="",this.reset(),this._boundEvents=null,this._isViewerInPresentationMode=!1,this.eventBus._on("presentationmodechanged",(function(e){r._isViewerInPresentationMode=e.active||e.switchInProgress})),this.eventBus._on("pagesinit",(function(){r._isPagesLoaded=!1;var e=function e(t){r.eventBus._off("pagesloaded",e),r._isPagesLoaded=!!t.pagesCount};r.eventBus._on("pagesloaded",e)}))}return d(e,[{key:"initialize",value:function(e){var t=e.fingerprint,r=e.resetHistory,n=void 0!==r&&r,i=e.updateUrl,a=void 0!==i&&i;if(t&&"string"===typeof t){this._initialized&&this.reset();var o=""!==this._fingerprint&&this._fingerprint!==t;this._fingerprint=t,this._updateUrl=!0===a,this._initialized=!0,this._bindEvents();var s=window.history.state;if(this._popStateInProgress=!1,this._blockHashChange=0,this._currentHash=y(),this._numPositionUpdates=0,this._uid=this._maxUid=0,this._destination=null,this._position=null,!this._isValidState(s,!0)||n){var u=this._parseCurrentHash(!0),c=u.hash,l=u.page,h=u.rotation;return!c||o||n?void this._pushOrReplaceState(null,!0):void this._pushOrReplaceState({hash:c,page:l,rotation:h},!0)}var f=s.destination;this._updateInternalState(f,s.uid,!0),this._uid>this._maxUid&&(this._maxUid=this._uid),void 0!==f.rotation&&(this._initialRotation=f.rotation),f.dest?(this._initialBookmark=JSON.stringify(f.dest),this._destination.page=null):f.hash?this._initialBookmark=f.hash:f.page&&(this._initialBookmark="page=".concat(f.page))}else console.error('PDFHistory.initialize: The "fingerprint" must be a non-empty string.')}},{key:"reset",value:function(){this._initialized&&(this._pageHide(),this._initialized=!1,this._unbindEvents()),this._updateViewareaTimeout&&(clearTimeout(this._updateViewareaTimeout),this._updateViewareaTimeout=null),this._initialBookmark=null,this._initialRotation=null}},{key:"push",value:function(e){var t=this,r=e.namedDest,n=void 0===r?null:r,i=e.explicitDest,a=e.pageNumber;if(this._initialized)if(n&&"string"!==typeof n)console.error("PDFHistory.push: "+'"'.concat(n,'" is not a valid namedDest parameter.'));else if(Array.isArray(i))if(Number.isInteger(a)&&a>0&&a<=this.linkService.pagesCount||null===a&&!this._destination){var o=n||JSON.stringify(i);if(o){var s=!1;if(this._destination&&(b(this._destination.hash,o)||_(this._destination.dest,i))){if(this._destination.page)return;s=!0}this._popStateInProgress&&!s||(this._pushOrReplaceState({dest:i,hash:o,page:a,rotation:this.linkService.rotation},s),this._popStateInProgress||(this._popStateInProgress=!0,Promise.resolve().then((function(){t._popStateInProgress=!1}))))}}else console.error("PDFHistory.push: "+'"'.concat(a,'" is not a valid pageNumber parameter.'));else console.error("PDFHistory.push: "+'"'.concat(i,'" is not a valid explicitDest parameter.'))}},{key:"pushCurrentPosition",value:function(){this._initialized&&!this._popStateInProgress&&this._tryPushCurrentPosition()}},{key:"back",value:function(){if(this._initialized&&!this._popStateInProgress){var e=window.history.state;this._isValidState(e)&&e.uid>0&&window.history.back()}}},{key:"forward",value:function(){if(this._initialized&&!this._popStateInProgress){var e=window.history.state;this._isValidState(e)&&e.uid<this._maxUid&&window.history.forward()}}},{key:"_pushOrReplaceState",value:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=r||!this._destination,i={fingerprint:this._fingerprint,uid:n?this._uid:this._uid+1,destination:e};if(this._updateInternalState(e,i.uid),this._updateUrl&&e&&e.hash){var a=document.location.href.split("#")[0];a.startsWith("file://")||(t="".concat(a,"#").concat(e.hash))}n?window.history.replaceState(i,"",t):(this._maxUid=this._uid,window.history.pushState(i,"",t))}},{key:"_tryPushCurrentPosition",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this._position){var t=this._position;if(e&&(t=Object.assign(Object.create(null),this._position),t.temporary=!0),this._destination){if(this._destination.temporary)this._pushOrReplaceState(t,!0);else if(this._destination.hash!==t.hash&&(this._destination.page||!(v<=0||this._numPositionUpdates<=v))){var r=!1;if(this._destination.page>=t.first&&this._destination.page<=t.page){if(this._destination.dest||!this._destination.first)return;r=!0}this._pushOrReplaceState(t,r)}}else this._pushOrReplaceState(t)}}},{key:"_isValidState",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e)return!1;if(e.fingerprint!==this._fingerprint){if(!t)return!1;if("string"!==typeof e.fingerprint||e.fingerprint.length!==this._fingerprint.length)return!1;var r=performance.getEntriesByType("navigation"),n=a(r,1),o=n[0];if(!o||"reload"!==o.type)return!1}return!(!Number.isInteger(e.uid)||e.uid<0)&&(null!==e.destination&&"object"===i(e.destination))}},{key:"_updateInternalState",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this._updateViewareaTimeout&&(clearTimeout(this._updateViewareaTimeout),this._updateViewareaTimeout=null),r&&e&&e.temporary&&delete e.temporary,this._destination=e,this._uid=t,this._numPositionUpdates=0}},{key:"_parseCurrentHash",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=unescape(y()).substring(1),r=(0,n.parseQueryString)(t),i=r.nameddest||"",a=0|r.page;return Number.isInteger(a)&&a>0&&a<=this.linkService.pagesCount&&!(e&&i.length>0)||(a=null),{hash:t,page:a,rotation:this.linkService.rotation}}},{key:"_updateViewarea",value:function(e){var t=this,r=e.location;this._updateViewareaTimeout&&(clearTimeout(this._updateViewareaTimeout),this._updateViewareaTimeout=null),this._position={hash:this._isViewerInPresentationMode?"page=".concat(r.pageNumber):r.pdfOpenParams.substring(1),page:this.linkService.page,first:r.pageNumber,rotation:r.rotation},this._popStateInProgress||(v>0&&this._isPagesLoaded&&this._destination&&!this._destination.page&&this._numPositionUpdates++,g>0&&(this._updateViewareaTimeout=setTimeout((function(){t._popStateInProgress||t._tryPushCurrentPosition(!0),t._updateViewareaTimeout=null}),g)))}},{key:"_popState",value:function(e){var t=this,r=e.state,i=y(),a=this._currentHash!==i;if(this._currentHash=i,r){if(this._isValidState(r)){this._popStateInProgress=!0,a&&(this._blockHashChange++,(0,n.waitOnEventOrTimeout)({target:window,name:"hashchange",delay:p}).then((function(){t._blockHashChange--})));var o=r.destination;this._updateInternalState(o,r.uid,!0),this._uid>this._maxUid&&(this._maxUid=this._uid),(0,n.isValidRotation)(o.rotation)&&(this.linkService.rotation=o.rotation),o.dest?this.linkService.navigateTo(o.dest):o.hash?this.linkService.setHash(o.hash):o.page&&(this.linkService.page=o.page),Promise.resolve().then((function(){t._popStateInProgress=!1}))}}else{this._uid++;var s=this._parseCurrentHash(),u=s.hash,c=s.page,l=s.rotation;this._pushOrReplaceState({hash:u,page:c,rotation:l},!0)}}},{key:"_pageHide",value:function(){this._destination&&!this._destination.temporary||this._tryPushCurrentPosition()}},{key:"_bindEvents",value:function(){this._boundEvents||(this._boundEvents={updateViewarea:this._updateViewarea.bind(this),popState:this._popState.bind(this),pageHide:this._pageHide.bind(this)},this.eventBus._on("updateviewarea",this._boundEvents.updateViewarea),window.addEventListener("popstate",this._boundEvents.popState),window.addEventListener("pagehide",this._boundEvents.pageHide))}},{key:"_unbindEvents",value:function(){this._boundEvents&&(this.eventBus._off("updateviewarea",this._boundEvents.updateViewarea),window.removeEventListener("popstate",this._boundEvents.popState),window.removeEventListener("pagehide",this._boundEvents.pageHide),this._boundEvents=null)}},{key:"popStateInProgress",get:function(){return this._initialized&&(this._popStateInProgress||this._blockHashChange>0)}},{key:"initialBookmark",get:function(){return this._initialized?this._initialBookmark:null}},{key:"initialRotation",get:function(){return this._initialized?this._initialRotation:null}}]),e}();function b(e,t){if("string"!==typeof e||"string"!==typeof t)return!1;if(e===t)return!0;var r=(0,n.parseQueryString)(e),i=r.nameddest;return i===t}function _(e,t){function r(e,t){if(i(e)!==i(t))return!1;if(Array.isArray(e)||Array.isArray(t))return!1;if(null!==e&&"object"===i(e)&&null!==t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(var n in e)if(!r(e[n],t[n]))return!1;return!0}return e===t||Number.isNaN(e)&&Number.isNaN(t)}if(!Array.isArray(e)||!Array.isArray(t))return!1;if(e.length!==t.length)return!1;for(var n=0,a=e.length;n<a;n++)if(!r(e[n],t[n]))return!1;return!0}t.PDFHistory=m},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PDFPageView=void 0;var n=u(r(4)),i=r(3),a=r(2),o=r(17),s=r(10);function u(e){return e&&e.__esModule?e:{default:e}}function c(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(c){return void r(c)}s.done?t(u):Promise.resolve(u).then(n,i)}function l(e){return function(){var t=this,r=arguments;return new Promise((function(n,i){var a=e.apply(t,r);function o(e){c(a,n,i,o,s,"next",e)}function s(e){c(a,n,i,o,s,"throw",e)}o(void 0)}))}}function h(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function f(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function d(e,t,r){return t&&f(e.prototype,t),r&&f(e,r),e}var p=s.viewerCompatibilityParams.maxCanvasPixels||16777216,v=function(){function e(t){h(this,e);var r=t.container,n=t.defaultViewport;this.id=t.id,this.renderingId="page"+this.id,this.pdfPage=null,this.pageLabel=null,this.rotation=0,this.scale=t.scale||i.DEFAULT_SCALE,this.viewport=n,this.pdfPageRotate=n.rotation,this._annotationStorage=t.annotationStorage||null,this._optionalContentConfigPromise=t.optionalContentConfigPromise||null,this.hasRestrictedScaling=!1,this.textLayerMode=Number.isInteger(t.textLayerMode)?t.textLayerMode:i.TextLayerMode.ENABLE,this.imageResourcesPath=t.imageResourcesPath||"",this.renderInteractiveForms="boolean"!==typeof t.renderInteractiveForms||t.renderInteractiveForms,this.useOnlyCssZoom=t.useOnlyCssZoom||!1,this.maxCanvasPixels=t.maxCanvasPixels||p,this.eventBus=t.eventBus,this.renderingQueue=t.renderingQueue,this.textLayerFactory=t.textLayerFactory,this.annotationLayerFactory=t.annotationLayerFactory,this.renderer=t.renderer||i.RendererType.CANVAS,this.enableWebGL=t.enableWebGL||!1,this.l10n=t.l10n||i.NullL10n,this.paintTask=null,this.paintedViewportMap=new WeakMap,this.renderingState=o.RenderingStates.INITIAL,this.resume=null,this.error=null,this.annotationLayer=null,this.textLayer=null,this.zoomLayer=null;var a=document.createElement("div");a.className="page",a.style.width=Math.floor(this.viewport.width)+"px",a.style.height=Math.floor(this.viewport.height)+"px",a.setAttribute("data-page-number",this.id),this.div=a,r.appendChild(a)}return d(e,[{key:"setPdfPage",value:function(e){this.pdfPage=e,this.pdfPageRotate=e.rotate;var t=(this.rotation+this.pdfPageRotate)%360;this.viewport=e.getViewport({scale:this.scale*i.CSS_UNITS,rotation:t}),this.stats=e.stats,this.reset()}},{key:"destroy",value:function(){this.reset(),this.pdfPage&&this.pdfPage.cleanup()}},{key:"_renderAnnotationLayer",value:function(){var e=l(n["default"].mark((function e(){var t;return n["default"].wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=null,e.prev=1,e.next=4,this.annotationLayer.render(this.viewport,"display");case 4:e.next=9;break;case 6:e.prev=6,e.t0=e["catch"](1),t=e.t0;case 9:return e.prev=9,this.eventBus.dispatch("annotationlayerrendered",{source:this,pageNumber:this.id,error:t}),e.finish(9);case 12:case"end":return e.stop()}}),e,this,[[1,6,9,12]])})));function t(){return e.apply(this,arguments)}return t}()},{key:"_resetZoomLayer",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.zoomLayer){var t=this.zoomLayer.firstChild;this.paintedViewportMap["delete"](t),t.width=0,t.height=0,e&&this.zoomLayer.remove(),this.zoomLayer=null}}},{key:"reset",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.cancelRendering(t),this.renderingState=o.RenderingStates.INITIAL;var r=this.div;r.style.width=Math.floor(this.viewport.width)+"px",r.style.height=Math.floor(this.viewport.height)+"px";for(var n=r.childNodes,i=e&&this.zoomLayer||null,a=t&&this.annotationLayer&&this.annotationLayer.div||null,s=n.length-1;s>=0;s--){var u=n[s];i!==u&&a!==u&&r.removeChild(u)}r.removeAttribute("data-loaded"),a?this.annotationLayer.hide():this.annotationLayer&&(this.annotationLayer.cancel(),this.annotationLayer=null),i||(this.canvas&&(this.paintedViewportMap["delete"](this.canvas),this.canvas.width=0,this.canvas.height=0,delete this.canvas),this._resetZoomLayer()),this.svg&&(this.paintedViewportMap["delete"](this.svg),delete this.svg),this.loadingIconDiv=document.createElement("div"),this.loadingIconDiv.className="loadingIcon",r.appendChild(this.loadingIconDiv)}},{key:"update",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;this.scale=e||this.scale,"undefined"!==typeof t&&(this.rotation=t),r instanceof Promise&&(this._optionalContentConfigPromise=r);var n=(this.rotation+this.pdfPageRotate)%360;if(this.viewport=this.viewport.clone({scale:this.scale*i.CSS_UNITS,rotation:n}),this.svg)return this.cssTransform(this.svg,!0),void this.eventBus.dispatch("pagerendered",{source:this,pageNumber:this.id,cssTransform:!0,timestamp:performance.now()});var a=!1;if(this.canvas&&this.maxCanvasPixels>0){var o=this.outputScale;(Math.floor(this.viewport.width)*o.sx|0)*(Math.floor(this.viewport.height)*o.sy|0)>this.maxCanvasPixels&&(a=!0)}if(this.canvas){if(this.useOnlyCssZoom||this.hasRestrictedScaling&&a)return this.cssTransform(this.canvas,!0),void this.eventBus.dispatch("pagerendered",{source:this,pageNumber:this.id,cssTransform:!0,timestamp:performance.now()});this.zoomLayer||this.canvas.hasAttribute("hidden")||(this.zoomLayer=this.canvas.parentNode,this.zoomLayer.style.position="absolute")}this.zoomLayer&&this.cssTransform(this.zoomLayer.firstChild),this.reset(!0,!0)}},{key:"cancelRendering",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.paintTask&&(this.paintTask.cancel(),this.paintTask=null),this.resume=null,this.textLayer&&(this.textLayer.cancel(),this.textLayer=null),!e&&this.annotationLayer&&(this.annotationLayer.cancel(),this.annotationLayer=null)}},{key:"cssTransform",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=this.viewport.width,n=this.viewport.height,i=this.div;e.style.width=e.parentNode.style.width=i.style.width=Math.floor(r)+"px",e.style.height=e.parentNode.style.height=i.style.height=Math.floor(n)+"px";var a=this.viewport.rotation-this.paintedViewportMap.get(e).rotation,o=Math.abs(a),s=1,u=1;90!==o&&270!==o||(s=n/r,u=r/n);var c="rotate("+a+"deg) scale("+s+","+u+")";if(e.style.transform=c,this.textLayer){var l=this.textLayer.viewport,h=this.viewport.rotation-l.rotation,f=Math.abs(h),d=r/l.width;90!==f&&270!==f||(d=r/l.height);var p,v,g=this.textLayer.textLayerDiv;switch(f){case 0:p=v=0;break;case 90:p=0,v="-"+g.style.height;break;case 180:p="-"+g.style.width,v="-"+g.style.height;break;case 270:p="-"+g.style.width,v=0;break;default:console.error("Bad rotation value.");break}g.style.transform="rotate("+f+"deg) scale("+d+", "+d+") translate("+p+", "+v+")",g.style.transformOrigin="0% 0%"}t&&this.annotationLayer&&this._renderAnnotationLayer()}},{key:"getPagePoint",value:function(e,t){return this.viewport.convertToPdfPoint(e,t)}},{key:"draw",value:function(){var e=this;this.renderingState!==o.RenderingStates.INITIAL&&(console.error("Must be in new state before drawing"),this.reset());var t=this.div,r=this.pdfPage;if(!r)return this.renderingState=o.RenderingStates.FINISHED,this.loadingIconDiv&&(t.removeChild(this.loadingIconDiv),delete this.loadingIconDiv),Promise.reject(new Error("pdfPage is not loaded"));this.renderingState=o.RenderingStates.RUNNING;var s=document.createElement("div");s.style.width=t.style.width,s.style.height=t.style.height,s.classList.add("canvasWrapper"),this.annotationLayer&&this.annotationLayer.div?t.insertBefore(s,this.annotationLayer.div):t.appendChild(s);var u=null;if(this.textLayerMode!==i.TextLayerMode.DISABLE&&this.textLayerFactory){var c=document.createElement("div");c.className="textLayer",c.style.width=s.style.width,c.style.height=s.style.height,this.annotationLayer&&this.annotationLayer.div?t.insertBefore(c,this.annotationLayer.div):t.appendChild(c),u=this.textLayerFactory.createTextLayerBuilder(c,this.id-1,this.viewport,this.textLayerMode===i.TextLayerMode.ENABLE_ENHANCE,this.eventBus)}this.textLayer=u;var h=null;this.renderingQueue&&(h=function(t){if(!e.renderingQueue.isHighestPriority(e))return e.renderingState=o.RenderingStates.PAUSED,void(e.resume=function(){e.renderingState=o.RenderingStates.RUNNING,t()});t()});var f=function(){var i=l(n["default"].mark((function i(s){return n["default"].wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(d===e.paintTask&&(e.paintTask=null),!(s instanceof a.RenderingCancelledException)){n.next=4;break}return e.error=null,n.abrupt("return");case 4:if(e.renderingState=o.RenderingStates.FINISHED,e.loadingIconDiv&&(t.removeChild(e.loadingIconDiv),delete e.loadingIconDiv),e._resetZoomLayer(!0),e.error=s,e.stats=r.stats,e.eventBus.dispatch("pagerendered",{source:e,pageNumber:e.id,cssTransform:!1,timestamp:performance.now()}),!s){n.next=12;break}throw s;case 12:case"end":return n.stop()}}),i)})));return function(e){return i.apply(this,arguments)}}(),d=this.renderer===i.RendererType.SVG?this.paintOnSvg(s):this.paintOnCanvas(s);d.onRenderContinue=h,this.paintTask=d;var p=d.promise.then((function(){return f(null).then((function(){if(u){var e=r.streamTextContent({normalizeWhitespace:!0});u.setTextContentStream(e),u.render()}}))}),(function(e){return f(e)}));return this.annotationLayerFactory&&(this.annotationLayer||(this.annotationLayer=this.annotationLayerFactory.createAnnotationLayerBuilder(t,r,this._annotationStorage,this.imageResourcesPath,this.renderInteractiveForms,this.l10n)),this._renderAnnotationLayer()),t.setAttribute("data-loaded",!0),this.eventBus.dispatch("pagerender",{source:this,pageNumber:this.id}),p}},{key:"paintOnCanvas",value:function(e){var t=(0,a.createPromiseCapability)(),r={promise:t.promise,onRenderContinue:function(e){e()},cancel:function(){m.cancel()}},n=this.viewport,o=document.createElement("canvas");this.l10n.get("page_canvas",{page:this.id},"Page {{page}}").then((function(e){o.setAttribute("aria-label",e)})),o.setAttribute("hidden","hidden");var s=!0,u=function(){s&&(o.removeAttribute("hidden"),s=!1)};e.appendChild(o),this.canvas=o,o.mozOpaque=!0;var c=o.getContext("2d",{alpha:!1}),l=(0,i.getOutputScale)(c);if(this.outputScale=l,this.useOnlyCssZoom){var h=n.clone({scale:i.CSS_UNITS});l.sx*=h.width/n.width,l.sy*=h.height/n.height,l.scaled=!0}if(this.maxCanvasPixels>0){var f=n.width*n.height,d=Math.sqrt(this.maxCanvasPixels/f);l.sx>d||l.sy>d?(l.sx=d,l.sy=d,l.scaled=!0,this.hasRestrictedScaling=!0):this.hasRestrictedScaling=!1}var p=(0,i.approximateFraction)(l.sx),v=(0,i.approximateFraction)(l.sy);o.width=(0,i.roundToDivide)(n.width*l.sx,p[0]),o.height=(0,i.roundToDivide)(n.height*l.sy,v[0]),o.style.width=(0,i.roundToDivide)(n.width,p[1])+"px",o.style.height=(0,i.roundToDivide)(n.height,v[1])+"px",this.paintedViewportMap.set(o,n);var g=l.scaled?[l.sx,0,0,l.sy,0,0]:null,y={canvasContext:c,transform:g,viewport:this.viewport,enableWebGL:this.enableWebGL,renderInteractiveForms:this.renderInteractiveForms,optionalContentConfigPromise:this._optionalContentConfigPromise},m=this.pdfPage.render(y);return m.onContinue=function(e){u(),r.onRenderContinue?r.onRenderContinue(e):e()},m.promise.then((function(){u(),t.resolve(void 0)}),(function(e){u(),t.reject(e)})),r}},{key:"paintOnSvg",value:function(e){var t=this,r=!1,n=function(){if(r)throw new a.RenderingCancelledException("Rendering cancelled, page ".concat(t.id),"svg")},s=this.pdfPage,u=this.viewport.clone({scale:i.CSS_UNITS}),c=s.getOperatorList().then((function(r){n();var i=new a.SVGGraphics(s.commonObjs,s.objs);return i.getSVG(r,u).then((function(r){n(),t.svg=r,t.paintedViewportMap.set(r,u),r.style.width=e.style.width,r.style.height=e.style.height,t.renderingState=o.RenderingStates.FINISHED,e.appendChild(r)}))}));return{promise:c,onRenderContinue:function(e){e()},cancel:function(){r=!0}}}},{key:"setPageLabel",value:function(e){this.pageLabel="string"===typeof e?e:null,null!==this.pageLabel?this.div.setAttribute("data-page-label",this.pageLabel):this.div.removeAttribute("data-page-label")}},{key:"width",get:function(){return this.viewport.width}},{key:"height",get:function(){return this.viewport.height}}]),e}();t.PDFPageView=v},function(e,t,r){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function a(e,t,r){return t&&i(e.prototype,t),r&&i(e,r),e}Object.defineProperty(t,"__esModule",{value:!0}),t.PDFRenderingQueue=t.RenderingStates=void 0;var o=3e4,s={INITIAL:0,RUNNING:1,PAUSED:2,FINISHED:3};t.RenderingStates=s;var u=function(){function e(){n(this,e),this.pdfViewer=null,this.pdfThumbnailViewer=null,this.onIdle=null,this.highestPriorityPage=null,this.idleTimeout=null,this.printing=!1,this.isThumbnailViewEnabled=!1}return a(e,[{key:"setViewer",value:function(e){this.pdfViewer=e}},{key:"setThumbnailViewer",value:function(e){this.pdfThumbnailViewer=e}},{key:"isHighestPriority",value:function(e){return this.highestPriorityPage===e.renderingId}},{key:"renderHighestPriority",value:function(e){this.idleTimeout&&(clearTimeout(this.idleTimeout),this.idleTimeout=null),this.pdfViewer.forceRendering(e)||this.pdfThumbnailViewer&&this.isThumbnailViewEnabled&&this.pdfThumbnailViewer.forceRendering()||this.printing||this.onIdle&&(this.idleTimeout=setTimeout(this.onIdle.bind(this),o))}},{key:"getHighestPriority",value:function(e,t,r){var n=e.views,i=n.length;if(0===i)return null;for(var a=0;a<i;++a){var o=n[a].view;if(!this.isViewFinished(o))return o}if(r){var s=e.last.id;if(t[s]&&!this.isViewFinished(t[s]))return t[s]}else{var u=e.first.id-2;if(t[u]&&!this.isViewFinished(t[u]))return t[u]}return null}},{key:"isViewFinished",value:function(e){return e.renderingState===s.FINISHED}},{key:"renderView",value:function(e){var t=this;switch(e.renderingState){case s.FINISHED:return!1;case s.PAUSED:this.highestPriorityPage=e.renderingId,e.resume();break;case s.RUNNING:this.highestPriorityPage=e.renderingId;break;case s.INITIAL:this.highestPriorityPage=e.renderingId,e.draw()["finally"]((function(){t.renderHighestPriority()}))["catch"]((function(e){console.error('renderView: "'.concat(e,'"'))}));break}return!0}}]),e}();t.PDFRenderingQueue=u},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PDFSinglePageViewer=void 0;var n=r(19),i=r(2);function a(e){return a="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function u(e,t,r){return t&&s(e.prototype,t),r&&s(e,r),e}function c(e,t,r){return c="undefined"!==typeof Reflect&&Reflect.get?Reflect.get:function(e,t,r){var n=l(e,t);if(n){var i=Object.getOwnPropertyDescriptor(n,t);return i.get?i.get.call(r):i.value}},c(e,t,r||e)}function l(e,t){while(!Object.prototype.hasOwnProperty.call(e,t))if(e=y(e),null===e)break;return e}function h(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&f(e,t)}function f(e,t){return f=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},f(e,t)}function d(e){var t=g();return function(){var r,n=y(e);if(t){var i=y(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return p(this,r)}}function p(e,t){return!t||"object"!==a(t)&&"function"!==typeof t?v(e):t}function v(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function g(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function y(e){return y=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},y(e)}var m=function(e){h(r,e);var t=d(r);function r(e){var n;return o(this,r),n=t.call(this,e),n.eventBus._on("pagesinit",(function(e){n._ensurePageViewVisible()})),n}return u(r,[{key:"_resetView",value:function(){c(y(r.prototype),"_resetView",this).call(this),this._previousPageNumber=1,this._shadowViewer=document.createDocumentFragment(),this._updateScrollDown=null}},{key:"_ensurePageViewVisible",value:function(){var e=this._pages[this._currentPageNumber-1],t=this._pages[this._previousPageNumber-1],r=this.viewer.childNodes;switch(r.length){case 0:this.viewer.appendChild(e.div);break;case 1:if(r[0]!==t.div)throw new Error("_ensurePageViewVisible: Unexpected previously visible page.");if(e===t)break;this._shadowViewer.appendChild(t.div),this.viewer.appendChild(e.div),this.container.scrollTop=0;break;default:throw new Error("_ensurePageViewVisible: Only one page should be visible at a time.")}this._previousPageNumber=this._currentPageNumber}},{key:"_scrollUpdate",value:function(){this._updateScrollDown&&this._updateScrollDown(),c(y(r.prototype),"_scrollUpdate",this).call(this)}},{key:"_scrollIntoView",value:function(e){var t=this,n=e.pageDiv,i=e.pageSpot,a=void 0===i?null:i,o=e.pageNumber,s=void 0===o?null:o;s&&this._setCurrentPageNumber(s);var u=this._currentPageNumber>=this._previousPageNumber;this._ensurePageViewVisible(),this.update(),c(y(r.prototype),"_scrollIntoView",this).call(this,{pageDiv:n,pageSpot:a,pageNumber:s}),this._updateScrollDown=function(){t.scroll.down=u,t._updateScrollDown=null}}},{key:"_getVisiblePages",value:function(){return this._getCurrentVisiblePage()}},{key:"_updateHelper",value:function(e){}},{key:"_updateScrollMode",value:function(){}},{key:"_updateSpreadMode",value:function(){}},{key:"_viewerElement",get:function(){return(0,i.shadow)(this,"_viewerElement",this._shadowViewer)}},{key:"_isScrollModeHorizontal",get:function(){return(0,i.shadow)(this,"_isScrollModeHorizontal",!1)}}]),r}(n.BaseViewer);t.PDFSinglePageViewer=m},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.BaseViewer=void 0;var n=r(3),i=r(17),a=r(1),o=r(2),s=r(16),u=r(7),c=r(8);function l(e,t){var r;if("undefined"===typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(r=h(e))||t&&e&&"number"===typeof e.length){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){r=e[Symbol.iterator]()},n:function(){var e=r.next();return o=e.done,e},e:function(e){s=!0,a=e},f:function(){try{o||null==r["return"]||r["return"]()}finally{if(s)throw a}}}}function h(e,t){if(e){if("string"===typeof e)return f(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?f(e,t):void 0}}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function d(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function p(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function v(e,t,r){return t&&p(e.prototype,t),r&&p(e,r),e}var g=10;function y(e){var t=[];this.push=function(r){var n=t.indexOf(r);n>=0&&t.splice(n,1),t.push(r),t.length>e&&t.shift().destroy()},this.resize=function(r,i){if(e=r,i){for(var a=new Set,o=0,s=i.length;o<s;++o)a.add(i[o].id);(0,n.moveToEndOfArray)(t,(function(e){return a.has(e.id)}))}while(t.length>e)t.shift().destroy()}}function m(e,t){return t===e||Math.abs(t-e)<1e-15}var b=function(){function e(t){var r=this;if(d(this,e),this.constructor===e)throw new Error("Cannot initialize BaseViewer.");if(this._name=this.constructor.name,this.container=t.container,this.viewer=t.viewer||t.container.firstElementChild,!(this.container instanceof HTMLDivElement&&this.viewer instanceof HTMLDivElement))throw new Error("Invalid `container` and/or `viewer` option.");this.eventBus=t.eventBus,this.linkService=t.linkService||new u.SimpleLinkService,this.downloadManager=t.downloadManager||null,this.findController=t.findController||null,this.removePageBorders=t.removePageBorders||!1,this.textLayerMode=Number.isInteger(t.textLayerMode)?t.textLayerMode:n.TextLayerMode.ENABLE,this.imageResourcesPath=t.imageResourcesPath||"",this.renderInteractiveForms="boolean"!==typeof t.renderInteractiveForms||t.renderInteractiveForms,this.enablePrintAutoRotate=t.enablePrintAutoRotate||!1,this.renderer=t.renderer||n.RendererType.CANVAS,this.enableWebGL=t.enableWebGL||!1,this.useOnlyCssZoom=t.useOnlyCssZoom||!1,this.maxCanvasPixels=t.maxCanvasPixels,this.l10n=t.l10n||n.NullL10n,this.defaultRenderingQueue=!t.renderingQueue,this.defaultRenderingQueue?(this.renderingQueue=new i.PDFRenderingQueue,this.renderingQueue.setViewer(this)):this.renderingQueue=t.renderingQueue,this.scroll=(0,n.watchScroll)(this.container,this._scrollUpdate.bind(this)),this.presentationModeState=n.PresentationModeState.UNKNOWN,this._onBeforeDraw=this._onAfterDraw=null,this._resetView(),this.removePageBorders&&this.viewer.classList.add("removePageBorders"),Promise.resolve().then((function(){r.eventBus.dispatch("baseviewerinit",{source:r})}))}return v(e,[{key:"getPageView",value:function(e){return this._pages[e]}},{key:"_setCurrentPageNumber",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return this._currentPageNumber===e?(t&&this._resetCurrentPageView(),!0):0<e&&e<=this.pagesCount&&(this._currentPageNumber=e,this.eventBus.dispatch("pagechanging",{source:this,pageNumber:e,pageLabel:this._pageLabels&&this._pageLabels[e-1]}),t&&this._resetCurrentPageView(),!0)}},{key:"_onePageRenderedOrForceFetch",value:function(){return this.container.offsetParent&&0!==this._getVisiblePages().views.length?this._onePageRenderedCapability.promise:Promise.resolve()}},{key:"setDocument",value:function(e){var t=this;if(this.pdfDocument&&(this._cancelRendering(),this._resetView(),this.findController&&this.findController.setDocument(null)),this.pdfDocument=e,e){var r=e.numPages,i=e.getPage(1),a=e.annotationStorage,o=e.getOptionalContentConfig();this._pagesCapability.promise.then((function(){t.eventBus.dispatch("pagesloaded",{source:t,pagesCount:r})})),this._onBeforeDraw=function(e){var r=t._pages[e.pageNumber-1];r&&t._buffer.push(r)},this.eventBus._on("pagerender",this._onBeforeDraw),this._onAfterDraw=function(e){e.cssTransform||t._onePageRenderedCapability.settled||(t._onePageRenderedCapability.resolve(),t.eventBus._off("pagerendered",t._onAfterDraw),t._onAfterDraw=null)},this.eventBus._on("pagerendered",this._onAfterDraw),i.then((function(i){t._firstPageCapability.resolve(i),t._optionalContentConfigPromise=o;for(var u=t.currentScale,c=i.getViewport({scale:u*n.CSS_UNITS}),l=t.textLayerMode!==n.TextLayerMode.DISABLE?t:null,h=1;h<=r;++h){var f=new s.PDFPageView({container:t._viewerElement,eventBus:t.eventBus,id:h,scale:u,defaultViewport:c.clone(),annotationStorage:a,optionalContentConfigPromise:o,renderingQueue:t.renderingQueue,textLayerFactory:l,textLayerMode:t.textLayerMode,annotationLayerFactory:t,imageResourcesPath:t.imageResourcesPath,renderInteractiveForms:t.renderInteractiveForms,renderer:t.renderer,enableWebGL:t.enableWebGL,useOnlyCssZoom:t.useOnlyCssZoom,maxCanvasPixels:t.maxCanvasPixels,l10n:t.l10n});t._pages.push(f)}var d=t._pages[0];d&&(d.setPdfPage(i),t.linkService.cachePageRef(1,i.ref)),t._spreadMode!==n.SpreadMode.NONE&&t._updateSpreadMode(),t._onePageRenderedOrForceFetch().then((function(){if(t.findController&&t.findController.setDocument(e),e.loadingParams.disableAutoFetch||r>7500)t._pagesCapability.resolve();else{var n=r-1;if(n<=0)t._pagesCapability.resolve();else for(var i=function(r){e.getPage(r).then((function(e){var i=t._pages[r-1];i.pdfPage||i.setPdfPage(e),t.linkService.cachePageRef(r,e.ref),0===--n&&t._pagesCapability.resolve()}),(function(e){console.error("Unable to get page ".concat(r," to initialize viewer"),e),0===--n&&t._pagesCapability.resolve()}))},a=2;a<=r;++a)i(a)}})),t.eventBus.dispatch("pagesinit",{source:t}),t.defaultRenderingQueue&&t.update()}))["catch"]((function(e){console.error("Unable to initialize viewer",e)}))}}},{key:"setPageLabels",value:function(e){if(this.pdfDocument){e?Array.isArray(e)&&this.pdfDocument.numPages===e.length?this._pageLabels=e:(this._pageLabels=null,console.error("".concat(this._name,".setPageLabels: Invalid page labels."))):this._pageLabels=null;for(var t=0,r=this._pages.length;t<r;t++){var n=this._pages[t],i=this._pageLabels&&this._pageLabels[t];n.setPageLabel(i)}}}},{key:"_resetView",value:function(){this._pages=[],this._currentPageNumber=1,this._currentScale=n.UNKNOWN_SCALE,this._currentScaleValue=null,this._pageLabels=null,this._buffer=new y(g),this._location=null,this._pagesRotation=0,this._optionalContentConfigPromise=null,this._pagesRequests=new WeakMap,this._firstPageCapability=(0,o.createPromiseCapability)(),this._onePageRenderedCapability=(0,o.createPromiseCapability)(),this._pagesCapability=(0,o.createPromiseCapability)(),this._scrollMode=n.ScrollMode.VERTICAL,this._spreadMode=n.SpreadMode.NONE,this._onBeforeDraw&&(this.eventBus._off("pagerender",this._onBeforeDraw),this._onBeforeDraw=null),this._onAfterDraw&&(this.eventBus._off("pagerendered",this._onAfterDraw),this._onAfterDraw=null),this.viewer.textContent="",this._updateScrollMode()}},{key:"_scrollUpdate",value:function(){0!==this.pagesCount&&this.update()}},{key:"_scrollIntoView",value:function(e){var t=e.pageDiv,r=e.pageSpot,i=void 0===r?null:r;e.pageNumber;(0,n.scrollIntoView)(t,i)}},{key:"_setScaleUpdatePages",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(this._currentScaleValue=t.toString(),m(this._currentScale,e))n&&this.eventBus.dispatch("scalechanging",{source:this,scale:e,presetValue:t});else{for(var i=0,a=this._pages.length;i<a;i++)this._pages[i].update(e);if(this._currentScale=e,!r){var o,s=this._currentPageNumber;!this._location||this.isInPresentationMode||this.isChangingPresentationMode||(s=this._location.pageNumber,o=[null,{name:"XYZ"},this._location.left,this._location.top,null]),this.scrollPageIntoView({pageNumber:s,destArray:o,allowNegativeOffset:!0})}this.eventBus.dispatch("scalechanging",{source:this,scale:e,presetValue:n?t:void 0}),this.defaultRenderingQueue&&this.update()}}},{key:"_setScale",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=parseFloat(e);if(r>0)this._setScaleUpdatePages(r,e,t,!1);else{var i=this._pages[this._currentPageNumber-1];if(!i)return;var a=this.isInPresentationMode||this.removePageBorders,o=a?0:n.SCROLLBAR_PADDING,s=a?0:n.VERTICAL_PADDING;if(!a&&this._isScrollModeHorizontal){var u=[s,o];o=u[0],s=u[1]}var c=(this.container.clientWidth-o)/i.width*i.scale,l=(this.container.clientHeight-s)/i.height*i.scale;switch(e){case"page-actual":r=1;break;case"page-width":r=c;break;case"page-height":r=l;break;case"page-fit":r=Math.min(c,l);break;case"auto":var h=(0,n.isPortraitOrientation)(i)?c:Math.min(l,c);r=Math.min(n.MAX_AUTO_SCALE,h);break;default:return void console.error("".concat(this._name,'._setScale: "').concat(e,'" is an unknown zoom value.'))}this._setScaleUpdatePages(r,e,t,!0)}}},{key:"_resetCurrentPageView",value:function(){this.isInPresentationMode&&this._setScale(this._currentScaleValue,!0);var e=this._pages[this._currentPageNumber-1];this._scrollIntoView({pageDiv:e.div})}},{key:"scrollPageIntoView",value:function(e){var t=e.pageNumber,r=e.destArray,i=void 0===r?null:r,a=e.allowNegativeOffset,o=void 0!==a&&a,s=e.ignoreDestinationZoom,u=void 0!==s&&s;if(this.pdfDocument){var c=Number.isInteger(t)&&this._pages[t-1];if(c)if(!this.isInPresentationMode&&i){var l,h,f=0,d=0,p=0,v=0,g=c.rotation%180!==0,y=(g?c.height:c.width)/c.scale/n.CSS_UNITS,m=(g?c.width:c.height)/c.scale/n.CSS_UNITS,b=0;switch(i[1].name){case"XYZ":f=i[2],d=i[3],b=i[4],f=null!==f?f:0,d=null!==d?d:m;break;case"Fit":case"FitB":b="page-fit";break;case"FitH":case"FitBH":d=i[2],b="page-width",null===d&&this._location&&(f=this._location.left,d=this._location.top);break;case"FitV":case"FitBV":f=i[2],p=y,v=m,b="page-height";break;case"FitR":f=i[2],d=i[3],p=i[4]-f,v=i[5]-d;var _=this.removePageBorders?0:n.SCROLLBAR_PADDING,w=this.removePageBorders?0:n.VERTICAL_PADDING;l=(this.container.clientWidth-_)/p/n.CSS_UNITS,h=(this.container.clientHeight-w)/v/n.CSS_UNITS,b=Math.min(Math.abs(l),Math.abs(h));break;default:return void console.error("".concat(this._name,".scrollPageIntoView: ")+'"'.concat(i[1].name,'" is not a valid destination type.'))}if(u||(b&&b!==this._currentScale?this.currentScaleValue=b:this._currentScale===n.UNKNOWN_SCALE&&(this.currentScaleValue=n.DEFAULT_SCALE_VALUE)),"page-fit"!==b||i[4]){var S=[c.viewport.convertToViewportPoint(f,d),c.viewport.convertToViewportPoint(f+p,d+v)],A=Math.min(S[0][0],S[1][0]),k=Math.min(S[0][1],S[1][1]);o||(A=Math.max(A,0),k=Math.max(k,0)),this._scrollIntoView({pageDiv:c.div,pageSpot:{left:A,top:k},pageNumber:t})}else this._scrollIntoView({pageDiv:c.div,pageNumber:t})}else this._setCurrentPageNumber(t,!0);else console.error("".concat(this._name,".scrollPageIntoView: ")+'"'.concat(t,'" is not a valid pageNumber parameter.'))}}},{key:"_updateLocation",value:function(e){var t=this._currentScale,r=this._currentScaleValue,n=parseFloat(r)===t?Math.round(1e4*t)/100:r,i=e.id,a="#page="+i;a+="&zoom="+n;var o=this._pages[i-1],s=this.container,u=o.getPagePoint(s.scrollLeft-e.x,s.scrollTop-e.y),c=Math.round(u[0]),l=Math.round(u[1]);a+=","+c+","+l,this._location={pageNumber:i,scale:n,top:l,left:c,rotation:this._pagesRotation,pdfOpenParams:a}}},{key:"_updateHelper",value:function(e){throw new Error("Not implemented: _updateHelper")}},{key:"update",value:function(){var e=this._getVisiblePages(),t=e.views,r=t.length;if(0!==r){var n=Math.max(g,2*r+1);this._buffer.resize(n,t),this.renderingQueue.renderHighestPriority(e),this._updateHelper(t),this._updateLocation(e.first),this.eventBus.dispatch("updateviewarea",{source:this,location:this._location})}}},{key:"containsElement",value:function(e){return this.container.contains(e)}},{key:"focus",value:function(){this.container.focus()}},{key:"_getCurrentVisiblePage",value:function(){if(!this.pagesCount)return{views:[]};var e=this._pages[this._currentPageNumber-1],t=e.div,r={id:e.id,x:t.offsetLeft+t.clientLeft,y:t.offsetTop+t.clientTop,view:e};return{first:r,last:r,views:[r]}}},{key:"_getVisiblePages",value:function(){return(0,n.getVisibleElements)(this.container,this._pages,!0,this._isScrollModeHorizontal)}},{key:"isPageVisible",value:function(e){return!!this.pdfDocument&&(e<1||e>this.pagesCount?(console.error("".concat(this._name,'.isPageVisible: "').concat(e,'" is out of bounds.')),!1):this._getVisiblePages().views.some((function(t){return t.id===e})))}},{key:"cleanup",value:function(){for(var e=0,t=this._pages.length;e<t;e++)this._pages[e]&&this._pages[e].renderingState!==i.RenderingStates.FINISHED&&this._pages[e].reset()}},{key:"_cancelRendering",value:function(){for(var e=0,t=this._pages.length;e<t;e++)this._pages[e]&&this._pages[e].cancelRendering()}},{key:"_ensurePdfPageLoaded",value:function(e){var t=this;if(e.pdfPage)return Promise.resolve(e.pdfPage);if(this._pagesRequests.has(e))return this._pagesRequests.get(e);var r=this.pdfDocument.getPage(e.id).then((function(r){return e.pdfPage||e.setPdfPage(r),t._pagesRequests["delete"](e),r}))["catch"]((function(r){console.error("Unable to get page for page view",r),t._pagesRequests["delete"](e)}));return this._pagesRequests.set(e,r),r}},{key:"forceRendering",value:function(e){var t=this,r=e||this._getVisiblePages(),n=this._isScrollModeHorizontal?this.scroll.right:this.scroll.down,i=this.renderingQueue.getHighestPriority(r,this._pages,n);return!!i&&(this._ensurePdfPageLoaded(i).then((function(){t.renderingQueue.renderView(i)})),!0)}},{key:"createTextLayerBuilder",value:function(e,t,r){var n=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i=arguments.length>4?arguments[4]:void 0;return new c.TextLayerBuilder({textLayerDiv:e,eventBus:i,pageIndex:t,viewport:r,findController:this.isInPresentationMode?null:this.findController,enhanceTextSelection:!this.isInPresentationMode&&n})}},{key:"createAnnotationLayerBuilder",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",o=arguments.length>4&&void 0!==arguments[4]&&arguments[4],s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:n.NullL10n;return new a.AnnotationLayerBuilder({pageDiv:e,pdfPage:t,annotationStorage:r,imageResourcesPath:i,renderInteractiveForms:o,linkService:this.linkService,downloadManager:this.downloadManager,l10n:s})}},{key:"getPagesOverview",value:function(){var e=this._pages.map((function(e){var t=e.pdfPage.getViewport({scale:1});return{width:t.width,height:t.height,rotation:t.rotation}}));return this.enablePrintAutoRotate?e.map((function(e){return(0,n.isPortraitOrientation)(e)?e:{width:e.height,height:e.width,rotation:(e.rotation+90)%360}})):e}},{key:"_updateScrollMode",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=this._scrollMode,r=this.viewer;r.classList.toggle("scrollHorizontal",t===n.ScrollMode.HORIZONTAL),r.classList.toggle("scrollWrapped",t===n.ScrollMode.WRAPPED),this.pdfDocument&&e&&(this._currentScaleValue&&isNaN(this._currentScaleValue)&&this._setScale(this._currentScaleValue,!0),this._setCurrentPageNumber(e,!0),this.update())}},{key:"_updateSpreadMode",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;if(this.pdfDocument){var t=this.viewer,r=this._pages;if(t.textContent="",this._spreadMode===n.SpreadMode.NONE)for(var i=0,a=r.length;i<a;++i)t.appendChild(r[i].div);else for(var o=this._spreadMode-1,s=null,u=0,c=r.length;u<c;++u)null===s?(s=document.createElement("div"),s.className="spread",t.appendChild(s)):u%2===o&&(s=s.cloneNode(!1),t.appendChild(s)),s.appendChild(r[u].div);e&&(this._setCurrentPageNumber(e,!0),this.update())}}},{key:"pagesCount",get:function(){return this._pages.length}},{key:"pageViewsReady",get:function(){return!!this._pagesCapability.settled&&this._pages.every((function(e){return e&&e.pdfPage}))}},{key:"currentPageNumber",get:function(){return this._currentPageNumber},set:function(e){if(!Number.isInteger(e))throw new Error("Invalid page number.");this.pdfDocument&&(this._setCurrentPageNumber(e,!0)||console.error("".concat(this._name,'.currentPageNumber: "').concat(e,'" is not a valid page.')))}},{key:"currentPageLabel",get:function(){return this._pageLabels&&this._pageLabels[this._currentPageNumber-1]},set:function(e){if(this.pdfDocument){var t=0|e;if(this._pageLabels){var r=this._pageLabels.indexOf(e);r>=0&&(t=r+1)}this._setCurrentPageNumber(t,!0)||console.error("".concat(this._name,'.currentPageLabel: "').concat(e,'" is not a valid page.'))}}},{key:"currentScale",get:function(){return this._currentScale!==n.UNKNOWN_SCALE?this._currentScale:n.DEFAULT_SCALE},set:function(e){if(isNaN(e))throw new Error("Invalid numeric scale.");this.pdfDocument&&this._setScale(e,!1)}},{key:"currentScaleValue",get:function(){return this._currentScaleValue},set:function(e){this.pdfDocument&&this._setScale(e,!1)}},{key:"pagesRotation",get:function(){return this._pagesRotation},set:function(e){if(!(0,n.isValidRotation)(e))throw new Error("Invalid pages rotation angle.");if(this.pdfDocument&&this._pagesRotation!==e){this._pagesRotation=e;for(var t=this._currentPageNumber,r=0,i=this._pages.length;r<i;r++){var a=this._pages[r];a.update(a.scale,e)}this._currentScaleValue&&this._setScale(this._currentScaleValue,!0),this.eventBus.dispatch("rotationchanging",{source:this,pagesRotation:e,pageNumber:t}),this.defaultRenderingQueue&&this.update()}}},{key:"firstPagePromise",get:function(){return this.pdfDocument?this._firstPageCapability.promise:null}},{key:"onePageRendered",get:function(){return this.pdfDocument?this._onePageRenderedCapability.promise:null}},{key:"pagesPromise",get:function(){return this.pdfDocument?this._pagesCapability.promise:null}},{key:"_viewerElement",get:function(){throw new Error("Not implemented: _viewerElement")}},{key:"_isScrollModeHorizontal",get:function(){return!this.isInPresentationMode&&this._scrollMode===n.ScrollMode.HORIZONTAL}},{key:"isInPresentationMode",get:function(){return this.presentationModeState===n.PresentationModeState.FULLSCREEN}},{key:"isChangingPresentationMode",get:function(){return this.presentationModeState===n.PresentationModeState.CHANGING}},{key:"isHorizontalScrollbarEnabled",get:function(){return!this.isInPresentationMode&&this.container.scrollWidth>this.container.clientWidth}},{key:"isVerticalScrollbarEnabled",get:function(){return!this.isInPresentationMode&&this.container.scrollHeight>this.container.clientHeight}},{key:"hasEqualPageSizes",get:function(){for(var e=this._pages[0],t=1,r=this._pages.length;t<r;++t){var n=this._pages[t];if(n.width!==e.width||n.height!==e.height)return!1}return!0}},{key:"optionalContentConfigPromise",get:function(){return this.pdfDocument?this._optionalContentConfigPromise?this._optionalContentConfigPromise:this.pdfDocument.getOptionalContentConfig():Promise.resolve(null)},set:function(e){if(!(e instanceof Promise))throw new Error("Invalid optionalContentConfigPromise: ".concat(e));if(this.pdfDocument&&this._optionalContentConfigPromise){this._optionalContentConfigPromise=e;var t,r=l(this._pages);try{for(r.s();!(t=r.n()).done;){var n=t.value;n.update(n.scale,n.rotation,e)}}catch(i){r.e(i)}finally{r.f()}this.update(),this.eventBus.dispatch("optionalcontentconfigchanged",{source:this,promise:e})}}},{key:"scrollMode",get:function(){return this._scrollMode},set:function(e){if(this._scrollMode!==e){if(!(0,n.isValidScrollMode)(e))throw new Error("Invalid scroll mode: ".concat(e));this._scrollMode=e,this.eventBus.dispatch("scrollmodechanged",{source:this,mode:e}),this._updateScrollMode(this._currentPageNumber)}}},{key:"spreadMode",get:function(){return this._spreadMode},set:function(e){if(this._spreadMode!==e){if(!(0,n.isValidSpreadMode)(e))throw new Error("Invalid spread mode: ".concat(e));this._spreadMode=e,this.eventBus.dispatch("spreadmodechanged",{source:this,mode:e}),this._updateSpreadMode(this._currentPageNumber)}}}]),e}();t.BaseViewer=b},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PDFViewer=void 0;var n=r(19),i=r(2);function a(e){return a="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function o(e,t){var r;if("undefined"===typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(r=s(e))||t&&e&&"number"===typeof e.length){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,u=!1;return{s:function(){r=e[Symbol.iterator]()},n:function(){var e=r.next();return o=e.done,e},e:function(e){u=!0,a=e},f:function(){try{o||null==r["return"]||r["return"]()}finally{if(u)throw a}}}}function s(e,t){if(e){if("string"===typeof e)return u(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?u(e,t):void 0}}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function c(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function l(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function h(e,t,r){return t&&l(e.prototype,t),r&&l(e,r),e}function f(e,t,r){return f="undefined"!==typeof Reflect&&Reflect.get?Reflect.get:function(e,t,r){var n=d(e,t);if(n){var i=Object.getOwnPropertyDescriptor(n,t);return i.get?i.get.call(r):i.value}},f(e,t,r||e)}function d(e,t){while(!Object.prototype.hasOwnProperty.call(e,t))if(e=_(e),null===e)break;return e}function p(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&v(e,t)}function v(e,t){return v=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},v(e,t)}function g(e){var t=b();return function(){var r,n=_(e);if(t){var i=_(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return y(this,r)}}function y(e,t){return!t||"object"!==a(t)&&"function"!==typeof t?m(e):t}function m(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function b(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function _(e){return _=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},_(e)}var w=function(e){p(r,e);var t=g(r);function r(){return c(this,r),t.apply(this,arguments)}return h(r,[{key:"_scrollIntoView",value:function(e){var t=e.pageDiv,n=e.pageSpot,i=void 0===n?null:n,a=e.pageNumber,o=void 0===a?null:a;if(!i&&!this.isInPresentationMode){var s=t.offsetLeft+t.clientLeft,u=s+t.clientWidth,c=this.container,l=c.scrollLeft,h=c.clientWidth;(this._isScrollModeHorizontal||s<l||u>l+h)&&(i={left:0,top:0})}f(_(r.prototype),"_scrollIntoView",this).call(this,{pageDiv:t,pageSpot:i,pageNumber:o})}},{key:"_getVisiblePages",value:function(){return this.isInPresentationMode?this._getCurrentVisiblePage():f(_(r.prototype),"_getVisiblePages",this).call(this)}},{key:"_updateHelper",value:function(e){if(!this.isInPresentationMode){var t,r=this._currentPageNumber,n=!1,i=o(e);try{for(i.s();!(t=i.n()).done;){var a=t.value;if(a.percent<100)break;if(a.id===r){n=!0;break}}}catch(s){i.e(s)}finally{i.f()}n||(r=e[0].id),this._setCurrentPageNumber(r)}}},{key:"_viewerElement",get:function(){return(0,i.shadow)(this,"_viewerElement",this.viewer)}}]),r}(n.BaseViewer);t.PDFViewer=w}])}))}}]);