<xml>
<!--  <corpId>ww2373931e70ddd7ef</corpId>-->
<!--  <corpSecret>fE5vO4mfI8OwBEVeX3h66K15mpe9f4AaG0yIDlF5ZmI</corpSecret>-->
<!--  <agentId>3010040</agentId>-->
<!--  <token>x4PE1kgSWkvon04TNvjHGl1wqvd</token>-->
<!--  <aesKey>YP16O6WLFnECFh6YROqrkRxlNoUw7vcJGABKQAFI5hi</aesKey>-->
<!--  <accessToken>x4PE1kgSWkvon04TNvjHGl1wqvd</accessToken>-->
<!--  <expiresTime>1</expiresTime>-->
<!--  <userId>企业号通讯录里的某个userid</userId>-->
<!--  <departmentId>企业号通讯录的某个部门id</departmentId>-->
<!--  <tagId>企业号通讯录里的某个tagid</tagId>-->
<!--  <oauth2redirectUri>网页授权获取用户信息回调地址</oauth2redirectUri>-->
  <webhookKey>fdef02ed-52e9-48ec-b2f4-7deb58c3e470</webhookKey>
  <!-- 企业微信会话存档，私钥，windows以及linux环境sdk路径 -->
<!--  <msgAuditSecret>会话存档的secret</msgAuditSecret>-->
<!--&lt;!&ndash;  <msgAuditLibPath>会话存档的lib path</msgAuditLibPath>&ndash;&gt;-->
<!--  <msgAuditPriKey>-&#45;&#45;&#45;&#45;BEGIN RSA PRIVATE KEY-&#45;&#45;&#45;&#45;-->
<!--    MIICXAIBAAKBgQCTfm5cxqfglfOV7b/Z7OtTZZoZpk2EPTvVhn/ngsfKR899xRdR-->
<!--    25s4h8HkK0XhxqYdOGoAdG3Gms+DvCSY/vu3UtImf0eZSNXpKZJBUnvUVjX4ivnr-->
<!--    Ohu2Rjw6O4gPjPnZKw8voCu0Nae1YLeNvFYw48PK7QrqmpHQv1sCd/8zHwIDAQAB-->
<!--    AoGAResz7xgfQghjsgnEHk9BGUY7YHhlG9CZWjYJ0Ro+ksYq9vClBuGHeitk/0CC-->
<!--    Pq7YVVbGbVPELFd8EvNwF/UcJsMlvFis16FzNS60Hn7M/o82gI6AVhSQmocoGhNs-->
<!--    MIKxTnXRqqlKFbCdcSfG+hQP7syHah6Z8UhLYuEA8s/ppd0CQQD99HTSvB4P5FfL-->
<!--    rlKTz6w6uh4qBYl53u5cLQxCRFGgXD6HvPnEwdzQf+2LCVM1zIhyxw2Kak1U467Q-->
<!--    6JizEuHDTC2YljEbg/j+/AlpA/Ua5HQYnH5yD3DCK7rQyTvqE5gU+CfRbwTbLGre-->
<!--    fk/WJK4iqizgZobNRyUCQGB7jR5b8K7NsX7SoV7v/PFOsoj4G2W5q7LSz4GaoXGl-->
<!--    3F+dSlXPYHdTow3dzfgVDldEzgoThs5UWMTQvBUZch0=-->
<!--    -&#45;&#45;&#45;&#45;END RSA PRIVATE KEY-&#45;&#45;&#45;&#45;-->
<!--  </msgAuditPriKey>-->
<!--  <msgAuditLibPath>-->
<!--    /www/osfile/libcrypto-1_1-x64.dll,libssl-1_1-x64.dll,libcurl-x64.dll,WeWorkFinanceSdk.dll,libWeWorkFinanceSdk_Java.so-->
<!--  </msgAuditLibPath>-->
</xml>
