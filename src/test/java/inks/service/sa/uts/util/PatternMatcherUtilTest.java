package inks.service.sa.uts.util;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * PatternMatcherUtil测试类
 * 验证正则表达式匹配功能的正确性
 *
 * <AUTHOR>
 * @since 2025-07-19
 */
public class PatternMatcherUtilTest {

    @Test
    public void testSuccessPatternMatching() {
        // 测试成功模式匹配
        String output = "Docker version 20.10.8, build 3967b7d";
        String pattern = "(?i).*(docker version).*";
        
        PatternMatcherUtil.MatchResult result = PatternMatcherUtil.matchSuccessPattern(pattern, output, null);
        
        assertTrue(result.isMatched());
        assertEquals(PatternMatcherUtil.MatchType.FIND, result.getMatchType());
        assertNotNull(result.getMatchedText());
    }

    @Test
    public void testErrorPatternMatching() {
        // 测试错误模式匹配
        String output = "bash: docker: command not found";
        String pattern = "(?i).*(command not found).*";
        
        PatternMatcherUtil.MatchResult result = PatternMatcherUtil.matchErrorPattern(pattern, output, null);
        
        assertTrue(result.isMatched());
        assertEquals(PatternMatcherUtil.MatchType.FIND, result.getMatchType());
    }

    @Test
    public void testExactMatching() {
        // 测试完全匹配
        String output = "SUCCESS";
        String pattern = "^SUCCESS$";
        
        PatternMatcherUtil.MatchResult result = PatternMatcherUtil.smartMatch(pattern, output, false);
        
        assertTrue(result.isMatched());
        assertEquals(PatternMatcherUtil.MatchType.EXACT_MATCH, result.getMatchType());
    }

    @Test
    public void testCaseInsensitiveMatching() {
        // 测试不区分大小写匹配
        String output = "Installation COMPLETED successfully";
        String pattern = "(?i).*(completed).*";
        
        PatternMatcherUtil.MatchResult result = PatternMatcherUtil.smartMatch(pattern, output, false);
        
        assertTrue(result.isMatched());
    }

    @Test
    public void testInvalidPattern() {
        // 测试无效正则表达式
        String output = "test output";
        String pattern = "[invalid regex";
        
        PatternMatcherUtil.MatchResult result = PatternMatcherUtil.smartMatch(pattern, output, false);
        
        assertFalse(result.isMatched());
        assertNotNull(result.getErrorMessage());
        assertTrue(result.getErrorMessage().contains("Invalid regex pattern"));
    }

    @Test
    public void testEmptyPattern() {
        // 测试空模式
        String output = "test output";
        String pattern = "";
        
        PatternMatcherUtil.MatchResult result = PatternMatcherUtil.smartMatch(pattern, output, false);
        
        assertTrue(result.isMatched()); // 空模式应该匹配
    }

    @Test
    public void testNullInput() {
        // 测试空输入
        String pattern = "(?i).*(success).*";
        
        PatternMatcherUtil.MatchResult result = PatternMatcherUtil.smartMatch(pattern, null, false);
        
        assertFalse(result.isMatched());
    }

    @Test
    public void testMultilineMatching() {
        // 测试多行匹配
        String output = "Line 1\nInstallation completed\nLine 3";
        String pattern = "(?i).*(installation completed).*";
        
        PatternMatcherUtil.MatchResult result = PatternMatcherUtil.smartMatch(pattern, output, false);
        
        assertTrue(result.isMatched());
    }

    @Test
    public void testPatternValidation() {
        // 测试正则表达式验证
        assertTrue(PatternMatcherUtil.isValidPattern("(?i).*(success).*"));
        assertTrue(PatternMatcherUtil.isValidPattern("^test$"));
        assertTrue(PatternMatcherUtil.isValidPattern(""));
        assertTrue(PatternMatcherUtil.isValidPattern(null));
        
        assertFalse(PatternMatcherUtil.isValidPattern("[invalid"));
        assertFalse(PatternMatcherUtil.isValidPattern("*invalid"));
    }

    @Test
    public void testPatternSuggestions() {
        // 测试正则表达式建议
        String successPattern = PatternMatcherUtil.getPatternSuggestion("success");
        assertTrue(successPattern.contains("success"));
        assertTrue(successPattern.contains("(?i)"));
        
        String errorPattern = PatternMatcherUtil.getPatternSuggestion("error");
        assertTrue(errorPattern.contains("error"));
        assertTrue(errorPattern.contains("fail"));
    }

    @Test
    public void testMatchTypeDetection() {
        // 测试匹配类型检测
        PatternMatcherUtil.MatchResult exactResult = PatternMatcherUtil.smartMatch("^test$", "test", false);
        assertEquals(PatternMatcherUtil.MatchType.EXACT_MATCH, exactResult.getMatchType());
        
        PatternMatcherUtil.MatchResult startsWithResult = PatternMatcherUtil.smartMatch("^test", "test123", false);
        assertEquals(PatternMatcherUtil.MatchType.STARTS_WITH, startsWithResult.getMatchType());
        
        PatternMatcherUtil.MatchResult endsWithResult = PatternMatcherUtil.smartMatch("test$", "123test", false);
        assertEquals(PatternMatcherUtil.MatchType.ENDS_WITH, endsWithResult.getMatchType());
        
        PatternMatcherUtil.MatchResult containsResult = PatternMatcherUtil.smartMatch(".*test.*", "123test456", false);
        assertEquals(PatternMatcherUtil.MatchType.CONTAINS, containsResult.getMatchType());
        
        PatternMatcherUtil.MatchResult findResult = PatternMatcherUtil.smartMatch("test", "123test456", false);
        assertEquals(PatternMatcherUtil.MatchType.FIND, findResult.getMatchType());
    }

    @Test
    public void testComplexScenarios() {
        // 测试复杂场景
        
        // Docker安装成功检测
        String dockerOutput = "Docker version 20.10.8, build 3967b7d\nDocker Engine - Community";
        String dockerPattern = "(?i).*(docker version \\d+\\.\\d+\\.\\d+).*";
        PatternMatcherUtil.MatchResult dockerResult = PatternMatcherUtil.matchSuccessPattern(dockerPattern, dockerOutput, null);
        assertTrue(dockerResult.isMatched());
        
        // 权限错误检测
        String permissionOutput = "cp: cannot create regular file '/etc/test': Permission denied";
        String permissionPattern = "(?i).*(permission denied|access denied).*";
        PatternMatcherUtil.MatchResult permissionResult = PatternMatcherUtil.matchErrorPattern(permissionPattern, permissionOutput, null);
        assertTrue(permissionResult.isMatched());
        
        // 网络连接错误检测
        String networkOutput = "wget: unable to resolve host address 'invalid.domain.com'";
        String networkPattern = "(?i).*(unable to resolve|connection refused|network unreachable).*";
        PatternMatcherUtil.MatchResult networkResult = PatternMatcherUtil.matchErrorPattern(networkPattern, networkOutput, null);
        assertTrue(networkResult.isMatched());
        
        // 服务状态检测
        String serviceOutput = "● docker.service - Docker Application Container Engine\n   Loaded: loaded\n   Active: active (running)";
        String servicePattern = "(?i).*(active.*running|status.*running).*";
        PatternMatcherUtil.MatchResult serviceResult = PatternMatcherUtil.matchSuccessPattern(servicePattern, serviceOutput, null);
        assertTrue(serviceResult.isMatched());
    }

    @Test
    public void testCombinedOutputAndError() {
        // 测试组合输出和错误信息的匹配
        String output = "Processing...";
        String error = "Warning: deprecated option used";
        String pattern = "(?i).*(warning|deprecated).*";
        
        PatternMatcherUtil.MatchResult result = PatternMatcherUtil.matchErrorPattern(pattern, output, error);
        assertTrue(result.isMatched());
    }
}
